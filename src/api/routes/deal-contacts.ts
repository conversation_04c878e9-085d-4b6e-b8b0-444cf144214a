/**
 * API routes for deal-contact relationships
 */

import express from 'express';
import { ContactRoleRepository, ContactRole } from '../repositories/relationships/contact-role-repository';
// Using any type for Database to avoid TypeScript namespace issues

export function setupDealContactRoutes(app: express.Express, db: any) {
  const router = express.Router();
  const contactRoleRepo = new ContactRoleRepository();

  /**
   * Add a contact to a deal
   * POST /api/deal-contacts
   */
  router.post('/', (req, res) => {
    try {
      const { dealId, contactId, role } = req.body;

      if (!dealId || !contactId || !role) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: dealId, contactId, role'
        });
      }

      const createdBy = req.session?.user?.id || 'system';
      const relationship = contactRoleRepo.addContactToDeal(
        dealId,
        contactId,
        role,
        createdBy
      );

      res.json({
        success: true,
        data: relationship
      });
    } catch (error) {
      console.error('Error adding contact to deal:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Add multiple contacts to a deal
   * POST /api/deal-contacts/batch
   */
  router.post('/batch', (req, res) => {
    try {
      const { dealId, contactIds, role } = req.body;

      if (!dealId || !contactIds || !Array.isArray(contactIds) || !role) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: dealId, contactIds (array), role'
        });
      }

      const createdBy = req.session?.user?.id || 'system';
      const relationships = contactRoleRepo.addContactsToDeal(
        dealId,
        contactIds,
        role,
        createdBy
      );

      res.json({
        success: true,
        data: relationships
      });
    } catch (error) {
      console.error('Error adding contacts to deal:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Remove a contact from a deal
   * DELETE /api/deal-contacts/:dealId/:contactId
   */
  router.delete('/:dealId/:contactId', (req, res) => {
    try {
      const { dealId, contactId } = req.params;

      const success = contactRoleRepo.removeContactFromDeal(dealId, contactId);

      if (!success) {
        return res.status(404).json({
          success: false,
          error: 'Association not found'
        });
      }

      res.json({
        success: true,
        message: 'Contact removed from deal successfully'
      });
    } catch (error) {
      console.error('Error removing contact from deal:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Update a contact's role in a deal
   * PATCH /api/deal-contacts/:dealId/:contactId
   */
  router.patch('/:dealId/:contactId', (req, res) => {
    try {
      const { dealId, contactId } = req.params;
      const { role } = req.body;

      if (!role) {
        return res.status(400).json({
          success: false,
          error: 'Missing required field: role'
        });
      }

      const updatedRole = contactRoleRepo.updateContactRole(dealId, contactId, role);

      res.json({
        success: true,
        data: updatedRole
      });
    } catch (error) {
      console.error('Error updating contact role in deal:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get all contacts for a deal
   * GET /api/deal-contacts/:dealId
   */
  router.get('/:dealId', (req, res) => {
    try {
      const { dealId } = req.params;
      const roleFilter = req.query.role as string;

      let contacts;
      if (roleFilter) {
        contacts = contactRoleRepo.getDealContactsByRole(dealId, roleFilter);
      } else {
        contacts = contactRoleRepo.getDealContacts(dealId);
      }

      res.json({
        success: true,
        data: contacts
      });
    } catch (error) {
      console.error('Error getting deal contacts:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get all deals for a contact
   * GET /api/deal-contacts/contact/:contactId
   */
  router.get('/contact/:contactId', (req, res) => {
    try {
      const { contactId } = req.params;

      const deals = contactRoleRepo.getContactDeals(contactId);

      res.json({
        success: true,
        data: deals
      });
    } catch (error) {
      console.error('Error getting contact deals:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Check if a contact is in a deal
   * GET /api/deal-contacts/:dealId/:contactId/check
   */
  router.get('/:dealId/:contactId/check', (req, res) => {
    try {
      const { dealId, contactId } = req.params;

      const isInDeal = contactRoleRepo.isContactInDeal(dealId, contactId);

      res.json({
        success: true,
        data: { isInDeal }
      });
    } catch (error) {
      console.error('Error checking if contact is in deal:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get the role of a contact in a deal
   * GET /api/deal-contacts/:dealId/:contactId/role
   */
  router.get('/:dealId/:contactId/role', (req, res) => {
    try {
      const { dealId, contactId } = req.params;

      const role = contactRoleRepo.getContactRoleInDeal(dealId, contactId);

      res.json({
        success: true,
        data: { role }
      });
    } catch (error) {
      console.error('Error getting contact role in deal:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  /**
   * Get available contact roles
   * GET /api/deal-contacts/roles
   */
  router.get('/roles', (req, res) => {
    try {
      const roles = Object.values(ContactRole);

      res.json({
        success: true,
        data: roles
      });
    } catch (error) {
      console.error('Error getting contact roles:', error);
      res.status(400).json({
        success: false,
        error: error.message
      });
    }
  });

  app.use('/api/deal-contacts', router);
}