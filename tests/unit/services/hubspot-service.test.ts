import { HubSpotService } from '../../../src/api/services/hubspot-service';
import axios from 'axios';
import { HubSpotRepository } from '../../../src/api/repositories/hubspot-import-repository';
import { CompanyRepository } from '../../../src/api/repositories/company-repository';
import { ContactRepository } from '../../../src/api/repositories/contact-repository';
import { DealRepository } from '../../../src/api/repositories/deal-repository';

// Mock dependencies
jest.mock('axios');
jest.mock('../../../src/api/repositories/hubspot-import-repository');
jest.mock('../../../src/api/repositories/company-repository');
jest.mock('../../../src/api/repositories/contact-repository');
jest.mock('../../../src/api/repositories/deal-repository');

describe('HubSpotService', () => {
  let service: HubSpotService;
  let mockAxios: jest.Mocked<typeof axios>;
  let mockHubSpotRepo: jest.Mocked<HubSpotRepository>;
  let mockCompanyRepo: jest.Mocked<CompanyRepository>;
  let mockContactRepo: jest.Mocked<ContactRepository>;
  let mockDealRepo: jest.Mocked<DealRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockAxios = axios as jest.Mocked<typeof axios>;
    mockAxios.create.mockReturnValue(mockAxios as any);
    
    mockHubSpotRepo = new HubSpotRepository() as jest.Mocked<HubSpotRepository>;
    mockCompanyRepo = new CompanyRepository() as jest.Mocked<CompanyRepository>;
    mockContactRepo = new ContactRepository() as jest.Mocked<ContactRepository>;
    mockDealRepo = new DealRepository() as jest.Mocked<DealRepository>;
    
    process.env.HUBSPOT_ACCESS_TOKEN = 'test-token';
    service = new HubSpotService();
    
    // Inject mocked repositories
    (service as any).hubspotRepo = mockHubSpotRepo;
    (service as any).companyRepo = mockCompanyRepo;
    (service as any).contactRepo = mockContactRepo;
    (service as any).dealRepo = mockDealRepo;
  });

  afterEach(() => {
    delete process.env.HUBSPOT_ACCESS_TOKEN;
  });

  describe('fetchCompanies', () => {
    it('should fetch and import companies', async () => {
      const mockCompanies = {
        results: [
          {
            id: '123',
            properties: {
              name: 'Test Company',
              domain: 'test.com',
              city: 'Test City',
              state: 'TS',
              country: 'Test Country',
              industry: 'Technology',
              numberofemployees: '50',
              annualrevenue: '1000000',
              createdate: '2024-01-01T00:00:00Z',
              hs_lastmodifieddate: '2024-01-02T00:00:00Z'
            }
          }
        ],
        paging: {}
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockCompanies });
      mockCompanyRepo.findByHubSpotId.mockResolvedValue(null);
      mockCompanyRepo.create.mockResolvedValue({
        id: 1,
        name: 'Test Company',
        hubspot_id: '123'
      });

      const result = await service.fetchCompanies();

      expect(mockAxios.get).toHaveBeenCalledWith('/crm/v3/objects/companies', {
        params: {
          limit: 100,
          properties: expect.stringContaining('name,domain')
        }
      });
      expect(mockCompanyRepo.create).toHaveBeenCalled();
      expect(result).toEqual({ imported: 1, updated: 0, errors: 0 });
    });

    it('should update existing companies', async () => {
      const mockCompanies = {
        results: [
          {
            id: '123',
            properties: {
              name: 'Updated Company',
              domain: 'updated.com',
              hs_lastmodifieddate: '2024-01-02T00:00:00Z'
            }
          }
        ],
        paging: {}
      };

      const existingCompany = {
        id: 1,
        name: 'Old Company',
        hubspot_id: '123'
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockCompanies });
      mockCompanyRepo.findByHubSpotId.mockResolvedValue(existingCompany);
      mockCompanyRepo.update.mockResolvedValue({
        ...existingCompany,
        name: 'Updated Company'
      });

      const result = await service.fetchCompanies();

      expect(mockCompanyRepo.update).toHaveBeenCalledWith(
        1,
        expect.objectContaining({ name: 'Updated Company' })
      );
      expect(result).toEqual({ imported: 0, updated: 1, errors: 0 });
    });

    it('should handle pagination', async () => {
      const page1 = {
        results: [{ id: '1', properties: { name: 'Company 1' } }],
        paging: { next: { after: '1' } }
      };

      const page2 = {
        results: [{ id: '2', properties: { name: 'Company 2' } }],
        paging: {}
      };

      mockAxios.get
        .mockResolvedValueOnce({ data: page1 })
        .mockResolvedValueOnce({ data: page2 });

      mockCompanyRepo.findByHubSpotId.mockResolvedValue(null);
      mockCompanyRepo.create.mockResolvedValue({ id: 1 });

      const result = await service.fetchCompanies();

      expect(mockAxios.get).toHaveBeenCalledTimes(2);
      expect(result).toEqual({ imported: 2, updated: 0, errors: 0 });
    });

    it('should handle API errors gracefully', async () => {
      mockAxios.get.mockRejectedValueOnce(new Error('API Error'));

      await expect(service.fetchCompanies()).rejects.toThrow('API Error');
    });
  });

  describe('fetchContacts', () => {
    it('should fetch and import contacts', async () => {
      const mockContacts = {
        results: [
          {
            id: '456',
            properties: {
              firstname: 'John',
              lastname: 'Doe',
              email: '<EMAIL>',
              phone: '************',
              jobtitle: 'CEO',
              createdate: '2024-01-01T00:00:00Z',
              hs_lastmodifieddate: '2024-01-02T00:00:00Z'
            },
            associations: {
              companies: {
                results: [{ id: '123' }]
              }
            }
          }
        ],
        paging: {}
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockContacts });
      mockContactRepo.findByHubSpotId.mockResolvedValue(null);
      mockContactRepo.create.mockResolvedValue({
        id: 1,
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        hubspot_id: '456'
      });
      mockCompanyRepo.findByHubSpotId.mockResolvedValue({ id: 1 });

      const result = await service.fetchContacts();

      expect(mockAxios.get).toHaveBeenCalledWith('/crm/v3/objects/contacts', {
        params: expect.objectContaining({
          associations: 'companies'
        })
      });
      expect(mockContactRepo.create).toHaveBeenCalled();
      expect(result).toEqual({ imported: 1, updated: 0, errors: 0 });
    });
  });

  describe('fetchDeals', () => {
    it('should fetch and import deals', async () => {
      const mockDeals = {
        results: [
          {
            id: '789',
            properties: {
              dealname: 'Big Deal',
              amount: '50000',
              dealstage: 'contractsent',
              closedate: '2024-12-31T00:00:00Z',
              pipeline: 'default',
              createdate: '2024-01-01T00:00:00Z',
              hs_lastmodifieddate: '2024-01-02T00:00:00Z'
            },
            associations: {
              companies: {
                results: [{ id: '123' }]
              },
              contacts: {
                results: [{ id: '456' }]
              }
            }
          }
        ],
        paging: {}
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockDeals });
      mockDealRepo.findByHubSpotId.mockResolvedValue(null);
      mockDealRepo.create.mockResolvedValue({
        id: 1,
        name: 'Big Deal',
        value: 50000,
        stage: 'contractsent',
        hubspot_id: '789'
      });
      mockCompanyRepo.findByHubSpotId.mockResolvedValue({ id: 1 });
      mockContactRepo.findByHubSpotId.mockResolvedValue({ id: 1 });

      const result = await service.fetchDeals();

      expect(mockAxios.get).toHaveBeenCalledWith('/crm/v3/objects/deals', {
        params: expect.objectContaining({
          associations: 'companies,contacts'
        })
      });
      expect(mockDealRepo.create).toHaveBeenCalled();
      expect(result).toEqual({ imported: 1, updated: 0, errors: 0 });
    });

    it('should map deal stages correctly', async () => {
      const mockDeals = {
        results: [
          {
            id: '789',
            properties: {
              dealname: 'Test Deal',
              dealstage: 'appointmentscheduled',
              amount: '10000'
            }
          }
        ],
        paging: {}
      };

      mockAxios.get.mockResolvedValueOnce({ data: mockDeals });
      mockDealRepo.findByHubSpotId.mockResolvedValue(null);
      mockDealRepo.create.mockResolvedValue({ id: 1 });

      await service.fetchDeals();

      expect(mockDealRepo.create).toHaveBeenCalledWith(
        expect.objectContaining({
          stage: 'Appointment Scheduled'
        })
      );
    });
  });

  describe('syncAll', () => {
    it('should sync all entities', async () => {
      // Mock successful syncs
      jest.spyOn(service, 'fetchCompanies').mockResolvedValue({ imported: 5, updated: 2, errors: 0 });
      jest.spyOn(service, 'fetchContacts').mockResolvedValue({ imported: 10, updated: 3, errors: 1 });
      jest.spyOn(service, 'fetchDeals').mockResolvedValue({ imported: 3, updated: 1, errors: 0 });

      const result = await service.syncAll();

      expect(service.fetchCompanies).toHaveBeenCalled();
      expect(service.fetchContacts).toHaveBeenCalled();
      expect(service.fetchDeals).toHaveBeenCalled();
      expect(result).toEqual({
        companies: { imported: 5, updated: 2, errors: 0 },
        contacts: { imported: 10, updated: 3, errors: 1 },
        deals: { imported: 3, updated: 1, errors: 0 }
      });
    });

    it('should continue syncing even if one entity type fails', async () => {
      jest.spyOn(service, 'fetchCompanies').mockRejectedValue(new Error('Company sync failed'));
      jest.spyOn(service, 'fetchContacts').mockResolvedValue({ imported: 10, updated: 3, errors: 1 });
      jest.spyOn(service, 'fetchDeals').mockResolvedValue({ imported: 3, updated: 1, errors: 0 });

      const result = await service.syncAll();

      expect(result.companies.errors).toBeGreaterThan(0);
      expect(result.contacts.imported).toBe(10);
      expect(result.deals.imported).toBe(3);
    });
  });

  describe('getImportProgress', () => {
    it('should return import progress', async () => {
      const mockProgress = {
        status: 'in_progress' as const,
        current: 50,
        total: 100,
        entity_type: 'companies',
        started_at: new Date(),
        message: 'Importing companies...'
      };

      mockHubSpotRepo.getLatestImport.mockResolvedValue(mockProgress);

      const result = await service.getImportProgress();

      expect(mockHubSpotRepo.getLatestImport).toHaveBeenCalled();
      expect(result).toEqual(mockProgress);
    });
  });

  describe('rate limiting', () => {
    it('should respect rate limits', async () => {
      const mockCompanies = {
        results: Array(100).fill({
          id: '1',
          properties: { name: 'Company' }
        }),
        paging: {}
      };

      mockAxios.get.mockResolvedValue({ data: mockCompanies });
      mockCompanyRepo.findByHubSpotId.mockResolvedValue(null);
      mockCompanyRepo.create.mockResolvedValue({ id: 1 });

      const startTime = Date.now();
      await service.fetchCompanies();
      const endTime = Date.now();

      // Should have some delay due to rate limiting
      expect(endTime - startTime).toBeGreaterThanOrEqual(0);
    });
  });

  describe('error handling', () => {
    it('should handle missing access token', () => {
      delete process.env.HUBSPOT_ACCESS_TOKEN;
      
      expect(() => new HubSpotService()).toThrow('HUBSPOT_ACCESS_TOKEN not configured');
    });

    it('should handle 429 rate limit errors', async () => {
      const rateLimitError = {
        response: {
          status: 429,
          headers: {
            'retry-after': '2'
          }
        }
      };

      mockAxios.get.mockRejectedValueOnce(rateLimitError);

      await expect(service.fetchCompanies()).rejects.toThrow();
    });

    it('should handle malformed API responses', async () => {
      mockAxios.get.mockResolvedValueOnce({ data: null });

      await expect(service.fetchCompanies()).rejects.toThrow();
    });
  });
});