/**
 * Base API Client with Circuit Breaker Integration
 * 
 * Provides a centralized API client with:
 * - Circuit breaker protection
 * - Automatic retry with exponential backoff
 * - Request/response interceptors
 * - Consistent error handling
 */

import { CircuitBreaker, CircuitBreakerError, circuitBreakerRegistry } from '../../utils/circuit-breaker';

// TypeScript type definitions for API client
type RequestBody = string | number | boolean | Record<string, unknown> | unknown[] | null | undefined;
type ResponseData = Record<string, unknown> | string | number | boolean | unknown[] | null;
type UnknownError = Error | { message?: string; code?: string; status?: number; response?: unknown };

export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
  circuitBreakerName?: string;
  retryConfig?: RetryConfig;
  interceptors?: {
    request?: RequestInterceptor[];
    response?: ResponseInterceptor[];
  };
}

export interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableStatuses?: number[];
  retryableErrors?: string[];
}

export interface ApiRequest {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: RequestBody;
  params?: Record<string, string | number | boolean>;
  timeout?: number;
}

export interface ApiResponse<T = ResponseData> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiError {
  message: string;
  code: string;
  status?: number;
  response?: ResponseData;
  isRetryable: boolean;
}

export type RequestInterceptor = (request: ApiRequest) => ApiRequest | Promise<ApiRequest>;
export type ResponseInterceptor = (response: ApiResponse) => ApiResponse | Promise<ApiResponse>;

export class BaseApiClient {
  private config: ApiClientConfig;
  private circuitBreaker: CircuitBreaker;
  private defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    retryableStatuses: [408, 429, 500, 502, 503, 504],
    retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'ENETUNREACH']
  };

  constructor(config: ApiClientConfig) {
    this.config = {
      ...config,
      timeout: config.timeout || 30000,
      retryConfig: { ...this.defaultRetryConfig, ...config.retryConfig }
    };

    // Get or create circuit breaker
    this.circuitBreaker = circuitBreakerRegistry.getBreaker(
      config.circuitBreakerName || 'default',
      { timeout: this.config.timeout }
    );
  }

  /**
   * Make an API request with circuit breaker protection
   */
  async request<T = any>(request: ApiRequest): Promise<ApiResponse<T>> {
    try {
      return await this.circuitBreaker.execute(async () => {
        return await this.executeWithRetry<T>(request);
      });
    } catch (error) {
      if (error instanceof CircuitBreakerError) {
        throw this.createApiError(
          error.message,
          error.code,
          undefined,
          false
        );
      }
      throw error;
    }
  }

  /**
   * Execute request with retry logic
   */
  private async executeWithRetry<T>(request: ApiRequest, attempt = 1): Promise<ApiResponse<T>> {
    try {
      // Apply request interceptors
      const interceptedRequest = await this.applyRequestInterceptors(request);
      
      // Execute the request
      const response = await this.executeRequest<T>(interceptedRequest);
      
      // Apply response interceptors
      return await this.applyResponseInterceptors(response);
    } catch (error) {
      const apiError = this.normalizeError(error);
      
      if (apiError.isRetryable && attempt < this.config.retryConfig!.maxRetries) {
        const delay = this.calculateRetryDelay(attempt);
        console.log(`[${this.config.circuitBreakerName}] Retrying request (attempt ${attempt + 1}) after ${delay}ms`);
        
        await this.sleep(delay);
        return this.executeWithRetry<T>(request, attempt + 1);
      }
      
      throw apiError;
    }
  }

  /**
   * Execute the actual HTTP request
   */
  private async executeRequest<T>(request: ApiRequest): Promise<ApiResponse<T>> {
    const url = this.buildURL(request.url, request.params);
    const headers = {
      ...this.config.headers,
      ...request.headers,
      'Content-Type': 'application/json'
    };

    const controller = new AbortController();
    const timeout = request.timeout || this.config.timeout;
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: request.method,
        headers,
        body: request.body ? JSON.stringify(request.body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      // Parse response body
      let data: T;
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text() as T;
      }

      // Check for HTTP errors
      if (!response.ok) {
        throw this.createApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          'HTTP_ERROR',
          response.status,
          this.isRetryableStatus(response.status),
          data as any
        );
      }

      return {
        data,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders
      };
    } catch (error) {
      const unknownError = error as UnknownError;
      clearTimeout(timeoutId);
      
      if (unknownError && typeof unknownError === 'object' && 'name' in unknownError && unknownError.name === 'AbortError') {
        throw this.createApiError(
          `Request timeout after ${timeout}ms`,
          'TIMEOUT',
          undefined,
          true
        );
      }
      
      throw unknownError;
    }
  }

  /**
   * Build full URL with query parameters
   */
  private buildURL(path: string, params?: Record<string, string | number | boolean>): string {
    const url = new URL(path, this.config.baseURL);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }
    
    return url.toString();
  }

  /**
   * Apply request interceptors
   */
  private async applyRequestInterceptors(request: ApiRequest): Promise<ApiRequest> {
    let interceptedRequest = request;
    
    if (this.config.interceptors?.request) {
      for (const interceptor of this.config.interceptors.request) {
        interceptedRequest = await interceptor(interceptedRequest);
      }
    }
    
    return interceptedRequest;
  }

  /**
   * Apply response interceptors
   */
  private async applyResponseInterceptors<T>(response: ApiResponse<T>): Promise<ApiResponse<T>> {
    let interceptedResponse = response;
    
    if (this.config.interceptors?.response) {
      for (const interceptor of this.config.interceptors.response) {
        interceptedResponse = await interceptor(interceptedResponse as any) as ApiResponse<T>;
      }
    }
    
    return interceptedResponse;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    const { initialDelay, maxDelay, backoffMultiplier } = this.config.retryConfig!;
    const delay = initialDelay * Math.pow(backoffMultiplier, attempt - 1);
    return Math.min(delay, maxDelay);
  }

  /**
   * Check if status code is retryable
   */
  private isRetryableStatus(status: number): boolean {
    return this.config.retryConfig!.retryableStatuses!.includes(status);
  }

  /**
   * Normalize errors to ApiError format
   */
  private normalizeError(error: UnknownError): ApiError {
    // Type assertion for error with additional properties
    const errorWithProps = error as any;
    
    if (error.message && errorWithProps.code && 'isRetryable' in error) {
      return errorWithProps as ApiError;
    }

    const isNetworkError = this.config.retryConfig!.retryableErrors!.some(
      code => errorWithProps.code === code || error.message?.includes(code)
    );

    return {
      message: error.message || 'Unknown error',
      code: errorWithProps.code || 'UNKNOWN',
      status: errorWithProps.status,
      response: errorWithProps.response,
      isRetryable: isNetworkError || this.isRetryableStatus(errorWithProps.status)
    };
  }

  /**
   * Create standardized API error
   */
  private createApiError(
    message: string,
    code: string,
    status?: number,
    isRetryable = false,
    response?: ResponseData
  ): ApiError {
    return {
      message,
      code,
      status,
      response,
      isRetryable
    };
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Convenience methods
   */
  async get<T = ResponseData>(url: string, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'GET', params });
  }

  async post<T = ResponseData>(url: string, body?: RequestBody, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'POST', body, params });
  }

  async put<T = ResponseData>(url: string, body?: RequestBody, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PUT', body, params });
  }

  async delete<T = ResponseData>(url: string, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'DELETE', params });
  }

  async patch<T = ResponseData>(url: string, body?: RequestBody, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    return this.request<T>({ url, method: 'PATCH', body, params });
  }
}