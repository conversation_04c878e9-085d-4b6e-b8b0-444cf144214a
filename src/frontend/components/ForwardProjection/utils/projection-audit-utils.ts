import { FilterDecision, FilterSummary, ProjectionFilterEvent, ProcessedDecision, FILTER_RULES } from '../types/projection-audit-types'; import { Transaction } from '../../../../types/financial'; import { getFormattedDescription as getTransactionFormattedDescription } from './transactions-list-utils'; /** * Safely converts any value to a string, handling objects and arrays * This prevents the "Objects are not valid as React child" error */ export function safeString(value: unknown): string { if (value === null || value === undefined) { return ''; } if (typeof value === 'object') { try { return JSON.stringify(value); } catch (error) { return '[Object]'; } } return String(value); } /** * Central utility for resolving client names from various data sources * This ensures consistent client name handling across all decision types */ export function resolveClientName( decision: Partial<FilterDecision>, transactionsById?: Map<string, Transaction>, // Use Transaction type projectClientMap?: Map<string, string> ): string | undefined { // If decision already has a valid client name, use it if (decision.clientName && decision.clientName !== 'Unknown Client') { return decision.clientName; } // Try to find client name in different ways, from highest to lowest priority // 1. First try by transaction ID if available if (transactionsById && decision.id && transactionsById.has(decision.id)) { const transaction = transactionsById.get(decision.id); if (transaction?.metadata?.clientName) { // Add safe navigation return transaction.metadata.clientName; } } // 2. Next try by project ID if available if (projectClientMap && decision.projectId && projectClientMap.has(decision.projectId)) { return projectClientMap.get(decision.projectId); } // 3. Try from related invoice if available if (transactionsById && decision.relatedInvoiceId) { // Construct potential IDs for related invoice (could be outstanding or other) const possibleRelatedIds = [ `outstanding-invoice-${decision.relatedInvoiceId}`, `harvest-invoice-${decision.relatedInvoiceId}`, decision.relatedInvoiceId // Check raw ID too ]; for (const possibleId of possibleRelatedIds) { if (transactionsById.has(possibleId)) { const relatedInvoice = transactionsById.get(possibleId); if (relatedInvoice?.metadata?.clientName) { // Add safe navigation return relatedInvoice.metadata.clientName; } } } } // 4. Try to find client name by projectId from transactions if (transactionsById && decision.projectId) { const matchingTransactions = Array.from(transactionsById.values()) .filter(t => t.metadata?.projectId === decision.projectId && t.metadata?.clientName); if (matchingTransactions.length > 0) { return matchingTransactions[0].metadata.clientName; } } // Return original client name or undefined if not found through any method return decision.clientName; } /** * Creates a transaction-like object from a decision for consistent formatting */ export function decisionToTransactionLike(decision: FilterDecision): Partial<Transaction> { // Ensure the date is valid before converting let date: Date; try { date = new Date(decision.paymentDate); if (isNaN(date.getTime())) { console.warn(`Invalid date in decision: ${decision.paymentDate}, using current date`); date = new Date(); } } catch (error) { console.warn(`Error parsing date: ${decision.paymentDate}`, error); date = new Date(); } return { id: decision.id || '', source: 'harvest', // Assuming decisions relate to Harvest projections type: 'invoice', // Treat decisions as invoice-like for formatting metadata: { clientName: decision.clientName || 'Unknown Client', projectName: decision.projectName || 'Unknown Project', projectId: decision.projectId || 'unknown', invoiceNumber: decision.relatedInvoiceNumber, // Include related invoice number if available issue_date: decision.relatedInvoiceDate, // Include related invoice date if available invoiceDate: decision.invoiceDate // Include original invoice date if available }, description: decision.projectName || 'Unknown Project', // Use projectName as description date: date, amount: typeof decision.amount === 'number' && !isNaN(decision.amount) ? decision.amount : 0 }; } /** * Converts a projection filter event to a filter decision */ export function convertEventToDecision(data: ProjectionFilterEvent): FilterDecision | null { if (!data || !data.invoice) { console.warn('Received invalid projection filter event:', data); return null; } // Extract client name only from metadata for consistency const clientName = data.invoice.metadata?.clientName; // Determine invoice type with safe fallback let invoiceType = 'Uninvoiced Work'; // Default if (data.invoice.id) { if (data.invoice.id.startsWith('future-work-')) { invoiceType = 'Projected Income'; } else if (data.invoice.id.startsWith('uninvoiced-')) { invoiceType = 'Uninvoiced Work'; } else if (data.invoice.id.startsWith('outstanding-')) { invoiceType = 'Invoice'; } } // Ensure amount is a valid number const amount = typeof data.invoice.amount === 'number' && !isNaN(data.invoice.amount) ? data.invoice.amount : 0; // Initial decision object structure const decision: FilterDecision = { projectId: data.invoice.metadata?.projectId || 'unknown', // Get from metadata projectName: data.invoice.metadata?.projectName || data.invoice.description || 'Unknown Project', // Use metadata or description clientName, invoiceType, invoiceDate: data.invoice.metadata?.invoiceDate // Check metadata for invoiceDate ? new Date(data.invoice.metadata.invoiceDate).toISOString() : '', paymentDate: new Date(data.invoice.date).toISOString(), // Use root date for paymentDate amount, reason: data.reason, action: data.action, id: data.invoice.id }; // Determine rule type immediately const ruleType = determineRuleType(decision); // Create a new decision object with the rule type let updatedDecision = { ...decision, rule: ruleType }; // Override the invoice type if there's a related invoice if (data.action === 'excluded' && data.relatedInvoice) { updatedDecision = { ...updatedDecision, invoiceType: 'Projected Income' // Excluded projected income due to real invoice }; console.log(`Updated invoice type to Projected Income for ${updatedDecision.projectName} due to related invoice`); } // Extract payment terms data if available if (data.invoice.metadata?.paymentTerms) { updatedDecision = { ...updatedDecision, paymentTermsDays: parseInt(String(data.invoice.metadata.paymentTerms)) // Ensure string conversion before parseInt }; } else if (ruleType === 'PAYMENT_TERMS') { // Default payment terms if not specified updatedDecision = { ...updatedDecision, paymentTermsDays: 30 }; } // Calculate cutoff date for payment terms (if needed) if (ruleType === 'PAYMENT_TERMS') { const today = new Date(); // const projectionDays = 90; // Unused variable already removed const cutoffDate = new Date(today); cutoffDate.setDate(today.getDate() + (updatedDecision.paymentTermsDays || 30)); // Use actual terms if available updatedDecision = { ...updatedDecision, cutoffDate: cutoffDate.toISOString() }; } // Add related invoice details if available if (data.relatedInvoice) { // Use description from metadata if available, otherwise root description, then fallback const relatedInvoiceDescription = data.relatedInvoice.metadata?.description ?? data.relatedInvoice.description ?? 'Related Invoice'; updatedDecision = { ...updatedDecision, relatedInvoice: relatedInvoiceDescription // Store description }; // Extract related invoice ID (check metadata first, then root) const relatedId = data.relatedInvoice.metadata?.id ?? data.relatedInvoice.id; // Use ?? if (relatedId) { const extractedId = extractInvoiceId(String(relatedId)); // Ensure string conversion updatedDecision = { ...updatedDecision, relatedInvoiceId: extractedId, rule: 'REAL_INVOICE' // Explicitly set rule }; console.log(`Extracted relatedInvoiceId: ${extractedId} from ${relatedId}`); } // Cannot reliably extract invoice number from the current relatedInvoice type // We will rely on the relatedInvoiceId if present. // If needed later, the full related invoice might need to be fetched separately. console.log(`Related invoice description available: ${relatedInvoiceDescription}`); // Extract related invoice date (payment/due date - access root property) const relatedDateStr = data.relatedInvoice.date; // Access root 'date' property if (relatedDateStr) { try { const relatedDate = new Date(relatedDateStr); if (!isNaN(relatedDate.getTime())) { updatedDecision = { ...updatedDecision, relatedInvoiceDate: relatedDate.toISOString() }; } } catch (e) { console.warn("Could not parse related invoice date", relatedDateStr); } } // Cannot reliably extract due date or client name from the current relatedInvoice type // If needed later, the full related invoice might need to be fetched separately. } // Convert to transaction-like object for consistent formatting const transactionLike = decisionToTransactionLike(updatedDecision); // Use the same formatting as TransactionsList for consistency const formattedDescription = getTransactionFormattedDescription(transactionLike as any); updatedDecision = { ...updatedDecision, formattedDescription }; return updatedDecision; } /** * Extracts invoice ID from Harvest invoice ID string */ export function extractInvoiceId(id: string): string { // Handle undefined/empty case if (!id) return ''; // Check for known prefixes first if (id.startsWith("harvest-invoice-")) { return id.replace("harvest-invoice-", ""); } if (id.startsWith("outstanding-invoice-")) { return id.replace("outstanding-invoice-", ""); } // Check if it follows pattern with any prefix and a hyphen if (id.includes('-')) { const parts = id.split('-'); // If the last part is numeric, use it as the ID const lastPart = parts[parts.length - 1]; if (/^\d+$/.test(lastPart)) { console.log(`Extracted numeric ID ${lastPart} from pattern with hyphen: ${id}`); return lastPart; } } // If the ID is purely numeric, use it as-is (common case) if (/^\d+$/.test(id)) { console.log(`Using numeric ID as-is: ${id}`); return id; } // Last resort - return the original string console.log(`Could not extract numeric ID from: ${id}, using as-is`); return id; } /** * Generates synthetic exclusions for real invoices */ export function generateSyntheticExclusions( realInvoices: Transaction[], existingExclusions: FilterDecision[] ): FilterDecision[] { // Track which projects already have a real invoice exclusion const projectsWithRealInvoiceExclusions = new Set( existingExclusions .filter(d => d.action === 'excluded' && (d.reason.includes('Real invoice') || d.reason.includes('direct capture')) ) .map(d => d.projectId) ); console.log(`Found ${projectsWithRealInvoiceExclusions.size} projects with existing real invoice exclusions`); console.log(`Creating synthetic exclusions for ${realInvoices.length} real invoices`); const syntheticExclusions: FilterDecision[] = []; // Add exclusions for real invoices that aren't already represented for (const realInvoice of realInvoices) { // CRITICAL DEBUG: Log the raw invoice structure console.log(`REAL INVOICE FOR EXCLUSION:`, JSON.stringify({ id: realInvoice.id, description: realInvoice.description, metadata: realInvoice.metadata, date: realInvoice.date }, null, 2)); const projectId = realInvoice.metadata?.projectId || 'unknown'; // Skip if we already have an exclusion for this project if (projectsWithRealInvoiceExclusions.has(projectId)) { console.log(`Skipping synthetic exclusion for project ${projectId} - already has an exclusion`); continue; } console.log(`Adding synthetic exclusion for project ${projectId} - real invoice: ${realInvoice.description || 'Unknown Description'}`); // Use description // Add this project to our tracking set projectsWithRealInvoiceExclusions.add(projectId); try { const paymentDate = new Date(realInvoice.date); if (isNaN(paymentDate.getTime())) { console.warn('Invalid date for synthetic exclusion:', realInvoice.date); continue; // Skip this exclusion if date is invalid } // CRITICAL: Get the invoice ID let invoiceId = ''; if (realInvoice.id && /^\d+$/.test(realInvoice.id)) { invoiceId = realInvoice.id; } else if (realInvoice.id) { invoiceId = extractInvoiceId(realInvoice.id); } else if (realInvoice.metadata?.id) { invoiceId = extractInvoiceId(realInvoice.metadata.id); } else if (realInvoice.metadata?.invoiceId) { invoiceId = extractInvoiceId(realInvoice.metadata.invoiceId); } else { invoiceId = `synth-${Date.now()}-${Math.floor(Math.random() * 10000)}`; } console.log(`Determined invoiceId: ${invoiceId}`); // CRITICAL: Get the invoice number (check metadata first) let invoiceNumber = realInvoice.metadata?.invoiceNumber || realInvoice.metadata?.number; if (invoiceNumber) { invoiceNumber = String(invoiceNumber); // Ensure string console.log(`Using invoice number from metadata: ${invoiceNumber}`); } else { // Try to extract from description const textToSearch = realInvoice.description || ''; const invoiceMatch = textToSearch.match(/invoice\s+#?(\d+)/i); if (invoiceMatch && invoiceMatch[1]) { invoiceNumber = invoiceMatch[1]; console.log(`Extracted invoice number from description: ${invoiceNumber}`); } else { // Last resort - use the ID itself as the number invoiceNumber = invoiceId; console.log(`Using invoice ID as number fallback: ${invoiceNumber}`); } } // Create the exclusion object with valid dates and using invoice description const exclusion: FilterDecision = { projectId, projectName: realInvoice.description || 'Unknown Invoice', // Use description only clientName: realInvoice.metadata?.clientName, invoiceType: 'Projected Income', // Synthetic exclusions represent overridden projected income invoiceDate: realInvoice.metadata?.invoiceDate ? new Date(realInvoice.metadata.invoiceDate).toISOString() : '', // Use metadata invoiceDate if available paymentDate: paymentDate.toISOString(), amount: realInvoice.amount, reason: FILTER_RULES.REAL_INVOICE, // Explicit reason action: 'excluded', relatedInvoice: realInvoice.description || 'Real Invoice', // Use description relatedInvoiceDate: paymentDate.toISOString(), // Date of the real invoice relatedInvoiceId: invoiceId, relatedInvoiceNumber: invoiceNumber || invoiceId, // Ensure number is never empty rule: 'REAL_INVOICE', // Explicitly set the rule type relatedInvoiceDueDate: realInvoice.metadata?.due_date ? new Date(realInvoice.metadata.due_date).toISOString() : undefined }; // Directly confirm the inclusion of critical fields console.log(`SYNTHETIC EXCLUSION CREATED with critical fields:`, { relatedInvoiceId: exclusion.relatedInvoiceId, relatedInvoiceNumber: exclusion.relatedInvoiceNumber, rule: exclusion.rule }); // Verify the critical fields are NOT undefined before adding if (!exclusion.relatedInvoiceId) { console.error('CRITICAL ERROR: Synthetic exclusion is missing relatedInvoiceId!'); // Force a valid invoice ID to guarantee display exclusion.relatedInvoiceId = invoiceId; // Use the derived invoiceId console.log(`FORCE ASSIGNED relatedInvoiceId: ${exclusion.relatedInvoiceId}`); } syntheticExclusions.push(exclusion); } catch (error) { console.error('Error creating synthetic exclusion:', error); } } // Final check before returning console.log(`Generated ${syntheticExclusions.length} synthetic exclusions`); // Log the first few exclusions syntheticExclusions.slice(0, 3).forEach((exclusion, i) => { console.log(`SYNTHETIC EXCLUSION ${i+1}:`, { relatedInvoiceId: exclusion.relatedInvoiceId, relatedInvoiceNumber: exclusion.relatedInvoiceNumber, rule: exclusion.rule, ruleType: determineRuleType(exclusion) }); }); return syntheticExclusions; } /** * Determines the rule type for a decision */ export function determineRuleType(decision: FilterDecision): keyof typeof FILTER_RULES { // If rule is already set, use it directly (highest priority) if (decision.rule && Object.keys(FILTER_RULES).includes(decision.rule)) { console.log(`Using explicitly set rule type: ${decision.rule}`); return decision.rule; } // Real Invoice Rule - highest priority after explicit rule if (decision.relatedInvoiceId) { console.log(`Decision has relatedInvoiceId: ${decision.relatedInvoiceId}, assigning REAL_INVOICE rule type`); return 'REAL_INVOICE'; } // Check if the reason exactly matches a FILTER_RULES value const ruleEntries = Object.entries(FILTER_RULES); for (const [rule, reasonText] of ruleEntries) { if (decision.reason === reasonText) { console.log(`Exact match found for reason "${decision.reason}" to rule ${rule}`); return rule as keyof typeof FILTER_RULES; } } // Check for substrings in reason (less reliable) // Real Invoice Rule - check reason substrings if relatedInvoiceId wasn't present if (decision.relatedInvoice && (decision.reason.includes('Real invoice') || decision.reason.includes('exists within 5 days') || decision.reason.includes('direct capture'))) { console.log(`Substring match for Real Invoice rule: ${decision.reason}`); return 'REAL_INVOICE'; } // Uninvoiced Work Rule if ((decision.reason.includes('Projected income') || decision.invoiceType === 'Uninvoiced Work' || (decision.id && decision.id.startsWith('uninvoiced-')))) { console.log(`Matching Uninvoiced Work rule for: ${decision.reason}`); return 'UNINVOICED_WORK'; } // Payment Terms Rule if (decision.reason.includes('Payment date before') || decision.reason.includes('cutoff')) { console.log(`Matching Payment Terms rule for: ${decision.reason}`); return 'PAYMENT_TERMS'; } // Only return MEETS_CRITERIA if no other rules match // AND it's a kept decision if (decision.action === 'kept') { console.log(`Kept decision with no specific rule, using MEETS_CRITERIA default`); return 'MEETS_CRITERIA'; } // For excluded decisions that don't match any rule patterns, // this is probably an error state, log it but still assign a default console.warn(`WARNING: Excluded decision doesn't match any rule patterns: ${decision.reason}`); // Use a different rule type for excluded items that don't match known patterns // to prevent weird UI states return 'PAYMENT_TERMS'; // Safest default for unknown excluded items } /** * Creates a formatted description for a decision using the same approach as transactions * with the project on the first line and client on the second line */ export function getDecisionDescription(decision: FilterDecision): string { // Convert decision to transaction-like object const transactionLike = decisionToTransactionLike(decision); // Use the same formatting function as TransactionsList for consistency return getTransactionFormattedDescription(transactionLike as any); } /** * Gets the appropriate color class for a decision type */ export function getTypeColorForDecision(decision: FilterDecision): string { const ruleType = determineRuleType(decision); if (decision.action === 'kept') { return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'; } switch (ruleType) { case 'REAL_INVOICE': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'; case 'UNINVOICED_WORK': return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300'; case 'PAYMENT_TERMS': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'; default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'; } } /** * Processes a FilterDecision into a ProcessedDecision with display properties */ export function processDecision(decision: FilterDecision): ProcessedDecision { // CRITICAL DEBUGGING - log the raw decision console.log('PROCESSING DECISION WITH ACTION:', decision.action, 'TYPE:', decision.invoiceType); // Log real invoice details - essential for debugging green box issues if (decision.relatedInvoiceId) { console.log('### IMPORTANT - DECISION HAS RELATED INVOICE ID:', { id: decision.id, relatedInvoiceId: decision.relatedInvoiceId, relatedInvoiceNumber: decision.relatedInvoiceNumber, rule: decision.rule, // Will show if our explicit rule setting works reason: decision.reason // The full reason string }); } // Create a deep copy of the decision to avoid modifying the original const decisionCopy = { ...decision }; // Log decision properties in detail console.log(`Processing decision: ID: ${decisionCopy.id || 'No ID'} Action: ${decisionCopy.action} Reason: ${decisionCopy.reason} Has relatedInvoice: ${Boolean(decisionCopy.relatedInvoice)} Has relatedInvoiceId: ${Boolean(decisionCopy.relatedInvoiceId)}` ); // CRITICALLY IMPORTANT: Force action to be either 'kept' or 'excluded' // This ensures we never have undefined or incorrect action values if (decisionCopy.action !== 'kept' && decisionCopy.action !== 'excluded') { console.warn(`Decision has invalid action: "${decisionCopy.action}", forcing to "kept"`); decisionCopy.action = 'kept'; } // Determine rule type // CRITICAL CHECK: If relatedInvoiceId exists, ALWAYS use REAL_INVOICE type if (decisionCopy.relatedInvoiceId) { console.log(`*** DETECTED RELATED INVOICE ID: ${decisionCopy.relatedInvoiceId} for decision ${decisionCopy.id}`); // Force the rule and ruleType to be REAL_INVOICE decisionCopy.rule = 'REAL_INVOICE'; // Set ruleType directly - this takes precedence const ruleType = 'REAL_INVOICE'; console.log('FOUND REAL_INVOICE DECISION:', { id: decisionCopy.id, projectName: decisionCopy.projectName, relatedInvoiceId: decisionCopy.relatedInvoiceId, relatedInvoiceNumber: decisionCopy.relatedInvoiceNumber, reason: decisionCopy.reason, rule: decisionCopy.rule, forcedRuleType: ruleType }); // Create a transaction-like object to reuse existing utility functions const transactionLike = decisionToTransactionLike(decisionCopy); // Return ProcessedDecision structure const processed: ProcessedDecision = { ...decisionCopy, // Spread original decision properties formattedDescription: getTransactionFormattedDescription(transactionLike as any), ruleType: ruleType, // Use the forced rule type displayType: 'Real Invoice', typeColor: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' }; return processed; } // For all other decisions without relatedInvoiceId, proceed normally const ruleType = determineRuleType(decisionCopy); // Create a transaction-like object to reuse existing utility functions const transactionLike = decisionToTransactionLike(decisionCopy); // Get the display type based on invoice type and rule - ensure consistent values let displayType = 'Uninvoiced'; // Default // If the rule type is UNINVOICED_WORK and action is 'excluded', it's Projected Income // Otherwise use invoice type if (ruleType === 'UNINVOICED_WORK' && decisionCopy.action === 'excluded') { displayType = 'Projected'; // For excluded Uninvoiced Work, it's related to Projected Income } else if (decisionCopy.invoiceType === 'Projected Income') { displayType = 'Projected'; } else if (decisionCopy.invoiceType === 'Invoice') { displayType = 'Invoice'; } // Use the same formatting approach as TransactionsList for consistency const formattedDescription = getTransactionFormattedDescription(transactionLike as any); // Create a completely new object instead of spreading the original decision const result: ProcessedDecision = { // First add all basic properties from the original decision projectId: decisionCopy.projectId, projectName: decisionCopy.projectName, // Ensure invoiceType is never null - provide default value invoiceType: decisionCopy.invoiceType || 'Uninvoiced Work', invoiceDate: decisionCopy.invoiceDate, paymentDate: decisionCopy.paymentDate, // Ensure amount is a valid number amount: typeof decisionCopy.amount === 'number' && !isNaN(decisionCopy.amount) ? decisionCopy.amount : 0, reason: decisionCopy.reason, action: decisionCopy.action, // This should always be 'kept' or 'excluded' now // Optional properties relatedInvoice: decisionCopy.relatedInvoice, relatedInvoiceDate: decisionCopy.relatedInvoiceDate, relatedInvoiceId: decisionCopy.relatedInvoiceId, relatedInvoiceNumber: decisionCopy.relatedInvoiceNumber, // Include this property id: decisionCopy.id, clientName: decisionCopy.clientName, paymentTermsDays: decisionCopy.paymentTermsDays, cutoffDate: decisionCopy.cutoffDate, rule: decisionCopy.rule, // New processed properties formattedDescription, ruleType, displayType, typeColor: getTypeColorForDecision(decisionCopy) }; // Debug the output (what actually gets returned) if (result.action !== 'kept' && result.action !== 'excluded') { console.error('CRITICAL ERROR: Processed decision has invalid action:', result.action); } // Log the ruleType that was determined - this is CRITICAL for debugging console.log(`Final ruleType for decision ${result.id}: ${result.ruleType}`); // For real invoice decisions, make sure we have all the required data if (result.ruleType === 'REAL_INVOICE') { console.log(`REAL_INVOICE RULE TYPE ASSIGNED TO ${result.id}:`, { relatedInvoiceId: result.relatedInvoiceId, relatedInvoiceNumber: result.relatedInvoiceNumber, // This property exists on FilterDecision relatedInvoiceDate: result.relatedInvoiceDate }); // Critical error check - real invoice without ID if (!result.relatedInvoiceId) { console.error('CRITICAL ERROR: Real invoice rule without relatedInvoiceId!', result); } } return result; } /** * Normalize filter decision reason for consistent categorization */ export function normalizeDecisionReason(decision: FilterDecision): string { // Ensure rule type is determined before accessing FILTER_RULES const ruleType = determineRuleType(decision); return FILTER_RULES[ruleType] || decision.reason; // Fallback to original reason if ruleType is somehow invalid } /** * Calculates summary data from filter decisions */ export function calculateSummary(decisions: FilterDecision[]): FilterSummary { const summary: FilterSummary = { totalInvoices: decisions.length, keptInvoices: decisions.filter(d => d.action === 'kept').length, excludedInvoices: decisions.filter(d => d.action === 'excluded').length, byReason: {} }; // Use normalized reasons for counting decisions.forEach(decision => { const normalizedReason = normalizeDecisionReason(decision); if (!summary.byReason[normalizedReason]) { summary.byReason[normalizedReason] = 0; } summary.byReason[normalizedReason]++; }); return summary; } 