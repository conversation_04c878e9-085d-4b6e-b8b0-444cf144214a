/**
 * Knowledge Graph End-to-End Tests
 * 
 * Tests the complete knowledge graph user workflow using Playwright.
 */
import { test, expect, Page } from '@playwright/test';

test.describe('Knowledge Graph Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await expect(page.locator('body')).toBeVisible();
  });
  
  test('should navigate to knowledge graph from intelligence page', async ({ page }) => {
    // Navigate to Intelligence page
    await page.click('[data-testid="nav-intelligence"]');
    await expect(page).toHaveURL(/.*\/intelligence/);
    
    // Look for Knowledge Graph tab
    await expect(page.locator('text=Knowledge Graph')).toBeVisible();
    
    // Click on Knowledge Graph tab
    await page.click('text=Knowledge Graph');
    
    // Verify knowledge graph is loading
    await expect(page.locator('text=Loading knowledge graph')).toBeVisible();
  });
  
  test('should display knowledge graph with nodes and links', async ({ page }) => {
    // Navigate directly to knowledge graph
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Check for graph statistics
    await expect(page.locator('text=/\\d+ nodes/')).toBeVisible();
    await expect(page.locator('text=/\\d+ links/')).toBeVisible();
    
    // Check for legend
    await expect(page.locator('text=Companies')).toBeVisible();
    await expect(page.locator('text=Contacts')).toBeVisible();
    await expect(page.locator('text=Deals')).toBeVisible();
    await expect(page.locator('text=Projects')).toBeVisible();
    await expect(page.locator('text=Estimates')).toBeVisible();
  });
  
  test('should filter nodes by type', async ({ page }) => {
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Get initial node count
    const initialStats = await page.locator('text=/\\d+ nodes/').textContent();
    const initialCount = parseInt(initialStats?.match(/\d+/)?.[0] || '0');
    
    // Uncheck contacts filter
    await page.uncheck('input[data-testid="filter-contact"]');
    
    // Wait for graph to update
    await page.waitForTimeout(500);
    
    // Verify node count decreased (assuming there were contacts)
    const updatedStats = await page.locator('text=/\\d+ nodes/').textContent();
    const updatedCount = parseInt(updatedStats?.match(/\d+/)?.[0] || '0');
    
    expect(updatedCount).toBeLessThanOrEqual(initialCount);
  });
  
  test('should search and highlight nodes', async ({ page }) => {
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Use search functionality
    const searchInput = page.locator('input[placeholder*="Search"]');
    await searchInput.fill('company');
    
    // Wait for search to process
    await page.waitForTimeout(500);
    
    // Check that search results are highlighted or filtered
    // This depends on the specific implementation
    await expect(searchInput).toHaveValue('company');
  });
  
  test('should show node details on click', async ({ page }) => {
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Mock clicking on a node (since we can't easily interact with canvas)
    // We'll simulate this by checking if the details panel exists
    await page.waitForTimeout(1000);
    
    // Look for any node that might be clickable
    const nodes = await page.locator('[data-testid^="node-"]').count();
    if (nodes > 0) {
      await page.click('[data-testid^="node-"]:first-child');
      
      // Check for details panel
      await expect(page.locator('text=/Selected:/i')).toBeVisible();
    }
  });
  
  test('should export graph data', async ({ page }) => {
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Click export button
    await page.click('button:has-text("Export")');
    
    // Check for export options
    await expect(page.locator('text=Export as JSON')).toBeVisible();
    await expect(page.locator('text=Export as CSV')).toBeVisible();
    
    // Set up download listener
    const downloadPromise = page.waitForEvent('download');
    
    // Click JSON export
    await page.click('text=Export as JSON');
    
    // Wait for download
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('knowledge-graph');
    expect(download.suggestedFilename()).toContain('.json');
  });
  
  test('should handle subgraph view when navigating from entity', async ({ page }) => {
    // First navigate to a company page (assuming we have one)
    await page.goto('/crm/companies');
    
    // Look for a company link
    const companyLink = page.locator('[data-testid="company-link"]').first();
    if (await companyLink.count() > 0) {
      const companyId = await companyLink.getAttribute('data-company-id');
      
      // Navigate to knowledge graph with entity context
      await page.goto(`/intelligence?entity=${companyId}&entityType=company`);
      await page.click('text=Knowledge Graph');
      
      // Wait for subgraph to load
      await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
      
      // Check for depth control
      await expect(page.locator('input[type="range"]')).toBeVisible();
      
      // Check that we have a focused view
      await expect(page.locator('text=/Depth/i')).toBeVisible();
    }
  });
  
  test('should handle empty state gracefully', async ({ page }) => {
    // Mock empty response
    await page.route('/api/knowledge-graph**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            nodes: [],
            links: [],
            stats: {
              totalNodes: 0,
              totalLinks: 0,
              nodeTypes: {},
              linkTypes: {}
            }
          }
        })
      });
    });
    
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Check for empty state message
    await expect(page.locator('text=/No data available/i')).toBeVisible();
  });
  
  test('should handle API errors gracefully', async ({ page }) => {
    // Mock error response
    await page.route('/api/knowledge-graph**', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: 'Server error'
        })
      });
    });
    
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Check for error message
    await expect(page.locator('text=/Failed to load/i')).toBeVisible();
    
    // Should show retry option
    await expect(page.locator('button:has-text("Retry")')).toBeVisible();
  });
  
  test('should be responsive on different screen sizes', async ({ page }) => {
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Graph should still be visible and functional
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Controls should be accessible
    await expect(page.locator('button:has-text("Export")')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);
    
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
  });
  
  test('should maintain performance with large datasets', async ({ page }) => {
    // Mock large dataset
    const generateMockData = (nodeCount: number) => {
      const nodes = Array.from({ length: nodeCount }, (_, i) => ({
        id: `node-${i}`,
        label: `Entity ${i}`,
        type: ['company', 'contact', 'deal', 'project'][i % 4],
        metadata: {}
      }));
      
      const links = Array.from({ length: nodeCount * 2 }, (_, i) => ({
        source: `node-${Math.floor(Math.random() * nodeCount)}`,
        target: `node-${Math.floor(Math.random() * nodeCount)}`,
        type: 'related',
        strength: 3
      }));
      
      return { nodes, links };
    };
    
    const { nodes, links } = generateMockData(500);
    
    await page.route('/api/knowledge-graph**', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            nodes,
            links,
            stats: {
              totalNodes: nodes.length,
              totalLinks: links.length,
              nodeTypes: { company: 125, contact: 125, deal: 125, project: 125 },
              linkTypes: { related: links.length }
            }
          }
        })
      });
    });
    
    const startTime = Date.now();
    
    await page.goto('/intelligence');
    await page.click('text=Knowledge Graph');
    
    // Wait for graph to load
    await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
    await expect(page.locator('text=500 nodes')).toBeVisible();
    
    const loadTime = Date.now() - startTime;
    
    // Should load within reasonable time (adjust threshold as needed)
    expect(loadTime).toBeLessThan(10000); // 10 seconds
    
    // Should still be interactive
    await expect(page.locator('button:has-text("Export")')).toBeVisible();
    await expect(page.locator('input[placeholder*="Search"]')).toBeVisible();
  });
  
  test('should integrate properly with existing CRM workflow', async ({ page }) => {
    // Start from CRM page
    await page.goto('/crm');
    
    // Navigate to companies
    await page.click('text=Companies');
    
    // Click on a company (if available)
    const companyCard = page.locator('[data-testid="company-card"]').first();
    if (await companyCard.count() > 0) {
      await companyCard.click();
      
      // Look for knowledge graph link/button in company details
      const kgButton = page.locator('button:has-text("View in Knowledge Graph")');
      if (await kgButton.count() > 0) {
        await kgButton.click();
        
        // Should navigate to knowledge graph with company context
        await expect(page).toHaveURL(/.*intelligence.*entity=/);
        await expect(page.locator('[data-testid="force-graph"]')).toBeVisible();
      }
    }
  });
});