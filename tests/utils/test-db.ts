/**
 * Test Database Utility
 * 
 * Provides a clean in-memory database for testing repositories
 */
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

let testDb: Database.Database;

/**
 * Initialize test database with schema
 */
export function initTestDatabase(): Database.Database {
  // Create in-memory database
  testDb = new Database(':memory:');
  
  // Initialize schema using the unified migration
  const migrationPath = path.join(__dirname, '../../migrations/000_unified_schema.sql');
  if (fs.existsSync(migrationPath)) {
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    testDb.exec(migrationSQL);
  } else {
    throw new Error(`Migration file not found at ${migrationPath}`);
  }
  
  return testDb;
}

/**
 * Get the test database instance
 */
export function getTestDatabase(): Database.Database {
  if (!testDb) {
    throw new Error('Test database not initialized. Call initTestDatabase() first.');
  }
  return testDb;
}

/**
 * Close the test database
 */
export function closeTestDatabase(): void {
  if (testDb) {
    testDb.close();
  }
}

/**
 * Clear all data from test database
 */
export function clearTestDatabase(): void {
  const tables = [
    'deal', 'company', 'contact', 'deal_estimate', 
    'field_ownership', 'change_log', 'note', 
    'contact_company', 'contact_role', 'estimate',
    'project', 'expense', 'tender'
  ];
  
  for (const table of tables) {
    try {
      testDb.prepare(`DELETE FROM ${table}`).run();
    } catch (e) {
      // Table might not exist, ignore
    }
  }
}