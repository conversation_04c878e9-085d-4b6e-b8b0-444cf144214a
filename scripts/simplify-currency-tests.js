#!/usr/bin/env node

/**
 * Simplify currency tests for AUD-only usage
 */

const fs = require('fs');
const path = require('path');

const testFile = path.join(__dirname, '..', 'tests/unit/utils/format.test.ts');
let content = fs.readFileSync(testFile, 'utf8');

// Replace the multi-currency test with AUD-only test
const newCurrencyTest = `    it('should format AUD currency correctly', () => {
      expect(formatCurrency(1000)).toBe('$1,000.00');
      expect(formatCurrency(1234.56)).toBe('$1,234.56');
      expect(formatCurrency(1000000)).toBe('$1,000,000.00');
      expect(formatCurrency(0.99)).toBe('$0.99');
    });`;

// Replace the old multi-currency tests
content = content.replace(
  /it\('should support different currencies'[\s\S]*?\}\);[\s\S]*?it\('should support custom decimal places'[\s\S]*?\}\);/,
  newCurrencyTest
);

// Also update the formatCurrency function if it exists
const formatFile = path.join(__dirname, '..', 'src/frontend/utils/format.ts');
if (fs.existsSync(formatFile)) {
  let formatContent = fs.readFileSync(formatFile, 'utf8');
  
  // Simplify the formatCurrency function signature comment
  formatContent = formatContent.replace(
    /\/\*\* Currency code \(defaults to AUD\) \*\//,
    '/** Always uses AUD currency */'
  );
  
  fs.writeFileSync(formatFile, formatContent);
  console.log('✅ Updated format.ts to reflect AUD-only usage');
}

fs.writeFileSync(testFile, content);
console.log('✅ Simplified currency tests for AUD-only usage');
console.log('\n💡 Since you only use AUD, consider:');
console.log('   - Removing currency parameter from formatCurrency()');
console.log('   - Hardcoding AUD/$ in all currency displays');
console.log('   - Removing currency selection from any UI components');