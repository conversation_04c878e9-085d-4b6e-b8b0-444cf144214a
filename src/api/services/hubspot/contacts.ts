import { Client } from '@hubspot/api-client';
import { ContactRepository } from '../../repositories/contact-repository';
import { HubSpotProgressTracker, ImportResult } from './progress-tracker';
import { HubSpotUtils } from './utils';

// Define types for HubSpot objects
interface HubSpotObject {
  id: string;
  properties: {
    [key: string]: string | undefined;
  };
}

/**
 * HubSpot contacts import functionality
 */
export class HubSpotContacts {
  private contactRepository: ContactRepository;
  private progressTracker: HubSpotProgressTracker;

  constructor(progressTracker: HubSpotProgressTracker) {
    this.contactRepository = new ContactRepository();
    this.progressTracker = progressTracker;
  }

  /**
   * Import contacts from HubSpot with progress tracking
   */
  async importContacts(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const updates: Array<{ item: string; changes: string[] }> = [];
    const created: Array<{ item: string }> = [];

    try {
      // Get contacts from HubSpot using the search API with pagination
      const properties = [
        'firstname',
        'lastname',
        'email',
        'phone',
        'jobtitle',
        'company',
        'createdate',
        'lastmodifieddate'
      ];

      let allContacts: HubSpotObject[] = [];
      let hasMore = true;
      let after = 0;
      const pageSize = 100;
      let totalFound = 0;

      console.log('Fetching contacts from HubSpot...');

      // Implement pagination to fetch all contacts
      while (hasMore) {
        const publicObjectSearchRequest = {
          filterGroups: [],
          properties,
          limit: pageSize,
          after: after ? after.toString() : undefined
        };

        const response = await HubSpotUtils.withRetry(
          () => client.crm.contacts.searchApi.doSearch(publicObjectSearchRequest)
        );

        allContacts = [...allContacts, ...response.results];
        totalFound += response.results.length;

        console.log(`Fetched page of ${response.results.length} contacts. Total so far: ${totalFound}`);

        if (response.paging && response.paging.next && response.paging.next.after) {
          after = parseInt(response.paging.next.after);
        } else {
          hasMore = false;
        }
      }

      console.log(`Found ${allContacts.length} contacts in HubSpot`);

      // Start progress tracking
      this.progressTracker.startStep('contacts', allContacts.length);

      let importedCount = 0;

      // Process each contact
      for (let i = 0; i < allContacts.length; i++) {
        const contact = allContacts[i];
        const contactName = `${contact.properties.firstname || 'Unknown'} ${contact.properties.lastname || 'Unknown'}`;

        this.progressTracker.updateProgress(i + 1, `Processing contact: ${contactName}`);

        try {
          const hubspotId = contact.id;

          // Check if the contact already exists by HubSpot ID
          let existingContact = null;
          try {
            existingContact = this.contactRepository.getContactByHubspotId(hubspotId);
          } catch (error) {
            console.log('Note: Could not check for existing contact by HubSpot ID, will create new contact');
          }

          if (existingContact) {
            // Update the existing contact
            console.log(`Updating existing contact "${existingContact.firstName} ${existingContact.lastName}" with HubSpot ID ${hubspotId}`);

            const newData = {
              firstName: HubSpotUtils.sanitizePropertyValue(contact.properties.firstname) || 'Unknown',
              lastName: HubSpotUtils.sanitizePropertyValue(contact.properties.lastname) || 'Unknown',
              email: HubSpotUtils.sanitizePropertyValue(contact.properties.email),
              phone: HubSpotUtils.sanitizePropertyValue(contact.properties.phone),
              jobTitle: HubSpotUtils.sanitizePropertyValue(contact.properties.jobtitle),
              notes: existingContact.notes
            };

            const fieldsToTrack = ['firstName', 'lastName', 'email', 'phone', 'jobTitle'];
            const changes = HubSpotUtils.trackChanges(existingContact, newData, fieldsToTrack);

            const updatedContact = this.contactRepository.updateContact(existingContact.id, newData);

            if (updatedContact) {
              importedCount++;
              if (changes.length > 0) {
                updates.push({
                  item: contactName,
                  changes: changes
                });
              }
            }
          } else {
            // Create new contact
            const contactData = {
              firstName: HubSpotUtils.sanitizePropertyValue(contact.properties.firstname) || 'Unknown',
              lastName: HubSpotUtils.sanitizePropertyValue(contact.properties.lastname) || 'Unknown',
              email: HubSpotUtils.sanitizePropertyValue(contact.properties.email),
              phone: HubSpotUtils.sanitizePropertyValue(contact.properties.phone),
              jobTitle: HubSpotUtils.sanitizePropertyValue(contact.properties.jobtitle),
              notes: `Imported from HubSpot on ${new Date().toLocaleString()}`,
              hubspotId: hubspotId
            };

            console.log(`Creating new contact "${contactData.firstName} ${contactData.lastName}" with HubSpot ID ${hubspotId}`);
            
            try {
              const createdContact = this.contactRepository.createContact(contactData);
              if (createdContact) {
                importedCount++;
                created.push({ item: contactName });
              }
            } catch (error) {
              // If the hubspot_id column doesn't exist yet, try creating without it
              console.log('Error creating contact with HubSpot ID, trying without HubSpot ID:', error);

              const simplifiedContactData = {
                firstName: HubSpotUtils.sanitizePropertyValue(contact.properties.firstname) || 'Unknown',
                lastName: HubSpotUtils.sanitizePropertyValue(contact.properties.lastname) || 'Unknown',
                email: HubSpotUtils.sanitizePropertyValue(contact.properties.email),
                phone: HubSpotUtils.sanitizePropertyValue(contact.properties.phone),
                jobTitle: HubSpotUtils.sanitizePropertyValue(contact.properties.jobtitle),
                notes: `Imported from HubSpot on ${new Date().toLocaleString()}`
              };

              const createdContact = this.contactRepository.createContact(simplifiedContactData);
              if (createdContact) {
                importedCount++;
                created.push({ item: contactName });
              }
            }
          }
        } catch (contactError) {
          const errorMessage = contactError instanceof Error ? contactError.message : 'Unknown error';
          console.error(`Error processing contact "${contactName}":`, errorMessage);
          this.progressTracker.addError(contactName, errorMessage);
          errors.push({
            item: contactName,
            error: errorMessage
          });
        }
      }

      this.progressTracker.completeStep();

      // Record the import
      HubSpotUtils.recordImport({
        type: 'contacts',
        success: true,
        count: importedCount,
        errors: errors,
        createdCount: created.length,
        updatedCount: updates.length
      });

      return this.progressTracker.createResult({
        success: true,
        count: importedCount,
        errors: errors,
        updates: updates,
        created: created
      });

    } catch (error) {
      console.error('Error importing contacts from HubSpot:', error);

      let errorMessage = 'Unknown error';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error && typeof error === 'object' && 'body' in error) {
        errorMessage = JSON.stringify((error as any).body);
      }

      HubSpotUtils.recordImport({
        type: 'contacts',
        success: false,
        count: 0,
        errors: [{ item: 'Import failed', error: errorMessage }]
      });

      return this.progressTracker.createResult({
        success: false,
        count: 0,
        error: errorMessage
      });
    }
  }
}