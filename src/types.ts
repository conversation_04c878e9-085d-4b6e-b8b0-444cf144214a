
/**
 * Interface for custom expense
 */
export interface CustomExpense {
  id: string;
  name: string;
  type: 'Monthly Payroll' | 'Superannuation' | 'Insurances' | 'Taxes' | 'Subcontractor Fees' | 'Rent' | 'Reimbursements' | 'Professional Fees' | 'General Expenses' | 'Director Distributions' | 'Hardware' | 'Subscriptions' | 'Other Fees' | 'Other';
  amount: number;
  date: Date;
  frequency: 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'one-off';
  repeatCount?: number; // Number of times to repeat the expense (for recurring expenses)
  description?: string;
  source?: string;
  metadata?: Record<string, unknown>;
  _source?: {
    type: string;
    importedAt: Date;
    [key: string]: unknown;
  };
}

/**
 * Interface for bank account
 */
export interface BankAccount {
  id: string;
  name: string;
  openingBalance?: number;
  closingBalance: number;
}

/**
 * Interface for bank balances
 */
export interface BankBalances {
  totalOpeningBalance?: number;
  totalClosingBalance: number;
  accounts: BankAccount[];
}

// Augment React's CSSProperties to allow custom properties (like --*)
declare module 'react' {
  interface CSSProperties {
    [key: `--${string}`]: string | number | undefined;
  }
}
