/**
 * Fix Contact Role Table Script
 * 
 * This script creates the contact_role table if it doesn't exist and
 * copies data from the deal_contact table if it exists.
 * It's useful for fixing the "no such table: deal_contact" error.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Get database path from environment or use default
const dbPath = process.env.DB_PATH || path.join(__dirname, '..', 'data', 'upstream.db');

// Check if database exists
if (!fs.existsSync(dbPath)) {
  console.error(`Database file not found at ${dbPath}`);
  console.log('Please run the initialize-fresh-database.js script first.');
  process.exit(1);
}

console.log(`Connecting to database at: ${dbPath}`);

// Initialize the database
const db = new BetterSqlite3(dbPath);

// Function to check if a table exists
function tableExists(tableName) {
  const result = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table' AND name=?
  `).get(tableName);
  
  return !!result;
}

// Function to create the contact_role table
function createContactRoleTable() {
  try {
    // Check if the table already exists
    if (tableExists('contact_role')) {
      console.log('contact_role table already exists.');
      return true;
    }

    console.log('Creating contact_role table...');
    
    // Create the table
    db.prepare(`
      CREATE TABLE contact_role (
        contact_id TEXT NOT NULL,
        deal_id TEXT NOT NULL,
        role TEXT,
        created_at TEXT NOT NULL,
        created_by TEXT,
        PRIMARY KEY (contact_id, deal_id),
        FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
        FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
      )
    `).run();

    console.log('contact_role table created successfully.');
    return true;
  } catch (error) {
    console.error('Error creating contact_role table:', error);
    return false;
  }
}

// Function to migrate data from deal_contact to contact_role
function migrateFromDealContact() {
  try {
    // Check if the deal_contact table exists
    if (!tableExists('deal_contact')) {
      console.log('deal_contact table does not exist, no migration needed.');
      return true;
    }

    // Check if the contact_role table exists
    if (!tableExists('contact_role')) {
      console.log('contact_role table does not exist, creating it first.');
      createContactRoleTable();
    }

    console.log('Migrating data from deal_contact to contact_role...');
    
    // Get all records from deal_contact
    const dealContacts = db.prepare(`
      SELECT * FROM deal_contact
    `).all();

    console.log(`Found ${dealContacts.length} records in deal_contact table.`);

    // Start a transaction
    db.prepare('BEGIN TRANSACTION').run();

    try {
      // Insert each record into contact_role
      const now = new Date().toISOString();
      const insertStmt = db.prepare(`
        INSERT OR IGNORE INTO contact_role (contact_id, deal_id, role, created_at, created_by)
        VALUES (?, ?, ?, ?, ?)
      `);

      let insertedCount = 0;
      for (const dc of dealContacts) {
        insertStmt.run(
          dc.contact_id,
          dc.deal_id,
          dc.role,
          dc.created_at || now,
          dc.created_by || 'system'
        );
        insertedCount++;
      }

      // Commit the transaction
      db.prepare('COMMIT').run();
      
      console.log(`Successfully migrated ${insertedCount} records to contact_role table.`);
      return true;
    } catch (error) {
      // Rollback the transaction on error
      db.prepare('ROLLBACK').run();
      console.error('Error during migration, transaction rolled back:', error);
      return false;
    }
  } catch (error) {
    console.error('Error migrating from deal_contact to contact_role:', error);
    return false;
  }
}

// Function to create a view for backward compatibility
function createDealContactView() {
  try {
    // Check if the view already exists
    const viewExists = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='view' AND name='deal_contact'
    `).get();

    if (viewExists) {
      console.log('deal_contact view already exists.');
      return true;
    }

    // Check if the deal_contact table exists
    if (tableExists('deal_contact')) {
      console.log('deal_contact table exists, cannot create view with the same name.');
      return false;
    }

    console.log('Creating deal_contact view for backward compatibility...');
    
    // Create the view
    db.prepare(`
      CREATE VIEW deal_contact AS
      SELECT contact_id, deal_id, role, created_at, created_by
      FROM contact_role
    `).run();

    console.log('deal_contact view created successfully.');
    return true;
  } catch (error) {
    console.error('Error creating deal_contact view:', error);
    return false;
  }
}

// Main function
function main() {
  try {
    // Create the contact_role table
    const contactRoleCreated = createContactRoleTable();
    
    // Migrate data from deal_contact to contact_role
    const dataMigrated = migrateFromDealContact();
    
    // Create a view for backward compatibility
    const viewCreated = createDealContactView();

    if (contactRoleCreated && dataMigrated) {
      console.log('Contact role table setup completed successfully.');
    } else {
      console.error('Failed to complete contact role table setup.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    db.close();
  }
}

// Run the main function
main();
