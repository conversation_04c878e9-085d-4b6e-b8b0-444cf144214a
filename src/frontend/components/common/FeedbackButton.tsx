import React, { useState, useRef, useEffect } from "react"; import { createPortal } from "react-dom"; import { submitFeedback, FeedbackSubmission } from "../../api/feedback"; import { useFloatingPanels } from "../../contexts/FloatingPanelsContext"; import { getRetryableErrorMessage } from "../../utils/error-helpers"; /** * FeedbackButton component * Displays a floating button at the bottom left of the screen for reporting bugs or requesting features */ const FeedbackButton: React.FC = () => { const { feedbackExpanded, expandFeedback, setFeedbackExpanded } = useFloatingPanels(); const [showModal, setShowModal] = useState(false); const [feedbackType, setFeedbackType] = useState<"bug" | "feature">("bug"); const [formData, setFormData] = useState({ title: "", description: "", email: "", priority: "medium", }); const [isSubmitting, setIsSubmitting] = useState(false); const [submitError, setSubmitError] = useState<string | null>(null); const [submitSuccess, setSubmitSuccess] = useState(false); const modalRef = useRef<HTMLDivElement>(null); // Handle click outside to close the modal useEffect(() => { const handleClickOutside = (event: MouseEvent) => { if ( modalRef.current && !modalRef.current.contains(event.target as Node) ) { setShowModal(false); } }; if (showModal) { document.addEventListener("mousedown", handleClickOutside); } return () => { document.removeEventListener("mousedown", handleClickOutside); }; }, [showModal]); // Handle form input changes const handleChange = ( e: React.ChangeEvent< HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement > ) => { const { name, value } = e.target; setFormData((prev) => ({ ...prev, [name]: value })); }; // Handle form submission const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); setIsSubmitting(true); setSubmitError(null); try { // Prepare the feedback submission const feedbackData: FeedbackSubmission = { type: feedbackType, title: formData.title, description: formData.description, priority: formData.priority as "low" | "medium" | "high" | "critical", }; // Add email if provided if (formData.email) { feedbackData.email = formData.email; } // Submit the feedback const result = await submitFeedback(feedbackData); if (result.success) { setSubmitSuccess(true); // Reset form and close modal after a short delay to show success message setTimeout(() => { setFormData({ title: "", description: "", email: "", priority: "medium", }); setShowModal(false); setFeedbackExpanded(false); setSubmitSuccess(false); }, 1500); } else { setSubmitError( getRetryableErrorMessage(new Error(result.error), "submit feedback") ); } } catch (error: any) { setSubmitError(getRetryableErrorMessage(error, "submit feedback")); } finally { setIsSubmitting(false); } }; // Detect dark mode const isDarkMode = document.documentElement.classList.contains("dark"); return ( <> <div className="fixed bottom-20 left-4 z-40 flex flex-col items-start"> {/* Collapsed state shows only an icon button */} {!feedbackExpanded ? ( <button onClick={expandFeedback} className="p-3 rounded-full focus:outline-none focus:ring-2 transition-all duration-200 hover:scale-110" style={{ backgroundColor: "var(--color-primary)", color: "var(--color-bg)", borderColor: "var(--color-primary)", }} onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "var(--color-primary-hover)") } onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "var(--color-primary)") } onFocus={(e) => (e.currentTarget.style.boxShadow = "0 0 0 2px var(--color-primary)") } onBlur={(e) => (e.currentTarget.style.boxShadow = "none")} aria-label="Report Bug or Request Feature" aria-expanded="false" title="Report Bug or Request Feature" > <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </button> ) : ( <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 w-60 border border-gray-200 dark:border-gray-700"> <div className="flex justify-between items-center mb-3"> <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100"> Feedback </h3> <button onClick={() => setFeedbackExpanded(false)} className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300" aria-label="Close panel" > <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /> </svg> </button> </div> <div className="space-y-3"> <button onClick={() => { setFeedbackType("bug"); setShowModal(true); }} className="w-full px-4 py-2 text-sm font-medium text-left text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 dark:/30 rounded-md transition-colors" > Report a Bug </button> <button onClick={() => { setFeedbackType("feature"); setShowModal(true); }} className="w-full px-4 py-2 text-sm font-medium text-left text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 dark:/30 rounded-md transition-colors" > Request a Feature </button> </div> </div> )} </div> {/* Modal for bug report or feature request */} {showModal && createPortal( <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"> <div ref={modalRef} className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto" > <div className="p-6"> <div className="flex justify-between items-center mb-4"> <h2 className="text-lg font-medium text-gray-900 dark:text-white"> {feedbackType === "bug" ? "Report a Bug" : "Request a Feature"} </h2> <button onClick={() => setShowModal(false)} className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300" aria-label="Close modal" > <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /> </svg> </button> </div> <form onSubmit={handleSubmit} className="space-y-4"> <div> <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" > {feedbackType === "bug" ? "Bug Summary" : "Feature Title"} </label> <input type="text" id="title" name="title" value={formData.title} onChange={handleChange} required className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder={ feedbackType === "bug" ? "Brief description of the issue" : "Brief description of the feature" } /> </div> <div> <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" > Description </label> <textarea id="description" name="description" value={formData.description} onChange={handleChange} required rows={4} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder={ feedbackType === "bug" ? "Please describe the bug in detail. What happened? What did you expect to happen?" : "Please describe the feature you would like to see added." } /> </div> {feedbackType === "bug" && ( <div> <label htmlFor="priority" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" > Priority </label> <select id="priority" name="priority" value={formData.priority} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" > <option value="low"> Low - Minor issue, not affecting work </option> <option value="medium"> Medium - Affects work but has workaround </option> <option value="high"> High - Significantly impacts work </option> <option value="critical"> Critical - Blocking work completely </option> </select> </div> )} <div> <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" > Email (optional) </label> <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Your email for follow-up (optional)" /> </div> {/* Error message */} {submitError && ( <div className="text-red-600 dark:text-red-400 text-sm bg-red-50 dark:bg-red-900/20 p-2 rounded-md"> {submitError} </div> )} {/* Success message */} {submitSuccess && ( <div className="text-green-600 dark:text-green-400 text-sm bg-green-50 dark:bg-green-900/20 p-2 rounded-md"> Thank you for your feedback! It has been submitted successfully. </div> )} <div className="flex justify-end pt-2"> <button type="button" onClick={() => setShowModal(false)} disabled={isSubmitting} className="mr-3 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50" > Cancel </button> <button type="submit" disabled={isSubmitting} className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md transition-colors disabled:opacity-50 min-w-[80px]" > {isSubmitting ? ( <span className="flex items-center justify-center"> <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> Sending </span> ) : ( "Submit" )} </button> </div> </form> </div> </div> </div>, document.body )} </> ); }; export default FeedbackButton; 