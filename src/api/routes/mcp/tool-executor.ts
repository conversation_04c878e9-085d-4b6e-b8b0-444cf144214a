import { Request } from 'express';
import { getXeroService } from '../../../services/xero';

/**
 * Execute a tool based on <PERSON>'s request
 */
export async function executeTool(toolName: string, toolArgs: any, req: Request): Promise<any> {
  const xeroService = getXeroService();
  const client = xeroService.getClient();
  const tenantId = xeroService.getActiveTenantId();

  if (!tenantId) {
    throw new Error('No active tenant found. Please connect to Xero first.');
  }

  let result: any;

  switch (toolName) {
    // Accounting Tools
    case 'list_invoices':
      const invoicesResponse = await client.accountingApi.getInvoices(tenantId, undefined, undefined, {
        page: toolArgs.page || 1,
        status: toolArgs.status
      });
      result = invoicesResponse.body.invoices;
      break;

    case 'list_contacts':
      const contactsResponse = await client.accountingApi.getContacts(tenantId, undefined, undefined, {
        page: toolArgs.page || 1
      });
      result = contactsResponse.body.contacts;
      break;

    case 'list_accounts':
      const accountsResponse = await client.accountingApi.getAccounts(tenantId);
      result = accountsResponse.body.accounts;
      break;

    case 'list_payments':
      const paymentsResponse = await client.accountingApi.getPayments(tenantId, undefined, {
        page: toolArgs.page || 1
      });
      result = paymentsResponse.body.payments;
      break;

    case 'list_bank_transactions':
      const bankTransactionsResponse = await client.accountingApi.getBankTransactions(tenantId, undefined, {
        page: toolArgs.page || 1
      });
      result = bankTransactionsResponse.body.bankTransactions;
      break;

    case 'list_bank_transfers':
      const bankTransfersResponse = await client.accountingApi.getBankTransfers(tenantId);
      result = bankTransfersResponse.body.bankTransfers;
      break;

    case 'list_credit_notes':
      const creditNotesResponse = await client.accountingApi.getCreditNotes(tenantId, undefined, {
        page: toolArgs.page || 1
      });
      result = creditNotesResponse.body.creditNotes;
      break;

    case 'list_quotes':
      const quotesResponse = await client.accountingApi.getQuotes(tenantId, undefined, {
        page: toolArgs.page || 1
      });
      result = quotesResponse.body.quotes;
      break;

    case 'list_purchase_orders':
      const purchaseOrdersResponse = await client.accountingApi.getPurchaseOrders(tenantId, undefined, {
        page: toolArgs.page || 1
      });
      result = purchaseOrdersResponse.body.purchaseOrders;
      break;

    case 'list_items':
      const itemsResponse = await client.accountingApi.getItems(tenantId);
      result = itemsResponse.body.items;
      break;

    case 'list_tax_rates':
      const taxRatesResponse = await client.accountingApi.getTaxRates(tenantId);
      result = taxRatesResponse.body.taxRates;
      break;

    case 'list_tracking_categories':
      const trackingCategoriesResponse = await client.accountingApi.getTrackingCategories(tenantId);
      result = trackingCategoriesResponse.body.trackingCategories;
      break;

    case 'list_currencies':
      const currenciesResponse = await client.accountingApi.getCurrencies(tenantId);
      result = currenciesResponse.body.currencies;
      break;

    case 'get_organisation':
      const organisationResponse = await client.accountingApi.getOrganisations(tenantId);
      result = organisationResponse.body.organisations?.[0];
      break;

    case 'get_user':
      const userResponse = await client.accountingApi.getUsers(tenantId);
      result = userResponse.body.users?.[0];
      break;

    // Reporting Tools
    case 'get_balance_sheet':
      const balanceSheetResponse = await client.accountingApi.getReportBalanceSheet(
        tenantId,
        toolArgs.date
      );
      result = balanceSheetResponse.body.reports?.[0];
      break;

    case 'get_profit_loss':
      const profitLossResponse = await client.accountingApi.getReportProfitAndLoss(
        tenantId,
        toolArgs.fromDate,
        toolArgs.toDate
      );
      result = profitLossResponse.body.reports?.[0];
      break;

    case 'get_trial_balance':
      const trialBalanceResponse = await client.accountingApi.getReportTrialBalance(
        tenantId,
        toolArgs.date
      );
      result = trialBalanceResponse.body.reports?.[0];
      break;

    case 'list_aged_receivables_by_contact':
      const agedReceivablesResponse = await client.accountingApi.getReportAgedReceivablesByContact(
        tenantId,
        toolArgs.contactId,
        toolArgs.date
      );
      result = agedReceivablesResponse.body.reports?.[0];
      break;

    case 'list_aged_payables_by_contact':
      const agedPayablesResponse = await client.accountingApi.getReportAgedPayablesByContact(
        tenantId,
        toolArgs.contactId,
        toolArgs.date
      );
      result = agedPayablesResponse.body.reports?.[0];
      break;

    // Payroll Tools
    case 'list_payroll_employees':
      const employeesResponse = await client.payrollAUApi.getEmployees(tenantId);
      result = employeesResponse.body.employees;
      break;

    case 'list_payroll_employee_leave':
      const leaveResponse = await client.payrollAUApi.getEmployeeLeave(
        tenantId,
        toolArgs.employeeId
      );
      result = leaveResponse.body.leave;
      break;

    case 'list_payroll_employee_leave_balances':
      const leaveBalancesResponse = await client.payrollAUApi.getEmployeeLeaveBalances(
        tenantId,
        toolArgs.employeeId
      );
      result = leaveBalancesResponse.body.leaveBalances;
      break;

    case 'list_payroll_employee_leave_types':
      const leaveTypesResponse = await client.payrollAUApi.getEmployeeLeaveTypes(
        tenantId,
        toolArgs.employeeId
      );
      result = leaveTypesResponse.body.leaveTypes;
      break;

    case 'list_payroll_leave_periods':
      const leavePeriodsResponse = await client.payrollAUApi.getEmployeeLeavePeriods(
        tenantId,
        toolArgs.employeeId
      );
      result = leavePeriodsResponse.body.leavePeriods;
      break;

    case 'list_payroll_leave_types':
      const allLeaveTypesResponse = await client.payrollAUApi.getLeaveTypes(tenantId);
      result = allLeaveTypesResponse.body.leaveTypes;
      break;

    case 'get_payroll_timesheet':
      const timesheetResponse = await client.payrollAUApi.getTimesheet(
        tenantId,
        toolArgs.timesheetId
      );
      result = timesheetResponse.body.timesheets?.[0];
      break;

    // Write Operations
    case 'create_contact':
      const newContact = {
        name: toolArgs.name,
        emailAddress: toolArgs.email,
        phones: toolArgs.phone ? [{ phoneType: 'DEFAULT', phoneNumber: toolArgs.phone }] : undefined,
        isCustomer: toolArgs.isCustomer,
        isSupplier: toolArgs.isSupplier
      };
      const createContactResponse = await client.accountingApi.createContacts(
        tenantId,
        { contacts: [newContact] }
      );
      result = createContactResponse.body.contacts?.[0];
      break;

    case 'create_invoice':
      const newInvoice = {
        type: toolArgs.type,
        contact: { contactID: toolArgs.contactId },
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        })),
        dueDate: toolArgs.dueDate ? toolArgs.dueDate : undefined
      };
      const createInvoiceResponse = await client.accountingApi.createInvoices(
        tenantId,
        { invoices: [newInvoice] }
      );
      result = createInvoiceResponse.body.invoices?.[0];
      break;

    case 'create_payment':
      const newPayment = {
        invoice: { invoiceID: toolArgs.invoiceId },
        account: { accountID: toolArgs.accountId },
        amount: toolArgs.amount,
        date: toolArgs.date ? toolArgs.date : undefined
      };
      const createPaymentResponse = await client.accountingApi.createPayment(
        tenantId,
        newPayment
      );
      result = createPaymentResponse.body.payments?.[0];
      break;

    case 'create_quote':
      const newQuote = {
        contact: { contactID: toolArgs.contactId },
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount
        })),
        expiryDate: toolArgs.expiryDate ? toolArgs.expiryDate : undefined
      };
      const createQuoteResponse = await client.accountingApi.createQuotes(
        tenantId,
        { quotes: [newQuote] }
      );
      result = createQuoteResponse.body.quotes?.[0];
      break;

    case 'create_credit_note':
      const newCreditNote = {
        type: toolArgs.type,
        contact: { contactID: toolArgs.contactId },
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        }))
      };
      const createCreditNoteResponse = await client.accountingApi.createCreditNotes(
        tenantId,
        { creditNotes: [newCreditNote] }
      );
      result = createCreditNoteResponse.body.creditNotes?.[0];
      break;

    case 'update_contact':
      const updateContact = {
        contactID: toolArgs.contactId,
        name: toolArgs.name,
        emailAddress: toolArgs.email,
        phones: toolArgs.phone ? [{ phoneType: 'DEFAULT', phoneNumber: toolArgs.phone }] : undefined
      };
      const updateContactResponse = await client.accountingApi.updateContact(
        tenantId,
        toolArgs.contactId,
        { contacts: [updateContact] }
      );
      result = updateContactResponse.body.contacts?.[0];
      break;

    case 'update_invoice':
      const updateInvoice = {
        invoiceID: toolArgs.invoiceId,
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        })),
        dueDate: toolArgs.dueDate ? toolArgs.dueDate : undefined
      };
      const updateInvoiceResponse = await client.accountingApi.updateInvoice(
        tenantId,
        toolArgs.invoiceId,
        { invoices: [updateInvoice] }
      );
      result = updateInvoiceResponse.body.invoices?.[0];
      break;

    case 'update_quote':
      const updateQuote = {
        quoteID: toolArgs.quoteId,
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount
        })),
        expiryDate: toolArgs.expiryDate ? toolArgs.expiryDate : undefined
      };
      const updateQuoteResponse = await client.accountingApi.updateQuote(
        tenantId,
        toolArgs.quoteId,
        { quotes: [updateQuote] }
      );
      result = updateQuoteResponse.body.quotes?.[0];
      break;

    case 'update_credit_note':
      const updateCreditNote = {
        creditNoteID: toolArgs.creditNoteId,
        lineItems: toolArgs.lineItems?.map((item: any) => ({
          description: item.description,
          quantity: item.quantity,
          unitAmount: item.unitAmount,
          accountCode: item.accountCode
        }))
      };
      const updateCreditNoteResponse = await client.accountingApi.updateCreditNote(
        tenantId,
        toolArgs.creditNoteId,
        { creditNotes: [updateCreditNote] }
      );
      result = updateCreditNoteResponse.body.creditNotes?.[0];
      break;

    // Payroll Write Operations
    case 'create_payroll_timesheet':
      const newTimesheet = {
        employeeID: toolArgs.employeeId,
        startDate: toolArgs.startDate,
        endDate: toolArgs.endDate
      };
      const createTimesheetResponse = await client.payrollAUApi.createTimesheet(
        tenantId,
        [newTimesheet]
      );
      result = createTimesheetResponse.body.timesheets?.[0];
      break;

    case 'update_payroll_timesheet_line':
    case 'add_payroll_timesheet_line':
    case 'approve_payroll_timesheet':
    case 'revert_payroll_timesheet':
    case 'delete_payroll_timesheet':
      result = { error: 'This timesheet operation is not currently supported by the Xero API' };
      break;

    // Miscellaneous Tools
    case 'list_contact_groups':
      const contactGroupsResponse = await client.accountingApi.getContactGroups(tenantId);
      result = contactGroupsResponse.body.contactGroups;
      break;

    default:
      throw new Error(`Unknown tool: ${toolName}`);
  }

  return result;
}