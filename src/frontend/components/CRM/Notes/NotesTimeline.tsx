import React, { useState } from 'react'; import { Note, NoteCreate } from '../../../types/crm-types'; import { useMutation, useQueryClient } from 'react-query'; import { addNoteToDeal } from '../../../api/crm'; interface NotesTimelineProps { dealId: string; notes: Note[]; } /** * Component for displaying and adding notes for a deal */ const NotesTimeline: React.FC<NotesTimelineProps> = ({ dealId, notes }) => { const [newNote, setNewNote] = useState(''); const queryClient = useQueryClient(); // Mutation for adding a note const addNoteMutation = useMutation( (noteData: NoteCreate) => addNoteToDeal(noteData), { onSuccess: () => { // Only invalidate the specific deal, not all deals queryClient.invalidateQueries(['deal', dealId]); queryClient.invalidateQueries(['deal-notes', dealId]); setNewNote(''); }, } ); // Handle form submission const handleSubmit = (e: React.FormEvent) => { e.preventDefault(); if (!newNote.trim()) return; addNoteMutation.mutate({ dealId, content: newNote, // In a real app, you might get the current user's name here createdBy: 'Current User', }); }; // Format date to local format with time const formatDateTime = (dateString: string): string => { return new Date(dateString).toLocaleString('en-AU', { day: 'numeric', month: 'short', year: 'numeric', hour: 'numeric', minute: '2-digit', }); }; // Sort notes by date (newest first) const sortedNotes = [...(notes || [])].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime() ); return ( <div className="space-y-4"> {/* Add note form */} <form onSubmit={handleSubmit} className="space-y-2"> <textarea value={newNote} onChange={(e) => setNewNote(e.target.value)} placeholder="Add a note..." className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" rows={3} /> <div className="flex justify-end"> <button type="submit" className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" disabled={!newNote.trim() || addNoteMutation.isLoading} > {addNoteMutation.isLoading ? 'Adding...' : 'Add Note'} </button> </div> </form> {/* Notes timeline */} {sortedNotes.length > 0 ? ( <div className="space-y-4"> {sortedNotes.map((note) => ( <div key={note.id} className="btn-modern--secondary"dark:bg-gray-700 p-3 rounded-lg"> <div className="flex justify-between items-start"> <p className="text-sm font-medium text-gray-900 dark:text-white"> {note.createdBy || 'Unknown User'} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> {formatDateTime(note.createdAt)} </p> </div> <p className="mt-1 text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line"> {note.content} </p> </div> ))} </div> ) : ( <div className="text-center py-4 text-gray-500 dark:text-gray-400"> <p>No notes yet</p> <p className="text-sm">Add the first note above</p> </div> )} </div> ); }; export default NotesTimeline; 