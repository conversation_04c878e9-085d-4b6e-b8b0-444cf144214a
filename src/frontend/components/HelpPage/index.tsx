import React, { useState, useRef, useEffect } from "react"; import { HELP_SECTIONS } from "./constants/helpContent"; import { TableOfContents } from "./components/TableOfContents"; import { SectionWrapper } from "./components/SectionWrapper"; // Import section components import { OverviewSection } from "./sections/OverviewSection"; import { RecentUpdatesSection } from "./sections/RecentUpdatesSection"; import { CashflowSection } from "./sections/CashflowSection"; import { IncomeSection } from "./sections/IncomeSection"; import { ExpensesSection } from "./sections/ExpensesSection"; import { EstimatesSection } from "./sections/EstimatesSection"; import { DealsSection } from "./sections/DealsSection"; import { ReportsSection } from "./sections/ReportsSection"; import { TroubleshootingSection } from "./sections/TroubleshootingSection"; /** * HelpPage component * Provides user-facing documentation on how to use the Upstream platform */ const HelpPage: React.FC = () => { const [expandedSection, setExpandedSection] = useState<string | null>( "overview" ); const [activeSection, setActiveSection] = useState<string>("overview"); const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({}); // Create refs for each section useEffect(() => { HELP_SECTIONS.forEach((section) => { sectionRefs.current[section.id] = null; }); }, []); // Function to scroll to a section const scrollToSection = (sectionId: string) => { setExpandedSection(sectionId); const sectionElement = sectionRefs.current[sectionId]; if (sectionElement) { sectionElement.scrollIntoView({ behavior: "smooth" }); setActiveSection(sectionId); } }; const toggleSection = (section: string) => { if (expandedSection === section) { setExpandedSection(null); } else { setExpandedSection(section); setActiveSection(section); } }; // Map section IDs to their corresponding components const sectionComponents = { overview: <OverviewSection />, "recent-updates": <RecentUpdatesSection />, cashflow: <CashflowSection />, income: <IncomeSection />, expenses: <ExpensesSection />, estimates: <EstimatesSection />, deals: <DealsSection />, reports: <ReportsSection />, troubleshooting: <TroubleshootingSection />, }; return ( <div className="flex flex-col md:flex-row gap-6"> {/* Table of Contents Sidebar */} <TableOfContents sections={HELP_SECTIONS} activeSection={activeSection} onSectionClick={scrollToSection} /> {/* Main Content */} <div className="md:w-3/4 space-y-6"> {HELP_SECTIONS.map((section) => ( <SectionWrapper key={section.id} id={section.id} title={section.title} isExpanded={expandedSection === section.id} onToggle={() => toggleSection(section.id)} sectionRef={(el) => (sectionRefs.current[section.id] = el)} > {sectionComponents[section.id as keyof typeof sectionComponents]} </SectionWrapper> ))} </div> </div> ); }; export default HelpPage; 