const Database = require('better-sqlite3');
const { v4: uuidv4 } = require('uuid');
const path = require('path');

// Connect to the database
const dbPath = path.join(__dirname, '../data/upstream.db');
const db = new Database(dbPath);

try {
  // Get all contacts with NULL IDs
  const nullIdContacts = db.prepare('SELECT rowid FROM contact WHERE id IS NULL').all();
  
  console.log(`Found ${nullIdContacts.length} contacts with NULL IDs`);
  
  // Update each contact with a new UUID
  const updateStmt = db.prepare('UPDATE contact SET id = ? WHERE rowid = ?');
  
  let updated = 0;
  for (const contact of nullIdContacts) {
    const newId = uuidv4();
    updateStmt.run(newId, contact.rowid);
    updated++;
  }
  
  console.log(`Updated ${updated} contacts with new UUIDs`);
  
  // Verify the fix
  const remainingNull = db.prepare('SELECT COUNT(*) as count FROM contact WHERE id IS NULL').get();
  console.log(`Remaining contacts with NULL IDs: ${remainingNull.count}`);
  
} catch (error) {
  console.error('Error fixing NULL contact IDs:', error);
} finally {
  db.close();
}