import React, { useEffect, useState } from "react";
import { useLoading, LoadingType } from "../contexts/LoadingContext";

interface GlobalLoadingIndicatorProps {
  delay?: number; // Delay in ms before showing the indicator
}

/**
 * Global loading indicator component
 * Shows a loading overlay when the application is loading
 * Includes a delay to prevent flashing for quick operations
 */
const GlobalLoadingIndicator: React.FC<GlobalLoadingIndicatorProps> = ({
  delay = 500, // Default delay of 500ms
}) => {
  const { isLoading, loadingType, pendingRequests } = useLoading();
  const [showIndicator, setShowIndicator] = React.useState(false);

  // State for fade-in/out animation
  const [opacity, setOpacity] = useState(0);

  // Use a timeout to delay showing the indicator
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;
    let fadeTimeoutId: NodeJS.Timeout | null = null;

    if (isLoading) {
      // Set a timeout to show the indicator after the delay
      timeoutId = setTimeout(() => {
        setShowIndicator(true);
        // Start fade-in animation after indicator is shown
        fadeTimeoutId = setTimeout(() => {
          setOpacity(1);
        }, 50); // Small delay for the DOM to update
      }, delay);
    } else {
      // Start fade-out animation
      setOpacity(0);

      // Hide the indicator after fade-out animation completes
      fadeTimeoutId = setTimeout(() => {
        setShowIndicator(false);
      }, 300); // Match the transition duration

      // Clear any pending timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    }

    // Clean up timeouts on unmount or when isLoading changes
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (fadeTimeoutId) clearTimeout(fadeTimeoutId);
    };
  }, [isLoading, delay]);

  // Don't render anything if not showing the indicator
  if (!showIndicator) {
    return null;
  }

  // Define branding colors and content based on loading type
  const getBrandingConfig = (type: LoadingType) => {
    switch (type) {
      case "xero":
        return {
          spinnerColor: "text-blue-600",
          badgeBg: "bg-blue-50 dark:bg-blue-900/30",
          badgeText: "text-blue-600 dark:text-blue-300",
          progressColor: "bg-blue-600",
          message: "Connecting to Xero",
        };
      case "harvest":
        return {
          spinnerColor: "text-orange-500",
          badgeBg: "bg-orange-50 dark:bg-orange-900/30",
          badgeText: "text-orange-500 dark:text-orange-300",
          progressColor: "bg-orange-500",
          message: "Connecting to Harvest",
        };
      case "projection":
        return {
          spinnerColor: "text-blue-500",
          badgeBg: "bg-blue-50 dark:bg-blue-900/30",
          badgeText: "text-blue-500 dark:text-blue-300",
          progressColor: "bg-blue-500",
          message: "Loading projection",
        };
      default: // generic
        return {
          spinnerColor: "text-blue-500",
          badgeBg: "bg-blue-50 dark:bg-blue-900/30",
          badgeText: "text-blue-500 dark:text-blue-300",
          progressColor: "bg-blue-500",
          message: "Loading data",
        };
    }
  };

  // Get branding config based on current loading type
  const branding = getBrandingConfig(loadingType);

  return (
    <div
      className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 z-50 flex flex-col items-center justify-center transition-all duration-300 backdrop-blur-sm"
      style={{ opacity }}
    >
      <div className="flex flex-col items-center p-8 rounded-xl bg-white/90 dark:bg-gray-800/90 shadow-xl border border-gray-100 dark:border-gray-700 transform transition-all duration-500">
        {/* Simple spinner */}
        <svg
          className={`animate-spin ${branding.spinnerColor} w-12 h-12 mb-5`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>

        {/* Loading text with animated dots */}
        <div className="flex flex-col items-center">
          <p className="text-gray-700 dark:text-gray-200 text-lg font-medium tracking-wide">
            Loading<span className="animate-ellipsis1">.</span>
            <span className="animate-ellipsis2">.</span>
            <span className="animate-ellipsis3">.</span>
          </p>

          {/* Source-specific message */}
          <div
            className={`mt-3 px-4 py-2 rounded ${branding.badgeBg} ${branding.badgeText} text-sm font-medium`}
          >
            {branding.message}
          </div>

          {/* Subtle progress bar */}
          <div className="w-48 h-1 bg-gray-100 dark:bg-gray-700 rounded-full mt-4 overflow-hidden">
            <div
              className={`h-full ${branding.progressColor} animate-progress-indeterminate`}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlobalLoadingIndicator;
