import React, { useState } from 'react'; import { Company, CompanyUpdate, CompanyRelationship, CompanyRelationshipType } from '../../../types/crm-types'; import { updateCompany, deleteCompany } from '../../../api/crm'; import { enrichCompany } from '../../../api/enrichment'; import { useMutation, useQueryClient } from 'react-query'; import { TeamCoverageMatrix } from '../TeamCoverage'; import { NetworkVisualization } from '../NetworkVisualization'; import { ProjectHistory } from '../ProjectHistory'; import { OpportunityIntelligence } from '../OpportunityIntelligence'; import { useNavigate } from 'react-router-dom'; import ContactCompanyLinker from '../Relationships/ContactCompanyLinker'; import Badge from '../../shared/Badge'; interface CompanyDetailProps { company: Company; onClose: () => void; } /** * Component for displaying detailed information about a company */ const CompanyDetail: React.FC<CompanyDetailProps> = ({ company, onClose }) => { const [isEditing, setIsEditing] = useState(false); const [showContactLinker, setShowContactLinker] = useState(false); const [formData, setFormData] = useState<CompanyUpdate>({ name: company.name, industry: company.industry, size: company.size, website: company.website, address: company.address, description: company.description, }); // State for managing company relationships const [parentCompanies, setParentCompanies] = useState<CompanyRelationship[]>( company.parentCompanies || [] ); const [childCompanies, setChildCompanies] = useState<CompanyRelationship[]>( company.childCompanies || [] ); const queryClient = useQueryClient(); const navigate = useNavigate(); // Mutation for updating a company const updateCompanyMutation = useMutation( (data: CompanyUpdate) => updateCompany(company.id, data), { onSuccess: () => { queryClient.invalidateQueries('companies'); setIsEditing(false); }, } ); // Mutation for deleting a company const deleteCompanyMutation = useMutation( () => deleteCompany(company.id), { onSuccess: () => { queryClient.invalidateQueries('companies'); onClose(); }, } ); // Mutation for enriching company data const enrichCompanyMutation = useMutation( () => { console.log('Starting enrichment for company:', company.id, company.name); return enrichCompany(company.id); }, { onSuccess: (data) => { console.log('Enrichment successful:', data); queryClient.invalidateQueries(['company', company.id]); queryClient.invalidateQueries('companies'); }, onError: (error) => { console.error('Enrichment failed:', error); } } ); // Handle form input changes const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => { const { name, value } = e.target; setFormData({ ...formData, [name]: value === '' ? undefined : value, }); }; // Handle form submission const handleSubmit = (e: React.FormEvent) => { e.preventDefault(); updateCompanyMutation.mutate(formData); }; // Handle company deletion with confirmation const handleDelete = () => { if (window.confirm('Are you sure you want to delete this company? This action cannot be undone.')) { deleteCompanyMutation.mutate(); } }; // Get company logo placeholder based on company name const getCompanyInitial = (name: string): string => { return name.charAt(0).toUpperCase(); }; // Get random color based on company ID for logo background const getLogoColor = (id: string | null | undefined): string => { const colors = [ 'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-yellow-500', 'bg-magenta-500', 'bg-blue-500', 'bg-red-500', 'bg-cyan-500' ]; // If no ID, return a default color if (!id) { return colors[0]; } // Simple hash function to get consistent color for the same ID const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0); return colors[hash % colors.length]; }; // Format date to local format const formatDate = (dateString: string): string => { return new Date(dateString).toLocaleDateString('en-AU', { day: 'numeric', month: 'short', year: 'numeric', }); }; // Format relationship type for display const formatRelationshipType = (type: CompanyRelationshipType): string => { switch (type) { case 'parent': return 'Parent'; case 'subsidiary': return 'Subsidiary'; case 'partner': return 'Partner'; case 'acquisition': return 'Acquisition'; default: return type.charAt(0).toUpperCase() + type.slice(1); } }; return ( <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4" onClick={(e) => { // Only close if clicking the backdrop, not the modal content if (e.target === e.currentTarget) { onClose(); } }} > <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col" onClick={(e) => e.stopPropagation()} > {/* Header */} <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"> <h2 className="text-xl font-semibold text-gray-900 dark:text-white"> {isEditing ? 'Edit Company' : 'Company Details'} </h2> <button onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" > <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /> </svg> </button> </div> {/* Content */} <div className="flex-1 overflow-y-auto p-4"> {isEditing ? ( // Edit form <form onSubmit={handleSubmit} className="space-y-4"> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Company Name </label> <input type="text" name="name" value={formData.name || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" required /> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Industry </label> <input type="text" name="industry" value={formData.industry || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Size </label> <select name="size" value={formData.size || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" > <option value="">Select size</option> <option value="1-10">1-10 employees</option> <option value="11-50">11-50 employees</option> <option value="51-200">51-200 employees</option> <option value="201-500">201-500 employees</option> <option value="501-1000">501-1000 employees</option> <option value="1001+">1001+ employees</option> </select> </div> </div> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Website </label> <input type="url" name="website" value={formData.website || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" placeholder="https://example.com" /> </div> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Address </label> <input type="text" name="address" value={formData.address || ''} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" /> </div> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Description </label> <textarea name="description" value={formData.description || ''} onChange={handleChange} rows={3} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" /> </div> {/* Company Relationships Section - Will be implemented when backend supports it */} {parentCompanies.length > 0 && ( <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Parent/Related Companies </label> <div className="space-y-3 max-h-60 overflow-y-auto"> {parentCompanies.map((parentRel, index) => ( <div key={`parent-edit-${parentRel.company.id || index}`} className="p-3 border border-gray-200 dark:border-gray-600 rounded-md"> <div className="flex items-center justify-between mb-2"> <span className="font-medium">{parentRel.company.name}</span> </div> <div> <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">Relationship Type</label> <select disabled value={parentRel.relationshipType} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" > <option value="parent">Parent</option> <option value="subsidiary">Subsidiary</option> <option value="partner">Partner</option> <option value="acquisition">Acquisition</option> </select> </div> </div> ))} </div> <p className="mt-1 text-xs text-gray-500 dark:text-gray-400"> Note: Editing company relationships will be enabled in a future update. </p> </div> )} {childCompanies.length > 0 && ( <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Subsidiaries & Related Companies </label> <div className="space-y-3 max-h-60 overflow-y-auto"> {childCompanies.map((childRel, index) => ( <div key={`child-edit-${childRel.company.id || index}`} className="p-3 border border-gray-200 dark:border-gray-600 rounded-md"> <div className="flex items-center justify-between mb-2"> <span className="font-medium">{childRel.company.name}</span> </div> <div> <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">Relationship Type</label> <select disabled value={childRel.relationshipType} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" > <option value="parent">Parent</option> <option value="subsidiary">Subsidiary</option> <option value="partner">Partner</option> <option value="acquisition">Acquisition</option> </select> </div> </div> ))} </div> <p className="mt-1 text-xs text-gray-500 dark:text-gray-400"> Note: Editing company relationships will be enabled in a future update. </p> </div> )} <div className="flex justify-between pt-4"> <button type="button" onClick={() => setIsEditing(false)} className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" > Cancel </button> <div className="flex space-x-2"> <button type="button" onClick={handleDelete} className="px-4 py-2 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-200 bg-white dark:bg-gray-700 dark: focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" > Delete </button> <button type="submit" className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" disabled={updateCompanyMutation.isLoading} > {updateCompanyMutation.isLoading ? 'Saving...' : 'Save Changes'} </button> </div> </div> </form> ) : ( // View mode <div className="space-y-6"> {/* Company logo and name */} <div className="flex items-center space-x-4"> <div className={`w-16 h-16 rounded-md ${getLogoColor(company.id)} flex items-center justify-center text-white font-medium text-xl`}> {getCompanyInitial(company.name)} </div> <div> <h3 className="text-lg font-medium text-gray-900 dark:text-white"> {company.name} </h3> {company.industry && ( <p className="text-sm text-gray-500 dark:text-gray-400"> {company.industry} </p> )} </div> </div> {/* Company info */} <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Size</p> <p className="mt-1 text-sm text-gray-900 dark:text-white"> {company.size || 'Not specified'} </p> </div> <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Website</p> <p className="mt-1 text-sm text-gray-900 dark:text-white"> {company.website ? ( <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" onClick={(e) => e.stopPropagation()} > {company.website} </a> ) : ( 'Not provided' )} </p> </div> </div> <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Address</p> <p className="mt-1 text-sm text-gray-900 dark:text-white"> {company.address || 'Not provided'} </p> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</p> <p className="mt-1 text-sm text-gray-900 dark:text-white"> {formatDate(company.createdAt)} </p> </div> <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</p> <p className="mt-1 text-sm text-gray-900 dark:text-white"> {formatDate(company.updatedAt)} </p> </div> </div> {company.description && ( <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Description</p> <p className="mt-1 text-sm text-gray-900 dark:text-white whitespace-pre-line"> {company.description} </p> </div> )} {/* Enrichment Status */} <div className="border-t border-gray-200 dark:border-gray-700 pt-4"> <div className="flex items-center justify-between mb-2"> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Data Enrichment</p> <button onClick={() => enrichCompanyMutation.mutate()} disabled={enrichCompanyMutation.isLoading} className="px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 border border-blue-600 dark:border-blue-400 rounded-md dark:/20 disabled:opacity-50 disabled:cursor-not-allowed" > {enrichCompanyMutation.isLoading ? 'Enriching...' : 'Enrich Data'} </button> </div> {/* Enrichment badges */} <div className="flex items-center gap-2"> {company.enrichmentStatus?.sources?.abn_lookup?.success && ( <Badge variant="success" size="sm"> ABN Verified </Badge> )} {company.lastEnrichedAt && ( <span className="text-xs text-gray-500 dark:text-gray-400"> Last enriched: {new Date(company.lastEnrichedAt).toLocaleDateString()} </span> )} {!company.enrichmentStatus && !company.lastEnrichedAt && ( <span className="text-xs text-gray-500 dark:text-gray-400"> Not yet enriched </span> )} </div> {/* Show enrichment results if just enriched */} {enrichCompanyMutation.isSuccess && enrichCompanyMutation.data && ( <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md"> <p className="text-xs text-blue-700 dark:text-blue-300"> Enrichment complete! Found data from {enrichCompanyMutation.data.results.filter(r => r.success).length} source(s). </p> </div> )} {/* Show error if enrichment failed */} {enrichCompanyMutation.isError && ( <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-md"> <p className="text-xs text-red-700 dark:text-red-300"> Enrichment failed. Please try again later. </p> </div> )} </div> {/* Company Relationships - Parent Companies */} {parentCompanies.length > 0 && ( <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Parent Companies</p> <div className="mt-1 space-y-2"> {parentCompanies.map((parentRel, index) => ( <div key={`parent-${parentRel.company.id || index}`} className="btn-modern--secondary"dark:bg-gray-700 p-2 rounded-md"> <div className="flex justify-between items-center"> <div> <p className="text-sm font-medium text-gray-900 dark:text-white"> {parentRel.company.name} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> Relationship: {formatRelationshipType(parentRel.relationshipType)} </p> </div> </div> </div> ))} </div> </div> )} {/* Company Relationships - Child Companies */} {childCompanies.length > 0 && ( <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Subsidiaries & Related Companies</p> <div className="mt-1 space-y-2"> {childCompanies.map((childRel, index) => ( <div key={`child-${childRel.company.id || index}`} className="btn-modern--secondary"dark:bg-gray-700 p-2 rounded-md"> <div className="flex justify-between items-center"> <div> <p className="text-sm font-medium text-gray-900 dark:text-white"> {childRel.company.name} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> Relationship: {formatRelationshipType(childRel.relationshipType)} </p> </div> </div> </div> ))} </div> </div> )} {/* Action Button for Contact Management */} <div> <button onClick={() => setShowContactLinker(true)} className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 border border-blue-600 dark:border-blue-400 rounded-md dark:/20" > Manage Contact Relationships </button> </div> {/* Associated contacts */} {company.contacts && company.contacts.length > 0 && ( <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Contacts</p> <div className="mt-1 space-y-2"> {company.contacts.map(contact => ( <div key={contact.id} className="btn-modern--secondary"dark:bg-gray-700 p-2 rounded-md"> <p className="text-sm font-medium text-gray-900 dark:text-white"> {contact.firstName} {contact.lastName} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> {contact.jobTitle || 'No title'} • {contact.email || 'No email'} </p> </div> ))} </div> </div> )} {/* Associated deals */} {company.deals && company.deals.length > 0 && ( <div> <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Deals</p> <div className="mt-1 space-y-2"> {company.deals.map(deal => ( <div key={deal.id} className="btn-modern--secondary"dark:bg-gray-700 p-2 rounded-md"> <p className="text-sm font-medium text-gray-900 dark:text-white">{deal.name}</p> <p className="text-xs text-gray-500 dark:text-gray-400"> {deal.stage} • {deal.value ? new Intl.NumberFormat('en-AU', { style: 'currency', currency: deal.currency || 'AUD', maximumFractionDigits: 0, }).format(deal.value) : 'No value'} </p> </div> ))} </div> </div> )} {/* Team Coverage Matrix */} <div className="mt-6"> <TeamCoverageMatrix companyId={company.id} companyName={company.name} onViewContact={(contactId) => { navigate(`/crm/contacts?selected=${contactId}`); onClose(); }} /> </div> {/* Relationship Network Visualization */} <div className="mt-6"> <NetworkVisualization entityId={company.id} entityType="company" depth={2} height={400} /> </div> {/* Project History from Harvest */} <div className="mt-6"> <ProjectHistory companyId={company.id} harvestId={company.harvestId} /> </div> {/* Opportunity Intelligence */} <div className="mt-6"> <OpportunityIntelligence companyId={company.id} /> </div> </div> )} </div> {/* Footer */} {!isEditing && ( <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end"> <button onClick={() => setIsEditing(true)} className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" > Edit Company </button> </div> )} </div> {/* Contact Linker Modal */} {showContactLinker && ( <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4"> <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col"> <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"> <h2 className="text-xl font-semibold text-gray-900 dark:text-white"> Manage Contact Relationships </h2> <button onClick={() => setShowContactLinker(false)} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" > <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /> </svg> </button> </div> <div className="flex-1 overflow-y-auto p-4"> <ContactCompanyLinker mode="company" entityId={company.id} onClose={() => setShowContactLinker(false)} /> </div> </div> </div> )} </div> ); }; export default CompanyDetail; 