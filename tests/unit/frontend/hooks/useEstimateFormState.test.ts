import { renderHook, act } from '@testing-library/react';
import { useEstimateFormState } from '../../../../src/frontend/hooks/useEstimateFormState';

describe('useEstimateFormState', () => {
  const mockEstimate = {
    id: '1',
    estimate_name: 'Test Estimate',
    type: 'estimate' as const,
    status: 'draft' as const,
    valid_until: '2024-12-31',
    revenue: 50000,
    expenses: 10000,
    profit: 40000,
    profit_margin: 80,
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    team_members: [],
    time_allocations: [],
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  };

  it('should initialize with estimate data', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    expect(result.current.formData).toEqual({
      estimate_name: 'Test Estimate',
      type: 'estimate',
      status: 'draft',
      valid_until: '2024-12-31',
      revenue: 50000,
      expenses: 10000,
      profit: 40000,
      profit_margin: 80,
      start_date: '2024-01-01',
      end_date: '2024-12-31'
    });
  });

  it('should handle field updates', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    act(() => {
      result.current.updateField('estimate_name', 'Updated Estimate');
    });

    expect(result.current.formData.estimate_name).toBe('Updated Estimate');
  });

  it('should handle multiple field updates', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    act(() => {
      result.current.updateFields({
        estimate_name: 'Bulk Update',
        revenue: 75000,
        expenses: 15000
      });
    });

    expect(result.current.formData).toMatchObject({
      estimate_name: 'Bulk Update',
      revenue: 75000,
      expenses: 15000
    });
  });

  it('should validate required fields', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    act(() => {
      result.current.updateField('estimate_name', '');
    });

    expect(result.current.errors.estimate_name).toBe('Estimate name is required');
  });

  it('should validate date ranges', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    act(() => {
      result.current.updateFields({
        start_date: '2024-12-31',
        end_date: '2024-01-01'
      });
    });

    expect(result.current.errors.date_range).toBe('End date must be after start date');
  });

  it('should calculate profit margin automatically', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    act(() => {
      result.current.updateFields({
        revenue: 100000,
        expenses: 25000
      });
    });

    expect(result.current.formData.profit).toBe(75000);
    expect(result.current.formData.profit_margin).toBe(75);
  });

  it('should track dirty state', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    expect(result.current.isDirty).toBe(false);

    act(() => {
      result.current.updateField('estimate_name', 'Changed');
    });

    expect(result.current.isDirty).toBe(true);
  });

  it('should reset form state', () => {
    const { result } = renderHook(() => useEstimateFormState(mockEstimate));

    act(() => {
      result.current.updateField('estimate_name', 'Changed');
      result.current.reset();
    });

    expect(result.current.formData.estimate_name).toBe('Test Estimate');
    expect(result.current.isDirty).toBe(false);
  });
});