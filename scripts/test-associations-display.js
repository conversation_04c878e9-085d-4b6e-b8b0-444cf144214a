/**
 * Test script to verify contact-company associations are properly displayed
 */

const Database = require('better-sqlite3');
const path = require('path');

// Use the same database path as the application
const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '..', 'data', 'upstream.db');
const db = new Database(dbPath, { readonly: true });

console.log('Testing Contact-Company Associations Display\n');

// 1. Check if contact_company table has any records
const associationCount = db.prepare('SELECT COUNT(*) as count FROM contact_company').get();
console.log(`Total contact-company associations: ${associationCount.count}`);

if (associationCount.count === 0) {
  console.log('\nNo associations found in database. Run HubSpot import first.');
  process.exit(0);
}

// 2. Get a sample of associations
const associations = db.prepare(`
  SELECT 
    cc.contact_id,
    cc.company_id,
    cc.role,
    cc.is_primary,
    c.first_name || ' ' || c.last_name as contact_name,
    comp.name as company_name
  FROM contact_company cc
  JOIN contact c ON cc.contact_id = c.id
  JOIN company comp ON cc.company_id = comp.id
  WHERE c.deleted_at IS NULL AND comp.deleted_at IS NULL
  LIMIT 5
`).all();

console.log('\nSample associations:');
associations.forEach(assoc => {
  console.log(`- ${assoc.contact_name} → ${assoc.company_name} (${assoc.role}${assoc.is_primary ? ', Primary' : ''})`);
});

// 3. Test if ContactRepository properly includes companies when fetching a contact
const contactWithCompanies = db.prepare(`
  SELECT 
    c.id,
    c.first_name,
    c.last_name,
    COUNT(cc.company_id) as company_count
  FROM contact c
  LEFT JOIN contact_company cc ON c.id = cc.contact_id
  WHERE c.deleted_at IS NULL
  GROUP BY c.id
  HAVING company_count > 0
  LIMIT 1
`).get();

if (contactWithCompanies) {
  console.log(`\nTesting contact with companies: ${contactWithCompanies.first_name} ${contactWithCompanies.last_name}`);
  console.log(`This contact has ${contactWithCompanies.company_count} associated companies`);
  
  // Get the companies for this contact
  const companies = db.prepare(`
    SELECT 
      comp.id,
      comp.name,
      cc.role,
      cc.is_primary
    FROM contact_company cc
    JOIN company comp ON cc.company_id = comp.id
    WHERE cc.contact_id = ?
  `).all(contactWithCompanies.id);
  
  console.log('Associated companies:');
  companies.forEach(comp => {
    console.log(`  - ${comp.name} (${comp.role}${comp.is_primary ? ', Primary' : ''})`);
  });
}

// 4. Test if CompanyRepository properly includes contacts when fetching a company
const companyWithContacts = db.prepare(`
  SELECT 
    comp.id,
    comp.name,
    COUNT(cc.contact_id) as contact_count
  FROM company comp
  LEFT JOIN contact_company cc ON comp.id = cc.company_id
  WHERE comp.deleted_at IS NULL
  GROUP BY comp.id
  HAVING contact_count > 0
  LIMIT 1
`).get();

if (companyWithContacts) {
  console.log(`\nTesting company with contacts: ${companyWithContacts.name}`);
  console.log(`This company has ${companyWithContacts.contact_count} associated contacts`);
  
  // Get the contacts for this company
  const contacts = db.prepare(`
    SELECT 
      c.id,
      c.first_name,
      c.last_name,
      cc.role,
      cc.is_primary
    FROM contact_company cc
    JOIN contact c ON cc.contact_id = c.id
    WHERE cc.company_id = ?
  `).all(companyWithContacts.id);
  
  console.log('Associated contacts:');
  contacts.forEach(cont => {
    console.log(`  - ${cont.first_name} ${cont.last_name} (${cont.role}${cont.is_primary ? ', Primary' : ''})`);
  });
}

console.log('\n✅ If associations are shown above, the database has the data.');
console.log('Now check if they appear in the UI when viewing contacts/companies.');

db.close();