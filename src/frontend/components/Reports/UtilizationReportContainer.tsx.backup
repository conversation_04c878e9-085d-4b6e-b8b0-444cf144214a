/**
 * Container component for Utilization Report
 * Handles data fetching, state management, and business logic
 */

import React from 'react';
import { useUtilizationReport } from '../../hooks/useUtilizationReport';
import UtilizationReportPresentation from './UtilizationReportPresentation';

/**
 * Container component that manages data and business logic for utilization report
 */
export const UtilizationReportContainer: React.FC = () => {
  const {
    // State
    timePeriod,
    customStartDate,
    customEndDate,
    excludeLeave,
    isDropdownOpen,
    
    // Date range
    startDate,
    endDate,
    goToPreviousPeriod,
    goToNextPeriod,
    isNextDisabled,
    
    // Data
    utilizationData,
    isLoading,
    error,
    
    // Actions
    setTimePeriod,
    setCustomStartDate,
    setCustomEndDate,
    setExcludeLeave,
    setIsDropdownOpen,
    refetch
  } = useUtilizationReport();

  return (
    <UtilizationReportPresentation
      // State
      timePeriod={timePeriod}
      customStartDate={customStartDate}
      customEndDate={customEndDate}
      excludeLeave={excludeLeave}
      isDropdownOpen={isDropdownOpen}
      
      // Date range
      startDate={startDate}
      endDate={endDate}
      isNextDisabled={isNextDisabled}
      
      // Data
      utilizationData={utilizationData}
      isLoading={isLoading}
      error={error}
      
      // Actions
      onTimePeriodChange={setTimePeriod}
      onCustomStartDateChange={setCustomStartDate}
      onCustomEndDateChange={setCustomEndDate}
      onExcludeLeaveChange={setExcludeLeave}
      onDropdownToggle={setIsDropdownOpen}
      onGoToPrevious={goToPreviousPeriod}
      onGoToNext={goToNextPeriod}
      onRefresh={refetch}
    />
  );
};

export default UtilizationReportContainer;