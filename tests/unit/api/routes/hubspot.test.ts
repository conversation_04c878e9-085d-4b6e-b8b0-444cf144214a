import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/services/hubspot-service');
jest.mock('../../../../src/api/repositories/hubspot-settings-repository');
jest.mock('../../../../src/api/repositories/hubspot-import-repository');
jest.mock('../../../../src/utils/backend-logger');

describe('HubSpot API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/hubspot/status', () => {
    it('should return connection status', async () => {
      const { getConnectionStatus } = await import('../../../../src/api/services/hubspot-service');
      const mockStatus = {
        connected: true,
        lastSync: '2024-01-01T00:00:00Z',
        companyCount: 100,
        contactCount: 500,
        dealCount: 50,
      };
      (getConnectionStatus as jest.Mock).mockResolvedValue(mockStatus);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/status'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getConnectionStatus).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockStatus);
    });

    it('should handle disconnected status', async () => {
      const { getConnectionStatus } = await import('../../../../src/api/services/hubspot-service');
      const mockStatus = { connected: false };
      (getConnectionStatus as jest.Mock).mockResolvedValue(mockStatus);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/status'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith(mockStatus);
    });
  });

  describe('POST /api/hubspot/import', () => {
    it('should start import process', async () => {
      mockRequest.body = {
        entities: ['companies', 'contacts', 'deals'],
        fullSync: true,
      };
      const { startImport } = await import('../../../../src/api/services/hubspot-service');
      const mockImportResult = {
        importId: 'import-123',
        status: 'started',
        entities: ['companies', 'contacts', 'deals'],
      };
      (startImport as jest.Mock).mockResolvedValue(mockImportResult);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/import' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(startImport).toHaveBeenCalledWith({
        entities: ['companies', 'contacts', 'deals'],
        fullSync: true,
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockImportResult);
    });

    it('should handle import errors', async () => {
      mockRequest.body = { entities: ['companies'] };
      const { startImport } = await import('../../../../src/api/services/hubspot-service');
      const error = new Error('Import failed');
      (startImport as jest.Mock).mockRejectedValue(error);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/import' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('GET /api/hubspot/import/:importId', () => {
    it('should get import status', async () => {
      mockRequest.params = { importId: 'import-123' };
      const { getImportStatus } = await import('../../../../src/api/services/hubspot-service');
      const mockStatus = {
        importId: 'import-123',
        status: 'in_progress',
        progress: {
          companies: { total: 100, processed: 50 },
          contacts: { total: 500, processed: 250 },
          deals: { total: 50, processed: 25 },
        },
      };
      (getImportStatus as jest.Mock).mockResolvedValue(mockStatus);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/import/:importId'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getImportStatus).toHaveBeenCalledWith('import-123');
      expect(mockResponse.json).toHaveBeenCalledWith(mockStatus);
    });
  });

  describe('GET /api/hubspot/settings', () => {
    it('should fetch HubSpot settings', async () => {
      const HubSpotSettingsRepository = (await import('../../../../src/api/repositories/hubspot-settings-repository')).default;
      const mockSettings = {
        syncEnabled: true,
        syncInterval: 3600,
        lastSync: '2024-01-01T00:00:00Z',
        fieldMappings: {
          company: { name: 'name', website: 'domain' },
          contact: { email: 'email', firstName: 'firstname' },
        },
      };
      HubSpotSettingsRepository.prototype.getSettings = jest.fn().mockResolvedValue(mockSettings);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/settings'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith(mockSettings);
    });
  });

  describe('PUT /api/hubspot/settings', () => {
    it('should update HubSpot settings', async () => {
      mockRequest.body = {
        syncEnabled: false,
        syncInterval: 7200,
      };
      const HubSpotSettingsRepository = (await import('../../../../src/api/repositories/hubspot-settings-repository')).default;
      const mockUpdatedSettings = {
        syncEnabled: false,
        syncInterval: 7200,
        lastSync: '2024-01-01T00:00:00Z',
      };
      HubSpotSettingsRepository.prototype.updateSettings = jest.fn().mockResolvedValue(mockUpdatedSettings);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/settings' && layer.route?.methods?.put
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(HubSpotSettingsRepository.prototype.updateSettings).toHaveBeenCalledWith({
        syncEnabled: false,
        syncInterval: 7200,
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUpdatedSettings);
    });
  });

  describe('POST /api/hubspot/sync', () => {
    it('should trigger manual sync', async () => {
      mockRequest.body = { entities: ['companies', 'contacts'] };
      const { triggerSync } = await import('../../../../src/api/services/hubspot-service');
      const mockSyncResult = {
        syncId: 'sync-456',
        status: 'started',
        entities: ['companies', 'contacts'],
      };
      (triggerSync as jest.Mock).mockResolvedValue(mockSyncResult);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/sync' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(triggerSync).toHaveBeenCalledWith(['companies', 'contacts']);
      expect(mockResponse.json).toHaveBeenCalledWith(mockSyncResult);
    });
  });

  describe('GET /api/hubspot/field-mappings', () => {
    it('should get field mappings', async () => {
      const { getFieldMappings } = await import('../../../../src/api/services/hubspot-service');
      const mockMappings = {
        company: {
          internal: ['name', 'website', 'industry'],
          hubspot: ['name', 'domain', 'industry'],
          mappings: {
            name: 'name',
            website: 'domain',
            industry: 'industry',
          },
        },
        contact: {
          internal: ['email', 'firstName', 'lastName'],
          hubspot: ['email', 'firstname', 'lastname'],
          mappings: {
            email: 'email',
            firstName: 'firstname',
            lastName: 'lastname',
          },
        },
      };
      (getFieldMappings as jest.Mock).mockResolvedValue(mockMappings);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/field-mappings'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getFieldMappings).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockMappings);
    });
  });

  describe('PUT /api/hubspot/field-mappings', () => {
    it('should update field mappings', async () => {
      mockRequest.body = {
        entity: 'company',
        mappings: {
          name: 'name',
          website: 'website',
          industry: 'custom_industry',
        },
      };
      const { updateFieldMappings } = await import('../../../../src/api/services/hubspot-service');
      const mockResult = { success: true };
      (updateFieldMappings as jest.Mock).mockResolvedValue(mockResult);

      const hubspotRoutes = await import('../../../../src/api/routes/hubspot');
      const handler = hubspotRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/field-mappings' && layer.route?.methods?.put
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(updateFieldMappings).toHaveBeenCalledWith('company', {
        name: 'name',
        website: 'website',
        industry: 'custom_industry',
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockResult);
    });
  });
});