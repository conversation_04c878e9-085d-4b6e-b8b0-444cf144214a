// React is used implicitly for JSX
import { useProjection, formatCurrency, useLabelVisibility } from ".";
import CashflowChart from "./CashflowChart";
import TransactionsList from "./TransactionsList";
import LatePaymentsSummary from "./LatePaymentsSummary";
import CashflowSummaryCard from "./CashflowSummaryCard";
import TimeMachine from "./TimeMachine";
import ScenarioToggle from "./ScenarioToggle";
import { SkeletonCard, SkeletonChart, SkeletonTable } from "./LoadingIndicator";

/**
 * Component for displaying the main content when data is loaded
 */
const ContentDisplay = () => {
  const {
    projectionData,
    loading,
    timeframe,
    handleTimeframeChange,
    totalInflows,
    totalOutflows,
    isHistoricalView,
    historicalDate,
    exitHistoricalView,
    showScenarios,
    setShowScenarios,
  } = useProjection();

  // Use the label visibility hook
  const { labelsVisible, toggleLabels } = useLabelVisibility();

  // Show loading skeletons when loading
  if (loading) {
    return (
      <div
        style={{ display: "flex", flexDirection: "column", gap: "4px" }}
        className="animate-fadeIn"
      >
        {/* Skeleton cards for summary - less compact layout */}
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, 1fr)",
            gap: "8px",
            marginBottom: "12px",
          }}
          className="md:grid-cols-3 lg:grid-cols-4"
        >
          <SkeletonCard height="80px" />
          <SkeletonCard height="80px" />
          <SkeletonCard height="80px" />
          <SkeletonCard height="80px" />
        </div>

        {/* Skeleton for late payment summary */}
        <div style={{ marginBottom: "8px" }}>
          <SkeletonCard height="40px" />
        </div>

        {/* Skeleton chart */}
        <SkeletonChart />

        {/* Skeleton transactions table */}
        <SkeletonTable rows={5} />
      </div>
    );
  }

  // Return null if no data and not loading
  if (!projectionData) return null;

  return (
    <div
      className="space-y-3 sm:space-y-4 mx-0 flex flex-col"
      data-tour="cashflow-content"
      style={
        {
          "--content-padding": "1rem",
          "--content-padding-sm": "1.5rem",
          "--content-padding-md": "2rem",
        } as React.CSSProperties
      }
    >
      {/* Historical view banner from TimeMachine */}
      {isHistoricalView && (
        <div className="mb-4 p-2 bg-yellow-100 border border-yellow-300 rounded">
          <div className="flex items-center justify-between">
            <span>
              <span className="mr-2">🕒</span>
              Viewing snapshot from{" "}
              {historicalDate
                ? new Date(historicalDate).toLocaleDateString()
                : ""}
            </span>
            <button
              onClick={exitHistoricalView}
              className="px-2 py-1 bg-yellow-200 hover:bg-yellow-300 text-yellow-800 rounded text-sm"
            >
              Return to Current View
            </button>
          </div>
        </div>
      )}

      {/* Financial summary section with less compact responsive layout */}
      <div style={{ marginBottom: "12px" }}>
        {/* Main financial summary cards - less compact grid layout
             Using direct style attributes to avoid CSS conflicts */}
        <div
          style={{
            display: "grid",
            gap: "8px",
            marginBottom: "12px",
            // Use CSS variables to allow Tailwind classes to override these
            gridTemplateColumns: "var(--grid-cols, repeat(2, 1fr))",
          }}
          className="md:grid-cols-3 lg:grid-cols-4 cashflow-summary-grid"
        >
          <CashflowSummaryCard
            title="Current Balance"
            value={formatCurrency(projectionData.startingBalance || 0)}
            subtitle="Today"
            icon={
              <svg
                className="text-secondary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            }
            cardClass="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
            valueClass="text-primary"
          />

          <CashflowSummaryCard
            title="Projected Balance"
            value={formatCurrency(projectionData.endingBalance || 0)}
            subtitle={`in ${timeframe} days`}
            icon={
              <svg
                className={`${
                  (projectionData.endingBalance || 0) >= 0
                    ? "text-success"
                    : "text-accent"
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth="2"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"
                />
              </svg>
            }
            cardClass={
              (projectionData.endingBalance || 0) >= 0
                ? "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30"
                : "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30"
            }
            valueClass={
              (projectionData.endingBalance || 0) >= 0
                ? "text-success"
                : "text-accent"
            }
          />

          <CashflowSummaryCard
            title="Money In"
            value={formatCurrency(totalInflows)}
            subtitle="Incoming payments"
            icon={
              <svg
                className="text-success"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            }
            cardClass="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30"
            valueClass="text-success"
          />

          <CashflowSummaryCard
            title="Money Out"
            value={formatCurrency(totalOutflows)}
            subtitle="Outgoing expenses"
            icon={
              <svg
                className="text-accent"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                  clipRule="evenodd"
                  transform="rotate(180, 10, 10)"
                />
              </svg>
            }
            cardClass="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30"
            valueClass="text-accent"
          />
        </div>

        {/* Late payment summary - single expandable component */}
        <div style={{ maxWidth: "100%", marginBottom: "-8px" }}>
          <LatePaymentsSummary />
        </div>
      </div>

      {/* Chart controls - mobile-first responsive layout */}
      <div className="px-2 sm:px-3 md:px-4 py-2 mb-1 overflow-x-auto -mx-2 -mt-4">
        <div className="flex flex-nowrap items-center justify-between gap-2 sm:gap-3 md:gap-4 min-w-max">
          {/* Left side controls */}
          <div className="flex flex-nowrap items-center gap-1 sm:gap-3 md:gap-4">
            {/* Timeframe Selector */}
            <div className="flex items-center">
              {/* Desktop version */}
              <select
                id="timeframe"
                value={timeframe}
                onChange={handleTimeframeChange}
                className="form-select text-xs sm:text-sm py-2 px-3 rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-secondary dark:focus:border-secondary focus:ring focus:ring-secondary/20 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium min-h-[40px] hidden sm:block"
                style={{
                  minWidth: "120px",
                  width: "auto",
                  maxWidth: "150px",
                }}
                aria-label="Select timeframe"
              >
                <option value={30}>30 days</option>
                <option value={60}>60 days</option>
                <option value={90}>90 days</option>
                <option value={120}>120 days</option>
                <option value={150}>150 days</option>
                <option value={180}>180 days</option>
                <option value={210}>210 days</option>
              </select>

              {/* Mobile version */}
              <select
                id="timeframe-mobile"
                value={timeframe}
                onChange={handleTimeframeChange}
                className="form-select text-xs py-2 px-1 rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-secondary dark:focus:border-secondary focus:ring focus:ring-secondary/20 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium min-h-[40px] block sm:hidden"
                style={{
                  minWidth: "70px",
                  width: "auto",
                  maxWidth: "90px",
                }}
                aria-label="Select timeframe"
              >
                <option value={30}>30d</option>
                <option value={60}>60d</option>
                <option value={90}>90d</option>
                <option value={120}>120d</option>
                <option value={150}>150d</option>
                <option value={180}>180d</option>
                <option value={210}>210d</option>
              </select>
            </div>

            {/* Label toggle button */}
            <button
              onClick={toggleLabels}
              className="flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-2 text-xs sm:text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors whitespace-nowrap min-h-[40px]"
              title="Toggle transaction labels"
            >
              <div
                className={`w-3.5 sm:w-4 h-3.5 sm:h-4 border border-current ${
                  labelsVisible ? "bg-secondary" : "bg-transparent"
                }`}
              ></div>
              <span className="ml-1 sm:ml-2 font-medium text-xs sm:text-sm">
                Labels
              </span>
            </button>

            {/* Time Machine controls */}
            <TimeMachine />
          </div>

          {/* Right side controls */}
          <div className="flex items-center">
            {/* Deal scenarios toggle */}
            <ScenarioToggle
              showScenarios={showScenarios}
              onToggle={() => setShowScenarios(!showScenarios)}
            />
          </div>
        </div>
      </div>

      <div
        className="flex justify-center w-full px-1 sm:px-2 overflow-visible"
        style={{
          width: "100%",
          maxWidth: "100%",
          position: "relative", // Create stacking context
          zIndex: 1, // Base z-index for proper stacking
        }}
      >
        <CashflowChart
          labelsVisible={labelsVisible}
          height="46vh"
          minHeight="290px"
          maxHeight="460px"
        />
      </div>
      <TransactionsList />
    </div>
  );
};

export default ContentDisplay;
