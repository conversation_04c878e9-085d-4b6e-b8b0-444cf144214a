import { HarvestInvoiceService } from '../../../../src/services/harvest/invoice-service';
import { HarvestApiClient } from '../../../../src/api/clients/harvest-api-client';
import { ActivityService } from '../../../../src/api/services/activity-service';
import type { HarvestInvoice, HarvestInvoiceListResponse } from '../../../../src/types/api';

jest.mock('../../../../src/api/clients/harvest-api-client');
jest.mock('../../../../src/api/services/activity-service');

describe('HarvestInvoiceService', () => {
  let service: HarvestInvoiceService;
  let mockApiClient: jest.Mocked<HarvestApiClient>;
  let mockActivityService: jest.Mocked<ActivityService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockApiClient = new HarvestApiClient('mock-token', '12345') as jest.Mocked<HarvestApiClient>;
    mockActivityService = new ActivityService(null as any) as jest.Mocked<ActivityService>;
    service = new HarvestInvoiceService(mockApiClient, mockActivityService);
  });

  describe('getInvoices', () => {
    const mockInvoices: HarvestInvoice[] = [
      {
        id: 1,
        client: { id: 101, name: 'Client A' },
        number: 'INV-001',
        purchase_order: 'PO-123',
        amount: 5000,
        due_amount: 0,
        tax: 500,
        tax_amount: 500,
        tax2: 0,
        tax2_amount: 0,
        discount: 0,
        discount_amount: 0,
        subject: 'January services',
        notes: 'Thank you for your business',
        state: 'paid',
        period_start: '2024-01-01',
        period_end: '2024-01-31',
        issue_date: '2024-02-01',
        due_date: '2024-03-01',
        payment_term: 'net 30',
        sent_at: '2024-02-01T10:00:00Z',
        paid_at: '2024-02-15T14:30:00Z',
        closed_at: '2024-02-15T14:30:00Z',
        created_at: '2024-02-01T09:00:00Z',
        updated_at: '2024-02-15T14:30:00Z'
      },
      {
        id: 2,
        client: { id: 102, name: 'Client B' },
        number: 'INV-002',
        purchase_order: null,
        amount: 3000,
        due_amount: 3000,
        tax: 300,
        tax_amount: 300,
        tax2: 0,
        tax2_amount: 0,
        discount: 10,
        discount_amount: 300,
        subject: 'February services',
        notes: '',
        state: 'open',
        period_start: '2024-02-01',
        period_end: '2024-02-29',
        issue_date: '2024-03-01',
        due_date: '2024-03-31',
        payment_term: 'net 30',
        sent_at: '2024-03-01T10:00:00Z',
        paid_at: null,
        closed_at: null,
        created_at: '2024-03-01T09:00:00Z',
        updated_at: '2024-03-01T10:00:00Z'
      }
    ];

    it('should fetch all invoices without filters', async () => {
      const mockResponse: HarvestInvoiceListResponse = {
        invoices: mockInvoices,
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getInvoices();

      expect(result).toEqual(mockInvoices);
      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices', {
        per_page: 100,
        page: 1
      });
    });

    it('should fetch invoices with date filters', async () => {
      const mockResponse: HarvestInvoiceListResponse = {
        invoices: [mockInvoices[0]],
        per_page: 100,
        total_pages: 1,
        total_entries: 1,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getInvoices({
        from: '2024-01-01',
        to: '2024-01-31'
      });

      expect(result).toEqual([mockInvoices[0]]);
      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices', {
        per_page: 100,
        page: 1,
        from: '2024-01-01',
        to: '2024-01-31'
      });
    });

    it('should handle pagination', async () => {
      const page1Response: HarvestInvoiceListResponse = {
        invoices: [mockInvoices[0]],
        per_page: 1,
        total_pages: 2,
        total_entries: 2,
        next_page: 2,
        previous_page: null,
        page: 1
      };

      const page2Response: HarvestInvoiceListResponse = {
        invoices: [mockInvoices[1]],
        per_page: 1,
        total_pages: 2,
        total_entries: 2,
        next_page: null,
        previous_page: 1,
        page: 2
      };

      mockApiClient.get
        .mockResolvedValueOnce(page1Response)
        .mockResolvedValueOnce(page2Response);

      const result = await service.getInvoices();

      expect(result).toEqual(mockInvoices);
      expect(mockApiClient.get).toHaveBeenCalledTimes(2);
      expect(mockApiClient.get).toHaveBeenNthCalledWith(1, '/invoices', { per_page: 100, page: 1 });
      expect(mockApiClient.get).toHaveBeenNthCalledWith(2, '/invoices', { per_page: 100, page: 2 });
    });

    it('should filter by client', async () => {
      const mockResponse: HarvestInvoiceListResponse = {
        invoices: [mockInvoices[0]],
        per_page: 100,
        total_pages: 1,
        total_entries: 1,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getInvoices({ client_id: 101 });

      expect(result).toEqual([mockInvoices[0]]);
      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices', {
        per_page: 100,
        page: 1,
        client_id: 101
      });
    });

    it('should filter by state', async () => {
      const mockResponse: HarvestInvoiceListResponse = {
        invoices: [mockInvoices[1]],
        per_page: 100,
        total_pages: 1,
        total_entries: 1,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getInvoices({ state: 'open' });

      expect(result).toEqual([mockInvoices[1]]);
      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices', {
        per_page: 100,
        page: 1,
        state: 'open'
      });
    });

    it('should handle API errors', async () => {
      mockApiClient.get.mockRejectedValue(new Error('API error'));

      await expect(service.getInvoices()).rejects.toThrow('API error');
    });
  });

  describe('getInvoiceById', () => {
    it('should fetch single invoice by ID', async () => {
      const mockInvoice = {
        id: 1,
        client: { id: 101, name: 'Client A' },
        number: 'INV-001',
        amount: 5000,
        state: 'paid'
      } as HarvestInvoice;

      mockApiClient.get.mockResolvedValue(mockInvoice);

      const result = await service.getInvoiceById(1);

      expect(result).toEqual(mockInvoice);
      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices/1');
    });

    it('should handle not found error', async () => {
      mockApiClient.get.mockRejectedValue(new Error('Invoice not found'));

      await expect(service.getInvoiceById(999)).rejects.toThrow('Invoice not found');
    });
  });

  describe('getLateInvoices', () => {
    it('should return only overdue invoices', async () => {
      const today = new Date();
      const pastDue = new Date(today);
      pastDue.setDate(today.getDate() - 10);
      const futureDue = new Date(today);
      futureDue.setDate(today.getDate() + 10);

      const invoices: HarvestInvoice[] = [
        {
          id: 1,
          client: { id: 101, name: 'Client A' },
          number: 'INV-001',
          amount: 5000,
          due_amount: 5000,
          due_date: pastDue.toISOString().split('T')[0],
          state: 'open'
        } as HarvestInvoice,
        {
          id: 2,
          client: { id: 102, name: 'Client B' },
          number: 'INV-002',
          amount: 3000,
          due_amount: 3000,
          due_date: futureDue.toISOString().split('T')[0],
          state: 'open'
        } as HarvestInvoice,
        {
          id: 3,
          client: { id: 103, name: 'Client C' },
          number: 'INV-003',
          amount: 2000,
          due_amount: 0,
          due_date: pastDue.toISOString().split('T')[0],
          state: 'paid'
        } as HarvestInvoice
      ];

      const mockResponse: HarvestInvoiceListResponse = {
        invoices,
        per_page: 100,
        total_pages: 1,
        total_entries: 3,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getLateInvoices();

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(1);
      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices', {
        per_page: 100,
        page: 1,
        state: 'open'
      });
    });

    it('should calculate days overdue', async () => {
      const today = new Date();
      const tenDaysAgo = new Date(today);
      tenDaysAgo.setDate(today.getDate() - 10);

      const invoices: HarvestInvoice[] = [
        {
          id: 1,
          client: { id: 101, name: 'Client A' },
          number: 'INV-001',
          amount: 5000,
          due_amount: 5000,
          due_date: tenDaysAgo.toISOString().split('T')[0],
          state: 'open'
        } as HarvestInvoice
      ];

      const mockResponse: HarvestInvoiceListResponse = {
        invoices,
        per_page: 100,
        total_pages: 1,
        total_entries: 1,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getLateInvoices();

      expect(result[0]).toHaveProperty('daysOverdue');
      expect(result[0].daysOverdue).toBeGreaterThanOrEqual(9); // Account for test execution time
      expect(result[0].daysOverdue).toBeLessThanOrEqual(11);
    });
  });

  describe('getInvoiceStatistics', () => {
    it('should calculate invoice statistics', async () => {
      const invoices: HarvestInvoice[] = [
        {
          id: 1,
          amount: 5000,
          due_amount: 0,
          state: 'paid',
          paid_at: '2024-02-15T14:30:00Z',
          due_date: '2024-03-01'
        } as HarvestInvoice,
        {
          id: 2,
          amount: 3000,
          due_amount: 3000,
          state: 'open',
          due_date: '2024-03-31'
        } as HarvestInvoice,
        {
          id: 3,
          amount: 2000,
          due_amount: 2000,
          state: 'open',
          due_date: '2024-01-01' // Overdue
        } as HarvestInvoice,
        {
          id: 4,
          amount: 1000,
          due_amount: 500,
          state: 'open',
          due_date: '2024-04-15'
        } as HarvestInvoice
      ];

      const mockResponse: HarvestInvoiceListResponse = {
        invoices,
        per_page: 100,
        total_pages: 1,
        total_entries: 4,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getInvoiceStatistics();

      expect(result).toEqual({
        totalInvoices: 4,
        totalAmount: 11000,
        totalPaid: 5000,
        totalOutstanding: 5500,
        overdueAmount: 2000,
        overdueCount: 1,
        averagePaymentTime: expect.any(Number)
      });
    });

    it('should handle empty invoice list', async () => {
      const mockResponse: HarvestInvoiceListResponse = {
        invoices: [],
        per_page: 100,
        total_pages: 0,
        total_entries: 0,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await service.getInvoiceStatistics();

      expect(result).toEqual({
        totalInvoices: 0,
        totalAmount: 0,
        totalPaid: 0,
        totalOutstanding: 0,
        overdueAmount: 0,
        overdueCount: 0,
        averagePaymentTime: 0
      });
    });
  });

  describe('syncInvoices', () => {
    it('should sync invoices and log activity', async () => {
      const mockInvoices: HarvestInvoice[] = [
        {
          id: 1,
          client: { id: 101, name: 'Client A' },
          number: 'INV-001',
          amount: 5000,
          state: 'paid'
        } as HarvestInvoice,
        {
          id: 2,
          client: { id: 102, name: 'Client B' },
          number: 'INV-002',
          amount: 3000,
          state: 'open'
        } as HarvestInvoice
      ];

      const mockResponse: HarvestInvoiceListResponse = {
        invoices: mockInvoices,
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(mockResponse);
      mockActivityService.create.mockResolvedValue(undefined);

      const result = await service.syncInvoices();

      expect(result).toEqual({
        syncedCount: 2,
        totalAmount: 8000,
        errors: []
      });

      expect(mockActivityService.create).toHaveBeenCalledWith({
        action: 'harvest_sync',
        targetType: 'invoice',
        targetId: null,
        metadata: {
          syncedCount: 2,
          totalAmount: 8000,
          source: 'harvest'
        }
      });
    });

    it('should handle sync errors gracefully', async () => {
      mockApiClient.get.mockRejectedValue(new Error('API unavailable'));

      const result = await service.syncInvoices();

      expect(result).toEqual({
        syncedCount: 0,
        totalAmount: 0,
        errors: ['API unavailable']
      });

      expect(mockActivityService.create).toHaveBeenCalledWith({
        action: 'harvest_sync_failed',
        targetType: 'invoice',
        targetId: null,
        metadata: {
          error: 'API unavailable',
          source: 'harvest'
        }
      });
    });
  });
});