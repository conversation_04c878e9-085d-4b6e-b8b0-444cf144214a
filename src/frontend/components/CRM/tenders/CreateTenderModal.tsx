import React, { useState } from 'react'; import { useQueryClient } from 'react-query'; import { XMarkIcon } from '@heroicons/react/24/outline'; import { useCreateTender } from '../../../api/tenders'; import type { BaseTender } from '../../../../types/shared-types'; interface CreateTenderModalProps { isOpen: boolean; onClose: () => void; } export const CreateTenderModal: React.FC<CreateTenderModalProps> = ({ isOpen, onClose }) => { const queryClient = useQueryClient(); const createTenderMutation = useCreateTender(); const [formData, setFormData] = useState({ requestNo: '', summary: '', issuedBy: '', closingDate: '', unspsc: '', tenderUrl: '', notes: '' }); const [errors, setErrors] = useState<Record<string, string>>({}); if (!isOpen) return null; const validateForm = () => { const newErrors: Record<string, string> = {}; if (!formData.requestNo) { newErrors.requestNo = 'Request number is required'; } if (!formData.summary) { newErrors.summary = 'Summary is required'; } if (!formData.issuedBy) { newErrors.issuedBy = 'Issuer is required'; } if (!formData.closingDate) { newErrors.closingDate = 'Closing date is required'; } else if (new Date(formData.closingDate) < new Date()) { newErrors.closingDate = 'Closing date must be in the future'; } setErrors(newErrors); return Object.keys(newErrors).length === 0; }; const handleSubmit = async (e: React.FormEvent) => { e.preventDefault(); if (!validateForm()) return; try { const tenderData: Partial<BaseTender> = { requestNo: formData.requestNo, summary: formData.summary, issuedBy: formData.issuedBy, closingDate: new Date(formData.closingDate).toISOString(), unspsc: formData.unspsc || undefined, tenderUrl: formData.tenderUrl || undefined, notes: formData.notes || undefined, status: 'Current', type: 'Tender', qualificationStatus: 'new' }; await createTenderMutation.mutateAsync(tenderData); // Success - close modal onClose(); // Reset form setFormData({ requestNo: '', summary: '', issuedBy: '', closingDate: '', unspsc: '', tenderUrl: '', notes: '' }); } catch (error) { console.error('Failed to create tender:', error); setErrors({ submit: 'Failed to create tender. Please try again.' }); } }; return ( <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"> <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto"> {/* Header */} <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"> <h2 className="text-xl font-semibold text-gray-900 dark:text-white"> Create Tender </h2> <button onClick={onClose} className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors" > <XMarkIcon className="w-5 h-5 text-gray-500" /> </button> </div> {/* Form */} <form onSubmit={handleSubmit} className="p-6 space-y-4"> {/* Request Number */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Request Number * </label> <input type="text" value={formData.requestNo} onChange={(e) => setFormData({ ...formData, requestNo: e.target.value })} className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white ${ errors.requestNo ? 'border-red-500' : 'border-gray-300 dark:border-gray-600' } focus:outline-none focus:ring-2 focus:ring-purple-500`} placeholder="e.g., GEN250120" /> {errors.requestNo && ( <p className="mt-1 text-xs text-red-500">{errors.requestNo}</p> )} </div> {/* Summary */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Summary * </label> <textarea value={formData.summary} onChange={(e) => setFormData({ ...formData, summary: e.target.value })} rows={2} className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white ${ errors.summary ? 'border-red-500' : 'border-gray-300 dark:border-gray-600' } focus:outline-none focus:ring-2 focus:ring-purple-500`} placeholder="Brief description of the tender" /> {errors.summary && ( <p className="mt-1 text-xs text-red-500">{errors.summary}</p> )} </div> {/* Issued By */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Issued By * </label> <input type="text" value={formData.issuedBy} onChange={(e) => setFormData({ ...formData, issuedBy: e.target.value })} className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white ${ errors.issuedBy ? 'border-red-500' : 'border-gray-300 dark:border-gray-600' } focus:outline-none focus:ring-2 focus:ring-purple-500`} placeholder="e.g., Edith Cowan University" /> {errors.issuedBy && ( <p className="mt-1 text-xs text-red-500">{errors.issuedBy}</p> )} </div> {/* Closing Date */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Closing Date * </label> <input type="datetime-local" value={formData.closingDate} onChange={(e) => setFormData({ ...formData, closingDate: e.target.value })} className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white ${ errors.closingDate ? 'border-red-500' : 'border-gray-300 dark:border-gray-600' } focus:outline-none focus:ring-2 focus:ring-purple-500`} /> {errors.closingDate && ( <p className="mt-1 text-xs text-red-500">{errors.closingDate}</p> )} </div> {/* UNSPSC */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> UNSPSC Code </label> <input type="text" value={formData.unspsc} onChange={(e) => setFormData({ ...formData, unspsc: e.target.value })} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500" placeholder="e.g., Management advisory services - (100%)" /> </div> {/* Tender URL */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Tender URL </label> <input type="url" value={formData.tenderUrl} onChange={(e) => setFormData({ ...formData, tenderUrl: e.target.value })} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500" placeholder="https://..." /> </div> {/* Notes */} <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Notes </label> <textarea value={formData.notes} onChange={(e) => setFormData({ ...formData, notes: e.target.value })} rows={3} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-900 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-purple-500" placeholder="Any additional notes..." /> </div> {/* Error Message */} {errors.submit && ( <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg"> <p className="text-sm text-red-600 dark:text-red-400">{errors.submit}</p> </div> )} {/* Actions */} <div className="flex justify-end gap-3 pt-4"> <button type="button" onClick={onClose} className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors" > Cancel </button> <button type="submit" disabled={createTenderMutation.isLoading} className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" > {createTenderMutation.isLoading ? 'Creating...' : 'Create Tender'} </button> </div> </form> </div> </div> ); }; export default CreateTenderModal;