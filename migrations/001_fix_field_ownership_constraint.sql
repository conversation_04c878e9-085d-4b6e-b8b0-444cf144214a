-- Migration: Fix field_ownership CHECK constraint to include 'deal' and 'system'
-- Date: 2025-06-14
-- Purpose: The field_ownership table's CHECK constraint is missing 'deal' and 'system' as valid owner_system values,
--          causing errors when linking deals to estimates. This migration updates the constraint to include all
--          values expected by the application code.
--
-- Also fixes: v_soft_deleted_records view references non-existent 'title' column in tender table

-- First, drop the problematic view that references the wrong column
DROP VIEW IF EXISTS v_soft_deleted_records;

-- Since SQLite doesn't support ALTER TABLE DROP CONSTRAINT, we need to recreate the table
-- But we'll do it in a way that preserves other views by using a transaction

-- Step 1: Check if the field_ownership table exists
-- If it doesn't exist, create it with the correct constraint
CREATE TABLE IF NOT EXISTS field_ownership (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL CHECK(entity_type IN ('company', 'contact', 'deal', 'estimate')),
  entity_id TEXT NOT NULL,
  field_name TEXT NOT NULL,
  owner_system TEXT NOT NULL CHECK(owner_system IN ('manual', 'hubspot', 'harvest', 'xero', 'estimate', 'deal', 'system')),
  set_at TEXT NOT NULL DEFAULT (datetime('now')),
  set_by TEXT,
  locked INTEGER DEFAULT 0 CHECK(locked IN (0, 1)),
  lock_reason TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  UNIQUE(entity_type, entity_id, field_name)
);

-- Step 2: If the table already exists, we need to migrate it
-- First, create a backup table
CREATE TABLE IF NOT EXISTS field_ownership_backup AS 
SELECT * FROM field_ownership 
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='field_ownership');

-- Step 3: Drop the old table if it exists
DROP TABLE IF EXISTS field_ownership;

-- Step 4: Create the new table with the updated constraint
CREATE TABLE field_ownership (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL CHECK(entity_type IN ('company', 'contact', 'deal', 'estimate')),
  entity_id TEXT NOT NULL,
  field_name TEXT NOT NULL,
  owner_system TEXT NOT NULL CHECK(owner_system IN ('manual', 'hubspot', 'harvest', 'xero', 'estimate', 'deal', 'system')),
  set_at TEXT NOT NULL DEFAULT (datetime('now')),
  set_by TEXT,
  locked INTEGER DEFAULT 0 CHECK(locked IN (0, 1)),
  lock_reason TEXT,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  UNIQUE(entity_type, entity_id, field_name)
);

-- Step 5: Restore data from backup if it exists
INSERT INTO field_ownership 
SELECT * FROM field_ownership_backup 
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='field_ownership_backup');

-- Step 6: Drop the backup table
DROP TABLE IF EXISTS field_ownership_backup;

-- Step 7: Create indexes
CREATE INDEX IF NOT EXISTS idx_field_ownership_entity ON field_ownership(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_field_ownership_owner_system ON field_ownership(owner_system);
CREATE INDEX IF NOT EXISTS idx_field_ownership_deleted_at ON field_ownership(deleted_at);

-- Step 8: Recreate the v_soft_deleted_records view with the correct column reference
CREATE VIEW IF NOT EXISTS v_soft_deleted_records AS
  SELECT 'company' as table_name, id, name as display_name, deleted_at, updated_by as deleted_by FROM company WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'contact', id, COALESCE(full_name, email) as display_name, deleted_at, updated_by as deleted_by FROM contact WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'deal', id, name, deleted_at, updated_by as deleted_by FROM deal WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'estimate', id, client_name || ' - ' || COALESCE(project_name, 'Untitled'), deleted_at, updated_by as deleted_by FROM estimate WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'project', id, name, deleted_at, updated_by as deleted_by FROM project WHERE deleted_at IS NOT NULL
  UNION ALL
  SELECT 'tender', id, summary, deleted_at, updated_by as deleted_by FROM tender WHERE deleted_at IS NOT NULL;