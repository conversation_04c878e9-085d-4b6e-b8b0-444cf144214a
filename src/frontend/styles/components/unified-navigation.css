/*
 * Unified Navigation Styles
 *
 * This file contains all styles for the unified navigation component.
 * It uses modern CSS techniques to create an adaptive navigation that
 * works across all screen sizes.
 */

/* Base styles for navigation components */
.mobile-navigation,
.desktop-navigation {
  display: flex;
  align-items: center;
}

/* Mobile Navigation (bottom bar) */
.mobile-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: theme("colors.white");
  border-top: 1px solid theme("colors.gray.200");
  z-index: 50;
  padding-bottom: env(safe-area-inset-bottom, 0px);
  height: calc(4rem + env(safe-area-inset-bottom, 0px));
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  gap: 0;
  box-shadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.1);
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
}

/* Hide scrollbar */
.mobile-navigation::-webkit-scrollbar {
  display: none;
}

/* Scroll indicators */
.mobile-navigation::before,
.mobile-navigation::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 10;
}

.mobile-navigation::before {
  left: 0;
  background: linear-gradient(to right, theme("colors.white"), transparent);
}

.mobile-navigation::after {
  right: 0;
  background: linear-gradient(to left, theme("colors.white"), transparent);
}

.dark .mobile-navigation::before {
  background: linear-gradient(to right, theme("colors.gray.800"), transparent);
}

.dark .mobile-navigation::after {
  background: linear-gradient(to left, theme("colors.gray.800"), transparent);
}

.dark .mobile-navigation {
  background-color: theme("colors.gray.800");
  border-top-color: theme("colors.gray.700");
  box-shadow: 0 -1px 3px 0 rgba(0, 0, 0, 0.3);
}

/* Desktop Navigation (header tabs) */
.desktop-navigation {
  display: none; /* Hidden by default on mobile */
  height: 100%;
  gap: 1.5rem;
  margin-left: 1rem;
}

/* Navigation Tab Styles */
.nav-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  color: theme("colors.gray.500");
  transition: color 150ms, background-color 150ms;
  border: none;
  background: none;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
}

.dark .nav-tab {
  color: theme("colors.gray.400");
}

/* Mobile-specific tab styles */
.mobile-navigation .nav-tab {
  flex: 0 0 auto;
  min-width: 64px;
  width: auto;
  height: 100%;
  flex-direction: column;
  padding: 0.5rem 0.75rem;
  gap: 0.25rem;
  min-height: 44px; /* Ensure minimum touch target size */
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-navigation .nav-tab__icon {
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
}

.mobile-navigation .nav-tab__label {
  font-size: 0.625rem; /* 10px */
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Active tab indicator for mobile */
.mobile-navigation .nav-tab--active {
  position: relative;
}

.mobile-navigation .nav-tab--active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: currentColor;
}

/* Desktop-specific tab styles */
.desktop-navigation .nav-tab {
  height: 100%;
  padding: 0 0.25rem;
  position: relative;
  display: flex;
  align-items: center;
}

.desktop-navigation .nav-tab__content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.desktop-navigation .nav-tab__icon {
  width: 1.25rem;
  height: 1.25rem;
}

.desktop-navigation .nav-tab__label {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Active state styles */
.nav-tab--active {
  color: theme("colors.primary.600");
}

.dark .nav-tab--active {
  color: theme("colors.primary.400");
}

/* Color variants for active tabs */
.nav-tab--secondary {
  color: theme("colors.primary.600");
}

.dark .nav-tab--secondary {
  color: theme("colors.primary.400");
}

.nav-tab--amber {
  color: theme("colors.amber.500");
}

.dark .nav-tab--amber {
  color: theme("colors.amber.400");
}

.nav-tab--green {
  color: theme("colors.green.600");
}

.dark .nav-tab--green {
  color: theme("colors.green.400");
}

.nav-tab--gray {
  color: theme("colors.gray.600");
}

.dark .nav-tab--gray {
  color: theme("colors.gray.400");
}

.nav-tab--blue {
  color: theme("colors.blue.600");
}

.dark .nav-tab--blue {
  color: theme("colors.blue.400");
}

/* Desktop active indicator (bottom border) */
.desktop-navigation .nav-tab--active::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: currentColor;
}

/* NEW badge styles */
.nav-tab__badge {
  display: inline-flex;
  font-size: 0.5rem;
  padding: 0 2px;
  line-height: 1.2;
  font-weight: 500;
  border-radius: 2px;
  border-width: 1px;
  border-color: theme("colors.blue.300");
  color: theme("colors.blue.600");
  background-color: theme("colors.blue.50");
  text-transform: uppercase;
  letter-spacing: -0.025em;
  margin-left: 2px;
  position: relative;
  top: -0.25rem;
}

.dark .nav-tab__badge {
  border-color: theme("colors.blue.600");
  color: theme("colors.blue.300");
  background-color: rgba(30, 64, 175, 0.2);
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .mobile-navigation {
    display: none; /* Hide mobile navigation on larger screens */
  }

  .desktop-navigation {
    display: flex; /* Show desktop navigation on larger screens */
  }
}

/* Ensure content doesn't get hidden behind mobile navigation */
@media (max-width: 767px) {
  body {
    padding-bottom: calc(4rem + env(safe-area-inset-bottom, 0px));
  }
  
  /* Prevent any horizontal overflow on mobile */
  #root, .app-container, main {
    overflow-x: hidden;
    max-width: 100vw;
  }
  
  /* Ensure all containers respect viewport width */
  .max-w-6xl, .max-w-7xl, .max-w-\\[84rem\\] {
    max-width: 100vw;
  }
}
