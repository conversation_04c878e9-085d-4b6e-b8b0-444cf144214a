import { StaffUtilization } from "../../../../../services/harvest/time-report-service";
import { FixedPriceProject } from "../../../../api/harvest";
import { DEFAULT_HOURLY_COST_RATE } from "../../../../../constants/costs";

/**
 * Calculate billable amounts grouped by currency
 */
export const calculateBillableAmountsByCurrency = (
  utilizationData: StaffUtilization[]
): Record<string, number> => {
  return utilizationData.reduce(
    (acc: Record<string, number>, staff) => {
      if (staff.billableAmount && staff.currency) {
        if (!acc[staff.currency]) {
          acc[staff.currency] = 0;
        }
        acc[staff.currency] += staff.billableAmount;
      }
      return acc;
    },
    {}
  );
};

/**
 * Calculate fixed price income grouped by currency
 */
export const calculateFixedPriceIncomeByCurrency = (
  fixedPriceProjects: FixedPriceProject[]
): Record<string, number> => {
  return fixedPriceProjects.reduce(
    (acc: Record<string, number>, project) => {
      if (!acc[project.currency]) {
        acc[project.currency] = 0;
      }
      acc[project.currency] += project.billableAmount;
      return acc;
    },
    {}
  );
};

/**
 * Calculate staff cost based on utilization data
 */
export const calculateStaffCost = (
  utilizationData: StaffUtilization[],
  fromDate?: string,
  toDate?: string
): number => {
  // Calculate total cost using individual staff cost rates
  // For salaried staff (non-contractors), cap hours at their weekly capacity
  // For contractors, use actual hours worked
  console.log(
    "Starting summary staff cost calculation for all staff members"
  );

  return utilizationData.reduce((total, staff) => {
    const staffCostRate = staff.costRate || DEFAULT_HOURLY_COST_RATE;

    // Determine billable hours to use for cost calculation
    let hoursForCost: number;
    let periodCapacity: number = 0;

    if (staff.isContractor) {
      // Contractors: use actual hours worked
      hoursForCost = staff.totalHours;
    } else {
      // Salaried staff: always use full capacity regardless of actual hours worked
      // First, determine how many weeks the period covers
      const fromDateObj = fromDate ? new Date(fromDate) : null;
      const toDateObj = toDate ? new Date(toDate) : null;

      if (fromDateObj && toDateObj) {
        // Calculate number of weeks in the period
        const daysDifference = Math.ceil(
          (toDateObj.getTime() - fromDateObj.getTime()) /
            (1000 * 60 * 60 * 24)
        ) + 1;
        const weeksDifference = daysDifference / 7;

        // Calculate capacity for the period
        periodCapacity = staff.weeklyCapacity * weeksDifference;

        // For salaried staff, always use full period capacity
        // This reflects that salaried staff are paid their full salary
        // regardless of whether they work more or fewer hours than capacity
        hoursForCost = periodCapacity;
      } else {
        // If we don't have date range, use weekly capacity as fallback
        hoursForCost = staff.weeklyCapacity;
        periodCapacity = staff.weeklyCapacity;
      }
    }

    // Calculate individual staff cost
    const individualStaffCost = hoursForCost * staffCostRate;

    // Log individual staff cost calculation for summary
    console.log(
      `Summary cost calculation for ${staff.userName}:`,
      {
        isContractor: staff.isContractor,
        costRate: staffCostRate,
        totalHours: staff.totalHours,
        weeklyCapacity: staff.weeklyCapacity,
        periodCapacity: periodCapacity || "N/A",
        hoursUsedForCost: hoursForCost,
        costCalculation: `${hoursForCost} hours × $${staffCostRate}/hour = $${individualStaffCost}`,
        explanation: staff.isContractor
          ? "CONTRACTOR: Using all actual hours worked for cost calculation"
          : "SALARIED: Using full period capacity for cost calculation (fixed salary)",
        actualHoursLessThanCapacity:
          !staff.isContractor && staff.totalHours < periodCapacity,
        actualHoursMoreThanCapacity:
          !staff.isContractor && staff.totalHours > periodCapacity,
        staffCost: individualStaffCost,
        runningTotal: total + individualStaffCost,
      }
    );

    return total + hoursForCost * staffCostRate;
  }, 0);
};

/**
 * Calculate non-billable cost
 */
export const calculateNonBillableCost = (
  utilizationData: StaffUtilization[],
  fromDate?: string,
  toDate?: string,
  excludeLeave: boolean = false
): number => {
  return utilizationData.reduce(
    (total, staff) => {
      // Skip contractors - we only care about salaried staff with fixed costs
      if (staff.isContractor) return total;

      const staffCostRate = staff.costRate || DEFAULT_HOURLY_COST_RATE;

      // Calculate period capacity
      let periodCapacity = 0;
      const fromDateObj = fromDate ? new Date(fromDate) : null;
      const toDateObj = toDate ? new Date(toDate) : null;

      if (fromDateObj && toDateObj) {
        const daysDifference = Math.ceil(
          (toDateObj.getTime() - fromDateObj.getTime()) /
            (1000 * 60 * 60 * 24)
        ) + 1;
        const weeksDifference = daysDifference / 7;
        periodCapacity = staff.weeklyCapacity * weeksDifference;
      } else {
        periodCapacity = staff.weeklyCapacity;
      }

      // Calculate non-billable hours (excluding leave if excludeLeave is true)
      const nonBillableHours = excludeLeave
        ? staff.totalHours - staff.billableHours - staff.leaveHours
        : staff.totalHours - staff.billableHours;

      // Calculate how much of the non-billable time is at the expense of available billable time
      // This is the minimum of:
      // 1. Non-billable hours
      // 2. Available capacity minus billable hours (can't be negative)
      const nonBillableAtExpenseOfBillable = Math.min(
        nonBillableHours,
        Math.max(0, periodCapacity - staff.billableHours)
      );

      // Calculate the cost of this non-billable time
      const staffNonBillableCost = nonBillableAtExpenseOfBillable * staffCostRate;

      // Log the non-billable cost calculation
      console.log(
        `Non-billable cost calculation for ${staff.userName}:`,
        {
          periodCapacity,
          billableHours: staff.billableHours,
          totalHours: staff.totalHours,
          leaveHours: staff.leaveHours,
          nonBillableHours,
          nonBillableAtExpenseOfBillable,
          costRate: staffCostRate,
          nonBillableCost: staffNonBillableCost,
          explanation:
            "This represents the cost of non-billable time that could have been used for billable work",
        }
      );

      return total + staffNonBillableCost;
    },
    0
  );
};

/**
 * Calculate period capacity for a staff member
 */
export const calculatePeriodCapacity = (
  weeklyCapacity: number,
  fromDate?: string,
  toDate?: string
): number => {
  let periodCapacity = 0;
  const fromDateObj = fromDate ? new Date(fromDate) : null;
  const toDateObj = toDate ? new Date(toDate) : null;

  if (fromDateObj && toDateObj) {
    const daysDifference = Math.ceil(
      (toDateObj.getTime() - fromDateObj.getTime()) /
        (1000 * 60 * 60 * 24)
    ) + 1;
    const weeksDifference = daysDifference / 7;
    periodCapacity = weeklyCapacity * weeksDifference;
  } else {
    periodCapacity = weeklyCapacity;
  }

  return periodCapacity;
};
