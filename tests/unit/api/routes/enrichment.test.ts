/**
 * Enrichment API Routes Unit Tests
 * 
 * Tests for enrichment API endpoints including:
 * - Company enrichment triggers
 * - Enrichment data retrieval
 * - Status checks
 * - Statistics endpoints
 * - Error handling
 * - Validation
 */

import request from 'supertest';
import express from 'express';
import enrichmentRoutes from '../../../../src/api/routes/enrichment';
import { enrichmentService } from '../../../../src/api/services/enrichment/enrichment-service';
import { CompanyRepository } from '../../../../src/api/repositories/company-repository';
import { ContactRepository } from '../../../../src/api/repositories/contact-repository';
import { EnrichmentRepository } from '../../../../src/api/repositories/enrichment-repository';

// Mock dependencies
jest.mock('../../../../src/api/services/enrichment/enrichment-service');
jest.mock('../../../../src/api/repositories/company-repository');
jest.mock('../../../../src/api/repositories/contact-repository');
jest.mock('../../../../src/api/repositories/enrichment-repository');

describe('Enrichment API Routes', () => {
  let app: express.Application;
  let mockEnrichmentService: jest.Mocked<typeof enrichmentService>;
  let mockCompanyRepository: jest.Mocked<CompanyRepository>;
  let mockContactRepository: jest.Mocked<ContactRepository>;
  let mockEnrichmentRepository: jest.Mocked<EnrichmentRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup Express app with routes
    app = express();
    app.use(express.json());
    app.use('/api/enrichment', enrichmentRoutes);
    
    // Add error handler
    app.use((error: any, req: any, res: any, next: any) => {
      res.status(500).json({ error: error.message });
    });

    // Setup mocks
    mockEnrichmentService = enrichmentService as jest.Mocked<typeof enrichmentService>;
    mockCompanyRepository = new CompanyRepository() as jest.Mocked<CompanyRepository>;
    mockContactRepository = new ContactRepository() as jest.Mocked<ContactRepository>;
    mockEnrichmentRepository = new EnrichmentRepository() as jest.Mocked<EnrichmentRepository>;

    // Mock constructors to return our mocks
    (CompanyRepository as jest.MockedClass<typeof CompanyRepository>).mockImplementation(
      () => mockCompanyRepository
    );
    (ContactRepository as jest.MockedClass<typeof ContactRepository>).mockImplementation(
      () => mockContactRepository
    );
    (EnrichmentRepository as jest.MockedClass<typeof EnrichmentRepository>).mockImplementation(
      () => mockEnrichmentRepository
    );
  });

  // Test data factories
  const createMockCompany = () => ({
    id: 'comp-123',
    name: 'Test Company Pty Ltd',
    industry: 'Technology',
    website: 'https://test.com.au',
    address: '123 Collins St, Melbourne VIC 3000',
    source: 'manual',
    radarState: 'active',
    priority: 'High',
    currentSpend: 5000,
    potentialSpend: 10000,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'test-user',
    updatedBy: 'test-user',
    lastEnrichedAt: null,
    enrichmentStatus: null
  });

  const createMockEnrichmentResult = () => [
    {
      source: 'abn_lookup',
      success: true,
      data: {
        abn: '12 ***********',
        abnStatus: 'Active',
        entityType: 'Australian Private Company',
        gstStatus: 'Registered',
        dataQuality: 'high'
      },
      confidence: 0.95
    }
  ];

  describe('POST /api/enrichment/company/:id', () => {
    it('should successfully enrich a company', async () => {
      const company = createMockCompany();
      const enrichmentResults = createMockEnrichmentResult();
      const enrichmentData = [
        {
          id: 'enrich-1',
          source: 'abn_lookup',
          data: { abn: '12 ***********' },
          confidence: 0.95
        }
      ];

      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockResolvedValue(enrichmentResults);
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue(enrichmentData);
      mockEnrichmentRepository.logEnrichmentAttempt.mockResolvedValue();

      const response = await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(200);

      expect(response.body).toHaveProperty('company');
      expect(response.body).toHaveProperty('enrichment');
      expect(response.body).toHaveProperty('results');
      expect(response.body.results).toHaveLength(1);
      expect(response.body.results[0].success).toBe(true);

      // Verify service calls
      expect(mockEnrichmentService.enrichCompany).toHaveBeenCalledWith(company, undefined);
      expect(mockEnrichmentService.getCompanyEnrichment).toHaveBeenCalledWith('comp-123');
      expect(mockEnrichmentRepository.logEnrichmentAttempt).toHaveBeenCalledWith({
        entityType: 'company',
        entityId: 'comp-123',
        source: 'abn_lookup',
        status: 'success',
        errorMessage: undefined,
        responseTimeMs: expect.any(Number),
        apiCreditsUsed: 0
      });
    });

    it('should enrich with specified sources', async () => {
      const company = createMockCompany();
      const enrichmentResults = createMockEnrichmentResult();

      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockResolvedValue(enrichmentResults);
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue([]);
      mockEnrichmentRepository.logEnrichmentAttempt.mockResolvedValue();

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send({ sources: ['abn_lookup'] })
        .expect(200);

      expect(mockEnrichmentService.enrichCompany).toHaveBeenCalledWith(company, ['abn_lookup']);
    });

    it('should return 404 for non-existent company', async () => {
      mockCompanyRepository.getCompanyById.mockReturnValue(null);

      const response = await request(app)
        .post('/api/enrichment/company/non-existent')
        .send()
        .expect(404);

      expect(response.body).toEqual({ error: 'Company not found' });
      expect(mockEnrichmentService.enrichCompany).not.toHaveBeenCalled();
    });

    it('should handle enrichment service errors', async () => {
      const company = createMockCompany();
      
      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockRejectedValue(new Error('Service error'));

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(500);

      expect(mockEnrichmentService.enrichCompany).toHaveBeenCalled();
    });

    it('should log failed enrichment attempts', async () => {
      const company = createMockCompany();
      const failedResults = [
        {
          source: 'abn_lookup',
          success: false,
          error: 'API rate limit exceeded',
          confidence: 0
        }
      ];

      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockResolvedValue(failedResults);
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue([]);
      mockEnrichmentRepository.logEnrichmentAttempt.mockResolvedValue();

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(200);

      expect(mockEnrichmentRepository.logEnrichmentAttempt).toHaveBeenCalledWith({
        entityType: 'company',
        entityId: 'comp-123',
        source: 'abn_lookup',
        status: 'failed',
        errorMessage: 'API rate limit exceeded',
        responseTimeMs: expect.any(Number),
        apiCreditsUsed: 0
      });
    });

    it('should handle logging errors gracefully', async () => {
      const company = createMockCompany();
      const enrichmentResults = createMockEnrichmentResult();

      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockResolvedValue(enrichmentResults);
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue([]);
      mockEnrichmentRepository.logEnrichmentAttempt.mockRejectedValue(new Error('Logging failed'));

      // Should still return success even if logging fails
      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(200);
    });
  });

  describe('GET /api/enrichment/company/:id', () => {
    it('should retrieve enrichment data for a company', async () => {
      const enrichmentData = [
        {
          id: 'enrich-1',
          source: 'abn_lookup',
          data: { abn: '12 ***********' },
          confidence: 0.95,
          enrichedAt: new Date().toISOString()
        }
      ];

      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue(enrichmentData);

      const response = await request(app)
        .get('/api/enrichment/company/comp-123')
        .expect(200);

      expect(response.body).toEqual({ enrichment: enrichmentData });
      expect(mockEnrichmentService.getCompanyEnrichment).toHaveBeenCalledWith('comp-123', undefined);
    });

    it('should filter by source when specified', async () => {
      const enrichmentData = [
        {
          id: 'enrich-1',
          source: 'abn_lookup',
          data: { abn: '12 ***********' },
          confidence: 0.95
        }
      ];

      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue(enrichmentData);

      await request(app)
        .get('/api/enrichment/company/comp-123?source=abn_lookup')
        .expect(200);

      expect(mockEnrichmentService.getCompanyEnrichment).toHaveBeenCalledWith('comp-123', 'abn_lookup');
    });

    it('should handle service errors', async () => {
      mockEnrichmentService.getCompanyEnrichment.mockRejectedValue(new Error('Database error'));

      await request(app)
        .get('/api/enrichment/company/comp-123')
        .expect(500);
    });
  });

  describe('GET /api/enrichment/company/:id/check', () => {
    it('should check if company needs enrichment', async () => {
      const company = createMockCompany();
      
      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.companyNeedsEnrichment.mockResolvedValue(true);

      const response = await request(app)
        .get('/api/enrichment/company/comp-123/check')
        .expect(200);

      expect(response.body).toEqual({
        needsEnrichment: true,
        lastEnrichedAt: null,
        enrichmentStatus: null
      });

      expect(mockEnrichmentService.companyNeedsEnrichment).toHaveBeenCalledWith(company);
    });

    it('should return enrichment status for companies that don\'t need enrichment', async () => {
      const company = {
        ...createMockCompany(),
        lastEnrichedAt: new Date().toISOString(),
        enrichmentStatus: { lastEnriched: new Date().toISOString() }
      };
      
      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.companyNeedsEnrichment.mockResolvedValue(false);

      const response = await request(app)
        .get('/api/enrichment/company/comp-123/check')
        .expect(200);

      expect(response.body.needsEnrichment).toBe(false);
      expect(response.body.lastEnrichedAt).toBeTruthy();
      expect(response.body.enrichmentStatus).toBeTruthy();
    });

    it('should return 404 for non-existent company', async () => {
      mockCompanyRepository.getCompanyById.mockReturnValue(null);

      const response = await request(app)
        .get('/api/enrichment/company/non-existent/check')
        .expect(404);

      expect(response.body).toEqual({ error: 'Company not found' });
    });
  });

  describe('GET /api/enrichment/companies/pending', () => {
    it('should return companies that need enrichment', async () => {
      const pendingCompanies = [
        { id: 'comp-1', name: 'Company 1', lastEnrichedAt: null },
        { id: 'comp-2', name: 'Company 2', lastEnrichedAt: null }
      ];

      mockEnrichmentRepository.getCompaniesNeedingEnrichment.mockResolvedValue(pendingCompanies);

      const response = await request(app)
        .get('/api/enrichment/companies/pending')
        .expect(200);

      expect(response.body).toEqual({ companies: pendingCompanies });
      expect(mockEnrichmentRepository.getCompaniesNeedingEnrichment).toHaveBeenCalledWith(100);
    });

    it('should respect limit parameter', async () => {
      mockEnrichmentRepository.getCompaniesNeedingEnrichment.mockResolvedValue([]);

      await request(app)
        .get('/api/enrichment/companies/pending?limit=50')
        .expect(200);

      expect(mockEnrichmentRepository.getCompaniesNeedingEnrichment).toHaveBeenCalledWith(50);
    });

    it('should handle invalid limit parameter', async () => {
      mockEnrichmentRepository.getCompaniesNeedingEnrichment.mockResolvedValue([]);

      await request(app)
        .get('/api/enrichment/companies/pending?limit=invalid')
        .expect(200);

      // Should use NaN which becomes 100 (default)
      expect(mockEnrichmentRepository.getCompaniesNeedingEnrichment).toHaveBeenCalledWith(NaN);
    });
  });

  describe('POST /api/enrichment/contact/:id', () => {
    it('should return 501 for contact enrichment (not implemented)', async () => {
      const contact = {
        id: 'cont-123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      };

      mockContactRepository.getContactById.mockReturnValue(contact);

      const response = await request(app)
        .post('/api/enrichment/contact/cont-123')
        .send()
        .expect(501);

      expect(response.body).toEqual({
        error: 'Contact enrichment not yet implemented',
        message: 'This feature is coming soon'
      });
    });

    it('should return 404 for non-existent contact', async () => {
      mockContactRepository.getContactById.mockReturnValue(null);

      const response = await request(app)
        .post('/api/enrichment/contact/non-existent')
        .send()
        .expect(404);

      expect(response.body).toEqual({ error: 'Contact not found' });
    });
  });

  describe('GET /api/enrichment/stats', () => {
    it('should return enrichment statistics', async () => {
      const overviewStats = {
        enrichedCompanies: 150,
        totalEnrichments: 200,
        avgConfidence: 0.85,
        lastEnrichmentAt: new Date().toISOString()
      };

      const sourceStats = [
        { source: 'abn_lookup', count: 120, avgConfidence: 0.9 },
        { source: 'clearbit', count: 80, avgConfidence: 0.8 }
      ];

      // Mock database calls
      mockEnrichmentRepository.db = {
        prepare: jest.fn()
          .mockReturnValueOnce({
            get: jest.fn().mockReturnValue(overviewStats)
          })
          .mockReturnValueOnce({
            all: jest.fn().mockReturnValue(sourceStats)
          })
      } as any;

      const response = await request(app)
        .get('/api/enrichment/stats')
        .expect(200);

      expect(response.body).toEqual({
        overview: overviewStats,
        bySource: sourceStats
      });
    });

    it('should handle database errors', async () => {
      mockEnrichmentRepository.db = {
        prepare: jest.fn().mockImplementation(() => {
          throw new Error('Database error');
        })
      } as any;

      await request(app)
        .get('/api/enrichment/stats')
        .expect(500);
    });
  });

  describe('POST /api/enrichment/cleanup', () => {
    it('should clean up expired enrichment data', async () => {
      mockEnrichmentRepository.deleteExpiredEnrichments.mockResolvedValue(15);

      const response = await request(app)
        .post('/api/enrichment/cleanup')
        .send()
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        deletedCount: 15,
        message: 'Cleaned up 15 expired enrichment records'
      });

      expect(mockEnrichmentRepository.deleteExpiredEnrichments).toHaveBeenCalled();
    });

    it('should handle cleanup errors', async () => {
      mockEnrichmentRepository.deleteExpiredEnrichments.mockRejectedValue(
        new Error('Cleanup failed')
      );

      await request(app)
        .post('/api/enrichment/cleanup')
        .send()
        .expect(500);
    });

    it('should handle zero deletions', async () => {
      mockEnrichmentRepository.deleteExpiredEnrichments.mockResolvedValue(0);

      const response = await request(app)
        .post('/api/enrichment/cleanup')
        .send()
        .expect(200);

      expect(response.body.deletedCount).toBe(0);
      expect(response.body.message).toBe('Cleaned up 0 expired enrichment records');
    });
  });

  describe('Error handling', () => {
    it('should handle JSON parsing errors', async () => {
      const company = createMockCompany();
      mockCompanyRepository.getCompanyById.mockReturnValue(company);

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });

    it('should handle missing route parameters', async () => {
      await request(app)
        .post('/api/enrichment/company/')
        .send()
        .expect(404);
    });

    it('should handle database connection errors gracefully', async () => {
      mockCompanyRepository.getCompanyById.mockImplementation(() => {
        throw new Error('Database connection lost');
      });

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(500);
    });
  });

  describe('Request validation', () => {
    it('should handle empty request bodies', async () => {
      const company = createMockCompany();
      
      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockResolvedValue([]);
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue([]);
      mockEnrichmentRepository.logEnrichmentAttempt.mockResolvedValue();

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(200);
    });

    it('should handle malformed source arrays', async () => {
      const company = createMockCompany();
      
      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockResolvedValue([]);
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue([]);
      mockEnrichmentRepository.logEnrichmentAttempt.mockResolvedValue();

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send({ sources: 'not-an-array' })
        .expect(200);

      // Should pass the malformed sources through (service should handle validation)
      expect(mockEnrichmentService.enrichCompany).toHaveBeenCalledWith(company, 'not-an-array');
    });
  });

  describe('Response time tracking', () => {
    it('should track response times for enrichment attempts', async () => {
      const company = createMockCompany();
      const enrichmentResults = createMockEnrichmentResult();

      mockCompanyRepository.getCompanyById.mockReturnValue(company);
      mockEnrichmentService.enrichCompany.mockImplementation(async () => {
        // Simulate some processing time
        await new Promise(resolve => setTimeout(resolve, 100));
        return enrichmentResults;
      });
      mockEnrichmentService.getCompanyEnrichment.mockResolvedValue([]);
      mockEnrichmentRepository.logEnrichmentAttempt.mockResolvedValue();

      await request(app)
        .post('/api/enrichment/company/comp-123')
        .send()
        .expect(200);

      expect(mockEnrichmentRepository.logEnrichmentAttempt).toHaveBeenCalledWith(
        expect.objectContaining({
          responseTimeMs: expect.any(Number)
        })
      );

      const call = mockEnrichmentRepository.logEnrichmentAttempt.mock.calls[0][0];
      expect(call.responseTimeMs).toBeGreaterThan(0);
    });
  });
});