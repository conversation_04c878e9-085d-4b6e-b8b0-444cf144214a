/**
 * Cashflow Controller Test
 * 
 * Tests for the cashflow API endpoints.
 * These endpoints are critical for the financial forecasting feature.
 */
import { Request, Response } from 'express';
import { DailyCashflowService } from '@/services/cashflow/daily-cashflow-service';
import { TransactionService } from '@/services/cashflow/transaction-service';
import { ProjectSettingsService } from '@/services/cashflow/project-settings-service';
import { CashflowController } from '@/api/controllers/cashflow';

// Mock the services
jest.mock('@/services/cashflow/daily-cashflow-service');
jest.mock('@/services/cashflow/transaction-service');
jest.mock('@/services/cashflow/project-settings-service');

describe('CashflowController', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockDailyCashflowService: jest.Mocked<DailyCashflowService>;
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockProjectSettingsService: jest.Mocked<ProjectSettingsService>;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock response
    mockRes = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };
    
    // Create mock request
    mockReq = {
      query: {},
      body: {},
      params: {}
    };
    
    // Get mocked service instances
    mockDailyCashflowService = new DailyCashflowService() as jest.Mocked<DailyCashflowService>;
    mockTransactionService = new TransactionService() as jest.Mocked<TransactionService>;
    mockProjectSettingsService = new ProjectSettingsService() as jest.Mocked<ProjectSettingsService>;
  });
  
  describe('GET /cashflow/daily', () => {
    it('returns daily cashflow data with default parameters', async () => {
      const mockData = {
        projections: [
          { date: '2025-01-01', balance: 10000, income: 5000, expenses: 2000 }
        ],
        summary: {
          startBalance: 10000,
          endBalance: 13000,
          totalIncome: 5000,
          totalExpenses: 2000
        }
      };
      
      mockDailyCashflowService.getProjections.mockResolvedValue(mockData);
      
      const controller = new CashflowController();
      await controller.getDailyCashflow(mockReq as Request, mockRes as Response);
      
      expect(mockDailyCashflowService.getProjections).toHaveBeenCalled();
      expect(mockRes.json).toHaveBeenCalledWith(mockData);
    });
    
    it('accepts date range parameters', async () => {
      mockReq.query = {
        startDate: '2025-01-01',
        endDate: '2025-03-31'
      };
      
      const controller = new CashflowController();
      await controller.getDailyCashflow(mockReq as Request, mockRes as Response);
      
      expect(mockDailyCashflowService.getProjections).toHaveBeenCalledWith(
        new Date('2025-01-01'),
        new Date('2025-03-31')
      );
    });
    
    it('handles service errors gracefully', async () => {
      mockDailyCashflowService.getProjections.mockRejectedValue(
        new Error('Service error')
      );
      
      const controller = new CashflowController();
      await controller.getDailyCashflow(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Failed to fetch cashflow data',
        message: 'Service error'
      });
    });
    
    it('validates date parameters', async () => {
      mockReq.query = {
        startDate: 'invalid-date',
        endDate: '2025-03-31'
      };
      
      const controller = new CashflowController();
      await controller.getDailyCashflow(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid date format'
      });
    });
  });
  
  describe('GET /cashflow/transactions', () => {
    it('returns grouped transactions', async () => {
      const mockTransactions = {
        income: [
          { id: '1', type: 'invoice', amount: 5000, date: '2025-01-15' }
        ],
        expenses: [
          { id: '2', type: 'expense', amount: 2000, date: '2025-01-20' }
        ]
      };
      
      mockTransactionService.getGroupedTransactions.mockResolvedValue(mockTransactions);
      
      const controller = new CashflowController();
      await controller.getTransactions(mockReq as Request, mockRes as Response);
      
      expect(mockRes.json).toHaveBeenCalledWith(mockTransactions);
    });
    
    it('filters transactions by type', async () => {
      mockReq.query = {
        type: 'income'
      };
      
      const controller = new CashflowController();
      await controller.getTransactions(mockReq as Request, mockRes as Response);
      
      expect(mockTransactionService.getGroupedTransactions).toHaveBeenCalledWith({
        type: 'income'
      });
    });
  });
  
  describe('GET /cashflow/projection-settings', () => {
    it('returns project-specific settings', async () => {
      const mockSettings = [
        {
          projectId: 'proj-1',
          projectName: 'Test Project',
          invoiceFrequency: 'monthly',
          paymentTerms: 14
        }
      ];
      
      mockProjectSettingsService.getAllSettings.mockResolvedValue(mockSettings);
      
      const controller = new CashflowController();
      await controller.getProjectionSettings(mockReq as Request, mockRes as Response);
      
      expect(mockRes.json).toHaveBeenCalledWith(mockSettings);
    });
  });
  
  describe('PUT /cashflow/projection-settings/:projectId', () => {
    it('updates project settings', async () => {
      mockReq.params = { projectId: 'proj-1' };
      mockReq.body = {
        invoiceFrequency: 'weekly',
        paymentTerms: 7
      };
      
      const updatedSettings = {
        projectId: 'proj-1',
        invoiceFrequency: 'weekly',
        paymentTerms: 7
      };
      
      mockProjectSettingsService.updateSettings.mockResolvedValue(updatedSettings);
      
      const controller = new CashflowController();
      await controller.updateProjectionSettings(mockReq as Request, mockRes as Response);
      
      expect(mockProjectSettingsService.updateSettings).toHaveBeenCalledWith(
        'proj-1',
        mockReq.body
      );
      expect(mockRes.json).toHaveBeenCalledWith(updatedSettings);
    });
    
    it('validates required fields', async () => {
      mockReq.params = { projectId: 'proj-1' };
      mockReq.body = {
        invoiceFrequency: 'invalid-frequency'
      };
      
      const controller = new CashflowController();
      await controller.updateProjectionSettings(mockReq as Request, mockRes as Response);
      
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid invoice frequency'
      });
    });
  });
  
  describe('POST /cashflow/scenario', () => {
    it('calculates scenario projections', async () => {
      mockReq.body = {
        scenario: 'best',
        adjustments: {
          revenueMultiplier: 1.2,
          expenseMultiplier: 0.9
        }
      };
      
      const mockScenarioData = {
        scenario: 'best',
        projections: [
          { date: '2025-01-01', balance: 12000 }
        ]
      };
      
      mockDailyCashflowService.calculateScenario.mockResolvedValue(mockScenarioData);
      
      const controller = new CashflowController();
      await controller.calculateScenario(mockReq as Request, mockRes as Response);
      
      expect(mockDailyCashflowService.calculateScenario).toHaveBeenCalledWith(
        'best',
        mockReq.body.adjustments
      );
      expect(mockRes.json).toHaveBeenCalledWith(mockScenarioData);
    });
  });
});