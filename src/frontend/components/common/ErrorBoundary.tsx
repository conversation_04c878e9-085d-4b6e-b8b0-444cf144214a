/** * Error Boundary Component * * Catches React component errors and displays fallback UI */ import React, { Component, ErrorInfo, ReactNode } from 'react'; import { ChevronDownIcon, ChevronRightIcon, RefreshCwIcon } from 'lucide-react'; interface Props { children: ReactNode; fallback?: ReactNode; onError?: (error: Error, errorInfo: ErrorInfo) => void; resetKeys?: Array<string | number>; resetOnPropsChange?: boolean; isolate?: boolean; level?: 'page' | 'section' | 'component'; } interface State { hasError: boolean; error: Error | null; errorInfo: ErrorInfo | null; errorBoundaryKey: number; showDetails: boolean; } export class ErrorBoundary extends Component<Props, State> { private resetTimeoutId: NodeJS.Timeout | null = null; constructor(props: Props) { super(props); this.state = { hasError: false, error: null, errorInfo: null, errorBoundaryKey: 0, showDetails: false }; } static getDerivedStateFromError(error: Error): Partial<State> { return { hasError: true, error }; } componentDidCatch(error: Error, errorInfo: ErrorInfo): void { console.error('Error caught by boundary:', error, errorInfo); // Call custom error handler if provided this.props.onError?.(error, errorInfo); // Log to error tracking service this.logErrorToService(error, errorInfo); this.setState({ errorInfo }); } componentDidUpdate(prevProps: Props): void { const { resetKeys, resetOnPropsChange } = this.props; const { hasError } = this.state; // Reset on prop changes if enabled if (hasError && resetOnPropsChange && prevProps.children !== this.props.children) { this.resetErrorBoundary(); } // Reset if resetKeys changed if (hasError && resetKeys && prevProps.resetKeys) { const hasResetKeyChanged = resetKeys.some((key, idx) => key !== prevProps.resetKeys![idx]); if (hasResetKeyChanged) { this.resetErrorBoundary(); } } } componentWillUnmount(): void { if (this.resetTimeoutId) { clearTimeout(this.resetTimeoutId); } } logErrorToService = (error: Error, errorInfo: ErrorInfo): void => { // In production, this would send to error tracking service if (process.env.NODE_ENV === 'production') { // Example: Sentry, LogRocket, etc. console.error('Production error:', { error: error.toString(), componentStack: errorInfo.componentStack, timestamp: new Date().toISOString(), url: window.location.href, userAgent: navigator.userAgent }); } }; resetErrorBoundary = (): void => { this.setState({ hasError: false, error: null, errorInfo: null, errorBoundaryKey: this.state.errorBoundaryKey + 1, showDetails: false }); }; toggleDetails = (): void => { this.setState(prev => ({ showDetails: !prev.showDetails })); }; render(): ReactNode { const { hasError, error, errorInfo, showDetails } = this.state; const { children, fallback, isolate = true, level = 'component' } = this.props; if (hasError && error) { // Use custom fallback if provided if (fallback) { return fallback; } // Default error UI based on level const errorLevelStyles = { page: 'min-h-screen', section: 'min-h-[400px]', component: 'min-h-[200px]' }; const errorLevelTitles = { page: 'Page Error', section: 'Section Error', component: 'Component Error' }; return ( <div className={`flex items-center justify-center p-8 ${errorLevelStyles[level]}`}> <div className="w-full max-w-2xl"> <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-red-200 dark:border-red-800"> {/* Error Header */} <div className="p-6 border-b border-gray-200 dark:border-gray-700"> <div className="flex items-start justify-between"> <div> <h2 className="text-xl font-semibold text-gray-900 dark:text-white"> {errorLevelTitles[level]} </h2> <p className="mt-1 text-sm text-gray-500 dark:text-gray-400"> Something went wrong while rendering this {level}. </p> </div> <button onClick={this.resetErrorBoundary} className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" > <RefreshCwIcon className="w-4 h-4 mr-1.5" /> Retry </button> </div> </div> {/* Error Message */} <div className="p-6"> <div className="btn-modern--primary"dark:bg-red-900/20 rounded-md p-4"> <p className="text-sm font-medium text-red-800 dark:text-red-200"> {error.message} </p> </div> {/* Error Details (Development Only) */} {process.env.NODE_ENV === 'development' && ( <div className="mt-4"> <button onClick={this.toggleDetails} className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" > {showDetails ? ( <ChevronDownIcon className="w-4 h-4 mr-1" /> ) : ( <ChevronRightIcon className="w-4 h-4 mr-1" /> )} {showDetails ? 'Hide' : 'Show'} Technical Details </button> {showDetails && ( <div className="mt-3 space-y-3"> {/* Stack Trace */} <div> <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Stack Trace: </h4> <pre className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900 rounded p-3 overflow-x-auto"> {error.stack} </pre> </div> {/* Component Stack */} {errorInfo && ( <div> <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> Component Stack: </h4> <pre className="text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900 rounded p-3 overflow-x-auto"> {errorInfo.componentStack} </pre> </div> )} </div> )} </div> )} {/* User-Friendly Message */} <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700"> <p className="text-sm text-gray-600 dark:text-gray-400"> {isolate ? ( <> This error has been isolated to prevent it from affecting the rest of the application. You can continue using other features normally. </> ) : ( <> This error may affect the functionality of the application. Please try refreshing the page if the problem persists. </> )} </p> </div> </div> </div> </div> </div> ); } // Render children with error boundary key to force remount on reset return <div key={this.state.errorBoundaryKey}>{children}</div>; } } /** * Higher-order component for adding error boundary */ export function withErrorBoundary<P extends object>( Component: React.ComponentType<P>, errorBoundaryProps?: Omit<Props, 'children'> ) { const WrappedComponent = (props: P) => ( <ErrorBoundary {...errorBoundaryProps}> <Component {...props} /> </ErrorBoundary> ); WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`; return WrappedComponent; } /** * Hook for error handling (to be used with error boundary) */ export function useErrorHandler() { return (error: Error) => { throw error; // This will be caught by the nearest error boundary }; }