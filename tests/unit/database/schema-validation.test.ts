import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';

describe('Database Schema Validation', () => {
  let db: Database.Database;

  beforeEach(() => {
    // Create in-memory database
    db = new Database(':memory:');
    
    // Run migrations
    const migrationsDir = path.join(__dirname, '../../../migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(f => f.endsWith('.sql'))
      .sort();

    migrationFiles.forEach(file => {
      const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
      db.exec(sql);
    });
  });

  afterEach(() => {
    db.close();
  });

  describe('Table Structure', () => {
    const expectedTables = [
      'company',
      'contact',
      'deal',
      'note',
      'activity',
      'change_log',
      'estimate',
      'estimate_allocation',
      'estimate_time_allocation',
      'expense',
      'cashflow_snapshot',
      'hubspot_settings',
      'hubspot_import_log',
      'harvest_invoice_cache',
      'field_ownership',
      'contact_company',
      'contact_role',
      'deal_estimate',
      'settings',
      'leads',
      'estimate_drafts',
      'team_coverage'
    ];

    test.each(expectedTables)('should have %s table', (tableName) => {
      const tableInfo = db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name=?`).get(tableName);
      expect(tableInfo).toBeTruthy();
    });

    it('should have correct columns in company table', () => {
      const columns = db.prepare("PRAGMA table_info(company)").all();
      const columnNames = columns.map((col: any) => col.name);

      expect(columnNames).toContain('id');
      expect(columnNames).toContain('name');
      expect(columnNames).toContain('website');
      expect(columnNames).toContain('industry');
      expect(columnNames).toContain('employeeCount');
      expect(columnNames).toContain('description');
      expect(columnNames).toContain('hubspot_id');
      expect(columnNames).toContain('harvest_id');
      expect(columnNames).toContain('createdAt');
      expect(columnNames).toContain('updatedAt');
    });

    it('should have correct columns in deal table', () => {
      const columns = db.prepare("PRAGMA table_info(deal)").all();
      const columnNames = columns.map((col: any) => col.name);

      expect(columnNames).toContain('id');
      expect(columnNames).toContain('dealName');
      expect(columnNames).toContain('companyId');
      expect(columnNames).toContain('companyName');
      expect(columnNames).toContain('stage');
      expect(columnNames).toContain('amount');
      expect(columnNames).toContain('closeDate');
      expect(columnNames).toContain('probability');
      expect(columnNames).toContain('owner');
      expect(columnNames).toContain('status');
      expect(columnNames).toContain('hubspotId');
    });

    it('should have correct columns in estimate table', () => {
      const columns = db.prepare("PRAGMA table_info(estimate)").all();
      const columnNames = columns.map((col: any) => col.name);

      expect(columnNames).toContain('id');
      expect(columnNames).toContain('estimate_number');
      expect(columnNames).toContain('estimate_name');
      expect(columnNames).toContain('status');
      expect(columnNames).toContain('date_sent');
      expect(columnNames).toContain('valid_until');
      expect(columnNames).toContain('start_date');
      expect(columnNames).toContain('end_date');
      expect(columnNames).toContain('total_cost');
      expect(columnNames).toContain('total_price');
      expect(columnNames).toContain('margin');
    });

    it('should have correct columns in expense table', () => {
      const columns = db.prepare("PRAGMA table_info(expense)").all();
      const columnNames = columns.map((col: any) => col.name);

      expect(columnNames).toContain('id');
      expect(columnNames).toContain('date'); // Not start_date
      expect(columnNames).toContain('amount');
      expect(columnNames).toContain('category');
      expect(columnNames).toContain('vendor');
      expect(columnNames).toContain('description');
      expect(columnNames).toContain('paymentMethod');
      expect(columnNames).toContain('isRecurring');
      expect(columnNames).toContain('status');
    });

    it('should have correct columns in estimate_allocation table', () => {
      const columns = db.prepare("PRAGMA table_info(estimate_allocation)").all();
      const columnNames = columns.map((col: any) => col.name);

      expect(columnNames).toContain('harvest_user_id'); // Not staff_id
      expect(columnNames).toContain('first_name'); // Not staff_name
      expect(columnNames).toContain('last_name');
      expect(columnNames).toContain('role');
      expect(columnNames).toContain('cost_rate');
      expect(columnNames).toContain('bill_rate');
      expect(columnNames).toContain('hours');
      expect(columnNames).toContain('total_cost');
      expect(columnNames).toContain('total_price');
    });

    it('should have correct columns in estimate_time_allocation table', () => {
      const columns = db.prepare("PRAGMA table_info(estimate_time_allocation)").all();
      const columnNames = columns.map((col: any) => col.name);

      expect(columnNames).toContain('week_identifier'); // Not week_number
      expect(columnNames).toContain('days'); // Not hours
      expect(columnNames).toContain('allocation_type');
      expect(columnNames).toContain('notes');
    });
  });

  describe('Indexes', () => {
    it('should have performance indexes', () => {
      const indexes = db.prepare("SELECT name FROM sqlite_master WHERE type='index'").all();
      const indexNames = indexes.map((idx: any) => idx.name);

      // Company indexes
      expect(indexNames).toContain('idx_company_hubspot_id');
      expect(indexNames).toContain('idx_company_harvest_id');
      
      // Deal indexes
      expect(indexNames).toContain('idx_deal_company');
      expect(indexNames).toContain('idx_deal_stage');
      expect(indexNames).toContain('idx_deal_status');
      expect(indexNames).toContain('idx_deal_close_date');
      
      // Contact indexes
      expect(indexNames).toContain('idx_contact_email');
      expect(indexNames).toContain('idx_contact_company');
      
      // Activity indexes
      expect(indexNames).toContain('idx_activity_timestamp');
      expect(indexNames).toContain('idx_activity_target');
    });
  });

  describe('Foreign Key Constraints', () => {
    it('should enforce foreign key constraints', () => {
      // Enable foreign keys
      db.exec('PRAGMA foreign_keys = ON');
      
      // Try to insert deal with non-existent company
      expect(() => {
        db.prepare(`
          INSERT INTO deal (id, dealName, companyId, companyName, stage, amount, closeDate, probability, owner, status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          'deal-1',
          'Test Deal',
          'non-existent-company',
          'Test Company',
          'qualification',
          10000,
          '2024-12-31',
          0.5,
          '<EMAIL>',
          'open'
        );
      }).toThrow(/FOREIGN KEY constraint failed/);
    });

    it('should cascade delete related records', () => {
      // Enable foreign keys
      db.exec('PRAGMA foreign_keys = ON');
      
      // Insert company
      db.prepare(`
        INSERT INTO company (id, name, createdAt, updatedAt)
        VALUES (?, ?, ?, ?)
      `).run('company-1', 'Test Company', new Date().toISOString(), new Date().toISOString());
      
      // Insert deal
      db.prepare(`
        INSERT INTO deal (id, dealName, companyId, companyName, stage, amount, closeDate, probability, owner, status, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'deal-1',
        'Test Deal',
        'company-1',
        'Test Company',
        'qualification',
        10000,
        '2024-12-31',
        0.5,
        '<EMAIL>',
        'open',
        new Date().toISOString(),
        new Date().toISOString()
      );
      
      // Delete company - should cascade delete deal
      db.prepare('DELETE FROM company WHERE id = ?').run('company-1');
      
      const deal = db.prepare('SELECT * FROM deal WHERE id = ?').get('deal-1');
      expect(deal).toBeUndefined();
    });
  });

  describe('Unique Constraints', () => {
    it('should enforce unique email for contacts', () => {
      const insertContact = () => db.prepare(`
        INSERT INTO contact (id, firstName, lastName, email, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      
      // Insert first contact
      insertContact().run(
        'contact-1',
        'John',
        'Doe',
        '<EMAIL>',
        new Date().toISOString(),
        new Date().toISOString()
      );
      
      // Try to insert second contact with same email
      expect(() => {
        insertContact().run(
          'contact-2',
          'Jane',
          'Doe',
          '<EMAIL>',
          new Date().toISOString(),
          new Date().toISOString()
        );
      }).toThrow(/UNIQUE constraint failed/);
    });

    it('should enforce unique company name', () => {
      const insertCompany = () => db.prepare(`
        INSERT INTO company (id, name, createdAt, updatedAt)
        VALUES (?, ?, ?, ?)
      `);
      
      // Insert first company
      insertCompany().run(
        'company-1',
        'Acme Corp',
        new Date().toISOString(),
        new Date().toISOString()
      );
      
      // Try to insert second company with same name
      expect(() => {
        insertCompany().run(
          'company-2',
          'Acme Corp',
          new Date().toISOString(),
          new Date().toISOString()
        );
      }).toThrow(/UNIQUE constraint failed/);
    });
  });

  describe('Default Values', () => {
    it('should set default values correctly', () => {
      // Insert minimal deal
      db.prepare(`
        INSERT INTO company (id, name, createdAt, updatedAt)
        VALUES (?, ?, ?, ?)
      `).run('company-1', 'Test Company', new Date().toISOString(), new Date().toISOString());
      
      db.prepare(`
        INSERT INTO deal (id, dealName, companyId, companyName, stage, amount, closeDate, owner, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'deal-1',
        'Test Deal',
        'company-1',
        'Test Company',
        'qualification',
        10000,
        '2024-12-31',
        '<EMAIL>',
        new Date().toISOString(),
        new Date().toISOString()
      );
      
      const deal = db.prepare('SELECT * FROM deal WHERE id = ?').get('deal-1') as any;
      
      // Check defaults
      expect(deal.status).toBe('open');
      expect(deal.probability).toBe(0.5); // Default for qualification stage
    });
  });

  describe('Triggers', () => {
    it('should update timestamps automatically', () => {
      const now = new Date().toISOString();
      
      // Insert company
      db.prepare(`
        INSERT INTO company (id, name, createdAt, updatedAt)
        VALUES (?, ?, ?, ?)
      `).run('company-1', 'Test Company', now, now);
      
      // Wait a bit
      const later = new Date(Date.now() + 1000).toISOString();
      
      // Update company
      db.prepare('UPDATE company SET name = ? WHERE id = ?').run('Updated Company', 'company-1');
      
      const company = db.prepare('SELECT * FROM company WHERE id = ?').get('company-1') as any;
      
      // updatedAt should be updated by trigger if one exists
      // Note: SQLite doesn't have automatic timestamp updates without triggers
      // This test assumes triggers are implemented
    });
  });

  describe('Data Types', () => {
    it('should handle all expected data types', () => {
      // Test JSON fields
      db.prepare(`
        INSERT INTO contact (id, firstName, lastName, email, tags, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        'contact-1',
        'John',
        'Doe',
        '<EMAIL>',
        JSON.stringify(['tag1', 'tag2']),
        new Date().toISOString(),
        new Date().toISOString()
      );
      
      const contact = db.prepare('SELECT * FROM contact WHERE id = ?').get('contact-1') as any;
      expect(JSON.parse(contact.tags)).toEqual(['tag1', 'tag2']);
      
      // Test decimal fields
      db.prepare(`
        INSERT INTO company (id, name, createdAt, updatedAt)
        VALUES (?, ?, ?, ?)
      `).run('company-1', 'Test Company', new Date().toISOString(), new Date().toISOString());
      
      db.prepare(`
        INSERT INTO deal (id, dealName, companyId, companyName, stage, amount, closeDate, probability, owner, status, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'deal-1',
        'Test Deal',
        'company-1',
        'Test Company',
        'qualification',
        12345.67,
        '2024-12-31',
        0.75,
        '<EMAIL>',
        'open',
        new Date().toISOString(),
        new Date().toISOString()
      );
      
      const deal = db.prepare('SELECT * FROM deal WHERE id = ?').get('deal-1') as any;
      expect(deal.amount).toBe(12345.67);
      expect(deal.probability).toBe(0.75);
    });
  });

  describe('Migration Integrity', () => {
    it('should run all migrations without errors', () => {
      // This is implicitly tested in beforeEach
      // Additional check: verify migration history table if implemented
      const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
      expect(tables.length).toBeGreaterThan(20);
    });

    it('should maintain referential integrity across migrations', () => {
      // Check that all foreign key references are valid
      const foreignKeys = db.prepare('PRAGMA foreign_key_check').all();
      expect(foreignKeys).toHaveLength(0);
    });
  });
});