import React from 'react'; interface EmptyTransactionsProps { hasTransactions: boolean; clearFilters: () => void; } /** * Component for displaying empty state when no transactions match filters */ const EmptyTransactions: React.FC<EmptyTransactionsProps> = ({ hasTransactions, clearFilters }) => { return ( <div className="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"> <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /> </svg> <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No transactions found</h3> <p className="mt-1 text-sm text-gray-500 dark:text-gray-400"> {hasTransactions ? "No transactions match your current filter criteria." : "No upcoming transactions in the selected timeframe."} </p> {hasTransactions && ( <button onClick={clearFilters} className="mt-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" > Clear Filters </button> )} </div> ); }; export default EmptyTransactions;