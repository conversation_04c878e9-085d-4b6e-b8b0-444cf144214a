import { CacheService } from '../../../../src/services/cashflow/cache-service';
import { createClient } from 'redis';

jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    on: jest.fn(),
    isOpen: true
  }))
}));

describe('CacheService', () => {
  let service: CacheService;
  let mockRedisClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRedisClient = {
      connect: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      keys: jest.fn(),
      on: jest.fn(),
      isOpen: true
    };
    
    (createClient as jest.Mock).mockReturnValue(mockRedisClient);
    
    service = new CacheService();
  });

  describe('get', () => {
    it('should return cached value if available', async () => {
      const cachedData = { test: 'data' };
      mockRedisClient.get.mockResolvedValue(JSON.stringify(cachedData));

      const result = await service.get('test-key', async () => ({ test: 'fresh' }));

      expect(result).toEqual(cachedData);
      expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
    });

    it('should fetch and cache data if not in cache', async () => {
      mockRedisClient.get.mockResolvedValue(null);
      const freshData = { test: 'fresh' };
      const fetchFn = jest.fn().mockResolvedValue(freshData);

      const result = await service.get('test-key', fetchFn, 3600);

      expect(result).toEqual(freshData);
      expect(fetchFn).toHaveBeenCalled();
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify(freshData),
        { EX: 3600 }
      );
    });

    it('should use in-memory cache when Redis is not available', async () => {
      mockRedisClient.isOpen = false;
      const freshData = { test: 'fresh' };
      const fetchFn = jest.fn().mockResolvedValue(freshData);

      const result = await service.get('test-key', fetchFn);

      expect(result).toEqual(freshData);
      expect(fetchFn).toHaveBeenCalled();
      expect(mockRedisClient.get).not.toHaveBeenCalled();
    });

    it('should handle JSON parse errors gracefully', async () => {
      mockRedisClient.get.mockResolvedValue('invalid json');
      const freshData = { test: 'fresh' };
      const fetchFn = jest.fn().mockResolvedValue(freshData);

      const result = await service.get('test-key', fetchFn);

      expect(result).toEqual(freshData);
      expect(fetchFn).toHaveBeenCalled();
    });

    it('should respect TTL expiration', async () => {
      const cachedData = { test: 'cached' };
      const freshData = { test: 'fresh' };
      
      // First call - cache miss
      mockRedisClient.get.mockResolvedValueOnce(null);
      const fetchFn = jest.fn().mockResolvedValue(freshData);
      
      await service.get('test-key', fetchFn, 1);
      
      // Verify data was cached
      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify(freshData),
        { EX: 1 }
      );
    });
  });

  describe('set', () => {
    it('should set value in Redis', async () => {
      const data = { test: 'data' };
      
      await service.set('test-key', data, 3600);

      expect(mockRedisClient.set).toHaveBeenCalledWith(
        'test-key',
        JSON.stringify(data),
        { EX: 3600 }
      );
    });

    it('should use in-memory cache when Redis is not available', async () => {
      mockRedisClient.isOpen = false;
      const data = { test: 'data' };
      
      await service.set('test-key', data, 3600);

      expect(mockRedisClient.set).not.toHaveBeenCalled();
      
      // Verify it was stored in memory
      mockRedisClient.isOpen = false; // Keep Redis disabled
      const fetchFn = jest.fn();
      const result = await service.get('test-key', fetchFn);
      
      expect(result).toEqual(data);
      expect(fetchFn).not.toHaveBeenCalled();
    });

    it('should handle serialization errors', async () => {
      const circularRef: any = { test: 'data' };
      circularRef.circular = circularRef; // Create circular reference

      await service.set('test-key', circularRef);

      // Should not throw, but also not cache
      expect(mockRedisClient.set).not.toHaveBeenCalled();
    });
  });

  describe('invalidate', () => {
    it('should delete specific key', async () => {
      await service.invalidate('test-key');

      expect(mockRedisClient.del).toHaveBeenCalledWith('test-key');
    });

    it('should delete keys matching pattern', async () => {
      const matchingKeys = ['test:1', 'test:2', 'test:3'];
      mockRedisClient.keys.mockResolvedValue(matchingKeys);

      await service.invalidate('test:*');

      expect(mockRedisClient.keys).toHaveBeenCalledWith('test:*');
      expect(mockRedisClient.del).toHaveBeenCalledWith(...matchingKeys);
    });

    it('should clear in-memory cache when Redis is not available', async () => {
      mockRedisClient.isOpen = false;
      
      // Set some data in memory
      await service.set('test-key', { test: 'data' });
      
      // Invalidate
      await service.invalidate('test-key');
      
      // Verify it's gone
      const fetchFn = jest.fn().mockResolvedValue({ test: 'fresh' });
      const result = await service.get('test-key', fetchFn);
      
      expect(fetchFn).toHaveBeenCalled();
      expect(result).toEqual({ test: 'fresh' });
    });

    it('should handle Redis errors gracefully', async () => {
      mockRedisClient.del.mockRejectedValue(new Error('Redis error'));

      // Should not throw
      await expect(service.invalidate('test-key')).resolves.not.toThrow();
    });
  });

  describe('clear', () => {
    it('should clear all cache entries', async () => {
      const allKeys = ['key1', 'key2', 'key3'];
      mockRedisClient.keys.mockResolvedValue(allKeys);

      await service.clear();

      expect(mockRedisClient.keys).toHaveBeenCalledWith('*');
      expect(mockRedisClient.del).toHaveBeenCalledWith(...allKeys);
    });

    it('should clear in-memory cache when Redis is not available', async () => {
      mockRedisClient.isOpen = false;
      
      // Set some data
      await service.set('key1', { test: 'data1' });
      await service.set('key2', { test: 'data2' });
      
      // Clear all
      await service.clear();
      
      // Verify everything is gone
      const fetchFn = jest.fn().mockResolvedValue({ test: 'fresh' });
      await service.get('key1', fetchFn);
      
      expect(fetchFn).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle Redis connection errors', async () => {
      mockRedisClient.connect.mockRejectedValue(new Error('Connection failed'));
      mockRedisClient.isOpen = false;

      const service = new CacheService();
      const fetchFn = jest.fn().mockResolvedValue({ test: 'data' });
      
      // Should fall back to in-memory cache
      const result = await service.get('test-key', fetchFn);
      
      expect(result).toEqual({ test: 'data' });
      expect(fetchFn).toHaveBeenCalled();
    });

    it('should handle null/undefined values', async () => {
      mockRedisClient.get.mockResolvedValue(null);
      const fetchFn = jest.fn().mockResolvedValue(null);

      const result = await service.get('test-key', fetchFn);

      expect(result).toBeNull();
      expect(mockRedisClient.set).not.toHaveBeenCalled(); // Don't cache null
    });
  });
});