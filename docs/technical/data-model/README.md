# Data Model Documentation

## Current Schema

The Onbord database uses **30 tables** defined in `/migrations/000_unified_schema.sql`.

For complete schema reference, see: [`/docs/technical/data-model.md`](../data-model.md)

## Active Documentation

- [`CLEAN-SLATE-DATA-MODEL-ANALYSIS.md`](./CLEAN-SLATE-DATA-MODEL-ANALYSIS.md) - Why we kept the current schema
- [`soft-delete-type-updates.md`](./soft-delete-type-updates.md) - TypeScript updates needed
- [`repository-pattern.md`](./repository-pattern.md) - How to use repositories
- [`hubspot-import-changes.md`](./hubspot-import-changes.md) - HubSpot integration

## Key Points

1. **30 tables total** (28 active after removing unused ones)
2. **All tables have soft delete** (`deleted_at` field)
3. **Foreign keys use SET NULL** (except junction tables)
4. **Complete audit fields** on all tables
5. **Repository pattern** for all data access