import {
  calculateEstimateMetrics,
  calculateTeamUtilization,
  calculateWeeklyBurn,
  calculateProjectDuration,
  validateEstimateData,
  calculateBudgetHealth,
  projectEstimateCompletion
} from '../../../src/shared/utils/estimate-calculations';

describe('Estimate Calculations', () => {
  const mockEstimate = {
    id: '1',
    revenue: 100000,
    expenses: 20000,
    start_date: '2024-01-01',
    end_date: '2024-03-31',
    team_members: [
      {
        id: '1',
        name: '<PERSON>',
        rate: 150,
        allocation: 80,
        hours_allocated: 320
      },
      {
        id: '2',
        name: '<PERSON>',
        rate: 200,
        allocation: 60,
        hours_allocated: 240
      }
    ],
    time_allocations: [
      { week: '2024-W01', hours: 80 },
      { week: '2024-W02', hours: 100 },
      { week: '2024-W03', hours: 90 }
    ]
  };

  describe('calculateEstimateMetrics', () => {
    it('should calculate basic metrics correctly', () => {
      const metrics = calculateEstimateMetrics(mockEstimate);

      expect(metrics.profit).toBe(80000);
      expect(metrics.profit_margin).toBe(80);
      expect(metrics.total_hours).toBe(560);
      expect(metrics.average_rate).toBe(171.43); // Weighted average
      expect(metrics.duration_weeks).toBe(13);
    });

    it('should handle zero revenue', () => {
      const estimate = { ...mockEstimate, revenue: 0 };
      const metrics = calculateEstimateMetrics(estimate);

      expect(metrics.profit).toBe(-20000);
      expect(metrics.profit_margin).toBe(0);
    });

    it('should calculate team costs', () => {
      const metrics = calculateEstimateMetrics(mockEstimate);

      expect(metrics.team_cost).toBe(96000); // (320 * 150) + (240 * 200)
      expect(metrics.total_cost).toBe(116000); // team_cost + expenses
    });

    it('should handle empty team', () => {
      const estimate = { ...mockEstimate, team_members: [] };
      const metrics = calculateEstimateMetrics(estimate);

      expect(metrics.total_hours).toBe(0);
      expect(metrics.average_rate).toBe(0);
      expect(metrics.team_cost).toBe(0);
    });
  });

  describe('calculateTeamUtilization', () => {
    it('should calculate utilization for each team member', () => {
      const utilization = calculateTeamUtilization(mockEstimate);

      expect(utilization).toHaveLength(2);
      expect(utilization[0]).toEqual({
        member_id: '1',
        member_name: 'John Doe',
        allocated_hours: 320,
        capacity_hours: 416, // 13 weeks * 40 hours * 0.8 allocation
        utilization_percentage: 76.92,
        revenue_contribution: 48000
      });
    });

    it('should handle over-allocation', () => {
      const overAllocated = {
        ...mockEstimate,
        team_members: [{
          id: '1',
          name: 'John Doe',
          rate: 150,
          allocation: 120, // 120% allocation
          hours_allocated: 500
        }]
      };

      const utilization = calculateTeamUtilization(overAllocated);
      
      expect(utilization[0].utilization_percentage).toBeGreaterThan(100);
      expect(utilization[0].over_allocated).toBe(true);
    });

    it('should calculate revenue contribution proportionally', () => {
      const utilization = calculateTeamUtilization(mockEstimate);
      const totalContribution = utilization.reduce((sum, u) => sum + u.revenue_contribution, 0);

      expect(totalContribution).toBeCloseTo(100000);
    });
  });

  describe('calculateWeeklyBurn', () => {
    it('should calculate weekly burn rate', () => {
      const burn = calculateWeeklyBurn(mockEstimate);

      expect(burn.weekly_team_cost).toBe(7384.62); // team_cost / duration_weeks
      expect(burn.weekly_expenses).toBe(1538.46); // expenses / duration_weeks
      expect(burn.weekly_total_burn).toBe(8923.08);
      expect(burn.weeks_of_runway).toBe(11.21); // revenue / weekly_total_burn
    });

    it('should handle custom time period', () => {
      const burn = calculateWeeklyBurn(mockEstimate, '2024-01-01', '2024-01-31');

      expect(burn.duration_weeks).toBe(4.43); // ~31 days / 7
      expect(burn.weekly_team_cost).toBeGreaterThan(0);
    });

    it('should track burn rate trend', () => {
      const estimateWithActuals = {
        ...mockEstimate,
        actuals: {
          hours_logged: 280,
          expenses_incurred: 10000,
          as_of_date: '2024-02-01'
        }
      };

      const burn = calculateWeeklyBurn(estimateWithActuals);

      expect(burn.actual_burn_rate).toBeDefined();
      expect(burn.burn_rate_variance).toBeDefined();
      expect(burn.on_track).toBeDefined();
    });
  });

  describe('calculateProjectDuration', () => {
    it('should calculate duration in various units', () => {
      const duration = calculateProjectDuration('2024-01-01', '2024-03-31');

      expect(duration.days).toBe(90);
      expect(duration.weeks).toBe(12.86);
      expect(duration.months).toBe(3);
      expect(duration.business_days).toBe(64); // Excluding weekends
    });

    it('should handle same start and end date', () => {
      const duration = calculateProjectDuration('2024-01-01', '2024-01-01');

      expect(duration.days).toBe(0);
      expect(duration.weeks).toBe(0);
      expect(duration.business_days).toBe(0);
    });

    it('should handle invalid dates', () => {
      const duration = calculateProjectDuration('invalid', '2024-01-01');

      expect(duration.error).toBeDefined();
      expect(duration.days).toBe(0);
    });

    it('should exclude holidays from business days', () => {
      const duration = calculateProjectDuration(
        '2023-12-20', 
        '2024-01-10',
        { excludeHolidays: true }
      );

      expect(duration.business_days).toBeLessThan(duration.days * 0.714); // Accounts for holidays
    });
  });

  describe('validateEstimateData', () => {
    it('should validate correct estimate data', () => {
      const validation = validateEstimateData(mockEstimate);

      expect(validation.is_valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should catch negative revenue', () => {
      const invalid = { ...mockEstimate, revenue: -1000 };
      const validation = validateEstimateData(invalid);

      expect(validation.is_valid).toBe(false);
      expect(validation.errors).toContain('Revenue cannot be negative');
    });

    it('should validate date ranges', () => {
      const invalid = {
        ...mockEstimate,
        start_date: '2024-03-31',
        end_date: '2024-01-01'
      };
      const validation = validateEstimateData(invalid);

      expect(validation.is_valid).toBe(false);
      expect(validation.errors).toContain('End date must be after start date');
    });

    it('should check team member allocations', () => {
      const invalid = {
        ...mockEstimate,
        team_members: [
          { ...mockEstimate.team_members[0], allocation: 150 },
          { ...mockEstimate.team_members[1], allocation: 200 }
        ]
      };
      const validation = validateEstimateData(invalid);

      expect(validation.warnings).toContain('Team member allocations exceed 100%');
    });

    it('should validate hour allocations match', () => {
      const invalid = {
        ...mockEstimate,
        team_members: [{ ...mockEstimate.team_members[0], hours_allocated: 1000 }]
      };
      const validation = validateEstimateData(invalid);

      expect(validation.warnings).toContain('Allocated hours exceed project capacity');
    });
  });

  describe('calculateBudgetHealth', () => {
    it('should assess budget health status', () => {
      const health = calculateBudgetHealth(mockEstimate, {
        hours_logged: 100,
        expenses_incurred: 5000,
        completion_percentage: 25
      });

      expect(health.status).toBe('healthy');
      expect(health.budget_consumed_percentage).toBe(23.96); // (100*171.43 + 5000) / 116000
      expect(health.projected_variance).toBeCloseTo(0);
    });

    it('should detect budget overrun risk', () => {
      const health = calculateBudgetHealth(mockEstimate, {
        hours_logged: 300,
        expenses_incurred: 15000,
        completion_percentage: 25
      });

      expect(health.status).toBe('at_risk');
      expect(health.budget_consumed_percentage).toBeGreaterThan(50);
      expect(health.projected_overrun).toBeGreaterThan(0);
    });

    it('should handle completed projects', () => {
      const health = calculateBudgetHealth(mockEstimate, {
        hours_logged: 560,
        expenses_incurred: 20000,
        completion_percentage: 100
      });

      expect(health.status).toBe('completed');
      expect(health.final_margin).toBeDefined();
    });
  });

  describe('projectEstimateCompletion', () => {
    it('should project completion date based on velocity', () => {
      const projection = projectEstimateCompletion(mockEstimate, {
        hours_logged: 140,
        start_date: '2024-01-01',
        as_of_date: '2024-02-01'
      });

      expect(projection.estimated_completion_date).toBeDefined();
      expect(projection.days_ahead_or_behind).toBeDefined();
      expect(projection.velocity_factor).toBeCloseTo(1.0, 1);
    });

    it('should detect if project is behind schedule', () => {
      const projection = projectEstimateCompletion(mockEstimate, {
        hours_logged: 50, // Low progress
        start_date: '2024-01-01',
        as_of_date: '2024-02-15' // 6 weeks in
      });

      expect(projection.on_track).toBe(false);
      expect(projection.days_ahead_or_behind).toBeLessThan(0);
      expect(projection.recommended_actions).toContain('Increase team velocity');
    });

    it('should handle ahead of schedule scenarios', () => {
      const projection = projectEstimateCompletion(mockEstimate, {
        hours_logged: 400, // High progress
        start_date: '2024-01-01',
        as_of_date: '2024-02-01'
      });

      expect(projection.on_track).toBe(true);
      expect(projection.days_ahead_or_behind).toBeGreaterThan(0);
      expect(projection.early_completion_bonus).toBeDefined();
    });

    it('should calculate required velocity to meet deadline', () => {
      const projection = projectEstimateCompletion(mockEstimate, {
        hours_logged: 100,
        start_date: '2024-01-01',
        as_of_date: '2024-03-01' // Getting close to deadline
      });

      expect(projection.required_weekly_hours).toBeGreaterThan(
        projection.current_weekly_velocity
      );
      expect(projection.feasibility).toBe('challenging');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing data gracefully', () => {
      const incomplete = {
        revenue: 50000,
        // Missing other required fields
      };

      const metrics = calculateEstimateMetrics(incomplete as any);
      expect(metrics.error).toBeDefined();
    });

    it('should handle extreme values', () => {
      const extreme = {
        ...mockEstimate,
        revenue: Number.MAX_SAFE_INTEGER,
        expenses: 1
      };

      const metrics = calculateEstimateMetrics(extreme);
      expect(metrics.profit_margin).toBeCloseTo(100);
    });

    it('should handle zero duration projects', () => {
      const sameDay = {
        ...mockEstimate,
        start_date: '2024-01-01',
        end_date: '2024-01-01'
      };

      const metrics = calculateEstimateMetrics(sameDay);
      expect(metrics.duration_weeks).toBe(0);
      expect(metrics.error).toBeDefined();
    });
  });
});