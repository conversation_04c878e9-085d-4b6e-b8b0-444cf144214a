import React, { forwardRef } from 'react'; import { cardVariants, type CardVariantProps } from '../../utils/component-variants'; import { cn } from '../../utils/component-variants'; interface CardProps extends CardVariantProps { children: React.ReactNode; className?: string; /** * Enable responsive behavior using container queries * When true, the card will adapt its layout based on its container width */ responsive?: boolean; /** * HTML element to render as (div by default) */ as?: keyof JSX.IntrinsicElements; /** * Optional click handler for interactive cards */ onClick?: () => void; /** * Optional id for the card element */ id?: string; /** * ARIA label for accessibility */ 'aria-label'?: string; /** * ARIA described by for accessibility */ 'aria-describedby'?: string; } /** * A shared, adaptive Card component that uses container queries * to adjust its internal layout based on its own size. * * Features: * - Container query support for truly responsive design * - Type-safe variant system for consistent styling * - Accessibility support with proper ARIA attributes * - Dark mode support built-in * - Flexible content layout with fluid spacing * * @example * ```tsx * <Card variant="interactive" size="comfortable" shadow="lg"> * <h3>Card Title</h3> * <p>Card content that adapts to container size</p> * </Card> * ``` */ export const Card = forwardRef<HTMLDivElement, CardProps>(({ children, className, variant = 'default', size = 'default', shadow = 'default', responsive = true, as: Component = 'div', onClick, id, 'aria-label': ariaLabel, 'aria-describedby': ariaDescribedby, ...props }, ref) => { // Determine if card should be interactive const isInteractive = variant === 'interactive' || !!onClick; // Map old variants to modern design system classes const modernVariantMap = { default: 'card-modern', interactive: 'card-modern card-modern--interactive spring-bounce', bordered: 'card-modern border-2', }; const classes = cn( modernVariantMap[variant as keyof typeof modernVariantMap] || modernVariantMap.default, responsive && '@container', isInteractive && 'touch-manipulation haptic-light', className ); const cardProps = { ref, className: classes, onClick: isInteractive ? onClick : undefined, id, 'aria-label': ariaLabel, 'aria-describedby': ariaDescribedby, // Add appropriate ARIA attributes for interactive cards ...(isInteractive && { role: onClick ? 'button' : undefined, tabIndex: onClick ? 0 : undefined, onKeyDown: onClick ? (e: React.KeyboardEvent) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); onClick(); } } : undefined, }), ...props, }; return React.createElement(Component, cardProps, children); }); Card.displayName = 'Card'; // Legacy export for backwards compatibility export { Card as default };