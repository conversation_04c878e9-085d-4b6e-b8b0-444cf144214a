/**
 * Knowledge Graph Routes Test
 * 
 * Tests for the knowledge graph API endpoints.
 */
import { Request, Response } from 'express';
import { jest } from '@jest/globals';
import express from 'express';

// Mock dependencies
jest.mock('../../../../src/api/repositories/knowledge-graph-repository');
jest.mock('../../../../src/utils/backend-logger');

describe('Knowledge Graph API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;
  let mockKnowledgeGraphRepo: any;
  
  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      user: { id: 'user-123', email: '<EMAIL>' },
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });
  
  describe('GET /api/knowledge-graph', () => {
    it('should fetch complete knowledge graph with default options', async () => {
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      const mockGraphData = {
        nodes: [
          { id: '1', label: 'Company A', type: 'company' },
          { id: '2', label: 'John Doe', type: 'contact' },
          { id: '3', label: 'Deal 1', type: 'deal' }
        ],
        links: [
          { source: '2', target: '1', type: 'works_at', strength: 5 },
          { source: '3', target: '1', type: 'belongs_to', strength: 5 }
        ],
        stats: {
          totalNodes: 3,
          totalLinks: 2,
          nodeTypes: { company: 1, contact: 1, deal: 1 },
          linkTypes: { works_at: 1, belongs_to: 1 }
        }
      };
      
      mockKnowledgeGraphRepo = {
        getKnowledgeGraph: jest.fn().mockResolvedValue(mockGraphData)
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const app = express();
      app.use('/api/knowledge-graph', knowledgeGraphRoutes.default);
      
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockKnowledgeGraphRepo.getKnowledgeGraph).toHaveBeenCalledWith({
        includeDeleted: false,
        entityTypes: ['company', 'contact', 'deal', 'project'],
        maxNodes: 1000
      });
      
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockGraphData
      });
    });
    
    it('should handle custom query parameters', async () => {
      mockRequest.query = {
        includeDeleted: 'true',
        entityTypes: 'company,deal',
        maxNodes: '500'
      };
      
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      mockKnowledgeGraphRepo = {
        getKnowledgeGraph: jest.fn().mockResolvedValue({
          nodes: [],
          links: [],
          stats: {
            totalNodes: 0,
            totalLinks: 0,
            nodeTypes: {},
            linkTypes: {}
          }
        })
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockKnowledgeGraphRepo.getKnowledgeGraph).toHaveBeenCalledWith({
        includeDeleted: true,
        entityTypes: ['company', 'deal'],
        maxNodes: 500
      });
    });
    
    it('should handle errors gracefully', async () => {
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      mockKnowledgeGraphRepo = {
        getKnowledgeGraph: jest.fn().mockRejectedValue(new Error('Database error'))
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Failed to fetch knowledge graph',
        message: 'Database error'
      });
    });
  });
  
  describe('GET /api/knowledge-graph/:entityId', () => {
    it('should fetch entity subgraph with default parameters', async () => {
      mockRequest.params = { entityId: 'company-123' };
      
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      const mockSubgraphData = {
        nodes: [
          { id: 'company-123', label: 'Test Company', type: 'company' },
          { id: 'contact-456', label: 'John Doe', type: 'contact' }
        ],
        links: [
          { source: 'contact-456', target: 'company-123', type: 'works_at', strength: 5 }
        ],
        stats: {
          totalNodes: 2,
          totalLinks: 1,
          nodeTypes: { company: 1, contact: 1 },
          linkTypes: { works_at: 1 }
        }
      };
      
      mockKnowledgeGraphRepo = {
        getEntitySubgraph: jest.fn().mockResolvedValue(mockSubgraphData)
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const app = express();
      app.use('/api/knowledge-graph', knowledgeGraphRoutes.default);
      
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:entityId' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockKnowledgeGraphRepo.getEntitySubgraph).toHaveBeenCalledWith(
        'company-123',
        'company',
        2
      );
      
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockSubgraphData
      });
    });
    
    it('should handle custom entity type and depth', async () => {
      mockRequest.params = { entityId: 'contact-789' };
      mockRequest.query = {
        entityType: 'contact',
        depth: '3'
      };
      
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      mockKnowledgeGraphRepo = {
        getEntitySubgraph: jest.fn().mockResolvedValue({
          nodes: [],
          links: [],
          stats: {
            totalNodes: 0,
            totalLinks: 0,
            nodeTypes: {},
            linkTypes: {}
          }
        })
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:entityId' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockKnowledgeGraphRepo.getEntitySubgraph).toHaveBeenCalledWith(
        'contact-789',
        'contact',
        3
      );
    });
    
    it('should handle non-existent entities', async () => {
      mockRequest.params = { entityId: 'non-existent' };
      
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      mockKnowledgeGraphRepo = {
        getEntitySubgraph: jest.fn().mockResolvedValue({
          nodes: [],
          links: [],
          stats: {
            totalNodes: 0,
            totalLinks: 0,
            nodeTypes: {},
            linkTypes: {}
          }
        })
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:entityId' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          nodes: [],
          links: [],
          stats: {
            totalNodes: 0,
            totalLinks: 0,
            nodeTypes: {},
            linkTypes: {}
          }
        }
      });
    });
    
    it('should handle errors in subgraph fetching', async () => {
      mockRequest.params = { entityId: 'company-123' };
      
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      mockKnowledgeGraphRepo = {
        getEntitySubgraph: jest.fn().mockRejectedValue(new Error('Network error'))
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:entityId' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Failed to fetch entity subgraph',
        message: 'Network error'
      });
    });
    
    it('should validate depth parameter', async () => {
      mockRequest.params = { entityId: 'company-123' };
      mockRequest.query = { depth: 'invalid' };
      
      const KnowledgeGraphRepository = (await import('../../../../src/api/repositories/knowledge-graph-repository')).KnowledgeGraphRepository;
      
      mockKnowledgeGraphRepo = {
        getEntitySubgraph: jest.fn().mockResolvedValue({
          nodes: [],
          links: [],
          stats: {
            totalNodes: 0,
            totalLinks: 0,
            nodeTypes: {},
            linkTypes: {}
          }
        })
      };
      
      KnowledgeGraphRepository.mockImplementation(() => mockKnowledgeGraphRepo);
      
      const knowledgeGraphRoutes = await import('../../../../src/api/routes/knowledge-graph');
      const handler = knowledgeGraphRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:entityId' && layer.route?.methods?.get
      )?.route?.stack[0]?.handle;
      
      await handler(mockRequest as Request, mockResponse as Response, mockNext);
      
      // Should use default depth of 2 when invalid
      expect(mockKnowledgeGraphRepo.getEntitySubgraph).toHaveBeenCalledWith(
        'company-123',
        'company',
        2
      );
    });
  });
});