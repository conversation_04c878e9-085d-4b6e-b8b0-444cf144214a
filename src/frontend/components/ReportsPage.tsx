import React, { useState } from "react"; import UtilizationReport from "./Reports/UtilizationReport"; import XeroReportsPage from "./Reports/XeroReportsPage"; import { LoadingProvider } from "../contexts/LoadingContext"; /** * Reports page component that will display various reports */ const ReportsPage: React.FC = () => { // State for active sub-tab (can be expanded later) const [activeSubTab, setActiveSubTab] = useState< "utilization" | "harvest" | "xero" >("utilization"); return ( <div className="space-y-6"> <div className="flex justify-between items-center"> <h1 className="text-2xl font-bold text-gray-900 dark:text-white"> Reports </h1> </div> {/* Sub-tabs for different report types */} <div className="border-b border-gray-200 dark:border-gray-700"> <nav className="flex space-x-8" aria-label="Report Types"> <button onClick={() => setActiveSubTab("utilization")} className={`py-4 px-1 border-b-2 font-medium text-sm ${ activeSubTab === "utilization" ? "border-secondary text-secondary dark:border-secondary-light dark:text-secondary-light" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} > Staff Utilisation </button> <button onClick={() => setActiveSubTab("harvest")} className={`py-4 px-1 border-b-2 font-medium text-sm ${ activeSubTab === "harvest" ? "border-secondary text-secondary dark:border-secondary-light dark:text-secondary-light" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} > Harvest Reports </button> <button onClick={() => setActiveSubTab("xero")} className={`py-4 px-1 border-b-2 font-medium text-sm ${ activeSubTab === "xero" ? "border-secondary text-secondary dark:border-secondary-light dark:text-secondary-light" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} > Xero Reports </button> </nav> </div> {/* Content area for reports */} <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6"> {activeSubTab === "utilization" ? ( <UtilizationReport /> ) : activeSubTab === "harvest" ? ( <div> <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4"> Harvest Reports </h2> <p className="text-gray-600 dark:text-gray-400"> This section will display reports from Harvest including time tracking, uninvoiced work, and expense reports. </p> </div> ) : ( <XeroReportsPage /> )} </div> </div> ); }; export default ReportsPage; 