import React, { useState, useEffect } from "react"; import LateBillsSummary from "./LateBillsSummary"; import LateInvoicesSummary from "./LateInvoicesSummary"; import LatePaymentsNetEffect from "./LatePaymentsNetEffect"; interface LatePaymentsSummaryProps { className?: string; } /** * A container component that groups all late payment information into a single expandable card */ const LatePaymentsSummary: React.FC<LatePaymentsSummaryProps> = ({ className = "", }) => { const [expanded, setExpanded] = useState(false); return ( <div className={`${className}`}> {/* Summary card showing just the net effect when collapsed */} <div className={` border border-gray-300 dark:border-gray-500 rounded-lg bg-white dark:bg-gray-800 overflow-hidden shadow-sm dark:shadow-lg dark:shadow-gray-900/50 transition-all duration-200 ease-in-out ${expanded ? "mb-2" : "mb-0"} `} > <button onClick={() => setExpanded(!expanded)} className="w-full px-3 py-2 flex justify-between items-center bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 border-none cursor-pointer transition-colors rounded-lg" > <span className="text-base font-semibold text-gray-700 dark:text-gray-100 transition-colors duration-200"> Late Payments </span> <div className="flex items-center gap-2"> {/* Only show net effect in the summary */} <LatePaymentsNetEffect inSummary={true} className="flex-grow-0" /> {/* Expand/collapse icon */} <svg className={`h-5 w-5 transform transition-transform duration-200 text-gray-500 dark:text-gray-300 ${ expanded ? "rotate-180" : "" }`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" > <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /> </svg> </div> </button> </div> {/* Expanded details with both bills and invoices */} {expanded && ( <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px", transition: "all 0.3s ease", }} className="dark:bg-transparent rounded-md p-1" > <LateBillsSummary /> <LateInvoicesSummary /> </div> )} </div> ); }; export default LatePaymentsSummary; 