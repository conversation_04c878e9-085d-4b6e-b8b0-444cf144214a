import React from "react"; interface LoadingIndicatorProps { size?: "small" | "medium" | "large"; text?: string; fullPage?: boolean; className?: string; } /** * Reusable loading indicator component * Can be used in different sizes and with optional text */ const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ size = "medium", text, fullPage = false, className = "", }) => { // Calculate sizes based on prop const spinnerSizes = { small: "w-4 h-4", medium: "w-8 h-8", large: "w-12 h-12", }; const textSizes = { small: "text-xs", medium: "text-sm", large: "text-base", }; // Base container classes let containerClasses = "flex flex-col items-center justify-center"; // Add fullPage styling if requested if (fullPage) { containerClasses += " fixed inset-0 bg-white bg-opacity-80 dark:bg-gray-900 dark:bg-opacity-80 z-50"; } else { containerClasses += " py-8"; } return ( <div className={`${containerClasses} ${className}`}> <svg className={`animate-spin text-blue-500 ${spinnerSizes[size]}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> {text && ( <p className={`mt-2 text-gray-700 dark:text-gray-300 ${textSizes[size]}`} > {text} </p> )} </div> ); }; /** * Skeleton loader for cards and summary items * Following the adaptive-card pattern for consistent styling */ export const SkeletonCard: React.FC<{ className?: string; height?: string; }> = ({ className = "", height = "auto" }) => { // Check if we're showing a summary card (taller) or a single-line card const isSummaryCard = height === "80px"; return ( <div className={`animate-pulse ${className} dark:bg-gray-800`} style={{ minHeight: height || "40px", borderRadius: "0.5rem", boxShadow: "0 1px 2px rgba(0, 0, 0, 0.05)", backgroundColor: "rgb(243, 244, 246)", // light gray for light mode overflow: "hidden", }} > {isSummaryCard ? ( // Summary card layout with top/bottom sections <div style={{ display: "flex", flexDirection: "column", justifyContent: "space-between", padding: "12px 16px", height: "100%", }} > {/* Top section with icon and title */} <div style={{ display: "flex", alignItems: "center", gap: "8px" }}> <div style={{ width: "16px", height: "16px", borderRadius: "50%", backgroundColor: "rgb(229, 231, 235)", }} className="dark:btn-modern--secondary" ></div> <div> <div style={{ height: "10px", width: "80px", marginBottom: "6px", borderRadius: "4px", backgroundColor: "rgb(229, 231, 235)", }} className="dark:btn-modern--secondary" ></div> <div style={{ height: "8px", width: "60px", borderRadius: "4px", backgroundColor: "rgb(229, 231, 235)", }} className="dark:btn-modern--secondary" ></div> </div> </div> {/* Bottom section with value */} <div style={{ alignSelf: "flex-end" }}> <div style={{ height: "12px", width: "70px", borderRadius: "6px", backgroundColor: "rgb(229, 231, 235)", }} className="dark:btn-modern--secondary" ></div> </div> </div> ) : ( // Single-line card layout (for late payments summary) <div style={{ display: "flex", alignItems: "center", justifyContent: "space-between", padding: "12px 16px", height: "100%", }} > <div style={{ height: "10px", width: "100px", borderRadius: "5px", backgroundColor: "rgb(229, 231, 235)", }} className="dark:btn-modern--secondary" ></div> <div style={{ height: "10px", width: "60px", borderRadius: "5px", backgroundColor: "rgb(229, 231, 235)", }} className="dark:btn-modern--secondary" ></div> </div> )} </div> ); }; /** * Skeleton loader for charts */ export const SkeletonChart: React.FC<{ className?: string }> = ({ className = "", }) => { return ( <div className={`animate-pulse bg-gray-100 dark:bg-gray-800 rounded-lg p-4 ${className}`} > <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div> <div className="h-[200px] bg-gray-200 dark:bg-gray-700 rounded-lg mb-2"></div> <div className="flex justify-between mt-4"> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-[10%]"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-[10%]"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-[10%]"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-[10%]"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-[10%]"></div> </div> </div> ); }; /** * Skeleton loader for transaction table */ export const SkeletonTable: React.FC<{ className?: string; rows?: number }> = ({ className = "", rows = 5, }) => { return ( <div className={`animate-pulse bg-gray-100 dark:bg-gray-800 rounded-lg p-4 ${className}`} > <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div> {/* Table header */} <div className="grid grid-cols-5 gap-4 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700"> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div> </div> {/* Table rows */} {Array.from({ length: rows }).map((_, index) => ( <div key={index} className="grid grid-cols-5 gap-4 py-3 border-b border-gray-200 dark:border-gray-700" > <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div> <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div> </div> ))} </div> ); }; export default LoadingIndicator; 