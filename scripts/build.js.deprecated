#!/usr/bin/env node

/**
 * Simple Build Script
 * Builds the application for production deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔨 Building Upstream Financial Dashboard...');

// Clean previous build
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true, force: true });
}
fs.mkdirSync('dist', { recursive: true });

try {
  // Build backend (TypeScript to JavaScript)
  console.log('📦 Building backend...');
  try {
    console.log('  🔨 Compiling TypeScript...');
    execSync('npx tsc --project tsconfig.backend.json --outDir dist', { stdio: 'inherit' });
    console.log('  ✅ TypeScript compiled successfully');
  } catch (error) {
    console.log('  ⚠️  TypeScript compilation failed, copying source files...');
    // Fallback: Copy ALL source directories maintaining structure
    const sourceDirs = ['src', 'api', 'services', 'database', 'utils', 'constants', 'types', 'shared'];
    sourceDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        execSync(`cp -r ${dir} dist/`, { stdio: 'inherit' });
        console.log(`    ✅ Copied ${dir}/`);
      }
    });
  }

  // Build frontend
  console.log('📦 Building frontend...');
  try {
    execSync('npx vite build', { stdio: 'inherit' });
    console.log('  ✅ Frontend built successfully');
  } catch (error) {
    console.log('  ⚠️  Vite build failed, creating fallback...');
    // Create minimal frontend
    fs.writeFileSync('dist/index.html', `
<!DOCTYPE html>
<html>
<head>
  <title>Upstream Financial Dashboard</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
  <h1>Upstream Financial Dashboard</h1>
  <p>API server is running. Frontend temporarily unavailable.</p>
  <p><a href="/api/health">Check API Health</a></p>
</body>
</html>`);
  }

  // Copy essential files
  console.log('📋 Copying essential files...');
  
  // Copy scripts
  fs.mkdirSync('dist/scripts', { recursive: true });
  const essentialScripts = ['simple-migrate.js', 'health-check.js'];
  essentialScripts.forEach(script => {
    const src = `scripts/${script}`;
    const dest = `dist/scripts/${script}`;
    if (fs.existsSync(src)) {
      fs.copyFileSync(src, dest);
      console.log(`  ✅ Copied ${script}`);
    }
  });

  // Copy migrations
  if (fs.existsSync('migrations')) {
    execSync('cp -r migrations dist/', { stdio: 'inherit' });
    console.log('  ✅ Copied migrations');
  }

  // Copy tsconfig for ts-node
  if (fs.existsSync('tsconfig.backend.json')) {
    fs.copyFileSync('tsconfig.backend.json', 'dist/tsconfig.backend.json');
    console.log('  ✅ Copied tsconfig.backend.json');
  }

  // Copy public assets
  if (fs.existsSync('public')) {
    execSync('cp -r public dist/', { stdio: 'inherit' });
    console.log('  ✅ Copied public assets');
  }

  // Copy the FULL package.json to root so Render uses it
  console.log('📄 Creating production package.json...');
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Fix the start command to point to the correct server.js location
  pkg.scripts.start = 'node scripts/simple-migrate.js && node server.js';
  
  // Put complete package.json at ROOT level
  fs.writeFileSync('dist/package.json', JSON.stringify(pkg, null, 2));
  
  // ALSO put it in src/ subdirectory since Render looks there (if src exists)
  if (fs.existsSync('dist/src')) {
    fs.writeFileSync('dist/src/package.json', JSON.stringify(pkg, null, 2));
  }

  // Skip dependency installation - let Render handle it
  console.log('📦 Skipping dependency installation (Render will install fresh native modules)');

  // Create simple server entry point
  console.log('🚀 Creating server entry point...');
  fs.writeFileSync('dist/server.js', `
// Production server entry point
const path = require('path');

console.log('🚀 Starting Upstream Financial Dashboard');
console.log('Environment:', process.env.NODE_ENV || 'production');
console.log('Port:', process.env.PORT || 3002);

try {
  // Look for compiled server or TypeScript source
  if (require('fs').existsSync(path.join(__dirname, 'api', 'server.js'))) {
    require(path.join(__dirname, 'api', 'server.js'));
  } else if (require('fs').existsSync(path.join(__dirname, 'src', 'api', 'server.ts'))) {
    // Register ts-node for TypeScript execution
    require('ts-node').register({
      project: path.join(__dirname, 'tsconfig.backend.json'),
      transpileOnly: true,
      compilerOptions: {
        module: 'commonjs',
        target: 'es2020',
        skipLibCheck: true,
        esModuleInterop: true
      }
    });
    require(path.join(__dirname, 'src', 'api', 'server.ts'));
  } else {
    throw new Error('No server file found');
  }
} catch (error) {
  console.error('Failed to start server:', error.message);
  console.error(error.stack);
  process.exit(1);
}
`);

  console.log('');
  console.log('✅ Build completed successfully!');
  console.log('📁 Output directory: dist/');

} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}