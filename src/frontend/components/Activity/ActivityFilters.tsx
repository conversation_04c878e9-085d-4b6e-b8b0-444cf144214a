/**
 * Activity Filters Component
 *
 * This component provides filtering controls for the activity feed.
 */

import React, { useState } from 'react';
import { ActivityFilters as ActivityFiltersType, ActivityType, ActivitySource, ActivityImportance } from '../../types/activity-types';

interface ActivityFiltersProps {
  filters: ActivityFiltersType;
  onFiltersChange: (filters: Partial<ActivityFiltersType>) => void;
}

/**
 * Activity Filters component
 */
const ActivityFilters: React.FC<ActivityFiltersProps> = ({ filters, onFiltersChange }) => {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');

  // Activity type options
  const activityTypes: { value: ActivityType; label: string }[] = [
    { value: 'deal_created', label: 'Deal Created' },
    { value: 'deal_updated', label: 'Deal Updated' },
    { value: 'deal_stage_changed', label: 'Deal Stage Changed' },
    { value: 'estimate_created', label: 'Estimate Created' },
    { value: 'estimate_published', label: 'Estimate Published' },
    { value: 'company_created', label: 'Company Created' },
    { value: 'company_linked', label: 'Company Linked' },
    { value: 'note_added', label: 'Note Added' },
    { value: 'hubspot_sync_completed', label: 'HubSpot Sync' },
    { value: 'xero_sync_completed', label: 'Xero Sync' },
    { value: 'harvest_sync_completed', label: 'Harvest Sync' },
  ];

  // Source options
  const sourceOptions: { value: ActivitySource; label: string }[] = [
    { value: 'user', label: 'User' },
    { value: 'system', label: 'System' },
    { value: 'hubspot', label: 'HubSpot' },
    { value: 'xero', label: 'Xero' },
    { value: 'harvest', label: 'Harvest' },
  ];

  // Importance options
  const importanceOptions: { value: ActivityImportance; label: string }[] = [
    { value: 'low', label: 'Low' },
    { value: 'normal', label: 'Normal' },
    { value: 'high', label: 'High' },
  ];

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      onFiltersChange({ search: value || undefined });
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // Handle type filter change
  const handleTypeChange = (type: ActivityType, checked: boolean) => {
    const currentTypes = Array.isArray(filters.type) ? filters.type : filters.type ? [filters.type] : [];
    
    let newTypes: ActivityType[];
    if (checked) {
      newTypes = [...currentTypes, type];
    } else {
      newTypes = currentTypes.filter(t => t !== type);
    }

    onFiltersChange({ 
      type: newTypes.length > 0 ? newTypes : undefined 
    });
  };

  // Handle source filter change
  const handleSourceChange = (source: ActivitySource, checked: boolean) => {
    const currentSources = Array.isArray(filters.source) ? filters.source : filters.source ? [filters.source] : [];
    
    let newSources: ActivitySource[];
    if (checked) {
      newSources = [...currentSources, source];
    } else {
      newSources = currentSources.filter(s => s !== source);
    }

    onFiltersChange({ 
      source: newSources.length > 0 ? newSources : undefined 
    });
  };

  // Handle date range change
  const handleDateFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({ dateFrom: e.target.value || undefined });
  };

  const handleDateToChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFiltersChange({ dateTo: e.target.value || undefined });
  };

  // Handle importance change
  const handleImportanceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFiltersChange({ importance: e.target.value as ActivityImportance || undefined });
  };

  // Handle read status change
  const handleReadStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onFiltersChange({ 
      isRead: value === 'read' ? true : value === 'unread' ? false : undefined 
    });
  };

  const selectedTypes = Array.isArray(filters.type) ? filters.type : filters.type ? [filters.type] : [];
  const selectedSources = Array.isArray(filters.source) ? filters.source : filters.source ? [filters.source] : [];

  return (
    <div className="space-y-6">
      {/* Search */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Search
        </label>
        <input
          type="text"
          value={searchTerm}
          onChange={handleSearchChange}
          placeholder="Search activities..."
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
        />
      </div>

      {/* Date Range */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Date Range
        </label>
        <div className="space-y-2">
          <input
            type="date"
            value={filters.dateFrom || ''}
            onChange={handleDateFromChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          />
          <input
            type="date"
            value={filters.dateTo || ''}
            onChange={handleDateToChange}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          />
        </div>
      </div>

      {/* Read Status */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Read Status
        </label>
        <select
          value={filters.isRead === true ? 'read' : filters.isRead === false ? 'unread' : 'all'}
          onChange={handleReadStatusChange}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
        >
          <option value="all">All</option>
          <option value="unread">Unread</option>
          <option value="read">Read</option>
        </select>
      </div>

      {/* Importance */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Importance
        </label>
        <select
          value={filters.importance || ''}
          onChange={handleImportanceChange}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
        >
          <option value="">All</option>
          {importanceOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Activity Types */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Activity Types
        </label>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {activityTypes.map(type => (
            <label key={type.value} className="flex items-center">
              <input
                type="checkbox"
                checked={selectedTypes.includes(type.value)}
                onChange={(e) => handleTypeChange(type.value, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {type.label}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Sources */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Sources
        </label>
        <div className="space-y-2">
          {sourceOptions.map(source => (
            <label key={source.value} className="flex items-center">
              <input
                type="checkbox"
                checked={selectedSources.includes(source.value)}
                onChange={(e) => handleSourceChange(source.value, e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
              />
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {source.label}
              </span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ActivityFilters;
