import React from "react"; import { Routes as RouterRoutes, Route } from "react-router-dom"; import { routes } from "../config/routes"; import { ErrorBoundary } from "./common/ErrorBoundary"; /** * Main Routes component that handles rendering the appropriate component based on the current URL * Authentication is now handled at the App level, so this component only handles routing * Each route is wrapped in an error boundary to prevent cascading failures */ const Routes: React.FC = () => { return ( <RouterRoutes> {routes.map((route) => ( <Route key={route.path} path={route.path} element={ <ErrorBoundary level="page" resetKeys={[route.path]} resetOnPropsChange > {route.element} </ErrorBoundary> } /> ))} </RouterRoutes> ); }; export default Routes; 