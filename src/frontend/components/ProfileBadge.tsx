import React, { useState, useEffect } from "react"; import { useNavigate } from "react-router-dom"; import { getGravatarUrl } from "../utils/gravatar"; interface ProfileBadgeProps { userName: string; userEmail: string; onLogout: () => void; } const ProfileBadge: React.FC<ProfileBadgeProps> = ({ userName, userEmail, onLogout, }) => { const [isOpen, setIsOpen] = useState(false); const [gravatarUrl, setGravatarUrl] = useState<string | null>(null); const [gravatarError, setGravatarError] = useState(false); const navigate = useNavigate(); // Generate initials from user's name const initials = userName .split(" ") .map((name) => name[0]) .join("") .toUpperCase() .substring(0, 2); // Get Gravatar URL when email changes useEffect(() => { if (userEmail) { try { // Use the proper getGravatarUrl utility that creates an MD5 hash const url = getGravatarUrl(userEmail, 80, "identicon"); setGravatarUrl(url); } catch (error) { console.error("Error setting Gravatar URL:", error); setGravatarError(true); } } }, [userEmail]); const toggleDropdown = () => setIsOpen(!isOpen); // Determine whether to show initials or Gravatar const showInitials = gravatarError || !gravatarUrl; return ( <div className="relative"> <button onClick={toggleDropdown} className="flex items-center space-x-2 focus:outline-none" > {showInitials ? ( <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium"> {initials} </div> ) : ( <img src={gravatarUrl || ""} alt={userName} className="w-8 h-8 rounded-full border border-gray-200 dark:border-gray-700" onError={() => setGravatarError(true)} /> )} <span className="hidden md:block text-gray-700 dark:text-gray-300"> {userName.split(" ")[0]} </span> </button> {isOpen && ( <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 border border-gray-200 dark:border-gray-700"> <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700"> <p className="text-sm font-medium text-gray-900 dark:text-white truncate"> {userName} </p> <p className="text-xs text-gray-500 dark:text-gray-400 truncate"> {userEmail} </p> </div> <a href="#" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" onClick={(e) => { e.preventDefault(); setIsOpen(false); navigate("/account"); }} > Account Settings </a> <a href="#" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" onClick={(e) => { e.preventDefault(); setIsOpen(false); onLogout(); }} > Logout </a> </div> )} </div> ); }; export default ProfileBadge; 