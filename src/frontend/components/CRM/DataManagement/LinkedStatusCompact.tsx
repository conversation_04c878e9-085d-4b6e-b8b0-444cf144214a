import React from "react"; import { LinkIcon, CheckCircleIcon, XMarkIcon } from "@heroicons/react/24/outline"; import { CheckCircleIcon as CheckCircleSolid } from "@heroicons/react/24/solid"; interface LinkedStatusCompactProps { system: "hubspot" | "harvest"; isLinked: boolean; externalId?: string | number | null; onLink?: () => void; onUnlink?: () => void; disabled?: boolean; } /** * Compact linked status component for table cells * Features a modern, streamlined design perfect for data tables */ const LinkedStatusCompact: React.FC<LinkedStatusCompactProps> = ({ system, isLinked, externalId, onLink, onUnlink, disabled = false, }) => { const systemConfig = { hubspot: { name: "HubSpot", bgColor: "bg-orange-50 dark:bg-orange-900/20", borderColor: "border-orange-200 dark:border-orange-800", textColor: "text-orange-700 dark:text-orange-300", iconColor: "text-orange-600 dark:text-orange-400", hoverBg: "hover:bg-orange-100 dark:hover:bg-orange-900/30", }, harvest: { name: "Harvest", bgColor: "bg-green-50 dark:bg-green-900/20", borderColor: "border-green-200 dark:border-green-800", textColor: "text-green-700 dark:text-green-300", iconColor: "text-green-600 dark:text-green-400", hoverBg: " dark:/30", }, }; const config = systemConfig[system]; if (isLinked) { return ( <div className={` inline-flex items-center gap-2 px-3 py-1.5 rounded-md border transition-all duration-200 ${config.bgColor} ${config.borderColor} ${!disabled && onUnlink ? config.hoverBg : ''} ${disabled ? 'opacity-50' : ''} `}> {/* Success Icon */} <CheckCircleSolid className={`w-4 h-4 ${config.iconColor}`} /> {/* System Name */} <span className={`text-xs font-medium ${config.textColor}`}> {config.name} </span> {/* External ID */} {externalId && ( <> <span className="text-gray-400 dark:text-gray-500">•</span> <span className="text-xs font-mono text-gray-600 dark:text-gray-400"> {externalId} </span> </> )} {/* Unlink Action */} {onUnlink && ( <button onClick={(e) => { e.stopPropagation(); onUnlink(); }} disabled={disabled} className={` ml-auto -mr-1 p-0.5 rounded transition-colors duration-150 ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'} text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 `} title={`Unlink from ${config.name}`} > <XMarkIcon className="w-3.5 h-3.5" /> </button> )} </div> ); } // Unlinked state return ( <button onClick={onLink} disabled={disabled || !onLink} className={` inline-flex items-center gap-2 px-3 py-1.5 rounded-md border-2 border-dashed transition-all duration-200 ${disabled || !onLink ? 'cursor-not-allowed' : 'cursor-pointer'} ${disabled ? 'opacity-50' : 'opacity-70 hover:opacity-100'} border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/30 hover:bg-gray-100 dark:hover:bg-gray-700/50 text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 `} title={`Link to ${config.name}`} > <LinkIcon className="w-4 h-4" /> <span className="text-xs font-medium"> Link {config.name} </span> </button> ); }; export default LinkedStatusCompact;