import React from 'react';
import { ProcessedDecision } from '../../types/projection-audit-types';
import { formatDate, formatCurrency } from '../../utils';

interface UninvoicedWorkReconciliationProps {
  decision: ProcessedDecision;
}

const UninvoicedWorkReconciliation: React.FC<UninvoicedWorkReconciliationProps> = ({ decision }) => {
  return (
    <div className="flex flex-col space-y-2 p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
      {/* Title and explanation */}
      <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-2">
        <div className="flex items-center">
          <svg className="w-3.5 h-3.5 text-yellow-500 dark:text-yellow-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          <div className="text-[11px] font-medium text-gray-700 dark:text-gray-300">
            Uninvoiced Work Reconciliation
          </div>
        </div>
      </div>
      
      {/* Main content in a very compact layout */}
      <div className="grid grid-cols-12 gap-x-1 items-center">
        {/* Left side - Excluded uninvoiced work - 5 columns */}
        <div className="col-span-5 p-1.5 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-700">
          <div className="flex flex-col">
            <div className="flex items-center gap-1 mb-1">
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[9px] font-medium bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300">
                Uninvoiced
              </span>
              <div className="text-[9px] font-medium text-gray-700 dark:text-gray-300 truncate">
                {decision.formattedDescription}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1">
              <div className="text-[8px] text-gray-500 dark:text-gray-400">
                <span className="font-bold uppercase tracking-wide">Date</span><br/>
                {formatDate(decision.paymentDate)}
              </div>
              <div className="text-[8px] text-gray-500 dark:text-gray-400 text-right">
                <span className="font-bold uppercase tracking-wide">Amount</span><br/>
                {formatCurrency(decision.amount)}
              </div>
            </div>
          </div>
        </div>
        
        {/* Center connector - 2 columns */}
        <div className="col-span-2 flex items-center justify-center">
          <div className="bg-green-100 dark:bg-green-900/30 rounded-full p-1">
            <svg className="w-3 h-3 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </div>
        </div>
        
        {/* Right side - Projected Income - 5 columns */}
        <div className="col-span-5 p-1.5 bg-gray-50 dark:bg-gray-750 rounded border border-gray-200 dark:border-gray-700">
          <div className="flex flex-col">
            <div className="flex items-center gap-1 mb-1">
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-[9px] font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                Projected
              </span>
              <div className="text-[9px] font-medium text-gray-700 dark:text-gray-300 truncate">
                {decision.formattedDescription}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1">
              <div className="text-[8px] text-gray-500 dark:text-gray-400">
                <span className="font-bold uppercase tracking-wide">Date</span><br/>
                {formatDate(decision.paymentDate)}
              </div>
              <div className="text-[8px] text-gray-500 dark:text-gray-400 text-right">
                <span className="font-bold uppercase tracking-wide">Usage</span><br/>
                <a 
                  href="https://onbord.harvestapp.com/reports/uninvoiced?kind=all_time"
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-700"
                  onClick={e => e.stopPropagation()}
                >
                  View Report
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Note at the bottom */}
      <div className="mt-1 text-[9px] text-gray-500 dark:text-gray-400 pl-2 border-l-2 border-yellow-300/50 dark:border-yellow-700">
        <span className="font-medium text-yellow-600 dark:text-yellow-400">Note:</span> 
        This uninvoiced work was excluded because there is already a projected income from the same project scheduled within 3 days.
      </div>
    </div>
  );
};

export default UninvoicedWorkReconciliation;