import { test, expect, Page } from '@playwright/test';
import { setupAuth } from './utils';

test.describe('CRM Workflow E2E Tests', () => {
  let page: Page;

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage();
    await setupAuth(page);
  });

  test.afterEach(async () => {
    await page.close();
  });

  test('Complete deal creation and management workflow', async () => {
    // Navigate to CRM
    await page.goto('/crm');
    await expect(page.locator('h1')).toContainText('CRM');

    // Navigate to deals
    await page.click('text=Deals');
    await expect(page.locator('h2')).toContainText('Deals');

    // Create new deal
    await page.click('button:has-text("New Deal")');
    
    // Fill deal form
    await page.fill('input[name="dealName"]', 'Test Enterprise Deal');
    await page.selectOption('select[name="companyId"]', { label: 'Acme Corp' });
    await page.fill('input[name="amount"]', '50000');
    await page.selectOption('select[name="stage"]', 'qualification');
    await page.fill('input[name="closeDate"]', '2024-12-31');
    await page.fill('input[name="probability"]', '0.75');
    await page.fill('textarea[name="notes"]', 'High priority enterprise deal');

    // Submit form
    await page.click('button[type="submit"]');

    // Verify success message
    await expect(page.locator('.toast-success')).toContainText('Deal created successfully');

    // Verify deal appears in pipeline
    await expect(page.locator('.deal-card')).toContainText('Test Enterprise Deal');
    await expect(page.locator('.deal-card')).toContainText('$50,000');

    // Click on deal to edit
    await page.click('.deal-card:has-text("Test Enterprise Deal")');
    
    // Update deal stage
    await page.selectOption('select[name="stage"]', 'negotiation');
    await expect(page.locator('.toast-success')).toContainText('Deal updated');

    // Add contact to deal
    await page.click('button:has-text("Add Contact")');
    await page.click('.contact-option:has-text("John Doe")');
    await page.selectOption('select[name="role"]', 'Decision Maker');
    await page.click('button:has-text("Save Contact")');

    // Link estimate to deal
    await page.click('button:has-text("Link Estimate")');
    await page.click('.estimate-option:has-text("Q4 Project Estimate")');
    await page.click('button:has-text("Link Selected")');

    // Add note
    await page.fill('textarea[placeholder="Add a note..."]', 'Initial meeting went well');
    await page.click('button:has-text("Add Note")');
    await expect(page.locator('.note-item')).toContainText('Initial meeting went well');

    // Move deal through pipeline
    await page.click('button:has-text("Move to Proposal")');
    await expect(page.locator('.deal-stage-badge')).toContainText('proposal');

    // Close deal as won
    await page.click('button:has-text("Mark as Won")');
    await page.click('button:has-text("Confirm")');
    
    await expect(page.locator('.deal-status')).toContainText('Won');
    await expect(page.locator('.toast-success')).toContainText('Deal closed successfully');
  });

  test('Company and contact management workflow', async () => {
    // Navigate to Companies
    await page.goto('/crm/companies');
    
    // Create new company
    await page.click('button:has-text("New Company")');
    
    await page.fill('input[name="name"]', 'New Tech Corp');
    await page.fill('input[name="website"]', 'https://newtechcorp.com');
    await page.fill('input[name="industry"]', 'Technology');
    await page.fill('input[name="employeeCount"]', '150');
    await page.fill('textarea[name="description"]', 'Innovative tech company');
    
    await page.click('button[type="submit"]');
    await expect(page.locator('.toast-success')).toContainText('Company created');

    // Navigate to company detail
    await page.click('.company-card:has-text("New Tech Corp")');
    
    // Add contact to company
    await page.click('button:has-text("Add Contact")');
    
    await page.fill('input[name="firstName"]', 'Jane');
    await page.fill('input[name="lastName"]', 'Smith');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '+1234567890');
    await page.fill('input[name="jobTitle"]', 'CEO');
    
    await page.click('button[type="submit"]');
    await expect(page.locator('.contact-list')).toContainText('Jane Smith');

    // Link to external systems
    await page.click('button:has-text("Link to HubSpot")');
    await page.click('.hubspot-company:has-text("New Tech Corp")');
    await page.click('button:has-text("Confirm Link")');
    
    await expect(page.locator('.linked-status')).toContainText('Linked to HubSpot');
  });

  test('Deal pipeline drag and drop', async () => {
    await page.goto('/crm/deals');

    // Find a deal in qualification stage
    const dealCard = page.locator('.deal-card:has-text("Startup Package")').first();
    const negotiationColumn = page.locator('.pipeline-column:has-text("Negotiation")');

    // Drag deal to negotiation stage
    await dealCard.hover();
    await page.mouse.down();
    await negotiationColumn.hover();
    await page.mouse.up();

    // Verify deal moved
    await expect(negotiationColumn.locator('.deal-card:has-text("Startup Package")')).toBeVisible();
    await expect(page.locator('.toast-success')).toContainText('Deal stage updated');
  });

  test('Activity feed integration', async () => {
    // Create a deal
    await page.goto('/crm/deals');
    await page.click('button:has-text("New Deal")');
    
    await page.fill('input[name="dealName"]', 'Activity Test Deal');
    await page.selectOption('select[name="companyId"]', { index: 1 });
    await page.fill('input[name="amount"]', '25000');
    await page.click('button[type="submit"]');

    // Navigate to activity feed
    await page.goto('/activity');
    
    // Verify deal creation activity
    await expect(page.locator('.activity-item').first()).toContainText('created deal');
    await expect(page.locator('.activity-item').first()).toContainText('Activity Test Deal');
    
    // Click on activity to navigate to deal
    await page.click('.activity-item:has-text("Activity Test Deal")');
    await expect(page.url()).toContain('/crm/deals/');
    await expect(page.locator('h1')).toContainText('Activity Test Deal');
  });

  test('Search and filter functionality', async () => {
    await page.goto('/crm');

    // Use global search
    await page.fill('input[placeholder="Search CRM..."]', 'Acme');
    await page.press('input[placeholder="Search CRM..."]', 'Enter');

    // Verify search results
    await expect(page.locator('.search-results')).toContainText('Acme Corp');
    
    // Click on result
    await page.click('.search-result:has-text("Acme Corp")');
    await expect(page.locator('h1')).toContainText('Acme Corp');

    // Test deal filters
    await page.goto('/crm/deals');
    
    // Filter by stage
    await page.selectOption('select[name="stageFilter"]', 'negotiation');
    await expect(page.locator('.deal-card')).toHaveCount(3); // Assuming 3 deals in negotiation

    // Filter by owner
    await page.selectOption('select[name="ownerFilter"]', '<EMAIL>');
    await expect(page.locator('.deal-card')).toHaveCount(2); // Assuming John owns 2 deals

    // Clear filters
    await page.click('button:has-text("Clear Filters")');
    await expect(page.locator('.deal-card')).toHaveCount(10); // All deals visible
  });

  test('Mobile responsive design', async () => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/crm');

    // Verify mobile menu
    await expect(page.locator('.mobile-menu-button')).toBeVisible();
    
    // Open mobile menu
    await page.click('.mobile-menu-button');
    await expect(page.locator('.mobile-nav')).toBeVisible();

    // Navigate via mobile menu
    await page.click('.mobile-nav >> text=Deals');
    await expect(page.locator('h2')).toContainText('Deals');

    // Verify cards stack vertically
    const dealCards = await page.locator('.deal-card').all();
    expect(dealCards.length).toBeGreaterThan(0);
    
    // Check first two cards are stacked
    const firstCardBox = await dealCards[0].boundingBox();
    const secondCardBox = await dealCards[1].boundingBox();
    
    expect(firstCardBox?.y).toBeLessThan(secondCardBox?.y || 0);
    expect(firstCardBox?.x).toBe(secondCardBox?.x);
  });

  test('Real-time updates', async () => {
    // Open two browser contexts
    const context2 = await page.context().browser()?.newContext();
    const page2 = await context2!.newPage();
    await setupAuth(page2);

    // Both navigate to same deal
    const dealUrl = '/crm/deals/deal-1/edit';
    await page.goto(dealUrl);
    await page2.goto(dealUrl);

    // Update deal in first page
    await page.fill('input[name="amount"]', '75000');
    await page.press('input[name="amount"]', 'Tab'); // Trigger save

    // Verify update appears in second page
    await expect(page2.locator('input[name="amount"]')).toHaveValue('75000', { timeout: 5000 });

    await page2.close();
    await context2?.close();
  });

  test('Data export functionality', async () => {
    await page.goto('/crm/deals');

    // Click export button
    await page.click('button:has-text("Export")');
    
    // Select export options
    await page.click('input[value="csv"]');
    await page.click('input[name="includeContacts"]');
    await page.click('input[name="includeEstimates"]');

    // Start download
    const downloadPromise = page.waitForEvent('download');
    await page.click('button:has-text("Download")');
    const download = await downloadPromise;

    // Verify download
    expect(download.suggestedFilename()).toContain('deals-export');
    expect(download.suggestedFilename()).toContain('.csv');
  });

  test('Estimate linking workflow', async () => {
    // Create a deal first
    await page.goto('/crm/deals');
    const dealCard = page.locator('.deal-card').first();
    await dealCard.click();

    // Navigate to estimates section
    await page.click('text=Estimates');

    // Link existing estimate
    await page.click('button:has-text("Link Existing Estimate")');
    
    // Search for estimate
    await page.fill('input[placeholder="Search estimates..."]', 'Q4 Development');
    
    // Select estimate
    await page.click('.estimate-option:has-text("Q4 Development Project")');
    await page.click('button:has-text("Link Selected")');

    // Verify estimate linked
    await expect(page.locator('.linked-estimate')).toContainText('Q4 Development Project');
    await expect(page.locator('.linked-estimate')).toContainText('$45,000');

    // Create new estimate from deal
    await page.click('button:has-text("Create New Estimate")');
    
    // Verify deal info pre-populated
    await expect(page.locator('input[name="dealId"]')).toHaveValue(/.+/);
    await expect(page.locator('input[name="companyName"]')).not.toBeEmpty();
  });

  test('Permission-based UI elements', async () => {
    // Login as viewer (limited permissions)
    await page.evaluate(() => {
      localStorage.setItem('userRole', 'viewer');
    });
    await page.reload();

    await page.goto('/crm/deals');

    // Verify read-only UI
    await expect(page.locator('button:has-text("New Deal")')).not.toBeVisible();
    await expect(page.locator('button:has-text("Export")')).toBeVisible();

    // Open deal detail
    await page.click('.deal-card').first();

    // Verify edit buttons are hidden
    await expect(page.locator('button:has-text("Edit")')).not.toBeVisible();
    await expect(page.locator('button:has-text("Delete")')).not.toBeVisible();

    // Reset to admin role
    await page.evaluate(() => {
      localStorage.setItem('userRole', 'admin');
    });
  });
});