import React, { useEffect, useState } from "react"; // Remove MouseEvent import
import {
  useProjection,
  ChartDataPoint,
  ScenarioChartDataPoint,
  formatCurrency,
  calculatePercentageChange,
} from ".";
import { Transaction } from "../../../types/financial";
import "../../styles/components/chart-tooltip.css"; // Import the CSS

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

/**
 * Adaptive custom tooltip component for the chart.
 * Uses container queries defined in chart-tooltip.css to adapt layout.
 */
const ChartTooltip = ({ active, payload, label }: CustomTooltipProps) => {
  const {
    projectionData,
    scenarioProjectionData,
    showScenarios,
    minBalanceThreshold,
    setHoveredDate,
  } = useProjection();
  const [tooltipVisible, setTooltipVisible] = useState(false);

  useEffect(() => {
    if (active && payload && payload.length) {
      const currentData = payload[0].payload as ChartDataPoint;
      setHoveredDate(currentData.date);
      setTooltipVisible(true);
    } else {
      // Logic to hide tooltip might need adjustment based on interaction model
      // For now, let's hide it when not active (can be refined)
      // setTooltipVisible(false); // Avoid immediate hiding on mouse out for better UX
    }
    // Cleanup handled by handleCloseTooltip or component unmount
  }, [active, payload, label, setHoveredDate]);

  // Click handler to close tooltip (useful for touch interactions)
  const handleCloseTooltip = (e) => {
    // Remove explicit type annotation
    e.stopPropagation();
    e.preventDefault();
    setTooltipVisible(false);
    setHoveredDate(null); // Ensure date is unhovered when closed
  };

  // Render tooltip only if explicitly set to visible and has payload
  if (!tooltipVisible || !payload || !payload.length) {
    return null;
  }

  // Find the data for this point
  const currentData = payload[0].payload as
    | ChartDataPoint
    | ScenarioChartDataPoint;

  // Check if we have scenario data
  const hasScenarioData =
    showScenarios &&
    "expectedCaseBalance" in currentData &&
    "bestCaseBalance" in currentData;

  // Calculate relative percent change from previous day
  const currentBalance = currentData.balance || 0;
  const dailyCashflowArray = projectionData?.dailyCashflow || [];
  const currentIndex = dailyCashflowArray.findIndex(
    (day) => new Date(day.date).toISOString().split("T")[0] === currentData.date
  );
  const previousBalance =
    currentIndex > 0
      ? dailyCashflowArray[currentIndex - 1].balance
      : projectionData?.startingBalance || 0;
  const percentChange = calculatePercentageChange(
    currentBalance,
    previousBalance
  ).toFixed(1);

  // Determine if balance is in danger zone
  const isDangerZone = currentBalance < minBalanceThreshold;

  // Find transactions for this date
  const dailyCashflow = projectionData?.dailyCashflow?.find(
    (day) => day.date.toString().split("T")[0] === currentData.date
  );
  const transactions = dailyCashflow?.transactions || [];

  return (
    <div
      className="chart-tooltip"
      // Removed inline width style, now controlled by wrapperStyle in CashflowChart.tsx
      style={{
        maxWidth: "100%",
        // Keep container type for potential future use or consistency
        containerType: "inline-size",
        // Ensure proper display
        display: "block",
        // Prevent scrolling
        overflow: "visible",
        // Remove height restriction
        maxHeight: "none",
      }}
    >
      <div className="chart-tooltip__header">
        <p className="chart-tooltip__date">
          {new Date(label || "").toLocaleDateString(undefined, {
            day: "numeric",
            month: "short",
            year: "numeric",
          })}
        </p>
        <button
          onClick={handleCloseTooltip}
          className="chart-tooltip__close"
          aria-label="Close tooltip"
        >
          <svg className="chart-tooltip__close-icon" viewBox="0 0 24 24">
            <path d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="chart-tooltip__content">
        <div className="chart-tooltip__summary">
          <div className="chart-tooltip__summary-grid">
            {/* First row */}
            <div className="chart-tooltip__summary-cell">
              <div className="chart-tooltip__label">Balance</div>
              <div
                className={`chart-tooltip__value ${
                  isDangerZone ? "text-red-600" : "text-blue-600"
                }`}
              >
                {formatCurrency(currentBalance)}
              </div>
            </div>

            <div className="chart-tooltip__summary-cell">
              <div className="chart-tooltip__label">Change</div>
              <div
                className={`chart-tooltip__value ${
                  parseFloat(percentChange) < 0
                    ? "text-red-600"
                    : parseFloat(percentChange) > 0
                    ? "text-green-600"
                    : "text-gray-600"
                }`}
              >
                {parseFloat(percentChange) > 0 ? "+" : ""}
                {percentChange}%
              </div>
            </div>

            {/* Second row */}
            <div className="chart-tooltip__summary-cell">
              <div className="chart-tooltip__label">Money In</div>
              <div className="chart-tooltip__value text-green-600">
                {formatCurrency(currentData.inflows || 0)}
              </div>
            </div>

            <div className="chart-tooltip__summary-cell">
              <div className="chart-tooltip__label">Money Out</div>
              <div className="chart-tooltip__value text-red-600">
                {formatCurrency(Math.abs(currentData.outflows || 0))}
              </div>
            </div>
          </div>
        </div>

        {isDangerZone && (
          <div className="chart-tooltip__warning">
            ⚠️ Below minimum threshold
          </div>
        )}

        {/* Add scenario information if available - more compact layout */}
        {hasScenarioData && (
          <div className="mt-1 border-t pt-1">
            <h4 className="text-xs font-medium mb-0.5">Deal Impact:</h4>
            <div className="flex justify-between text-xs">
              <span>Best case:</span>
              <span className="font-medium text-blue-500">
                {formatCurrency(
                  (currentData as ScenarioChartDataPoint).bestCaseBalance
                )}
              </span>
            </div>
            <div className="flex justify-between text-xs">
              <span>Expected case:</span>
              <span className="font-medium text-blue-500">
                {formatCurrency(
                  (currentData as ScenarioChartDataPoint).expectedCaseBalance
                )}
              </span>
            </div>
            <div className="flex justify-between text-xs">
              <span>Potential impact:</span>
              <span className="font-medium text-green-500">
                {formatCurrency(
                  (currentData as ScenarioChartDataPoint).dealImpact
                )}
              </span>
            </div>
          </div>
        )}

        {/* Transactions list */}
        {transactions.length > 0 && (
          <div className="chart-tooltip__transactions">
            <p className="chart-tooltip__transactions-title">
              Transactions ({transactions.length}):
            </p>
            <div className="chart-tooltip__transactions-list">
              {transactions.map((transaction: Transaction, idx: number) => (
                <div key={idx} className="chart-tooltip__transaction-item">
                  <div className="chart-tooltip__transaction-details">
                    <span
                      className="chart-tooltip__transaction-desc"
                      title={
                        transaction.source === "harvest" &&
                        transaction.type === "invoice" &&
                        transaction.metadata?.projectName &&
                        transaction.metadata?.clientName
                          ? `${transaction.metadata.projectName} - ${transaction.metadata.clientName}`
                          : transaction.description
                      }
                    >
                      <div className="flex flex-col">
                        <span>
                          {transaction.source === "harvest" &&
                          transaction.type === "invoice" &&
                          transaction.metadata?.projectName
                            ? // For Harvest invoices, use project name as main description
                              transaction.metadata.projectName.length > 30
                              ? transaction.metadata.projectName.substring(
                                  0,
                                  30
                                ) + "..."
                              : transaction.metadata.projectName
                            : // For other transactions, use description as main description
                            transaction.description?.length > 30
                            ? transaction.description.substring(0, 30) + "..."
                            : transaction.description}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {transaction.source === "harvest" &&
                          transaction.type === "invoice" &&
                          transaction.metadata?.clientName
                            ? // For Harvest invoices, use client name as secondary description
                              transaction.metadata.clientName.length > 30
                              ? transaction.metadata.clientName.substring(
                                  0,
                                  30
                                ) + "..."
                              : transaction.metadata.clientName
                            : // For other transactions, use source as secondary description
                              transaction.source || "Unknown"}
                        </span>
                      </div>
                    </span>
                    <span
                      className={`chart-tooltip__transaction-amount ${
                        transaction.amount < 0
                          ? "text-red-600"
                          : "text-green-600"
                      }`}
                    >
                      {formatCurrency(transaction.amount)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChartTooltip;
