-- Migration 003: Fix billing rate storage to support true hourly/daily billing
-- 
-- Context: The current implementation always stores rates as daily rates, which causes
-- incorrect billing when hours/day changes for hourly contracts. This migration adds
-- proper tracking of how rates were originally entered.
--
-- Example of the bug:
-- - User enters $200/hour at 8h/day 
-- - System stores $1600/day
-- - Admin changes to 6h/day
-- - System still bills $1600/day instead of $1200/day (33% overcharge!)

-- Add columns to track original rate entry
ALTER TABLE estimate_allocation ADD COLUMN rate_type TEXT DEFAULT 'daily' 
  CHECK(rate_type IN ('daily', 'hourly'));

ALTER TABLE estimate_allocation ADD COLUMN rate_as_entered REAL;

-- For existing data, we'll assume all current rates are daily rates
-- since that's how the system currently stores them
UPDATE estimate_allocation 
SET rate_type = 'daily',
    rate_as_entered = proposed_rate_daily
WHERE rate_as_entered IS NULL;

-- Make rate_as_entered required for new records
-- (Can't do this in the ALTER TABLE above because existing records don't have it yet)
-- Note: SQLite doesn't support adding NOT NULL to existing columns, so we'll handle this in application code

-- Add an index for querying by rate type
CREATE INDEX IF NOT EXISTS idx_estimate_allocation_rate_type ON estimate_allocation(rate_type);

-- Add a comment to clarify the columns (SQLite doesn't support column comments, so this is just documentation)
-- rate_proposed_daily: Always contains the daily rate equivalent (for backward compatibility)
-- rate_as_entered: The actual rate value as entered by the user
-- rate_type: Whether the rate was entered as 'daily' or 'hourly'