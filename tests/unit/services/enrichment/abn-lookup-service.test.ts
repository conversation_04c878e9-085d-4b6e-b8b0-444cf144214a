/**
 * ABN Lookup Service Unit Tests
 * 
 * Comprehensive tests for the ABN Lookup Service including:
 * - Configuration validation
 * - Name search functionality
 * - ABN detail retrieval
 * - Error handling
 * - XML parsing
 * - Rate limiting
 */

import axios from 'axios';
import { ABNLookupService } from '../../../../src/api/services/enrichment/abn-lookup-service';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ABNLookupService', () => {
  let service: ABNLookupService;
  
  // Mock console methods to avoid noise in tests
  beforeAll(() => {
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset environment variables
    delete process.env.ABN_LOOKUP_GUID;
  });

  describe('Configuration', () => {
    it('should initialize with provided config', () => {
      const service = new ABNLookupService({
        guid: 'test-guid-123',
        maxResults: 5
      });

      expect(service.isConfigured()).toBe(true);
    });

    it('should initialize with environment variable', () => {
      process.env.ABN_LOOKUP_GUID = 'env-guid-456';
      
      const service = new ABNLookupService();
      
      expect(service.isConfigured()).toBe(true);
    });

    it('should warn when no GUID is configured', () => {
      const service = new ABNLookupService();
      
      expect(service.isConfigured()).toBe(false);
      expect(console.warn).toHaveBeenCalledWith(
        'ABN Lookup GUID not configured. ABN enrichment will be disabled.'
      );
    });

    it('should use default maxResults when not provided', () => {
      const service = new ABNLookupService({ guid: 'test-guid' });
      
      // Should use default of 10 - we can verify this through the API call
      expect(service.isConfigured()).toBe(true);
    });
  });

  describe('searchByName', () => {
    beforeEach(() => {
      service = new ABNLookupService({ guid: 'test-guid-123' });
    });

    it('should throw error when not configured', async () => {
      const unconfiguredService = new ABNLookupService();
      
      await expect(unconfiguredService.searchByName('Test Company'))
        .rejects.toThrow('ABN Lookup service is not configured');
    });

    it('should successfully search by company name', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>*********01</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Test Company Pty Ltd</organisationName>
                </mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Test Company');

      expect(results).toHaveLength(1);
      expect(results[0]).toEqual({
        abn: '**************',
        name: 'Test Company Pty Ltd',
        status: 'Active',
        score: 95.5
      });

      // Verify API call
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('/ABRXMLSearchByName'),
        expect.objectContaining({
          timeout: 10000,
          headers: { 'Accept': 'application/xml' }
        })
      );
    });

    it('should handle multiple search results', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>*********01</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Test Company One</organisationName>
                </mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>***********</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Test Company Two</organisationName>
                </mainName>
                <relevancyScore>87.3</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Test Company');

      expect(results).toHaveLength(2);
      expect(results[0].score).toBe(95.5);
      expect(results[1].score).toBe(87.3);
    });

    it('should filter out inactive ABNs', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>*********01</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Active Company</organisationName>
                </mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>***********</identifierValue>
                  <identifierStatus>Cancelled</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Cancelled Company</organisationName>
                </mainName>
                <relevancyScore>87.3</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Test Company');

      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('Active Company');
      expect(results[0].status).toBe('Active');
    });

    it('should include postcode in search when provided', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>*********01</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Local Company</organisationName>
                </mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      await service.searchByName('Test Company', '3000');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('postcode=3000'),
        expect.any(Object)
      );
    });

    it('should clean company name before search', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>*********01</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Clean Company</organisationName>
                </mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      await service.searchByName('Test Company Pty Ltd (Trading)');

      // Should clean the name and include it in search
      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('searchString=Test%20Company'),
        expect.any(Object)
      );
    });

    it('should return empty array when no results found', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList />
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Nonexistent Company');

      expect(results).toEqual([]);
    });

    it('should return empty array when API returns exception', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <exception>
              <exceptionDescription>Invalid search criteria</exceptionDescription>
            </exception>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Invalid Search');

      expect(results).toEqual([]);
      expect(console.error).toHaveBeenCalledWith(
        'ABN search exception:', 
        expect.objectContaining({ exceptionDescription: 'Invalid search criteria' })
      );
    });

    it('should handle network errors', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network timeout'));

      await expect(service.searchByName('Test Company'))
        .rejects.toThrow('Failed to search ABN: Network timeout');

      expect(console.error).toHaveBeenCalledWith('Error searching ABN:', expect.any(Error));
    });

    it('should handle malformed XML response', async () => {
      mockedAxios.get.mockResolvedValueOnce({ data: 'Invalid XML' });

      await expect(service.searchByName('Test Company'))
        .rejects.toThrow('Failed to search ABN:');
    });

    it('should apply timeout to requests', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList />
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      await service.searchByName('Test Company');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          timeout: 10000
        })
      );
    });
  });

  describe('getByABN', () => {
    beforeEach(() => {
      service = new ABNLookupService({ guid: 'test-guid-123' });
    });

    it('should throw error when not configured', async () => {
      const unconfiguredService = new ABNLookupService();
      
      await expect(unconfiguredService.getByABN('*********01'))
        .rejects.toThrow('ABN Lookup service is not configured');
    });

    it('should successfully get ABN details', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <businessEntity>
              <ABN>
                <identifierValue>*********01</identifierValue>
                <identifierStatus>Active</identifierStatus>
              </ABN>
              <entityType>
                <entityTypeCode>PRV</entityTypeCode>
                <entityDescription>Australian Private Company</entityDescription>
              </entityType>
              <mainName>
                <organisationName>Test Company Pty Ltd</organisationName>
              </mainName>
              <goodsAndServicesTax>
                <effectiveFrom>2020-01-01</effectiveFrom>
              </goodsAndServicesTax>
              <mainBusinessPhysicalAddress>
                <stateCode>VIC</stateCode>
                <postcode>3000</postcode>
              </mainBusinessPhysicalAddress>
              <businessName>
                <organisationName>Test Trading Name</organisationName>
              </businessName>
              <ASICNumber>*********</ASICNumber>
            </businessEntity>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const result = await service.getByABN('*********01');

      expect(result).toEqual({
        abn: '**************',
        abnStatus: 'Active',
        entityTypeCode: 'PRV',
        entityTypeName: 'Australian Private Company',
        gstStatus: 'Registered',
        entityName: 'Test Company Pty Ltd',
        businessLocation: {
          stateCode: 'VIC',
          postcode: '3000'
        },
        tradingNames: ['Test Trading Name'],
        asicNumber: '*********'
      });
    });

    it('should handle missing business entity', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response />
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const result = await service.getByABN('*********01');

      expect(result).toBeNull();
    });

    it('should handle GST not registered status', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <businessEntity>
              <ABN>
                <identifierValue>*********01</identifierValue>
                <identifierStatus>Active</identifierStatus>
              </ABN>
              <entityType>
                <entityTypeCode>PRV</entityTypeCode>
                <entityDescription>Australian Private Company</entityDescription>
              </entityType>
              <mainName>
                <organisationName>Test Company Pty Ltd</organisationName>
              </mainName>
            </businessEntity>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const result = await service.getByABN('*********01');

      expect(result?.gstStatus).toBe('Not Registered');
    });

    it('should handle multiple trading names', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <businessEntity>
              <ABN>
                <identifierValue>*********01</identifierValue>
                <identifierStatus>Active</identifierStatus>
              </ABN>
              <entityType>
                <entityTypeCode>PRV</entityTypeCode>
                <entityDescription>Australian Private Company</entityDescription>
              </entityType>
              <mainName>
                <organisationName>Test Company Pty Ltd</organisationName>
              </mainName>
              <businessName>
                <organisationName>Trading Name One</organisationName>
              </businessName>
              <businessName>
                <organisationName>Trading Name Two</organisationName>
              </businessName>
            </businessEntity>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const result = await service.getByABN('*********01');

      expect(result?.tradingNames).toEqual(['Trading Name One', 'Trading Name Two']);
    });

    it('should clean ABN before making request', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <businessEntity>
              <ABN>
                <identifierValue>*********01</identifierValue>
                <identifierStatus>Active</identifierStatus>
              </ABN>
              <entityType>
                <entityTypeCode>PRV</entityTypeCode>
                <entityDescription>Australian Private Company</entityDescription>
              </entityType>
              <mainName>
                <organisationName>Test Company</organisationName>
              </mainName>
            </businessEntity>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      await service.getByABN('**************'); // ABN with spaces

      expect(mockedAxios.get).toHaveBeenCalledWith(
        expect.stringContaining('searchString=*********01'),
        expect.any(Object)
      );
    });

    it('should handle network errors gracefully', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Network error'));

      const result = await service.getByABN('*********01');

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith('Error getting ABN details:', expect.any(Error));
    });
  });

  describe('Name extraction methods', () => {
    beforeEach(() => {
      service = new ABNLookupService({ guid: 'test-guid-123' });
    });

    it('should extract business name from various record formats', async () => {
      // Test different name formats through the search method
      const testCases = [
        {
          xmlData: `
            <searchResultsRecord>
              <ABN><identifierValue>*********01</identifierValue><identifierStatus>Active</identifierStatus></ABN>
              <mainName><organisationName>Main Company Name</organisationName></mainName>
              <relevancyScore>95.5</relevancyScore>
            </searchResultsRecord>
          `,
          expectedName: 'Main Company Name'
        },
        {
          xmlData: `
            <searchResultsRecord>
              <ABN><identifierValue>*********01</identifierValue><identifierStatus>Active</identifierStatus></ABN>
              <businessName><organisationName>Business Name</organisationName></businessName>
              <relevancyScore>95.5</relevancyScore>
            </searchResultsRecord>
          `,
          expectedName: 'Business Name'
        },
        {
          xmlData: `
            <searchResultsRecord>
              <ABN><identifierValue>*********01</identifierValue><identifierStatus>Active</identifierStatus></ABN>
              <legalName><givenName>John</givenName><familyName>Smith</familyName></legalName>
              <relevancyScore>95.5</relevancyScore>
            </searchResultsRecord>
          `,
          expectedName: 'John Smith'
        }
      ];

      for (const testCase of testCases) {
        const mockXmlResponse = `
          <?xml version="1.0" encoding="utf-8"?>
          <ABRPayloadSearchResults>
            <response>
              <searchResultsList>
                ${testCase.xmlData}
              </searchResultsList>
            </response>
          </ABRPayloadSearchResults>
        `;

        mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

        const results = await service.searchByName('Test');
        expect(results[0].name).toBe(testCase.expectedName);
      }
    });
  });

  describe('ABN formatting', () => {
    beforeEach(() => {
      service = new ABNLookupService({ guid: 'test-guid-123' });
    });

    it('should format valid 11-digit ABN correctly', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN><identifierValue>*********01</identifierValue><identifierStatus>Active</identifierStatus></ABN>
                <mainName><organisationName>Test Company</organisationName></mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Test Company');

      expect(results[0].abn).toBe('**************');
    });

    it('should handle invalid ABN length', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN><identifierValue>*********</identifierValue><identifierStatus>Active</identifierStatus></ABN>
                <mainName><organisationName>Test Company</organisationName></mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });

      const results = await service.searchByName('Test Company');

      expect(results[0].abn).toBe('*********'); // Should return unformatted
    });
  });

  describe('Company name cleaning', () => {
    beforeEach(() => {
      service = new ABNLookupService({ guid: 'test-guid-123' });
    });

    it('should clean various company suffixes', async () => {
      const mockXmlResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList />
          </response>
        </ABRPayloadSearchResults>
      `;

      const testCases = [
        'Test Company Pty Ltd',
        'Test Company PTY LTD',
        'Test Company Limited',
        'Test Company Proprietary',
        'Test Company Incorporated',
        'Test Company (Trading)',
        'Test Company - Division'
      ];

      for (const companyName of testCases) {
        mockedAxios.get.mockResolvedValueOnce({ data: mockXmlResponse });
        await service.searchByName(companyName);
        
        // Should clean to just "Test Company"
        expect(mockedAxios.get).toHaveBeenCalledWith(
          expect.stringContaining('searchString=Test%20Company'),
          expect.any(Object)
        );
      }
    });
  });
});