import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import ForwardProjectionPage from '../../../src/frontend/components/ForwardProjectionPage';
import * as cashflowApi from '../../../src/frontend/api/cashflow';
import * as xeroApi from '../../../src/frontend/api/xero';
import { ProjectionProvider } from '../../../src/frontend/components/ForwardProjection/ProjectionContext';

// Mock API modules
jest.mock('../../../src/frontend/api/cashflow');
jest.mock('../../../src/frontend/api/xero');

// Mock child components
jest.mock('../../../src/frontend/components/ForwardProjection/CashflowChart', () => ({
  __esModule: true,
  default: ({ data }: any) => (
    <div data-testid="cashflow-chart">
      Chart with {data?.length || 0} data points
    </div>
  )
}));

jest.mock('../../../src/frontend/components/ForwardProjection/TransactionsList', () => ({
  __esModule: true,
  default: ({ transactions }: any) => (
    <div data-testid="transactions-list">
      {transactions?.length || 0} transactions
    </div>
  )
}));

jest.mock('../../../src/frontend/components/ForwardProjection/CashflowSummaryCards', () => ({
  __esModule: true,
  default: ({ summary }: any) => (
    <div data-testid="summary-cards">
      Current Balance: ${summary?.currentBalance || 0}
    </div>
  )
}));

jest.mock('../../../src/frontend/components/ForwardProjection/ProjectionSettingsPanel', () => ({
  __esModule: true,
  default: ({ onSettingsChange }: any) => (
    <div data-testid="settings-panel">
      <button onClick={() => onSettingsChange({ showWorstCase: true })}>
        Update Settings
      </button>
    </div>
  )
}));

describe('ForwardProjectionPage', () => {
  let queryClient: QueryClient;

  const mockProjectionData = {
    projections: {
      daily: [
        {
          date: '2024-01-01',
          balance: 10000,
          income: 5000,
          expenses: 3000,
          transactions: []
        },
        {
          date: '2024-01-02',
          balance: 12000,
          income: 2000,
          expenses: 0,
          transactions: []
        }
      ],
      summary: {
        currentBalance: 10000,
        projectedBalance30Days: 15000,
        projectedBalance90Days: 25000,
        totalIncome: 50000,
        totalExpenses: 20000
      }
    },
    transactions: [
      {
        id: '1',
        type: 'income',
        amount: 5000,
        description: 'Client Payment',
        date: '2024-01-01',
        category: 'invoice'
      },
      {
        id: '2',
        type: 'expense',
        amount: 3000,
        description: 'Office Rent',
        date: '2024-01-01',
        category: 'rent'
      }
    ]
  };

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <ProjectionProvider>
            <ForwardProjectionPage />
          </ProjectionProvider>
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    // Default API mocks
    (cashflowApi.getProjections as jest.Mock).mockResolvedValue(mockProjectionData);
    (xeroApi.getConnections as jest.Mock).mockResolvedValue({
      xero: { connected: true }
    });
  });

  describe('rendering', () => {
    it('should render all main components', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('summary-cards')).toBeInTheDocument();
        expect(screen.getByTestId('cashflow-chart')).toBeInTheDocument();
        expect(screen.getByTestId('transactions-list')).toBeInTheDocument();
      });
    });

    it('should render the page header', () => {
      renderComponent();

      expect(screen.getByText(/Cash Flow Projection/i)).toBeInTheDocument();
    });

    it('should show correct data in components', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByText('Current Balance: $10000')).toBeInTheDocument();
        expect(screen.getByText('Chart with 2 data points')).toBeInTheDocument();
        expect(screen.getByText('2 transactions')).toBeInTheDocument();
      });
    });
  });

  describe('data fetching', () => {
    it('should fetch projections on mount', async () => {
      renderComponent();

      await waitFor(() => {
        expect(cashflowApi.getProjections).toHaveBeenCalled();
      });
    });

    it('should handle loading state', () => {
      let resolveProjections: any;
      (cashflowApi.getProjections as jest.Mock).mockImplementation(() => 
        new Promise(resolve => { resolveProjections = resolve; })
      );

      renderComponent();

      // Should show loading state
      expect(screen.getByText(/Loading/i)).toBeInTheDocument();

      // Resolve the promise
      resolveProjections(mockProjectionData);
    });

    it('should handle error state', async () => {
      (cashflowApi.getProjections as jest.Mock).mockRejectedValue(
        new Error('Failed to fetch projections')
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/Error loading projections/i)).toBeInTheDocument();
      });
    });
  });

  describe('settings and filtering', () => {
    it('should update when settings change', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('settings-panel')).toBeInTheDocument();
      });

      // Click update settings button
      fireEvent.click(screen.getByText('Update Settings'));

      // Should refetch with new settings
      await waitFor(() => {
        expect(cashflowApi.getProjections).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('date range selection', () => {
    it('should allow date range changes', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/30 Days/i)).toBeInTheDocument();
      });

      // Click on 90 days option
      const ninetyDaysButton = screen.getByText(/90 Days/i);
      fireEvent.click(ninetyDaysButton);

      // Should refetch with new date range
      await waitFor(() => {
        expect(cashflowApi.getProjections).toHaveBeenCalledWith(
          expect.objectContaining({
            days: 90
          })
        );
      });
    });
  });

  describe('scenario toggling', () => {
    it('should toggle between scenarios', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/Expected/i)).toBeInTheDocument();
      });

      // Toggle to worst case
      const worstCaseButton = screen.getByText(/Worst Case/i);
      fireEvent.click(worstCaseButton);

      // Should update UI accordingly
      await waitFor(() => {
        expect(worstCaseButton).toHaveClass('active');
      });
    });
  });

  describe('transaction filtering', () => {
    it('should filter transactions by type', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('transactions-list')).toBeInTheDocument();
      });

      // Filter by income
      const incomeFilter = screen.getByText(/Income/i);
      fireEvent.click(incomeFilter);

      // Should update transaction list
      await waitFor(() => {
        expect(screen.getByText('1 transactions')).toBeInTheDocument();
      });
    });
  });

  describe('empty state', () => {
    it('should show empty state when no data', async () => {
      (cashflowApi.getProjections as jest.Mock).mockResolvedValue({
        projections: { daily: [], summary: {} },
        transactions: []
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/No projection data available/i)).toBeInTheDocument();
      });
    });
  });

  describe('refresh functionality', () => {
    it('should allow manual refresh', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Refresh/i })).toBeInTheDocument();
      });

      fireEvent.click(screen.getByRole('button', { name: /Refresh/i }));

      await waitFor(() => {
        expect(cashflowApi.getProjections).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('export functionality', () => {
    it('should provide export options', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Export/i })).toBeInTheDocument();
      });

      fireEvent.click(screen.getByRole('button', { name: /Export/i }));

      // Should show export menu
      await waitFor(() => {
        expect(screen.getByText(/Export as CSV/i)).toBeInTheDocument();
        expect(screen.getByText(/Export as PDF/i)).toBeInTheDocument();
      });
    });
  });

  describe('integration warnings', () => {
    it('should show warning when Xero not connected', async () => {
      (xeroApi.getConnections as jest.Mock).mockResolvedValue({
        xero: { connected: false }
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/Connect Xero for more accurate projections/i)).toBeInTheDocument();
      });
    });
  });

  describe('responsive behavior', () => {
    it('should adjust layout for mobile', () => {
      // Mock window.matchMedia for mobile
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(max-width: 768px)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        }))
      });

      renderComponent();

      // Should have mobile-specific classes
      expect(screen.getByTestId('cashflow-chart').parentElement).toHaveClass('mobile-layout');
    });
  });
});