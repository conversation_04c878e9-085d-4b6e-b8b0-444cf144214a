import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { jest } from '@jest/globals';
import CashflowSummaryCards from '../../../../src/frontend/components/ForwardProjection/CashflowSummaryCards';

describe('CashflowSummaryCards Component', () => {
  const mockCashflowData = {
    today: '2024-01-15',
    projectionDays: 90,
    startingBalance: 50000,
    scenarios: {
      expected: {
        endingBalance: 75000,
        lowestBalance: 35000,
        lowestBalanceDate: '2024-02-10',
        totalIncome: 150000,
        totalExpenses: 125000,
        netCashflow: 25000,
      },
      bestCase: {
        endingBalance: 95000,
        lowestBalance: 45000,
        lowestBalanceDate: '2024-02-05',
        totalIncome: 170000,
        totalExpenses: 125000,
        netCashflow: 45000,
      },
      worstCase: {
        endingBalance: 55000,
        lowestBalance: 25000,
        lowestBalanceDate: '2024-02-15',
        totalIncome: 130000,
        totalExpenses: 125000,
        netCashflow: 5000,
      },
    },
    upcomingTransactions: {
      nextWeek: {
        income: 15000,
        expenses: 10000,
        net: 5000,
      },
      nextMonth: {
        income: 45000,
        expenses: 35000,
        net: 10000,
      },
    },
    alerts: [
      {
        type: 'warning',
        message: 'Low balance expected on Feb 15',
        date: '2024-02-15',
        severity: 'medium',
      },
      {
        type: 'info',
        message: 'Large payment due on Mar 1',
        date: '2024-03-01',
        severity: 'low',
      },
    ],
  };

  const mockEmptyData = {
    today: '2024-01-15',
    projectionDays: 90,
    startingBalance: 0,
    scenarios: {
      expected: {
        endingBalance: 0,
        lowestBalance: 0,
        lowestBalanceDate: '',
        totalIncome: 0,
        totalExpenses: 0,
        netCashflow: 0,
      },
      bestCase: {
        endingBalance: 0,
        lowestBalance: 0,
        lowestBalanceDate: '',
        totalIncome: 0,
        totalExpenses: 0,
        netCashflow: 0,
      },
      worstCase: {
        endingBalance: 0,
        lowestBalance: 0,
        lowestBalanceDate: '',
        totalIncome: 0,
        totalExpenses: 0,
        netCashflow: 0,
      },
    },
    upcomingTransactions: {
      nextWeek: { income: 0, expenses: 0, net: 0 },
      nextMonth: { income: 0, expenses: 0, net: 0 },
    },
    alerts: [],
  };

  const defaultProps = {
    data: mockCashflowData,
    selectedScenario: 'expected' as const,
    onScenarioChange: jest.fn(),
  };

  const renderComponent = (props = {}) => {
    return render(<CashflowSummaryCards {...defaultProps} {...props} />);
  };

  describe('Summary Cards Display', () => {
    it('should display current balance card', () => {
      renderComponent();

      expect(screen.getByText(/current balance/i)).toBeInTheDocument();
      expect(screen.getByText('$50,000')).toBeInTheDocument();
    });

    it('should display projected balance card', () => {
      renderComponent();

      expect(screen.getByText(/projected balance/i)).toBeInTheDocument();
      expect(screen.getByText(/90 days/i)).toBeInTheDocument();
      expect(screen.getByText('$75,000')).toBeInTheDocument();
    });

    it('should display lowest balance card with warning', () => {
      renderComponent();

      const lowestBalanceCard = screen.getByText(/lowest balance/i).closest('.summary-card');
      expect(lowestBalanceCard).toBeInTheDocument();
      expect(screen.getByText('$35,000')).toBeInTheDocument();
      expect(screen.getByText(/feb 10/i)).toBeInTheDocument();
      expect(lowestBalanceCard).toHaveClass('warning');
    });

    it('should display net cashflow card', () => {
      renderComponent();

      expect(screen.getByText(/net cash flow/i)).toBeInTheDocument();
      expect(screen.getByText('+$25,000')).toBeInTheDocument();
      expect(screen.getByText('+50%')).toBeInTheDocument(); // Percentage change
    });
  });

  describe('Scenario Switching', () => {
    it('should display scenario selector', () => {
      renderComponent();

      expect(screen.getByLabelText(/scenario/i)).toBeInTheDocument();
      expect(screen.getByText(/expected/i)).toBeInTheDocument();
    });

    it('should update values when scenario changes', () => {
      renderComponent();

      const scenarioSelect = screen.getByLabelText(/scenario/i);
      fireEvent.change(scenarioSelect, { target: { value: 'bestCase' } });

      expect(defaultProps.onScenarioChange).toHaveBeenCalledWith('bestCase');
    });

    it('should display best case values', () => {
      renderComponent({ selectedScenario: 'bestCase' });

      expect(screen.getByText('$95,000')).toBeInTheDocument(); // Projected balance
      expect(screen.getByText('$45,000')).toBeInTheDocument(); // Lowest balance
      expect(screen.getByText('+$45,000')).toBeInTheDocument(); // Net cashflow
    });

    it('should display worst case values with alerts', () => {
      renderComponent({ selectedScenario: 'worstCase' });

      expect(screen.getByText('$55,000')).toBeInTheDocument(); // Projected balance
      expect(screen.getByText('$25,000')).toBeInTheDocument(); // Lowest balance
      
      const lowestBalanceCard = screen.getByText(/lowest balance/i).closest('.summary-card');
      expect(lowestBalanceCard).toHaveClass('danger'); // Lower threshold
    });
  });

  describe('Upcoming Transactions', () => {
    it('should display next week summary', () => {
      renderComponent();

      const nextWeekSection = screen.getByText(/next 7 days/i).closest('.upcoming-section');
      expect(nextWeekSection).toBeInTheDocument();
      expect(nextWeekSection).toHaveTextContent('+$15,000'); // Income
      expect(nextWeekSection).toHaveTextContent('-$10,000'); // Expenses
      expect(nextWeekSection).toHaveTextContent('+$5,000'); // Net
    });

    it('should display next month summary', () => {
      renderComponent();

      const nextMonthSection = screen.getByText(/next 30 days/i).closest('.upcoming-section');
      expect(nextMonthSection).toBeInTheDocument();
      expect(nextMonthSection).toHaveTextContent('+$45,000'); // Income
      expect(nextMonthSection).toHaveTextContent('-$35,000'); // Expenses
      expect(nextMonthSection).toHaveTextContent('+$10,000'); // Net
    });

    it('should show expandable transaction details', () => {
      renderComponent();

      const expandButton = screen.getByRole('button', { name: /show details/i });
      fireEvent.click(expandButton);

      expect(screen.getByText(/transaction breakdown/i)).toBeInTheDocument();
    });
  });

  describe('Alerts and Warnings', () => {
    it('should display alert summary', () => {
      renderComponent();

      expect(screen.getByText(/2 alerts/i)).toBeInTheDocument();
      expect(screen.getByText(/low balance expected/i)).toBeInTheDocument();
    });

    it('should show alert details on hover', async () => {
      renderComponent();

      const alertIcon = screen.getByLabelText(/view alerts/i);
      fireEvent.mouseEnter(alertIcon);

      await waitFor(() => {
        expect(screen.getByText(/large payment due/i)).toBeInTheDocument();
      });
    });

    it('should categorize alerts by severity', () => {
      renderComponent();

      const alertsSection = screen.getByText(/alerts/i).closest('.alerts-section');
      expect(alertsSection).toHaveClass('has-warnings');
    });

    it('should not show alerts section when no alerts', () => {
      renderComponent({ data: mockEmptyData });

      expect(screen.queryByText(/alerts/i)).not.toBeInTheDocument();
    });
  });

  describe('Income and Expense Breakdown', () => {
    it('should display income breakdown', () => {
      renderComponent();

      fireEvent.click(screen.getByRole('button', { name: /income breakdown/i }));

      expect(screen.getByText(/total income/i)).toBeInTheDocument();
      expect(screen.getByText('$150,000')).toBeInTheDocument();
    });

    it('should display expense breakdown', () => {
      renderComponent();

      fireEvent.click(screen.getByRole('button', { name: /expense breakdown/i }));

      expect(screen.getByText(/total expenses/i)).toBeInTheDocument();
      expect(screen.getByText('$125,000')).toBeInTheDocument();
    });

    it('should show category breakdowns', () => {
      renderComponent();

      fireEvent.click(screen.getByRole('button', { name: /expense breakdown/i }));

      expect(screen.getByText(/by category/i)).toBeInTheDocument();
      // Would show actual categories if provided in data
    });
  });

  describe('Visual Indicators', () => {
    it('should show positive indicators for growth', () => {
      renderComponent();

      const netCashflowCard = screen.getByText(/net cash flow/i).closest('.summary-card');
      expect(netCashflowCard).toHaveClass('positive');
      expect(screen.getByTestId('arrow-up-icon')).toBeInTheDocument();
    });

    it('should show negative indicators for decline', () => {
      const negativeData = {
        ...mockCashflowData,
        scenarios: {
          ...mockCashflowData.scenarios,
          expected: {
            ...mockCashflowData.scenarios.expected,
            netCashflow: -15000,
            endingBalance: 35000,
          },
        },
      };

      renderComponent({ data: negativeData });

      const netCashflowCard = screen.getByText(/net cash flow/i).closest('.summary-card');
      expect(netCashflowCard).toHaveClass('negative');
      expect(screen.getByTestId('arrow-down-icon')).toBeInTheDocument();
    });

    it('should highlight critical low balance', () => {
      const criticalData = {
        ...mockCashflowData,
        scenarios: {
          ...mockCashflowData.scenarios,
          expected: {
            ...mockCashflowData.scenarios.expected,
            lowestBalance: 5000,
          },
        },
      };

      renderComponent({ data: criticalData });

      const lowestBalanceCard = screen.getByText(/lowest balance/i).closest('.summary-card');
      expect(lowestBalanceCard).toHaveClass('critical');
    });
  });

  describe('Empty and Loading States', () => {
    it('should handle empty data gracefully', () => {
      renderComponent({ data: mockEmptyData });

      expect(screen.getByText('$0')).toBeInTheDocument();
      expect(screen.queryByText(/no data available/i)).toBeInTheDocument();
    });

    it('should show loading skeleton when data is null', () => {
      renderComponent({ data: null });

      expect(screen.getAllByTestId('skeleton-loader')).toHaveLength(4);
    });
  });

  describe('Responsive Behavior', () => {
    it('should adjust layout for mobile', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      renderComponent();

      const container = screen.getByTestId('cashflow-summary-cards');
      expect(container).toHaveClass('mobile-layout');
    });

    it('should show condensed view on small screens', () => {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      renderComponent();

      expect(screen.queryByText(/income breakdown/i)).not.toBeInTheDocument();
      expect(screen.getByRole('button', { name: /more/i })).toBeInTheDocument();
    });
  });

  describe('Interactive Features', () => {
    it('should allow copying values to clipboard', async () => {
      const mockClipboard = {
        writeText: jest.fn().mockResolvedValue(undefined),
      };
      Object.assign(navigator, { clipboard: mockClipboard });

      renderComponent();

      const balanceValue = screen.getByText('$50,000');
      fireEvent.click(balanceValue);

      await waitFor(() => {
        expect(mockClipboard.writeText).toHaveBeenCalledWith('50000');
        expect(screen.getByText(/copied/i)).toBeInTheDocument();
      });
    });

    it('should export summary data', () => {
      renderComponent();

      const exportButton = screen.getByRole('button', { name: /export summary/i });
      fireEvent.click(exportButton);

      // Would trigger download in real implementation
      expect(screen.getByText(/exporting/i)).toBeInTheDocument();
    });
  });

  describe('Trend Analysis', () => {
    it('should show trend indicators', () => {
      renderComponent();

      expect(screen.getByText(/trending up/i)).toBeInTheDocument();
      expect(screen.getByTestId('trend-chart')).toBeInTheDocument();
    });

    it('should display comparison to previous period', () => {
      renderComponent();

      expect(screen.getByText(/vs last period/i)).toBeInTheDocument();
      expect(screen.getByText(/\+15%/i)).toBeInTheDocument();
    });
  });
});