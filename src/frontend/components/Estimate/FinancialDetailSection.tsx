import React from 'react';
import { AllocationWithTotals, ProjectTotals } from '../../hooks/useEstimateStaffManagement';
import { formatCurrency } from './utils';
import BudgetSummary from './BudgetSummary';

interface FinancialDetailSectionProps {
  /**
   * Staff allocations with calculated totals
   */
  allocationsWithTotals: AllocationWithTotals[];
  
  /**
   * Project totals calculations
   */
  projectTotals: ProjectTotals;
  
  /**
   * Callback for when discount type changes
   */
  onDiscountTypeChange?: (type: 'percentage' | 'amount' | 'none') => void;
  
  /**
   * Callback for when discount value changes
   */
  onDiscountValueChange?: (value: number) => void;
}

/**
 * Displays detailed financial information including rate analysis and breakdowns
 */
const FinancialDetailSection: React.FC<FinancialDetailSectionProps> = ({
  allocationsWithTotals,
  projectTotals,
  onDiscountTypeChange,
  onDiscountValueChange,
}) => {
  if (allocationsWithTotals.length === 0) {
    return null;
  }

  return (
    <div className="mt-6">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">Financial Detail</h3>
      
      {/* Rate Comparison and Target Analysis */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mt-4">
        <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">Rate Analysis</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Target Revenue</div>
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {formatCurrency(projectTotals.totalTargetRevenue)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Based on staff target rates
            </div>
          </div>
          
          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Actual Revenue</div>
            <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
              {formatCurrency(projectTotals.totalRevenue)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Based on proposed rates
            </div>
          </div>
          
          <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">Difference from Target</div>
            <div className={`text-lg font-bold ${
              projectTotals.targetRateDifference > 0 
                ? 'text-green-700 dark:text-green-400' 
                : projectTotals.targetRateDifference < 0
                ? 'text-red-700 dark:text-red-400'
                : 'text-gray-900 dark:text-gray-100'
            }`}>
              {projectTotals.targetRateDifference > 0 ? '+' : ''}
              {formatCurrency(projectTotals.targetRateDifference)}
              <span className="text-sm ml-1">
                ({projectTotals.targetRatePercentageDifference > 0 ? '+' : ''}
                {projectTotals.targetRatePercentageDifference.toFixed(1)}%)
              </span>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Compared to target rates
            </div>
          </div>
        </div>
        
        {/* Detailed Breakdown Table */}
        {allocationsWithTotals.length >= 3 && (
          <div className="mt-4">
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Detailed Financial Breakdown</h5>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Cost
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Revenue
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      % of Total
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Margin ($)
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Margin (%)
                    </th>
                    <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Vs Target
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {/* Staff Rows */}
                  {allocationsWithTotals.map((staff) => {
                    const marginAmount = staff.totalFees - staff.totalCost;
                    const marginPercentage = staff.totalFees > 0 ? (marginAmount / staff.totalFees) * 100 : 0;
                    const percentOfTotal = projectTotals.totalRevenue > 0 ? (staff.totalFees / projectTotals.totalRevenue) * 100 : 0;
                    
                    // Calculate rate difference
                    const targetTotal = staff.onbordTargetRateDaily * staff.totalDays;
                    const rateDifference = staff.totalFees - targetTotal;
                    
                    return (
                      <tr key={staff.internalId} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <td className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">
                          {staff.firstName} {staff.lastName || ''} - {staff.projectRole || 'Staff'}
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                          {formatCurrency(staff.totalCost)}
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                          {formatCurrency(staff.totalFees)}
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                          {percentOfTotal.toFixed(1)}%
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-right">
                          <span className={`${
                            marginAmount >= 0
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {formatCurrency(marginAmount)}
                          </span>
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-right">
                          <span className={`${
                            marginPercentage >= 30 
                              ? 'text-green-600 dark:text-green-400' 
                              : marginPercentage >= 20
                              ? 'text-yellow-500 dark:text-yellow-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {marginPercentage.toFixed(1)}%
                          </span>
                        </td>
                        <td className="px-3 py-2 text-sm font-medium text-right">
                          <span className={`${
                            rateDifference > 0
                              ? 'text-green-600 dark:text-green-400'
                              : rateDifference < 0
                              ? 'text-red-600 dark:text-red-400' 
                              : 'text-gray-700 dark:text-gray-300'
                          }`}>
                            {rateDifference > 0 ? '+' : ''}
                            {formatCurrency(rateDifference)}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
                <tfoot className="bg-gray-50 dark:bg-gray-700/80 font-semibold border-t-2 border-gray-300 dark:border-gray-600">
                  <tr>
                    <td className="px-3 py-2 text-sm font-medium text-gray-800 dark:text-gray-200">
                      Project Totals
                    </td>
                    <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                      {formatCurrency(projectTotals.totalCost)}
                    </td>
                    <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                      {formatCurrency(projectTotals.totalRevenue)}
                    </td>
                    <td className="px-3 py-2 text-sm font-medium text-right text-gray-700 dark:text-gray-300">
                      100%
                    </td>
                    <td className="px-3 py-2 text-sm font-medium text-right">
                      <span className={`${
                        projectTotals.marginAmount >= 0
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {formatCurrency(projectTotals.marginAmount)}
                      </span>
                    </td>
                    <td className="px-3 py-2 text-sm font-medium text-right">
                      <span className={`${
                        projectTotals.marginPercentage >= 30 
                          ? 'text-green-600 dark:text-green-400' 
                          : projectTotals.marginPercentage >= 20
                          ? 'text-yellow-500 dark:text-yellow-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {projectTotals.marginPercentage.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-3 py-2 text-sm font-medium text-right">
                      <span className={`${
                        projectTotals.targetRateDifference > 0
                          ? 'text-green-600 dark:text-green-400'
                          : projectTotals.targetRateDifference < 0
                          ? 'text-red-600 dark:text-red-400' 
                          : 'text-gray-700 dark:text-gray-300'
                      }`}>
                        {projectTotals.targetRateDifference > 0 ? '+' : ''}
                        {formatCurrency(projectTotals.targetRateDifference)}
                      </span>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}
      </div>
      
      {/* Budget Summary under Financial Detail */}
      <div className="mt-4">
        <BudgetSummary
          allocationsWithTotals={allocationsWithTotals}
          projectTotals={projectTotals}
          onDiscountTypeChange={onDiscountTypeChange}
          onDiscountValueChange={onDiscountValueChange}
        />
      </div>
    </div>
  );
};

export default FinancialDetailSection;