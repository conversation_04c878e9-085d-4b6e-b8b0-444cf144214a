/**
 * Repository for managing companies
 */

import { v4 as uuidv4 } from 'uuid';
import { BaseRepositoryEnhanced, EnhancedQueryOptions } from './base-repository-enhanced';
import {
  Company,
  CompanyCreate,
  CompanyUpdate
} from '../../types/company-types';
import { Contact } from '../../frontend/types/crm-types';

// Types for company repository operations
interface CompanyMergeData {
  updated_at: string;
  updated_by: string;
  hubspot_id?: string;
  harvest_id?: number;
}

interface CompanyLinkingCategorized {
  hubspotOnly: Company[];
  harvestOnly: Company[];
  bothMissing: Company[];
}

interface FieldChangeParams {
  entityId: string;
  fieldName: string;
  oldValue: string | number | null;
  newValue: string | number | null;
  source: string;
  userId: string;
}

/**
 * Repository for managing companies
 */
export class CompanyRepository extends BaseRepositoryEnhanced {
  /**
   * Constructor
   */
  constructor() {
    super('company');
  }

  /**
   * Helper method to normalize priority values from database to frontend format
   * @param company Company object with potentially lowercase priority
   * @returns Company object with capitalized priority
   */
  private normalizePriority(company: any): any {
    if (company && company.priority) {
      const priorityMap: { [key: string]: string } = {
        'high': 'High',
        'medium': 'Medium', 
        'low': 'Low',
        'qualified out': 'Qualified out'
      };
      company.priority = priorityMap[company.priority] || company.priority;
    }
    return company;
  }

  /**
   * Get all companies with deal statistics
   * @param options Query options including soft delete handling
   * @returns Array of companies
   */
  getAllCompanies(options: EnhancedQueryOptions = {}): Company[] {
    try {
      const { includeDeleted = false } = options;
      
      const query = `
        SELECT
          c.id,
          c.name,
          c.industry,
          c.size,
          c.website,
          c.address,
          c.description,
          c.hubspot_id as hubspotId,
          c.harvest_id as harvestId,
          c.source,
          c.radar_state as radarState,
          c.priority,
          c.current_spend as currentSpend,
          c.potential_spend as potentialSpend,
          c.last_interaction_date as lastInteractionDate,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          c.created_by as createdBy,
          c.updated_by as updatedBy,
          c.deleted_at as deletedAt,
          -- Enrichment fields
          c.enrichment_status as enrichmentStatus,
          c.last_enriched_at as lastEnrichedAt,
          -- Deal statistics
          COALESCE(deal_stats.active_deals_count, 0) AS activeDealsCount,
          COALESCE(deal_stats.total_deal_value, 0) AS totalDealValue,
          -- Contact count
          COALESCE(contact_stats.contact_count, 0) AS contactCount
        FROM company c
        LEFT JOIN (
          SELECT
            company_id,
            COUNT(*) AS active_deals_count,
            SUM(COALESCE(value, 0)) AS total_deal_value
          FROM deal
          WHERE deleted_at IS NULL
            AND stage NOT IN ('Closed won', 'Closed lost', 'Abandoned')
          GROUP BY company_id
        ) deal_stats ON c.id = deal_stats.company_id
        LEFT JOIN (
          SELECT
            company_id,
            COUNT(*) AS contact_count
          FROM contact_company
          GROUP BY company_id
        ) contact_stats ON c.id = contact_stats.company_id
        ${includeDeleted ? '' : 'WHERE c.deleted_at IS NULL'}
        ORDER BY c.name
      `;

      const companies = this.db.prepare(query).all() as Company[];
      return companies.map(company => ({
        ...this.normalizePriority(company),
        enrichmentStatus: company.enrichmentStatus ? JSON.parse(company.enrichmentStatus) : null
      }));
    } catch (error) {
      console.error('Error fetching companies from database:', error);
      return [];
    }
  }

  /**
   * Get company by ID
   * @param id Company ID
   * @param options Query options including soft delete handling
   * @returns Company or null if not found
   */
  getCompanyById(id: string, options: EnhancedQueryOptions = {}): Company | null {
    try {
      const { includeDeleted = false } = options;
      
      const company = this.db.prepare(`
        SELECT
          c.id,
          c.name,
          c.industry,
          c.size,
          c.website,
          c.address,
          c.description,
          c.hubspot_id as hubspotId,
          c.harvest_id as harvestId,
          c.source,
          c.radar_state as radarState,
          c.priority,
          c.current_spend as currentSpend,
          c.potential_spend as potentialSpend,
          c.last_interaction_date as lastInteractionDate,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          c.created_by as createdBy,
          c.updated_by as updatedBy,
          c.deleted_at as deletedAt,
          -- Enrichment fields
          c.enrichment_status as enrichmentStatus,
          c.last_enriched_at as lastEnrichedAt,
          -- Deal statistics
          COALESCE(deal_stats.active_deals_count, 0) AS activeDealsCount,
          COALESCE(deal_stats.total_deal_value, 0) AS totalDealValue
        FROM company c
        LEFT JOIN (
          SELECT
            company_id,
            COUNT(*) AS active_deals_count,
            SUM(COALESCE(value, 0)) AS total_deal_value
          FROM deal
          WHERE deleted_at IS NULL
            AND stage NOT IN ('Closed won', 'Closed lost', 'Abandoned')
          GROUP BY company_id
        ) deal_stats ON c.id = deal_stats.company_id
        WHERE c.id = ?
          ${includeDeleted ? '' : 'AND c.deleted_at IS NULL'}
      `).get(id) as Company | undefined;

      if (!company) return null;

      // Parse enrichment status if present
      if (company.enrichmentStatus) {
        company.enrichmentStatus = JSON.parse(company.enrichmentStatus);
      }

      // Get associated contacts through the junction table
      const contacts = this.db.prepare(`
        SELECT
          c.id, c.first_name as firstName, c.last_name as lastName,
          c.email, c.phone, c.job_title as jobTitle,
          c.created_at as createdAt, c.updated_at as updatedAt, c.notes,
          c.hubspot_id as hubspotId,
          cc.role, cc.is_primary as isPrimary
        FROM contact c
        INNER JOIN contact_company cc ON c.id = cc.contact_id
        WHERE cc.company_id = ?
          AND c.deleted_at IS NULL
      `).all(company.id) as Contact[];

      company.contacts = contacts;

      return this.normalizePriority(company);
    } catch (error) {
      console.error('Error fetching company by ID from database:', error);
      return null;
    }
  }

  /**
   * Get company by HubSpot ID
   * @param hubspotId HubSpot ID
   * @returns Company or null if not found
   */
  getCompanyByHubspotId(hubspotId: string): Company | null {
    try {
      const company = this.db.prepare(`
        SELECT
          c.id,
          c.name,
          c.industry,
          c.size,
          c.website,
          c.address,
          c.description,
          c.hubspot_id as hubspotId,
          c.harvest_id as harvestId,
          c.source,
          c.radar_state as radarState,
          c.priority,
          c.current_spend as currentSpend,
          c.potential_spend as potentialSpend,
          c.last_interaction_date as lastInteractionDate,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          c.created_by as createdBy,
          c.updated_by as updatedBy,
          c.deleted_at as deletedAt,
          -- Enrichment fields
          c.enrichment_status as enrichmentStatus,
          c.last_enriched_at as lastEnrichedAt,
          -- Deal statistics
          COALESCE(deal_stats.active_deals_count, 0) AS activeDealsCount,
          COALESCE(deal_stats.total_deal_value, 0) AS totalDealValue
        FROM company c
        LEFT JOIN (
          SELECT
            company_id,
            COUNT(*) AS active_deals_count,
            SUM(COALESCE(value, 0)) AS total_deal_value
          FROM deal
          WHERE deleted_at IS NULL
            AND stage NOT IN ('Closed won', 'Closed lost', 'Abandoned')
          GROUP BY company_id
        ) deal_stats ON c.id = deal_stats.company_id
        WHERE c.hubspot_id = ?
      `).get(hubspotId) as Company | undefined;

      if (!company) return null;

      // Parse enrichment status if present
      if (company.enrichmentStatus) {
        company.enrichmentStatus = JSON.parse(company.enrichmentStatus);
      }

      // Get associated contacts through the junction table
      const contacts = this.db.prepare(`
        SELECT
          c.id, c.first_name as firstName, c.last_name as lastName,
          c.email, c.phone, c.job_title as jobTitle,
          c.created_at as createdAt, c.updated_at as updatedAt, c.notes,
          c.hubspot_id as hubspotId,
          cc.role, cc.is_primary as isPrimary
        FROM contact c
        INNER JOIN contact_company cc ON c.id = cc.contact_id
        WHERE cc.company_id = ?
          AND c.deleted_at IS NULL
      `).all(company.id) as Contact[];

      company.contacts = contacts;

      return this.normalizePriority(company);
    } catch (error) {
      console.error('Error fetching company by HubSpot ID from database:', error);
      return null;
    }
  }

  /**
   * Create a new company
   * @param companyData Company data
   * @param source Data source
   * @returns Created company
   */
  createCompany(companyData: CompanyCreate, source: string = 'manual'): Company {
    try {
      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO company (
          id,
          name,
          industry,
          size,
          website,
          address,
          description,
          hubspot_id,
          harvest_id,
          source,
          radar_state,
          priority,
          current_spend,
          potential_spend,
          last_interaction_date,
          created_at,
          updated_at,
          created_by,
          updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        companyData.name,
        companyData.industry || null,
        companyData.size || null,
        companyData.website || null,
        companyData.address || null,
        companyData.description || null,
        companyData.hubspotId || null,
        companyData.harvestId || null,
        source,
        companyData.radarState || null,
        companyData.priority ? companyData.priority.toLowerCase() : null,
        companyData.currentSpend || 0,
        companyData.potentialSpend || 0,
        companyData.lastInteractionDate || null,
        now,
        now,
        companyData.createdBy || 'system',
        companyData.updatedBy || 'system'
      );

      return this.getCompanyById(id) as Company;
    } catch (error) {
      console.error('Error creating company in database:', error);
      throw error;
    }
  }

  /**
   * Update a company
   * @param id Company ID
   * @param companyData Company data
   * @returns Updated company
   */
  updateCompany(id: string, companyData: CompanyUpdate): Company | null {
    try {
      const company = this.getCompanyById(id);
      if (!company) {
        console.error(`Company with ID ${id} not found`);
        return null;
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE company
        SET
          name = ?,
          industry = ?,
          size = ?,
          website = ?,
          address = ?,
          description = ?,
          hubspot_id = ?,
          harvest_id = ?,
          source = ?,
          radar_state = ?,
          priority = ?,
          current_spend = ?,
          potential_spend = ?,
          last_interaction_date = ?,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(
        companyData.name || company.name,
        companyData.industry !== undefined ? companyData.industry : company.industry,
        companyData.size !== undefined ? companyData.size : company.size,
        companyData.website !== undefined ? companyData.website : company.website,
        companyData.address !== undefined ? companyData.address : company.address,
        companyData.description !== undefined ? companyData.description : company.description,
        companyData.hubspotId !== undefined ? companyData.hubspotId : company.hubspotId,
        companyData.harvestId !== undefined ? companyData.harvestId : company.harvestId,
        companyData.source || company.source,
        companyData.radarState !== undefined ? companyData.radarState : company.radarState,
        companyData.priority !== undefined ? (companyData.priority ? companyData.priority.toLowerCase() : companyData.priority) : company.priority,
        companyData.currentSpend !== undefined ? (companyData.currentSpend || 0) : company.currentSpend,
        companyData.potentialSpend !== undefined ? (companyData.potentialSpend || 0) : company.potentialSpend,
        companyData.lastInteractionDate !== undefined ? companyData.lastInteractionDate : company.lastInteractionDate,
        now,
        companyData.updatedBy || 'system',
        id
      );

      return this.getCompanyById(id);
    } catch (error) {
      console.error('Error updating company in database:', error);
      throw error;
    }
  }

  /**
   * Delete a company
   * @param id Company ID
   * @param softDelete Whether to perform a soft delete (default: true)
   * @returns Boolean indicating success
   */
  deleteCompany(id: string, softDelete: boolean = true): boolean {
    try {
      if (softDelete) {
        const now = new Date().toISOString();
        this.db.prepare(`
          UPDATE company
          SET deleted_at = ?
          WHERE id = ?
        `).run(now, id);
      } else {
        this.db.prepare(`
          DELETE FROM company
          WHERE id = ?
        `).run(id);
      }
      return true;
    } catch (error) {
      console.error('Error deleting company from database:', error);
      return false;
    }
  }

  /**
   * Get all companies with their linking status and external system details
   * @returns Array of companies with linking information
   */
  getAllCompaniesWithLinkingStatus(): Array<Company & {
    linkingStatus: 'both' | 'hubspot_only' | 'harvest_only' | 'none'
  }> {
    try {
      // Get all companies from upstream database
      const companies = this.db.prepare(`
        SELECT
          c.id,
          c.name,
          c.industry,
          c.size,
          c.website,
          c.address,
          c.description,
          c.hubspot_id as hubspotId,
          c.harvest_id as harvestId,
          c.source,
          c.radar_state as radarState,
          c.priority,
          c.current_spend as currentSpend,
          c.potential_spend as potentialSpend,
          c.last_interaction_date as lastInteractionDate,
          c.created_at as createdAt,
          c.updated_at as updatedAt,
          c.created_by as createdBy,
          c.updated_by as updatedBy,
          c.deleted_at as deletedAt,
          -- Enrichment fields
          c.enrichment_status as enrichmentStatus,
          c.last_enriched_at as lastEnrichedAt,
          -- Deal statistics
          COALESCE(deal_stats.active_deals_count, 0) AS activeDealsCount,
          COALESCE(deal_stats.total_deal_value, 0) AS totalDealValue
        FROM company c
        LEFT JOIN (
          SELECT
            company_id,
            COUNT(*) AS active_deals_count,
            SUM(COALESCE(value, 0)) AS total_deal_value
          FROM deal
          WHERE deleted_at IS NULL
            AND stage NOT IN ('Closed won', 'Closed lost', 'Abandoned')
          GROUP BY company_id
        ) deal_stats ON c.id = deal_stats.company_id
        WHERE c.deleted_at IS NULL
        ORDER BY c.name
      `).all() as Company[];

      // For each company, determine linking status
      return companies.map(company => {
        let linkingStatus: 'both' | 'hubspot_only' | 'harvest_only' | 'none';

        if (company.hubspotId && company.harvestId) {
          linkingStatus = 'both';
        } else if (company.hubspotId && !company.harvestId) {
          linkingStatus = 'hubspot_only';
        } else if (!company.hubspotId && company.harvestId) {
          linkingStatus = 'harvest_only';
        } else {
          linkingStatus = 'none';
        }

        return {
          ...this.normalizePriority(company),
          enrichmentStatus: company.enrichmentStatus ? JSON.parse(company.enrichmentStatus) : null,
          linkingStatus
        };
      });
    } catch (error) {
      console.error('Error getting companies with linking status:', error);
      throw error;
    }
  }

  /**
   * Get companies that need linking (missing either HubSpot or Harvest IDs)
   * @returns Object with arrays of companies that need linking
   */
  getUnlinkedCompanies(): CompanyLinkingCategorized {
    try {
      // Companies with HubSpot ID but no Harvest ID
      const hubspotOnly = this.db.prepare(`
        SELECT
          id,
          name,
          industry,
          size,
          website,
          address,
          description,
          hubspot_id as hubspotId,
          harvest_id as harvestId,
          source,
          radar_state as radarState,
          priority,
          current_spend as currentSpend,
          potential_spend as potentialSpend,
          last_interaction_date as lastInteractionDate,
          created_at as createdAt,
          updated_at as updatedAt,
          created_by as createdBy,
          updated_by as updatedBy,
          deleted_at as deletedAt
        FROM company
        WHERE hubspot_id IS NOT NULL
          AND harvest_id IS NULL
          AND deleted_at IS NULL
        ORDER BY name
      `).all() as Company[];

      // Companies with Harvest ID but no HubSpot ID
      const harvestOnly = this.db.prepare(`
        SELECT
          id,
          name,
          industry,
          size,
          website,
          address,
          description,
          hubspot_id as hubspotId,
          harvest_id as harvestId,
          source,
          radar_state as radarState,
          priority,
          current_spend as currentSpend,
          potential_spend as potentialSpend,
          last_interaction_date as lastInteractionDate,
          created_at as createdAt,
          updated_at as updatedAt,
          created_by as createdBy,
          updated_by as updatedBy,
          deleted_at as deletedAt
        FROM company
        WHERE harvest_id IS NOT NULL
          AND hubspot_id IS NULL
          AND deleted_at IS NULL
        ORDER BY name
      `).all() as Company[];

      // Companies with neither ID (manual entries)
      const bothMissing = this.db.prepare(`
        SELECT
          id,
          name,
          industry,
          size,
          website,
          address,
          description,
          hubspot_id as hubspotId,
          harvest_id as harvestId,
          source,
          radar_state as radarState,
          priority,
          current_spend as currentSpend,
          potential_spend as potentialSpend,
          last_interaction_date as lastInteractionDate,
          created_at as createdAt,
          updated_at as updatedAt,
          created_by as createdBy,
          updated_by as updatedBy,
          deleted_at as deletedAt
        FROM company
        WHERE hubspot_id IS NULL
          AND harvest_id IS NULL
          AND deleted_at IS NULL
        ORDER BY name
      `).all() as Company[];

      return { 
        hubspotOnly: hubspotOnly.map(company => this.normalizePriority(company)), 
        harvestOnly: harvestOnly.map(company => this.normalizePriority(company)), 
        bothMissing: bothMissing.map(company => this.normalizePriority(company))
      };
    } catch (error) {
      console.error('Error fetching unlinked companies from database:', error);
      return { hubspotOnly: [], harvestOnly: [], bothMissing: [] };
    }
  }

  /**
   * Link a company to HubSpot
   * @param companyId Company ID
   * @param hubspotId HubSpot company ID
   * @param userId User making the change
   * @returns Updated company or null if failed
   */
  linkCompanyToHubSpot(companyId: string, hubspotId: string, userId: string): Company | null {
    try {
      const company = this.getCompanyById(companyId);
      if (!company) {
        console.error(`Company with ID ${companyId} not found`);
        return null;
      }

      // Check if HubSpot ID is already used
      const existingCompany = this.getCompanyByHubspotId(hubspotId);
      if (existingCompany && existingCompany.id !== companyId) {
        throw new Error(`HubSpot ID ${hubspotId} is already linked to company: ${existingCompany.name}`);
      }

      const now = new Date().toISOString();

      // Update the company with HubSpot ID
      this.db.prepare(`
        UPDATE company
        SET
          hubspot_id = ?,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(hubspotId, now, userId, companyId);

      // Log the change for audit trail
      this.logFieldChange(companyId, 'hubspot_id', company.hubspotId, hubspotId, 'Manual', userId);

      return this.getCompanyById(companyId);
    } catch (error) {
      console.error('Error linking company to HubSpot:', error);
      throw error;
    }
  }

  /**
   * Link a company to Harvest
   * @param companyId Company ID
   * @param harvestId Harvest client ID
   * @param userId User making the change
   * @returns Updated company or null if failed
   */
  linkCompanyToHarvest(companyId: string, harvestId: number, userId: string): Company | null {
    try {
      const company = this.getCompanyById(companyId);
      if (!company) {
        console.error(`Company with ID ${companyId} not found`);
        return null;
      }

      // Check if Harvest ID is already used
      const existingCompany = this.db.prepare(`
        SELECT id, name FROM company WHERE harvest_id = ? AND id != ?
      `).get(harvestId, companyId);

      if (existingCompany) {
        throw new Error(`Harvest ID ${harvestId} is already linked to company: ${existingCompany.name}`);
      }

      const now = new Date().toISOString();

      // Update the company with Harvest ID
      this.db.prepare(`
        UPDATE company
        SET
          harvest_id = ?,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(harvestId, now, userId, companyId);

      // Log the change for audit trail
      this.logFieldChange(companyId, 'harvest_id', company.harvestId, harvestId, 'Manual', userId);

      return this.getCompanyById(companyId);
    } catch (error) {
      console.error('Error linking company to Harvest:', error);
      throw error;
    }
  }

  /**
   * Unlink a company from HubSpot
   * @param companyId Company ID
   * @param userId User making the change
   * @returns Updated company or null if failed
   */
  unlinkCompanyFromHubSpot(companyId: string, userId: string): Company | null {
    try {
      const company = this.getCompanyById(companyId);
      if (!company) {
        console.error(`Company with ID ${companyId} not found`);
        return null;
      }

      const now = new Date().toISOString();

      // Remove HubSpot ID
      this.db.prepare(`
        UPDATE company
        SET
          hubspot_id = NULL,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(now, userId, companyId);

      // Log the change for audit trail
      this.logFieldChange(companyId, 'hubspot_id', company.hubspotId, null, 'Manual', userId);

      return this.getCompanyById(companyId);
    } catch (error) {
      console.error('Error unlinking company from HubSpot:', error);
      throw error;
    }
  }

  /**
   * Unlink a company from Harvest
   * @param companyId Company ID
   * @param userId User making the change
   * @returns Updated company or null if failed
   */
  unlinkCompanyFromHarvest(companyId: string, userId: string): Company | null {
    try {
      const company = this.getCompanyById(companyId);
      if (!company) {
        console.error(`Company with ID ${companyId} not found`);
        return null;
      }

      const now = new Date().toISOString();

      // Remove Harvest ID
      this.db.prepare(`
        UPDATE company
        SET
          harvest_id = NULL,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(now, userId, companyId);

      // Log the change for audit trail
      this.logFieldChange(companyId, 'harvest_id', company.harvestId, null, 'Manual', userId);

      return this.getCompanyById(companyId);
    } catch (error) {
      console.error('Error unlinking company from Harvest:', error);
      throw error;
    }
  }

  /**
   * Merge two companies - useful when the same company exists in both HubSpot and Harvest
   * @param sourceCompanyId Company to merge from (will be deleted)
   * @param targetCompanyId Company to merge to (will be updated)
   * @param userId User making the change
   * @returns Merged company or null if failed
   */
  mergeCompanies(sourceCompanyId: string, targetCompanyId: string, userId: string): Company | null {
    try {
      const sourceCompany = this.getCompanyById(sourceCompanyId);
      const targetCompany = this.getCompanyById(targetCompanyId);

      if (!sourceCompany || !targetCompany) {
        throw new Error('Source or target company not found');
      }

      // Check if both companies have the same external ID for the same system
      if (sourceCompany.hubspotId && targetCompany.hubspotId && sourceCompany.hubspotId === targetCompany.hubspotId) {
        throw new Error('Cannot merge companies with the same HubSpot ID');
      }
      if (sourceCompany.harvestId && targetCompany.harvestId && sourceCompany.harvestId === targetCompany.harvestId) {
        throw new Error('Cannot merge companies with the same Harvest ID');
      }

      const now = new Date().toISOString();

      // Start transaction
      const transaction = this.db.transaction(() => {
        // Update the target company with missing IDs from source
        const updateData: CompanyMergeData = {
          updated_at: now,
          updated_by: userId
        };

        if (!targetCompany.hubspotId && sourceCompany.hubspotId) {
          updateData.hubspot_id = sourceCompany.hubspotId;
          this.logFieldChange(targetCompanyId, 'hubspot_id', null, sourceCompany.hubspotId, 'Manual', userId);
        }

        if (!targetCompany.harvestId && sourceCompany.harvestId) {
          updateData.harvest_id = sourceCompany.harvestId;
          this.logFieldChange(targetCompanyId, 'harvest_id', null, sourceCompany.harvestId, 'Manual', userId);
        }

        // Update target company with validated columns
        const allowedUpdateColumns = ['name', 'linkedin_url', 'website', 'industry', 'domain', 'hubspot_id', 'harvest_id', 'updated_at', 'updated_by'];
        const updateColumns: string[] = [];
        const updateValues: (string | number)[] = [];
        
        for (const [key, value] of Object.entries(updateData)) {
          if (allowedUpdateColumns.includes(key)) {
            updateColumns.push(`${key} = ?`);
            updateValues.push(value);
          } else {
            console.warn(`Skipping invalid column in company update: ${key}`);
          }
        }
        
        if (updateColumns.length > 0) {
          const updateQuery = `
            UPDATE company
            SET ${updateColumns.join(', ')}
            WHERE id = ?
          `;
          this.db.prepare(updateQuery).run(...updateValues, targetCompanyId);
        }

        // Update any related records to point to the target company
        // Update deals
        this.db.prepare(`
          UPDATE deal SET company_id = ?, updated_at = ? WHERE company_id = ?
        `).run(targetCompanyId, now, sourceCompanyId);

        // Update contacts
        this.db.prepare(`
          UPDATE contact SET company_id = ?, updated_at = ? WHERE company_id = ?
        `).run(targetCompanyId, now, sourceCompanyId);

        // Soft delete the source company
        this.db.prepare(`
          UPDATE company
          SET deleted_at = ?, updated_at = ?, updated_by = ?
          WHERE id = ?
        `).run(now, now, userId, sourceCompanyId);

        // Log the merge action
        this.logFieldChange(targetCompanyId, 'merged_from', null, sourceCompanyId, 'Manual', userId);
      });

      transaction();

      return this.getCompanyById(targetCompanyId);
    } catch (error) {
      console.error('Error merging companies:', error);
      throw error;
    }
  }

  /**
   * Log field change for audit trail
   * @param entityId Entity ID
   * @param fieldName Field name
   * @param oldValue Old value
   * @param newValue New value
   * @param source Change source
   * @param userId User making the change
   */
  private logFieldChange(
    entityId: string,
    fieldName: string,
    oldValue: string | number | null,
    newValue: string | number | null,
    source: string,
    userId: string
  ): void {
    try {
      // Check if change_log table exists
      const tableExists = this.db.prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='change_log'
      `).get();

      if (tableExists) {
        const now = new Date().toISOString();

        this.db.prepare(`
          INSERT INTO change_log (
            id, entity_type, entity_id, field_name,
            old_value, new_value, change_type, change_source, changed_at, changed_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          uuidv4(),
          'company',
          entityId,
          fieldName,
          oldValue ? String(oldValue) : null,
          newValue ? String(newValue) : null,
          'update', // change_type
          source,
          now,
          userId
        );
      }
    } catch (error) {
      console.error('Error logging field change:', error);
      // Don't throw here - logging failure shouldn't break the main operation
    }
  }
}
