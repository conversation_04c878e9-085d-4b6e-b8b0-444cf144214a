import React from 'react'; /** * Expenses section content for the help page */ export const ExpensesSection: React.FC = () => { return ( <> <p> The Expenses section allows you to add recurring expenses that aren't tracked in your accounting system, enhancing the accuracy of your cashflow forecasts. </p> <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-4"> Expense Types </h3> <div className="grid md:grid-cols-2 gap-4 mt-3"> <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg"> <h4 className="font-medium text-gray-800 dark:text-gray-200">Recurring Expenses</h4> <p className="text-sm mt-1">Weekly, monthly, quarterly, or annual expenses</p> </div> <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg"> <h4 className="font-medium text-gray-800 dark:text-gray-200">One-off Expenses</h4> <p className="text-sm mt-1">Single future expenses for accurate forecasting</p> </div> </div> <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-6"> Categories </h3> <ul className="list-disc pl-6 space-y-1 mt-2 text-sm"> <li>Monthly Payroll</li> <li>Software</li> <li>Taxes</li> <li>Superannuation</li> <li>Insurance</li> <li>Other</li> </ul> </> ); };