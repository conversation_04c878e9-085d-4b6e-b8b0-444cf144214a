/**
 * Change Log Repository Test
 * 
 * Tests for the change log repository focusing on business behavior of audit trails.
 * Uses an in-memory SQLite database for realistic testing without mocks.
 */
import { ChangeLogRepository } from '../../../../src/api/repositories/change-log-repository';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

describe('ChangeLogRepository - Business Behavior', () => {
  let repository: ChangeLogRepository;
  let db: Database.Database;
  
  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Initialize schema using the unified migration
    const migrationPath = path.join(__dirname, '../../../../migrations/000_unified_schema.sql');
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    } else {
      throw new Error(`Migration file not found at ${migrationPath}`);
    }
  });
  
  beforeEach(() => {
    repository = new ChangeLogRepository();
    
    // Clear data before each test
    db.prepare('DELETE FROM change_log').run();
    db.prepare('DELETE FROM company').run();
    db.prepare('DELETE FROM contact').run();
    db.prepare('DELETE FROM deal').run();
    
    // Insert test data
    db.prepare(`
      INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
      VALUES ('company-1', 'Acme Corp', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO contact (id, first_name, last_name, email, created_at, updated_at, created_by, updated_by)
      VALUES ('contact-1', 'John', 'Doe', '<EMAIL>', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO deal (id, deal_name, company_id, stage, amount, created_at, updated_at, created_by, updated_by)
      VALUES ('deal-1', 'Big Deal', 'company-1', 'negotiation', 100000, datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
  });
  
  afterAll(() => {
    db.close();
  });

  describe('Business Rule: Audit Trail Creation', () => {
    it('should track field changes for compliance and history', () => {
      const changeData = {
        entity_type: 'company',
        entity_id: 'company-1',
        field_name: 'revenue',
        old_value: '1000000',
        new_value: '1500000',
        changed_by: 'sales-manager',
        source: 'manual',
      };

      const result = repository.logChange(changeData);

      // Business expectation: Every change is audited
      expect(result.id).toBeDefined();
      expect(result.entity_type).toBe('company');
      expect(result.entity_id).toBe('company-1');
      expect(result.field_name).toBe('revenue');
      expect(result.old_value).toBe('1000000');
      expect(result.new_value).toBe('1500000');
      expect(result.changed_by).toBe('sales-manager');
      expect(result.source).toBe('manual');
      expect(result.created_at).toBeDefined();
    });

    it('should track when fields are set for the first time', () => {
      const changeData = {
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'phone',
        old_value: null,
        new_value: '+**********',
        changed_by: 'data-entry',
        source: 'manual',
      };

      const result = repository.logChange(changeData);

      // Business requirement: Track data completion
      expect(result.old_value).toBeNull();
      expect(result.new_value).toBe('+**********');
    });

    it('should differentiate between manual and automated changes', () => {
      // Manual change
      const manualChange = repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'stage',
        old_value: 'negotiation',
        new_value: 'closed-won',
        changed_by: 'sales-rep',
        source: 'manual',
      });

      // Automated sync change
      const syncChange = repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'last_activity_date',
        old_value: '2024-01-01',
        new_value: '2024-01-15',
        changed_by: 'system',
        source: 'hubspot_sync',
      });

      // Business requirement: Distinguish between user actions and system updates
      expect(manualChange.source).toBe('manual');
      expect(syncChange.source).toBe('hubspot_sync');
    });
  });

  describe('Business Rule: Change History Retrieval', () => {
    beforeEach(() => {
      // Create a series of changes for testing
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'stage',
        old_value: 'qualified',
        new_value: 'proposal',
        changed_by: 'sales-rep-1',
        source: 'manual',
      });
      
      // Wait a bit to ensure different timestamps
      const delay = () => new Promise(resolve => setTimeout(resolve, 10));
      
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'amount',
        old_value: '50000',
        new_value: '75000',
        changed_by: 'sales-manager',
        source: 'manual',
      });
      
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'stage',
        old_value: 'proposal',
        new_value: 'negotiation',
        changed_by: 'sales-rep-1',
        source: 'manual',
      });
    });

    it('should retrieve complete audit trail for an entity', () => {
      const history = repository.getChangeHistory('deal', 'deal-1');

      // Business requirement: View complete history of changes
      expect(history.length).toBeGreaterThanOrEqual(3);
      expect(history[0].entity_type).toBe('deal');
      expect(history[0].entity_id).toBe('deal-1');
      
      // Should be ordered by most recent first
      const stages = history.filter(h => h.field_name === 'stage');
      expect(stages.length).toBeGreaterThanOrEqual(2);
    });

    it('should support pagination for large audit trails', () => {
      // Create many changes
      for (let i = 0; i < 25; i++) {
        repository.logChange({
          entity_type: 'company',
          entity_id: 'company-1',
          field_name: 'status',
          old_value: `status-${i}`,
          new_value: `status-${i+1}`,
          changed_by: 'batch-process',
          source: 'automated',
        });
      }

      // Business requirement: Handle large audit trails efficiently
      const page1 = repository.getChangeHistory('company', 'company-1', { limit: 10, offset: 0 });
      const page2 = repository.getChangeHistory('company', 'company-1', { limit: 10, offset: 10 });
      
      expect(page1).toHaveLength(10);
      expect(page2).toHaveLength(10);
      expect(page1[0].id).not.toBe(page2[0].id);
    });
  });

  describe('Business Rule: Field-Specific History', () => {
    it('should track evolution of specific fields over time', () => {
      // Create a progression of status changes
      repository.logChange({
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'status',
        old_value: null,
        new_value: 'pending',
        changed_by: 'onboarding-system',
        source: 'automated',
      });
      
      repository.logChange({
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'status',
        old_value: 'pending',
        new_value: 'active',
        changed_by: 'account-manager',
        source: 'manual',
      });
      
      repository.logChange({
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'email',
        old_value: '<EMAIL>',
        new_value: '<EMAIL>',
        changed_by: 'contact-self',
        source: 'portal',
      });

      const statusHistory = repository.getFieldHistory('contact', 'contact-1', 'status');

      // Business requirement: Track field-specific changes for analysis
      expect(statusHistory).toHaveLength(2);
      expect(statusHistory.every(h => h.field_name === 'status')).toBe(true);
      
      // Verify progression
      expect(statusHistory[1].new_value).toBe('pending'); // Oldest
      expect(statusHistory[0].new_value).toBe('active');  // Newest
    });
  });

  describe('Business Rule: Activity Monitoring', () => {
    it('should provide recent system-wide activity for monitoring', () => {
      // Create various changes across different entities
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'probability',
        old_value: '50',
        new_value: '75',
        changed_by: 'sales-rep',
        source: 'manual',
      });
      
      repository.logChange({
        entity_type: 'company',
        entity_id: 'company-1',
        field_name: 'name',
        old_value: 'Acme Corp',
        new_value: 'Acme Corporation',
        changed_by: 'data-admin',
        source: 'manual',
      });
      
      repository.logChange({
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'position',
        old_value: 'Manager',
        new_value: 'Director',
        changed_by: 'hr-sync',
        source: 'automated',
      });

      const recentChanges = repository.getRecentChanges(10);

      // Business requirement: Monitor all system activity
      expect(recentChanges.length).toBeGreaterThanOrEqual(3);
      
      // Should include changes from different entity types
      const entityTypes = [...new Set(recentChanges.map(c => c.entity_type))];
      expect(entityTypes.length).toBeGreaterThanOrEqual(3);
      
      // Most recent changes first
      for (let i = 1; i < recentChanges.length; i++) {
        const prev = new Date(recentChanges[i-1].created_at).getTime();
        const curr = new Date(recentChanges[i].created_at).getTime();
        expect(prev).toBeGreaterThanOrEqual(curr);
      }
    });
  });

  describe('Business Rule: User Activity Tracking', () => {
    it('should track all changes made by specific users for accountability', () => {
      const userId = 'power-user';
      
      // User makes various changes
      repository.logChange({
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'email',
        old_value: '<EMAIL>',
        new_value: '<EMAIL>',
        changed_by: userId,
        source: 'manual',
      });
      
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'stage',
        old_value: 'qualified',
        new_value: 'proposal',
        changed_by: userId,
        source: 'manual',
      });
      
      repository.logChange({
        entity_type: 'company',
        entity_id: 'company-1',
        field_name: 'revenue',
        old_value: '1000000',
        new_value: '1500000',
        changed_by: 'different-user',
        source: 'manual',
      });

      const userChanges = repository.getChangesByUser(userId);

      // Business requirement: Audit individual user activity
      expect(userChanges.length).toBe(2);
      expect(userChanges.every(c => c.changed_by === userId)).toBe(true);
      
      // Should span multiple entity types
      const entityTypes = userChanges.map(c => c.entity_type);
      expect(entityTypes).toContain('contact');
      expect(entityTypes).toContain('deal');
    });
  });

  describe('Business Rule: Integration Monitoring', () => {
    it('should track changes by source system for integration health', () => {
      const today = new Date().toISOString().split('T')[0];
      
      // Changes from different sources
      repository.logChange({
        entity_type: 'company',
        entity_id: 'company-1',
        field_name: 'website',
        old_value: 'old-site.com',
        new_value: 'new-site.com',
        changed_by: 'hubspot-integration',
        source: 'hubspot_sync',
      });
      
      repository.logChange({
        entity_type: 'contact',
        entity_id: 'contact-1',
        field_name: 'phone',
        old_value: null,
        new_value: '+**********',
        changed_by: 'hubspot-integration',
        source: 'hubspot_sync',
      });
      
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'deal-1',
        field_name: 'amount',
        old_value: '10000',
        new_value: '15000',
        changed_by: 'user-manual',
        source: 'manual',
      });

      const hubspotChanges = repository.getChangesBySource('hubspot_sync', {
        startDate: today + 'T00:00:00Z',
        endDate: today + 'T23:59:59Z',
      });

      // Business requirement: Monitor integration sync activity
      expect(hubspotChanges.length).toBe(2);
      expect(hubspotChanges.every(c => c.source === 'hubspot_sync')).toBe(true);
      
      // Should track different types of changes from the integration
      const fieldNames = hubspotChanges.map(c => c.field_name);
      expect(fieldNames).toContain('website');
      expect(fieldNames).toContain('phone');
    });
  });

  describe('Business Rule: Change Analytics', () => {
    it('should provide summary statistics for system health monitoring', () => {
      // Create a variety of changes
      const entities = [
        { type: 'company', count: 5 },
        { type: 'contact', count: 8 },
        { type: 'deal', count: 3 }
      ];
      
      for (const entity of entities) {
        for (let i = 0; i < entity.count; i++) {
          repository.logChange({
            entity_type: entity.type,
            entity_id: `${entity.type}-${i}`,
            field_name: 'status',
            old_value: 'old',
            new_value: 'new',
            changed_by: 'test-user',
            source: 'manual',
          });
        }
      }

      const summary = repository.getChangeSummary();

      // Business requirement: Understand system usage patterns
      expect(summary.length).toBeGreaterThanOrEqual(3);
      
      const companySummary = summary.find(s => s.entity_type === 'company');
      const contactSummary = summary.find(s => s.entity_type === 'contact');
      const dealSummary = summary.find(s => s.entity_type === 'deal');
      
      expect(companySummary?.total_changes).toBeGreaterThanOrEqual(5);
      expect(contactSummary?.total_changes).toBeGreaterThanOrEqual(8);
      expect(dealSummary?.total_changes).toBeGreaterThanOrEqual(3);
      
      // Each should have last_change timestamp
      expect(companySummary?.last_change).toBeDefined();
      expect(contactSummary?.last_change).toBeDefined();
      expect(dealSummary?.last_change).toBeDefined();
    });
  });

  describe('Business Rule: Data Retention Compliance', () => {
    it('should clean up old audit logs per retention policy', () => {
      // Create old changes (manually set dates in the past)
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 100); // 100 days ago
      
      // Insert old changes directly
      db.prepare(`
        INSERT INTO change_log (entity_type, entity_id, field_name, old_value, new_value, changed_by, source, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run('company', 'old-1', 'status', 'active', 'inactive', 'system', 'cleanup', oldDate.toISOString());
      
      db.prepare(`
        INSERT INTO change_log (entity_type, entity_id, field_name, old_value, new_value, changed_by, source, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run('contact', 'old-2', 'email', '<EMAIL>', '<EMAIL>', 'system', 'cleanup', oldDate.toISOString());
      
      // Create recent change
      repository.logChange({
        entity_type: 'deal',
        entity_id: 'recent-1',
        field_name: 'stage',
        old_value: 'new',
        new_value: 'qualified',
        changed_by: 'user',
        source: 'manual',
      });
      
      // Count before cleanup
      const beforeCount = db.prepare('SELECT COUNT(*) as count FROM change_log').get() as any;
      expect(beforeCount.count).toBeGreaterThanOrEqual(3);
      
      // Business requirement: Remove logs older than 90 days
      const deletedCount = repository.cleanupOldChanges(90);
      
      // Verify old changes were deleted
      expect(deletedCount).toBe(2);
      
      const afterCount = db.prepare('SELECT COUNT(*) as count FROM change_log').get() as any;
      expect(afterCount.count).toBe(beforeCount.count - 2);
      
      // Recent changes should remain
      const remaining = repository.getRecentChanges(10);
      expect(remaining.find(c => c.entity_id === 'recent-1')).toBeDefined();
    });
  });

  describe('Business Rule: Bulk Import Tracking', () => {
    it('should atomically log multiple changes from bulk operations', () => {
      const bulkChanges = [
        {
          entity_type: 'company',
          entity_id: 'company-bulk-1',
          field_name: 'name',
          old_value: 'Old Company Name',
          new_value: 'New Company Name',
          changed_by: 'csv-import',
          source: 'csv_import',
        },
        {
          entity_type: 'company',
          entity_id: 'company-bulk-2',
          field_name: 'industry',
          old_value: null,
          new_value: 'Technology',
          changed_by: 'csv-import',
          source: 'csv_import',
        },
        {
          entity_type: 'contact',
          entity_id: 'contact-bulk-1',
          field_name: 'email',
          old_value: '<EMAIL>',
          new_value: '<EMAIL>',
          changed_by: 'csv-import',
          source: 'csv_import',
        },
      ];

      // Business requirement: All or nothing for bulk imports
      repository.bulkLogChanges(bulkChanges);

      // Verify all changes were logged
      const importChanges = repository.getChangesBySource('csv_import', {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
      });
      
      expect(importChanges.length).toBeGreaterThanOrEqual(3);
      
      // All should be from the same import operation
      expect(importChanges.every(c => c.source === 'csv_import')).toBe(true);
      expect(importChanges.every(c => c.changed_by === 'csv-import')).toBe(true);
      
      // Should include different entity types
      const entityTypes = [...new Set(importChanges.map(c => c.entity_type))];
      expect(entityTypes).toContain('company');
      expect(entityTypes).toContain('contact');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid entity references gracefully', () => {
      // Business expectation: Log attempt even if entity doesn't exist
      const result = repository.logChange({
        entity_type: 'company',
        entity_id: 'non-existent',
        field_name: 'status',
        old_value: 'active',
        new_value: 'inactive',
        changed_by: 'system',
        source: 'manual',
      });
      
      // Should still create the log entry
      expect(result.id).toBeDefined();
      expect(result.entity_id).toBe('non-existent');
    });

    it('should handle very long field values', () => {
      const longValue = 'x'.repeat(1000);
      
      // Business expectation: Handle large text changes
      const result = repository.logChange({
        entity_type: 'company',
        entity_id: 'company-1',
        field_name: 'description',
        old_value: 'short',
        new_value: longValue,
        changed_by: 'user',
        source: 'manual',
      });
      
      expect(result.new_value).toBe(longValue);
    });
  });
});