import { Page, test, expect } from '@playwright/test';
import fs from 'fs';

/**
 * Waits for a page to be in a stable state (no network activity, animations completed)
 */
export async function waitForStableState(page: Page, timeout = 5000): Promise<boolean> {
  // Wait for network to be idle
  await page.waitForLoadState('networkidle', { timeout });
  
  // Wait for any animations to complete (give a small buffer)
  await page.waitForTimeout(500);
  
  return true;
}

/**
 * Checks if an element exists on the page
 */
export async function elementExists(page: Page, selector: string): Promise<boolean> {
  const count = await page.locator(selector).count();
  return count > 0;
}

/**
 * Gets page information for debugging
 */
export async function getPageInfo(page: Page): Promise<Record<string, unknown>> {
  return {
    url: page.url(),
    title: await page.title(),
    cookies: await page.context().cookies(),
    localStorageLength: await page.evaluate(() => Object.keys(localStorage).length),
  };
}

/**
 * Skip the application tour if it appears
 */
export async function skipTour(page: Page): Promise<void> {
  try {
    console.log('Looking for tour to skip...');
    
    // Look for Skip button with various possible texts
    const skipButton = page.locator('button:has-text("Skip"), button:has-text("Skip tour"), button:has-text("Close"), button[aria-label="Close tour"]');
    
    // Check if skip button is visible
    const isSkipVisible = await skipButton.isVisible({ timeout: 2000 }).catch(() => false);
    
    if (isSkipVisible) {
      console.log('Tour found, clicking Skip button...');
      await skipButton.click();
      console.log('Tour skipped successfully');
    } else {
      console.log('No tour found or Skip button not visible');
    }
    
    // Also try clicking on the floating tour button to dismiss it if present
    const tourButton = page.locator('#tour-button, button[aria-label="Take a tour"]');
    const isTourButtonVisible = await tourButton.isVisible({ timeout: 1000 }).catch(() => false);
    
    if (isTourButtonVisible) {
      console.log('Tour button found, will dismiss it...');
      // Click away from the tour button to dismiss any related overlays
      await page.mouse.click(10, 10);
    }
    
  } catch (e) {
    console.log('Error handling tour skip:', e);
    // We don't fail the test if we can't skip the tour, just continue
  }
}

/**
 * Verifies Xero authentication is valid
 * This should be called at the start of each test requiring authentication
 */
export async function verifyAuthentication(page: Page, timeout = 10000): Promise<void> {
  try {
    // Check auth state file exists
    if (!fs.existsSync('./auth.json')) {
      test.fail(true, 'Authentication state file not found. Please run npm run test:e2e:auth-setup first.');
      return;
    }
    
    // Check for authentication on the page by looking for elements that exist in the dashboard
    // First, check if the Xero connect button is NOT visible (which means we're authenticated)
    const connectButtonVisible = await page.locator('button:has-text("Connect to Xero")')
      .isVisible({ timeout: 5000 })
      .catch(() => false);
      
    if (connectButtonVisible) {
      test.fail(true, 'Not authenticated. The "Connect to Xero" button is visible. Please run npm run test:e2e:auth-setup again.');
      return;
    }
    
    // Next, check if dashboard elements are visible (navigation tabs, header)
    // We'll check for either tabs or some dashboard element that should be visible when authenticated
    const hasNavigation = await page.locator('button:has-text("Cashflow Projection"), button:has-text("Custom Expenses")')
      .isVisible({ timeout })
      .catch(() => false);
      
    // Also check for header to be safe
    const hasHeader = await page.locator('header:has-text("Onbord Dashboard")')
      .isVisible({ timeout })
      .catch(() => false);
      
    if (!hasNavigation && !hasHeader) {
      test.fail(true, 'Authentication appears invalid. Cannot find dashboard elements. Please run npm run test:e2e:auth-setup again.');
      return;
    }
    
    console.log('✅ Authentication verified successfully');
    
    // Skip tour right after authentication is verified
    await skipTour(page);
    
  } catch (e) {
    test.fail(true, 'Authentication verification failed. Please run npm run test:e2e:auth-setup again.');
    console.error('Authentication verification failed:', e);
    throw e;
  }
}

/**
 * Checks if user is logged in based on dashboard elements
 */
export async function isLoggedIn(page: Page): Promise<boolean> {
  // Check for dashboard elements that would only be visible when logged in
  return elementExists(page, '[data-testid="dashboard-authenticated"]');
}