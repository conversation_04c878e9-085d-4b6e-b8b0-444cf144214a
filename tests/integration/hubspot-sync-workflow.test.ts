import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeDatabase, closeDatabase } from '../../src/api/services/db-service';
import HubSpotService from '../../src/api/services/hubspot-service';
import CompanyRepository from '../../src/api/repositories/company-repository';
import ContactRepository from '../../src/api/repositories/contact-repository';
import DealRepository from '../../src/api/repositories/deal-repository';
import ActivityService from '../../src/api/services/activity-service';

// Mock external services
jest.mock('../../src/api/services/hubspot-service');
jest.mock('../../src/utils/backend-logger');

describe('HubSpot Sync Workflow Integration', () => {
  let app: any;
  let companyRepo: CompanyRepository;
  let contactRepo: ContactRepository;
  let dealRepo: DealRepository;
  let activityService: ActivityService;
  let mockHubSpotService: jest.Mocked<HubSpotService>;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    await initializeDatabase();
    
    // Import app after database initialization
    const { app: testApp } = await import('../../src/api/server');
    app = testApp;
    
    // Initialize repositories
    companyRepo = new CompanyRepository();
    contactRepo = new ContactRepository();
    dealRepo = new DealRepository();
    activityService = new ActivityService();
    
    mockHubSpotService = new HubSpotService() as jest.Mocked<HubSpotService>;
  });

  afterAll(async () => {
    await closeDatabase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Full Sync Workflow', () => {
    const mockHubSpotData = {
      companies: [
        {
          id: 'hs-comp-1',
          properties: {
            name: 'HubSpot Company 1',
            domain: 'company1.com',
            industry: 'Technology',
            annualrevenue: '1000000',
            numberofemployees: '50',
          },
        },
        {
          id: 'hs-comp-2',
          properties: {
            name: 'HubSpot Company 2',
            domain: 'company2.com',
            industry: 'Finance',
            annualrevenue: '5000000',
            numberofemployees: '200',
          },
        },
      ],
      contacts: [
        {
          id: 'hs-cont-1',
          properties: {
            firstname: 'John',
            lastname: 'Doe',
            email: '<EMAIL>',
            phone: '+1234567890',
            jobtitle: 'CEO',
          },
          associations: {
            companies: ['hs-comp-1'],
          },
        },
        {
          id: 'hs-cont-2',
          properties: {
            firstname: 'Jane',
            lastname: 'Smith',
            email: '<EMAIL>',
            jobtitle: 'CFO',
          },
          associations: {
            companies: ['hs-comp-2'],
          },
        },
      ],
      deals: [
        {
          id: 'hs-deal-1',
          properties: {
            dealname: 'Enterprise Deal',
            amount: '100000',
            dealstage: 'contractsent',
            closedate: '2024-03-31',
            hs_probability: '0.75',
          },
          associations: {
            companies: ['hs-comp-1'],
            contacts: ['hs-cont-1'],
          },
        },
      ],
    };

    it('should successfully import all HubSpot data', async () => {
      // Mock HubSpot service responses
      mockHubSpotService.getCompanies = jest.fn().mockResolvedValue(mockHubSpotData.companies);
      mockHubSpotService.getContacts = jest.fn().mockResolvedValue(mockHubSpotData.contacts);
      mockHubSpotService.getDeals = jest.fn().mockResolvedValue(mockHubSpotData.deals);

      // Start import
      const importResponse = await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies', 'contacts', 'deals'],
          fullSync: true,
        })
        .expect(200);

      expect(importResponse.body).toHaveProperty('importId');
      const { importId } = importResponse.body;

      // Simulate import processing
      await mockHubSpotService.processImport(importId);

      // Verify companies were imported
      const companies = await companyRepo.getAllCompanies();
      expect(companies.companies).toHaveLength(2);
      expect(companies.companies[0]).toMatchObject({
        name: 'HubSpot Company 1',
        website: 'company1.com',
        industry: 'Technology',
        annual_revenue: 1000000,
        employee_count: 50,
        hubspot_id: 'hs-comp-1',
      });

      // Verify contacts were imported
      const contacts = await contactRepo.getAllContacts();
      expect(contacts.contacts).toHaveLength(2);
      expect(contacts.contacts[0]).toMatchObject({
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        position: 'CEO',
        hubspot_id: 'hs-cont-1',
      });

      // Verify deals were imported
      const deals = await dealRepo.getAllDeals();
      expect(deals.deals).toHaveLength(1);
      expect(deals.deals[0]).toMatchObject({
        name: 'Enterprise Deal',
        amount: 100000,
        stage: 'contractsent',
        probability: 0.75,
        hubspot_id: 'hs-deal-1',
      });

      // Verify associations were created
      const companyContacts = await companyRepo.getCompanyContacts(companies.companies[0].id);
      expect(companyContacts).toHaveLength(1);
      expect(companyContacts[0].email).toBe('<EMAIL>');

      // Verify activity logs were created
      const activities = await activityService.getActivities({ limit: 10 });
      const importActivities = activities.activities.filter(a => a.action.includes('import'));
      expect(importActivities.length).toBeGreaterThan(0);
    });

    it('should handle incremental sync correctly', async () => {
      // First, create existing data
      const existingCompany = await companyRepo.createCompany({
        name: 'Existing Company',
        hubspot_id: 'hs-comp-1',
        website: 'old-domain.com',
      });

      // Mock updated data from HubSpot
      const updatedCompanyData = {
        id: 'hs-comp-1',
        properties: {
          name: 'Updated Company Name',
          domain: 'new-domain.com',
          industry: 'Updated Industry',
        },
      };

      mockHubSpotService.getCompanies = jest.fn().mockResolvedValue([updatedCompanyData]);
      mockHubSpotService.getContacts = jest.fn().mockResolvedValue([]);
      mockHubSpotService.getDeals = jest.fn().mockResolvedValue([]);

      // Perform incremental sync
      await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies'],
          fullSync: false,
        })
        .expect(200);

      // Verify company was updated, not duplicated
      const companies = await companyRepo.getAllCompanies();
      expect(companies.companies).toHaveLength(1);
      expect(companies.companies[0]).toMatchObject({
        id: existingCompany.id,
        name: 'Updated Company Name',
        website: 'new-domain.com',
        industry: 'Updated Industry',
        hubspot_id: 'hs-comp-1',
      });
    });

    it('should handle sync conflicts with field ownership', async () => {
      // Create a company with local modifications
      const company = await companyRepo.createCompany({
        name: 'Local Company Name',
        hubspot_id: 'hs-comp-1',
        website: 'local-domain.com',
      });

      // Mark website as locally owned
      await companyRepo.setFieldOwnership(company.id, 'website', 'local');

      // Mock HubSpot data with different website
      mockHubSpotService.getCompanies = jest.fn().mockResolvedValue([
        {
          id: 'hs-comp-1',
          properties: {
            name: 'HubSpot Company Name',
            domain: 'hubspot-domain.com',
          },
        },
      ]);

      // Perform sync
      await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies'],
          fullSync: false,
        })
        .expect(200);

      // Verify local field was preserved
      const updatedCompany = await companyRepo.getCompanyById(company.id);
      expect(updatedCompany).toMatchObject({
        name: 'HubSpot Company Name', // Updated from HubSpot
        website: 'local-domain.com', // Preserved local value
      });
    });

    it('should handle association updates', async () => {
      // Create initial data
      const company = await companyRepo.createCompany({
        name: 'Test Company',
        hubspot_id: 'hs-comp-1',
      });

      const contact1 = await contactRepo.createContact({
        first_name: 'Contact',
        last_name: 'One',
        email: '<EMAIL>',
        hubspot_id: 'hs-cont-1',
      });

      // Mock HubSpot data with new contact association
      mockHubSpotService.getContacts = jest.fn().mockResolvedValue([
        {
          id: 'hs-cont-1',
          properties: {
            firstname: 'Contact',
            lastname: 'One',
            email: '<EMAIL>',
          },
          associations: {
            companies: ['hs-comp-1'],
          },
        },
        {
          id: 'hs-cont-2',
          properties: {
            firstname: 'Contact',
            lastname: 'Two',
            email: '<EMAIL>',
          },
          associations: {
            companies: ['hs-comp-1'],
          },
        },
      ]);

      // Perform sync
      await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['contacts'],
          fullSync: false,
        })
        .expect(200);

      // Verify associations were updated
      const companyContacts = await companyRepo.getCompanyContacts(company.id);
      expect(companyContacts).toHaveLength(2);
      expect(companyContacts.map(c => c.email)).toContain('<EMAIL>');
    });
  });

  describe('Error Handling and Recovery', () => {
    it('should handle partial sync failures gracefully', async () => {
      // Mock partial failure
      mockHubSpotService.getCompanies = jest.fn().mockResolvedValue([
        {
          id: 'hs-comp-1',
          properties: { name: 'Good Company' },
        },
      ]);
      
      mockHubSpotService.getContacts = jest.fn().mockRejectedValue(
        new Error('HubSpot API Error')
      );

      // Attempt sync
      const response = await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies', 'contacts'],
          fullSync: false,
        })
        .expect(200);

      // Check import status
      const statusResponse = await request(app)
        .get(`/api/hubspot/import/${response.body.importId}`)
        .expect(200);

      expect(statusResponse.body).toMatchObject({
        status: 'partial_failure',
        progress: {
          companies: { processed: 1, errors: 0 },
          contacts: { processed: 0, errors: 1 },
        },
      });
    });

    it('should support import retry with resume capability', async () => {
      // Create partial import state
      const company1 = await companyRepo.createCompany({
        name: 'Already Imported',
        hubspot_id: 'hs-comp-1',
      });

      // Mock data including already imported and new items
      mockHubSpotService.getCompanies = jest.fn().mockResolvedValue([
        {
          id: 'hs-comp-1',
          properties: { name: 'Already Imported' },
        },
        {
          id: 'hs-comp-2',
          properties: { name: 'New Company' },
        },
      ]);

      // Resume import
      const response = await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies'],
          fullSync: false,
          resumeFrom: 'hs-comp-1',
        })
        .expect(200);

      // Verify only new items were processed
      const companies = await companyRepo.getAllCompanies();
      expect(companies.companies).toHaveLength(2);
      expect(companies.companies.find(c => c.hubspot_id === 'hs-comp-2')).toBeDefined();
    });
  });

  describe('Real-time Progress Updates', () => {
    it('should provide real-time import progress via SSE', async (done) => {
      // Start import
      const importResponse = await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies'],
          fullSync: true,
        })
        .expect(200);

      const { importId } = importResponse.body;

      // Connect to SSE endpoint
      const eventSource = request(app)
        .get(`/api/hubspot/import/${importId}/stream`)
        .set('Accept', 'text/event-stream');

      let progressEvents: any[] = [];

      eventSource.on('data', (data: Buffer) => {
        const event = JSON.parse(data.toString());
        progressEvents.push(event);

        if (event.status === 'completed') {
          expect(progressEvents.length).toBeGreaterThan(0);
          expect(progressEvents.some(e => e.entity === 'companies')).toBe(true);
          done();
        }
      });

      // Simulate import processing
      setTimeout(() => {
        mockHubSpotService.processImport(importId);
      }, 100);
    });
  });

  describe('Data Validation and Integrity', () => {
    it('should validate imported data before saving', async () => {
      // Mock invalid data from HubSpot
      mockHubSpotService.getCompanies = jest.fn().mockResolvedValue([
        {
          id: 'hs-comp-invalid',
          properties: {
            // Missing required field 'name'
            domain: 'invalid.com',
          },
        },
      ]);

      // Attempt import
      const response = await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['companies'],
          fullSync: false,
        })
        .expect(200);

      // Check that invalid record was not imported
      const companies = await companyRepo.getAllCompanies();
      expect(companies.companies.find(c => c.hubspot_id === 'hs-comp-invalid')).toBeUndefined();

      // Check error was logged
      const status = await request(app)
        .get(`/api/hubspot/import/${response.body.importId}`)
        .expect(200);

      expect(status.body.progress.companies.errors).toBeGreaterThan(0);
    });

    it('should maintain referential integrity', async () => {
      // Mock deal with non-existent company reference
      mockHubSpotService.getDeals = jest.fn().mockResolvedValue([
        {
          id: 'hs-deal-orphan',
          properties: {
            dealname: 'Orphan Deal',
            amount: '50000',
          },
          associations: {
            companies: ['hs-comp-nonexistent'],
          },
        },
      ]);

      // Import deal
      await request(app)
        .post('/api/hubspot/import')
        .send({
          entities: ['deals'],
          fullSync: false,
        })
        .expect(200);

      // Verify deal was created without invalid association
      const deals = await dealRepo.getAllDeals();
      const orphanDeal = deals.deals.find(d => d.hubspot_id === 'hs-deal-orphan');
      expect(orphanDeal).toBeDefined();
      expect(orphanDeal!.company_id).toBeNull();
    });
  });
});