import React from "react";
import { IconBadge } from "./IconBadge";
import type { IconBadgeVariantProps } from "../../utils/component-variants";

interface HubSpotBadgeProps extends Partial<IconBadgeVariantProps> {
  className?: string;
  /**
   * Click handler for interactive HubSpot badge
   */
  onClick?: () => void;
}

/**
 * HubSpot logo badge component with consistent styling and accessibility.
 *
 * Features:
 * - Uses official HubSpot logo SVG
 * - Consistent sizing with design system
 * - Proper accessibility attributes
 * - Optional interactive behavior
 * - Performance optimized loading
 *
 * @example
 * ```tsx
 * <HubSpotBadge size="lg" title="CRM data from HubSpot" />
 * ```
 */
const HubSpotBadge = ({
  className,
  size = "default",
  rounded = "default",
  onClick,
  ...props
}: HubSpotBadgeProps) => {
  return (
    <IconBadge
      src="/hubspot.svg"
      alt="HubSpot"
      title="From HubSpot"
      size={size}
      rounded={rounded}
      className={className}
      onClick={onClick}
      loading="eager" // HubSpot badges are frequently used, load immediately
      {...props}
    />
  );
};

export default HubSpotBadge;
