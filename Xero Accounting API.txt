Xero Accounting API
SDK Documentation

Accounting

createAccount

Creates a new chart of accounts


/Accounts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const account: Account = { 
  code: "123456",
  name: "<PERSON>oo<PERSON><PERSON>",
  type: AccountType.EXPENSE,
  description: "Hello World"
}; 

try {
  const response = await xero.accountingApi.createAccount(xeroTenantId, account, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
account *	
Account
Account object in body of request

Required
createAccountAttachmentByFileName

Creates an attachment on a specific account


/Accounts/{AccountID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createAccountAttachmentByFileName(xeroTenantId, accountID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createBankTransactionAttachmentByFileName

Creates an attachment for a specific bank transaction by filename


/BankTransactions/{BankTransactionID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createBankTransactionAttachmentByFileName(xeroTenantId, bankTransactionID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createBankTransactionHistoryRecord

Creates a history record for a specific bank transactions


/BankTransactions/{BankTransactionID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createBankTransactionHistoryRecord(xeroTenantId, bankTransactionID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createBankTransactions

Creates one or more spent or received money transaction


/BankTransactions
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const bankAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const bankTransaction: BankTransaction = { 
  type: BankTransaction.TypeEnum.RECEIVE,
  contact: contact,
  lineItems: lineItems,
  bankAccount: bankAccount
}; 

const bankTransactions: BankTransactions = {  
  bankTransactions: [bankTransaction]
}; 

try {
  const response = await xero.accountingApi.createBankTransactions(xeroTenantId, bankTransactions,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
bankTransactions *	
BankTransactions
BankTransactions with an array of BankTransaction objects in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

createBankTransfer

Creates a bank transfer


/BankTransfers
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const fromBankAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const toBankAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const bankTransfer: BankTransfer = { 
  fromBankAccount: fromBankAccount,
  toBankAccount: toBankAccount,
  amount: 1.0
}; 

const bankTransfers: BankTransfers = {  
  bankTransfers: [bankTransfer]
}; 

try {
  const response = await xero.accountingApi.createBankTransfer(xeroTenantId, bankTransfers, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
bankTransfers *	
BankTransfers
BankTransfers with array of BankTransfer objects in request body

Required
createBankTransferAttachmentByFileName


/BankTransfers/{BankTransferID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createBankTransferAttachmentByFileName(xeroTenantId, bankTransferID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createBankTransferHistoryRecord

Creates a history record for a specific bank transfer


/BankTransfers/{BankTransferID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createBankTransferHistoryRecord(xeroTenantId, bankTransferID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createBatchPayment

Creates one or many batch payments for invoices


/BatchPayments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const paymentAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const bankAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const invoice: Invoice = { 
  invoiceID: "********-0000-0000-0000-********0000"
}; 

const payment: Payment = { 
  account: bankAccount,
  date: currDate,
  amount: 1.0,
  invoice: invoice
};   
const payments = [];
payments.push(payment)

const batchPayment: BatchPayment = { 
  account: paymentAccount,
  reference: "hello foobar",
  date: currDate,
  payments: payments
}; 

const batchPayments: BatchPayments = {  
  batchPayments: [batchPayment]
}; 

try {
  const response = await xero.accountingApi.createBatchPayment(xeroTenantId, batchPayments,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
batchPayments *	
BatchPayments
BatchPayments with an array of Payments in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createBatchPaymentHistoryRecord

Creates a history record for a specific batch payment


/BatchPayments/{BatchPaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const batchPaymentID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createBatchPaymentHistoryRecord(xeroTenantId, batchPaymentID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
BatchPaymentID*	
UUID (uuid)
Unique identifier for BatchPayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createBrandingThemePaymentServices

Creates a new custom payment service for a specific branding theme


/BrandingThemes/{BrandingThemeID}/PaymentServices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const brandingThemeID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const paymentService: PaymentService = { 
  paymentServiceID: "********-0000-0000-0000-********0000",
  paymentServiceName: "ACME Payments",
  paymentServiceUrl: "https://www.payupnow.com/",
  payNowText: "Pay Now"
}; 

const paymentServices: PaymentServices = {  
  paymentServices: [paymentService]
}; 

try {
  const response = await xero.accountingApi.createBrandingThemePaymentServices(xeroTenantId, brandingThemeID, paymentServices, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

paymentservices	Grant read-write access to payment services
Parameters

Path parameters
Name	Description
BrandingThemeID*	
UUID (uuid)
Unique identifier for a Branding Theme

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
paymentServices *	
PaymentServices
PaymentServices array with PaymentService object in body of request

Required
createContactAttachmentByFileName


/Contacts/{ContactID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createContactAttachmentByFileName(xeroTenantId, contactID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createContactGroup

Creates a contact group


/ContactGroups
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const contactGroup: ContactGroup = { 
  name: "VIPs"
}; 

const contactGroups: ContactGroups = {  
  contactGroups: [contactGroup]
}; 

try {
  const response = await xero.accountingApi.createContactGroup(xeroTenantId, contactGroups, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
contactGroups *	
ContactGroups
ContactGroups with an array of names in request body

Required
createContactGroupContacts

Creates contacts to a specific contact group


/ContactGroups/{ContactGroupID}/Contacts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactGroupID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const contacts: Contacts = {  
  contacts: [contact]
}; 

try {
  const response = await xero.accountingApi.createContactGroupContacts(xeroTenantId, contactGroupID, contacts, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactGroupID*	
UUID (uuid)
Unique identifier for a Contact Group

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
contacts *	
Contacts
Contacts with array of contacts specifying the ContactID to be added to ContactGroup in body of request

Required
createContactHistory

Creates a new history record for a specific contact


/Contacts/{ContactID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createContactHistory(xeroTenantId, contactID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createContacts

Creates multiple contacts (bulk) in a Xero organisation


/Contacts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';

const phone: Phone = { 
  phoneNumber: "555-1212",
  phoneType: Phone.PhoneTypeEnum.MOBILE
};   
const phones = [];
phones.push(phone)

const contact: Contact = { 
  name: "Bruce Banner",
  emailAddress: "<EMAIL>",
  phones: phones
}; 

const contacts: Contacts = {  
  contacts: [contact]
}; 

try {
  const response = await xero.accountingApi.createContacts(xeroTenantId, contacts,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
contacts *	
Contacts
Contacts with an array of Contact objects to create in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createCreditNoteAllocation

Creates allocation for a specific credit note


/CreditNotes/{CreditNoteID}/Allocations
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const invoice: Invoice = { 
  invoiceID: "********-0000-0000-0000-********0000"
}; 

const allocation: Allocation = { 
  amount: 1.0,
  date: currDate,
  invoice: invoice
}; 

const allocations: Allocations = {  
  allocations: [allocation]
}; 

try {
  const response = await xero.accountingApi.createCreditNoteAllocation(xeroTenantId, creditNoteID, allocations,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
allocations *	
Allocations
Allocations with array of Allocation object in body of request.

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createCreditNoteAttachmentByFileName

Creates an attachment for a specific credit note


/CreditNotes/{CreditNoteID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const includeOnline = true;
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createCreditNoteAttachmentByFileName(xeroTenantId, creditNoteID, fileName, body,  includeOnline,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
Query parameters
Name	Description
IncludeOnline	
Boolean
Allows an attachment to be seen by the end customer within their online invoice

createCreditNoteHistory

Retrieves history records of a specific credit note


/CreditNotes/{CreditNoteID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createCreditNoteHistory(xeroTenantId, creditNoteID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createCreditNotes

Creates a new credit note


/CreditNotes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const creditNote: CreditNote = { 
  type: CreditNote.TypeEnum.ACCPAYCREDIT,
  contact: contact,
  date: currDate,
  lineItems: lineItems
}; 

const creditNotes: CreditNotes = {  
  creditNotes: [creditNote]
}; 

try {
  const response = await xero.accountingApi.createCreditNotes(xeroTenantId, creditNotes,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
creditNotes *	
CreditNotes
Credit Notes with array of CreditNote object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

createCurrency

Create a new currency for a Xero organisation


/Currencies
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const currency: Currency = { 
  code: CurrencyCode.USD,
  description: "United States Dollar"
}; 

try {
  const response = await xero.accountingApi.createCurrency(xeroTenantId, currency, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
currency *	
Currency
Currency object in the body of request

Required
createEmployees

Creates new employees used in Xero payrun


/Employees
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';

const employee: Employee = { 
  firstName: "Nick",
  lastName: "Fury"
}; 

const employees: Employees = {  
  employees: [employee]
}; 

try {
  const response = await xero.accountingApi.createEmployees(xeroTenantId, employees,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
employees *	
Employees
Employees with array of Employee object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createExpenseClaimHistory

Creates a history record for a specific expense claim


/ExpenseClaims/{ExpenseClaimID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const expenseClaimID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createExpenseClaimHistory(xeroTenantId, expenseClaimID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
ExpenseClaimID*	
UUID (uuid)
Unique identifier for a ExpenseClaim

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createExpenseClaims

Creates expense claims


/ExpenseClaims
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const user: User = { 
  userID: "********-0000-0000-0000-********0000"
}; 

const receipt: Receipt = { 
  receiptID: "********-0000-0000-0000-********0000",
  date: currDate
};   
const receipts = [];
receipts.push(receipt)

const expenseClaim: ExpenseClaim = { 
  status: ExpenseClaim.StatusEnum.SUBMITTED,
  user: user,
  receipts: receipts
}; 

const expenseClaims: ExpenseClaims = {  
  expenseClaims: [expenseClaim]
}; 

try {
  const response = await xero.accountingApi.createExpenseClaims(xeroTenantId, expenseClaims, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
expenseClaims *	
ExpenseClaims
ExpenseClaims with array of ExpenseClaim object in body of request

Required
createInvoiceAttachmentByFileName

Creates an attachment for a specific invoice or purchase bill by filename


/Invoices/{InvoiceID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const includeOnline = true;
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createInvoiceAttachmentByFileName(xeroTenantId, invoiceID, fileName, body,  includeOnline,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
Query parameters
Name	Description
IncludeOnline	
Boolean
Allows an attachment to be seen by the end customer within their online invoice

createInvoiceHistory

Creates a history record for a specific invoice


/Invoices/{InvoiceID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createInvoiceHistory(xeroTenantId, invoiceID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createInvoices

Creates one or more sales invoices or purchase bills


/Invoices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'
const dueDateValue = '2020-10-28'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItemTracking: LineItemTracking = { 
  trackingCategoryID: "********-0000-0000-0000-********0000",
  trackingOptionID: "********-0000-0000-0000-********0000"
};   
const lineItemTrackings = [];
lineItemTrackings.push(lineItemTracking)

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000",
  tracking: lineItemTrackings
};   
const lineItems = [];
lineItems.push(lineItem)

const invoice: Invoice = { 
  type: Invoice.TypeEnum.ACCREC,
  contact: contact,
  date: dateValue,
  dueDate: dueDateValue,
  lineItems: lineItems,
  reference: "Website Design",
  status: Invoice.StatusEnum.DRAFT
}; 

const invoices: Invoices = {  
  invoices: [invoice]
}; 

try {
  const response = await xero.accountingApi.createInvoices(xeroTenantId, invoices,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
invoices *	
Invoices
Invoices with an array of invoice objects in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

createItemHistory

Creates a history record for a specific item


/Items/{ItemID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const itemID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createItemHistory(xeroTenantId, itemID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
ItemID*	
UUID (uuid)
Unique identifier for an Item

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createItems

Creates one or more items


/Items
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const purchaseDetails: Purchase = { 
  cOGSAccountCode: "500"
}; 

const item: Item = { 
  code: "abcXYZ123",
  name: "HelloWorld",
  description: "Foobar",
  inventoryAssetAccountCode: "140",
  purchaseDetails: purchaseDetails
}; 

const items: Items = {  
  items: [item]
}; 

try {
  const response = await xero.accountingApi.createItems(xeroTenantId, items,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
items *	
Items
Items with an array of Item objects in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

createLinkedTransaction

Creates linked transactions (billable expenses)


/LinkedTransactions
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const linkedTransaction: LinkedTransaction = { 
  sourceTransactionID: "********-0000-0000-0000-********0000",
  sourceLineItemID: "********-0000-0000-0000-********0000"
}; 

try {
  const response = await xero.accountingApi.createLinkedTransaction(xeroTenantId, linkedTransaction, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
linkedTransaction *	
LinkedTransaction
LinkedTransaction object in body of request

Required
createManualJournalAttachmentByFileName

Creates a specific attachment for a specific manual journal by file name


/ManualJournals/{ManualJournalID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createManualJournalAttachmentByFileName(xeroTenantId, manualJournalID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createManualJournalHistoryRecord

Creates a history record for a specific manual journal


/ManualJournals/{ManualJournalID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createManualJournalHistoryRecord(xeroTenantId, manualJournalID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createManualJournals

Creates one or more manual journals


/ManualJournals
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'  
const manualJournalLines = [];

const credit: ManualJournalLine = { 
  lineAmount: -100.0,
  accountCode: "400",
  description: "Hello there"
}; 
manualJournalLines.push(credit)

const debit: ManualJournalLine = { 
  lineAmount: 100.0,
  accountCode: "120",
  description: "Hello there"
}; 
manualJournalLines.push(debit)

const manualJournal: ManualJournal = { 
  narration: "Foobar",
  date: dateValue,
  journalLines: manualJournalLines
}; 

const manualJournals: ManualJournals = {  
  manualJournals: [manualJournal]
}; 

try {
  const response = await xero.accountingApi.createManualJournals(xeroTenantId, manualJournals,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
manualJournals *	
ManualJournals
ManualJournals array with ManualJournal object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createOverpaymentAllocations

Creates a single allocation for a specific overpayment


/Overpayments/{OverpaymentID}/Allocations
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const overpaymentID = '********-0000-0000-0000-********0000';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const invoice: Invoice = { 
  invoiceID: "********-0000-0000-0000-********0000"
}; 

const allocation: Allocation = { 
  amount: 1.0,
  date: currDate,
  invoice: invoice
}; 

const allocations: Allocations = {  
  allocations: [allocation]
}; 

try {
  const response = await xero.accountingApi.createOverpaymentAllocations(xeroTenantId, overpaymentID, allocations,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
OverpaymentID*	
UUID (uuid)
Unique identifier for a Overpayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
allocations *	
Allocations
Allocations array with Allocation object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createOverpaymentHistory

Creates a history record for a specific overpayment


/Overpayments/{OverpaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const overpaymentID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createOverpaymentHistory(xeroTenantId, overpaymentID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
OverpaymentID*	
UUID (uuid)
Unique identifier for a Overpayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createPayment

Creates a single payment for invoice or credit notes


/Payments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const invoice: Invoice = { 
  invoiceID: "********-0000-0000-0000-********0000"
}; 

const account: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const payment: Payment = { 
  invoice: invoice,
  account: account,
  amount: 1.0,
  date: dateValue
}; 

const payments: Payments = {  
  payments: [payment]
}; 

try {
  const response = await xero.accountingApi.createPayment(xeroTenantId, payment, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
payment *	
Payment
Request body with a single Payment object

Required
createPaymentHistory

Creates a history record for a specific payment


/Payments/{PaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const paymentID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createPaymentHistory(xeroTenantId, paymentID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PaymentID*	
UUID (uuid)
Unique identifier for a Payment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createPaymentService

Creates a payment service


/PaymentServices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const paymentService: PaymentService = { 
  paymentServiceName: "ACME Payments",
  paymentServiceUrl: "https://www.payupnow.com/",
  payNowText: "Pay Now"
}; 

const paymentServices: PaymentServices = {  
  paymentServices: [paymentService]
}; 

try {
  const response = await xero.accountingApi.createPaymentService(xeroTenantId, paymentServices, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

paymentservices	Grant read-write access to payment services
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
paymentServices *	
PaymentServices
PaymentServices array with PaymentService object in body of request

Required
createPayments

Creates multiple payments for invoices or credit notes


/Payments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const invoice: Invoice = { 
  invoiceID: "********-0000-0000-0000-********0000"
}; 

const account: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const payment: Payment = { 
  invoice: invoice,
  account: account,
  amount: 1.0,
  date: dateValue
}; 

const payments: Payments = {  
  payments: [payment]
}; 

try {
  const response = await xero.accountingApi.createPayments(xeroTenantId, payments,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
payments *	
Payments
Payments array with Payment object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createPrepaymentAllocations

Allows you to create an Allocation for prepayments


/Prepayments/{PrepaymentID}/Allocations
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const prepaymentID = '********-0000-0000-0000-********0000';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const invoice: Invoice = { 
  invoiceID: "********-0000-0000-0000-********0000"
}; 

const allocation: Allocation = { 
  invoice: invoice,
  amount: 1.0,
  date: currDate
}; 

const allocations: Allocations = {  
  allocations: [allocation]
}; 

try {
  const response = await xero.accountingApi.createPrepaymentAllocations(xeroTenantId, prepaymentID, allocations,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PrepaymentID*	
UUID (uuid)
Unique identifier for a PrePayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
allocations *	
Allocations
Allocations with an array of Allocation object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createPrepaymentHistory

Creates a history record for a specific prepayment


/Prepayments/{PrepaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const prepaymentID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createPrepaymentHistory(xeroTenantId, prepaymentID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PrepaymentID*	
UUID (uuid)
Unique identifier for a PrePayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createPurchaseOrderAttachmentByFileName

Creates attachment for a specific purchase order


/PurchaseOrders/{PurchaseOrderID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createPurchaseOrderAttachmentByFileName(xeroTenantId, purchaseOrderID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createPurchaseOrderHistory

Creates a history record for a specific purchase orders


/PurchaseOrders/{PurchaseOrderID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createPurchaseOrderHistory(xeroTenantId, purchaseOrderID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createPurchaseOrders

Creates one or more purchase orders


/PurchaseOrders
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const purchaseOrder: PurchaseOrder = { 
  contact: contact,
  lineItems: lineItems,
  date: dateValue
}; 

const purchaseOrders: PurchaseOrders = {  
  purchaseOrders: [purchaseOrder]
}; 

try {
  const response = await xero.accountingApi.createPurchaseOrders(xeroTenantId, purchaseOrders,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
purchaseOrders *	
PurchaseOrders
PurchaseOrders with an array of PurchaseOrder object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createQuoteAttachmentByFileName

Creates attachment for a specific quote


/Quotes/{QuoteID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createQuoteAttachmentByFileName(xeroTenantId, quoteID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createQuoteHistory

Creates a history record for a specific quote


/Quotes/{QuoteID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createQuoteHistory(xeroTenantId, quoteID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createQuotes

Create one or more quotes


/Quotes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const quote: Quote = { 
  contact: contact,
  lineItems: lineItems,
  date: dateValue
}; 

const quotes: Quotes = {  
  quotes: [quote]
}; 

try {
  const response = await xero.accountingApi.createQuotes(xeroTenantId, quotes,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
quotes *	
Quotes
Quotes with an array of Quote object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createReceipt

Creates draft expense claim receipts for any user


/Receipts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const user: User = { 
  userID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const receipt: Receipt = { 
  contact: contact,
  user: user,
  lineItems: lineItems,
  lineAmountTypes: LineAmountTypes.Inclusive,
  status: Receipt.StatusEnum.DRAFT
}; 

const receipts: Receipts = {  
  receipts: [receipt]
}; 

try {
  const response = await xero.accountingApi.createReceipt(xeroTenantId, receipts,  unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
receipts *	
Receipts
Receipts with an array of Receipt object in body of request

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

createReceiptAttachmentByFileName

Creates an attachment on a specific expense claim receipts by file name


/Receipts/{ReceiptID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createReceiptAttachmentByFileName(xeroTenantId, receiptID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createReceiptHistory

Creates a history record for a specific receipt


/Receipts/{ReceiptID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createReceiptHistory(xeroTenantId, receiptID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createRepeatingInvoiceAttachmentByFileName

Creates an attachment from a specific repeating invoices by file name


/RepeatingInvoices/{RepeatingInvoiceID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createRepeatingInvoiceAttachmentByFileName(xeroTenantId, repeatingInvoiceID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
createRepeatingInvoiceHistory

Creates a history record for a specific repeating invoice


/RepeatingInvoices/{RepeatingInvoiceID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const historyRecord: HistoryRecord = { 
  details: "Hello World"
}; 

const historyRecords: HistoryRecords = {  
  historyRecords: [historyRecord]
}; 

try {
  const response = await xero.accountingApi.createRepeatingInvoiceHistory(xeroTenantId, repeatingInvoiceID, historyRecords, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
historyRecords *	
HistoryRecords
HistoryRecords containing an array of HistoryRecord objects in body of request

Required
createRepeatingInvoices

Creates one or more repeating invoice templates


/RepeatingInvoices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.createRepeatingInvoices(xeroTenantId, repeatingInvoices,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
repeatingInvoices *	
RepeatingInvoices
RepeatingInvoices with an array of repeating invoice objects in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

createTaxRates

Creates one or more tax rates


/TaxRates
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const taxComponent: TaxComponent = { 
  name: "State Tax",
  rate: 2.25
};   
const taxComponent = [];
taxComponents.push(taxComponent)

const taxRate: TaxRate = { 
  name: "CA State Tax",
  taxComponents: taxComponents,

const taxRates: TaxRates = {  
  taxRates: [taxRate]
}; 

try {
  const response = await xero.accountingApi.createTaxRates(xeroTenantId, taxRates, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
taxRates *	
TaxRates
TaxRates array with TaxRate object in body of request

Required
createTrackingCategory

Create tracking categories


/TrackingCategories
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const trackingCategory: TrackingCategory = { 
  name: "Foobar"
}; 

try {
  const response = await xero.accountingApi.createTrackingCategory(xeroTenantId, trackingCategory, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
trackingCategory *	
TrackingCategory
TrackingCategory object in body of request

Required
createTrackingOptions

Creates options for a specific tracking category


/TrackingCategories/{TrackingCategoryID}/Options
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const trackingCategoryID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const trackingOption: TrackingOption = { 
  name: "Foobar"
}; 

try {
  const response = await xero.accountingApi.createTrackingOptions(xeroTenantId, trackingCategoryID, trackingOption, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
TrackingCategoryID*	
UUID (uuid)
Unique identifier for a TrackingCategory

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
trackingOption *	
TrackingOption
TrackingOption object in body of request

Required
deleteAccount

Deletes a chart of accounts


/Accounts/{AccountID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteAccount(xeroTenantId, accountID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteBatchPayment

Updates a specific batch payment for invoices and credit notes


/BatchPayments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const batchPaymentDelete: BatchPaymentDelete = { 
  status: "DELETED"
}; 
  batchPaymentID: "********-0000-0000-0000-********0000"
}; 

try {
  const response = await xero.accountingApi.deleteBatchPayment(xeroTenantId, batchPaymentDelete, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
batchPaymentDelete *	
BatchPaymentDelete
Required
deleteBatchPaymentByUrlParam

Updates a specific batch payment for invoices and credit notes


/BatchPayments/{BatchPaymentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const batchPaymentID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.deleteBatchPaymentByUrlParam(xeroTenantId, batchPaymentID, batchPaymentDeleteByUrlParam, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
BatchPaymentID*	
UUID (uuid)
Unique identifier for BatchPayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
batchPaymentDeleteByUrlParam *	
BatchPaymentDeleteByUrlParam
Required
deleteContactGroupContact

Deletes a specific contact from a contact group using a unique contact Id


/ContactGroups/{ContactGroupID}/Contacts/{ContactID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactGroupID = '********-0000-0000-0000-********0000';
const contactID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteContactGroupContact(xeroTenantId, contactGroupID, contactID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactGroupID*	
UUID (uuid)
Unique identifier for a Contact Group

Required
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteContactGroupContacts

Deletes all contacts from a specific contact group


/ContactGroups/{ContactGroupID}/Contacts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactGroupID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteContactGroupContacts(xeroTenantId, contactGroupID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactGroupID*	
UUID (uuid)
Unique identifier for a Contact Group

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteCreditNoteAllocations

Deletes an Allocation from a Credit Note


/CreditNotes/{CreditNoteID}/Allocations/{AllocationID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const allocationID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteCreditNoteAllocations(xeroTenantId, creditNoteID, allocationID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
AllocationID*	
UUID (uuid)
Unique identifier for Allocation object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteItem

Deletes a specific item


/Items/{ItemID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const itemID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteItem(xeroTenantId, itemID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
ItemID*	
UUID (uuid)
Unique identifier for an Item

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteLinkedTransaction

Deletes a specific linked transactions (billable expenses)


/LinkedTransactions/{LinkedTransactionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const linkedTransactionID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteLinkedTransaction(xeroTenantId, linkedTransactionID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
LinkedTransactionID*	
UUID (uuid)
Unique identifier for a LinkedTransaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteOverpaymentAllocations

Deletes an Allocation from an overpayment


/Overpayments/{OverpaymentID}/Allocations/{AllocationID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const overpaymentID = '********-0000-0000-0000-********0000';
const allocationID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteOverpaymentAllocations(xeroTenantId, overpaymentID, allocationID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
OverpaymentID*	
UUID (uuid)
Unique identifier for a Overpayment

Required
AllocationID*	
UUID (uuid)
Unique identifier for Allocation object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deletePayment

Updates a specific payment for invoices and credit notes


/Payments/{PaymentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const paymentID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const paymentDelete: PaymentDelete = { 
  status: "DELETED"
}; 

try {
  const response = await xero.accountingApi.deletePayment(xeroTenantId, paymentID, paymentDelete, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PaymentID*	
UUID (uuid)
Unique identifier for a Payment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
paymentDelete *	
PaymentDelete
Required
deletePrepaymentAllocations

Deletes an Allocation from a Prepayment


/Prepayments/{PrepaymentID}/Allocations/{AllocationID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const prepaymentID = '********-0000-0000-0000-********0000';
const allocationID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deletePrepaymentAllocations(xeroTenantId, prepaymentID, allocationID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PrepaymentID*	
UUID (uuid)
Unique identifier for a PrePayment

Required
AllocationID*	
UUID (uuid)
Unique identifier for Allocation object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteTrackingCategory

Deletes a specific tracking category


/TrackingCategories/{TrackingCategoryID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const trackingCategoryID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteTrackingCategory(xeroTenantId, trackingCategoryID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
TrackingCategoryID*	
UUID (uuid)
Unique identifier for a TrackingCategory

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
deleteTrackingOptions

Deletes a specific option for a specific tracking category


/TrackingCategories/{TrackingCategoryID}/Options/{TrackingOptionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const trackingCategoryID = '********-0000-0000-0000-********0000';
const trackingOptionID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.deleteTrackingOptions(xeroTenantId, trackingCategoryID, trackingOptionID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
TrackingCategoryID*	
UUID (uuid)
Unique identifier for a TrackingCategory

Required
TrackingOptionID*	
UUID (uuid)
Unique identifier for a Tracking Option

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
emailInvoice

Sends a copy of a specific invoice to related contact via email


/Invoices/{InvoiceID}/Email
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const requestEmpty: RequestEmpty = { };

try {
  const response = await xero.accountingApi.emailInvoice(xeroTenantId, invoiceID, requestEmpty, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
requestEmpty *	
RequestEmpty
Required
getAccount

Retrieves a single chart of accounts by using a unique account Id


/Accounts/{AccountID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getAccount(xeroTenantId, accountID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getAccountAttachmentByFileName

Retrieves an attachment for a specific account by filename


/Accounts/{AccountID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getAccountAttachmentByFileName(xeroTenantId, accountID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getAccountAttachmentById

Retrieves a specific attachment from a specific account using a unique attachment Id


/Accounts/{AccountID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getAccountAttachmentById(xeroTenantId, accountID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getAccountAttachments

Retrieves attachments for a specific accounts by using a unique account Id


/Accounts/{AccountID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getAccountAttachments(xeroTenantId, accountID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getAccounts

Retrieves the full chart of accounts


/Accounts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="ACTIVE" AND Type=="BANK"';
const order = 'Name ASC';

try {
  const response = await xero.accountingApi.getAccounts(xeroTenantId, ifModifiedSince, where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getBankTransaction

Retrieves a single spent or received money transaction by using a unique bank transaction Id


/BankTransactions/{BankTransactionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getBankTransaction(xeroTenantId, bankTransactionID,  unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getBankTransactionAttachmentByFileName

Retrieves a specific attachment from a specific bank transaction by filename


/BankTransactions/{BankTransactionID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getBankTransactionAttachmentByFileName(xeroTenantId, bankTransactionID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getBankTransactionAttachmentById

Retrieves specific attachments from a specific BankTransaction using a unique attachment Id


/BankTransactions/{BankTransactionID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getBankTransactionAttachmentById(xeroTenantId, bankTransactionID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getBankTransactionAttachments

Retrieves any attachments from a specific bank transactions


/BankTransactions/{BankTransactionID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBankTransactionAttachments(xeroTenantId, bankTransactionID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBankTransactions

Retrieves any spent or received money transactions


/BankTransactions
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="AUTHORISED"';
const order = 'Type ASC';
const page = 1;
const unitdp = 4;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getBankTransactions(xeroTenantId, ifModifiedSince, where, order, page, unitdp, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

page	
Integer
Up to 100 bank transactions will be returned in a single API call with line items details

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

pageSize	
Integer
Number of records to retrieve per page

getBankTransactionsHistory

Retrieves history from a specific bank transaction using a unique bank transaction Id


/BankTransactions/{BankTransactionID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBankTransactionsHistory(xeroTenantId, bankTransactionID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBankTransfer

Retrieves specific bank transfers by using a unique bank transfer Id


/BankTransfers/{BankTransferID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBankTransfer(xeroTenantId, bankTransferID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBankTransferAttachmentByFileName

Retrieves a specific attachment on a specific bank transfer by file name


/BankTransfers/{BankTransferID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getBankTransferAttachmentByFileName(xeroTenantId, bankTransferID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getBankTransferAttachmentById

Retrieves a specific attachment from a specific bank transfer using a unique attachment ID


/BankTransfers/{BankTransferID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getBankTransferAttachmentById(xeroTenantId, bankTransferID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getBankTransferAttachments

Retrieves attachments from a specific bank transfer


/BankTransfers/{BankTransferID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBankTransferAttachments(xeroTenantId, bankTransferID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBankTransferHistory

Retrieves history from a specific bank transfer using a unique bank transfer Id


/BankTransfers/{BankTransferID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBankTransferHistory(xeroTenantId, bankTransferID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBankTransfers

Retrieves all bank transfers


/BankTransfers
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'HasAttachments==true';
const order = 'Amount ASC';

try {
  const response = await xero.accountingApi.getBankTransfers(xeroTenantId, ifModifiedSince, where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getBatchPayment

Retrieves a specific batch payment using a unique batch payment Id


/BatchPayments/{BatchPaymentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const batchPaymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBatchPayment(xeroTenantId, batchPaymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
BatchPaymentID*	
UUID (uuid)
Unique identifier for BatchPayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBatchPaymentHistory

Retrieves history from a specific batch payment


/BatchPayments/{BatchPaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const batchPaymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBatchPaymentHistory(xeroTenantId, batchPaymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
BatchPaymentID*	
UUID (uuid)
Unique identifier for BatchPayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBatchPayments

Retrieves either one or many batch payments for invoices


/BatchPayments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="AUTHORISED"';
const order = 'Date ASC';

try {
  const response = await xero.accountingApi.getBatchPayments(xeroTenantId, ifModifiedSince, where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getBrandingTheme

Retrieves a specific branding theme using a unique branding theme Id


/BrandingThemes/{BrandingThemeID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const brandingThemeID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBrandingTheme(xeroTenantId, brandingThemeID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
BrandingThemeID*	
UUID (uuid)
Unique identifier for a Branding Theme

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBrandingThemePaymentServices

Retrieves the payment services for a specific branding theme


/BrandingThemes/{BrandingThemeID}/PaymentServices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const brandingThemeID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getBrandingThemePaymentServices(xeroTenantId, brandingThemeID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

paymentservices	Grant read-write access to payment services
Parameters

Path parameters
Name	Description
BrandingThemeID*	
UUID (uuid)
Unique identifier for a Branding Theme

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBrandingThemes

Retrieves all the branding themes


/BrandingThemes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';

try {
  const response = await xero.accountingApi.getBrandingThemes(xeroTenantId);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getBudget

Retrieves a specific budget, which includes budget lines


/Budgets/{BudgetID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const budgetID = '********-0000-0000-0000-********0000';
const dateTo: Date = new Date("2019-10-31");
const dateFrom: Date = new Date("2019-10-31");

try {
  const response = await xero.accountingApi.getBudget(xeroTenantId, budgetID,  dateTo, dateFrom);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.budgets.read	
Parameters

Path parameters
Name	Description
BudgetID*	
UUID (uuid)
Unique identifier for Budgets

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
DateTo	
date (date)
Filter by start date

DateFrom	
date (date)
Filter by end date

getBudgets

Retrieve a list of budgets


/Budgets
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const iDs = ["********-0000-0000-0000-********0000"];
const dateTo: Date = new Date("2019-10-31");
const dateFrom: Date = new Date("2019-10-31");

try {
  const response = await xero.accountingApi.getBudgets(xeroTenantId,  iDs, dateTo, dateFrom);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.budgets.read	
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
IDs	
array[UUID] (uuid)
Filter by BudgetID. Allows you to retrieve a specific individual budget.

DateTo	
date (date)
Filter by start date

DateFrom	
date (date)
Filter by end date

getContact

Retrieves a specific contacts in a Xero organisation using a unique contact Id


/Contacts/{ContactID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getContact(xeroTenantId, contactID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
accounting.contacts.read	Grant read-only access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getContactAttachmentByFileName

Retrieves a specific attachment from a specific contact by file name


/Contacts/{ContactID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getContactAttachmentByFileName(xeroTenantId, contactID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getContactAttachmentById

Retrieves a specific attachment from a specific contact using a unique attachment Id


/Contacts/{ContactID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getContactAttachmentById(xeroTenantId, contactID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getContactAttachments

Retrieves attachments for a specific contact in a Xero organisation


/Contacts/{ContactID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getContactAttachments(xeroTenantId, contactID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getContactByContactNumber

Retrieves a specific contact by contact number in a Xero organisation


/Contacts/{ContactNumber}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactNumber = 'SB2';

try {
  const response = await xero.accountingApi.getContactByContactNumber(xeroTenantId, contactNumber);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
accounting.contacts.read	Grant read-only access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactNumber*	
String
This field is read only on the Xero contact screen, used to identify contacts in external systems (max length = 50).

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getContactCISSettings

Retrieves CIS settings for a specific contact in a Xero organisation


/Contacts/{ContactID}/CISSettings
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getContactCISSettings(xeroTenantId, contactID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getContactGroup

Retrieves a specific contact group by using a unique contact group Id


/ContactGroups/{ContactGroupID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactGroupID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getContactGroup(xeroTenantId, contactGroupID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
accounting.contacts.read	Grant read-only access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactGroupID*	
UUID (uuid)
Unique identifier for a Contact Group

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getContactGroups

Retrieves the contact Id and name of each contact group


/ContactGroups
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const where = 'Status=="ACTIVE"';
const order = 'Name ASC';

try {
  const response = await xero.accountingApi.getContactGroups(xeroTenantId,  where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
accounting.contacts.read	Grant read-only access to contacts and contact groups
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getContactHistory

Retrieves history records for a specific contact


/Contacts/{ContactID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getContactHistory(xeroTenantId, contactID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
accounting.contacts.read	Grant read-only access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getContacts

Retrieves all contacts in a Xero organisation


/Contacts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'ContactStatus=="ACTIVE"';
const order = 'Name ASC';
const iDs = ["********-0000-0000-0000-********0000"];
const page = 1;
const includeArchived = true;
const summaryOnly = true;
const searchTerm = 'Joe Bloggs';
const pageSize = 100;

try {
  const response = await xero.accountingApi.getContacts(xeroTenantId, ifModifiedSince, where, order, iDs, page, includeArchived, summaryOnly, searchTerm, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
accounting.contacts.read	Grant read-only access to contacts and contact groups
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

IDs	
array[UUID] (uuid)
Filter by a comma separated list of ContactIDs. Allows you to retrieve a specific set of contacts in a single call.

page	
Integer
e.g. page=1 - Up to 100 contacts will be returned in a single API call.

includeArchived	
Boolean
e.g. includeArchived=true - Contacts with a status of ARCHIVED will be included in the response

summaryOnly	
Boolean
Use summaryOnly=true in GET Contacts and Invoices endpoint to retrieve a smaller version of the response object. This returns only lightweight fields, excluding computation-heavy fields from the response, making the API calls quick and efficient.

searchTerm	
String
Search parameter that performs a case-insensitive text search across the Name, FirstName, LastName, ContactNumber and EmailAddress fields.

pageSize	
Integer
Number of records to retrieve per page

getCreditNote

Retrieves a specific credit note using a unique credit note Id


/CreditNotes/{CreditNoteID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getCreditNote(xeroTenantId, creditNoteID,  unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getCreditNoteAsPdf

Retrieves credit notes as PDF files


/CreditNotes/{CreditNoteID}/pdf
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getCreditNoteAsPdf(xeroTenantId, creditNoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getCreditNoteAttachmentByFileName

Retrieves a specific attachment on a specific credit note by file name


/CreditNotes/{CreditNoteID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getCreditNoteAttachmentByFileName(xeroTenantId, creditNoteID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getCreditNoteAttachmentById

Retrieves a specific attachment from a specific credit note using a unique attachment Id


/CreditNotes/{CreditNoteID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getCreditNoteAttachmentById(xeroTenantId, creditNoteID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getCreditNoteAttachments

Retrieves attachments for a specific credit notes


/CreditNotes/{CreditNoteID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getCreditNoteAttachments(xeroTenantId, creditNoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getCreditNoteHistory

Retrieves history records of a specific credit note


/CreditNotes/{CreditNoteID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getCreditNoteHistory(xeroTenantId, creditNoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getCreditNotes

Retrieves any credit notes


/CreditNotes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="DRAFT"';
const order = 'CreditNoteNumber ASC';
const page = 1;
const unitdp = 4;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getCreditNotes(xeroTenantId, ifModifiedSince, where, order, page, unitdp, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

page	
Integer
e.g. page=1 – Up to 100 credit notes will be returned in a single API call with line items shown for each credit note

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

pageSize	
Integer
Number of records to retrieve per page

getCurrencies

Retrieves currencies for your Xero organisation


/Currencies
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const where = 'Code=="USD"';
const order = 'Code ASC';

try {
  const response = await xero.accountingApi.getCurrencies(xeroTenantId,  where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getEmployee

Retrieves a specific employee used in Xero payrun using a unique employee Id


/Employees/{EmployeeID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const employeeID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getEmployee(xeroTenantId, employeeID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
EmployeeID*	
UUID (uuid)
Unique identifier for a Employee

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getEmployees

Retrieves employees used in Xero payrun


/Employees
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="ACTIVE"';
const order = 'LastName ASC';

try {
  const response = await xero.accountingApi.getEmployees(xeroTenantId, ifModifiedSince, where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getExpenseClaim

Retrieves a specific expense claim using a unique expense claim Id


/ExpenseClaims/{ExpenseClaimID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const expenseClaimID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getExpenseClaim(xeroTenantId, expenseClaimID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
ExpenseClaimID*	
UUID (uuid)
Unique identifier for a ExpenseClaim

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getExpenseClaimHistory

Retrieves history records of a specific expense claim


/ExpenseClaims/{ExpenseClaimID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const expenseClaimID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getExpenseClaimHistory(xeroTenantId, expenseClaimID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
ExpenseClaimID*	
UUID (uuid)
Unique identifier for a ExpenseClaim

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getExpenseClaims

Retrieves expense claims


/ExpenseClaims
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="SUBMITTED"';
const order = 'Status ASC';

try {
  const response = await xero.accountingApi.getExpenseClaims(xeroTenantId, ifModifiedSince, where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getInvoice

Retrieves a specific sales invoice or purchase bill using a unique invoice Id


/Invoices/{InvoiceID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getInvoice(xeroTenantId, invoiceID,  unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getInvoiceAsPdf

Retrieves invoices or purchase bills as PDF files


/Invoices/{InvoiceID}/pdf
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getInvoiceAsPdf(xeroTenantId, invoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getInvoiceAttachmentByFileName

Retrieves an attachment from a specific invoice or purchase bill by filename


/Invoices/{InvoiceID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getInvoiceAttachmentByFileName(xeroTenantId, invoiceID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getInvoiceAttachmentById

Retrieves a specific attachment from a specific invoices or purchase bills by using a unique attachment Id


/Invoices/{InvoiceID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getInvoiceAttachmentById(xeroTenantId, invoiceID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getInvoiceAttachments

Retrieves attachments for a specific invoice or purchase bill


/Invoices/{InvoiceID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getInvoiceAttachments(xeroTenantId, invoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getInvoiceHistory

Retrieves history records for a specific invoice


/Invoices/{InvoiceID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getInvoiceHistory(xeroTenantId, invoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getInvoiceReminders

Retrieves invoice reminder settings


/InvoiceReminders/Settings
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';

try {
  const response = await xero.accountingApi.getInvoiceReminders(xeroTenantId);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getInvoices

Retrieves sales invoices or purchase bills


/Invoices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="DRAFT"';
const order = 'InvoiceNumber ASC';
const iDs = ["********-0000-0000-0000-********0000"];
const invoiceNumbers = ["INV-001", "INV-002"];
const contactIDs = ["********-0000-0000-0000-********0000"];
const statuses = ["DRAFT", "SUBMITTED"];
const page = 1;
const includeArchived = true;
const createdByMyApp = false;
const unitdp = 4;
const summaryOnly = true;
const pageSize = 100;
const searchTerm = 'SearchTerm=REF12';

try {
  const response = await xero.accountingApi.getInvoices(xeroTenantId, ifModifiedSince, where, order, iDs, invoiceNumbers, contactIDs, statuses, page, includeArchived, createdByMyApp, unitdp, summaryOnly, pageSize, searchTerm);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

IDs	
array[UUID] (uuid)
Filter by a comma-separated list of InvoicesIDs.

InvoiceNumbers	
array[String]
Filter by a comma-separated list of InvoiceNumbers.

ContactIDs	
array[UUID] (uuid)
Filter by a comma-separated list of ContactIDs.

Statuses	
array[String]
Filter by a comma-separated list Statuses. For faster response times we recommend using these explicit parameters instead of passing OR conditions into the Where filter.

page	
Integer
e.g. page=1 – Up to 100 invoices will be returned in a single API call with line items shown for each invoice

includeArchived	
Boolean
e.g. includeArchived=true - Invoices with a status of ARCHIVED will be included in the response

createdByMyApp	
Boolean
When set to true you'll only retrieve Invoices created by your app

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

summaryOnly	
Boolean
Use summaryOnly=true in GET Contacts and Invoices endpoint to retrieve a smaller version of the response object. This returns only lightweight fields, excluding computation-heavy fields from the response, making the API calls quick and efficient.

pageSize	
Integer
Number of records to retrieve per page

searchTerm	
String
Search parameter that performs a case-insensitive text search across the fields e.g. InvoiceNumber, Reference.

getItem

Retrieves a specific item using a unique item Id


/Items/{ItemID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const itemID = '********-0000-0000-0000-********0000';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getItem(xeroTenantId, itemID,  unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
ItemID*	
UUID (uuid)
Unique identifier for an Item

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getItemHistory

Retrieves history for a specific item


/Items/{ItemID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const itemID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getItemHistory(xeroTenantId, itemID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
ItemID*	
UUID (uuid)
Unique identifier for an Item

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getItems

Retrieves items


/Items
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'IsSold==true';
const order = 'Code ASC';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getItems(xeroTenantId, ifModifiedSince, where, order, unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getJournal

Retrieves a specific journal using a unique journal Id.


/Journals/{JournalID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const journalID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getJournal(xeroTenantId, journalID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.journals.read	Grant read-only access to journals
Parameters

Path parameters
Name	Description
JournalID*	
UUID (uuid)
Unique identifier for a Journal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getJournalByNumber

Retrieves a specific journal using a unique journal number.


/Journals/{JournalNumber}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const journalNumber = 1000;

try {
  const response = await xero.accountingApi.getJournalByNumber(xeroTenantId, journalNumber);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.journals.read	Grant read-only access to journals
Parameters

Path parameters
Name	Description
JournalNumber*	
Integer
Number of a Journal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getJournals

Retrieves journals


/Journals
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const offset = 10;
const paymentsOnly = true;

try {
  const response = await xero.accountingApi.getJournals(xeroTenantId, ifModifiedSince, offset, paymentsOnly);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.journals.read	Grant read-only access to journals
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
offset	
Integer
Offset by a specified journal number. e.g. journals with a JournalNumber greater than the offset will be returned

paymentsOnly	
Boolean
Filter to retrieve journals on a cash basis. Journals are returned on an accrual basis by default.

getLinkedTransaction

Retrieves a specific linked transaction (billable expenses) using a unique linked transaction Id


/LinkedTransactions/{LinkedTransactionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const linkedTransactionID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getLinkedTransaction(xeroTenantId, linkedTransactionID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
LinkedTransactionID*	
UUID (uuid)
Unique identifier for a LinkedTransaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getLinkedTransactions

Retrieves linked transactions (billable expenses)


/LinkedTransactions
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const page = 1;
const linkedTransactionID = '********-0000-0000-0000-********0000';
const sourceTransactionID = '********-0000-0000-0000-********0000';
const contactID = '********-0000-0000-0000-********0000';
const status = 'APPROVED';
const targetTransactionID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getLinkedTransactions(xeroTenantId,  page, linkedTransactionID, sourceTransactionID, contactID, status, targetTransactionID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
page	
Integer
Up to 100 linked transactions will be returned in a single API call. Use the page parameter to specify the page to be returned e.g. page=1.

LinkedTransactionID	
UUID (uuid)
The Xero identifier for an Linked Transaction

SourceTransactionID	
UUID (uuid)
Filter by the SourceTransactionID. Get the linked transactions created from a particular ACCPAY invoice

ContactID	
UUID (uuid)
Filter by the ContactID. Get all the linked transactions that have been assigned to a particular customer.

Status	
String
Filter by the combination of ContactID and Status. Get the linked transactions associated to a customer and with a status

TargetTransactionID	
UUID (uuid)
Filter by the TargetTransactionID. Get all the linked transactions allocated to a particular ACCREC invoice

getManualJournal

Retrieves a specific manual journal


/ManualJournals/{ManualJournalID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getManualJournal(xeroTenantId, manualJournalID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getManualJournalAttachmentByFileName

Retrieves a specific attachment from a specific manual journal by file name


/ManualJournals/{ManualJournalID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getManualJournalAttachmentByFileName(xeroTenantId, manualJournalID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getManualJournalAttachmentById

Allows you to retrieve a specific attachment from a specific manual journal using a unique attachment Id


/ManualJournals/{ManualJournalID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getManualJournalAttachmentById(xeroTenantId, manualJournalID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getManualJournalAttachments

Retrieves attachment for a specific manual journal


/ManualJournals/{ManualJournalID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getManualJournalAttachments(xeroTenantId, manualJournalID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getManualJournals

Retrieves manual journals


/ManualJournals
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="DRAFT"';
const order = 'Date ASC';
const page = 1;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getManualJournals(xeroTenantId, ifModifiedSince, where, order, page, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

page	
Integer
e.g. page=1 – Up to 100 manual journals will be returned in a single API call with line items shown for each overpayment

pageSize	
Integer
Number of records to retrieve per page

getManualJournalsHistory

Retrieves history for a specific manual journal


/ManualJournals/{ManualJournalID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getManualJournalsHistory(xeroTenantId, manualJournalID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOnlineInvoice

Retrieves a URL to an online invoice


/Invoices/{InvoiceID}/OnlineInvoice
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getOnlineInvoice(xeroTenantId, invoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOrganisationActions

Retrieves a list of the key actions your app has permission to perform in the connected Xero organisation.


/Organisation/Actions
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';

try {
  const response = await xero.accountingApi.getOrganisationActions(xeroTenantId);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOrganisationCISSettings

Retrieves the CIS settings for the Xero organistaion.


/Organisation/{OrganisationID}/CISSettings
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const organisationID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getOrganisationCISSettings(xeroTenantId, organisationID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
OrganisationID*	
UUID (uuid)
The unique Xero identifier for an organisation

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOrganisations

Retrieves Xero organisation details


/Organisation
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';

try {
  const response = await xero.accountingApi.getOrganisations(xeroTenantId);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOverpayment

Retrieves a specific overpayment using a unique overpayment Id


/Overpayments/{OverpaymentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const overpaymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getOverpayment(xeroTenantId, overpaymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
OverpaymentID*	
UUID (uuid)
Unique identifier for a Overpayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOverpaymentHistory

Retrieves history records of a specific overpayment


/Overpayments/{OverpaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const overpaymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getOverpaymentHistory(xeroTenantId, overpaymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
OverpaymentID*	
UUID (uuid)
Unique identifier for a Overpayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getOverpayments

Retrieves overpayments


/Overpayments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="AUTHORISED"';
const order = 'Status ASC';
const page = 1;
const unitdp = 4;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getOverpayments(xeroTenantId, ifModifiedSince, where, order, page, unitdp, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

page	
Integer
e.g. page=1 – Up to 100 overpayments will be returned in a single API call with line items shown for each overpayment

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

pageSize	
Integer
Number of records to retrieve per page

getPayment

Retrieves a specific payment for invoices and credit notes using a unique payment Id


/Payments/{PaymentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const paymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPayment(xeroTenantId, paymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PaymentID*	
UUID (uuid)
Unique identifier for a Payment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPaymentHistory

Retrieves history records of a specific payment


/Payments/{PaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const paymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPaymentHistory(xeroTenantId, paymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PaymentID*	
UUID (uuid)
Unique identifier for a Payment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPaymentServices

Retrieves payment services


/PaymentServices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';

try {
  const response = await xero.accountingApi.getPaymentServices(xeroTenantId);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

paymentservices	Grant read-write access to payment services
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPayments

Retrieves payments for invoices and credit notes


/Payments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="AUTHORISED"';
const order = 'Amount ASC';
const page = 1;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getPayments(xeroTenantId, ifModifiedSince, where, order, page, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

page	
Integer
Up to 100 payments will be returned in a single API call

pageSize	
Integer
Number of records to retrieve per page

getPrepayment

Allows you to retrieve a specified prepayments


/Prepayments/{PrepaymentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const prepaymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPrepayment(xeroTenantId, prepaymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PrepaymentID*	
UUID (uuid)
Unique identifier for a PrePayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPrepaymentHistory

Retrieves history record for a specific prepayment


/Prepayments/{PrepaymentID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const prepaymentID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPrepaymentHistory(xeroTenantId, prepaymentID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PrepaymentID*	
UUID (uuid)
Unique identifier for a PrePayment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPrepayments

Retrieves prepayments


/Prepayments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="AUTHORISED"';
const order = 'Reference ASC';
const page = 1;
const unitdp = 4;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getPrepayments(xeroTenantId, ifModifiedSince, where, order, page, unitdp, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

page	
Integer
e.g. page=1 – Up to 100 prepayments will be returned in a single API call with line items shown for each overpayment

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

pageSize	
Integer
Number of records to retrieve per page

getPurchaseOrder

Retrieves a specific purchase order using a unique purchase order Id


/PurchaseOrders/{PurchaseOrderID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPurchaseOrder(xeroTenantId, purchaseOrderID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPurchaseOrderAsPdf

Retrieves specific purchase order as PDF files using a unique purchase order Id


/PurchaseOrders/{PurchaseOrderID}/pdf
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPurchaseOrderAsPdf(xeroTenantId, purchaseOrderID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPurchaseOrderAttachmentByFileName

Retrieves a specific attachment for a specific purchase order by filename


/PurchaseOrders/{PurchaseOrderID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getPurchaseOrderAttachmentByFileName(xeroTenantId, purchaseOrderID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getPurchaseOrderAttachmentById

Retrieves specific attachment for a specific purchase order using a unique attachment Id


/PurchaseOrders/{PurchaseOrderID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getPurchaseOrderAttachmentById(xeroTenantId, purchaseOrderID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getPurchaseOrderAttachments

Retrieves attachments for a specific purchase order


/PurchaseOrders/{PurchaseOrderID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPurchaseOrderAttachments(xeroTenantId, purchaseOrderID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPurchaseOrderByNumber

Retrieves a specific purchase order using purchase order number


/PurchaseOrders/{PurchaseOrderNumber}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderNumber = 'PO1234';

try {
  const response = await xero.accountingApi.getPurchaseOrderByNumber(xeroTenantId, purchaseOrderNumber);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PurchaseOrderNumber*	
String
Unique identifier for a PurchaseOrder

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPurchaseOrderHistory

Retrieves history for a specific purchase order


/PurchaseOrders/{PurchaseOrderID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getPurchaseOrderHistory(xeroTenantId, purchaseOrderID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getPurchaseOrders

Retrieves purchase orders


/PurchaseOrders
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const status = 'SUBMITTED';
const dateFrom = '2019-12-01';
const dateTo = '2019-12-31';
const order = 'PurchaseOrderNumber ASC';
const page = 1;
const pageSize = 100;

try {
  const response = await xero.accountingApi.getPurchaseOrders(xeroTenantId, ifModifiedSince, status, dateFrom, dateTo, order, page, pageSize);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
Status	
String
Filter by purchase order status

DateFrom	
String
Filter by purchase order date (e.g. GET https://.../PurchaseOrders?DateFrom=2015-12-01&DateTo=2015-12-31

DateTo	
String
Filter by purchase order date (e.g. GET https://.../PurchaseOrders?DateFrom=2015-12-01&DateTo=2015-12-31

order	
String
Order by an any element

page	
Integer
To specify a page, append the page parameter to the URL e.g. ?page=1. If there are 100 records in the response you will need to check if there is any more data by fetching the next page e.g ?page=2 and continuing this process until no more results are returned.

pageSize	
Integer
Number of records to retrieve per page

getQuote

Retrieves a specific quote using a unique quote Id


/Quotes/{QuoteID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getQuote(xeroTenantId, quoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getQuoteAsPdf

Retrieves a specific quote as a PDF file using a unique quote Id


/Quotes/{QuoteID}/pdf
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getQuoteAsPdf(xeroTenantId, quoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getQuoteAttachmentByFileName

Retrieves a specific attachment from a specific quote by filename


/Quotes/{QuoteID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getQuoteAttachmentByFileName(xeroTenantId, quoteID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getQuoteAttachmentById

Retrieves a specific attachment from a specific quote using a unique attachment Id


/Quotes/{QuoteID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getQuoteAttachmentById(xeroTenantId, quoteID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getQuoteAttachments

Retrieves attachments for a specific quote


/Quotes/{QuoteID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getQuoteAttachments(xeroTenantId, quoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getQuoteHistory

Retrieves history records of a specific quote


/Quotes/{QuoteID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getQuoteHistory(xeroTenantId, quoteID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getQuotes

Retrieves sales quotes


/Quotes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const dateFrom: Date = new Date("2019-10-31");
const dateTo: Date = new Date("2019-10-31");
const expiryDateFrom: Date = new Date("2019-10-31");
const expiryDateTo: Date = new Date("2019-10-31");
const contactID = '********-0000-0000-0000-********0000';
const status = 'DRAFT';
const page = 1;
const order = 'Status ASC';
const quoteNumber = 'QU-0001';

try {
  const response = await xero.accountingApi.getQuotes(xeroTenantId, ifModifiedSince, dateFrom, dateTo, expiryDateFrom, expiryDateTo, contactID, status, page, order, quoteNumber);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
DateFrom	
date (date)
Filter for quotes after a particular date

DateTo	
date (date)
Filter for quotes before a particular date

ExpiryDateFrom	
date (date)
Filter for quotes expiring after a particular date

ExpiryDateTo	
date (date)
Filter for quotes before a particular date

ContactID	
UUID (uuid)
Filter for quotes belonging to a particular contact

Status	
String
Filter for quotes of a particular Status

page	
Integer
e.g. page=1 – Up to 100 Quotes will be returned in a single API call with line items shown for each quote

order	
String
Order by an any element

QuoteNumber	
String
Filter by quote number (e.g. GET https://.../Quotes?QuoteNumber=QU-0001)

getReceipt

Retrieves a specific draft expense claim receipt by using a unique receipt Id


/Receipts/{ReceiptID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getReceipt(xeroTenantId, receiptID,  unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getReceiptAttachmentByFileName

Retrieves a specific attachment from a specific expense claim receipts by file name


/Receipts/{ReceiptID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getReceiptAttachmentByFileName(xeroTenantId, receiptID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getReceiptAttachmentById

Retrieves a specific attachments from a specific expense claim receipts by using a unique attachment Id


/Receipts/{ReceiptID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getReceiptAttachmentById(xeroTenantId, receiptID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getReceiptAttachments

Retrieves attachments for a specific expense claim receipt


/Receipts/{ReceiptID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getReceiptAttachments(xeroTenantId, receiptID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getReceiptHistory

Retrieves a history record for a specific receipt


/Receipts/{ReceiptID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getReceiptHistory(xeroTenantId, receiptID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getReceipts

Retrieves draft expense claim receipts for any user


/Receipts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'Status=="DRAFT"';
const order = 'ReceiptNumber ASC';
const unitdp = 4;

try {
  const response = await xero.accountingApi.getReceipts(xeroTenantId, ifModifiedSince, where, order, unitdp);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

getRepeatingInvoice

Retrieves a specific repeating invoice by using a unique repeating invoice Id


/RepeatingInvoices/{RepeatingInvoiceID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getRepeatingInvoice(xeroTenantId, repeatingInvoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getRepeatingInvoiceAttachmentByFileName

Retrieves a specific attachment from a specific repeating invoices by file name


/RepeatingInvoices/{RepeatingInvoiceID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getRepeatingInvoiceAttachmentByFileName(xeroTenantId, repeatingInvoiceID, fileName, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getRepeatingInvoiceAttachmentById

Retrieves a specific attachment from a specific repeating invoice


/RepeatingInvoices/{RepeatingInvoiceID}/Attachments/{AttachmentID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';
const attachmentID = '********-0000-0000-0000-********0000';
const contentType = 'image/jpg';

try {
  const response = await xero.accountingApi.getRepeatingInvoiceAttachmentById(xeroTenantId, repeatingInvoiceID, attachmentID, contentType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
AttachmentID*	
UUID (uuid)
Unique identifier for Attachment object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
contentType*	
String
The mime type of the attachment file you are retrieving i.e image/jpg, application/pdf

Required
getRepeatingInvoiceAttachments

Retrieves attachments from a specific repeating invoice


/RepeatingInvoices/{RepeatingInvoiceID}/Attachments
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getRepeatingInvoiceAttachments(xeroTenantId, repeatingInvoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
accounting.attachments.read	Grant read-only access to attachments
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getRepeatingInvoiceHistory

Retrieves history record for a specific repeating invoice


/RepeatingInvoices/{RepeatingInvoiceID}/History
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getRepeatingInvoiceHistory(xeroTenantId, repeatingInvoiceID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getRepeatingInvoices

Retrieves repeating invoices


/RepeatingInvoices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const where = 'Status=="DRAFT"';
const order = 'Total ASC';

try {
  const response = await xero.accountingApi.getRepeatingInvoices(xeroTenantId,  where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
accounting.transactions.read	Grant read-only access to invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getReportAgedPayablesByContact

Retrieves report for aged payables by contact


/Reports/AgedPayablesByContact
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactId = '********-0000-0000-0000-********0000';
const date: Date = new Date("2019-10-31");
const fromDate: Date = new Date("2019-10-31");
const toDate: Date = new Date("2019-10-31");

try {
  const response = await xero.accountingApi.getReportAgedPayablesByContact(xeroTenantId, contactId,  date, fromDate, toDate);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
contactId*	
UUID (uuid)
Unique identifier for a Contact

Required
date	
date (date)
The date of the Aged Payables By Contact report

fromDate	
date (date)
filter by the from date of the report e.g. 2021-02-01

toDate	
date (date)
filter by the to date of the report e.g. 2021-02-28

getReportAgedReceivablesByContact

Retrieves report for aged receivables by contact


/Reports/AgedReceivablesByContact
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactId = '********-0000-0000-0000-********0000';
const date: Date = new Date("2019-10-31");
const fromDate: Date = new Date("2019-10-31");
const toDate: Date = new Date("2019-10-31");

try {
  const response = await xero.accountingApi.getReportAgedReceivablesByContact(xeroTenantId, contactId,  date, fromDate, toDate);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
contactId*	
UUID (uuid)
Unique identifier for a Contact

Required
date	
date (date)
The date of the Aged Receivables By Contact report

fromDate	
date (date)
filter by the from date of the report e.g. 2021-02-01

toDate	
date (date)
filter by the to date of the report e.g. 2021-02-28

getReportBalanceSheet

Retrieves report for balancesheet


/Reports/BalanceSheet
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const date: Date = new Date("2019-11-01");
const periods = 3;
const timeframe = 'MONTH';
const trackingOptionID1 = '********-0000-0000-0000-********0000';
const trackingOptionID2 = '********-0000-0000-0000-********0000';
const standardLayout = true;
const paymentsOnly = false;

try {
  const response = await xero.accountingApi.getReportBalanceSheet(xeroTenantId,  date, periods, timeframe, trackingOptionID1, trackingOptionID2, standardLayout, paymentsOnly);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
date	
date (date)
The date of the Balance Sheet report

periods	
Integer
The number of periods for the Balance Sheet report

timeframe	
String
The period size to compare to (MONTH, QUARTER, YEAR)

trackingOptionID1	
String
The tracking option 1 for the Balance Sheet report

trackingOptionID2	
String
The tracking option 2 for the Balance Sheet report

standardLayout	
Boolean
The standard layout boolean for the Balance Sheet report

paymentsOnly	
Boolean
return a cash basis for the Balance Sheet report

getReportBankSummary

Retrieves report for bank summary


/Reports/BankSummary
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const fromDate: Date = new Date("2019-10-31");
const toDate: Date = new Date("2019-10-31");

try {
  const response = await xero.accountingApi.getReportBankSummary(xeroTenantId,  fromDate, toDate);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
fromDate	
date (date)
filter by the from date of the report e.g. 2021-02-01

toDate	
date (date)
filter by the to date of the report e.g. 2021-02-28

getReportBudgetSummary

Retrieves report for budget summary


/Reports/BudgetSummary
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const date: Date = new Date("2019-03-31");
const periods = 2;
const timeframe = 3;

try {
  const response = await xero.accountingApi.getReportBudgetSummary(xeroTenantId,  date, periods, timeframe);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
date	
date (date)
The date for the Bank Summary report e.g. 2018-03-31

periods	
Integer
The number of periods to compare (integer between 1 and 12)

timeframe	
Integer
The period size to compare to (1=month, 3=quarter, 12=year)

getReportExecutiveSummary

Retrieves report for executive summary


/Reports/ExecutiveSummary
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const date: Date = new Date("2019-03-31");

try {
  const response = await xero.accountingApi.getReportExecutiveSummary(xeroTenantId,  date);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
date	
date (date)
The date for the Bank Summary report e.g. 2018-03-31

getReportFromId

Retrieves a specific report using a unique ReportID


/Reports/{ReportID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const reportID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getReportFromId(xeroTenantId, reportID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Path parameters
Name	Description
ReportID*	
String
Unique identifier for a Report

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getReportProfitAndLoss

Retrieves report for profit and loss


/Reports/ProfitAndLoss
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const fromDate: Date = new Date("2019-10-31");
const toDate: Date = new Date("2019-10-31");
const periods = 3;
const timeframe = 'MONTH';
const trackingCategoryID = '********-0000-0000-0000-********0000';
const trackingCategoryID2 = '********-0000-0000-0000-********0000';
const trackingOptionID = '********-0000-0000-0000-********0000';
const trackingOptionID2 = '********-0000-0000-0000-********0000';
const standardLayout = true;
const paymentsOnly = false;

try {
  const response = await xero.accountingApi.getReportProfitAndLoss(xeroTenantId,  fromDate, toDate, periods, timeframe, trackingCategoryID, trackingCategoryID2, trackingOptionID, trackingOptionID2, standardLayout, paymentsOnly);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
fromDate	
date (date)
filter by the from date of the report e.g. 2021-02-01

toDate	
date (date)
filter by the to date of the report e.g. 2021-02-28

periods	
Integer
The number of periods to compare (integer between 1 and 12)

timeframe	
String
The period size to compare to (MONTH, QUARTER, YEAR)

trackingCategoryID	
String
The trackingCategory 1 for the ProfitAndLoss report

trackingCategoryID2	
String
The trackingCategory 2 for the ProfitAndLoss report

trackingOptionID	
String
The tracking option 1 for the ProfitAndLoss report

trackingOptionID2	
String
The tracking option 2 for the ProfitAndLoss report

standardLayout	
Boolean
Return the standard layout for the ProfitAndLoss report

paymentsOnly	
Boolean
Return cash only basis for the ProfitAndLoss report

getReportTenNinetyNine

Retrieve reports for 1099


/Reports/TenNinetyNine
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const reportYear = '2019';

try {
  const response = await xero.accountingApi.getReportTenNinetyNine(xeroTenantId,  reportYear);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
accounting.reports.tenninetynine.read	Grant read-only access to 1099 reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
reportYear	
String
The year of the 1099 report

getReportTrialBalance

Retrieves report for trial balance


/Reports/TrialBalance
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const date: Date = new Date("2019-10-31");
const paymentsOnly = true;

try {
  const response = await xero.accountingApi.getReportTrialBalance(xeroTenantId,  date, paymentsOnly);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
date	
date (date)
The date for the Trial Balance report e.g. 2018-03-31

paymentsOnly	
Boolean
Return cash only basis for the Trial Balance report

getReportsList

Retrieves a list of the organistaions unique reports that require a uuid to fetch


/Reports
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';

try {
  const response = await xero.accountingApi.getReportsList(xeroTenantId);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.reports.read	Grant read-only access to accounting reports
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getTaxRateByTaxType

Retrieves a specific tax rate according to given TaxType code


/TaxRates/{TaxType}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const taxType = 'INPUT2';

try {
  const response = await xero.accountingApi.getTaxRateByTaxType(xeroTenantId, taxType);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
TaxType*	
String
A valid TaxType code

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getTaxRates

Retrieves tax rates


/TaxRates
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const where = 'Status=="ACTIVE"';
const order = 'Name ASC';

try {
  const response = await xero.accountingApi.getTaxRates(xeroTenantId,  where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

getTrackingCategories

Retrieves tracking categories and options


/TrackingCategories
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const where = 'Status=="ACTIVE"';
const order = 'Name ASC';
const includeArchived = true;

try {
  const response = await xero.accountingApi.getTrackingCategories(xeroTenantId,  where, order, includeArchived);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

includeArchived	
Boolean
e.g. includeArchived=true - Categories and options with a status of ARCHIVED will be included in the response

getTrackingCategory

Retrieves specific tracking categories and options using a unique tracking category Id


/TrackingCategories/{TrackingCategoryID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const trackingCategoryID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getTrackingCategory(xeroTenantId, trackingCategoryID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
TrackingCategoryID*	
UUID (uuid)
Unique identifier for a TrackingCategory

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getUser

Retrieves a specific user


/Users/<USER>
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const userID = '********-0000-0000-0000-********0000';

try {
  const response = await xero.accountingApi.getUser(xeroTenantId, userID);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Path parameters
Name	Description
UserID*	
UUID (uuid)
Unique identifier for a User

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
getUsers

Retrieves users


/Users
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const ifModifiedSince: Date = new Date("2020-02-06T12:17:43.202-08:00");
const where = 'IsSubscriber==true';
const order = 'LastName ASC';

try {
  const response = await xero.accountingApi.getUsers(xeroTenantId, ifModifiedSince, where, order);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
accounting.settings.read	Grant read-only access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
If-Modified-Since	
Date (date-time)
Only records created or modified since this timestamp will be returned

Query parameters
Name	Description
where	
String
Filter by an any element

order	
String
Order by an any element

postSetup

Sets the chart of accounts, the conversion date and conversion balances


/Setup
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const account: Account = { 
  code: "123",
  name: "Business supplies",
  type: AccountType.EXPENSE
};   
const accounts = [];
accounts.push(account)

const conversionDate: ConversionDate = { 
  month: 10,
  year: 2020
};   
const conversionBalances = [];

const setup: Setup = { 
  accounts: accounts,
  conversionDate: conversionDate,
  conversionBalances: conversionBalances
}; 

try {
  const response = await xero.accountingApi.postSetup(xeroTenantId, setup, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
setup *	
Setup
Object including an accounts array, a conversion balances array and a conversion date object in body of request

Required
updateAccount

Updates a chart of accounts


/Accounts/{AccountID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const account: Account = { 
  code: "123456",
  name: "BarFoo",
  type: AccountType.EXPENSE,
  description: "Hello World",
  taxType: "NONE"
}; 

const accounts: Accounts = {  
  accounts: [account]
}; 

try {
  const response = await xero.accountingApi.updateAccount(xeroTenantId, accountID, accounts, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
accounts *	
Accounts
Request of type Accounts array with one Account

Required
updateAccountAttachmentByFileName

Updates attachment on a specific account by filename


/Accounts/{AccountID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const accountID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateAccountAttachmentByFileName(xeroTenantId, accountID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
AccountID*	
UUID (uuid)
Unique identifier for Account object

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateBankTransaction

Updates a single spent or received money transaction


/BankTransactions/{BankTransactionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const bankAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const bankTransaction: BankTransaction = { 
  reference: "You just updated",
  type: BankTransaction.TypeEnum.RECEIVE,
  contact: contact,
  lineItems: lineItems,
  bankAccount: bankAccount
}; 

const bankTransactions: BankTransactions = {  
  bankTransactions: [bankTransaction]
}; 

try {
  const response = await xero.accountingApi.updateBankTransaction(xeroTenantId, bankTransactionID, bankTransactions,  unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
bankTransactions *	
BankTransactions
Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateBankTransactionAttachmentByFileName

Updates a specific attachment from a specific bank transaction by filename


/BankTransactions/{BankTransactionID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransactionID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateBankTransactionAttachmentByFileName(xeroTenantId, bankTransactionID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
BankTransactionID*	
UUID (uuid)
Xero generated unique identifier for a bank transaction

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateBankTransferAttachmentByFileName


/BankTransfers/{BankTransferID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const bankTransferID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateBankTransferAttachmentByFileName(xeroTenantId, bankTransferID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
BankTransferID*	
UUID (uuid)
Xero generated unique identifier for a bank transfer

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateContact

Updates a specific contact in a Xero organisation


/Contacts/{ContactID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const contact: Contact = { 
  name: "Thanos",
  contactID: "********-0000-0000-0000-********0000"
}; 

const contacts: Contacts = {  
  contacts: [contact]
}; 

try {
  const response = await xero.accountingApi.updateContact(xeroTenantId, contactID, contacts, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
contacts *	
Contacts
an array of Contacts containing single Contact object with properties to update

Required
updateContactAttachmentByFileName


/Contacts/{ContactID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateContactAttachmentByFileName(xeroTenantId, contactID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
ContactID*	
UUID (uuid)
Unique identifier for a Contact

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateContactGroup

Updates a specific contact group


/ContactGroups/{ContactGroupID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const contactGroupID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const contactGroup: ContactGroup = { 
  name: "Vendor"
}; 

const contactGroups: ContactGroups = {  
  contactGroups: [contactGroup]
}; 

try {
  const response = await xero.accountingApi.updateContactGroup(xeroTenantId, contactGroupID, contactGroups, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Path parameters
Name	Description
ContactGroupID*	
UUID (uuid)
Unique identifier for a Contact Group

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
contactGroups *	
ContactGroups
an array of Contact groups with Name of specific group to update

Required
updateCreditNote

Updates a specific credit note


/CreditNotes/{CreditNoteID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const creditNote: CreditNote = { 
  type: CreditNote.TypeEnum.ACCPAYCREDIT,
  status: CreditNote.StatusEnum.AUTHORISED,
  reference: "My ref.",
  contact: contact,
  date: currDate,
  lineItems: lineItems
}; 

const creditNotes: CreditNotes = {  
  creditNotes: [creditNote]
}; 

try {
  const response = await xero.accountingApi.updateCreditNote(xeroTenantId, creditNoteID, creditNotes,  unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
creditNotes *	
CreditNotes
an array of Credit Notes containing credit note details to update

Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateCreditNoteAttachmentByFileName

Updates attachments on a specific credit note by file name


/CreditNotes/{CreditNoteID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const creditNoteID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateCreditNoteAttachmentByFileName(xeroTenantId, creditNoteID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
CreditNoteID*	
UUID (uuid)
Unique identifier for a Credit Note

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateExpenseClaim

Updates a specific expense claims


/ExpenseClaims/{ExpenseClaimID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const expenseClaimID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const user: User = { 
  userID: "********-0000-0000-0000-********0000"
}; 

const receipt: Receipt = { 
  receiptID: "********-0000-0000-0000-********0000",
  date: currDate
};   
const receipts = [];
receipts.push(receipt)

const expenseClaim: ExpenseClaim = { 
  status: ExpenseClaim.StatusEnum.SUBMITTED,
  user: user,
  receipts: receipts
}; 

const expenseClaims: ExpenseClaims = {  
  expenseClaims: [expenseClaim]
}; 

try {
  const response = await xero.accountingApi.updateExpenseClaim(xeroTenantId, expenseClaimID, expenseClaims, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
ExpenseClaimID*	
UUID (uuid)
Unique identifier for a ExpenseClaim

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
expenseClaims *	
ExpenseClaims
Required
updateInvoice

Updates a specific sales invoices or purchase bills


/Invoices/{InvoiceID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const invoice: Invoice = { 
  reference: "I am Iron man"
}; 

const invoices: Invoices = {  
  invoices: [invoice]
}; 

try {
  const response = await xero.accountingApi.updateInvoice(xeroTenantId, invoiceID, invoices,  unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
invoices *	
Invoices
Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateInvoiceAttachmentByFileName

Updates an attachment from a specific invoices or purchase bill by filename


/Invoices/{InvoiceID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const invoiceID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateInvoiceAttachmentByFileName(xeroTenantId, invoiceID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
InvoiceID*	
UUID (uuid)
Unique identifier for an Invoice

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateItem

Updates a specific item


/Items/{ItemID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const itemID = '********-0000-0000-0000-********0000';
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const item: Item = { 
  code: "ItemCode123",
  description: "Goodbye"
}; 

const items: Items = {  
  items: [item]
}; 

try {
  const response = await xero.accountingApi.updateItem(xeroTenantId, itemID, items,  unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
ItemID*	
UUID (uuid)
Unique identifier for an Item

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
items *	
Items
Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateLinkedTransaction

Updates a specific linked transactions (billable expenses)


/LinkedTransactions/{LinkedTransactionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const linkedTransactionID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const linkedTransaction: LinkedTransaction = { 
  sourceLineItemID: "********-0000-0000-0000-********0000",
  contactID: "********-0000-0000-0000-********0000"
}; 

const linkedTransactions: LinkedTransactions = {  
  linkedTransactions: [linkedTransaction]
}; 

try {
  const response = await xero.accountingApi.updateLinkedTransaction(xeroTenantId, linkedTransactionID, linkedTransactions, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
LinkedTransactionID*	
UUID (uuid)
Unique identifier for a LinkedTransaction

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
linkedTransactions *	
LinkedTransactions
Required
updateManualJournal

Updates a specific manual journal


/ManualJournals/{ManualJournalID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'  
const manualJournalLines = [];

const credit: ManualJournalLine = { 
  lineAmount: -100.0,
  accountCode: "400",
  description: "Hello there"
}; 
manualJournalLines.push(credit)

const debit: ManualJournalLine = { 
  lineAmount: 100.0,
  accountCode: "120",
  description: "Hello there"
}; 
manualJournalLines.push(debit)

const manualJournal: ManualJournal = { 
  narration: "Foobar",
  date: dateValue,
  manualJournalLines: manualJournalLines
}; 

const manualJournals: ManualJournals = {  
  manualJournals: [manualJournal]
}; 

try {
  const response = await xero.accountingApi.updateManualJournal(xeroTenantId, manualJournalID, manualJournals, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
manualJournals *	
ManualJournals
Required
updateManualJournalAttachmentByFileName

Updates a specific attachment from a specific manual journal by file name


/ManualJournals/{ManualJournalID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const manualJournalID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateManualJournalAttachmentByFileName(xeroTenantId, manualJournalID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
ManualJournalID*	
UUID (uuid)
Unique identifier for a ManualJournal

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateOrCreateBankTransactions

Updates or creates one or more spent or received money transaction


/BankTransactions
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const bankAccount: Account = { 
  accountID: "********-0000-0000-0000-********0000"
}; 

const bankTransaction: BankTransaction = { 
  type: BankTransaction.TypeEnum.RECEIVE,
  contact: contact,
  lineItems: lineItems,
  bankAccount: bankAccount
}; 

const bankTransactions: BankTransactions = {  
  bankTransactions: [bankTransaction]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateBankTransactions(xeroTenantId, bankTransactions,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
bankTransactions *	
BankTransactions
Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateOrCreateContacts

Updates or creates one or more contacts in a Xero organisation


/Contacts
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';

const phone: Phone = { 
  phoneNumber: "555-1212",
  phoneType: Phone.PhoneTypeEnum.MOBILE
};   
const phones = [];
phones.push(phone)

const contact: Contact = { 
  name: "Bruce Banner",
  emailAddress: "<EMAIL>",
  phones: phones
}; 

const contacts: Contacts = {  
  contacts: [contact]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateContacts(xeroTenantId, contacts,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.contacts	Grant read-write access to contacts and contact groups
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
contacts *	
Contacts
Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

updateOrCreateCreditNotes

Updates or creates one or more credit notes


/CreditNotes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';
const currDate = '2020-12-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const creditNote: CreditNote = { 
  type: CreditNote.TypeEnum.ACCPAYCREDIT,
  contact: contact,
  date: currDate,
  lineItems: lineItems
}; 

const creditNotes: CreditNotes = {  
  creditNotes: [creditNote]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateCreditNotes(xeroTenantId, creditNotes,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
creditNotes *	
CreditNotes
an array of Credit Notes with a single CreditNote object.

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateOrCreateEmployees

Creates a single new employees used in Xero payrun


/Employees
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';

const employee: Employee = { 
  firstName: "Nick",
  lastName: "Fury"
}; 

const employees: Employees = {  
  employees: [employee]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateEmployees(xeroTenantId, employees,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
employees *	
Employees
Employees with array of Employee object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

updateOrCreateInvoices

Updates or creates one or more sales invoices or purchase bills


/Invoices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'
const dueDateValue = '2020-10-28'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const invoice: Invoice = { 
  type: Invoice.TypeEnum.ACCREC,
  contact: contact,
  date: dateValue,
  dueDate: dueDateValue,
  lineItems: lineItems,
  reference: "Website Design",
  status: Invoice.StatusEnum.DRAFT
}; 

const invoices: Invoices = {  
  invoices: [invoice]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateInvoices(xeroTenantId, invoices,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
invoices *	
Invoices
Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateOrCreateItems

Updates or creates one or more items


/Items
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';

const item: Item = { 
  code: "abcXYZ123",
  name: "HelloWorld",
  description: "Foobar"
}; 

const items: Items = {  
  items: [item]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateItems(xeroTenantId, items,  summarizeErrors, unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
items *	
Items
Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateOrCreateManualJournals

Updates or creates a single manual journal


/ManualJournals
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'  
const manualJournalLines = [];

const credit: ManualJournalLine = { 
  lineAmount: -100.0,
  accountCode: "400",
  description: "Hello there"
}; 
manualJournalLines.push(credit)

const debit: ManualJournalLine = { 
  lineAmount: 100.0,
  accountCode: "120",
  description: "Hello there"
}; 
manualJournalLines.push(debit)

const manualJournal: ManualJournal = { 
  narration: "Foobar",
  date: dateValue,
  manualJournalLines: manualJournalLines
}; 

const manualJournals: ManualJournals = {  
  manualJournals: [manualJournal]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateManualJournals(xeroTenantId, manualJournals,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
manualJournals *	
ManualJournals
ManualJournals array with ManualJournal object in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

updateOrCreatePurchaseOrders

Updates or creates one or more purchase orders


/PurchaseOrders
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const purchaseOrder: PurchaseOrder = { 
  contact: contact,
  lineItems: lineItems,
  date: dateValue
}; 

const purchaseOrders: PurchaseOrders = {  
  purchaseOrders: [purchaseOrder]
}; 

try {
  const response = await xero.accountingApi.updateOrCreatePurchaseOrders(xeroTenantId, purchaseOrders,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
purchaseOrders *	
PurchaseOrders
Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

updateOrCreateQuotes

Updates or creates one or more quotes


/Quotes
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const lineItem: LineItem = { 
  description: "Foobar",
  quantity: 1.0,
  unitAmount: 20.0,
  accountCode: "000"
};   
const lineItems = [];
lineItems.push(lineItem)

const quote: Quote = { 
  contact: contact,
  lineItems: lineItems,
  date: dateValue
}; 

const quotes: Quotes = {  
  quotes: [quote]
}; 

try {
  const response = await xero.accountingApi.updateOrCreateQuotes(xeroTenantId, quotes,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
quotes *	
Quotes
Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

updateOrCreateRepeatingInvoices

Creates or deletes one or more repeating invoice templates


/RepeatingInvoices
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const summarizeErrors = true;
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateOrCreateRepeatingInvoices(xeroTenantId, repeatingInvoices,  summarizeErrors,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
repeatingInvoices *	
RepeatingInvoices
RepeatingInvoices with an array of repeating invoice objects in body of request

Required
Query parameters
Name	Description
summarizeErrors	
Boolean
If false return 200 OK and mix of successfully created objects and any with validation errors

updatePurchaseOrder

Updates a specific purchase order


/PurchaseOrders/{PurchaseOrderID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const purchaseOrder: PurchaseOrder = { 
  attentionTo: "Peter Parker"
}; 

const purchaseOrders: PurchaseOrders = {  
  purchaseOrders: [purchaseOrder]
}; 

try {
  const response = await xero.accountingApi.updatePurchaseOrder(xeroTenantId, purchaseOrderID, purchaseOrders, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
purchaseOrders *	
PurchaseOrders
Required
updatePurchaseOrderAttachmentByFileName

Updates a specific attachment for a specific purchase order by filename


/PurchaseOrders/{PurchaseOrderID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const purchaseOrderID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updatePurchaseOrderAttachmentByFileName(xeroTenantId, purchaseOrderID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
PurchaseOrderID*	
UUID (uuid)
Unique identifier for an Purchase Order

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateQuote

Updates a specific quote


/Quotes/{QuoteID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const contact: Contact = { 
  contactID: "********-0000-0000-0000-********0000"
}; 

const quote: Quote = { 
  reference: "I am an update",
  contact: contact,
  date: dateValue
}; 

const quotes: Quotes = {  
  quotes: [quote]
}; 

try {
  const response = await xero.accountingApi.updateQuote(xeroTenantId, quoteID, quotes, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
quotes *	
Quotes
Required
updateQuoteAttachmentByFileName

Updates a specific attachment from a specific quote by filename


/Quotes/{QuoteID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const quoteID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateQuoteAttachmentByFileName(xeroTenantId, quoteID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
QuoteID*	
UUID (uuid)
Unique identifier for an Quote

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateReceipt

Updates a specific draft expense claim receipts


/Receipts/{ReceiptID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const unitdp = 4;
const idempotencyKey = 'KEY_VALUE';
const dateValue = '2020-10-10'

const user: User = { 
  userID: "********-0000-0000-0000-********0000"
}; 

const receipt: Receipt = { 
  user: user,
  reference: "Foobar",
  date: dateValue
}; 

const receipts: Receipts = {  
  receipts: [receipt]
}; 

try {
  const response = await xero.accountingApi.updateReceipt(xeroTenantId, receiptID, receipts,  unitdp,idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
receipts *	
Receipts
Required
Query parameters
Name	Description
unitdp	
Integer
e.g. unitdp=4 – (Unit Decimal Places) You can opt in to use four decimal places for unit amounts

updateReceiptAttachmentByFileName

Updates a specific attachment on a specific expense claim receipts by file name


/Receipts/{ReceiptID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const receiptID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateReceiptAttachmentByFileName(xeroTenantId, receiptID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
ReceiptID*	
UUID (uuid)
Unique identifier for a Receipt

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateRepeatingInvoice

Deletes a specific repeating invoice template


/RepeatingInvoices/{RepeatingInvoiceID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateRepeatingInvoice(xeroTenantId, repeatingInvoiceID, repeatingInvoices, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.transactions	Grant read-write access to bank transactions, credit notes, invoices, repeating invoices
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
repeatingInvoices *	
RepeatingInvoices
Required
updateRepeatingInvoiceAttachmentByFileName

Updates a specific attachment from a specific repeating invoices by file name


/RepeatingInvoices/{RepeatingInvoiceID}/Attachments/{FileName}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const repeatingInvoiceID = '********-0000-0000-0000-********0000';
const fileName = 'xero-dev.jpg';
const idempotencyKey = 'KEY_VALUE';

try {
  const response = await xero.accountingApi.updateRepeatingInvoiceAttachmentByFileName(xeroTenantId, repeatingInvoiceID, fileName, body, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.attachments	Grant read-write access to attachments
Parameters

Path parameters
Name	Description
RepeatingInvoiceID*	
UUID (uuid)
Unique identifier for a Repeating Invoice

Required
FileName*	
String
Name of the attachment

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
body *	
File
Byte array of file in body of request

Required
updateTaxRate

Updates tax rates


/TaxRates
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const idempotencyKey = 'KEY_VALUE';

const taxComponent: TaxComponent = { 
  name: "State Tax",
  rate: 2.25
};   
const taxComponents = [];
taxComponents.push(taxComponent)

const taxRate: TaxRate = { 
  name: "CA State Tax",
  taxComponents: taxComponents,

const taxRates: TaxRates = {  
  taxRates: [taxRate]
}; 

try {
  const response = await xero.accountingApi.updateTaxRate(xeroTenantId, taxRates, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
taxRates *	
TaxRates
Required
updateTrackingCategory

Updates a specific tracking category


/TrackingCategories/{TrackingCategoryID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const trackingCategoryID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const trackingCategory: TrackingCategory = { 
  name: "Foobar"
}; 

try {
  const response = await xero.accountingApi.updateTrackingCategory(xeroTenantId, trackingCategoryID, trackingCategory, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
TrackingCategoryID*	
UUID (uuid)
Unique identifier for a TrackingCategory

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
trackingCategory *	
TrackingCategory
Required
updateTrackingOptions

Updates a specific option for a specific tracking category


/TrackingCategories/{TrackingCategoryID}/Options/{TrackingOptionID}
Usage and SDK Samples

Node
await xero.setTokenSet(tokenSet);

const xeroTenantId = 'YOUR_XERO_TENANT_ID';
const trackingCategoryID = '********-0000-0000-0000-********0000';
const trackingOptionID = '********-0000-0000-0000-********0000';
const idempotencyKey = 'KEY_VALUE';

const trackingOption: TrackingOption = { 
  name: "Foobar"
}; 

try {
  const response = await xero.accountingApi.updateTrackingOptions(xeroTenantId, trackingCategoryID, trackingOptionID, trackingOption, idempotencyKey);
  console.log(response.body || response.response.statusCode)
} catch (err) {
  const error = JSON.stringify(err.response.body, null, 2)
  console.log(`Status Code: ${err.response.statusCode} => ${error}`);
}
Scopes

accounting.settings	Grant read-write access to organisation and account settings
Parameters

Path parameters
Name	Description
TrackingCategoryID*	
UUID (uuid)
Unique identifier for a TrackingCategory

Required
TrackingOptionID*	
UUID (uuid)
Unique identifier for a Tracking Option

Required
Header parameters
Name	Description
xero-tenant-id*	
String
Xero identifier for Tenant

Required
Idempotency-Key	
String
This allows you to safely retry requests without the risk of duplicate processing. 128 character max.

Body parameters
Name	Description
trackingOption *	
TrackingOption
Required
