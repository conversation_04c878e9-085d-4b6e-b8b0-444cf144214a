import React from 'react'; interface HeaderProps { displayYear: number; onPreviousYear: () => void; onNextYear: () => void; } const Header: React.FC<HeaderProps> = ({ displayYear, onPreviousYear, onNextYear }) => { return ( <div className="tax-calendar-header"> <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100"> Tax Payment Schedule </h3> <div className="tax-calendar-year-selector"> <button onClick={onPreviousYear} className="tax-calendar-year-button" aria-label="Previous Year" > <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" /> </svg> </button> <span className="tax-calendar-year">{displayYear}</span> <button onClick={onNextYear} className="tax-calendar-year-button" aria-label="Next Year" > <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" /> </svg> </button> </div> </div> ); }; export default Header; 