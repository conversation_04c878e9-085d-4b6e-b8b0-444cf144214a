-- Migration: Create estimate allocation tables
-- Date: 2025-05-23
-- Purpose: Add missing tables for estimate staff allocations and time tracking

-- Create estimate_allocation table
CREATE TABLE IF NOT EXISTS estimate_allocation (
  id TEXT PRIMARY KEY,
  estimate_id TEXT NOT NULL,
  harvest_user_id INTEGER NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT,
  project_role TEXT,
  level TEXT,
  target_rate_daily REAL NOT NULL,
  cost_rate_daily REAL NOT NULL,
  proposed_rate_daily REAL NOT NULL,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (estimate_id) REFERENCES estimate(id) ON DELETE CASCADE
);

-- Create indexes for estimate_allocation table
CREATE INDEX IF NOT EXISTS idx_estimate_allocation_estimate_id ON estimate_allocation(estimate_id);
CREATE INDEX IF NOT EXISTS idx_estimate_allocation_harvest_user_id ON estimate_allocation(harvest_user_id);

-- Create estimate_time_allocation table
CREATE TABLE IF NOT EXISTS estimate_time_allocation (
  id TEXT PRIMARY KEY,
  allocation_id TEXT NOT NULL,
  week_identifier TEXT NOT NULL, -- ISO week identifier
  days REAL NOT NULL,
  FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE CASCADE,
  UNIQUE(allocation_id, week_identifier)
);

-- Create indexes for estimate_time_allocation table
CREATE INDEX IF NOT EXISTS idx_estimate_time_allocation_allocation_id ON estimate_time_allocation(allocation_id);
CREATE INDEX IF NOT EXISTS idx_estimate_time_allocation_week ON estimate_time_allocation(week_identifier);