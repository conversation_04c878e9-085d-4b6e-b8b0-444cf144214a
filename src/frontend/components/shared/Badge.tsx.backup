import React, { forwardRef } from 'react';
import { badgeVariants, type BadgeVariantProps } from '../../utils/component-variants';

interface BadgeProps extends BadgeVariantProps {
  children: React.ReactNode;
  className?: string;
  /**
   * Icon to display before text (optional)
   */
  icon?: React.ReactNode;
  /**
   * Icon to display after text (optional)
   */
  iconRight?: React.ReactNode;
  /**
   * Tooltip text for accessibility
   */
  title?: string;
  /**
   * ARIA label for accessibility
   */
  'aria-label'?: string;
  /**
   * HTML element to render as (span by default)
   */
  as?: keyof JSX.IntrinsicElements;
}

/**
 * A flexible Badge component with built-in variants and icon support.
 * 
 * Features:
 * - Type-safe variant system
 * - Icon support (left and right)
 * - Financial-specific variants (predicted, accrued)
 * - Full accessibility support
 * - Dark mode support
 * - Consistent styling with design system
 * 
 * @example
 * ```tsx
 * <Badge variant="predicted" icon={<LightningIcon />}>
 *   Predicted | 75% | Q2
 * </Badge>
 * ```
 */
export const Badge = forwardRef<HTMLSpanElement, BadgeProps>(({
  children,
  className,
  variant = 'primary',
  size = 'default',
  withIcon,
  icon,
  iconRight,
  title,
  'aria-label': ariaLabel,
  as: Component = 'span',
  ...props
}, ref) => {
  const hasIcon = !!icon || !!iconRight;
  const classes = badgeVariants({
    variant,
    size,
    withIcon: withIcon ?? hasIcon,
    className,
  });

  const badgeProps = {
    ref,
    className: classes,
    title,
    'aria-label': ariaLabel,
    ...props,
  };

  return React.createElement(
    Component,
    badgeProps,
    <>
      {/* Left icon */}
      {icon && (
        <span className="mr-1 -ml-0.5" aria-hidden="true">
          {icon}
        </span>
      )}
      
      {/* Badge text */}
      <span>{children}</span>
      
      {/* Right icon */}
      {iconRight && (
        <span className="ml-1 -mr-0.5" aria-hidden="true">
          {iconRight}
        </span>
      )}
    </>
  );
});

Badge.displayName = 'Badge';

export { Badge as default };