import React, { useState, useEffect } from "react"; import { useNavigate } from "react-router-dom"; import { versionHistoryService, type VersionUpdate } from "../../../services/version-history-service"; import { format, formatDistanceToNow } from "date-fns"; import { useFloatingPanels } from "../../contexts/FloatingPanelsContext"; interface UpdateNotificationProps { className?: string; } /** * Component that shows a notification about the latest update */ const UpdateNotification: React.FC<UpdateNotificationProps> = ({ className = "", }) => { const navigate = useNavigate(); const { updateNotificationExpanded, expandUpdateNotification, setUpdateNotificationExpanded, } = useFloatingPanels(); const [showDetails, setShowDetails] = useState(false); const [latestUpdate, setLatestUpdate] = useState<VersionUpdate | null>(null); // Load latest update useEffect(() => { let mounted = true; const loadLatestUpdate = async () => { try { const latest = await versionHistoryService.getLatestVersion(); if (mounted) { setLatestUpdate(latest); } } catch (error) { console.error('Failed to load latest update:', error); } }; loadLatestUpdate(); return () => { mounted = false; }; }, []); // Show notification when component mounts - MUST BE BEFORE ANY CONDITIONAL RENDERS useEffect(() => { if (!latestUpdate?.version) return; // Check if this update has been seen before const lastSeenVersion = localStorage.getItem("lastSeenVersion"); if (lastSeenVersion !== latestUpdate.version) { setUpdateNotificationExpanded(true); } }, [latestUpdate?.version, setUpdateNotificationExpanded]); // Helper function to get time ago const getTimeAgo = (dateString: string) => { try { return formatDistanceToNow(new Date(dateString), { addSuffix: true }); } catch (error) { return 'recently'; } }; if (!latestUpdate || !latestUpdate.date || !latestUpdate.version) { return null; // Don't render if no update data } const timeAgo = getTimeAgo(latestUpdate.date); // Handle dismissing the notification const handleDismiss = () => { setUpdateNotificationExpanded(false); localStorage.setItem("lastSeenVersion", latestUpdate.version); }; // Toggle showing details const toggleDetails = () => { setShowDetails(!showDetails); }; if (!updateNotificationExpanded) { return ( <button onClick={expandUpdateNotification} className={`fixed bottom-36 left-4 bg-primary text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-110 ${className}`} title="Show update information" > <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> </button> ); } return ( <div className={`fixed bottom-36 left-4 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-blue-200 dark:border-blue-800 overflow-hidden transition-all duration-200 z-50 ${className}`} > <div className="p-4"> <div className="flex items-start justify-between"> <div className="flex items-center"> <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400 mr-3"> <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> </div> <div> <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100"> Updated {timeAgo} </h3> <p className="text-xs text-gray-500 dark:text-gray-400"> Version {latestUpdate.version} </p> </div> </div> <button onClick={handleDismiss} className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400" > <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /> </svg> </button> </div> <div className="mt-3"> <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100"> {latestUpdate.title} </h4> <p className="text-xs text-gray-600 dark:text-gray-300 mt-1"> {latestUpdate.description} </p> {showDetails && ( <div className="mt-2 text-xs text-gray-600 dark:text-gray-300"> <h5 className="font-medium mb-1">What's new:</h5> <ul className="list-disc pl-5 space-y-1"> {latestUpdate.changes.map((change, index) => ( <li key={index}>{change}</li> ))} </ul> </div> )} <div className="mt-3 flex justify-between"> <button onClick={toggleDetails} className="text-xs text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300" > {showDetails ? "Hide details" : "What's new"} </button> <button onClick={() => navigate("/version-history")} className="text-xs text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300" > View all updates </button> </div> </div> </div> </div> ); }; export default UpdateNotification; 