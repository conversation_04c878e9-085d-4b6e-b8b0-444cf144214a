import React from "react"; import SegmentedControl from "../shared/SegmentedControl"; export type RateDisplayMode = "daily" | "hourly"; export type HoursPerDay = 7.5 | 8; interface RateDisplayControlsProps { /** * Current rate display mode */ rateDisplayMode: RateDisplayMode; /** * Callback when rate display mode changes */ onRateDisplayModeChange: (mode: RateDisplayMode) => void; /** * Current hours per day setting */ hoursPerDay: HoursPerDay; /** * Callback when hours per day changes */ onHoursPerDayChange: (hours: HoursPerDay) => void; /** * Additional CSS classes */ className?: string; } /** * Controls for pricing model selection in estimates. * * Features: * - Toggle between daily and hourly pricing models * - Configure hours per day (7.5h or 8h) for hourly pricing calculations * - Hours per day control only shows when hourly pricing is selected * - Clean segmented control UI * - Full accessibility support * * @example * ```tsx * <RateDisplayControls * rateDisplayMode={rateDisplayMode} * onRateDisplayModeChange={setRateDisplayMode} * hoursPerDay={hoursPerDay} * onHoursPerDayChange={setHoursPerDay} * /> * ``` */ const RateDisplayControls: React.FC<RateDisplayControlsProps> = ({ rateDisplayMode, onRateDisplayModeChange, hoursPerDay, onHoursPerDayChange, className = "", }) => { const rateDisplayOptions = [ { value: "daily" as const, label: "Daily", icon: ( <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /> </svg> ), }, { value: "hourly" as const, label: "Hourly", icon: ( <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> ), }, ]; const hoursPerDayOptions = [ { value: 7.5 as const, label: "7.5h" }, { value: 8 as const, label: "8h" }, ]; return ( <div className={`flex flex-col sm:flex-row gap-3 sm:gap-4 items-start sm:items-center ${className}`} > {/* Pricing Model */} <div className="flex items-center gap-2"> <label className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap"> Pricing Model: </label> <SegmentedControl options={rateDisplayOptions} value={rateDisplayMode} onChange={onRateDisplayModeChange} size="sm" aria-label="Pricing model" /> </div> {/* Hours per Day - only show when in hourly mode */} {rateDisplayMode === "hourly" && ( <div className="flex items-center gap-2"> <label className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap"> Hours/Day: </label> <SegmentedControl options={hoursPerDayOptions} value={hoursPerDay} onChange={onHoursPerDayChange} size="sm" aria-label="Hours per day for calculations" /> </div> )} </div> ); }; export default RateDisplayControls; 