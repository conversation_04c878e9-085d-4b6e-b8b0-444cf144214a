import React from 'react'; import { ProcessedDecision } from '../../types/projection-audit-types'; import { formatDate, formatCurrency } from '../../utils'; interface PaymentTermsReconciliationProps { decision: ProcessedDecision; } const PaymentTermsReconciliation: React.FC<PaymentTermsReconciliationProps> = ({ decision }) => { // Get today's date and format it const today = new Date(); const todayFormatted = formatDate(today.toISOString()); // Get projection window cutoff date const cutoffDate = decision.cutoffDate ? formatDate(decision.cutoffDate) : 'unknown'; // Ensure decision has required properties const paymentTerms = decision.paymentTermsDays || 30; const paymentDate = decision.paymentDate || new Date().toISOString(); const amount = typeof decision.amount === 'number' && !isNaN(decision.amount) ? decision.amount : 0; return ( <div className="flex flex-col space-y-1.5"> {/* Title row */} <div className="flex items-center justify-between"> <div className="flex items-center space-x-1.5"> <div className="inline-flex items-center text-[10px] font-medium text-yellow-600 dark:text-yellow-300"> <svg className="h-3 w-3 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /> </svg> Payment Terms Timeline </div> </div> <div className="text-[10px] text-gray-500 dark:text-gray-400"> Terms: {paymentTerms} days </div> </div> {/* Timeline visualization */} <div className="rounded-lg bg-gray-50 dark:bg-gray-700/20 p-3 border border-gray-200 dark:border-gray-700"> <div className="relative"> {/* Timeline track */} <div className="absolute top-3 left-0 right-0 h-0.5 bg-gray-200 dark:bg-gray-600"></div> {/* Today marker */} <div className="relative flex flex-col items-center mb-6"> <div className="absolute -top-1.5 w-3 h-3 rounded-full bg-blue-500"></div> <div className="mt-3 text-[10px] font-medium text-blue-600 dark:text-blue-300"> Today<br/>{todayFormatted} </div> </div> {/* Flexible spacer */} <div className="h-6"></div> {/* Cutoff date (projection window end) */} <div className="relative flex flex-col items-center ml-auto mr-0 mb-6" style={{ width: '40%' }}> <div className="absolute -top-1.5 w-3 h-3 rounded-full bg-red-500"></div> <div className="mt-3 text-[10px] font-medium text-red-600 dark:text-red-300"> Cutoff Date<br/>{cutoffDate} </div> </div> {/* Flexible spacer */} <div className="h-6"></div> {/* Invoice payment date (outside projection window) */} <div className="relative flex flex-col items-end mb-6" style={{ width: '100%' }}> <div className="absolute -top-1.5 w-3 h-3 rounded-full bg-yellow-500"></div> <div className="mt-3 text-[10px] font-medium text-yellow-600 dark:text-yellow-300"> Payment Date<br/>{formatDate(paymentDate)} </div> </div> </div> </div> {/* Explanation */} <div className="text-[9px] text-gray-500 dark:text-gray-400 pl-2 border-l-2 border-yellow-300/50 dark:border-yellow-700"> <span className="font-medium text-yellow-600 dark:text-yellow-400">Exclusion Reason:</span> This invoice was excluded because its payment date ({formatDate(paymentDate)}) falls outside the projection window, which ends on {cutoffDate}. Based on the project's {paymentTerms}-day payment terms, this invoice is not expected to be paid within the forecast timeframe. </div> </div> ); }; export default PaymentTermsReconciliation;