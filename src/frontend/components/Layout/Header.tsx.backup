import React from "react";
import { useNavigate } from "react-router-dom";
import DarkModeToggle from "../DarkModeToggle";
import { UnifiedNavigation } from "../Navigation";
import ProfileBadge from "../ProfileBadge";
import { useUser } from "../../contexts/UserContext";
import { useSearch } from "../../contexts/SearchContext";

interface HeaderProps {
  handleLogout: () => void;
}

export const Header: React.FC<HeaderProps> = ({ handleLogout }) => {
  const navigate = useNavigate();
  const { user } = useUser();
  const { openSearch } = useSearch();

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm py-4 px-4 sm:px-8 mb-1 sticky top-0 z-50 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <div className="flex items-center h-full">
          <img
            src="/logo.png"
            alt="Upstream Logo"
            className="h-8 sm:h-9 w-auto object-contain mr-4"
            onClick={() => navigate("/")}
            style={{ cursor: "pointer" }}
          />

          {/* Desktop Navigation - only visible on larger screens */}
          <div className="h-full">
            <UnifiedNavigation variant="desktop" />
          </div>
        </div>

        {/* Header actions */}
        <div className="flex items-center gap-3 sm:gap-4">
          <DarkModeToggle />

          {/* Search button */}
          <button
            onClick={openSearch}
            className="flex items-center justify-center w-9 h-9 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
            title="Search (Cmd+K)"
            aria-label="Search"
          >
            <svg
              className="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {/* Help button styled like the dark mode toggle */}
          <button
            onClick={() => navigate("/help")}
            className="flex items-center justify-center w-9 h-9 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
            title="Help"
            aria-label="Help"
          >
            <svg
              className="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path
                d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {user ? (
            <ProfileBadge
              userName={user.name}
              userEmail={user.email}
              onLogout={handleLogout}
            />
          ) : (
            <button
              onClick={handleLogout}
              className="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              title="Logout"
            >
              <svg
                className="w-4 h-4 mr-1.5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              <span>Logout</span>
            </button>
          )}
        </div>
      </div>
    </header>
  );
};
