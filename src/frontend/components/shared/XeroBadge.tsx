import React from 'react'; import { IconBadge } from './IconBadge'; import type { IconBadgeVariantProps } from '../../utils/component-variants'; interface XeroBadgeProps extends Partial<IconBadgeVariantProps> { className?: string; /** * Click handler for interactive Xero badge */ onClick?: () => void; } /** * Xero logo badge component with consistent styling and accessibility. * * Features: * - Uses official Xero logo SVG * - Consistent sizing with design system * - Proper accessibility attributes * - Optional interactive behavior * - Performance optimized loading * * @example * ```tsx * <XeroBadge size="lg" title="Data synced from Xero" /> * ``` */ const XeroBadge: React.FC<XeroBadgeProps> = ({ className, size = 'default', rounded = 'default', onClick, ...props }) => { return ( <IconBadge src="/xero.svg" alt="Xero" title="From Xero" size={size} rounded={rounded} className={className} onClick={onClick} loading="eager" // Xero badges are frequently used, load immediately {...props} /> ); }; export default XeroBadge;