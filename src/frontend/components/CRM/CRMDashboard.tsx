import { useState } from "react"; import { useNavigate, useLocation } from "react-router-dom"; import DealBoard from "./Board/DealBoard"; import ContactsList from "./Contacts/ContactsList"; import CompaniesList from "./Companies/CompaniesList"; import HubSpotIntegration from "./HubSpot/HubSpotIntegration"; import CRMReports from "./Reports/CRMReports"; import DataManagementPage from "./DataManagement/DataManagementPage"; import { UnifiedSearch } from "./directory/UnifiedSearch"; import { EyeSlashIcon, EyeIcon } from "@heroicons/react/24/outline"; /** * Main CRM dashboard component with tabs for different sections */ const CRMDashboard = () => { const navigate = useNavigate(); const location = useLocation(); // Determine active tab based on URL path const getActiveTab = (): | "directory" | "deals" | "contacts" | "companies" | "hubspot" | "reports" | "data-management" => { const path = location.pathname; // Check if we're on a specific deal page (matches pattern /crm/deals/123) if (path.match(/\/crm\/deals\/[^/]+$/)) { // Don't render any tab content when on a specific deal page // This allows the router to render the DealEditPage instead return "deals"; // Still return "deals" but we'll handle this case separately } if (path.includes("/directory")) return "directory"; if (path.includes("/contacts")) return "contacts"; if (path.includes("/companies")) return "companies"; if (path.includes("/hubspot")) return "hubspot"; if (path.includes("/reports")) return "reports"; if (path.includes("/data-management")) return "data-management"; if (path.includes("/deals")) return "deals"; return "directory"; // Default to directory for enhanced search }; const activeTab = getActiveTab(); // Set the active tab by navigating to the appropriate URL const setActiveTab = ( tab: | "directory" | "deals" | "contacts" | "companies" | "hubspot" | "reports" | "data-management" ) => { switch (tab) { case "directory": navigate("/crm/directory"); break; case "contacts": navigate("/crm/contacts"); break; case "companies": navigate("/crm/companies"); break; case "hubspot": navigate("/crm/hubspot"); break; case "reports": navigate("/crm/reports"); break; case "data-management": navigate("/crm/data-management"); break; case "deals": navigate("/crm/deals"); break; default: navigate("/crm/directory"); break; } }; // Always default to normal view (false) when the page loads const [isCompactView, setIsCompactView] = useState(false); // Default to showing all columns (false = show all) const [hideInactive, setHideInactive] = useState(false); // No longer saving to localStorage as we always want to default to normal view // Toggle compact view const toggleCompactView = () => { setIsCompactView((prev: boolean) => !prev); }; // Toggle hide inactive columns const toggleHideInactive = () => { setHideInactive((prev: boolean) => !prev); }; // If we're on a specific deal page URL (like /crm/deals/123), // don't render the CRM dashboard content, to allow the router to render the DealEditPage instead const isDealDetailPage = location.pathname.match(/\/crm\/deals\/[^/]+$/); if (isDealDetailPage) { return null; // Return null to let the router handle rendering DealEditPage } return ( <div className="space-y-6 overflow-x-hidden min-h-0 max-w-full"> {/* Tabs */} <div className="border-b border-gray-200 dark:border-gray-700 mx-4 md:mx-0"> <nav className="-mb-px flex space-x-4 md:space-x-8 overflow-x-auto pb-1 scrollbar-none"> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "directory" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("directory")} > Directory </button> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "deals" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("deals")} > Deals </button> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "contacts" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("contacts")} > Contacts </button> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "companies" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("companies")} > Companies </button> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "reports" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("reports")} > Reports </button> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "hubspot" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("hubspot")} > HubSpot </button> <button className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${ activeTab === "data-management" ? "border-blue-500 text-blue-600 dark:text-blue-400" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600" }`} onClick={() => setActiveTab("data-management")} > Data Management </button> </nav> </div> {/* Tab content */} <div className="overflow-y-auto max-h-[calc(100vh-180px)] h-auto px-4 md:px-0"> {activeTab === "deals" && ( <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4 md:p-6 w-full overflow-x-hidden flex flex-col"> <div className="flex justify-end mb-4 space-x-2 flex-shrink-0"> <button onClick={toggleHideInactive} className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${ hideInactive ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300" }`} title={ hideInactive ? "Show all columns" : "Hide inactive columns" } > {hideInactive ? ( <EyeIcon className="w-4 h-4 mr-1.5" /> ) : ( <EyeSlashIcon className="w-4 h-4 mr-1.5" /> )} {hideInactive ? "Show Inactive" : "Hide Inactive"} </button> <button onClick={toggleCompactView} className={`flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${ isCompactView ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300" }`} title={ isCompactView ? "Switch to normal view" : "Switch to compact view" } > <svg className="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={ isCompactView ? "M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" // Expand icon : "M4 4v4m0 0h4M4 8l5-5m11 5V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" // Compact icon } /> </svg> {isCompactView ? "Normal View" : "Compact View"} </button> </div> <div className="overflow-visible flex-grow"> <DealBoard isCompactView={isCompactView} hideInactive={hideInactive} /> </div> </div> )} {activeTab === "directory" && ( <UnifiedSearch onSelectEntity={(type, id) => { // Navigate to the appropriate page with the selected entity if (type === 'contact') { navigate(`/crm/contacts?selected=${id}`); } else if (type === 'company') { navigate(`/crm/companies?selected=${id}`); } else if (type === 'deal') { navigate(`/crm/deals/${id}`); } }} /> )} {activeTab === "contacts" && <ContactsList />} {activeTab === "companies" && <CompaniesList />} {activeTab === "reports" && <CRMReports />} {activeTab === "hubspot" && <HubSpotIntegration />} {activeTab === "data-management" && <DataManagementPage />} </div> </div> ); }; export default CRMDashboard; 