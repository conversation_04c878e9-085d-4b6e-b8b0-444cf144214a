import { XeroClient } from '../../api/integrations/xero';
import { XeroActivityStatementDisplay } from '../../api/types/xero';

/**
 * Service for handling Xero activity statements operations
 */
export class ActivityStatementService {
  constructor(private readonly client: XeroClient) {}
  
  /**
   * Get activity statements from Xero for display
   * @param tenantId Xero tenant ID
   * @param fromDate Optional date to filter from
   * @returns Processed activity statements for display
   */
  async getActivityStatementsForDisplay(tenantId: string, fromDate?: Date): Promise<XeroActivityStatementDisplay[]> {
    try {
      // Check for necessary scopes
      const tokenSet = this.client.readTokenSet();
      const scopes = tokenSet?.scope?.split(' ') || [];
      
      const hasAccountingScope = scopes.some(scope => 
        scope.includes('accounting.') || 
        scope.includes('accounting/')
      );
      
      if (!hasAccountingScope) {
        console.log('Missing accounting scopes in token, using empty set');
        return [];
      }
      
      // Fetch activity statements from Xero
      console.log('Fetching activity statements from Xero API...');
      
      // Try BAS report first
      try {
        const basResponse = await this.client.getActivityStatements(tenantId, fromDate, 'BAS');
        
        // Check if we got valid data back
        if (basResponse && basResponse.body && basResponse.body.Reports && basResponse.body.Reports.length > 0) {
          console.log(`Found ${basResponse.body.Reports.length} BAS reports`);
          
          // Process the reports data
          return this.extractActivityStatementsFromReports(basResponse.body.Reports, 'BAS');
        } else if (basResponse && basResponse.body && Array.isArray(basResponse.body.Reports)) {
          // Empty array of reports
          console.log('No BAS reports found, will try GST report next');
        }
      } catch (error) {
        console.log('Error fetching BAS reports, this might be expected:', error);
      }
      
      // If BAS fails, try GST report
      try {
        const gstResponse = await this.client.getActivityStatements(tenantId, fromDate, 'GST');
        
        // Check if we got valid data back
        if (gstResponse && gstResponse.body && gstResponse.body.Reports && gstResponse.body.Reports.length > 0) {
          console.log(`Found ${gstResponse.body.Reports.length} GST reports`);
          
          // Process the reports data
          return this.extractActivityStatementsFromReports(gstResponse.body.Reports, 'GST');
        } else if (gstResponse && gstResponse.body && Array.isArray(gstResponse.body.Reports)) {
          // Empty array of reports
          console.log('No GST reports found');
        }
      } catch (error) {
        console.log('Error fetching GST reports, this might be expected:', error);
      }
      
      console.log('No activity statement data returned from Xero');
      return [];
    } catch (error) {
      console.error('Error getting activity statements from Xero:', error);
      // Return empty array for resilience
      return [];
    }
  }
  
  /**
   * Extract activity statement information from Xero reports
   * @param reports Raw report data from Xero API
   * @param type The report type ('BAS' or 'GST')
   * @returns Transformed activity statements for display
   */
  private extractActivityStatementsFromReports(reports: any[], type: string): XeroActivityStatementDisplay[] {
    // This is a conceptual implementation
    // The actual extraction will depend on the structure of the reports
    
    const statements: XeroActivityStatementDisplay[] = [];
    
    for (const report of reports) {
      // Extract report title and date
      const reportTitle = report.ReportTitles?.[0] || 'Unknown Report';
      const reportDate = report.ReportDate ? new Date(report.ReportDate) : new Date();
      
      // Extract report period
      const periodMatch = reportTitle.match(/(\w+)\s+(\d{4})/);
      let taxPeriod = 'Unknown Period';
      
      if (periodMatch) {
        taxPeriod = `${periodMatch[1]} ${periodMatch[2]}`;
      }
      
      // Determine statement type based on report title
      let statementType: 'BAS' | 'IAS' | 'Other' = 'Other';
      if (reportTitle.includes('BAS') || type === 'BAS') {
        statementType = 'BAS';
      } else if (reportTitle.includes('IAS')) {
        statementType = 'IAS';
      }
      
      // Determine frequency based on period name
      let frequency: 'monthly' | 'quarterly' | 'annual' = 'quarterly';
      if (reportTitle.includes('Monthly')) {
        frequency = 'monthly';
      } else if (reportTitle.includes('Annual')) {
        frequency = 'annual';
      }
      
      // Extract due date (this is conceptual, actual implementation will vary)
      const dueDate = new Date(reportDate);
      dueDate.setDate(dueDate.getDate() + 21); // Assuming 21 days after report date
      
      // Extract amount (this is conceptual, actual implementation will vary)
      // Look for specific cells in the report that contain the payment amount
      let amount = 0;
      
      // Try to find amount in the report data
      if (report.Rows) {
        for (const row of report.Rows) {
          // Example: Look for a row with "Total Activity Statement", "Total", etc.
          if (row.Title && (
              row.Title.includes('Total') || 
              row.Title.includes('Pay') || 
              row.Title.includes('GST')
            )) {
            // Extract the amount from the relevant cell
            const lastCellValue = row.Cells?.[row.Cells.length - 1]?.Value;
            if (lastCellValue && !isNaN(parseFloat(lastCellValue))) {
              amount = Math.abs(parseFloat(lastCellValue));
              break;
            }
          }
        }
      }
      
      // Create statement object
      const statement: XeroActivityStatementDisplay = {
        id: `xero-${statementType.toLowerCase()}-${Date.now()}-${Math.random()}`,
        type: statementType,
        taxPeriod,
        dueDate,
        amount,
        status: 'Due', // Default status
        description: `${statementType}: ${taxPeriod}`,
        frequency,
        isAlreadyAdded: false
      };
      
      statements.push(statement);
    }
    
    return statements;
  }
}