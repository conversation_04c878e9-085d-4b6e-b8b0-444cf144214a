
> onbord-financial-dashboard-preview@0.1.0 dev:backend
> nodemon

[33m[nodemon] 3.1.9[39m
[33m[nodemon] to restart at any time, enter `rs`[39m
[33m[nodemon] watching path(s): src/**/*[39m
[33m[nodemon] watching extensions: ts,tsx,js,jsx,json[39m
[32m[nodemon] starting `ts-node --project tsconfig.backend.json src/api/server.ts`[39m
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Initializing XeroClient with config: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
Creating Xero client with config: {
  clientId: '72609...',
  clientSecret: 'present',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
✓ Xero API method "getReportBalanceSheet" available
✓ Xero API method "getReportBankSummary" available
✓ Xero API method "getRepeatingInvoices" available
Found Australian Payroll API
Xero SDK initialized with: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: 9,
  hasPayrollApi: true
}
Initializing HarvestClient...
Harvest credentials: Token=Present, Account ID=Present
Harvest settings: Running in development mode, using data directory: /Users/<USER>/Github/onbord-financial-dashboard/data
Harvest settings: Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
Using database path: /Users/<USER>/Github/onbord-financial-dashboard/data/upstream.db
Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
PRAGMA journal_mode = WAL
PRAGMA busy_timeout = 30000
PRAGMA foreign_keys = ON
Successfully connected to SQLite database with timeouts configured

        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      

            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          
LeadsRepository: Using unified company table
[7:04:02 AM] [INFO] () HubSpot MCP Session Manager initialized {
  sessionId: '***MASKED***',
  requestId: '6e970215-6062-4ed8-8a7f-82490ecacf9b'
}
KnowledgeGraphRepository: Database instance: available

        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      
KnowledgeGraphRepository: All repositories initialized successfully
Proxy trust disabled for development (rate limiting security)
Cookie domain configured as: default (current domain)
Session config: NODE_ENV=development, API_PORT=3002, isLocalDevelopment=true, isPreviewDeployment=false
Using session configuration with cookie settings: {
  secure: false,
  maxAge: *********,
  httpOnly: true,
  sameSite: 'lax',
  domain: '(default)',
  path: '/'
}
Session middleware configured with RedisStore: {
  resave: false,
  saveUninitialized: false,
  cookieSecure: false,
  cookieMaxAge: '7 days',
  sameSite: 'lax',
  domain: 'default (browser determined)'
}
Initializing database...

      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    

      SELECT MAX(version) as version FROM schema_version
    
Current database schema version: 1
Checking for missing tables...

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    
Database initialization complete
Testing activity logging system...
BEGIN TRANSACTION

        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES ('02db9019-b8fe-4a2e-bfe6-460f2255'/*+4 bytes*/, 'system_started', 'Application server started', 'The Onbord Financial Dashboard s'/*+30 bytes*/, 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"port":"3002","timestamp":"2025'/*+22 bytes*/, 0.0, 'normal', 'system', 'system', '2025-06-09T23:04:02.257Z', '2025-06-09T23:04:02.257Z')
      
COMMIT
Initializing cashflow snapshot job...
Initializing cashflow snapshot job...
Cashflow snapshot job scheduled to run at 1:00 AM daily
Cashflow snapshot job initialized and started
Environment configuration:
- Xero integration: Configured
- Harvest integration: Configured
Port 3002 is already in use! The application won't work correctly with Xero.
Please free up port 3002 and restart the application.
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node --project tsconfig.backend.json src/api/server.ts`[39m
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Initializing XeroClient with config: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
Creating Xero client with config: {
  clientId: '72609...',
  clientSecret: 'present',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
✓ Xero API method "getReportBalanceSheet" available
✓ Xero API method "getReportBankSummary" available
✓ Xero API method "getRepeatingInvoices" available
Found Australian Payroll API
Xero SDK initialized with: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: 9,
  hasPayrollApi: true
}
Initializing HarvestClient...
Harvest credentials: Token=Present, Account ID=Present
Harvest settings: Running in development mode, using data directory: /Users/<USER>/Github/onbord-financial-dashboard/data
Harvest settings: Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
Using database path: /Users/<USER>/Github/onbord-financial-dashboard/data/upstream.db
Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
PRAGMA journal_mode = WAL
PRAGMA busy_timeout = 30000
PRAGMA foreign_keys = ON
Successfully connected to SQLite database with timeouts configured

        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      

            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          
LeadsRepository: Using unified company table
[7:14:41 AM] [INFO] () HubSpot MCP Session Manager initialized {
  sessionId: '***MASKED***',
  requestId: 'c3b9affe-abdc-4484-b130-5d0e9f6c8ffb'
}
KnowledgeGraphRepository: Database instance: available

        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      
KnowledgeGraphRepository: All repositories initialized successfully
Proxy trust disabled for development (rate limiting security)
Cookie domain configured as: default (current domain)
Session config: NODE_ENV=development, API_PORT=3002, isLocalDevelopment=true, isPreviewDeployment=false
Using session configuration with cookie settings: {
  secure: false,
  maxAge: *********,
  httpOnly: true,
  sameSite: 'lax',
  domain: '(default)',
  path: '/'
}
Session middleware configured with RedisStore: {
  resave: false,
  saveUninitialized: false,
  cookieSecure: false,
  cookieMaxAge: '7 days',
  sameSite: 'lax',
  domain: 'default (browser determined)'
}
Initializing database...

      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    

      SELECT MAX(version) as version FROM schema_version
    
Current database schema version: 1
Checking for missing tables...

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    
Database initialization complete
Testing activity logging system...
BEGIN TRANSACTION

        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES ('8715e9e5-3814-4dfd-ad99-0091e10e'/*+4 bytes*/, 'system_started', 'Application server started', 'The Onbord Financial Dashboard s'/*+30 bytes*/, 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"port":"3002","timestamp":"2025'/*+22 bytes*/, 0.0, 'normal', 'system', 'system', '2025-06-09T23:14:41.237Z', '2025-06-09T23:14:41.237Z')
      
COMMIT
Initializing cashflow snapshot job...
Initializing cashflow snapshot job...
Cashflow snapshot job scheduled to run at 1:00 AM daily
Cashflow snapshot job initialized and started
Environment configuration:
- Xero integration: Configured
- Harvest integration: Configured
API server running on port 3002
Socket.IO server running on port 3002
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Activity created: system_started - Application server started
✓ Activity logging test successful
Connected to Redis successfully.
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node --project tsconfig.backend.json src/api/server.ts`[39m
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Initializing XeroClient with config: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
Creating Xero client with config: {
  clientId: '72609...',
  clientSecret: 'present',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
✓ Xero API method "getReportBalanceSheet" available
✓ Xero API method "getReportBankSummary" available
✓ Xero API method "getRepeatingInvoices" available
Found Australian Payroll API
Xero SDK initialized with: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: 9,
  hasPayrollApi: true
}
Initializing HarvestClient...
Harvest credentials: Token=Present, Account ID=Present
Harvest settings: Running in development mode, using data directory: /Users/<USER>/Github/onbord-financial-dashboard/data
Harvest settings: Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
Using database path: /Users/<USER>/Github/onbord-financial-dashboard/data/upstream.db
Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
PRAGMA journal_mode = WAL
PRAGMA busy_timeout = 30000
PRAGMA foreign_keys = ON
Successfully connected to SQLite database with timeouts configured

        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      

            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          
LeadsRepository: Using unified company table
[7:14:51 AM] [INFO] () HubSpot MCP Session Manager initialized {
  sessionId: '***MASKED***',
  requestId: '088f239d-6aa4-46f5-b91d-9ea606d4bd01'
}
KnowledgeGraphRepository: Database instance: available

        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      
KnowledgeGraphRepository: All repositories initialized successfully
Proxy trust disabled for development (rate limiting security)
Cookie domain configured as: default (current domain)
Session config: NODE_ENV=development, API_PORT=3002, isLocalDevelopment=true, isPreviewDeployment=false
Using session configuration with cookie settings: {
  secure: false,
  maxAge: *********,
  httpOnly: true,
  sameSite: 'lax',
  domain: '(default)',
  path: '/'
}
Session middleware configured with RedisStore: {
  resave: false,
  saveUninitialized: false,
  cookieSecure: false,
  cookieMaxAge: '7 days',
  sameSite: 'lax',
  domain: 'default (browser determined)'
}
Initializing database...

      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    

      SELECT MAX(version) as version FROM schema_version
    
Current database schema version: 1
Checking for missing tables...

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    
Database initialization complete
Testing activity logging system...
BEGIN TRANSACTION

        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES ('4df06dbc-38a8-4a6d-87d5-c25ac8a9'/*+4 bytes*/, 'system_started', 'Application server started', 'The Onbord Financial Dashboard s'/*+30 bytes*/, 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"port":"3002","timestamp":"2025'/*+22 bytes*/, 0.0, 'normal', 'system', 'system', '2025-06-09T23:14:51.193Z', '2025-06-09T23:14:51.193Z')
      
COMMIT
Initializing cashflow snapshot job...
Initializing cashflow snapshot job...
Cashflow snapshot job scheduled to run at 1:00 AM daily
Cashflow snapshot job initialized and started
Environment configuration:
- Xero integration: Configured
- Harvest integration: Configured
API server running on port 3002
Socket.IO server running on port 3002
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Activity created: system_started - Application server started
✓ Activity logging test successful
Connected to Redis successfully.
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node --project tsconfig.backend.json src/api/server.ts`[39m
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Initializing XeroClient with config: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
Creating Xero client with config: {
  clientId: '72609...',
  clientSecret: 'present',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
✓ Xero API method "getReportBalanceSheet" available
✓ Xero API method "getReportBankSummary" available
✓ Xero API method "getRepeatingInvoices" available
Found Australian Payroll API
Xero SDK initialized with: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: 9,
  hasPayrollApi: true
}
Initializing HarvestClient...
Harvest credentials: Token=Present, Account ID=Present
Harvest settings: Running in development mode, using data directory: /Users/<USER>/Github/onbord-financial-dashboard/data
Harvest settings: Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
Using database path: /Users/<USER>/Github/onbord-financial-dashboard/data/upstream.db
Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
PRAGMA journal_mode = WAL
PRAGMA busy_timeout = 30000
PRAGMA foreign_keys = ON
Successfully connected to SQLite database with timeouts configured

        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      

            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          
LeadsRepository: Using unified company table
[7:15:01 AM] [INFO] () HubSpot MCP Session Manager initialized {
  sessionId: '***MASKED***',
  requestId: '748e15d3-a183-4a9f-8898-faad81bdea3a'
}
KnowledgeGraphRepository: Database instance: available

        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      
KnowledgeGraphRepository: All repositories initialized successfully
Proxy trust disabled for development (rate limiting security)
Cookie domain configured as: default (current domain)
Session config: NODE_ENV=development, API_PORT=3002, isLocalDevelopment=true, isPreviewDeployment=false
Using session configuration with cookie settings: {
  secure: false,
  maxAge: *********,
  httpOnly: true,
  sameSite: 'lax',
  domain: '(default)',
  path: '/'
}
Session middleware configured with RedisStore: {
  resave: false,
  saveUninitialized: false,
  cookieSecure: false,
  cookieMaxAge: '7 days',
  sameSite: 'lax',
  domain: 'default (browser determined)'
}
Initializing database...

      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    

      SELECT MAX(version) as version FROM schema_version
    
Current database schema version: 1
Checking for missing tables...

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    
Database initialization complete
Testing activity logging system...
BEGIN TRANSACTION

        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES ('57932c72-e8b6-4676-a6be-f70360d3'/*+4 bytes*/, 'system_started', 'Application server started', 'The Onbord Financial Dashboard s'/*+30 bytes*/, 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"port":"3002","timestamp":"2025'/*+22 bytes*/, 0.0, 'normal', 'system', 'system', '2025-06-09T23:15:01.842Z', '2025-06-09T23:15:01.842Z')
      
COMMIT
Initializing cashflow snapshot job...
Initializing cashflow snapshot job...
Cashflow snapshot job scheduled to run at 1:00 AM daily
Cashflow snapshot job initialized and started
Environment configuration:
- Xero integration: Configured
- Harvest integration: Configured
Port 3002 is already in use! The application won't work correctly with Xero.
Please free up port 3002 and restart the application.
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node --project tsconfig.backend.json src/api/server.ts`[39m
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Initializing XeroClient with config: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
Creating Xero client with config: {
  clientId: '72609...',
  clientSecret: 'present',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
✓ Xero API method "getReportBalanceSheet" available
✓ Xero API method "getReportBankSummary" available
✓ Xero API method "getRepeatingInvoices" available
Found Australian Payroll API
Xero SDK initialized with: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: 9,
  hasPayrollApi: true
}
Initializing HarvestClient...
Harvest credentials: Token=Present, Account ID=Present
Harvest settings: Running in development mode, using data directory: /Users/<USER>/Github/onbord-financial-dashboard/data
Harvest settings: Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
Using database path: /Users/<USER>/Github/onbord-financial-dashboard/data/upstream.db
Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
PRAGMA journal_mode = WAL
PRAGMA busy_timeout = 30000
PRAGMA foreign_keys = ON
Successfully connected to SQLite database with timeouts configured

        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      

            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          
LeadsRepository: Using unified company table
[7:15:09 AM] [INFO] () HubSpot MCP Session Manager initialized {
  sessionId: '***MASKED***',
  requestId: '6b4c78b2-7b41-46c7-b49b-fa2b11063654'
}
KnowledgeGraphRepository: Database instance: available

        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      
KnowledgeGraphRepository: All repositories initialized successfully
Proxy trust disabled for development (rate limiting security)
Cookie domain configured as: default (current domain)
Session config: NODE_ENV=development, API_PORT=3002, isLocalDevelopment=true, isPreviewDeployment=false
Using session configuration with cookie settings: {
  secure: false,
  maxAge: *********,
  httpOnly: true,
  sameSite: 'lax',
  domain: '(default)',
  path: '/'
}
Session middleware configured with RedisStore: {
  resave: false,
  saveUninitialized: false,
  cookieSecure: false,
  cookieMaxAge: '7 days',
  sameSite: 'lax',
  domain: 'default (browser determined)'
}
Initializing database...

      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    

      SELECT MAX(version) as version FROM schema_version
    
Current database schema version: 1
Checking for missing tables...

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    
Database initialization complete
Testing activity logging system...
BEGIN TRANSACTION

        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES ('79fb4a62-6aa2-4858-8bb5-58a8f672'/*+4 bytes*/, 'system_started', 'Application server started', 'The Onbord Financial Dashboard s'/*+30 bytes*/, 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"port":"3002","timestamp":"2025'/*+22 bytes*/, 0.0, 'normal', 'system', 'system', '2025-06-09T23:15:10.039Z', '2025-06-09T23:15:10.039Z')
      
COMMIT
Initializing cashflow snapshot job...
Initializing cashflow snapshot job...
Cashflow snapshot job scheduled to run at 1:00 AM daily
Cashflow snapshot job initialized and started
Environment configuration:
- Xero integration: Configured
- Harvest integration: Configured
Port 3002 is already in use! The application won't work correctly with Xero.
Please free up port 3002 and restart the application.
[31m[nodemon] app crashed - waiting for file changes before starting...[39m
[32m[nodemon] restarting due to changes...[39m
[32m[nodemon] starting `ts-node --project tsconfig.backend.json src/api/server.ts`[39m
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Initializing XeroClient with config: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
Creating Xero client with config: {
  clientId: '72609...',
  clientSecret: 'present',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: [
    'openid',
    'profile',
    'email',
    'accounting.transactions',
    'accounting.reports.read',
    'accounting.settings',
    'offline_access',
    'payroll.employees',
    'payroll.payruns'
  ]
}
✓ Xero API method "getReportBalanceSheet" available
✓ Xero API method "getReportBankSummary" available
✓ Xero API method "getRepeatingInvoices" available
Found Australian Payroll API
Xero SDK initialized with: {
  clientId: '72609...',
  redirectUri: 'http://localhost:3002/api/xero/callback',
  scopes: 9,
  hasPayrollApi: true
}
Initializing HarvestClient...
Harvest credentials: Token=Present, Account ID=Present
Harvest settings: Running in development mode, using data directory: /Users/<USER>/Github/onbord-financial-dashboard/data
Harvest settings: Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
Using database path: /Users/<USER>/Github/onbord-financial-dashboard/data/upstream.db
Successfully verified write access to /Users/<USER>/Github/onbord-financial-dashboard/data
PRAGMA journal_mode = WAL
PRAGMA busy_timeout = 30000
PRAGMA foreign_keys = ON
Successfully connected to SQLite database with timeouts configured

        SELECT name FROM sqlite_master
        WHERE type='table' AND name='hubspot_imports'
      

            SELECT name FROM pragma_table_info('hubspot_imports')
            WHERE name = 'metadata'
          
LeadsRepository: Using unified company table
[7:15:18 AM] [INFO] () HubSpot MCP Session Manager initialized {
  sessionId: '***MASKED***',
  requestId: '0337f1b1-b813-4a23-bef1-6ac34f0c3310'
}
KnowledgeGraphRepository: Database instance: available

        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='project'
      
KnowledgeGraphRepository: All repositories initialized successfully
Proxy trust disabled for development (rate limiting security)
Cookie domain configured as: default (current domain)
Session config: NODE_ENV=development, API_PORT=3002, isLocalDevelopment=true, isPreviewDeployment=false
Using session configuration with cookie settings: {
  secure: false,
  maxAge: *********,
  httpOnly: true,
  sameSite: 'lax',
  domain: '(default)',
  path: '/'
}
Session middleware configured with RedisStore: {
  resave: false,
  saveUninitialized: false,
  cookieSecure: false,
  cookieMaxAge: '7 days',
  sameSite: 'lax',
  domain: 'default (browser determined)'
}
Initializing database...

      SELECT 1 FROM sqlite_master
      WHERE type='table' AND name='schema_version'
    

      SELECT MAX(version) as version FROM schema_version
    
Current database schema version: 1
Checking for missing tables...

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='note'
    

      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='radar_action_items'
    
Database initialization complete
Testing activity logging system...
BEGIN TRANSACTION

        INSERT INTO activity_feed (
          id, type, subject, description, status,
          entity_type, entity_id, due_date, completed_date,
          company_id, contact_id, deal_id, metadata,
          is_read, importance, created_by, source,
          created_at, updated_at
        ) VALUES ('0756d5c6-5d84-4b8b-91e5-36a4c3b6'/*+4 bytes*/, 'system_started', 'Application server started', 'The Onbord Financial Dashboard s'/*+30 bytes*/, 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '{"port":"3002","timestamp":"2025'/*+22 bytes*/, 0.0, 'normal', 'system', 'system', '2025-06-09T23:15:18.385Z', '2025-06-09T23:15:18.385Z')
      
COMMIT
Initializing cashflow snapshot job...
Initializing cashflow snapshot job...
Cashflow snapshot job scheduled to run at 1:00 AM daily
Cashflow snapshot job initialized and started
Environment configuration:
- Xero integration: Configured
- Harvest integration: Configured
API server running on port 3002
Socket.IO server running on port 3002
Xero callback URL configured as: http://localhost:3002/api/xero/callback
Activity created: system_started - Application server started
✓ Activity logging test successful
Connected to Redis successfully.
[32m[nodemon] restarting due to changes...[39m
