/**
 * API Connection Test
 * 
 * This file tests connections to external APIs used by the application.
 * It serves as both a test and documentation for how to connect to these APIs.
 */
import { isTestEnvironmentConfigured } from './utils';

// We'll need to import the actual service classes
// These imports might need adjustment based on actual file paths
import { getXeroService } from '../../src/services/xero';
import { getHarvestService } from '../../src/services/harvest';

// Skip all tests if environment is not configured
const runTests = isTestEnvironmentConfigured();

describe('External API Connections', () => {
  // Skip the entire suite if credentials are missing
  if (!runTests) {
    it.skip('API tests skipped due to missing credentials', () => {
      // This test will be skipped
    });
    return;
  }

  // Xero connection tests
  describe('Xero API', () => {
    let xeroService: any;

    beforeAll(async () => {
      // Get the Xero service instance
      xeroService = getXeroService();
      
      // Perform authentication if needed
      // Note: This might need adjustment based on actual auth flow
      if (xeroService.authenticate) {
        await xeroService.authenticate();
      }
    });

    it('can retrieve bank accounts', async () => {
      const accounts = await xeroService.getBankAccounts();
      
      // We don't test for specific values, just that the API returns something
      expect(accounts).toBeDefined();
      expect(Array.isArray(accounts)).toBe(true);
      
      // Log for debugging
      console.log(`Retrieved ${accounts.length} bank accounts from Xero`);
      
      // If we have accounts, check their structure
      if (accounts.length > 0) {
        // Just test the structure, not specific values
        expect(accounts[0]).toHaveProperty('id');
        expect(accounts[0]).toHaveProperty('name');
      }
    });
  });

  // Harvest connection tests
  describe('Harvest API', () => {
    let harvestService: any;

    beforeAll(() => {
      // Get the Harvest service instance
      harvestService = getHarvestService();
    });

    it('can retrieve projects', async () => {
      const projects = await harvestService.getProjects();
      
      // We don't test for specific values, just that the API returns something
      expect(projects).toBeDefined();
      expect(Array.isArray(projects)).toBe(true);
      
      // Log for debugging
      console.log(`Retrieved ${projects.length} projects from Harvest`);
      
      // If we have projects, check their structure
      if (projects.length > 0) {
        // Just test the structure, not specific values
        expect(projects[0]).toHaveProperty('id');
        expect(projects[0]).toHaveProperty('name');
      }
    });
  });
});
