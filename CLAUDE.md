# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build & Test Commands

### Development Commands
- `npm run dev`: Start both frontend (Vite) and backend (Node.js)
- `npm run dev:backend`: Run backend only (Node.js) with nodemon
- `npm run dev:frontend`: Run frontend only (Vite) on port 5173
- `npm run dev:mock`: Start development with backend mock auth (bypasses Xero OAuth)
- `npm run build`: Build application for production
- `npm run lint`: Run ESLint on src directory
- `npm run kill-ports`: Kill processes on ports 3002 and 5173

### Test Commands
- `npm test`: Run all Jest tests
- `npm test:unit`: Run unit tests only
- `npm test:integration`: Run integration tests
- `npm test:e2e`: Run Playwright e2e tests
- `npm test:e2e:ui`: Run Playwright tests with UI debugger
- `npm test:e2e:headed`: Run Playwright tests in headed mode
- `npm test:e2e:debug`: Run Playwright tests in debug mode
- `npm test:schema`: Run database schema validation tests
- `npm test:mock`: Run tests with mock authentication enabled
- `npm test:real-data`: Run tests against real API data (requires API keys)
- `npm test -- --coverage`: Generate test coverage report
- `npm test -- --watch`: Run tests in watch mode for development
- `npm test -- [path]`: Run specific test file or directory

### Database Commands
- `npm run migrate`: Run database migrations
- `npm run migrate:status`: Show migration status
- `npm run validate-schema`: Validate database schema structure

## Code Style & Architecture
- TypeScript-first with proper interfaces and type definitions
- Functional React components with hooks (no class components)
- Controllers handle request/response, services contain business logic
- PascalCase for components and types, camelCase for functions and variables
- Group imports: external first, then internal, use aliases (@/* for src/*)
- Error handling: Use utilities in `src/utils/error.ts` with specific error types
- Adaptive responsive design using container queries over device detection
- Container/Presentation pattern for React components
- Repository pattern for data access abstraction
- Service layer for business logic encapsulation
- Adapter pattern for normalizing external API data
- Cache-aside pattern for performance optimization
- CSS Architecture: Foundation layers in `src/frontend/styles/foundation.css` using Tailwind `@layer`
- Component-specific styles using container queries (`@container`) for responsive behavior
- Variant system in `src/frontend/utils/component-variants.ts` for consistent styling
- **Tailwind Configuration**: Extended theme in `tailwind.config.js` with:
  - Fluid typography/spacing system using `clamp()` for responsive scaling
  - Consolidated color system mapped from CSS variables
  - Custom animations (fade, slide, scale) and transitions
  - Container queries plugin enabled for component-level responsiveness

## Project-Specific Conventions
- **Financial Color Standards**:
  - Green ONLY for income/positive transactions
  - Red/accent ONLY for expenses/negative values
  - Category colors: Payroll=blue, Software=purple, Taxes=indigo
- **Smart Forecast System**: Refer to DEVELOPMENT-GUIDE.md for projection logic rules
- **Testing Strategy**: 
  - **Test Philosophy**: Focus on business behavior over implementation details (see TESTING-BEST-PRACTICES.md)
  - **Architecture Limitations**: Repository testing limited by global database singleton (see docs/technical/testing-architecture-limitations.md)
  - **Unit Tests**: Jest + React Testing Library for component and service testing
  - **Integration Tests**: Preferred for data access layer due to architecture constraints
  - **E2E Tests**: Playwright for critical user workflows
  - **Real Data Tests**: Optional tests against live APIs in `tests/real-data/`
  - **Mock Mode** for development without authentication:
    - **Backend Mock**: `npm run dev:mock` - bypasses all auth at server level
    - Only activates with explicit environment flags (never in production)
  - **Test Organization**: 
    - `tests/unit/` - Component behavior and service logic tests
    - `tests/integration/` - API and cross-module workflow tests
    - `tests/e2e/` - Browser automation tests
    - `tests/real-data/` - Live API integration tests
  - **What We Test**:
    - Business rules and validation logic
    - User-visible behavior and workflows
    - Financial calculations and projections
    - API contracts and data transformations
    - Error handling and edge cases
  - **What We Don't Test**:
    - Implementation details (DOM structure, CSS classes)
    - Framework behavior (React rendering, routing)
    - Mock interactions (avoid testing the test)
- **Performance Optimizations**:
  - Memoization for expensive calculations
  - Efficient data transformation using map/reduce
  - Cache invalidation strategies by service
  - Environment-specific caching TTLs

## Git & PR Guidelines
- **ALWAYS use the "preview" branch** for changes unless instructed otherwise
- Never push directly to main branch without explicit confirmation
- PR descriptions should be clear, reference issues, include screenshots for UI changes

## Database Schema & Data Model
- SQLite database with persistent storage on Render.com (mounted at `/data/upstream.db`)
- **31 Core Tables** defined in `/migrations/000_unified_schema.sql` (29 active after removing unused)
- **Migration System**: Simple SQL-based migrations in `migrations/` directory
- **Schema Organization by Category**:
  - **CRM (8 tables)**: company, contact, deal, note, contact_company, contact_role, deal_estimate, radar_action_items
  - **Financial (6 tables)**: estimate, estimate_allocation (with `sort_index` for drag-drop ordering), estimate_time_allocation, expense, project, project_contact
  - **Integration (4 tables)**: harvest_invoice_cache, hubspot_settings, field_ownership, settings
  - **Enrichment (3 tables)**: company_enrichment, contact_enrichment, enrichment_log
  - **Audit/System (4 tables)**: activity_feed, change_log, migrations, cashflow_snapshot
  - **Potentially Unused (2 tables)**: company_relationship, opportunity_intelligence
- **Repository Pattern**: All data access goes through repository classes in `src/api/repositories/`
- **Field Ownership Tracking**: Prevents conflicts during external system synchronization
  - Tracks which system (Manual, HubSpot, Estimate, Deal, System) controls each field
  - Database stores lowercase values (e.g., 'estimate', 'hubspot', 'deal', 'system')
  - TypeScript uses PascalCase DataSource types (e.g., 'Estimate', 'HubSpot')
  - System automatically normalizes for comparison
  - Key utilities in `src/utils/deal-tracking.ts`
  - CHECK constraint enforces valid owner_system values
- **Audit Trail System**: Complete change tracking with activity feed
- **External System Integration**:
  - Company linking to HubSpot (`hubspot_id`), Harvest (`harvest_id`), and Xero (`xero_id`)
  - Contact linking to HubSpot (`hubspot_id`) and Harvest (`harvest_user_id`)
  - Deal linking to HubSpot deals (`hubspot_id`)
  - Project linking to Harvest projects (`harvest_project_id`)
- **Relationship Management**:
  - Deal-Estimate linking (one-to-one via deal_estimate table)
  - Contact-Company relations (many-to-many via contact_company with roles)
  - Contact roles in deals (via contact_role table with flexible permissions)
  - Project team members with allocation tracking (via project_contact)
- **Key Design Principles**:
  - All tables have audit fields: created_at, updated_at, created_by, updated_by, deleted_at
  - Foreign keys use ON DELETE SET NULL to preserve data (except junction tables which use CASCADE)
  - Soft delete support with deleted_at fields on ALL tables
  - Strategic indexes for optimal query performance
  - JSON fields for flexible metadata and custom fields
- **Important Schema Notes**:
  - `expense` table uses `date` column (not `start_date`)
  - `estimate` table has both estimate dates AND project dates
  - `note` table supports threaded conversations
  - `estimate_allocation` (with drag-drop reorderable rows via `sort_index`) and `estimate_time_allocation` for detailed resource planning
  - Enrichment tables (company_enrichment, contact_enrichment) for external data augmentation
  - Field ownership system tracks which external system controls each field
- **See**: `/docs/technical/data-model.md` for complete reference and `/docs/technical/data-model/CLEAN-SLATE-DATA-MODEL-ANALYSIS.md` for design rationale

## Key Libraries
### React & Frontend
- Uses functional components with hooks (useState, useEffect, useMemo, useContext)
- React Query for data fetching with proper error handling and loading states
- TailwindCSS for styling with project-specific custom components
- Recharts for financial visualizations with consistent tooltip and responsive components
- **Context Providers**: Multiple contexts for state management:
  - `UserContext`: User authentication and profile data
  - `LoadingContext`: Global loading state management
  - `EventContext`: Event bus for cross-component communication
  - `FloatingPanelsContext`: Manages floating UI panels state
  - `SearchContext`: Global search functionality
- **UI Libraries**: 
  - `cmdk`: Command menu/palette functionality
  - `react-dnd`: Drag and drop for Kanban boards, time allocation grid, and UI interactions
  - `react-force-graph-2d`: Knowledge graph visualization
  - `xml2js`: XML parsing for data enrichment APIs
- **Custom Hooks Pattern**: Domain-specific hooks in `src/frontend/hooks/`:
  - `useEstimate*`: Suite of hooks for estimate management
  - `useAuthStatus`: Authentication state management
  - `useMediaQuery`: Responsive design utilities

### Backend & API Integration
- Express for RESTful API endpoints with consistent response structures
- Xero API for accounting data with proper OAuth2 authentication
- Harvest API for time tracking and project management integration
- HubSpot API for CRM synchronization with OAuth support
- Redis for session management and caching (required in production)
- Socket.io for real-time features
- Cache responses appropriately to minimize external API calls
- Robust error handling with exponential backoff for API requests
- Rate limiting management for external APIs
- Parallel data fetching for performance optimization
- Circuit breaker pattern for external service failures
- Caching mechanism with configurable TTL per service
- Field mapping between external systems and internal data model
- **Middleware**: Security, validation, and OAuth error handling in `src/api/middleware/`
- **Database Utilities**: Timeout handling for database operations in `src/api/utils/`
- **Network API**: Advanced CRM endpoints in `src/api/routes/crm/network.ts`:
  - `/api/crm/network/opportunities/:companyId`: AI-powered opportunity intelligence
  - `/api/crm/network/contacts/:id/project-history`: Harvest project history for contacts
  - `/api/crm/network/relationships/*`: Relationship network management
  - `/api/crm/network/team/coverage/*`: Team coverage analytics

## Core Application Features
### Smart Forecast System
The Smart Forecast system is a central feature that automatically generates income projections based on:
- Remaining project budgets from Harvest
- Uninvoiced work already completed
- Project-specific invoice frequencies and payment terms
- Retrospective billing model for invoice projections
- Three key filtering rules: payment terms, real invoice duplicate, uninvoiced work
- Projected vs. real invoice distinction to prevent double-counting
- Key files: `src/services/cashflow/projection-filter-service.ts`, `src/services/cashflow/daily-cashflow-service.ts`

### Cashflow Projection
Visualizes financial projections with:
- Interactive chart with scenarios (worst-case, expected, best-case)
- Color-coded transaction types (income/expense)
- Projection auditing with detailed decision tracking
- Key components: `src/frontend/components/ForwardProjection/CashflowChart.tsx`, `src/frontend/components/ForwardProjection/TransactionsList.tsx`

### CRM & Deal Management
- Enhanced Kanban board for deal pipeline visualization
- Deal editing with custom fields and internal estimate linking (Harvest estimates are read-only)
- Deal probability visualization for financial forecasting
- **Opportunity Intelligence**: AI-powered business opportunity detection
  - Coverage risk assessment and team assignment recommendations
  - Project renewal tracking for Harvest projects
  - Network expansion analysis based on relationship mapping
  - Automated scoring and priority recommendations
- **Project History Tracking**: Comprehensive work history for Harvest-linked contacts
- **Relationship Network Management**: Visual mapping of professional relationships
- **Team Coverage Analytics**: Risk assessment and team optimization
- Key components: `src/frontend/components/CRM/Board/`, `src/frontend/components/CRM/DealEdit/`, `src/frontend/components/CRM/OpportunityIntelligence/`, `src/frontend/components/CRM/ProjectHistory/`

### Knowledge Graph Visualization
- Interactive network visualization of business relationships and entities
- Force-directed graph layout similar to Obsidian's graph view
- Supports companies, contacts, deals, projects, and estimates
- Real-time interactions: hover, click, drag, zoom
- Entity type filtering and search functionality
- Performance optimized with configurable node limits (default: 1000, max: 2000)
- Key components: `src/frontend/components/Leads/KnowledgeGraph/KnowledgeGraph.tsx`
- Backend: `src/api/repositories/knowledge-graph-repository.ts`, `src/api/routes/knowledge-graph.ts`

### Data Enrichment System
- External data augmentation for companies and contacts
- **ABN Lookup**: Australian Business Number validation and company information
- Confidence scoring (0.0 to 1.0) for data quality assessment
- TTL-based caching with automatic re-enrichment scheduling
- Comprehensive logging of enrichment attempts and performance metrics
- Extensible architecture for adding new enrichment sources (Clearbit, Apollo, etc.)
- Key components: `src/api/services/enrichment/`, `src/api/repositories/enrichment-repository.ts`
- API endpoints: `/api/enrichment/*` for company/contact enrichment

### Integrations
- **Xero**: Authentication via OAuth2, handles rate limiting with retry logic
- **Harvest**: Projects, invoices, time tracking with API token authentication
- **HubSpot**: CRM data synchronization with bidirectional field mapping
  - Imports companies, deals, contacts with association validation
  - **Notes/Activities Import**: Imports all HubSpot engagements (notes, emails, calls, meetings, tasks)
  - **Association Import**: Imports contact-company and deal-contact relationships
  - Strict import order: Companies → Deals → Contacts → Notes → Associations
  - **Deal Name Synchronization**: HubSpot is the source of truth for deal names. When syncing from HubSpot, deal names automatically update linked estimate project names
  - **Two-Tier Sync System**:
    - **Quick Sync**: Companies, Deals, Contacts only (~30 seconds to 2 minutes)
    - **Full Sync**: Everything including notes/activities/associations (~5-15+ minutes)
  - **Automated Daily Sync**: Full sync runs automatically at 3:00 AM daily via cron job
- **ABN Lookup**: Australian Business Number lookup via Australian Business Register API
- All integrations have robust error handling and rate limiting management
- Key files: `src/api/integrations/xero.ts`, `src/api/integrations/harvest.ts`, `src/api/integrations/base.ts`

### Xero MCP (Model Context Protocol) Integration
- Chat-based interaction with Xero data via `/api/mcp` endpoint
- Comprehensive command set documented in `docs/xero-mcp-*.md`
- Uses `@modelcontextprotocol/sdk` and `@anthropic-ai/sdk`
- Enables natural language queries for Xero accounting data

## Deployment & Environment
- **Platform**: Render.com with persistent disk ($7/month Starter plan)
- **Database**: SQLite mounted at `/data/upstream.db` in production
- **Branch Structure**: 
  - `main` → Production environment (auto-deploy)
  - `preview` → Preview environment (auto-deploy)
- **Deployment**: Standard git workflow with Render auto-deploy
- **Build Process**: Native Render build with `npm ci --legacy-peer-deps --include=dev && npm run build`
- **URLs**:
  - Preview: `https://upstream-preview.onbord.au`
  - Production: `https://upstream.onbord.au`
- See `DEPLOYMENT-GUIDE.md` for complete deployment guide

## Important Development Notes
- **API Keys**: Required for Xero, Harvest, and optionally HubSpot - see README.md for setup
- **Environment Variables**:
  - `REDIS_URL`: Required in production for session/caching
  - `FEEDBACK_EMAIL` and `FEEDBACK_EMAIL_API_KEY`: For Resend-powered feedback emails
  - `HUBSPOT_ACCESS_TOKEN`: For HubSpot CRM integration
  - `HUBSPOT_SYNC_DELETIONS`: Set to 'false' to disable syncing deletions from HubSpot (defaults to enabled)
- **Ports**: API on 3002, Frontend on 5173 (fixed for Xero OAuth compatibility)
- **Development**: `npm run dev` starts both frontend and backend
- **Deployment**: Standard Render auto-deploy on git push (simplified June 2025)
- **Database**: SQLite with automatic migrations and repository pattern
- **Testing**: Real data testing config in `tests/real-data-config.js` for live API integration
- **Build System**: Standard TypeScript + Vite build with ts-node fallback
- **Known Issues**: TypeScript errors in `ProjectionContext.tsx` may affect test runs

## Activity Feed Implementation
- Activity tracking system records all significant events across the application
- Activities are stored in SQLite database with actor, action, target, and metadata
- Frontend displays activities in timeline format with real-time updates via React Query
- Key components: `ActivityFeedPage.tsx`, `ActivityTimeline.tsx`, `ActivityItem.tsx`
- API endpoints: `/api/activity` (GET/POST), uses `activity-service.ts` and `activity-repository.ts`
- Activity types include: company_created, deal_updated, estimate_published, xero_sync, etc.
- Each activity has consistent structure: actor (who), action (what), target (on what), metadata (details)

## Recent Feature Development
- **Harvest Estimate Simplification** (January 2025): 
  - Removed ability to link Harvest estimates to deals (only internal estimates can be linked)
  - Harvest estimates remain visible but are read-only throughout the system
  - Existing Harvest estimate links are preserved but cannot be modified
  - Simplified codebase by removing complex Harvest estimate synchronization logic
  - Clear separation between external read-only data and internal editable data
  - **Modal Consolidation**: Removed SimpleEstimateLinkModal in favor of the enhanced EstimateLinkModal
    - EstimateLinkModal now used throughout the application with consistent styling
    - Updated to match DealLinkModal's modern UI pattern with clickable list items
    - Shows estimate details including value, dates, and status
    - Consistent emerald color scheme for selection states
    - Better error handling and loading states with inline display
  - **Field Ownership System**: Proper tracking of estimate-controlled deal fields
    - When estimates are linked, fields (value, dates, invoice frequency, payment terms) become read-only
    - Field ownership is properly passed to all components that need it
    - LinkedEstimateModal now displays which fields are controlled by the estimate
    - Field ownership is cleared when estimates are unlinked, making fields editable again
- **Client Radar Enhancement**: Modern UI with gradient backgrounds, enhanced company cards, drag-and-drop
- **CRM Directory**: New unified search component in `src/frontend/components/CRM/directory/`
  - **Directory Optimizations**: Removed saved searches panel, hidden deals from view, removed pagination for full data loading
  - **Performance Improvements**: Load all records at once (up to 1000) instead of paginated display
- **Shared CRM Components**: Command palette and contextual side panel in `src/frontend/components/CRM/shared/`
- **Activity Stats Redesign**: Compact modern UI for activity statistics with improved visual hierarchy
- **Knowledge Graph Implementation**: Complete network visualization system
  - **KnowledgeGraph Component**: Interactive force-directed graph using react-force-graph-2d
  - **KnowledgeGraphRepository**: Aggregates all entity relationships for visualization (does not extend BaseRepository)
  - **ProjectRepository**: Full CRUD operations for projects with team and dependency management
  - Migration 004_projects_table.sql adds project, project_contact, and project_dependency tables
  - Production-safe with IF NOT EXISTS checks and table existence validation
- **Data Enrichment System**: External data augmentation infrastructure
  - **ABN Lookup Service**: Australian Business Number validation and company information retrieval
  - **EnrichmentRepository**: Manages external data with confidence scoring and TTL
  - Migration 005_data_enrichment.sql adds company_enrichment, contact_enrichment, and enrichment_log tables
  - **Extensible Architecture**: Framework for adding additional enrichment sources (Clearbit, Apollo, etc.)
- **Estimate Type System** (June 2025):
  - **Estimate Types**: Use 'internal' | 'harvest' (NOT 'draft' | 'harvest')
  - **Internal Estimates**: Created within the system, fully editable, can be linked to deals
  - **Harvest Estimates**: Read-only external estimates, cannot be linked to deals
  - **Database**: CHECK constraint enforces estimate_type IN ('internal', 'harvest')
  - **Important**: The term "draft" is reserved for estimate STATUS (draft/sent/accepted/declined), not TYPE
- **Harvest Decoupling** (January 2025):
  - **Company Reference**: Estimates use `companyId` (UUID string), not `clientId` (Harvest ID number)
  - **No Harvest Requirement**: Companies don't need a `harvest_id` to create internal estimates
  - **Publishing Check**: Harvest ID only required when publishing estimates to Harvest
  - **Clean Separation**: Harvest integration only at import/export boundaries, not in core data model
- **Deal-Estimate Linking Enhancement** (January 2025):
  - **One-Way Name Sync**: Deal name controls estimate project_name (deal → estimate only)
  - **Field Ownership**: System tracks which entity controls which fields
    - Deal controls: estimate's project_name (when linked)
    - Estimate controls: deal's value, dates, invoice frequency, payment terms (NOT name)
  - **Case-Insensitive Ownership**: Field ownership now handles PascalCase/lowercase mismatch
    - Database stores lowercase ('estimate', 'deal', 'hubspot')
    - TypeScript uses PascalCase ('Estimate', 'Deal', 'HubSpot')
    - Automatic normalization for comparisons
  - **Company Consistency**: Client/company field is read-only in estimates when linked to ensure consistency
  - **Unlink from Estimate**: Added "Unlink from Deal" button in EstimatePage header
  - **Visual Indicators**: Fields show when they're synced/controlled by another entity
  - **Batch API Optimization**: Deal estimates loaded efficiently using chunked batch requests
  - **HubSpot Sync Fix** (December 2025): Deal names can always be edited and synced from HubSpot
    - Deal name is never locked by estimates
    - HubSpot updates work properly even when deals are linked to estimates
    - Users can always edit deal names in the UI
- **HubSpot Sync Enhancement** (December 2025): Added two-tier sync system
  - **Quick Sync**: New option for fast sync of core entities only (companies, deals, contacts)
  - **Full Sync**: Complete sync including notes, activities, and associations
  - **Automated Daily Sync**: Full sync runs automatically at 3:00 AM via cron job
  - **UI Improvements**: Clear sync type indicators, last sync timestamps, import history enhancements
  - **Key files**: `src/jobs/hubspot-sync-job.ts`, `src/services/hubspot/job-initializer.ts`
- **Testing Architecture Refactor** (June 2025):
  - **Repository Testing Limitations**: Global database singleton prevents proper unit testing
  - **Test Philosophy Change**: Focus on business behavior over implementation details
  - **Removed Low-Value Tests**: Deleted tests that only verified method existence
  - **Integration Tests Preferred**: For data access layer due to architecture constraints
  - **See**: `TESTING-BEST-PRACTICES.md` and `docs/technical/testing-architecture-limitations.md`
- **HubSpot Deal Deletion Sync** (June 2025):
  - **Missing = Deleted Pattern**: Deals that exist in Upstream but not in HubSpot are soft deleted during sync
  - **Soft Delete Only**: Preserves audit trail and allows recovery if needed
  - **Configurable**: Set `HUBSPOT_SYNC_DELETIONS=false` to disable this behavior
  - **Safe Cascading**: Deal-estimate links cleaned up automatically by CASCADE constraint
  - **Activity Logging**: Deletions logged to activity feed with reason
  - **Manual Deals Unaffected**: Only affects deals that have a HubSpot ID
- **Time Allocation Grid Enhancement** (June 2025):
  - **Drag-and-Drop Reordering**: Added ability to reorder estimate allocations by dragging rows
  - **Database Change**: Added `sort_index` column to `estimate_allocation` table
  - **Component**: New `TimeAllocationGridEnhanced` component with react-dnd integration
  - **Migration**: Applied via manual SQL execution in production (field_ownership constraint also fixed)
  - **API Endpoint**: `/api/estimates/drafts/:id/allocation-order` to update sort order
  - **User Experience**: Smooth drag feedback with visual indicators and automatic saving

## Important Instruction Reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.
