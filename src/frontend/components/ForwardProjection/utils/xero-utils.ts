import { Transaction } from '../../../../types/financial'; import { isXeroSource } from '../../../../constants/xero'; /** * Checks if a transaction is from Xero * * This function determines if a transaction originated from Xero (not a payment TO Xero). * It uses specific source prefixes to make this determination rather than broad string matching. */ export function isXeroTransaction(transaction: Transaction): boolean { // Check if the source indicates Xero origin if (transaction.source && typeof transaction.source === 'string' && isXeroSource(transaction.source)) { return true; } // Check if the transaction has metadata with a specific xero source if (transaction.metadata?.source && typeof transaction.metadata.source === 'string' && isXeroSource(transaction.metadata.source)) { return true; } // Check for metadata that explicitly marks this as from Xero if (transaction.metadata?.isFromXero === true) { return true; } // Check for custom expense types with specific Xero source metadata if (transaction.type.startsWith('custom_expense_') && transaction.metadata?._source?.type && typeof transaction.metadata._source.type === 'string' && transaction.metadata._source.type.startsWith('xero_')) { return true; } return false; } 