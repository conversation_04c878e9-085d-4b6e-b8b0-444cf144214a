import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/repositories/estimate-allocation-repository');
jest.mock('../../../../src/api/repositories/estimate-time-allocation-repository');
jest.mock('../../../../src/api/repositories/estimate-drafts-repository');
jest.mock('../../../../src/api/services/activity-service');
jest.mock('../../../../src/utils/backend-logger');

describe('Estimates API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      user: { id: 'user-123', email: '<EMAIL>' },
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/estimates', () => {
    it('should fetch all estimates', async () => {
      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const mockEstimates = [
        {
          id: 1,
          estimate_name: 'Q1 2024 Project',
          date_sent: '2024-01-01',
          valid_until: '2024-01-31',
          total_hours: 160,
          total_cost: 16000,
          status: 'draft',
        },
        {
          id: 2,
          estimate_name: 'Q2 2024 Project',
          date_sent: '2024-04-01',
          valid_until: '2024-04-30',
          total_hours: 200,
          total_cost: 20000,
          status: 'published',
        },
      ];
      EstimateAllocationRepository.prototype.getAllEstimates = jest.fn().mockResolvedValue(mockEstimates);

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateAllocationRepository.prototype.getAllEstimates).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockEstimates);
    });

    it('should handle errors when fetching estimates', async () => {
      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const error = new Error('Database error');
      EstimateAllocationRepository.prototype.getAllEstimates = jest.fn().mockRejectedValue(error);

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('GET /api/estimates/:id', () => {
    it('should fetch a specific estimate with allocations', async () => {
      mockRequest.params = { id: '1' };
      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const EstimateTimeAllocationRepository = (await import('../../../../src/api/repositories/estimate-time-allocation-repository')).default;
      
      const mockEstimate = {
        id: 1,
        estimate_name: 'Q1 2024 Project',
        total_hours: 160,
        total_cost: 16000,
      };
      const mockAllocations = [
        {
          id: 1,
          harvest_user_id: 123,
          first_name: 'John',
          last_name: 'Doe',
          role: 'Developer',
          hourly_rate: 100,
        },
      ];
      const mockTimeAllocations = [
        {
          id: 1,
          week_identifier: '2024-W01',
          days: 5,
        },
      ];

      EstimateAllocationRepository.prototype.getEstimateById = jest.fn().mockResolvedValue(mockEstimate);
      EstimateAllocationRepository.prototype.getAllocationsByEstimateId = jest.fn().mockResolvedValue(mockAllocations);
      EstimateTimeAllocationRepository.prototype.getTimeAllocationsByEstimateId = jest.fn().mockResolvedValue(mockTimeAllocations);

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateAllocationRepository.prototype.getEstimateById).toHaveBeenCalledWith(1);
      expect(mockResponse.json).toHaveBeenCalledWith({
        ...mockEstimate,
        allocations: mockAllocations,
        timeAllocations: mockTimeAllocations,
      });
    });

    it('should handle not found errors', async () => {
      mockRequest.params = { id: '999' };
      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      EstimateAllocationRepository.prototype.getEstimateById = jest.fn().mockResolvedValue(null);

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Estimate not found' });
    });
  });

  describe('POST /api/estimates', () => {
    it('should create a new estimate', async () => {
      mockRequest.body = {
        estimate_name: 'New Project',
        client_name: 'Test Client',
        date_sent: '2024-01-15',
        valid_until: '2024-02-15',
        start_date: '2024-02-01',
        end_date: '2024-03-31',
        total_hours: 320,
        total_cost: 32000,
        allocations: [
          {
            harvest_user_id: 123,
            first_name: 'John',
            last_name: 'Doe',
            role: 'Developer',
            hourly_rate: 100,
          },
        ],
        timeAllocations: [
          {
            week_identifier: '2024-W05',
            days: 5,
          },
        ],
      };

      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const EstimateTimeAllocationRepository = (await import('../../../../src/api/repositories/estimate-time-allocation-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockCreatedEstimate = { id: 3, ...mockRequest.body };
      EstimateAllocationRepository.prototype.createEstimate = jest.fn().mockResolvedValue(mockCreatedEstimate);
      EstimateAllocationRepository.prototype.createAllocation = jest.fn().mockResolvedValue({ id: 1 });
      EstimateTimeAllocationRepository.prototype.createTimeAllocation = jest.fn().mockResolvedValue({ id: 1 });
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateAllocationRepository.prototype.createEstimate).toHaveBeenCalled();
      expect(EstimateAllocationRepository.prototype.createAllocation).toHaveBeenCalled();
      expect(EstimateTimeAllocationRepository.prototype.createTimeAllocation).toHaveBeenCalled();
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_created',
        target_type: 'estimate',
        target_id: '3',
        metadata: {
          estimate_name: 'New Project',
          total_hours: 320,
          total_cost: 32000,
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockCreatedEstimate);
    });
  });

  describe('PUT /api/estimates/:id', () => {
    it('should update an existing estimate', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        estimate_name: 'Updated Project',
        total_hours: 400,
        total_cost: 40000,
      };

      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockUpdatedEstimate = { id: 1, ...mockRequest.body };
      EstimateAllocationRepository.prototype.updateEstimate = jest.fn().mockResolvedValue(mockUpdatedEstimate);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.put
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateAllocationRepository.prototype.updateEstimate).toHaveBeenCalledWith(1, mockRequest.body);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_updated',
        target_type: 'estimate',
        target_id: '1',
        metadata: {
          changes: ['estimate_name', 'total_hours', 'total_cost'],
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUpdatedEstimate);
    });
  });

  describe('DELETE /api/estimates/:id', () => {
    it('should delete an estimate', async () => {
      mockRequest.params = { id: '1' };

      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      EstimateAllocationRepository.prototype.deleteEstimate = jest.fn().mockResolvedValue(true);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateAllocationRepository.prototype.deleteEstimate).toHaveBeenCalledWith(1);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_deleted',
        target_type: 'estimate',
        target_id: '1',
        metadata: {},
      });
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
    });
  });

  describe('POST /api/estimates/:id/publish', () => {
    it('should publish an estimate', async () => {
      mockRequest.params = { id: '1' };

      const EstimateAllocationRepository = (await import('../../../../src/api/repositories/estimate-allocation-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockPublishedEstimate = {
        id: 1,
        estimate_name: 'Q1 2024 Project',
        status: 'published',
        published_at: '2024-01-15T00:00:00Z',
      };
      EstimateAllocationRepository.prototype.updateEstimate = jest.fn().mockResolvedValue(mockPublishedEstimate);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/publish'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateAllocationRepository.prototype.updateEstimate).toHaveBeenCalledWith(1, {
        status: 'published',
        published_at: expect.any(String),
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_published',
        target_type: 'estimate',
        target_id: '1',
        metadata: {
          estimate_name: 'Q1 2024 Project',
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockPublishedEstimate);
    });
  });

  describe('GET /api/estimates/drafts', () => {
    it('should fetch draft estimates', async () => {
      const EstimateDraftsRepository = (await import('../../../../src/api/repositories/estimate-drafts-repository')).default;
      const mockDrafts = [
        {
          id: 1,
          name: 'Draft 1',
          data: { estimate_name: 'Draft Project 1' },
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 2,
          name: 'Draft 2',
          data: { estimate_name: 'Draft Project 2' },
          created_at: '2024-01-02T00:00:00Z',
          updated_at: '2024-01-02T00:00:00Z',
        },
      ];
      EstimateDraftsRepository.prototype.getAllDrafts = jest.fn().mockResolvedValue(mockDrafts);

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/drafts'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateDraftsRepository.prototype.getAllDrafts).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockDrafts);
    });
  });

  describe('POST /api/estimates/drafts', () => {
    it('should create a draft estimate', async () => {
      mockRequest.body = {
        name: 'New Draft',
        data: {
          estimate_name: 'Draft Project',
          total_hours: 100,
        },
      };

      const EstimateDraftsRepository = (await import('../../../../src/api/repositories/estimate-drafts-repository')).default;
      const mockCreatedDraft = {
        id: 3,
        ...mockRequest.body,
        created_at: '2024-01-15T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
      };
      EstimateDraftsRepository.prototype.createDraft = jest.fn().mockResolvedValue(mockCreatedDraft);

      const estimatesRoutes = await import('../../../../src/api/routes/estimates');
      const handler = estimatesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/drafts' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(EstimateDraftsRepository.prototype.createDraft).toHaveBeenCalledWith(mockRequest.body);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockCreatedDraft);
    });
  });
});