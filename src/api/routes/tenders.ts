/**
 * Tender API Routes
 * 
 * Handles tender qualification workflow
 */

import express, { Request, Response } from 'express';
import { TenderRepository } from '../repositories/tender-repository';
import { TenderIngestionService } from '../services/tender-ingestion-service';
import { createRateLimiter } from '../middleware/security';
import { TenderQualificationStatus } from '../../types/shared-types';

const router = express.Router();

// Initialize services
const tenderRepository = new TenderRepository();
const tenderIngestionService = new TenderIngestionService();

// Rate limiter for webhook endpoint
const webhookLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 10, // 10 requests per minute
  message: 'Too many webhook requests'
});

/**
 * GET /api/tenders
 * Get all tenders with optional filtering
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const { qualificationStatus, includeDeleted } = req.query;
    
    const filters = {
      qualificationStatus: qualificationStatus as TenderQualificationStatus,
      includeDeleted: includeDeleted === 'true'
    };
    
    const tenders = tenderRepository.getAllTenders(filters);
    res.json(tenders);
  } catch (error) {
    console.error('Error fetching tenders:', error);
    res.status(500).json({ 
      error: 'Failed to fetch tenders',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/tenders/stats
 * Get tender statistics
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const stats = tenderIngestionService.getTenderStats();
    res.json(stats);
  } catch (error) {
    console.error('Error fetching tender stats:', error);
    res.status(500).json({ 
      error: 'Failed to fetch tender statistics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/tenders/closing-soon
 * Get tenders closing within specified days
 */
router.get('/closing-soon', async (req: Request, res: Response) => {
  try {
    const days = parseInt(req.query.days as string) || 7;
    const tenders = tenderRepository.getTendersClosingSoon(days);
    res.json(tenders);
  } catch (error) {
    console.error('Error fetching tenders closing soon:', error);
    res.status(500).json({ 
      error: 'Failed to fetch tenders closing soon',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/tenders/:id
 * Get a specific tender
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const tender = tenderRepository.getTenderById(id);
    
    if (!tender) {
      return res.status(404).json({ 
        error: 'Tender not found' 
      });
    }
    
    res.json(tender);
  } catch (error) {
    console.error('Error fetching tender:', error);
    res.status(500).json({ 
      error: 'Failed to fetch tender',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/tenders/webhook
 * Webhook endpoint for receiving tender emails
 * 
 * Supports both formats:
 * 1. Original format:
 * {
 *   "subject": "New Tender: GEN250120",
 *   "body": "Email content with tender details",
 *   "from": "<EMAIL>",
 *   "timestamp": "2025-01-06T10:00:00Z"
 * }
 * 
 * 2. Deliverhook format:
 * {
 *   "subject": "New Tender: GEN250120",
 *   "text": "Email content with tender details",
 *   "from": { "address": "<EMAIL>", "name": "Sender Name" },
 *   "date": "Mon, 17 Mar 2025 14:32:00 +0000"
 * }
 */
router.post('/webhook', webhookLimiter, async (req: Request, res: Response) => {
  try {
    // Handle both formats
    const emailBody = req.body.body || req.body.text;
    const subject = req.body.subject;
    const from = typeof req.body.from === 'string' 
      ? req.body.from 
      : req.body.from?.address || req.body.from?.email;
    const timestamp = req.body.timestamp || req.body.date;
    
    // Basic validation
    if (!emailBody) {
      return res.status(400).json({ 
        error: 'Missing email body' 
      });
    }
    
    // Log webhook receipt
    console.log('Tender webhook received:', {
      subject,
      from,
      timestamp,
      bodyLength: emailBody.length
    });
    
    // Process the tender email
    const tender = await tenderIngestionService.processTenderEmail(emailBody);
    
    if (!tender) {
      return res.status(422).json({ 
        error: 'Failed to parse tender email',
        message: 'Email content did not match expected tender format'
      });
    }
    
    res.status(201).json({
      success: true,
      tender: {
        id: tender.id,
        requestNo: tender.requestNo,
        summary: tender.summary
      }
    });
  } catch (error) {
    console.error('Error processing tender webhook:', error);
    res.status(500).json({ 
      error: 'Failed to process tender email',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/tenders
 * Manually create a tender
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const tenderData = req.body;
    
    // Validate required fields
    if (!tenderData.requestNo || !tenderData.summary || 
        !tenderData.issuedBy || !tenderData.closingDate) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['requestNo', 'summary', 'issuedBy', 'closingDate']
      });
    }
    
    // Check if tender already exists
    const existingTender = tenderRepository.getTenderByRequestNo(tenderData.requestNo);
    if (existingTender) {
      return res.status(409).json({ 
        error: 'Tender already exists',
        tenderId: existingTender.id
      });
    }
    
    const tender = tenderRepository.createTender(tenderData);
    
    if (!tender) {
      return res.status(500).json({ 
        error: 'Failed to create tender' 
      });
    }
    
    res.status(201).json(tender);
  } catch (error) {
    console.error('Error creating tender:', error);
    res.status(500).json({ 
      error: 'Failed to create tender',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/tenders/:id
 * Update a tender
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const tender = tenderRepository.updateTender(id, updateData);
    
    if (!tender) {
      return res.status(404).json({ 
        error: 'Tender not found' 
      });
    }
    
    res.json(tender);
  } catch (error) {
    console.error('Error updating tender:', error);
    res.status(500).json({ 
      error: 'Failed to update tender',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/tenders/:id/qualify
 * Qualify a tender (move through workflow)
 */
router.post('/:id/qualify', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status, reason, qualifiedBy } = req.body;
    
    // Validate status
    const validStatuses: TenderQualificationStatus[] = ['new', 'reviewing', 'not_interested', 'interested'];
    if (!status || !validStatuses.includes(status)) {
      return res.status(400).json({ 
        error: 'Invalid qualification status',
        validStatuses
      });
    }
    
    const tender = await tenderIngestionService.qualifyTender(
      id, 
      status, 
      reason,
      qualifiedBy || req.headers['x-user-id'] as string
    );
    
    if (!tender) {
      return res.status(404).json({ 
        error: 'Tender not found' 
      });
    }
    
    res.json(tender);
  } catch (error) {
    console.error('Error qualifying tender:', error);
    res.status(500).json({ 
      error: 'Failed to qualify tender',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/tenders/:id
 * Soft delete a tender
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const success = tenderRepository.deleteTender(id);
    
    if (!success) {
      return res.status(404).json({ 
        error: 'Tender not found' 
      });
    }
    
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting tender:', error);
    res.status(500).json({ 
      error: 'Failed to delete tender',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/tenders/company/:companyId
 * Get tenders for a specific company
 */
router.get('/company/:companyId', async (req: Request, res: Response) => {
  try {
    const { companyId } = req.params;
    const tenders = tenderRepository.getTendersByCompany(companyId);
    res.json(tenders);
  } catch (error) {
    console.error('Error fetching tenders by company:', error);
    res.status(500).json({ 
      error: 'Failed to fetch tenders',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;