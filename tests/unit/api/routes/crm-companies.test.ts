import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/repositories/company-repository');
jest.mock('../../../../src/api/repositories/company-relationship-repository');
jest.mock('../../../../src/api/services/activity-service');
jest.mock('../../../../src/utils/backend-logger');

describe('CRM Companies API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      user: { id: 'user-123', email: '<EMAIL>' },
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/crm/companies', () => {
    it('should fetch all companies with optional filters', async () => {
      mockRequest.query = {
        search: 'Acme',
        limit: '20',
        offset: '0',
      };
      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const mockCompanies = [
        {
          id: 1,
          name: 'Acme Corp',
          website: 'https://acme.com',
          industry: 'Technology',
          hubspot_id: 'hs-123',
          harvest_id: 'hv-456',
          created_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 2,
          name: 'Acme Industries',
          website: 'https://acme-industries.com',
          industry: 'Manufacturing',
          hubspot_id: 'hs-789',
          harvest_id: null,
          created_at: '2024-01-02T00:00:00Z',
        },
      ];
      CompanyRepository.prototype.getAllCompanies = jest.fn().mockResolvedValue({
        companies: mockCompanies,
        total: 2,
      });

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.getAllCompanies).toHaveBeenCalledWith({
        search: 'Acme',
        limit: 20,
        offset: 0,
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        companies: mockCompanies,
        total: 2,
      });
    });
  });

  describe('GET /api/crm/companies/:id', () => {
    it('should fetch a company by ID with relationships', async () => {
      mockRequest.params = { id: '1' };
      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const CompanyRelationshipRepository = (await import('../../../../src/api/repositories/company-relationship-repository')).default;
      
      const mockCompany = {
        id: 1,
        name: 'Acme Corp',
        website: 'https://acme.com',
        industry: 'Technology',
        hubspot_id: 'hs-123',
        harvest_id: 'hv-456',
      };
      const mockRelationships = {
        parent: { id: 10, name: 'Parent Corp' },
        children: [
          { id: 2, name: 'Acme Subsidiary 1' },
          { id: 3, name: 'Acme Subsidiary 2' },
        ],
      };

      CompanyRepository.prototype.getCompanyById = jest.fn().mockResolvedValue(mockCompany);
      CompanyRelationshipRepository.prototype.getCompanyRelationships = jest.fn().mockResolvedValue(mockRelationships);

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.getCompanyById).toHaveBeenCalledWith(1);
      expect(CompanyRelationshipRepository.prototype.getCompanyRelationships).toHaveBeenCalledWith(1);
      expect(mockResponse.json).toHaveBeenCalledWith({
        ...mockCompany,
        relationships: mockRelationships,
      });
    });

    it('should handle not found errors', async () => {
      mockRequest.params = { id: '999' };
      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      CompanyRepository.prototype.getCompanyById = jest.fn().mockResolvedValue(null);

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Company not found' });
    });
  });

  describe('POST /api/crm/companies', () => {
    it('should create a new company', async () => {
      mockRequest.body = {
        name: 'New Company',
        website: 'https://newcompany.com',
        industry: 'Finance',
        description: 'A new financial services company',
        annual_revenue: 1000000,
        employee_count: 50,
      };

      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockCreatedCompany = { id: 3, ...mockRequest.body };
      CompanyRepository.prototype.createCompany = jest.fn().mockResolvedValue(mockCreatedCompany);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.createCompany).toHaveBeenCalledWith(mockRequest.body);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'company_created',
        target_type: 'company',
        target_id: '3',
        metadata: {
          company_name: 'New Company',
          industry: 'Finance',
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockCreatedCompany);
    });

    it('should handle validation errors', async () => {
      mockRequest.body = {
        // Missing required field: name
        website: 'https://invalid.com',
      };

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Company name is required',
      });
    });
  });

  describe('PUT /api/crm/companies/:id', () => {
    it('should update a company', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        name: 'Updated Acme Corp',
        website: 'https://new-acme.com',
        employee_count: 100,
      };

      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockUpdatedCompany = { id: 1, ...mockRequest.body };
      CompanyRepository.prototype.updateCompany = jest.fn().mockResolvedValue(mockUpdatedCompany);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.put
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.updateCompany).toHaveBeenCalledWith(1, mockRequest.body);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'company_updated',
        target_type: 'company',
        target_id: '1',
        metadata: {
          changes: ['name', 'website', 'employee_count'],
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUpdatedCompany);
    });
  });

  describe('DELETE /api/crm/companies/:id', () => {
    it('should delete a company', async () => {
      mockRequest.params = { id: '1' };

      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      CompanyRepository.prototype.deleteCompany = jest.fn().mockResolvedValue(true);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.deleteCompany).toHaveBeenCalledWith(1);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'company_deleted',
        target_type: 'company',
        target_id: '1',
        metadata: {},
      });
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
    });
  });

  describe('POST /api/crm/companies/:id/link', () => {
    it('should link a company to external systems', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        hubspot_id: 'hs-new-123',
        harvest_id: 'hv-new-456',
      };

      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockLinkedCompany = {
        id: 1,
        name: 'Acme Corp',
        hubspot_id: 'hs-new-123',
        harvest_id: 'hv-new-456',
      };
      CompanyRepository.prototype.linkCompany = jest.fn().mockResolvedValue(mockLinkedCompany);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/link'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.linkCompany).toHaveBeenCalledWith(1, {
        hubspot_id: 'hs-new-123',
        harvest_id: 'hv-new-456',
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'company_linked',
        target_type: 'company',
        target_id: '1',
        metadata: {
          systems: ['hubspot', 'harvest'],
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockLinkedCompany);
    });
  });

  describe('POST /api/crm/companies/:id/unlink', () => {
    it('should unlink a company from external systems', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        systems: ['hubspot'],
      };

      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockUnlinkedCompany = {
        id: 1,
        name: 'Acme Corp',
        hubspot_id: null,
        harvest_id: 'hv-456',
      };
      CompanyRepository.prototype.unlinkCompany = jest.fn().mockResolvedValue(mockUnlinkedCompany);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/unlink'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.unlinkCompany).toHaveBeenCalledWith(1, ['hubspot']);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'company_unlinked',
        target_type: 'company',
        target_id: '1',
        metadata: {
          systems: ['hubspot'],
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUnlinkedCompany);
    });
  });

  describe('GET /api/crm/companies/:id/contacts', () => {
    it('should fetch contacts for a company', async () => {
      mockRequest.params = { id: '1' };
      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      
      const mockContacts = [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          role: 'CEO',
          is_primary: true,
        },
        {
          id: 2,
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          role: 'CTO',
          is_primary: false,
        },
      ];
      CompanyRepository.prototype.getCompanyContacts = jest.fn().mockResolvedValue(mockContacts);

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/contacts'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.getCompanyContacts).toHaveBeenCalledWith(1);
      expect(mockResponse.json).toHaveBeenCalledWith(mockContacts);
    });
  });

  describe('GET /api/crm/companies/:id/deals', () => {
    it('should fetch deals for a company', async () => {
      mockRequest.params = { id: '1' };
      const CompanyRepository = (await import('../../../../src/api/repositories/company-repository')).default;
      
      const mockDeals = [
        {
          id: 1,
          name: 'Q1 2024 Project',
          amount: 50000,
          stage: 'negotiation',
          probability: 0.75,
          close_date: '2024-03-31',
        },
        {
          id: 2,
          name: 'Annual Contract',
          amount: 120000,
          stage: 'closed_won',
          probability: 1,
          close_date: '2024-01-15',
        },
      ];
      CompanyRepository.prototype.getCompanyDeals = jest.fn().mockResolvedValue(mockDeals);

      const companiesRoutes = await import('../../../../src/api/routes/crm/companies');
      const handler = companiesRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/deals'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(CompanyRepository.prototype.getCompanyDeals).toHaveBeenCalledWith(1);
      expect(mockResponse.json).toHaveBeenCalledWith(mockDeals);
    });
  });
});