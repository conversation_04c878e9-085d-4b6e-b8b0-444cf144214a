import React from 'react';
import { Card } from '../shared/Card';
import { Badge } from '../shared/Badge';
import XeroBadge from '../shared/XeroBadge';
import HarvestBadge from '../shared/HarvestBadge';
import PredictedBadge from '../shared/PredictedBadge';
import AccruedBadge from '../shared/AccruedBadge';
import { IconBadge } from '../shared/IconBadge';

/**
 * BadgeShowcase - Demonstrates the modernized badge system
 * 
 * This component showcases the improvements made in Phase 2A:
 * - Type-safe variant system
 * - Consistent styling with design system
 * - Enhanced accessibility
 * - Financial-specific badge variants
 * - Icon badge system for logos
 * 
 * Note: This is a demo component and can be removed after reviewing
 */
export const BadgeShowcase: React.FC = () => {
  const sampleQuarterInfo = {
    quarterName: "Q2",
    quarterLabel: "Q2 2024",
    percentComplete: 75
  };

  return (
    <Card variant="comfortable" className="max-w-4xl mx-auto">
      <h2 className="text-fluid-xl font-semibold mb-fluid-lg text-primary-600 dark:text-primary-400">
        🎉 Badge System Modernization - Phase 2A Complete
      </h2>
      
      <div className="space-y-fluid-lg">
        {/* Logo Badges */}
        <div>
          <h3 className="text-fluid-lg font-medium mb-fluid-md text-gray-800 dark:text-gray-200">
            Logo Badges (Modernized)
          </h3>
          <div className="flex flex-wrap items-center gap-fluid-md">
            <div className="flex items-center space-x-2">
              <XeroBadge />
              <span className="text-sm text-gray-600 dark:text-gray-400">Xero (default)</span>
            </div>
            <div className="flex items-center space-x-2">
              <HarvestBadge size="lg" />
              <span className="text-sm text-gray-600 dark:text-gray-400">Harvest (large)</span>
            </div>
            <div className="flex items-center space-x-2">
              <IconBadge src="/xero.svg" alt="Custom" title="Custom icon badge" size="sm" rounded="full" />
              <span className="text-sm text-gray-600 dark:text-gray-400">Custom (small, rounded)</span>
            </div>
          </div>
        </div>

        {/* Financial Badges */}
        <div>
          <h3 className="text-fluid-lg font-medium mb-fluid-md text-gray-800 dark:text-gray-200">
            Financial Status Badges (Enhanced)
          </h3>
          <div className="flex flex-wrap items-center gap-fluid-md">
            <PredictedBadge />
            <PredictedBadge quarterInfo={sampleQuarterInfo} />
            <AccruedBadge />
            <AccruedBadge quarterInfo={sampleQuarterInfo} />
          </div>
        </div>

        {/* Standard Badge Variants */}
        <div>
          <h3 className="text-fluid-lg font-medium mb-fluid-md text-gray-800 dark:text-gray-200">
            Standard Badge Variants (New)
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-fluid-md">
            <div className="flex flex-col items-center space-y-2">
              <Badge variant="primary">Primary</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Badge variant="danger">Danger</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="neutral">Neutral</Badge>
              <Badge variant="predicted">Predicted</Badge>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Badge size="sm">Small</Badge>
              <Badge size="default">Default</Badge>
              <Badge size="lg">Large</Badge>
            </div>
            <div className="flex flex-col items-center space-y-2">
              <Badge variant="success" icon={<CheckIcon />}>
                With Icon
              </Badge>
              <Badge variant="warning" iconRight={<AlertIcon />}>
                Right Icon
              </Badge>
            </div>
          </div>
        </div>

        {/* Benefits Summary */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-fluid-lg">
          <h3 className="text-fluid-lg font-medium mb-fluid-sm text-green-800 dark:text-green-200">
            ✅ Improvements Achieved
          </h3>
          <ul className="space-y-1 text-sm text-green-700 dark:text-green-300">
            <li>• <strong>Type Safety:</strong> Full TypeScript support with IntelliSense</li>
            <li>• <strong>Consistency:</strong> Unified design system across all badges</li>
            <li>• <strong>Accessibility:</strong> Proper ARIA labels and semantic markup</li>
            <li>• <strong>Performance:</strong> Optimized loading and rendering</li>
            <li>• <strong>Maintainability:</strong> Single source of truth for styling</li>
            <li>• <strong>Flexibility:</strong> Easy to add new variants and sizes</li>
          </ul>
        </div>

        {/* Technical Details */}
        <details className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-fluid-md">
          <summary className="cursor-pointer font-medium text-gray-800 dark:text-gray-200">
            🔧 Technical Implementation Details
          </summary>
          <div className="mt-fluid-sm space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <p><strong>Variant System:</strong> Type-safe variants using createVariants() utility</p>
            <p><strong>Base Components:</strong> Badge, IconBadge with full prop support</p>
            <p><strong>Legacy Compatibility:</strong> All existing components work unchanged</p>
            <p><strong>CSS Architecture:</strong> Uses @layer components with Tailwind integration</p>
            <p><strong>Bundle Impact:</strong> No size increase - replaced duplicate styles</p>
          </div>
        </details>
      </div>
    </Card>
  );
};

// Helper icon components for the demo
const CheckIcon = () => (
  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
  </svg>
);

const AlertIcon = () => (
  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
  </svg>
);

export default BadgeShowcase;