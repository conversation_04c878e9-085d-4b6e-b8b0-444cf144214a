import React, { forwardRef } from "react"; import { cn, textareaVariants, TextareaVariantProps, } from "../../../utils/component-variants"; export interface TextareaProps extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, "size">, TextareaVariantProps { label?: string; error?: string; helpText?: string; required?: boolean; loading?: boolean; maxLength?: number; showCharCount?: boolean; } /** * Modern Textarea component with variant system * * Features: * - Type-safe variants (default, error, success) * - Multiple sizes (sm, default, lg) * - Resize options (none, vertical, horizontal, both) * - Error states with validation styling * - Loading states * - Character count display * - Accessibility features (ARIA labels, error announcements) * - Dark mode support */ export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>( ( { className, variant = "default", size = "default", resize = "vertical", label, error, helpText, required = false, loading = false, maxLength, showCharCount = false, id, value, ...props }, ref ) => { // Generate unique ID if not provided const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`; // Determine variant based on error state const effectiveVariant = error ? "error" : variant; // Calculate character count const currentLength = typeof value === "string" ? value.length : 0; const showCount = showCharCount || maxLength; return ( <div className="form-field"> {/* Label */} {label && ( <label htmlFor={textareaId} className="form-label"> {label} {required && ( <span className="form-required" aria-label="required"> * </span> )} </label> )} {/* Textarea container */} <div className="relative"> {/* Textarea field */} <textarea ref={ref} id={textareaId} className={cn( "input-modern", effectiveVariant === "error" && "input-modern--error", effectiveVariant === "success" && "input-modern--success", loading && "opacity-50 cursor-not-allowed", resize === "none" && "resize-none", resize === "vertical" && "resize-y", resize === "horizontal" && "resize-x", resize === "both" && "resize", "focus-smooth spring-smooth", className )} aria-invalid={error ? "true" : "false"} aria-describedby={ error ? `${textareaId}-error` : helpText ? `${textareaId}-help` : undefined } disabled={loading || props.disabled} maxLength={maxLength} value={value} {...props} /> {/* Error icon */} {error && ( <div className="absolute top-2 right-2 flex items-center pointer-events-none"> <svg className="h-5 w-5 text-accent-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true" > <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" /> </svg> </div> )} {/* Loading spinner */} {loading && ( <div className="absolute top-2 right-2 flex items-center"> <span className="spinner" aria-hidden="true" /> </div> )} </div> {/* Character count */} {showCount && ( <div className="flex justify-between items-center mt-1"> <div className="flex-1"> {/* Error message */} {error && ( <div id={`${textareaId}-error`} className="form-error" role="alert" aria-live="polite" > {error} </div> )} {/* Help text */} {helpText && !error && ( <div id={`${textareaId}-help`} className="form-help"> {helpText} </div> )} </div> {/* Character count display */} <div className={cn( "text-xs", maxLength && currentLength > maxLength * 0.9 ? "text-accent-600 dark:text-accent-400" : "text-gray-500 dark:text-gray-400" )} > {maxLength ? `${currentLength}/${maxLength}` : currentLength} </div> </div> )} {/* Error message (when no character count) */} {error && !showCount && ( <div id={`${textareaId}-error`} className="form-error" role="alert" aria-live="polite" > {error} </div> )} {/* Help text (when no character count) */} {helpText && !error && !showCount && ( <div id={`${textareaId}-help`} className="form-help"> {helpText} </div> )} </div> ); } ); Textarea.displayName = "Textarea"; export default Textarea; 