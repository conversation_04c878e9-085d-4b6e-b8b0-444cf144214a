# Upstream Documentation

Welcome to the Upstream financial dashboard documentation. This directory contains comprehensive documentation for users and developers.

## 📚 Documentation Structure

The documentation is organized into the following main sections:

### 👥 [User Guide](./user-guide/README.md)
Documentation for end users of the Upstream application:

- [Getting Started](./user-guide/getting-started/README.md) - Introduction and setup guide
- **Features:**
  - [Cashflow Projection](./user-guide/features/cashflow/README.md) - Cashflow projection and Smart Forecast system
  - [Expenses Management](./user-guide/features/expenses/README.md) - Custom expenses and Xero integrations
  - [CRM and Deal Management](./user-guide/features/crm/README.md) - Deal tracking and pipeline management
  - [Estimates](./user-guide/features/estimates/README.md) - Project estimation and Harvest integration
  - [Reports](./user-guide/features/reports/README.md) - Staff utilization and financial reports
- [Troubleshooting](./user-guide/troubleshooting/README.md) - Common issues and solutions

### 🔧 [Technical Documentation](./technical/README.md)
Documentation for developers and system administrators:

- [Architecture Overview](./technical/architecture/README.md) - System architecture and design patterns
- [Data Model](./technical/data-model/README.md) - Database schema, migrations, and repository patterns
- [Component Documentation](./technical/components/) - Frontend and backend component details
- [Repository Refactoring Guide](./technical/repository-refactoring-guide.md) - Repository pattern implementation
- **[Testing Documentation](../tests/README.md)** - Comprehensive test suite documentation
  - [Testing Strategy](../TESTING-STRATEGY.md) - Current comprehensive testing strategy
  - [Testing Guide](../tests/TESTING-GUIDE.md) - Practical testing approach and patterns
  - [Test Coverage Summary](../tests/TEST-COVERAGE-SUMMARY.md) - Current test coverage metrics
  - [Mock Mode Guide](../MOCK-MODE-GUIDE.md) - Running the app in mock mode for testing

### 🔌 [Integrations](./integrations/README.md)
**NEW:** Organized integration documentation:

- [Xero MCP Integration](./integrations/xero-mcp/README.md) - Chat-based Xero data interaction
- [HubSpot Integration](./integrations/hubspot/README.md) - CRM data synchronization
- More integrations organized by service

### 📖 [API Reference](./api-reference/README.md)
Comprehensive API documentation:

- **Internal APIs:** Application endpoints and services
- **External APIs:** 
  - [Harvest API](./api-reference/external/harvest/README.md) - Time tracking and project management
  - [Xero API](./api-reference/xero/README.md) - Accounting and financial data
  - [Company Linking API](./api-reference/company-linking-api.md) - Company management endpoints

### 🗂️ [Archive](./archive/README.md)
Historical documentation and completed projects:

- **[Completed Plans](./archive/completed-plans/)** - Implementation plans that have been finished
- **[Deprecated Documentation](./archive/deprecated/README.md)** - Outdated docs kept for reference

### 📋 [Features](./features/README.md)
Feature-specific documentation:

- [Activity Feed](./features/activity-feed.md) - Comprehensive activity tracking system
- [Client Radar](./features/client-radar.md) - Strategic client relationship management
- [Harvest Invoice Cache](./features/harvest-invoice-cache.md) - Performance optimization for invoice data
- [HubSpot Company Linking](./features/hubspot-company-linking.md) - Company relationship management
- [HubSpot Realtime Import Progress](./features/hubspot-realtime-import-progress.md) - Import tracking system

## 🚀 Quick Start

### For Users
New to Upstream? Start with the **[Getting Started Guide](./user-guide/getting-started/README.md)** to learn about the application and its features.

### For Developers
Working on Upstream? Begin with the **[Technical Documentation](./technical/README.md)** to understand the architecture, then review the **[Data Model](./technical/data-model/README.md)** for database patterns. Check the **[Testing Strategy](../TESTING-STRATEGY.md)** for our comprehensive testing approach.

### For Integration Development
Adding external system integrations? Check the **[Integrations](./integrations/README.md)** section for patterns and examples.

## 📊 Documentation Standards

### Organization Principles
- **User-focused** documentation in `user-guide/`
- **Developer-focused** documentation in `technical/`
- **Integration-specific** documentation in `integrations/`
- **API reference** documentation in `api-reference/`
- **Historical content** in `archive/`

### Content Guidelines
- Each major section has a comprehensive `README.md` index
- Documentation includes practical examples and code samples
- All external links are verified and current
- Screenshots and diagrams are kept up-to-date

## 🆘 Support

For questions or support:
- **Email:** [<EMAIL>](mailto:<EMAIL>)
- **Documentation Issues:** Please check the troubleshooting guides first
- **Technical Questions:** Review the technical documentation and API reference

---

**Last Updated:** December 2024  
**Documentation Version:** 2.0 (Reorganized and cleaned up)