/**
 * Flexoki Theme - Single Source of Truth for all colors
 * Based on https://stephango.com/flexoki
 * 
 * This file serves as the central color configuration for the entire application.
 * All color references should import from this file.
 */

// ============================================================================
// FLEXOKI COLOR PALETTE
// ============================================================================

export const flexokiPalette = {
  // Base Colors (Paper and Black)
  black: '#100F0F',
  paper: '#FFFCF0',
  
  // Base Scale (Neutral grays)
  base: {
    50: '#F2F0E5',
    100: '#E6E4D9',
    150: '#DAD8CE',
    200: '#CECDC3',
    300: '#B7B5AC',
    400: '#9F9D96',
    500: '#878580',
    600: '#6F6E69',
    700: '#575653',
    800: '#403E3C',
    850: '#343331',
    900: '#282726',
    950: '#1C1B1A'
  },
  
  // Accent Colors (Full ranges)
  red: {
    50: '#FFE1D5',
    100: '#FFCABB',
    150: '#FDB2A2',
    200: '#F89A8A',
    300: '#E8705F',
    400: '#D14D41',
    500: '#C03E35',
    600: '#AF3029',
    700: '#942822',
    800: '#6C201C',
    850: '#551B18',
    900: '#3E1715',
    950: '#261312'
  },
  
  orange: {
    50: '#FFE7CE',
    100: '#FED3AF',
    150: '#FCC192',
    200: '#F9AE77',
    300: '#EC8B49',
    400: '#DA702C',
    500: '#CB6120',
    600: '#BC5215',
    700: '#9D4310',
    800: '#71320D',
    850: '#59290D',
    900: '#40200D',
    950: '#27180E'
  },
  
  yellow: {
    50: '#FAEEC6',
    100: '#F6E2A0',
    150: '#F1D67E',
    200: '#ECCB60',
    300: '#DFB431',
    400: '#D0A215',
    500: '#BE9207',
    600: '#AD8301',
    700: '#8E6B01',
    800: '#664D01',
    850: '#503D02',
    900: '#3A2D04',
    950: '#241E08'
  },
  
  green: {
    50: '#EDEECF',
    100: '#DDE2B2',
    150: '#CDD597',
    200: '#BEC97E',
    300: '#A0AF54',
    400: '#879A39',
    500: '#768D21',
    600: '#66800B',
    700: '#536907',
    800: '#3D4C07',
    850: '#313D07',
    900: '#252D09',
    950: '#1A1E0C'
  },
  
  cyan: {
    50: '#DDF1E4',
    100: '#BFE8D9',
    150: '#A2DECE',
    200: '#87D3C3',
    300: '#5ABDAC',
    400: '#3AA99F',
    500: '#2F968D',
    600: '#24837B',
    700: '#1C6C66',
    800: '#164F4A',
    850: '#143F3C',
    900: '#122F2C',
    950: '#101F1D'
  },
  
  blue: {
    50: '#E1ECEB',
    100: '#C6DDE8',
    150: '#ABCFE2',
    200: '#92BFDB',
    300: '#66A0C8',
    400: '#4385BE',
    500: '#3171B2',
    600: '#205EA6',
    700: '#1A4F8C',
    800: '#163B66',
    850: '#133051',
    900: '#12253B',
    950: '#101A24'
  },
  
  purple: {
    50: '#F0EAEC',
    100: '#E2D9E9',
    150: '#D3CAE6',
    200: '#C4B9E0',
    300: '#A699D0',
    400: '#8B7EC8',
    500: '#735EB5',
    600: '#5E409D',
    700: '#4F3685',
    800: '#3C2A62',
    850: '#31234E',
    900: '#261C39',
    950: '#1A1623'
  },
  
  magenta: {
    50: '#FEE4E5',
    100: '#FCCFDA',
    150: '#F9B9CF',
    200: '#F4A4C2',
    300: '#E47DA8',
    400: '#CE5D97',
    500: '#B74583',
    600: '#A02F6F',
    700: '#87285E',
    800: '#641F46',
    850: '#4F1B39',
    900: '#39172B',
    950: '#24131D'
  }
} as const;

// ============================================================================
// SEMANTIC COLOR MAPPINGS
// ============================================================================

export const semanticColors = {
  light: {
    // Core UI Colors
    background: flexokiPalette.paper,
    surface: flexokiPalette.base[50],
    surfaceAlt: flexokiPalette.base[100],
    border: flexokiPalette.base[200],
    borderStrong: flexokiPalette.base[300],
    
    // Text Colors
    text: flexokiPalette.black,
    textMuted: flexokiPalette.base[600],
    textSubtle: flexokiPalette.base[500],
    textDisabled: flexokiPalette.base[400],
    
    // Semantic Colors
    primary: flexokiPalette.blue[600],
    primaryHover: flexokiPalette.blue[700],
    primaryText: flexokiPalette.blue[600],
    
    success: flexokiPalette.green[600],
    successHover: flexokiPalette.green[700],
    successText: flexokiPalette.green[700],
    successBg: flexokiPalette.green[50],
    
    warning: flexokiPalette.orange[600],
    warningHover: flexokiPalette.orange[700],
    warningText: flexokiPalette.orange[700],
    warningBg: flexokiPalette.orange[50],
    
    error: flexokiPalette.red[600],
    errorHover: flexokiPalette.red[700],
    errorText: flexokiPalette.red[700],
    errorBg: flexokiPalette.red[50],
    
    info: flexokiPalette.cyan[600],
    infoHover: flexokiPalette.cyan[700],
    infoText: flexokiPalette.cyan[700],
    infoBg: flexokiPalette.cyan[50],
  },
  
  dark: {
    // Core UI Colors
    background: flexokiPalette.black,
    surface: flexokiPalette.base[950],
    surfaceAlt: flexokiPalette.base[900],
    border: flexokiPalette.base[800],
    borderStrong: flexokiPalette.base[700],
    
    // Text Colors
    text: flexokiPalette.base[200],
    textMuted: flexokiPalette.base[400],
    textSubtle: flexokiPalette.base[500],
    textDisabled: flexokiPalette.base[600],
    
    // Semantic Colors
    primary: flexokiPalette.blue[400],
    primaryHover: flexokiPalette.blue[300],
    primaryText: flexokiPalette.blue[400],
    
    success: flexokiPalette.green[400],
    successHover: flexokiPalette.green[300],
    successText: flexokiPalette.green[400],
    successBg: `${flexokiPalette.green[400]}20`, // 20% opacity
    
    warning: flexokiPalette.orange[400],
    warningHover: flexokiPalette.orange[300],
    warningText: flexokiPalette.orange[400],
    warningBg: `${flexokiPalette.orange[400]}20`, // 20% opacity
    
    error: flexokiPalette.red[400],
    errorHover: flexokiPalette.red[300],
    errorText: flexokiPalette.red[400],
    errorBg: `${flexokiPalette.red[400]}20`, // 20% opacity
    
    info: flexokiPalette.cyan[400],
    infoHover: flexokiPalette.cyan[300],
    infoText: flexokiPalette.cyan[400],
    infoBg: `${flexokiPalette.cyan[400]}20`, // 20% opacity
  }
} as const;

// ============================================================================
// CHART COLOR SYSTEM
// ============================================================================

export const flexokiChartColors = {
  light: [
    flexokiPalette.blue[600],    // Primary data
    flexokiPalette.orange[600],  // Secondary data
    flexokiPalette.purple[600],  // Tertiary data
    flexokiPalette.cyan[600],    // Quaternary data
    flexokiPalette.yellow[600],  // Additional data
    flexokiPalette.magenta[600], // Additional data
    flexokiPalette.green[600],   // Success/positive
    flexokiPalette.red[600],     // Error/negative
    // Light variants for additional series
    flexokiPalette.blue[400],
    flexokiPalette.orange[400],
    flexokiPalette.purple[400],
    flexokiPalette.cyan[400],
  ],
  dark: [
    flexokiPalette.blue[400],    // Primary data
    flexokiPalette.orange[400],  // Secondary data
    flexokiPalette.purple[400],  // Tertiary data
    flexokiPalette.cyan[400],    // Quaternary data
    flexokiPalette.yellow[400],  // Additional data
    flexokiPalette.magenta[400], // Additional data
    flexokiPalette.green[400],   // Success/positive
    flexokiPalette.red[400],     // Error/negative
    // Lighter variants for additional series
    flexokiPalette.blue[300],
    flexokiPalette.orange[300],
    flexokiPalette.purple[300],
    flexokiPalette.cyan[300],
  ]
} as const;

// ============================================================================
// FINANCIAL SEMANTIC COLORS
// ============================================================================

export const financialColors = {
  light: {
    income: flexokiPalette.green[600],
    incomeBg: flexokiPalette.green[50],
    incomeHover: flexokiPalette.green[700],
    
    expense: flexokiPalette.red[600],
    expenseBg: flexokiPalette.red[50],
    expenseHover: flexokiPalette.red[700],
    
    neutral: flexokiPalette.base[600],
    neutralBg: flexokiPalette.base[100],
    neutralHover: flexokiPalette.base[700],
    
    projected: flexokiPalette.orange[600],
    projectedBg: flexokiPalette.orange[50],
    projectedHover: flexokiPalette.orange[700],
  },
  dark: {
    income: flexokiPalette.green[400],
    incomeBg: `${flexokiPalette.green[400]}20`,
    incomeHover: flexokiPalette.green[300],
    
    expense: flexokiPalette.red[400],
    expenseBg: `${flexokiPalette.red[400]}20`,
    expenseHover: flexokiPalette.red[300],
    
    neutral: flexokiPalette.base[400],
    neutralBg: `${flexokiPalette.base[400]}20`,
    neutralHover: flexokiPalette.base[300],
    
    projected: flexokiPalette.orange[400],
    projectedBg: `${flexokiPalette.orange[400]}20`,
    projectedHover: flexokiPalette.orange[300],
  }
} as const;

// ============================================================================
// TAILWIND CONFIGURATION GENERATOR
// ============================================================================

export const generateTailwindColors = () => {
  return {
    // Direct palette mapping
    ...flexokiPalette,
    
    // Semantic mappings for easier use
    primary: flexokiPalette.blue,
    success: flexokiPalette.green,
    warning: flexokiPalette.orange,
    error: flexokiPalette.red,
    info: flexokiPalette.cyan,
    
    // Special colors
    paper: flexokiPalette.paper,
    black: flexokiPalette.black,
    
    // Legacy color mappings for backward compatibility
    gray: flexokiPalette.base,      // Map gray to base for existing Tailwind classes
    slate: flexokiPalette.base,     // Map slate to base
    zinc: flexokiPalette.base,      // Map zinc to base
    neutral: flexokiPalette.base,   // Map neutral to base
    stone: flexokiPalette.base,     // Map stone to base
    
    // Consolidated color mappings (non-Flexoki colors to closest Flexoki equivalents)
    indigo: flexokiPalette.blue,    // Indigo → Blue
    sky: flexokiPalette.cyan,       // Sky → Cyan
    teal: flexokiPalette.cyan,      // Teal → Cyan
    emerald: flexokiPalette.green,  // Emerald → Green
    lime: flexokiPalette.green,     // Lime → Green
    amber: flexokiPalette.yellow,   // Amber → Yellow
    rose: flexokiPalette.red,       // Rose → Red
    pink: flexokiPalette.magenta,   // Pink → Magenta
    fuchsia: flexokiPalette.magenta,// Fuchsia → Magenta
    violet: flexokiPalette.purple,  // Violet → Purple
    
    // Chart colors (for Tailwind classes if needed)
    chart: {
      1: flexokiPalette.blue[600],
      2: flexokiPalette.orange[600],
      3: flexokiPalette.purple[600],
      4: flexokiPalette.cyan[600],
      5: flexokiPalette.yellow[600],
      6: flexokiPalette.magenta[600],
    }
  };
};

// ============================================================================
// CSS VARIABLES GENERATOR
// ============================================================================

export const generateCSSVariables = (isDark: boolean = false) => {
  const theme = isDark ? semanticColors.dark : semanticColors.light;
  const chartColors = isDark ? flexokiChartColors.dark : flexokiChartColors.light;
  
  return `
    /* Core UI Colors */
    --color-bg: ${theme.background};
    --color-surface: ${theme.surface};
    --color-surface-alt: ${theme.surfaceAlt};
    --color-border: ${theme.border};
    --color-border-strong: ${theme.borderStrong};
    
    /* Text Colors */
    --color-text: ${theme.text};
    --color-text-muted: ${theme.textMuted};
    --color-text-subtle: ${theme.textSubtle};
    --color-text-disabled: ${theme.textDisabled};
    
    /* Primary Colors */
    --color-primary: ${theme.primary};
    --color-primary-hover: ${theme.primaryHover};
    --color-primary-text: ${theme.primaryText};
    
    /* Semantic Colors */
    --color-success: ${theme.success};
    --color-success-hover: ${theme.successHover};
    --color-success-text: ${theme.successText};
    --color-success-bg: ${theme.successBg};
    
    --color-warning: ${theme.warning};
    --color-warning-hover: ${theme.warningHover};
    --color-warning-text: ${theme.warningText};
    --color-warning-bg: ${theme.warningBg};
    
    --color-error: ${theme.error};
    --color-error-hover: ${theme.errorHover};
    --color-error-text: ${theme.errorText};
    --color-error-bg: ${theme.errorBg};
    
    --color-info: ${theme.info};
    --color-info-hover: ${theme.infoHover};
    --color-info-text: ${theme.infoText};
    --color-info-bg: ${theme.infoBg};
    
    /* Chart Colors */
    ${chartColors.map((color, i) => `--color-chart-${i + 1}: ${color};`).join('\n    ')}
    
    /* Legacy Support Variables */
    --primary-color: ${theme.primary};
    --secondary-color: ${theme.primary};
    --accent-color: ${theme.error};
    --text-color: ${theme.text};
    --background-color: ${theme.background};
    --border-color: ${theme.border};
    --success-color: ${theme.success};
    --warning-color: ${theme.warning};
    --error-color: ${theme.error};
  `.trim();
};

// ============================================================================
// THEME HOOKS AND UTILITIES
// ============================================================================

/**
 * Get the appropriate theme colors based on dark mode
 */
export const getThemeColors = (isDark: boolean) => {
  return isDark ? semanticColors.dark : semanticColors.light;
};

/**
 * Get chart colors for the current theme
 */
export const getChartColors = (isDark: boolean) => {
  return isDark ? flexokiChartColors.dark : flexokiChartColors.light;
};

/**
 * Get financial colors for the current theme
 */
export const getFinancialColors = (isDark: boolean) => {
  return isDark ? financialColors.dark : financialColors.light;
};

// ============================================================================
// COLOR MIGRATION MAPPING
// ============================================================================

/**
 * Mapping of old color values to new Flexoki equivalents
 * Used for systematic migration
 */
export const colorMigrationMap: Record<string, string> = {
  // Old primary blues → Flexoki blue
  '#2870ab': flexokiPalette.blue[600],
  '#3b82f6': flexokiPalette.blue[600],
  '#3498db': flexokiPalette.blue[600],
  '#2563eb': flexokiPalette.blue[600],
  
  // Success greens → Flexoki green
  '#27ae60': flexokiPalette.green[600],
  '#10b981': flexokiPalette.green[600],
  '#22c55e': flexokiPalette.green[600],
  
  // Error/danger reds → Flexoki red
  '#e74c3c': flexokiPalette.red[600],
  '#ef4444': flexokiPalette.red[600],
  '#dc2626': flexokiPalette.red[600],
  '#f36c21': flexokiPalette.orange[600], // Harvest orange → Flexoki orange
  
  // Warning/caution → Flexoki orange/yellow
  '#f97316': flexokiPalette.orange[600],
  '#f59e0b': flexokiPalette.yellow[600],
  '#eab308': flexokiPalette.yellow[600],
  
  // Grays → Flexoki base scale
  '#f3f4f6': flexokiPalette.base[100],
  '#e5e7eb': flexokiPalette.base[200],
  '#d1d5db': flexokiPalette.base[300],
  '#9ca3af': flexokiPalette.base[400],
  '#6b7280': flexokiPalette.base[500],
  '#4b5563': flexokiPalette.base[600],
  '#374151': flexokiPalette.base[700],
  '#1f2937': flexokiPalette.base[800],
  '#111827': flexokiPalette.base[900],
  
  // Special colors
  '#ffffff': flexokiPalette.paper,
  '#000000': flexokiPalette.black,
};

// Export everything as default for convenience
export default {
  palette: flexokiPalette,
  semantic: semanticColors,
  charts: flexokiChartColors,
  financial: financialColors,
  generateTailwindColors,
  generateCSSVariables,
  getThemeColors,
  getChartColors,
  getFinancialColors,
  colorMigrationMap,
};