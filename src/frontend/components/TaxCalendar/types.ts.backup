// Common types for the Tax Calendar components

export interface DayInfo {
  day: number;
  isPAYGWDueDate: boolean;
  isGSTDueDate: boolean;
  isCurrentDay: boolean;
  paygwPaymentForMonth: number | null;
  gstPaymentForQuarter: number | null;
}

export interface QuarterInfo {
  quarter: number;
  label: string;
  colorIndex: number;
}

export interface MonthData {
  index: number;
  name: string;
  days: DayInfo[];
  daysInMonth: number;
  quarter: QuarterInfo;
  paygwDueDate: Date;
  paygwDueYear: number;
}

export interface GSTQuarter {
  label: string;
  colorIndex: number;
  startMonth: number;
  endMonth: number;
  dueMonth: number;
  dueDay: number;
  dueYear: number;
}

export interface CalendarData {
  months: MonthData[];
  gstQuarters: GSTQuarter[];
}

export interface TooltipData {
  visible: boolean;
  title: string;
  content: string;
  x: number;
  y: number;
}
