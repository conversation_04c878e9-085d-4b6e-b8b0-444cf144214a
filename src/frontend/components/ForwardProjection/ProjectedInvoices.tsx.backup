import React, { useState, useMemo } from 'react';
import { formatCurrency, formatDate } from './utils';
import { ProjectedInvoice } from '../../api/harvest';

export interface InvoiceFilters {
  types: string[];
  clients: string[];
  searchText: string;
  amountRange: [number | null, number | null];
}

interface ProjectedInvoicesProps {
  projectedInvoices: ProjectedInvoice[];
}

const ProjectedInvoices: React.FC<ProjectedInvoicesProps> = ({ projectedInvoices }) => {
  // Define filters state
  const [filters, setFilters] = useState<InvoiceFilters>({
    types: [],
    clients: [],
    searchText: '',
    amountRange: [null, null]
  });
  
  // State for filter accordion
  const [filtersExpanded, setFiltersExpanded] = useState<boolean>(false);
  
  // Toggle filters visibility
  const toggleFilters = () => {
    setFiltersExpanded(!filtersExpanded);
  };

  // Extract unique invoice types and clients for filter options
  const invoiceTypes = useMemo(() => {
    const typesSet = new Set<string>();
    projectedInvoices.forEach(invoice => {
      typesSet.add(invoice.type);
    });
    return Array.from(typesSet);
  }, [projectedInvoices]);

  const clientNames = useMemo(() => {
    const clientsSet = new Set<string>();
    projectedInvoices.forEach(invoice => {
      clientsSet.add(invoice.clientName);
    });
    return Array.from(clientsSet);
  }, [projectedInvoices]);

  // Filter invoices based on current filters
  const filteredInvoices = useMemo(() => {
    return projectedInvoices.filter(invoice => {
      // Filter by type
      if (filters.types.length > 0 && !filters.types.includes(invoice.type)) {
        return false;
      }
      
      // Filter by client
      if (filters.clients.length > 0 && !filters.clients.includes(invoice.clientName)) {
        return false;
      }
      
      // Filter by search text (project name or client name)
      if (filters.searchText && 
          !invoice.projectName.toLowerCase().includes(filters.searchText.toLowerCase()) &&
          !invoice.clientName.toLowerCase().includes(filters.searchText.toLowerCase())) {
        return false;
      }
      
      // Filter by amount range
      if (filters.amountRange[0] !== null && invoice.amount < filters.amountRange[0]) {
        return false;
      }
      if (filters.amountRange[1] !== null && invoice.amount > filters.amountRange[1]) {
        return false;
      }
      
      return true;
    });
  }, [projectedInvoices, filters]);
  
  // Group invoices by type for better display
  // Note: We no longer include outstanding invoices as they're handled by the cashflow projection
  const uninvoicedWork = filteredInvoices.filter(inv => inv.type === 'uninvoiced_work');
  const futureWork = filteredInvoices.filter(inv => inv.type === 'future_work');
  
  // Calculate total projected income
  const totalProjected = filteredInvoices.reduce((sum, inv) => sum + inv.amount, 0);
  
  // Get type label
  const getTypeLabel = (type: string) => {
    switch(type) {
      case 'uninvoiced_work': return 'Uninvoiced Work';
      case 'future_work': return 'Future Work';
      default: return type;
    }
  };
  
  // Get type color class
  const getTypeColorClass = (type: string) => {
    switch(type) {
      case 'uninvoiced_work': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'future_work': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };
  
  // Handle filter changes
  const handleTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const type = e.target.value;
    const newTypes = e.target.checked
      ? [...filters.types, type]
      : filters.types.filter(t => t !== type);
    
    setFilters({
      ...filters,
      types: newTypes
    });
  };

  const handleClientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const client = e.target.value;
    const newClients = e.target.checked
      ? [...filters.clients, client]
      : filters.clients.filter(c => c !== client);
    
    setFilters({
      ...filters,
      clients: newClients
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({
      ...filters,
      searchText: e.target.value
    });
  };

  const handleAmountMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const min = e.target.value === '' ? null : parseFloat(e.target.value);
    setFilters({
      ...filters,
      amountRange: [min, filters.amountRange[1]]
    });
  };

  const handleAmountMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const max = e.target.value === '' ? null : parseFloat(e.target.value);
    setFilters({
      ...filters,
      amountRange: [filters.amountRange[0], max]
    });
  };

  const handleClearFilters = () => {
    setFilters({
      types: [],
      clients: [],
      searchText: '',
      amountRange: [null, null]
    });
  };

  return (
    <div className="py-2 md:py-4 px-2 md:px-4 onboarding-projected-invoices">
      <h2 className="text-lg font-medium text-primary mb-3">
        Projected Income
      </h2>
      
      <div className="mb-4">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500 dark:text-gray-400">Total Projected Income</div>
          <div className="text-lg font-semibold text-success">{formatCurrency(totalProjected)}</div>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2 mb-4">
        <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColorClass('uninvoiced_work')} border-purple-200 dark:border-purple-800/30`}>
          Uninvoiced: {uninvoicedWork.length}
        </span>
        <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColorClass('future_work')} border-green-200 dark:border-green-800/30`}>
          Future: {futureWork.length}
        </span>
      </div>
      
      {/* Filter accordion */}
      <div className="mb-4">
        <div className="border border-gray-100 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
          {/* Accordion header */}
          <div className="flex items-center justify-between w-full">
            <button
              type="button"
              onClick={toggleFilters}
              className="flex items-center grow justify-between px-3 py-2 text-sm font-medium text-left text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus-visible:ring focus-visible:ring-secondary focus-visible:ring-opacity-50"
              aria-expanded={filtersExpanded}
              aria-controls="income-filters-panel"
            >
              <div className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                <span className="font-medium">Filter Projected Income</span>
                {(filters.types.length > 0 || filters.clients.length > 0 || filters.searchText || 
                  filters.amountRange[0] !== null || filters.amountRange[1] !== null) && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded border border-blue-200 dark:border-blue-800/30 text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                    Active
                  </span>
                )}
              </div>
              <svg className={`w-5 h-5 transition-transform duration-200 ${filtersExpanded ? 'transform rotate-180' : ''}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
            {(filters.types.length > 0 || filters.clients.length > 0 || filters.searchText || 
              filters.amountRange[0] !== null || filters.amountRange[1] !== null) && (
              <button 
                onClick={handleClearFilters}
                className="px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 border-l border-gray-200 dark:border-gray-600"
              >
                Clear All
              </button>
            )}
          </div>
          
          {/* Filters UI - conditionally rendered based on accordion state */}
          {filtersExpanded && (
            <div id="income-filters-panel" className="bg-white dark:bg-gray-800 p-4">
          
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Search filter */}
            <div className="lg:col-span-2">
              <label htmlFor="invoice-search-filter" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Search
              </label>
              <input
                type="text"
                id="invoice-search-filter"
                value={filters.searchText}
                onChange={handleSearchChange}
                placeholder="Search project or client..."
                className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>
            
            {/* Amount range */}
            <div className="lg:col-span-2">
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Amount Range
              </label>
              <div className="flex space-x-2">
                <input
                  type="number"
                  value={filters.amountRange[0] === null ? '' : filters.amountRange[0]}
                  onChange={handleAmountMinChange}
                  placeholder="Min"
                  className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
                <input
                  type="number"
                  value={filters.amountRange[1] === null ? '' : filters.amountRange[1]}
                  onChange={handleAmountMaxChange}
                  placeholder="Max"
                  className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            {/* Invoice types filter */}
            {invoiceTypes.length > 0 && (
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Invoice Types
                </label>
                <div className="flex flex-wrap gap-2">
                  {invoiceTypes.map(type => (
                    <div key={type} className="flex items-center">
                      <input
                        id={`invoice-type-${type}`}
                        name={`invoice-type-${type}`}
                        type="checkbox"
                        value={type}
                        checked={filters.types.includes(type)}
                        onChange={handleTypeChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label
                        htmlFor={`invoice-type-${type}`}
                        className={`ml-2 ${getTypeColorClass(type)} text-xs px-2 py-1 rounded border ${type === 'uninvoiced_work' ? 'border-purple-200 dark:border-purple-800/30' : 'border-green-200 dark:border-green-800/30'}`}
                      >
                        {getTypeLabel(type)}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Clients filter */}
            {clientNames.length > 0 && (
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Clients
                </label>
                <div className="flex flex-wrap gap-2">
                  {clientNames.map(client => (
                    <div key={client} className="flex items-center">
                      <input
                        id={`client-${client}`}
                        name={`client-${client}`}
                        type="checkbox"
                        value={client}
                        checked={filters.clients.includes(client)}
                        onChange={handleClientChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label
                        htmlFor={`client-${client}`}
                        className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-xs px-2 py-1 rounded border border-gray-200 dark:border-gray-600"
                      >
                        {client}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="w-full">
        {/* Desktop view - Grid layout for md screens and above */}
        <div className="hidden md:grid grid-cols-12 gap-2">
          {/* Table header */}
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-gray-500 dark:text-gray-300 uppercase">Project</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-gray-500 dark:text-gray-300 uppercase">Client</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-gray-500 dark:text-gray-300 uppercase">Type</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-gray-500 dark:text-gray-300 uppercase">Amount</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-gray-500 dark:text-gray-300 uppercase">Invoice Date</div>
          <div className="col-span-2 py-2 px-2 font-medium text-xs text-gray-500 dark:text-gray-300 uppercase">Payment Date</div>
          
          {/* Divider */}
          <div className="col-span-12 border-t border-gray-200 dark:border-gray-700 my-1"></div>
          
          {/* Invoice rows */}
          {filteredInvoices.length > 0 ? (
            filteredInvoices
              .sort((a, b) => new Date(a.paymentDate).getTime() - new Date(b.paymentDate).getTime())
              .map(invoice => (
                <React.Fragment key={invoice.id}>
                  {/* Project */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">{invoice.projectName}</div>
                  </div>
                  
                  {/* Client */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-gray-500 dark:text-gray-300 truncate">{invoice.clientName}</div>
                  </div>
                  
                  {/* Type */}
                  <div className="col-span-2 py-3 px-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded border ${getTypeColorClass(invoice.type)} ${invoice.type === 'uninvoiced_work' ? 'border-purple-200 dark:border-purple-800/30' : 'border-green-200 dark:border-green-800/30'}`}>
                      {getTypeLabel(invoice.type)}
                    </span>
                  </div>
                  
                  {/* Amount */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-success font-medium">{formatCurrency(invoice.amount)}</div>
                  </div>
                  
                  {/* Invoice Date */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-gray-500 dark:text-gray-300">{formatDate(invoice.invoiceDate)}</div>
                  </div>
                  
                  {/* Payment Date */}
                  <div className="col-span-2 py-3 px-2">
                    <div className="text-sm text-gray-500 dark:text-gray-300">{formatDate(invoice.paymentDate)}</div>
                  </div>
                  
                  {/* Divider after each row */}
                  <div className="col-span-12 border-t border-gray-200 dark:border-gray-700 my-1"></div>
                </React.Fragment>
              ))
          ) : (
            <div className="col-span-12 py-4 text-sm text-center text-gray-500 dark:text-gray-400">
              {projectedInvoices.length > 0 
                ? 'No invoices match the current filters. Try adjusting your filter criteria.'
                : 'No projected invoices available.'}
            </div>
          )}
        </div>
        
        {/* Mobile card view - visible below md screens */}
        <div className="md:hidden space-y-2">
          {filteredInvoices.length > 0 ? (
            filteredInvoices
              .sort((a, b) => new Date(a.paymentDate).getTime() - new Date(b.paymentDate).getTime())
              .map(invoice => (
                <div 
                  key={invoice.id} 
                  className={`py-2.5 px-3 rounded-md border border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm mb-2 tap-highlight-transparent touch-manipulation mobile-active-feedback mobile-card`}
                >
                  {/* Top section: Dates and amount */}
                  <div className="mb-2">
                    {/* Invoice and payment dates */}
                    <div className="flex justify-between items-center mb-1">
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        <span className="inline-block mr-2">
                          <svg className="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Invoice: {formatDate(invoice.invoiceDate)}
                        </span>
                        <span className="inline-block">
                          <svg className="w-3 h-3 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                          </svg>
                          Payment: {formatDate(invoice.paymentDate)}
                        </span>
                      </div>
                      
                      <span className="text-sm font-medium text-success">
                        {formatCurrency(invoice.amount)}
                      </span>
                    </div>
                    
                    {/* Project name */}
                    <p className="font-medium text-sm truncate" title={invoice.projectName}>
                      {invoice.projectName}
                    </p>
                  </div>
                  
                  {/* Bottom row: client name and invoice type */}
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[60%]">
                      {invoice.clientName}
                    </div>
                    
                    <span className={`px-1.5 py-0.5 text-xs font-medium rounded border whitespace-nowrap ${getTypeColorClass(invoice.type)} ${invoice.type === 'uninvoiced_work' ? 'border-purple-200 dark:border-purple-800/30' : 'border-green-200 dark:border-green-800/30'}`}>
                      {getTypeLabel(invoice.type)}
                    </span>
                  </div>
                </div>
              ))
          ) : (
            <div className="py-4 text-sm text-center text-gray-500 dark:text-gray-400">
              {projectedInvoices.length > 0 
                ? 'No invoices match the current filters. Try adjusting your filter criteria.'
                : 'No projected invoices available.'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectedInvoices;