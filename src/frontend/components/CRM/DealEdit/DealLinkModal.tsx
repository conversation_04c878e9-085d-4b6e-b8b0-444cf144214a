import React, { useState, useEffect, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "react-query";
import { getDeals, linkDealEstimate } from "../../../api/crm";
import { getDraftEstimate } from "../../../api/estimates";
import { getEstimates as getHarvestEstimates } from "../../../api/harvest";
import { Deal } from "../../../types/crm-types";

interface DealLinkModalProps {
  isOpen: boolean;
  onClose: () => void;
  estimateId: string;
  estimateType: "internal" | "harvest";
}

/**
 * Calculate similarity score between two strings
 * Returns a score between 0 and 1, where 1 is an exact match
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0;
  
  const s1 = str1.toLowerCase().trim();
  const s2 = str2.toLowerCase().trim();
  
  // Exact match
  if (s1 === s2) return 1;
  
  // Check if one contains the other
  if (s1.includes(s2) || s2.includes(s1)) return 0.8;
  
  // Check for common words
  const words1 = s1.split(/\s+/);
  const words2 = s2.split(/\s+/);
  const commonWords = words1.filter(word => words2.includes(word));
  const commonScore = commonWords.length / Math.max(words1.length, words2.length);
  
  // Simple character-based similarity
  const maxLen = Math.max(s1.length, s2.length);
  let matches = 0;
  for (let i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) matches++;
  }
  const charScore = matches / maxLen;
  
  // Return weighted average
  return commonScore * 0.7 + charScore * 0.3;
};

/**
 * Modal for linking an estimate to a deal
 */
const DealLinkModal: React.FC<DealLinkModalProps> = ({
  isOpen,
  onClose,
  estimateId,
  estimateType,
}) => {
  const [selectedDealId, setSelectedDealId] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [estimateName, setEstimateName] = useState<string>("");
  const queryClient = useQueryClient();

  // Fetch deals and filter out closed ones
  const { data: allDeals = [], isLoading: isDealsLoading } = useQuery(
    "deals",
    getDeals,
    {
      enabled: isOpen, // Only fetch when modal is open
      staleTime: 30000, // 30 seconds
    }
  );

  // Filter out closed deals (closed won, closed lost, abandoned)
  const deals = useMemo(() => {
    const closedStages = ["closed won", "closed lost", "abandoned"];
    return allDeals.filter(
      (deal: Deal) => !closedStages.includes(deal.stage.toLowerCase())
    );
  }, [allDeals]);

  // Fetch estimate details to get the name for matching
  const { data: estimateData } = useQuery(
    ["estimate", estimateId, estimateType],
    async () => {
      if (estimateType === "internal") {
        return await getDraftEstimate(estimateId);
      } else {
        // For harvest estimates, we need to find it from the list
        const estimates = await getHarvestEstimates();
        return estimates.find(e => e.id.toString() === estimateId);
      }
    },
    {
      enabled: isOpen && !!estimateId,
      onSuccess: (data) => {
        if (!data) return;
        
        // Set estimate name based on type
        if (estimateType === "internal") {
          setEstimateName(data.projectName || data.clientName || "");
        } else {
          setEstimateName(data.client?.name || data.subject || "");
        }
      },
    }
  );

  // Reset selected deal when modal opens
  useEffect(() => {
    if (isOpen) {
      setSelectedDealId("");
      setSearchTerm("");
      setError(null);
    }
  }, [isOpen]);

  // Calculate suggested matches based on similarity
  const suggestedMatches = useMemo(() => {
    if (!estimateName || deals.length === 0) return [];
    
    const dealsWithScores = deals.map(deal => {
      // Calculate similarity with deal name
      const nameScore = calculateSimilarity(estimateName, deal.name);
      
      // Calculate similarity with company name if available
      const companyScore = deal.company?.name 
        ? calculateSimilarity(estimateName, deal.company.name) 
        : 0;
      
      // Take the higher score
      const score = Math.max(nameScore, companyScore);
      
      return { deal, score };
    });
    
    // Sort by score and take top 3 matches with score > 0.3
    return dealsWithScores
      .filter(item => item.score > 0.3)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(item => item.deal);
  }, [estimateName, deals]);

  // Mutation for linking an estimate to a deal
  const linkEstimateMutation = useMutation(
    (dealId: string) => linkDealEstimate(dealId, estimateId, estimateType),
    {
      onSuccess: (_data, variables) => {
        // Invalidate relevant queries using the dealId from variables
        queryClient.invalidateQueries(["deal", variables]);
        queryClient.invalidateQueries(["deal-estimates", variables]);
        queryClient.invalidateQueries("draftEstimates");
        queryClient.invalidateQueries("harvestEstimates");
        queryClient.invalidateQueries("deals");
        // IMPORTANT: Also invalidate the batch estimates query used by EnhancedDealBoard
        queryClient.invalidateQueries(['all-deal-estimates-batch']);

        // Close the modal
        onClose();
      },
      onError: (err: any) => {
        setError(err.message || "Failed to link estimate to deal");
      },
    }
  );

  // Handle linking the estimate to the selected deal
  const handleLinkEstimate = () => {
    if (!selectedDealId) {
      setError("Please select a deal");
      return;
    }

    linkEstimateMutation.mutate(selectedDealId);
  };

  // Filter deals based on search term
  const filteredDeals = useMemo(() => {
    // If no search term, show all deals except suggested matches
    if (!searchTerm) {
      const suggestedIds = new Set(suggestedMatches.map(d => d.id));
      return deals.filter((deal: Deal) => !suggestedIds.has(deal.id));
    }

    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    return deals.filter((deal: Deal) => {
      return (
        deal.name.toLowerCase().includes(lowerCaseSearchTerm) ||
        deal.stage.toLowerCase().includes(lowerCaseSearchTerm) ||
        (deal.company?.name || "").toLowerCase().includes(lowerCaseSearchTerm)
      );
    });
  }, [deals, searchTerm, suggestedMatches]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Link to Deal
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4 flex-1 overflow-auto">
          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-300 rounded-md">
              {error}
            </div>
          )}

          {/* Estimate being linked */}
          {estimateName && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                Estimate to link:
              </div>
              <div className="font-medium text-gray-900 dark:text-white">
                {estimateName}
              </div>
            </div>
          )}

          {/* Suggested matches */}
          {suggestedMatches.length > 0 && !searchTerm && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Suggested Matches
              </label>
              <div className="space-y-2">
                {suggestedMatches.map((deal: Deal) => (
                  <div
                    key={deal.id}
                    className={`p-3 border rounded-md cursor-pointer transition-colors relative ${
                      selectedDealId === deal.id
                        ? "border-green-500 bg-green-50 dark:bg-green-900/20 dark:border-green-700"
                        : "border-green-300 dark:border-green-600 hover:bg-green-50 dark:hover:bg-green-900/10"
                    }`}
                    onClick={() => setSelectedDealId(deal.id)}
                  >
                    <div className="absolute top-2 right-2">
                      <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded">
                        Suggested
                      </span>
                    </div>
                    <div className="font-medium text-gray-900 dark:text-white pr-20">
                      {deal.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400 flex justify-between">
                      <span>{deal.company?.name || "No company"}</span>
                      <span className="capitalize">{deal.stage}</span>
                    </div>
                    {deal.value && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Value: ${deal.value.toLocaleString()}
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 italic">
                These suggestions are based on name similarity
              </div>
            </div>
          )}

          <div className="mb-4">
            <label
              htmlFor="search-deals"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              {searchTerm ? 'Search Results' : 'All Active Deals'}
            </label>
            <input
              type="text"
              id="search-deals"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white mb-2"
              placeholder="Search by name, stage, or company..."
            />
          </div>

          {isDealsLoading ? (
            <div className="flex justify-center items-center py-8">
              <svg
                className="animate-spin h-8 w-8 text-blue-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : filteredDeals.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No deals found matching your search.
            </div>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {filteredDeals.map((deal: Deal) => (
                <div
                  key={deal.id}
                  className={`p-3 border rounded-md cursor-pointer transition-colors ${
                    selectedDealId === deal.id
                      ? "border-green-500 bg-green-50 dark:bg-green-900/20 dark:border-green-700"
                      : "border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                  }`}
                  onClick={() => setSelectedDealId(deal.id)}
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {deal.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 flex justify-between">
                    <span>{deal.company?.name || "No company"}</span>
                    <span className="capitalize">{deal.stage}</span>
                  </div>
                  {deal.value && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Value: ${deal.value.toLocaleString()}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Warning message */}
        {selectedDealId && (
          <div className="mx-4 mb-4 p-3 bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded">
            <p className="text-xs text-yellow-800 dark:text-yellow-200 font-medium">
              ⚠️ Important: This action is permanent
            </p>
            <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
              Once linked, this estimate cannot be unlinked from the deal. The estimate will control 
              the deal's value, dates, and payment terms.
            </p>
          </div>
        )}

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleLinkEstimate}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!selectedDealId || linkEstimateMutation.isLoading}
          >
            {linkEstimateMutation.isLoading ? "Linking..." : "Link to Deal"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DealLinkModal;
