import { renderHook, act } from '@testing-library/react';
import { useAuthStatus } from '../../../../src/frontend/hooks/useAuthStatus';
import { UserContext } from '../../../../src/frontend/contexts/UserContext';
import React from 'react';

describe('useAuthStatus', () => {
  const createWrapper = (user: any = null, loading = false) => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <UserContext.Provider value={{ user, loading, setUser: jest.fn() }}>
        {children}
      </UserContext.Provider>
    );
    return wrapper;
  };

  it('should return authenticated status when user exists', () => {
    const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>' };
    const wrapper = createWrapper(mockUser);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.user).toEqual(mockUser);
  });

  it('should return unauthenticated status when no user', () => {
    const wrapper = createWrapper(null);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.user).toBeNull();
  });

  it('should return loading status', () => {
    const wrapper = createWrapper(null, true);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.isLoading).toBe(true);
  });

  it('should check for specific permissions', () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      permissions: ['view_estimates', 'edit_estimates']
    };
    const wrapper = createWrapper(mockUser);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.hasPermission('view_estimates')).toBe(true);
    expect(result.current.hasPermission('delete_estimates')).toBe(false);
  });

  it('should check for multiple permissions', () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      permissions: ['view_estimates', 'edit_estimates', 'view_reports']
    };
    const wrapper = createWrapper(mockUser);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.hasAnyPermission(['view_estimates', 'delete_estimates'])).toBe(true);
    expect(result.current.hasAllPermissions(['view_estimates', 'view_reports'])).toBe(true);
    expect(result.current.hasAllPermissions(['view_estimates', 'delete_estimates'])).toBe(false);
  });

  it('should check for role-based access', () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      role: 'admin'
    };
    const wrapper = createWrapper(mockUser);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.hasRole('admin')).toBe(true);
    expect(result.current.hasRole('viewer')).toBe(false);
    expect(result.current.hasAnyRole(['admin', 'manager'])).toBe(true);
  });

  it('should handle permission checks when user is null', () => {
    const wrapper = createWrapper(null);

    const { result } = renderHook(() => useAuthStatus(), { wrapper });

    expect(result.current.hasPermission('any_permission')).toBe(false);
    expect(result.current.hasRole('any_role')).toBe(false);
  });

  it('should memoize authentication status', () => {
    const mockUser = { id: '1', name: 'Test User' };
    const wrapper = createWrapper(mockUser);

    const { result, rerender } = renderHook(() => useAuthStatus(), { wrapper });

    const firstResult = result.current;
    rerender();
    const secondResult = result.current;

    expect(firstResult).toBe(secondResult);
  });
});