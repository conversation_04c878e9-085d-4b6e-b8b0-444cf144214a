import { CashflowSnapshotService } from '../services/cashflow/snapshot-service';
import db from '../api/services/db-init';

// Mock the database
jest.mock('../api/services/db-init', () => ({
  default: {
    run: jest.fn(),
    get: jest.fn(),
    all: jest.fn()
  }
}));

describe('CashflowSnapshotService', () => {
  let snapshotService: CashflowSnapshotService;
  const mockTenantId = 'test-tenant-id';
  const mockDate = '2023-07-01';
  const mockForecast = {
    startingBalance: 10000,
    endingBalance: 15000,
    dailyCashflow: [
      { date: '2023-07-01', balance: 10000, inflows: 1000, outflows: 500 },
      { date: '2023-07-02', balance: 10500, inflows: 2000, outflows: 1000 }
    ]
  };

  beforeEach(() => {
    snapshotService = new CashflowSnapshotService();
    jest.clearAllMocks();

    // Mock Date.now() to return a fixed date
    const mockDate = new Date('2023-07-01');
    jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('createSnapshot', () => {
    it('should create a snapshot successfully', async () => {
      // Mock the database run function to resolve
      (db.run as jest.Mock).mockResolvedValue(undefined);

      // Call the method
      const result = await snapshotService.createSnapshot(mockTenantId, mockForecast);

      // Check that the database was called correctly
      expect(db.run).toHaveBeenCalledWith(
        expect.stringContaining('INSERT OR REPLACE INTO cashflow_snapshots'),
        expect.arrayContaining([expect.any(String), mockTenantId, 90, expect.any(String)])
      );

      // Check that the method returned the date
      expect(result).toBe('2023-07-01');
    });

    it('should throw an error if database operation fails', async () => {
      // Mock the database run function to reject
      (db.run as jest.Mock).mockRejectedValue(new Error('Database error'));

      // Call the method and expect it to throw
      await expect(snapshotService.createSnapshot(mockTenantId, mockForecast))
        .rejects.toThrow('Failed to create snapshot');
    });
  });

  describe('getSnapshot', () => {
    it('should retrieve a snapshot successfully', async () => {
      // Mock the database get function to return a snapshot
      (db.get as jest.Mock).mockResolvedValue({
        snapshot_data: JSON.stringify(mockForecast)
      });

      // Call the method
      const result = await snapshotService.getSnapshot(mockTenantId, mockDate);

      // Check that the database was called correctly
      expect(db.get).toHaveBeenCalledWith(
        expect.stringContaining('SELECT snapshot_data FROM cashflow_snapshots'),
        expect.arrayContaining([mockTenantId, mockDate, 90])
      );

      // Check that the method returned the forecast
      expect(result).toEqual(mockForecast);
    });

    it('should return null if no snapshot is found', async () => {
      // Mock the database get function to return null
      (db.get as jest.Mock).mockResolvedValue(null);

      // Call the method
      const result = await snapshotService.getSnapshot(mockTenantId, mockDate);

      // Check that the method returned null
      expect(result).toBeNull();
    });
  });

  describe('getAvailableDates', () => {
    it('should retrieve available dates successfully', async () => {
      // Mock the database all function to return dates
      (db.all as jest.Mock).mockResolvedValue([
        { date: '2023-07-01' },
        { date: '2023-06-30' }
      ]);

      // Call the method
      const result = await snapshotService.getAvailableDates(mockTenantId);

      // Check that the database was called correctly
      expect(db.all).toHaveBeenCalledWith(
        expect.stringContaining('SELECT date FROM cashflow_snapshots'),
        expect.arrayContaining([mockTenantId, 90])
      );

      // Check that the method returned the dates
      expect(result).toEqual(['2023-07-01', '2023-06-30']);
    });

    it('should return an empty array if no dates are found', async () => {
      // Mock the database all function to return an empty array
      (db.all as jest.Mock).mockResolvedValue([]);

      // Call the method
      const result = await snapshotService.getAvailableDates(mockTenantId);

      // Check that the method returned an empty array
      expect(result).toEqual([]);
    });
  });
});
