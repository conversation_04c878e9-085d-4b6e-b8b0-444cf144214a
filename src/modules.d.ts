
// Module declarations for modules without type definitions
declare module 'cors';
declare module 'better-sqlite3';
declare module 'react/jsx-runtime';
declare module 'react';
declare module 'react-dom/client';

// Express session augmentation
declare namespace Express {
  export interface Session {
    xeroTokenSet?: any;
    tenantId?: string;
    user?: {
      email: string;
      name: string;
      id: string;
    };
  }
}

// Ensure JSX is properly defined
declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any;
  }
}
