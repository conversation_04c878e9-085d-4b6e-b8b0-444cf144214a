# Deployment Guide

This guide provides instructions for deploying the Onbord Financial Dashboard to Render using the simplified auto-deploy workflow.

## Quick Start

Deploy to preview environment:

```bash
git push origin preview
```

Deploy to production environment:

```bash
git push origin main
```

## Deployment Process

The deployment uses <PERSON>der's native auto-deploy feature that:

1. **Detects Changes**: Automatically triggers on git push to configured branch
2. **Builds Application**: Runs standard npm build commands with dependency installation
3. **Deploys**: Uses zero-downtime deployment with health checks
4. **Serves**: Starts application with persistent data and automatic restarts

## Branch Strategy

- **Development**: `preview` branch for staging/testing
- **Production**: `main` branch for live environment

Render services are configured to automatically deploy when changes are pushed to these branches.

## Environment Configuration

### Local Development

Create a `.env` file from `.env.example` with your API credentials.

### Render Environment Variables

Set these in the Render dashboard for each service:

#### Required Variables
- `NODE_ENV=production`
- `HARVEST_ACCESS_TOKEN` - Your Harvest API token
- `HARVEST_ACCOUNT_ID` - Your Harvest account ID
- `XERO_CLIENT_ID` - Xero OAuth application client ID
- `XERO_CLIENT_SECRET` - Xero OAuth application secret
- `SESSION_SECRET` - Random secret for session encryption
- `REDIS_URL` - Redis connection URL (provided by Render add-on)

#### Optional Variables
- `DATABASE_PATH` - Custom database path (defaults to `/data/upstream.db`)

### Render Service Configuration

#### Build Settings
- **Build Command**: `npm ci --legacy-peer-deps --include=dev && npm run build`
- **Start Command**: `npm start`
- **Node Version**: 18+ (specified in `.node-version` file)

#### Infrastructure
- **Plan**: Starter ($7/month) minimum for persistent disk
- **Persistent Disk**: 1GB mounted at `/data` for SQLite database
- **Auto-Deploy**: Enabled for continuous deployment

## Build Process

The build process follows standard Node.js practices:

### Frontend Build
1. **Dependency Installation**: All packages including dev dependencies
2. **React Compilation**: Vite builds optimized production bundle
3. **Asset Processing**: Images, CSS, and other static assets
4. **Output**: Built files in `dist/` directory

### Backend Build
1. **TypeScript Compilation**: Compiles `.ts` files to `.js` in `dist/`
2. **Fallback Strategy**: Uses ts-node if compilation has errors
3. **Migration Copy**: SQL migration files available at runtime
4. **Server Entry**: Robust server startup with multiple path detection

## Database Management

### Automatic Migrations
- Migrations run automatically on application startup
- SQL-based migration files in `/migrations/` directory
- Migration status tracked in database `migrations` table
- Unified schema approach: `000_unified_schema.sql` creates all tables with IF NOT EXISTS
- Additional migrations modify existing structures (e.g., `001_fix_field_ownership_constraint.sql`)

### Migration System Notes
- **Production Safety**: Always test migrations on a database backup first
- **Manual Execution**: Some changes may be applied directly via SQLite shell (e.g., adding columns)
- **Archive Cleanup**: Old unused migrations can be safely removed from `/migrations/archive/`

### Local Migration Commands
```bash
# Check current migration status
npm run migrate:status

# Run pending migrations
npm run migrate
```

## Deployment Workflow

### Standard Development Flow

1. **Feature Development**:
   ```bash
   git checkout preview
   # Make your changes
   git add -A
   git commit -m "feat: description of changes"
   git push origin preview
   ```

2. **Preview Testing**:
   - Visit: https://upstream-preview.onbord.au
   - Test functionality thoroughly
   - Verify database migrations

3. **Production Deployment**:
   ```bash
   git checkout main
   git merge preview
   git push origin main
   ```

4. **Production Verification**:
   - Visit: https://upstream.onbord.au
   - Monitor Render logs for any issues
   - Verify all functionality works

### Hotfix Deployment

For urgent production fixes:

```bash
git checkout main
# Make minimal fix
git add -A
git commit -m "fix: urgent production issue"
git push origin main

# Then update preview branch
git checkout preview
git merge main
git push origin preview
```

## Troubleshooting

### Build Failures

1. **Check Render Logs**: Detailed build output in dashboard
2. **Dependency Issues**: Verify `package.json` and Node version
3. **TypeScript Errors**: Won't block deployment due to fallback
4. **Frontend Build**: Check Vite configuration and React code

### Runtime Issues

1. **Server Won't Start**: Check environment variables and logs
2. **Database Errors**: Verify persistent disk mount and migrations
3. **Static Files**: Ensure `dist/` directory structure is correct
4. **API Errors**: Check external service credentials and rate limits

### Rollback Procedures

#### Quick Rollback (Emergency)
```bash
git checkout main
git log --oneline -10  # Find previous working commit
git reset --hard <commit-hash>
git push --force-with-lease origin main
```

#### Safe Rollback (Planned)
```bash
git checkout main
git revert <problematic-commit>
git push origin main
```

## Monitoring & Observability

### Health Checks
- **Endpoint**: `/api/health`
- **Status**: Returns `{"status": "ok"}` when healthy
- **Automated**: Render monitors this endpoint

### Logging
- **Application Logs**: Available in Render dashboard
- **Migration Logs**: Startup logs show migration execution
- **Error Tracking**: Server errors logged with stack traces

### Performance Monitoring
- **Response Times**: Monitor via Render metrics
- **Database Performance**: SQLite operations in logs
- **External APIs**: Rate limiting and error rates

## Security Considerations

### Environment Variables
- Never commit secrets to repository
- Use Render environment variable groups
- Rotate secrets regularly

### Database Security
- Database file only accessible within Render environment
- Automatic backups via persistent disk snapshots
- No direct external access

### API Security
- OAuth2 for Xero integration
- API tokens for Harvest and HubSpot
- Rate limiting on all endpoints
- CORS configuration for frontend

## Cost Optimization

### Current Costs
- **Preview Environment**: $7/month (Render Starter)
- **Production Environment**: $7/month (Render Starter)
- **Total**: $14/month

### Optimization Options
- **Shared Environment**: Use single service for both (not recommended)
- **Resource Scaling**: Monitor usage and adjust plan as needed
- **Database Management**: Regular cleanup of old data

## Migration Notes

This simplified deployment strategy was implemented in June 2025, replacing the previous complex system with:

### Removed Components
- Custom build scripts (`scripts/build.js`)
- Manual deployment scripts (`scripts/deploy.sh`)
- Pre-built branch system (`pre-built-*` branches)
- Complex fallback logic and error handling

### Benefits Achieved
- **Faster Deployments**: 2-3 minutes vs 5+ minutes
- **Standard Tooling**: Native Render features
- **Easier Onboarding**: Standard git workflow
- **Better Reliability**: Proven platform features
- **Simplified Maintenance**: No custom scripts to debug

The backup of the old system is preserved in the `backup-complex-deployment` branch for reference.