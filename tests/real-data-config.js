/**
 * Jest configuration for real-data tests
 */
/** @type {import('jest').Config} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/tests/real-data'],
  testMatch: [
    '**/?(*.)+(spec|test).+(ts|tsx|js)'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  // Set longer timeout for API calls
  testTimeout: 60000,
  // Silent by default, use VERBOSE=true to see output
  silent: process.env.VERBOSE !== 'true'
};
