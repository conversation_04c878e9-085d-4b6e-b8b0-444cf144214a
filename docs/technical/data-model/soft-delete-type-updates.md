# Type Updates for Soft Delete Support

## Overview

With the unified schema adding `deleted_at` to all tables, we need to ensure all TypeScript interfaces include this field.

## Already Updated ✅

These interfaces already have `deletedAt?: string`:

### In `src/types/shared-types.ts`:
- `BaseCompany` (line 96)
- `BaseContact` (line 135)
- `BaseDeal` (line 178)
- `BaseTender` (line 229)

### Inherited from Base Types:
- `Company` (extends BaseCompany)
- `Contact` (extends BaseContact in CRM types)
- `Deal` (extends BaseDeal in CRM types)

## Need Updates ❌

These interfaces/types need `deletedAt?: string` added:

### Frontend Types (`src/frontend/types/`):
1. **estimate-types.ts**:
   - `StaffAllocation`
   - `EstimateFormData`
   - `SavedEstimate`

2. **activity-types.ts**:
   - `Activity`
   - `ActivityStats`

3. **leads-types.ts**:
   - `Lead`
   - `LeadSource`

### Backend Types (`src/types/`):
1. **financial.ts**:
   - `Expense`
   - `CashflowSnapshot`

2. **project-types.ts**:
   - `Project`
   - `ProjectContact`
   - `ProjectDependency`

3. **index.ts**:
   - `Note`
   - `ContactCompany`
   - `ContactRole`
   - `DealEstimate`
   - `CompanyRelationship`

### API Types (`src/types/api.ts`):
- Check all response/request interfaces

## Repository Updates Needed

All repositories extending `BaseRepository` should be updated to use `BaseRepositoryEnhanced` for automatic soft delete support:

1. `CompanyRepository`
2. `ContactRepository`
3. `DealRepository`
4. `EstimateRepository`
5. `ExpenseRepository`
6. `ProjectRepository`
7. `NoteRepository`
8. `TenderRepository`
9. `ActivityRepository`
10. `EnrichmentRepository`

## Implementation Steps

1. **Update Type Definitions**:
   ```typescript
   interface MyEntity {
     // ... existing fields
     deletedAt?: string;
   }
   ```

2. **Update Repositories**:
   ```typescript
   import { BaseRepositoryEnhanced } from './base-repository-enhanced';
   
   export class MyRepository extends BaseRepositoryEnhanced {
     // Update methods to use enhanced versions
     getAllItems() {
       return this.getAllEnhanced<MyEntity>();
     }
     
     getItemById(id: string) {
       return this.getByIdEnhanced<MyEntity>(id);
     }
   }
   ```

3. **Update UI Components**:
   - Add filters to hide soft-deleted items by default
   - Add "Show Deleted" toggle where appropriate
   - Add restore functionality for admin users

## Testing Checklist

- [ ] All CRUD operations respect soft delete
- [ ] Deleted items don't appear in lists by default
- [ ] Foreign key references handle soft-deleted parents
- [ ] Performance is acceptable with deleted_at filtering
- [ ] Admin can view and restore soft-deleted items