# Pricing Calculation Guide

## Overview

This guide explains how pricing calculations work in the Upstream estimates system, which supports both Daily Rate and Hourly Rate billing models.

## Core Principles

### 1. Two Distinct Billing Models

**Daily Rate Billing**
- Client pays a fixed daily rate regardless of hours worked
- Example: $800/day whether that's 7.5 or 8 hours

**Hourly Rate Billing**
- Client pays for actual hours worked
- Example: $100/hour × actual hours
- A 7.5 hour day costs less than an 8 hour day

### 2. Rate Storage

- **Rates are stored in the format they were entered** (daily or hourly)
- **Time allocations are stored as DAYS** in the database
- The billing type determines how calculations are performed
- Hours per day setting affects pricing for hourly billing only

## How Each Billing Model Works

### Daily Rate Billing
- **Rate Entry**: Enter daily rate (e.g., $800/day)
- **Storage**: `{value: 800, type: 'daily'}`
- **Calculation**: `Total = Daily Rate × Days`
- **Hours/Day Setting**: Only affects hourly reference display

**Example:**
```
Rate: $800/day
Time: 5 days
Total: $800 × 5 = $4,000
(Shows $100/hr @ 8h/day or $106.67/hr @ 7.5h/day for reference only)
```

### Hourly Rate Billing
- **Rate Entry**: Enter hourly rate (e.g., $100/hour)
- **Storage**: `{value: 100, type: 'hourly'}`
- **Calculation**: `Total = Hourly Rate × (Days × Hours/Day)`
- **Hours/Day Setting**: Directly affects total price

**Example with 8 hour days:**
```
Rate: $100/hour
Time: 5 days
Hours/Day: 8
Total: $100 × (5 × 8) = $4,000
```

**Example with 7.5 hour days:**
```
Rate: $100/hour
Time: 5 days
Hours/Day: 7.5
Total: $100 × (5 × 7.5) = $3,750
```

## Toggling Between Billing Models

When switching between billing types, rates are converted to maintain the effective daily rate:

### Example: Complete Toggle Sequence

**Starting point:** Daily billing at $1000/day, 8h/day, 5 days allocated

| Action | Stored Rate | Calculation | Total |
|--------|-------------|-------------|-------|
| Initial state | $1000 daily | $1000 × 5 days | $5000 |
| Toggle to Hourly | $125 hourly | $125 × (5 × 8) | $5000 |
| Change to 7.5h/day | $125 hourly | $125 × (5 × 7.5) | $3750 |
| Toggle to Daily | $937.50 daily | $937.50 × 5 days | $4687.50 |

### Rate Conversion Rules

**Daily → Hourly:** `Hourly Rate = Daily Rate ÷ Hours per Day`

**Hourly → Daily:** `Daily Rate = Hourly Rate × Hours per Day`

## Real-World Examples

### Law Firm (Daily Billing)

**Scenario:** Senior lawyer bills at $1,500/day
```
Monday: Full day = $1,500
Tuesday: Full day = $1,500  
Wednesday: Half day (0.5) = $750
Total: $3,750

Hours worked irrelevant - client pays for availability
```

### Development Agency (Hourly Billing)

**Scenario:** Developer bills at $150/hour, client has 7.5 hour days
```
Week 1: 5 days × 7.5 hours = 37.5 hours × $150 = $5,625
Week 2: 3 days × 7.5 hours = 22.5 hours × $150 = $3,375
Total: $9,000

Precise hour tracking ensures accurate billing
```

### Mixed Team Example

**Project with both daily and hourly resources:**
```
Lawyer (Daily): $1,000/day × 2 days = $2,000
Developer (Hourly, 8h): $125/hour × 40 hours = $5,000
Designer (Hourly, 7.5h): $100/hour × 37.5 hours = $3,750
Total Project: $10,750
```

## Discounts

Discounts are applied to the total revenue before GST:

### Percentage Discount
```
Discount Amount = Total Revenue × (Discount % / 100)
Discounted Revenue = Total Revenue - Discount Amount
```

### Fixed Amount Discount
```
Discounted Revenue = Total Revenue - Discount Amount
```

## GST (Australian Tax)

GST is calculated on the discounted revenue:
```
GST = Discounted Revenue × 0.10 (10%)
Grand Total = Discounted Revenue + GST
```

## Key Features

1. **True Hourly Billing**: Hourly clients are billed based on actual hours worked
2. **Flexible Daily Billing**: Daily clients pay fixed rates regardless of hours
3. **Seamless Toggling**: Switch between billing models with automatic rate conversion
4. **Hours/Day Awareness**: System correctly handles both 7.5 and 8 hour standard days

## Common Questions

**Q: What happens when I switch from daily to hourly billing?**
A: The rate is converted to maintain the same effective daily rate. For example, $800/day becomes $100/hour (at 8h/day) or $106.67/hour (at 7.5h/day).

**Q: Does changing hours/day affect daily billing?**
A: No, daily billing ignores hours/day for pricing. It only affects the hourly reference display.

**Q: Can I have different billing types for different team members?**
A: Yes, each team member's rate is stored with its type, allowing mixed billing within a single estimate.

**Q: How does this work with Harvest integration?**
A: Harvest provides hourly rates. These are converted to daily rates if the estimate uses daily billing.