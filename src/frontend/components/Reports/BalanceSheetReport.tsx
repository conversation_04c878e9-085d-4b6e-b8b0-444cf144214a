import React, { useState } from "react";
import { XeroBalanceSheetResponse } from "../../api/xero";
import { formatCurrency } from "../../utils/format";

interface BalanceSheetReportProps {
  data: XeroBalanceSheetResponse;
}

/**
 * Component for displaying a Xero balance sheet report
 */
const BalanceSheetReport: React.FC<BalanceSheetReportProps> = ({ data }) => {
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [sectionId]: !prev[sectionId],
    }));
  };

  // Extract report data
  const report = data.data?.Reports?.[0] || data.data?.reports?.[0];

  if (!report) {
    return (
      <div className="text-center text-red-500 p-4">
        <p>No balance sheet data found</p>
      </div>
    );
  }

  // Extract report title and date
  const reportTitle =
    report.ReportTitles?.[0] || report.reportTitles?.[0] || "Balance Sheet";

  // Get the date from the report data
  // First try to get it from the API response
  let reportDate = data.date || new Date().toISOString().split("T")[0];

  // Then check if there's a date in the report cells (which is what's displayed in the UI)
  // This handles cases where Xero returns data for a different date than requested
  const headerRow = report.Rows?.[0] || report.rows?.[0];
  if (headerRow) {
    const headerCells = headerRow.Cells || headerRow.cells || [];
    if (headerCells.length > 0) {
      const headerValue =
        headerCells[headerCells.length - 1].Value ||
        headerCells[headerCells.length - 1].value;
      if (headerValue && typeof headerValue === "string") {
        // If the header contains a date, use that instead
        if (headerValue.includes("20")) {
          // Simple check for a year
          console.log(`Found date in report header: ${headerValue}`);
          // Don't override the reportDate variable as we want to keep the requested date
          // Instead, we'll display both dates in the UI
        }
      }
    }
  }

  // Extract report rows
  const rows = report.Rows || report.rows || [];

  // Render a row based on its type
  const renderRow = (row: any, level: number = 0, parentId: string = "") => {
    // Generate a unique ID for this row
    const rowId = `${parentId}-${
      row.RowID || row.rowID || Math.random().toString(36).substring(2, 9)
    }`;

    // Determine if this row is a section
    const isSection = row.RowType === "Section" || row.rowType === "Section";

    // Determine if this row has children
    const hasChildren =
      (row.Rows && row.Rows.length > 0) || (row.rows && row.rows.length > 0);

    // Determine if this section is expanded
    const isExpanded = expandedSections[rowId] !== false; // Default to expanded

    // Get the row title
    const title = row.Title || row.title || "";

    // Get the row cells
    const cells = row.Cells || row.cells || [];

    // Get the row value (usually in the last cell)
    const value =
      cells.length > 0
        ? cells[cells.length - 1].Value || cells[cells.length - 1].value
        : null;

    // Format the value if it's a number
    const formattedValue =
      typeof value === "number" ? formatCurrency(value) : value;

    // Determine the indentation based on level
    const indentClass = `pl-${level * 4}`;

    // Determine text style based on level and type
    const textClass = isSection
      ? "font-bold text-gray-900 dark:text-white"
      : "text-gray-700 dark:text-gray-300";

    // Render the row
    return (
      <React.Fragment key={rowId}>
        <tr
          className={`border-b border-gray-200 dark:border-gray-700 ${
            isSection ? "bg-gray-50 dark:bg-gray-800" : ""
          }`}
        >
          <td className={`py-2 ${indentClass} ${textClass}`}>
            {hasChildren && isSection ? (
              <button
                onClick={() => toggleSection(rowId)}
                className="flex items-center focus:outline-none"
              >
                <svg
                  className={`w-4 h-4 mr-1 transition-transform ${
                    isExpanded ? "transform rotate-90" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
                {title}
              </button>
            ) : (
              <span className={`${hasChildren ? "ml-5" : ""}`}>{title}</span>
            )}
          </td>
          <td className={`py-2 text-right ${textClass}`}>{formattedValue}</td>
        </tr>

        {/* Render children if expanded */}
        {hasChildren &&
          isExpanded &&
          (row.Rows || row.rows).map((childRow: any) =>
            renderRow(childRow, level + 1, rowId)
          )}
      </React.Fragment>
    );
  };

  // Get the actual date from the Xero response
  const getActualDateFromXero = () => {
    if (
      headerRow &&
      headerRow.Cells &&
      headerRow.Cells.length > 0 &&
      headerRow.Cells[headerRow.Cells.length - 1].Value &&
      typeof headerRow.Cells[headerRow.Cells.length - 1].Value === "string" &&
      headerRow.Cells[headerRow.Cells.length - 1].Value.includes("20")
    ) {
      return headerRow.Cells[headerRow.Cells.length - 1].Value;
    }

    if (
      headerRow &&
      headerRow.cells &&
      headerRow.cells.length > 0 &&
      headerRow.cells[headerRow.cells.length - 1].value &&
      typeof headerRow.cells[headerRow.cells.length - 1].value === "string" &&
      headerRow.cells[headerRow.cells.length - 1].value.includes("20")
    ) {
      return headerRow.cells[headerRow.cells.length - 1].value;
    }

    return null;
  };

  const actualDate = getActualDateFromXero();

  // Check if the actual date from Xero is different from the requested date
  // Extract years from both dates for comparison
  const getYearFromDate = (dateStr: string) => {
    // Try to extract a 4-digit year from the date string
    const yearMatch = dateStr.match(/20\d\d/);
    return yearMatch ? yearMatch[0] : null;
  };

  const requestedYear = getYearFromDate(reportDate);
  const actualYear = actualDate ? getYearFromDate(actualDate) : null;

  // Check if years are different
  const hasYearMismatch =
    requestedYear && actualYear && requestedYear !== actualYear;

  // Check if dates are completely different (not just formatting)
  const datesAreDifferent = actualDate && actualDate !== reportDate;

  return (
    <div>
      <div className="mb-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {reportTitle}
        </h2>

        {/* Show the requested date */}
        <p className="text-sm text-gray-500 dark:text-gray-400">
          As of {reportDate}
        </p>

        {/* Display actual date from Xero if different from requested date */}
        {datesAreDifferent && (
          <div className="mt-1">
            {hasYearMismatch ? (
              <p className="text-xs text-yellow-600 dark:text-yellow-400">
                <span className="font-medium">Note:</span> Data shown is from{" "}
                {actualDate} ({actualYear}). This may be due to how Xero handles
                Australian financial years (July-June).
              </p>
            ) : (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Data shown is for {actualDate}
              </p>
            )}
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Account
              </th>
              <th className="py-3 px-4 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Balance
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
            {rows.map((row: any) => renderRow(row))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BalanceSheetReport;
