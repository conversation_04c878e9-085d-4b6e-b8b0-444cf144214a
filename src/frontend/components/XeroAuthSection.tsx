import React, { useState, useEffect } from 'react'; import axios from 'axios'; interface XeroAuthSectionProps { onAuthenticated: () => void; } /** * Component for handling Xero authentication * This component is responsible for the Xero authentication flow UI * It does NOT perform authentication checks - that's handled by useAuthStatus */ const XeroAuthSection: React.FC<XeroAuthSectionProps> = ({ onAuthenticated }) => { const [error, setError] = useState<string | null>(null); const [loading, setLoading] = useState<boolean>(false); // Handle connect to Xero button click const handleConnectClick = () => { setLoading(true); window.location.href = '/api/xero/auth'; }; // Check URL parameters for auth success useEffect(() => { const urlParams = new URLSearchParams(window.location.search); // If we have the auth_success parameter, call onAuthenticated if (urlParams.get('auth_success') === 'true') { console.log('Auth success parameter detected, triggering onAuthenticated'); // Clean up URL if (window.history && window.history.replaceState) { window.history.replaceState({}, document.title, window.location.pathname); } // Notify parent that authentication is complete onAuthenticated(); } // Check for auth code and state (OAuth callback parameters) const authCode = urlParams.get('code'); const stateParam = urlParams.get('state'); if (authCode && stateParam) { console.log('OAuth callback parameters detected, cleaning up URL'); // Clean up URL if (window.history && window.history.replaceState) { window.history.replaceState({}, document.title, window.location.pathname); } } }, [onAuthenticated]); if (loading) { return ( <div className="card mb-6 border border-gray-100 dark:border-gray-700"> <div className="flex justify-center items-center py-12"> <svg className="animate-spin h-8 w-8 text-secondary dark:text-secondary-light mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path> </svg> <span className="text-lg text-gray-600 dark:text-gray-300">Redirecting to Xero...</span> </div> </div> ); } if (error) { return ( <div className="card bg-red-50 text-center py-8 px-6 max-w-2xl mx-auto mb-6 border border-red-200 animate-fadeIn"> <svg className="w-12 h-12 text-accent mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20"> <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" /> </svg> <h3 className="text-xl font-semibold text-accent mb-3">Authentication Error</h3> <p className="mb-5 text-gray-700 dark:text-gray-300">{error}</p> <button onClick={() => window.location.reload()} className="btn btn-danger" > <span className="flex items-center justify-center"> <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /> </svg> Retry </span> </button> </div> ); } return ( <div className="card text-center py-8 px-6 max-w-2xl mx-auto mb-6 border border-blue-100 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/20"> <button onClick={handleConnectClick} className="w-full sm:w-auto px-4 py-3 sm:py-2 h-12 sm:h-10 bg-primary text-white rounded-lg active:bg-primary-dark transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary tap-highlight-transparent touch-manipulation font-medium mb-6" > <span className="flex items-center justify-center"> Connect to Xero </span> </button> <p className="text-base text-gray-700 dark:text-gray-300 mb-6 max-w-md mx-auto"> To view your cash flow projections, you need to connect to your Xero account. This will allow us to fetch your financial data. </p> <p className="text-xs text-gray-500 dark:text-gray-400 mt-4"> Your data is securely handled according to Xero's privacy policy. </p> </div> ); }; export default XeroAuthSection;