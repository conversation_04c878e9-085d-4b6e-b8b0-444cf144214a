import { fetchFrom<PERSON><PERSON> } from './utils';

export interface Project {
  id: string;
  name: string;
  client: string;
  budgetRemaining: number;
  uninvoicedAmount: number;
  startDate: Date | null;
  endDate: Date | null;
  status: string;
}

export interface ProjectSetting {
  projectId: string;
  invoiceFrequency: 'weekly' | 'biweekly' | 'monthly' | 'custom';
  invoiceIntervalDays: number;
  paymentTerms: number; // days
}

// Define the ProjectedInvoice interface here to avoid circular dependencies
export interface ProjectedInvoice {
  id: string;
  projectId: string;
  projectName: string;
  clientName: string;
  amount: number;
  type: 'uninvoiced_work' | 'future_work'; // Removed 'outstanding_invoice' as it's handled by cashflow projection
  invoiceDate: Date;
  paymentDate: Date;
}

// Simplified Client interface for frontend use
export interface Client {
  id: number;
  name: string;
}

// Simplified User interface for frontend use
export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  roles: string[];
  default_hourly_rate: number | null;
  cost_rate: number | null;
  is_active: boolean;
  access_roles: string[]; // Added for permissions check
  avatar_url?: string; // Profile photo URL from Harvest
}

// Interface for the current user details (subset of User)
export interface CurrentUser {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  access_roles: string[]; // e.g., ["projects_manager", "estimates_manager", "administrator"]
}

// Interface for staff utilization data
export interface StaffUtilization {
  userId: number;
  userName: string;
  billableHours: number;
  totalHours: number;
  weeklyCapacity: number; // in hours, not seconds
  utilization: number; // percentage (0-100)
  harvestUtilization: number; // Harvest's calculation (billable/total)
  avatarUrl?: string;
  isContractor: boolean;
}

// Interface for fixed price project income
export interface FixedPriceProject {
  projectId: number;
  projectName: string;
  clientId: number;
  clientName: string;
  billableAmount: number;
  currency: string;
}

// Interface for late (overdue) invoice
export interface LateInvoice {
  id: string;
  invoiceNumber: string;
  reference: string;
  dueDate: Date;
  amount: number;
  client: string;
  status: string;
  daysPastDue: number;
}

// Interface for late invoices response
export interface LateInvoicesResponse {
  invoices: LateInvoice[];
  totalAmount: number;
  count: number;
}


// Interface for Estimate Line Item payload
export interface EstimateLineItemPayload {
  kind: string; // e.g., "Service"
  description: string;
  quantity: number; // Total days/hours
  unit_price: number; // Daily/hourly rate
  taxed?: boolean; // Whether tax applies to this line item
  taxed2?: boolean; // Whether second tax applies
}

// Interface for Estimate payload
export interface EstimatePayload {
  client_id: number;
  number?: string;
  purchase_order?: string;
  tax?: number;
  tax2?: number;
  discount?: number;
  subject?: string;
  notes?: string;
  currency?: string; // e.g., "USD"
  issue_date?: string; // Format: YYYY-MM-DD
  line_items: EstimateLineItemPayload[];
}

// Interface for the response when saving an estimate (simplified)
export interface SaveEstimateResponse {
  id: number; // The ID of the created Harvest estimate
  number: string;
  // Add other relevant fields from the Harvest response if needed
}


/**
 * Get project data from Harvest
 * @returns Project data
 */
export const getProjects = async (): Promise<Project[]> => {
  const response = await fetchFromApi('/api/harvest/projects');
  return response.data;
};

/**
 * Save project settings
 * @param settings Project settings
 * @returns Success response
 */
export const saveProjectSettings = async (settings: ProjectSetting[]): Promise<{ success: boolean }> => {
  const response = await fetchFromApi('/api/harvest/project-settings', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(settings),
  });
  return response;
};

/**
 * Get project settings
 * @returns Project settings
 */
export const getProjectSettings = async (): Promise<ProjectSetting[]> => {
  const response = await fetchFromApi('/api/harvest/project-settings');
  return response.data;
};

/**
 * Get projected invoices
 * @param timeframe Number of days ahead
 * @returns Projected invoices
 */
export const getProjectedInvoices = async (timeframe: number = 90): Promise<ProjectedInvoice[]> => {
  const response = await fetchFromApi(`/api/harvest/projected-invoices?timeframe=${timeframe}`);

  // Convert date strings to Date objects
  const projectedInvoices = response.data.map((invoice: any) => ({
    ...invoice,
    invoiceDate: new Date(invoice.invoiceDate),
    paymentDate: new Date(invoice.paymentDate)
  }));

  return projectedInvoices;
};

/**
 * Get client data from Harvest
 * @returns List of clients
 */
export const getClients = async (): Promise<Client[]> => {
  // Assumes a backend endpoint exists at /api/harvest/clients
  const response = await fetchFromApi('/api/harvest/clients');
  return response.data; // Assuming the backend returns data in this structure
};

/**
 * Publish an estimate to Harvest via the backend
 * @param estimatePayload The estimate data to publish
 * @returns The created estimate details from Harvest
 */
export const publishEstimate = async (estimatePayload: EstimatePayload): Promise<SaveEstimateResponse> => {
  // Assumes a backend endpoint exists at /api/harvest/estimates
  const response = await fetchFromApi('/api/harvest/estimates', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(estimatePayload),
  });
  // Assuming the backend forwards the relevant response data from Harvest
  return response.data;
};

/**
 * Get all estimates from Harvest
 * @param params Optional query parameters
 * @returns List of estimates
 */
export const getEstimates = async (params: {
  client_id?: number;
  from?: string;
  to?: string;
  state?: string;
} = {}): Promise<any[]> => {
  const queryString = new URLSearchParams(
    Object.entries(params).reduce((acc, [key, value]) => {
      if (value !== undefined) acc[key] = String(value);
      return acc;
    }, {} as Record<string, string>)
  ).toString();
  const url = queryString ? `/api/harvest/estimates?${queryString}` : '/api/harvest/estimates';
  const response = await fetchFromApi(url);
  return response.data;
};

/**
 * Get a single estimate by ID
 * @param estimateId The ID of the estimate to retrieve
 * @returns The estimate details
 */
export const getEstimate = async (estimateId: number): Promise<any> => {
  const response = await fetchFromApi(`/api/harvest/estimates/${estimateId}`);
  return response.data;
};

/**
 * Get users from Harvest with optional filtering
 * @param params Query parameters (e.g., { is_active: true })
 * @returns List of users
 */
export const getUsers = async (params: {
  is_active?: boolean;
  role?: string;
} = {}): Promise<User[]> => {
  const queryString = new URLSearchParams(
    Object.entries(params).reduce((acc, [key, value]) => {
      if (value !== undefined) acc[key] = String(value);
      return acc;
    }, {} as Record<string, string>)
  ).toString();
  const url = queryString ? `/api/harvest/users?${queryString}` : '/api/harvest/users';
  const response = await fetchFromApi(url);
  return response.data;
};

/**
 * Get active user data from Harvest
 * @returns List of active users
 */
export const getActiveUsers = async (): Promise<User[]> => {
  // Uses the more generic getUsers function with a filter
  return getUsers({ is_active: true });
};

/**
 * Get the currently authenticated user's details from Harvest
 * @returns Current user details including access roles
 */
export const getCurrentUser = async (): Promise<CurrentUser> => {
  // Assumes a backend endpoint exists at /api/harvest/users/me
  const response = await fetchFromApi('/api/harvest/users/me');
  return response.data; // Assuming the backend returns data in this structure
};

/**
 * Get staff utilization data
 * @param fromDate Start date in YYYY-MM-DD format
 * @param toDate End date in YYYY-MM-DD format
 * @returns Staff utilization data
 */
export const getStaffUtilization = async (fromDate: string, toDate: string): Promise<StaffUtilization[]> => {
  const response = await fetchFromApi(`/api/harvest/reports/utilization?from=${fromDate}&to=${toDate}`);
  return response.data;
};

/**
 * Get fixed price project income for a given time period
 * @param fromDate Start date in YYYY-MM-DD format
 * @param toDate End date in YYYY-MM-DD format
 * @returns Fixed price project income data
 */
export const getFixedPriceProjectIncome = async (fromDate: string, toDate: string): Promise<FixedPriceProject[]> => {
  const response = await fetchFromApi(`/api/harvest/reports/fixed-price-income?from=${fromDate}&to=${toDate}`);
  return response.data;
};

/**
 * Get late (overdue) invoices from Harvest
 * @returns Late invoices response with invoices, total amount, and count
 */
export const getLateInvoices = async (): Promise<LateInvoicesResponse> => {
  const response = await fetchFromApi('/api/harvest/late-invoices');

  // Convert date strings to Date objects
  const data = response.data;
  const invoices = data.invoices.map((invoice: any) => ({
    ...invoice,
    dueDate: new Date(invoice.dueDate)
  }));

  return {
    invoices,
    totalAmount: data.totalAmount,
    count: data.count
  };
};
