/**
 * Transaction Service Test
 * 
 * This test verifies that the transaction service correctly transforms
 * data between external API formats and our internal transaction format.
 * These tests can be run without real API connections.
 */
import { TransactionService } from '@/services/cashflow/transaction-service';
import { Transaction } from '@/types/financial';

describe('TransactionService', () => {
  let service: TransactionService;
  
  beforeEach(() => {
    service = new TransactionService();
  });
  
  describe('transformExpensesToTransactions', () => {
    it('transforms expense data correctly', () => {
      // Explicit test data - no mocks
      const expenses = [
        { 
          id: 'exp1', 
          type: 'expense', 
          reference: 'Office Supplies', 
          date: new Date('2025-04-10'), 
          source: 'Manual', 
          amount: 150.75 
        },
        { 
          name: 'Software Subscription', 
          date: new Date('2025-04-15'), 
          amount: 49.99 
        }
      ];
      
      const result = service.transformExpensesToTransactions(expenses);
      
      // Verify transformation
      expect(result).toHaveLength(2);
      
      // First expense should keep its ID and properties
      expect(result[0].id).toBe('exp1');
      expect(result[0].type).toBe('expense');
      expect(result[0].description).toBe('Office Supplies');
      expect(result[0].date).toEqual(new Date('2025-04-10'));
      expect(result[0].source).toBe('Manual');
      expect(result[0].amount).toBe(150.75);
      
      // Second expense should get defaults for missing properties
      expect(result[1].id).toMatch(/^expense-\d+-\d+/); // Generated ID
      expect(result[1].type).toBe('expense');
      expect(result[1].description).toBe('Software Subscription');
      expect(result[1].date).toEqual(new Date('2025-04-15'));
      expect(result[1].source).toBe('Expense');
      expect(result[1].amount).toBe(49.99);
    });
    
    it('handles empty or null input gracefully', () => {
      expect(service.transformExpensesToTransactions([])).toEqual([]);
      expect(service.transformExpensesToTransactions(null as any)).toEqual([]);
      expect(service.transformExpensesToTransactions(undefined as any)).toEqual([]);
    });
  });
  
  describe('transformBillsToTransactions', () => {
    it('transforms bill data correctly', () => {
      // Explicit test data - no mocks
      const bills = [
        { 
          id: 'bill1', 
          type: 'repeating_bill', 
          reference: 'Rent', 
          date: new Date('2025-05-01'), 
          amount: 2500 
        },
        { 
          reference: 'Utilities', 
          date: new Date('2025-05-05'), 
          amount: 300.50 
        }
      ];
      
      const result = service.transformBillsToTransactions(bills);
      
      // Verify transformation
      expect(result).toHaveLength(2);
      
      // First bill should keep its ID and transform properties
      expect(result[0].id).toBe('bill1');
      expect(result[0].type).toBe('bill');
      expect(result[0].description).toBe('Rent');
      expect(result[0].date).toEqual(new Date('2025-05-01'));
      expect(result[0].source).toBe('xero');
      expect(result[0].amount).toBe(2500);
      expect(result[0].metadata).toEqual({ originalType: 'repeating_bill' });
      
      // Second bill should get defaults for missing properties
      expect(result[1].id).toMatch(/^bill-\d+-\d+/); // Generated ID
      expect(result[1].type).toBe('bill');
      expect(result[1].description).toBe('Utilities');
      expect(result[1].date).toEqual(new Date('2025-05-05'));
      expect(result[1].source).toBe('xero');
      expect(result[1].amount).toBe(300.50);
      expect(result[1].metadata).toEqual({ originalType: 'repeating_bill' });
    });
  });
  
  describe('transformInvoicesToTransactions', () => {
    it('transforms invoice data correctly', () => {
      // Explicit test data - no mocks
      const invoices = [
        { 
          id: 'inv1', 
          what: 'Project Alpha', 
          date: new Date('2025-04-20'), 
          amount: 5000, 
          metadata: { client: 'Client A' } 
        },
        { 
          what: 'Consulting Services', 
          date: new Date('2025-04-25'), 
          amount: 1200 
        }
      ];
      
      const result = service.transformInvoicesToTransactions(invoices);
      
      // Verify transformation
      expect(result).toHaveLength(2);
      
      // First invoice should keep its ID and transform properties
      expect(result[0].id).toBe('inv1');
      expect(result[0].type).toBe('invoice');
      expect(result[0].description).toBe('Project Alpha');
      expect(result[0].date).toEqual(new Date('2025-04-20'));
      expect(result[0].source).toBe('harvest');
      expect(result[0].amount).toBe(5000);
      expect(result[0].metadata).toEqual({ client: 'Client A' });
      
      // Second invoice should get defaults for missing properties
      expect(result[1].id).toMatch(/^invoice-\d+-\d+/); // Generated ID
      expect(result[1].type).toBe('invoice');
      expect(result[1].description).toBe('Consulting Services');
      expect(result[1].date).toEqual(new Date('2025-04-25'));
      expect(result[1].source).toBe('harvest');
      expect(result[1].amount).toBe(1200);
      expect(result[1].metadata).toEqual({});
    });
  });
});
