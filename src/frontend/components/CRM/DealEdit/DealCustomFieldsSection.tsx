import React, { useState } from 'react'; import { Deal, DealUpdate } from '../../../types/crm-types'; interface DealCustomFieldsSectionProps { deal: Deal; isEditing: boolean; formData: DealUpdate; onChange: (field: keyof DealUpdate, value: any) => void; } /** * Component for displaying and editing custom fields */ const DealCustomFieldsSection: React.FC<DealCustomFieldsSectionProps> = ({ deal, isEditing, formData, onChange }) => { const [newFieldKey, setNewFieldKey] = useState(''); const [newFieldValue, setNewFieldValue] = useState(''); // Get custom fields from form data or deal const customFields = formData.customFields || deal.customFields || {}; // Add a new custom field const handleAddCustomField = () => { if (!newFieldKey.trim()) return; const updatedCustomFields = { ...customFields, [newFieldKey]: newFieldValue }; onChange('customFields', updatedCustomFields); setNewFieldKey(''); setNewFieldValue(''); }; // Update an existing custom field const handleUpdateCustomField = (key: string, value: string) => { const updatedCustomFields = { ...customFields, [key]: value }; onChange('customFields', updatedCustomFields); }; // Remove a custom field const handleRemoveCustomField = (key: string) => { const updatedCustomFields = { ...customFields }; delete updatedCustomFields[key]; onChange('customFields', updatedCustomFields); }; // Check if there are any custom fields const hasCustomFields = Object.keys(customFields).length > 0; return ( <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mt-6"> <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"> <div> <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white"> Custom Fields </h3> <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400"> Additional information about the deal. </p> </div> {isEditing && ( <div className="flex items-center"> <span className="text-sm text-gray-500 dark:text-gray-400 mr-2"> These fields are preserved during HubSpot imports </span> </div> )} </div> <div className="px-4 py-5 sm:p-6"> {/* Display custom fields */} {hasCustomFields ? ( <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6"> {Object.entries(customFields).map(([key, value]) => ( <div key={key} className="sm:col-span-3"> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300"> {key} </label> <div className="mt-1 flex"> {isEditing ? ( <> <input type="text" value={value as string} onChange={(e) => handleUpdateCustomField(key, e.target.value)} className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white" /> <button type="button" onClick={() => handleRemoveCustomField(key)} className="ml-2 inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" > <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" /> </svg> </button> </> ) : ( <div className="text-sm text-gray-900 dark:text-gray-200"> {value as string} </div> )} </div> </div> ))} </div> ) : ( <div className="text-sm text-gray-500 dark:text-gray-400"> No custom fields have been added yet. </div> )} {/* Add new custom field form */} {isEditing && ( <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6"> <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4"> Add New Custom Field </h4> <div className="flex items-end space-x-4"> <div className="flex-1"> <label htmlFor="newFieldKey" className="block text-sm font-medium text-gray-700 dark:text-gray-300"> Field Name </label> <input type="text" id="newFieldKey" value={newFieldKey} onChange={(e) => setNewFieldKey(e.target.value)} className="mt-1 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="e.g., Project Code" /> </div> <div className="flex-1"> <label htmlFor="newFieldValue" className="block text-sm font-medium text-gray-700 dark:text-gray-300"> Field Value </label> <input type="text" id="newFieldValue" value={newFieldValue} onChange={(e) => setNewFieldValue(e.target.value)} className="mt-1 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white" placeholder="e.g., PRJ-123" /> </div> <button type="button" onClick={handleAddCustomField} disabled={!newFieldKey.trim()} className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed" > Add Field </button> </div> </div> )} </div> </div> ); }; export default DealCustomFieldsSection; 