# Database Initialization Guide

## Overview

The Onbord Financial Dashboard uses SQLite with a unified schema approach. All database tables are defined in a single schema file: `/migrations/000_unified_schema.sql`.

## Database Initialization Process

### Fresh Deployment (Empty Database)

When the application starts with no existing database:

1. **Server Startup**: The server calls `initializeDatabase()` from `src/database/index.ts`
2. **Schema Detection**: Checks for existing schema version
3. **Schema Creation**: Loads and executes `000_unified_schema.sql`
4. **Result**: Complete database with all 31 tables including `radar_action_items`

### Existing Deployment (Database Exists)

When the application starts with an existing database:

1. **Server Startup**: The server calls `initializeDatabase()`
2. **Schema Verification**: Checks current schema version
3. **Missing Table Detection**: Specifically checks for critical tables:
   - `note` table (common issue after refactors)
   - `radar_action_items` table (added for action tracking)
4. **Table Creation**: Creates any missing tables with proper indexes
5. **Result**: Database updated with all required tables

## Key Components

### Schema File: `/migrations/000_unified_schema.sql`

The unified schema contains 31 tables organized by category:

- **CRM Tables** (8): company, contact, deal, note, contact_company, contact_role, deal_estimate, radar_action_items
- **Financial Tables** (6): estimate, estimate_allocation, estimate_time_allocation, expense, project, project_contact
- **Integration Tables** (4): harvest_invoice_cache, hubspot_settings, field_ownership, settings
- **Enrichment Tables** (3): company_enrichment, contact_enrichment, enrichment_log
- **Audit/System Tables** (4): activity_feed, change_log, migrations, cashflow_snapshot
- **Relationship Tables** (4): contact_relationships, company_relationship, project_contact, project_dependency
- **Other Tables** (2): tender, opportunity_intelligence

### Database Initialization Code: `src/database/index.ts`

Key features:
- Automatic schema detection and creation
- Self-healing for missing tables
- Non-destructive updates (never drops existing data)
- Proper error handling and logging

## Migration System

The application uses a simple migration approach:

1. **Initial Setup**: Uses `000_unified_schema.sql` for fresh installations
2. **Updates**: Individual migration files in `/migrations/` for schema changes
3. **Tracking**: Migration status tracked in `migrations` table
4. **Safety**: All operations use `CREATE TABLE IF NOT EXISTS` to prevent errors

## Manual Database Operations

### Reset Database (DESTROYS ALL DATA)
```bash
node scripts/initialize-fresh-database.js --force
```

### Run Migrations (Preserves Data)
```bash
npm run migrate
```

### Check Migration Status
```bash
npm run migrate:status
```

## Production Considerations

- **Data Persistence**: Database stored at `/data/upstream.db` on Render.com
- **Automatic Initialization**: No manual steps required for deployment
- **Self-Healing**: Missing tables are automatically created
- **Backup Strategy**: Render's persistent disk includes automatic snapshots

## Troubleshooting

### "No such table" Errors

If you encounter table not found errors:

1. Check if the table exists in `000_unified_schema.sql`
2. Add table creation logic to `src/database/index.ts` if it's a critical table
3. For non-critical tables, run migrations manually

### Schema Version Mismatch

If schema version is out of sync:

1. Check `schema_version` table for current version
2. Compare with expected version in migrations
3. Run `npm run migrate` to apply pending migrations

### Database Lock Errors

SQLite database lock errors are handled with:
- 30-second busy timeout
- Write-Ahead Logging (WAL) mode
- Proper transaction management

## Development Tips

1. **Always use the unified schema** as the source of truth
2. **Never modify `000_unified_schema.sql`** directly - create new migration files
3. **Test database changes** on a fresh database before deploying
4. **Use transactions** for multi-table operations
5. **Check table existence** before assuming it exists in code