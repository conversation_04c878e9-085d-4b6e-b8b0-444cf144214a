import React from "react"; import { CustomExpense } from "../../../types"; import { formatCurrency, formatDate, getTypeLabel, getFrequencyLabel, getTypeClass, getFrequencyClass, isXeroExpense, isGSTExpense, } from "./utils"; import { getCurrentQuarterInfo } from "../../utils/quarter-utils"; import XeroBadge from "../shared/XeroBadge"; import PredictedBadge from "../shared/PredictedBadge"; import AccruedBadge from "../shared/AccruedBadge"; import { Card } from "../shared/Card"; interface ExpenseListItemProps { expense: CustomExpense; onEdit: (expense: CustomExpense) => void; onDelete: (id: string) => void; loading: boolean; isEditing: boolean; isAddingNew: boolean; deletingId: string | null; isMobile: boolean; } /** * Component for displaying a single expense item in mobile view */ export const ExpenseListItemMobile: React.FC<ExpenseListItemProps> = ({ expense, onEdit, onDelete, loading, isEditing, isAddingNew, deletingId, }) => { return ( <Card variant="interactive" shadow="sm" className="border-l-4 border-l-gray-200 hover:border-l-primary-400 transition-colors duration-200" > <div className="flex justify-between items-center mb-3"> <div className="flex items-center"> <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate mr-2"> {expense.name} </h3> <div className="flex items-center space-x-1"> {isXeroExpense(expense) && <XeroBadge className="ml-1" />} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === true && ( <PredictedBadge className="ml-1" /> )} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === false && ( <AccruedBadge className="ml-1" quarterInfo={getCurrentQuarterInfo()} /> )} </div> </div> <span className="font-bold text-accent whitespace-nowrap"> {formatCurrency(expense.amount)} </span> </div> <div className="flex justify-between items-center mb-3"> <div className="flex items-center text-gray-600 dark:text-gray-400"> <svg className="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" > <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" /> </svg> <span className="text-sm">{formatDate(expense.date)}</span> </div> </div> <div className="flex flex-wrap justify-between items-center"> <div className="flex items-center mb-2 sm:mb-0 space-x-2"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getTypeClass( expense.type )}`} > {getTypeLabel(expense.type)} </span> <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getFrequencyClass( expense.frequency )}`} > {getFrequencyLabel(expense.frequency)} </span> </div> <div className="flex space-x-2"> <button onClick={() => onEdit(expense)} className={`inline-flex items-center justify-center px-2 py-1 border text-xs font-medium rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 w-[26px] h-[26px] ${ isXeroExpense(expense) ? "border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed" : "border-secondary text-secondary hover:bg-secondary/5 focus:ring-secondary/50" }`} disabled={ loading || isAddingNew || isEditing || deletingId !== null || isXeroExpense(expense) } aria-label="Edit expense" title={ isXeroExpense(expense) ? "Xero expenses cannot be edited directly. Use the sync feature instead." : "Edit expense" } > <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /> </svg> </button> <button onClick={() => onDelete(expense.id)} className="inline-flex items-center justify-center px-2 py-1 border border-accent text-accent text-xs font-medium rounded shadow-sm hover:bg-accent/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent/50 w-[26px] h-[26px]" disabled={ loading || isAddingNew || isEditing || deletingId !== null } aria-label="Delete expense" > {deletingId === expense.id ? ( <svg className="animate-spin h-3 w-3 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> ) : ( <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /> </svg> )} </button> </div> </div> </Card> ); }; /** * Component for displaying a single expense item in desktop view (table row) */ export const ExpenseListItemDesktop: React.FC<ExpenseListItemProps> = ({ expense, onEdit, onDelete, loading, isEditing, isAddingNew, deletingId, }) => { return ( <tr className="table-row hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"> <td className="table-cell font-medium text-gray-900 dark:text-gray-100"> <div className="flex items-center"> <span className="mr-2">{expense.name}</span> <div className="flex items-center space-x-1"> {isXeroExpense(expense) && <XeroBadge className="ml-1" />} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === true && ( <PredictedBadge className="ml-1" /> )} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === false && ( <AccruedBadge className="ml-1" quarterInfo={getCurrentQuarterInfo()} /> )} </div> </div> </td> <td className="table-cell w-40"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded border text-xs font-medium ${getTypeClass( expense.type )}`} > {getTypeLabel(expense.type)} </span> </td> <td className="table-cell font-bold text-accent"> {formatCurrency(expense.amount)} </td> <td className="table-cell w-28">{formatDate(expense.date)}</td> <td className="table-cell"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded border text-xs font-medium ${getFrequencyClass( expense.frequency )}`} > {getFrequencyLabel(expense.frequency)} </span> </td> <td className="table-cell whitespace-nowrap"> <div className="flex space-x-2"> <button onClick={() => onEdit(expense)} className={`inline-flex items-center justify-center px-3 py-1 border text-xs font-medium rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 h-[26px] w-[70px] ${ isXeroExpense(expense) ? "border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed" : "border-secondary text-secondary hover:bg-secondary/5 focus:ring-secondary/50" }`} disabled={ loading || isAddingNew || isEditing || isXeroExpense(expense) } aria-label="Edit expense" title={ isXeroExpense(expense) ? "Xero expenses cannot be edited directly. Use the sync feature instead." : "Edit expense" } > <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /> </svg> Edit </button> <button onClick={() => onDelete(expense.id)} className="inline-flex items-center justify-center px-3 py-1 border border-accent text-accent text-xs font-medium rounded shadow-sm hover:bg-accent/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent/50 h-[26px] w-[80px]" disabled={loading || isAddingNew || isEditing} aria-label="Delete expense" > {deletingId === expense.id ? ( <> <svg className="animate-spin h-3.5 w-3.5 mr-1 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> Delete </> ) : ( <> <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /> </svg> Delete </> )} </button> </div> </td> </tr> ); }; /** * Modern unified ExpenseListItem component with mobile/desktop support * Renders card for mobile, table row for desktop */ export const ExpenseListItem: React.FC<ExpenseListItemProps> = ({ expense, onEdit, onDelete, loading, isEditing, isAddingNew, deletingId, isMobile, }) => { // Background color for table rows const isDeleting = deletingId === expense.id; // Mobile card view if (isMobile) { return ( <Card variant="interactive" shadow="sm" className="border-l-4 border-l-gray-200 hover:border-l-primary-400 transition-colors duration-200" > <div className="flex justify-between items-center mb-3"> <div className="flex items-center"> <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate mr-2"> {expense.name} </h3> <div className="flex items-center space-x-1"> {isXeroExpense(expense) && <XeroBadge className="ml-1" />} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === true && ( <PredictedBadge className="ml-1" /> )} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === false && ( <AccruedBadge className="ml-1" quarterInfo={getCurrentQuarterInfo()} /> )} </div> </div> <span className="font-bold text-accent whitespace-nowrap"> {formatCurrency(expense.amount)} </span> </div> <div className="flex justify-between items-center mb-3"> <div className="flex items-center text-gray-600 dark:text-gray-400"> <svg className="w-4 h-4 mr-1 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" > <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" /> </svg> <span className="text-sm">{formatDate(expense.date)}</span> </div> </div> <div className="flex flex-wrap justify-between items-center"> <div className="flex items-center mb-2 sm:mb-0 space-x-2"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getTypeClass( expense.type )}`} > {getTypeLabel(expense.type)} </span> <span className={`inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium ${getFrequencyClass( expense.frequency )}`} > {getFrequencyLabel(expense.frequency)} </span> </div> <div className="flex space-x-2"> <button onClick={() => onEdit(expense)} className={`inline-flex items-center justify-center px-2 py-1 border text-xs font-medium rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 w-[26px] h-[26px] ${ isXeroExpense(expense) ? "border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed" : "border-secondary text-secondary hover:bg-secondary/5 focus:ring-secondary/50" }`} disabled={ loading || isAddingNew || isEditing || deletingId !== null || isXeroExpense(expense) } aria-label="Edit expense" title={ isXeroExpense(expense) ? "Xero expenses cannot be edited directly. Use the sync feature instead." : "Edit expense" } > <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /> </svg> </button> <button onClick={() => onDelete(expense.id)} className="inline-flex items-center justify-center px-2 py-1 border border-accent text-accent text-xs font-medium rounded shadow-sm hover:bg-accent/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent/50 w-[26px] h-[26px]" disabled={ loading || isAddingNew || isEditing || deletingId !== null } aria-label="Delete expense" > {deletingId === expense.id ? ( <svg className="animate-spin h-3 w-3 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> ) : ( <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /> </svg> )} </button> </div> </div> </Card> ); } // Desktop table row view return ( <tr className={`hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors ${ isDeleting ? "opacity-50" : "" }`} > <td className="px-4 py-2.5 text-gray-900 dark:text-gray-100"> <div className="flex items-center"> <span className="mr-2">{expense.name}</span> <div className="flex items-center space-x-1"> {isXeroExpense(expense) && <XeroBadge className="ml-1" />} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === true && ( <PredictedBadge className="ml-1" /> )} {isGSTExpense(expense) && expense.metadata?.usedProjectedAmount === false && ( <AccruedBadge className="ml-1" quarterInfo={getCurrentQuarterInfo()} /> )} </div> </div> </td> <td className="px-4 py-2.5"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded border text-xs font-medium ${getTypeClass( expense.type )}`} > {getTypeLabel(expense.type)} </span> </td> <td className="px-4 py-2.5 text-right font-bold text-accent"> {formatCurrency(expense.amount)} </td> <td className="px-4 py-2.5">{formatDate(expense.date)}</td> <td className="px-4 py-2.5"> <span className={`inline-flex items-center px-2.5 py-0.5 rounded border text-xs font-medium ${getFrequencyClass( expense.frequency )}`} > {getFrequencyLabel(expense.frequency)} </span> </td> <td className="px-4 py-2.5 text-center"> <div className="flex items-center justify-center space-x-2"> <button onClick={() => onEdit(expense)} className={`inline-flex items-center justify-center px-3 py-1 border text-xs font-medium rounded shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 ${ isXeroExpense(expense) ? "border-gray-300 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed" : "border-secondary text-secondary hover:bg-secondary/5 focus:ring-secondary/50" }`} disabled={ loading || isAddingNew || isEditing || isXeroExpense(expense) } aria-label="Edit expense" title={ isXeroExpense(expense) ? "Xero expenses cannot be edited directly. Use the sync feature instead." : "Edit expense" } > <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /> </svg> Edit </button> <button onClick={() => onDelete(expense.id)} className="inline-flex items-center justify-center px-3 py-1 border border-accent text-accent text-xs font-medium rounded shadow-sm hover:bg-accent/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent/50" disabled={loading || isAddingNew || isEditing} aria-label="Delete expense" > {deletingId === expense.id ? ( <> <svg className="animate-spin h-3.5 w-3.5 mr-1 text-accent" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> Deleting... </> ) : ( <> <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /> </svg> Delete </> )} </button> </div> </td> </tr> ); }; 