#!/usr/bin/env node
/**
 * Test the unified schema by creating a fresh database
 *
 * This script:
 * 1. Creates a test database
 * 2. Executes the unified schema
 * 3. Validates all tables were created
 * 4. Reports any errors
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Use test database path
const dbPath = path.join(__dirname, '..', 'data', 'test-unified-schema.db');

// Create data directory if it doesn't exist
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  console.log(`Creating data directory: ${dataDir}`);
  fs.mkdirSync(dataDir, { recursive: true });
}

// Delete existing test database
if (fs.existsSync(dbPath)) {
  console.log('Removing existing test database...');
  fs.unlinkSync(dbPath);
}

console.log(`Creating test database at: ${dbPath}`);

// Initialize the database
const db = new BetterSqlite3(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

console.log('\n=== Executing Unified Schema ===\n');

try {
  // Read the unified schema
  const schemaPath = path.join(__dirname, '..', 'migrations', '000_unified_schema.sql');
  if (!fs.existsSync(schemaPath)) {
    throw new Error('Unified schema file not found: ' + schemaPath);
  }
  
  const schema = fs.readFileSync(schemaPath, 'utf8');
  
  // Execute the schema
  console.log('Executing unified schema...');
  db.exec(schema);
  
  console.log('✅ Schema executed successfully!\n');
  
  // Get list of all tables created
  console.log('=== Tables Created ===');
  const tables = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
    ORDER BY name
  `).all();
  
  tables.forEach(table => {
    // Count columns for each table
    const columns = db.prepare(`PRAGMA table_info('${table.name}')`).all();
    console.log(`✓ ${table.name} (${columns.length} columns)`);
  });
  
  console.log(`\nTotal tables: ${tables.length}`);
  
  // Check for views
  console.log('\n=== Views Created ===');
  const views = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='view'
    ORDER BY name
  `).all();
  
  if (views.length > 0) {
    views.forEach(view => {
      console.log(`✓ ${view.name}`);
    });
    console.log(`\nTotal views: ${views.length}`);
  } else {
    console.log('No views found');
  }
  
  // Check for indexes
  console.log('\n=== Index Summary ===');
  const indexes = db.prepare(`
    SELECT COUNT(*) as count FROM sqlite_master
    WHERE type='index' AND name NOT LIKE 'sqlite_%'
  `).get();
  console.log(`Total indexes: ${indexes.count}`);
  
  // Check for triggers
  console.log('\n=== Trigger Summary ===');
  const triggers = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='trigger'
    ORDER BY name
  `).all();
  
  if (triggers.length > 0) {
    triggers.forEach(trigger => {
      console.log(`✓ ${trigger.name}`);
    });
    console.log(`\nTotal triggers: ${triggers.length}`);
  } else {
    console.log('No triggers found');
  }
  
  // Validate key tables have expected structure
  console.log('\n=== Key Table Validation ===');
  
  // Check company table
  const companyColumns = db.prepare(`PRAGMA table_info('company')`).all();
  const hasDeletedAt = companyColumns.some(col => col.name === 'deleted_at');
  const hasAuditFields = companyColumns.some(col => col.name === 'created_at') &&
                        companyColumns.some(col => col.name === 'updated_at');
  
  console.log(`Company table:`);
  console.log(`  - Has deleted_at: ${hasDeletedAt ? '✅' : '❌'}`);
  console.log(`  - Has audit fields: ${hasAuditFields ? '✅' : '❌'}`);
  
  // Check foreign key constraints on estimate table
  const estimateFKs = db.prepare(`PRAGMA foreign_key_list('estimate')`).all();
  const companyFK = estimateFKs.find(fk => fk.table === 'company');
  
  console.log(`\nEstimate table foreign keys:`);
  console.log(`  - Company FK: ${companyFK ? companyFK.on_delete : 'NOT FOUND'}`);
  
  // Test inserting sample data
  console.log('\n=== Testing Data Insertion ===');
  
  try {
    // Insert a test company
    db.prepare(`
      INSERT INTO company (id, name, created_by)
      VALUES ('test-company-1', 'Test Company', 'system')
    `).run();
    console.log('✅ Successfully inserted test company');
    
    // Insert a test contact
    db.prepare(`
      INSERT INTO contact (id, first_name, last_name, email)
      VALUES ('test-contact-1', 'John', 'Doe', '<EMAIL>')
    `).run();
    console.log('✅ Successfully inserted test contact');
    
    // Test soft delete
    db.prepare(`
      UPDATE company SET deleted_at = datetime('now') WHERE id = 'test-company-1'
    `).run();
    console.log('✅ Successfully soft-deleted test company');
    
    // Verify soft delete worked
    const activeCompanies = db.prepare(`
      SELECT COUNT(*) as count FROM company WHERE deleted_at IS NULL
    `).get();
    const deletedCompanies = db.prepare(`
      SELECT COUNT(*) as count FROM company WHERE deleted_at IS NOT NULL
    `).get();
    
    console.log(`\nSoft delete verification:`);
    console.log(`  - Active companies: ${activeCompanies.count}`);
    console.log(`  - Deleted companies: ${deletedCompanies.count}`);
    
  } catch (error) {
    console.error('❌ Error testing data insertion:', error.message);
  }
  
  console.log('\n✅ Unified schema test completed successfully!');
  
} catch (error) {
  console.error('\n❌ Error executing unified schema:', error.message);
  
  // Try to provide more detailed error info
  if (error.message.includes('syntax error')) {
    console.error('\nThis appears to be a SQL syntax error.');
    console.error('Check the unified schema file for SQL syntax issues.');
  } else if (error.message.includes('foreign key')) {
    console.error('\nThis appears to be a foreign key constraint error.');
    console.error('Check that tables are created in the correct order.');
  }
  
  process.exit(1);
} finally {
  // Close the database
  db.close();
  
  // Clean up test database
  if (fs.existsSync(dbPath)) {
    console.log('\nCleaning up test database...');
    fs.unlinkSync(dbPath);
  }
}