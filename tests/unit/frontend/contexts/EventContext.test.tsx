import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { EventContext, EventProvider } from '../../../../src/frontend/contexts/EventContext';

describe('EventContext', () => {
  it('should provide event context to children', () => {
    const TestComponent = () => {
      const context = React.useContext(EventContext);
      return (
        <div>
          <span>Has Context: {context ? 'Yes' : 'No'}</span>
        </div>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    expect(screen.getByText('Has Context: Yes')).toBeInTheDocument();
  });

  it('should emit and subscribe to events', async () => {
    const eventHandler = jest.fn();
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        const unsubscribe = subscribe('test-event', eventHandler);
        return unsubscribe;
      }, []);

      return (
        <button onClick={() => emit('test-event', { data: 'test' })}>
          Emit Event
        </button>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit Event').click();
    });

    await waitFor(() => {
      expect(eventHandler).toHaveBeenCalledWith({ data: 'test' });
    });
  });

  it('should handle multiple subscribers', async () => {
    const handler1 = jest.fn();
    const handler2 = jest.fn();
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        const unsub1 = subscribe('multi-event', handler1);
        const unsub2 = subscribe('multi-event', handler2);
        return () => {
          unsub1();
          unsub2();
        };
      }, []);

      return (
        <button onClick={() => emit('multi-event', { value: 123 })}>
          Emit to Multiple
        </button>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit to Multiple').click();
    });

    await waitFor(() => {
      expect(handler1).toHaveBeenCalledWith({ value: 123 });
      expect(handler2).toHaveBeenCalledWith({ value: 123 });
    });
  });

  it('should unsubscribe from events', async () => {
    const handler = jest.fn();
    let unsubscribe: (() => void) | null = null;
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        unsubscribe = subscribe('unsub-event', handler);
        return () => {
          if (unsubscribe) unsubscribe();
        };
      }, []);

      return (
        <div>
          <button onClick={() => emit('unsub-event', 'data')}>Emit</button>
          <button onClick={() => unsubscribe && unsubscribe()}>Unsubscribe</button>
        </div>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    // First emission should call handler
    act(() => {
      screen.getByText('Emit').click();
    });

    await waitFor(() => {
      expect(handler).toHaveBeenCalledTimes(1);
    });

    // Unsubscribe
    act(() => {
      screen.getByText('Unsubscribe').click();
    });

    // Second emission should not call handler
    act(() => {
      screen.getByText('Emit').click();
    });

    await waitFor(() => {
      expect(handler).toHaveBeenCalledTimes(1); // Still only 1 call
    });
  });

  it('should handle different event types', async () => {
    const userHandler = jest.fn();
    const dataHandler = jest.fn();
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        const unsub1 = subscribe('user-event', userHandler);
        const unsub2 = subscribe('data-event', dataHandler);
        return () => {
          unsub1();
          unsub2();
        };
      }, []);

      return (
        <div>
          <button onClick={() => emit('user-event', { user: 'John' })}>
            Emit User Event
          </button>
          <button onClick={() => emit('data-event', { data: [1, 2, 3] })}>
            Emit Data Event
          </button>
        </div>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit User Event').click();
    });

    await waitFor(() => {
      expect(userHandler).toHaveBeenCalledWith({ user: 'John' });
      expect(dataHandler).not.toHaveBeenCalled();
    });

    act(() => {
      screen.getByText('Emit Data Event').click();
    });

    await waitFor(() => {
      expect(dataHandler).toHaveBeenCalledWith({ data: [1, 2, 3] });
    });
  });

  it('should emit once for single events', async () => {
    const handler = jest.fn();
    
    const TestComponent = () => {
      const { emitOnce, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        const unsubscribe = subscribe('once-event', handler);
        return unsubscribe;
      }, []);

      return (
        <button onClick={() => {
          emitOnce('once-event', 'first');
          emitOnce('once-event', 'second');
        }}>
          Emit Once
        </button>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit Once').click();
    });

    await waitFor(() => {
      expect(handler).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalledWith('first');
    });
  });

  it('should clear all listeners', async () => {
    const handler1 = jest.fn();
    const handler2 = jest.fn();
    
    const TestComponent = () => {
      const { emit, subscribe, clearAll } = React.useContext(EventContext);
      
      React.useEffect(() => {
        subscribe('event1', handler1);
        subscribe('event2', handler2);
      }, []);

      return (
        <div>
          <button onClick={clearAll}>Clear All</button>
          <button onClick={() => {
            emit('event1', 'data1');
            emit('event2', 'data2');
          }}>
            Emit Both
          </button>
        </div>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Clear All').click();
    });

    act(() => {
      screen.getByText('Emit Both').click();
    });

    await waitFor(() => {
      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).not.toHaveBeenCalled();
    });
  });

  it('should handle errors in event handlers', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    const goodHandler = jest.fn();
    const badHandler = jest.fn(() => {
      throw new Error('Handler error');
    });
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        const unsub1 = subscribe('error-event', badHandler);
        const unsub2 = subscribe('error-event', goodHandler);
        return () => {
          unsub1();
          unsub2();
        };
      }, []);

      return (
        <button onClick={() => emit('error-event', 'data')}>
          Emit with Error
        </button>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit with Error').click();
    });

    await waitFor(() => {
      expect(badHandler).toHaveBeenCalled();
      expect(goodHandler).toHaveBeenCalled(); // Should still call other handlers
      expect(consoleSpy).toHaveBeenCalledWith(
        'Error in event handler:',
        expect.any(Error)
      );
    });

    consoleSpy.mockRestore();
  });

  it('should support wildcard event subscriptions', async () => {
    const wildcardHandler = jest.fn();
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      
      React.useEffect(() => {
        const unsubscribe = subscribe('*', wildcardHandler);
        return unsubscribe;
      }, []);

      return (
        <div>
          <button onClick={() => emit('any-event', 'data1')}>Emit Any</button>
          <button onClick={() => emit('other-event', 'data2')}>Emit Other</button>
        </div>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit Any').click();
    });

    await waitFor(() => {
      expect(wildcardHandler).toHaveBeenCalledWith('data1', 'any-event');
    });

    act(() => {
      screen.getByText('Emit Other').click();
    });

    await waitFor(() => {
      expect(wildcardHandler).toHaveBeenCalledWith('data2', 'other-event');
    });
  });

  it('should maintain event subscriptions across re-renders', () => {
    const handler = jest.fn();
    
    const TestComponent = () => {
      const { emit, subscribe } = React.useContext(EventContext);
      const [count, setCount] = React.useState(0);
      
      React.useEffect(() => {
        const unsubscribe = subscribe('stable-event', handler);
        return unsubscribe;
      }, []); // Empty deps, subscribe only once

      return (
        <div>
          <span>Count: {count}</span>
          <button onClick={() => setCount(count + 1)}>Increment</button>
          <button onClick={() => emit('stable-event', count)}>Emit Count</button>
        </div>
      );
    };

    render(
      <EventProvider>
        <TestComponent />
      </EventProvider>
    );

    act(() => {
      screen.getByText('Emit Count').click();
    });

    expect(handler).toHaveBeenCalledWith(0);

    act(() => {
      screen.getByText('Increment').click();
    });

    act(() => {
      screen.getByText('Emit Count').click();
    });

    expect(handler).toHaveBeenCalledWith(1);
    expect(handler).toHaveBeenCalledTimes(2);
  });
});