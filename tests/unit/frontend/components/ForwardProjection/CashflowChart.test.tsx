/**
 * CashflowChart Component Test
 * 
 * Tests for the critical cashflow visualization component.
 * This component is central to the financial forecasting feature.
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CashflowChart } from '@/frontend/components/ForwardProjection/CashflowChart';
import { Transaction } from '@/types/financial';

// Mock Recharts to avoid rendering issues in tests
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: any) => <div>{children}</div>,
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: ({ dataKey }: any) => <div data-testid={`line-${dataKey}`} />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  Dot: () => <div data-testid="dot" />
}));

describe('CashflowChart', () => {
  const mockTransactions: Transaction[] = [
    {
      id: '1',
      type: 'invoice',
      description: 'Client Invoice',
      date: new Date('2025-01-15'),
      amount: 5000,
      source: 'harvest'
    },
    {
      id: '2',
      type: 'expense',
      description: 'Office Rent',
      date: new Date('2025-01-20'),
      amount: 2000,
      source: 'xero'
    },
    {
      id: '3',
      type: 'bill',
      description: 'Software Subscription',
      date: new Date('2025-02-01'),
      amount: 500,
      source: 'xero'
    }
  ];
  
  const defaultProps = {
    transactions: mockTransactions,
    startBalance: 10000,
    projectionMonths: 3,
    selectedScenario: 'expected' as const,
    onScenarioChange: jest.fn()
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders the chart with all components', () => {
    render(<CashflowChart {...defaultProps} />);
    
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByTestId('x-axis')).toBeInTheDocument();
    expect(screen.getByTestId('y-axis')).toBeInTheDocument();
    expect(screen.getByTestId('grid')).toBeInTheDocument();
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    expect(screen.getByTestId('legend')).toBeInTheDocument();
  });
  
  it('renders scenario lines based on selected scenario', () => {
    render(<CashflowChart {...defaultProps} />);
    
    // Should show expected scenario line
    expect(screen.getByTestId('line-expected')).toBeInTheDocument();
  });
  
  it('renders all scenario lines when "all" is selected', () => {
    render(<CashflowChart {...defaultProps} selectedScenario="all" />);
    
    expect(screen.getByTestId('line-worst')).toBeInTheDocument();
    expect(screen.getByTestId('line-expected')).toBeInTheDocument();
    expect(screen.getByTestId('line-best')).toBeInTheDocument();
  });
  
  it('calls onScenarioChange when scenario is changed', () => {
    const onScenarioChange = jest.fn();
    
    render(
      <CashflowChart 
        {...defaultProps} 
        onScenarioChange={onScenarioChange}
      />
    );
    
    // Assuming there's a scenario selector in the component
    // This would need to be implemented in the actual component
    const scenarioSelector = screen.getByRole('combobox', { name: /scenario/i });
    fireEvent.change(scenarioSelector, { target: { value: 'best' } });
    
    expect(onScenarioChange).toHaveBeenCalledWith('best');
  });
  
  it('handles empty transactions gracefully', () => {
    render(<CashflowChart {...defaultProps} transactions={[]} />);
    
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    // Should still render but with just the starting balance
  });
  
  it('calculates balance progression correctly', () => {
    // This test would verify the internal calculations
    // In a real implementation, you might expose these calculations
    // or test them through visual regression testing
    render(<CashflowChart {...defaultProps} />);
    
    // The chart should be rendered without errors
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });
  
  it('formats currency values correctly', () => {
    // This would test the tooltip formatting
    // In the actual implementation, you'd test the formatter function
    render(<CashflowChart {...defaultProps} />);
    
    // Chart should render with proper formatting
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });
  
  it('handles negative balances', () => {
    const transactionsWithLargeExpense: Transaction[] = [
      {
        id: '1',
        type: 'expense',
        description: 'Large Payment',
        date: new Date('2025-01-15'),
        amount: 15000, // More than starting balance
        source: 'xero'
      }
    ];
    
    render(
      <CashflowChart 
        {...defaultProps} 
        transactions={transactionsWithLargeExpense}
        startBalance={10000}
      />
    );
    
    // Should render without crashing
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });
  
  it('groups transactions by day correctly', () => {
    const sameDay: Transaction[] = [
      {
        id: '1',
        type: 'invoice',
        description: 'Invoice 1',
        date: new Date('2025-01-15'),
        amount: 1000,
        source: 'harvest'
      },
      {
        id: '2',
        type: 'invoice',
        description: 'Invoice 2',
        date: new Date('2025-01-15'),
        amount: 2000,
        source: 'harvest'
      }
    ];
    
    render(
      <CashflowChart 
        {...defaultProps} 
        transactions={sameDay}
      />
    );
    
    // Should combine same-day transactions
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });
  
  it('respects projection months parameter', () => {
    render(
      <CashflowChart 
        {...defaultProps} 
        projectionMonths={6}
      />
    );
    
    // Chart should extend to 6 months
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });
  
  it('applies transaction type colors correctly', () => {
    // This would test that income/expense transactions
    // are displayed with the correct colors
    render(<CashflowChart {...defaultProps} />);
    
    // In the actual implementation, you'd verify the color mapping
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
  });
});