/**
 * Comprehensive Schema Validation Tests
 * 
 * These tests ensure that the database schema matches the unified data model documentation
 * and migration files. They validate table structures, relationships, indexes, and constraints.
 * 
 * This is a complete rewrite based on the current migration system and repository patterns.
 */

import { describe, expect, test, beforeAll, afterAll } from '@jest/globals';
import BetterSqlite3 from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// Test database setup
let testDb: BetterSqlite3.Database;
const testDbPath = path.join(__dirname, '../../../data/test_comprehensive_schema.db');

beforeAll(async () => {
  // Remove existing test database
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  // Create test database with the main migration file
  testDb = new BetterSqlite3(testDbPath);
  
  // Enable foreign keys
  testDb.pragma('foreign_keys = ON');
  
  // Run the main migration file that contains the complete schema
  const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
  if (fs.existsSync(migrationPath)) {
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    testDb.exec(migrationSQL);
  } else {
    throw new Error(`Migration file not found: ${migrationPath}`);
  }
});

afterAll(() => {
  testDb?.close();
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }
});

/**
 * Get column information for a table
 */
function getTableColumns(tableName: string): Array<{name: string, type: string, notnull: number, pk: number}> {
  const columns = testDb.prepare(`PRAGMA table_info(${tableName})`).all() as Array<{
    cid: number;
    name: string;
    type: string;
    notnull: number;
    dflt_value: any;
    pk: number;
  }>;
  
  return columns.map(col => ({ 
    name: col.name, 
    type: col.type, 
    notnull: col.notnull,
    pk: col.pk 
  }));
}

/**
 * Check if table exists
 */
function tableExists(tableName: string): boolean {
  const result = testDb.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name=?
  `).get(tableName);
  return !!result;
}

/**
 * Check if index exists
 */
function indexExists(tableName: string, indexName: string): boolean {
  const result = testDb.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='index' AND name=? AND tbl_name=?
  `).get(indexName, tableName);
  return !!result;
}

/**
 * Get foreign keys for a table
 */
function getForeignKeys(tableName: string): Array<{from: string, table: string, to: string, on_delete: string}> {
  return testDb.prepare(`PRAGMA foreign_key_list(${tableName})`).all() as Array<{
    from: string;
    table: string;
    to: string;
    on_delete: string;
  }>;
}

describe('Comprehensive Database Schema Validation', () => {
  
  describe('Core Tables Exist', () => {
    const requiredTables = [
      // Core CRM tables
      'company',
      'contact', 
      'deal',
      
      // Relationship tables
      'contact_company',
      'contact_role',
      'deal_estimate',
      'company_relationship',
      
      // Estimates and allocations
      'estimate',
      'estimate_allocation',
      'estimate_time_allocation',
      
      // Financial tables
      'expense',
      'cashflow_snapshot',
      
      // Notes and audit
      'note',
      'activity_feed',
      'field_ownership',
      'change_log',
      
      // Integration tables
      'harvest_invoice_cache',
      'settings',
      'hubspot_settings',
      
      // Migration tracking
      'migrations'
    ];

    test.each(requiredTables)('table %s should exist', (tableName) => {
      expect(tableExists(tableName)).toBe(true);
    });
  });

  describe('Company Table Schema', () => {
    test('should have all required columns', () => {
      const columns = getTableColumns('company').map(col => col.name);
      
      const requiredColumns = [
        'id',
        'name',
        'industry',
        'size',
        'website',
        'address',
        'description',
        'hubspot_id',
        'harvest_id',
        'source',
        'radar_state',
        'priority',
        'current_spend',
        'potential_spend',
        'last_interaction_date',
        'notes',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'contacts'
      ];

      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
    
    test('should have correct column types and constraints', () => {
      const columns = getTableColumns('company');
      const idCol = columns.find(col => col.name === 'id');
      const nameCol = columns.find(col => col.name === 'name');
      const hubspotIdCol = columns.find(col => col.name === 'hubspot_id');
      const harvestIdCol = columns.find(col => col.name === 'harvest_id');
      
      expect(idCol?.type).toBe('TEXT');
      expect(idCol?.pk).toBe(1); // Primary key
      expect(nameCol?.type).toBe('TEXT');
      expect(nameCol?.notnull).toBe(1); // NOT NULL
      expect(hubspotIdCol?.type).toBe('TEXT');
      expect(harvestIdCol?.type).toBe('TEXT');
    });

    test('should have correct indexes', () => {
      const expectedIndexes = [
        'idx_company_hubspot_id',
        'idx_company_harvest_id',
        'idx_company_source',
        'idx_company_radar_state',
        'idx_company_priority',
        'idx_company_created_at'
      ];

      for (const indexName of expectedIndexes) {
        expect(indexExists('company', indexName)).toBe(true);
      }
    });
  });

  describe('Contact Table Schema', () => {
    test('should have all required columns', () => {
      const columns = getTableColumns('contact').map(col => col.name);
      
      const requiredColumns = [
        'id',
        'first_name', 
        'last_name',
        'email',
        'phone',
        'job_title',
        'notes',
        'hubspot_id',
        'harvest_user_id',
        'source',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at'
      ];

      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
    
    test('should have correct column types and constraints', () => {
      const columns = getTableColumns('contact');
      const idCol = columns.find(col => col.name === 'id');
      const emailCol = columns.find(col => col.name === 'email');
      const firstNameCol = columns.find(col => col.name === 'first_name');
      const lastNameCol = columns.find(col => col.name === 'last_name');
      
      expect(idCol?.type).toBe('TEXT');
      expect(idCol?.pk).toBe(1);
      expect(emailCol?.type).toBe('TEXT');
      expect(firstNameCol?.type).toBe('TEXT');
      expect(firstNameCol?.notnull).toBe(1);
      expect(lastNameCol?.type).toBe('TEXT');
      expect(lastNameCol?.notnull).toBe(1);
    });

    test('should have correct indexes', () => {
      const expectedIndexes = [
        'idx_contact_hubspot_id',
        'idx_contact_harvest_user_id',
        'idx_contact_email',
        'idx_contact_source'
      ];

      for (const indexName of expectedIndexes) {
        expect(indexExists('contact', indexName)).toBe(true);
      }
    });
  });

  describe('Deal Table Schema', () => {
    test('should have all required columns', () => {
      const columns = getTableColumns('deal').map(col => col.name);
      
      const requiredColumns = [
        'id',
        'name',
        'stage',
        'value',
        'currency',
        'probability',
        'expected_close_date',
        'start_date',
        'end_date',
        'invoice_frequency',
        'payment_terms',
        'company_id',
        'hubspot_id',
        'harvest_project_id',
        'description',
        'source',
        'priority',
        'owner',
        'custom_fields',
        'include_in_projections',
        'status',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at'
      ];

      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
    
    test('should have correct foreign key to company', () => {
      const foreignKeys = getForeignKeys('deal');
      const companyFK = foreignKeys.find(fk => fk.from === 'company_id' && fk.table === 'company');
      
      expect(companyFK).toBeDefined();
      expect(companyFK!.to).toBe('id');
      expect(companyFK!.on_delete).toBe('CASCADE');
    });

    test('should have correct indexes', () => {
      const expectedIndexes = [
        'idx_deal_hubspot_id',
        'idx_deal_company_id',
        'idx_deal_stage',
        'idx_deal_status',
        'idx_deal_expected_close_date',
        'idx_deal_harvest_project_id',
        'idx_deal_include_in_projections',
        'idx_deal_priority',
        'idx_deal_owner'
      ];

      for (const indexName of expectedIndexes) {
        expect(indexExists('deal', indexName)).toBe(true);
      }
    });
  });

  describe('Relationship Tables', () => {
    test('contact_company junction table should be properly structured', () => {
      expect(tableExists('contact_company')).toBe(true);
      
      const columns = getTableColumns('contact_company').map(col => col.name);
      const requiredColumns = ['id', 'contact_id', 'company_id', 'role', 'is_primary', 'created_at'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check foreign keys
      const foreignKeys = getForeignKeys('contact_company');
      expect(foreignKeys.length).toBeGreaterThanOrEqual(2);
      
      const contactFK = foreignKeys.find(fk => fk.from === 'contact_id');
      const companyFK = foreignKeys.find(fk => fk.from === 'company_id');
      
      expect(contactFK?.table).toBe('contact');
      expect(contactFK?.on_delete).toBe('CASCADE');
      expect(companyFK?.table).toBe('company');
      expect(companyFK?.on_delete).toBe('CASCADE');
    });
    
    test('contact_role junction table should be properly structured', () => {
      expect(tableExists('contact_role')).toBe(true);
      
      const columns = getTableColumns('contact_role').map(col => col.name);
      const requiredColumns = ['id', 'contact_id', 'deal_id', 'role', 'created_at'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check foreign keys
      const foreignKeys = getForeignKeys('contact_role');
      const contactFK = foreignKeys.find(fk => fk.from === 'contact_id');
      const dealFK = foreignKeys.find(fk => fk.from === 'deal_id');
      
      expect(contactFK?.table).toBe('contact');
      expect(contactFK?.on_delete).toBe('CASCADE');
      expect(dealFK?.table).toBe('deal');
      expect(dealFK?.on_delete).toBe('CASCADE');
    });
    
    test('contact_role junction table should be properly structured', () => {
      expect(tableExists('contact_role')).toBe(true);
      
      const columns = getTableColumns('contact_role').map(col => col.name);
      const requiredColumns = ['id', 'contact_id', 'deal_id', 'role', 'created_at', 'created_by'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check foreign keys
      const foreignKeys = getForeignKeys('contact_role');
      const dealFK = foreignKeys.find(fk => fk.from === 'deal_id');
      const contactFK = foreignKeys.find(fk => fk.from === 'contact_id');
      
      expect(dealFK?.table).toBe('deal');
      expect(dealFK?.on_delete).toBe('CASCADE');
      expect(contactFK?.table).toBe('contact');
      expect(contactFK?.on_delete).toBe('CASCADE');
    });
    
    test('deal_estimate junction table should be properly structured', () => {
      expect(tableExists('deal_estimate')).toBe(true);
      
      const columns = getTableColumns('deal_estimate').map(col => col.name);
      const requiredColumns = ['deal_id', 'estimate_id', 'estimate_type', 'harvest_estimate_id', 'linked_at', 'linked_by', 'created_at'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check composite primary key
      const primaryKeyColumns = getTableColumns('deal_estimate').filter(col => col.pk > 0);
      expect(primaryKeyColumns.length).toBe(3); // deal_id, estimate_id, estimate_type
      
      // Check foreign keys
      const foreignKeys = getForeignKeys('deal_estimate');
      const dealFK = foreignKeys.find(fk => fk.from === 'deal_id');
      const estimateFK = foreignKeys.find(fk => fk.from === 'estimate_id');
      
      expect(dealFK?.table).toBe('deal');
      expect(dealFK?.on_delete).toBe('CASCADE');
      expect(estimateFK?.table).toBe('estimate');
      expect(estimateFK?.on_delete).toBe('CASCADE');
    });
    
    // Note: company_relationship table has been removed from the schema
  });

  describe('Estimate Tables', () => {
    test('estimate table should have all required columns', () => {
      expect(tableExists('estimate')).toBe(true);
      
      const columns = getTableColumns('estimate').map(col => col.name);
      const requiredColumns = [
        'id', 'company_id', 'client_name', 'project_name', 'project_code',
        'estimate_number', 'date_sent', 'valid_until', 'total_consultancy',
        'total_expenses', 'total', 'tax', 'grand_total', 'payment_schedule',
        'payment_terms', 'payment_percentage', 'staff_allocations', 'status',
        'version', 'notes', 'harvest_estimate_id', 'created_at', 'updated_at',
        'created_by', 'updated_by', 'deleted_at'
      ];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check foreign key to company
      const foreignKeys = getForeignKeys('estimate');
      const companyFK = foreignKeys.find(fk => fk.from === 'company_id');
      expect(companyFK?.table).toBe('company');
      expect(companyFK?.on_delete).toBe('CASCADE');
    });
    
    test('estimate_allocation table should have all required columns', () => {
      expect(tableExists('estimate_allocation')).toBe(true);
      
      const columns = getTableColumns('estimate_allocation').map(col => col.name);
      const requiredColumns = [
        'id', 'estimate_id', 'staff_name', 'role', 'rate', 'hours', 'total',
        'staff_id', 'proposed_daily_rate', 'target_daily_rate', 'created_at', 'updated_at'
      ];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check foreign key to estimate
      const foreignKeys = getForeignKeys('estimate_allocation');
      const estimateFK = foreignKeys.find(fk => fk.from === 'estimate_id');
      expect(estimateFK?.table).toBe('estimate');
      expect(estimateFK?.on_delete).toBe('CASCADE');
    });
    
    test('estimate_time_allocation table should have all required columns', () => {
      expect(tableExists('estimate_time_allocation')).toBe(true);
      
      const columns = getTableColumns('estimate_time_allocation').map(col => col.name);
      const requiredColumns = [
        'id', 'allocation_id', 'week_number', 'hours', 'week_starting',
        'days_allocated', 'created_at', 'updated_at'
      ];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check foreign key to estimate_allocation
      const foreignKeys = getForeignKeys('estimate_time_allocation');
      const allocationFK = foreignKeys.find(fk => fk.from === 'allocation_id');
      expect(allocationFK?.table).toBe('estimate_allocation');
      expect(allocationFK?.on_delete).toBe('CASCADE');
    });
  });

  describe('Financial Tables', () => {
    test('expense table should have all required columns', () => {
      expect(tableExists('expense')).toBe(true);
      
      const columns = getTableColumns('expense').map(col => col.name);
      const requiredColumns = [
        'id', 'name', 'amount', 'frequency', 'type', 'start_date', 'end_date',
        'created_at', 'updated_at', 'deleted_at'
      ];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
    
    test('cashflow_snapshot table should have all required columns', () => {
      expect(tableExists('cashflow_snapshot')).toBe(true);
      
      const columns = getTableColumns('cashflow_snapshot').map(col => col.name);
      const requiredColumns = [
        'id', 'date', 'tenant_id', 'days_ahead', 'snapshot_data',
        'created_at', 'created_by'
      ];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
  });

  describe('Activity Feed and Audit Tables', () => {
    test('activity_feed table should have modern structure', () => {
      expect(tableExists('activity_feed')).toBe(true);
      
      const columns = getTableColumns('activity_feed').map(col => col.name);
      const requiredColumns = [
        'id', 'type', 'subject', 'description', 'status', 'entity_type',
        'entity_id', 'due_date', 'completed_date', 'company_id', 'contact_id',
        'deal_id', 'metadata', 'is_read', 'importance', 'created_by',
        'source', 'created_at', 'updated_at'
      ];

      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check optional foreign keys
      const foreignKeys = getForeignKeys('activity_feed');
      const companyFK = foreignKeys.find(fk => fk.from === 'company_id');
      const contactFK = foreignKeys.find(fk => fk.from === 'contact_id');
      const dealFK = foreignKeys.find(fk => fk.from === 'deal_id');
      
      if (companyFK) {
        expect(companyFK.table).toBe('company');
        expect(companyFK.on_delete).toBe('CASCADE');
      }
      if (contactFK) {
        expect(contactFK.table).toBe('contact');
        expect(contactFK.on_delete).toBe('CASCADE');
      }
      if (dealFK) {
        expect(dealFK.table).toBe('deal');
        expect(dealFK.on_delete).toBe('CASCADE');
      }
    });

    test('note table should have polymorphic structure', () => {
      expect(tableExists('note')).toBe(true);
      
      const columns = getTableColumns('note').map(col => col.name);
      const requiredColumns = [
        'id', 'content', 'entity_type', 'entity_id', 'author',
        'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at'
      ];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });

    test('field_ownership table should have correct structure', () => {
      expect(tableExists('field_ownership')).toBe(true);
      
      const columns = getTableColumns('field_ownership').map(col => col.name);
      const requiredColumns = ['id', 'entity_type', 'entity_id', 'field_name', 'owner', 'set_at', 'set_by'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
    
    test('change_log table should have correct structure', () => {
      expect(tableExists('change_log')).toBe(true);
      
      const columns = getTableColumns('change_log').map(col => col.name);
      const requiredColumns = ['id', 'entity_type', 'entity_id', 'field_name', 'old_value', 'new_value', 'change_source', 'changed_at', 'changed_by'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
  });

  describe('Integration Tables', () => {
    test('harvest_invoice_cache table should have correct structure', () => {
      expect(tableExists('harvest_invoice_cache')).toBe(true);
      
      const columns = getTableColumns('harvest_invoice_cache').map(col => col.name);
      const requiredColumns = ['harvest_client_id', 'total_invoiced', 'invoice_count', 'last_updated', 'created_at'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
      
      // Check primary key
      const primaryKey = getTableColumns('harvest_invoice_cache').find(col => col.pk === 1);
      expect(primaryKey?.name).toBe('harvest_client_id');
    });
    
    test('hubspot_settings table should have correct structure', () => {
      expect(tableExists('hubspot_settings')).toBe(true);
      
      const columns = getTableColumns('hubspot_settings').map(col => col.name);
      const requiredColumns = ['id', 'key', 'value', 'created_at', 'updated_at'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });

    test('settings table should exist', () => {
      expect(tableExists('settings')).toBe(true);
      
      const columns = getTableColumns('settings').map(col => col.name);
      const requiredColumns = ['key', 'value', 'created_at', 'updated_at'];
      
      for (const column of requiredColumns) {
        expect(columns).toContain(column);
      }
    });
  });

  describe('Indexes Validation', () => {
    test('critical indexes should exist', () => {
      const indexes = testDb.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      `).all() as Array<{name: string}>;
      
      const indexNames = indexes.map(idx => idx.name);
      
      const criticalIndexes = [
        // Company indexes
        'idx_company_hubspot_id',
        'idx_company_harvest_id',
        'idx_company_source',
        'idx_company_radar_state',
        'idx_company_priority',
        
        // Contact indexes
        'idx_contact_hubspot_id',
        'idx_contact_harvest_user_id',
        'idx_contact_email',
        'idx_contact_source',
        
        // Deal indexes
        'idx_deal_hubspot_id',
        'idx_deal_company_id',
        'idx_deal_stage',
        'idx_deal_status',
        'idx_deal_expected_close_date',
        'idx_deal_harvest_project_id',
        'idx_deal_include_in_projections',
        
        // Activity feed indexes
        'idx_activity_feed_entity',
        'idx_activity_feed_created_at',
        'idx_activity_feed_type',
        'idx_activity_feed_source',
        'idx_activity_feed_created_by',
        'idx_activity_feed_unread',
        'idx_activity_feed_importance',
        'idx_activity_feed_status',
        
        // Relationship table indexes
        'idx_contact_company_contact_id',
        'idx_contact_company_company_id',
        'idx_contact_company_is_primary',
        'idx_contact_role_contact_id',
        'idx_contact_role_deal_id',
        'idx_deal_estimate_deal_id',
        'idx_deal_estimate_estimate_id',
        'idx_deal_estimate_type',
        
        // Estimate indexes
        'idx_estimate_company_id',
        'idx_estimate_harvest_id',
        'idx_estimate_status',
        'idx_estimate_created_at',
        'idx_estimate_allocation_estimate_id',
        'idx_estimate_allocation_staff_id',
        'idx_estimate_time_allocation_allocation_id',
        'idx_estimate_time_allocation_week_starting'
      ];

      for (const indexName of criticalIndexes) {
        expect(indexNames).toContain(indexName);
      }
    });
  });

  describe('Query Validation', () => {
    test('common repository queries should be valid', () => {
      // Test common queries that repositories use
      const testQueries = [
        'SELECT id, first_name, last_name, email, job_title FROM contact WHERE id = ?',
        'SELECT id, name, hubspot_id, harvest_id FROM company WHERE id = ?',
        'SELECT id, name, stage, value, expected_close_date FROM deal WHERE id = ?',
        'INSERT INTO activity_feed (id, type, subject, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)',
        'SELECT key, value FROM hubspot_settings WHERE key = ?',
        'SELECT deal_id, estimate_id, estimate_type FROM deal_estimate WHERE deal_id = ?',
        'SELECT contact_id, company_id, role, is_primary FROM contact_company WHERE contact_id = ?',
        'SELECT parent_company_id, child_company_id, relationship_type FROM company_relationship WHERE parent_company_id = ?'
      ];

      for (const query of testQueries) {
        expect(() => {
          testDb.prepare(query);
        }).not.toThrow();
      }
    });
  });

  describe('Foreign Key Constraints', () => {
    test('foreign key constraints should be properly defined', () => {
      // Test that foreign key constraints exist in key tables
      const tablesWithForeignKeys = [
        'deal',
        'contact_company', 
        'contact_role',
        'deal_estimate',
        'company_relationship',
        'estimate',
        'estimate_allocation',
        'estimate_time_allocation',
        'activity_feed'
      ];
      
      for (const tableName of tablesWithForeignKeys) {
        const foreignKeys = getForeignKeys(tableName);
        expect(foreignKeys.length).toBeGreaterThan(0);
      }
    });
    
    test('deal table foreign key to company should have CASCADE delete', () => {
      const foreignKeys = getForeignKeys('deal');
      const companyFK = foreignKeys.find(fk => fk.from === 'company_id' && fk.table === 'company');
      
      expect(companyFK).toBeDefined();
      expect(companyFK!.on_delete).toBe('CASCADE');
    });
    
    test('junction tables should have CASCADE delete constraints', () => {
      const junctionTables = [
        'contact_company',
        'contact_role', 
        'deal_estimate',
        'company_relationship'
      ];
      
      for (const tableName of junctionTables) {
        const foreignKeys = getForeignKeys(tableName);
        
        // All foreign keys in junction tables should have CASCADE delete
        for (const fk of foreignKeys) {
          expect(fk.on_delete).toBe('CASCADE');
        }
      }
    });
  });

  describe('Schema Integrity', () => {
    test('should have correct number of tables', () => {
      const tableCount = testDb.prepare(`
        SELECT COUNT(*) as count FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).get() as {count: number};
      
      // Should have at least 20 tables based on the migration
      expect(tableCount.count).toBeGreaterThanOrEqual(20);
    });
    
    test('should have migration tracking table with correct record', () => {
      expect(tableExists('migrations')).toBe(true);
      
      const migrationRecord = testDb.prepare(`
        SELECT * FROM migrations WHERE id = '001'
      `).get();
      
      expect(migrationRecord).toBeDefined();
      expect(migrationRecord.name).toBe('corrected_initial_schema_with_text_uuids');
    });

    test('should have TEXT UUIDs as primary keys for main entities', () => {
      const mainTables = ['company', 'contact', 'deal', 'estimate', 'note', 'activity_feed'];
      
      for (const tableName of mainTables) {
        const columns = getTableColumns(tableName);
        const idColumn = columns.find(col => col.name === 'id' && col.pk === 1);
        
        expect(idColumn).toBeDefined();
        expect(idColumn!.type).toBe('TEXT');
      }
    });

    test('should have proper audit fields on main tables', () => {
      const mainTables = ['company', 'contact', 'deal', 'estimate'];
      const auditFields = ['created_at', 'updated_at', 'created_by', 'updated_by'];
      
      for (const tableName of mainTables) {
        const columns = getTableColumns(tableName).map(col => col.name);
        
        for (const auditField of auditFields) {
          expect(columns).toContain(auditField);
        }
      }
    });

    test('should have soft delete support on main tables', () => {
      const mainTables = ['company', 'contact', 'deal', 'estimate', 'expense', 'note'];
      
      for (const tableName of mainTables) {
        const columns = getTableColumns(tableName).map(col => col.name);
        expect(columns).toContain('deleted_at');
      }
    });
  });
});