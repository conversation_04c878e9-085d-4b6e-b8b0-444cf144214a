#!/bin/bash

# <PERSON><PERSON>t to find potential issues related to repository refactoring

echo "Searching for potential repository refactoring issues..."

# 1. Search for references to the old CRM repository
echo "=== References to old CRM repository ==="
grep -r "crmRepository" --include="*.ts" --include="*.tsx" src/

# 2. Search for SQL queries with potential column reference issues
echo "=== SQL queries with potential column reference issues ==="
grep -r "c\.company_id" --include="*.ts" src/
grep -r "JOIN contact c" --include="*.ts" src/

# 3. Search for old table names
echo "=== References to old table names ==="
grep -r "deal_contact" --include="*.ts" src/

# 4. Search for import statements referencing old repository paths
echo "=== Import statements referencing old repository paths ==="
grep -r "import.*crm-repository" --include="*.ts" --include="*.tsx" src/

# 5. Search for repository method access patterns
echo "=== Repository method access patterns ==="
grep -r "this\.crmRepository\." --include="*.ts" src/

echo "Search complete. Review the results and update the code accordingly."
