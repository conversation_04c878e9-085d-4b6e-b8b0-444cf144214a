import React from 'react';
import { useProjection } from '.';

/**
 * Component for threshold settings
 */
const ThresholdSettings = () => {
  const {
    showMinBalanceLine,
    minBalanceThreshold,
    setShowMinBalanceLine,
    setMinBalanceThreshold
  } = useProjection();

  return (
    <div className="flex flex-wrap items-center justify-between xs:justify-start gap-1 xs:gap-2 sm:gap-4">
      <div className="flex items-center">
        <label htmlFor="showMinBalance" className="text-2xs xs:text-xs sm:text-sm mr-1 sm:mr-2 cursor-pointer text-gray-700 dark:text-gray-300">
          Show min balance
        </label>
        <input 
          type="checkbox" 
          id="showMinBalance" 
          checked={showMinBalanceLine}
          onChange={(e) => setShowMinBalanceLine(e.target.checked)}
          className="form-checkbox h-3 w-3 sm:h-4 sm:w-4 text-secondary rounded"
        />
      </div>
      
      <div className="flex items-center">
        <label htmlFor="minBalanceThreshold" className="text-2xs xs:text-xs sm:text-sm mr-1 sm:mr-2 text-gray-700 dark:text-gray-300">
          Threshold:
        </label>
        <input 
          type="number" 
          id="minBalanceThreshold"
          value={minBalanceThreshold}
          onChange={(e) => setMinBalanceThreshold(Number(e.target.value))}
          className="form-input py-0.5 sm:py-1 px-1 sm:px-2 w-16 sm:w-24 text-2xs xs:text-xs sm:text-sm"
          disabled={!showMinBalanceLine}
        />
      </div>
    </div>
  );
};

export default ThresholdSettings;
