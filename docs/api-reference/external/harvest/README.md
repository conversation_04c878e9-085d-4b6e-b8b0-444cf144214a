# Harvest API Reference

This document provides reference information for the Harvest API integration in Upstream, covering authentication, endpoints, data structures, and usage examples.

## Table of Contents

- [Overview](#overview)
- [Authentication](#authentication)
- [Key Endpoints](#key-endpoints)
- [Data Structures](#data-structures)
- [Usage Examples](#usage-examples)
- [Rate Limiting](#rate-limiting)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)

## Overview

Upstream integrates with Harvest's API to access project management data, including projects, time entries, invoices, and estimates. The application uses this data to provide accurate cashflow projections, staff utilization reports, and estimate management.

### API Version

Upstream uses Harvest API v2, the latest stable version available.

## Authentication

Harvest uses OAuth 2.0 for API authentication, but Upstream currently uses Personal Access Token (PAT) authentication for simplicity.

### Personal Access Token Authentication

1. **Token Generation**: Generate a personal access token in the Harvest Developer Tools section
2. **Account ID**: Obtain your account ID from the Harvest Developer Tools section
3. **Configuration**: Set the token and account ID as environment variables

### Implementation

```typescript
// Example of Harvest API request with PAT authentication
async function fetchHarvestData(endpoint: string): Promise<any> {
  const response = await fetch(`https://api.harvestapp.com/v2/${endpoint}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${process.env.HARVEST_ACCESS_TOKEN}`,
      'Harvest-Account-Id': process.env.HARVEST_ACCOUNT_ID,
      'User-Agent': 'Upstream Financial Dashboard (<EMAIL>)',
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Harvest API request failed: ${response.statusText}`);
  }
  
  return await response.json();
}
```

## Key Endpoints

Upstream uses the following key Harvest API endpoints:

### Projects

```
GET /projects
```

Returns information about all projects, including:
- Project ID
- Client details
- Project name
- Budget information
- Start and end dates
- Status (active/inactive)
- Billing method

### Time Entries

```
GET /time_entries
```

Returns information about time entries, including:
- Entry ID
- User details
- Project and task details
- Hours logged
- Date
- Billable status
- Notes

### Invoices

```
GET /invoices
```

Returns information about invoices, including:
- Invoice ID
- Client details
- Issue date
- Due date
- Amount due
- Status
- Line items

### Estimates

```
GET /estimates
```

Returns information about estimates, including:
- Estimate ID
- Client details
- Issue date
- Amount
- Status
- Line items

### Users

```
GET /users
```

Returns information about users, including:
- User ID
- Name
- Email
- Role
- Weekly capacity
- Cost rate
- Billable rate

### Clients

```
GET /clients
```

Returns information about clients, including:
- Client ID
- Name
- Currency
- Status (active/inactive)
- Contact details

### Time Reports

```
GET /reports/time/projects
```

Returns aggregated time report data, including:
- Project details
- Total hours
- Billable hours
- Non-billable hours
- Billable amount

## Data Structures

### Key Data Types

#### Project

```typescript
interface HarvestProject {
  id: number;
  client_id: number;
  name: string;
  code: string;
  is_active: boolean;
  is_billable: boolean;
  is_fixed_fee: boolean;
  bill_by: string;
  budget: number;
  budget_by: string;
  budget_is_monthly: boolean;
  cost_budget: number;
  cost_budget_include_expenses: boolean;
  hourly_rate: number;
  fee: number;
  notes: string;
  starts_on: string;
  ends_on: string;
  created_at: string;
  updated_at: string;
  client: {
    id: number;
    name: string;
    currency: string;
  };
}
```

#### Time Entry

```typescript
interface HarvestTimeEntry {
  id: number;
  spent_date: string;
  hours: number;
  notes: string;
  billable: boolean;
  billable_rate: number;
  cost_rate: number;
  user_id: number;
  user_name: string;
  client_id: number;
  client_name: string;
  project_id: number;
  project_name: string;
  task_id: number;
  task_name: string;
  created_at: string;
  updated_at: string;
}
```

#### Invoice

```typescript
interface HarvestInvoice {
  id: number;
  client_id: number;
  number: string;
  purchase_order: string;
  amount: number;
  due_amount: number;
  tax: number;
  tax_amount: number;
  tax2: number;
  tax2_amount: number;
  discount: number;
  discount_amount: number;
  subject: string;
  notes: string;
  currency: string;
  state: string;
  period_start: string;
  period_end: string;
  issue_date: string;
  due_date: string;
  payment_term: string;
  sent_at: string;
  paid_at: string;
  paid_date: string;
  closed_at: string;
  created_at: string;
  updated_at: string;
  client: {
    id: number;
    name: string;
  };
  line_items: HarvestInvoiceLineItem[];
}
```

#### Estimate

```typescript
interface HarvestEstimate {
  id: number;
  client_id: number;
  number: string;
  purchase_order: string;
  amount: number;
  tax: number;
  tax_amount: number;
  tax2: number;
  tax2_amount: number;
  discount: number;
  discount_amount: number;
  subject: string;
  notes: string;
  currency: string;
  state: string;
  issue_date: string;
  sent_at: string;
  accepted_at: string;
  declined_at: string;
  created_at: string;
  updated_at: string;
  client: {
    id: number;
    name: string;
  };
  line_items: HarvestEstimateLineItem[];
}
```

#### User

```typescript
interface HarvestUser {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  telephone: string;
  timezone: string;
  weekly_capacity: number;
  has_access_to_all_future_projects: boolean;
  is_contractor: boolean;
  is_admin: boolean;
  is_project_manager: boolean;
  can_see_rates: boolean;
  can_create_projects: boolean;
  can_create_invoices: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  default_hourly_rate: number;
  cost_rate: number;
  roles: string[];
}
```

## Usage Examples

### Getting Projects

```typescript
async function getProjects(): Promise<Project[]> {
  const response = await fetch('https://api.harvestapp.com/v2/projects?is_active=true', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${process.env.HARVEST_ACCESS_TOKEN}`,
      'Harvest-Account-Id': process.env.HARVEST_ACCOUNT_ID,
      'User-Agent': 'Upstream Financial Dashboard (<EMAIL>)',
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get projects: ${response.statusText}`);
  }
  
  const data = await response.json();
  
  return data.projects.map((project: HarvestProject) => ({
    id: project.id,
    name: project.name,
    clientId: project.client_id,
    clientName: project.client.name,
    isActive: project.is_active,
    isBillable: project.is_billable,
    isFixedFee: project.is_fixed_fee,
    budget: project.budget,
    startDate: project.starts_on,
    endDate: project.ends_on,
    hourlyRate: project.hourly_rate,
    fee: project.fee,
    currency: project.client.currency || 'AUD'
  }));
}
```

### Getting Time Entries

```typescript
async function getTimeEntries(fromDate: string, toDate: string): Promise<TimeEntry[]> {
  const response = await fetch(`https://api.harvestapp.com/v2/time_entries?from=${fromDate}&to=${toDate}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${process.env.HARVEST_ACCESS_TOKEN}`,
      'Harvest-Account-Id': process.env.HARVEST_ACCOUNT_ID,
      'User-Agent': 'Upstream Financial Dashboard (<EMAIL>)',
      'Content-Type': 'application/json'
    }
  });
  
  if (!response.ok) {
    throw new Error(`Failed to get time entries: ${response.statusText}`);
  }
  
  const data = await response.json();
  
  return data.time_entries.map((entry: HarvestTimeEntry) => ({
    id: entry.id,
    date: entry.spent_date,
    hours: entry.hours,
    notes: entry.notes,
    billable: entry.billable,
    billableRate: entry.billable_rate,
    costRate: entry.cost_rate,
    userId: entry.user_id,
    userName: entry.user_name,
    clientId: entry.client_id,
    clientName: entry.client_name,
    projectId: entry.project_id,
    projectName: entry.project_name,
    taskId: entry.task_id,
    taskName: entry.task_name
  }));
}
```

### Creating an Estimate

```typescript
async function createEstimate(estimateData: any): Promise<number> {
  const response = await fetch('https://api.harvestapp.com/v2/estimates', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.HARVEST_ACCESS_TOKEN}`,
      'Harvest-Account-Id': process.env.HARVEST_ACCOUNT_ID,
      'User-Agent': 'Upstream Financial Dashboard (<EMAIL>)',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(estimateData)
  });
  
  if (!response.ok) {
    throw new Error(`Failed to create estimate: ${response.statusText}`);
  }
  
  const data = await response.json();
  
  return data.id;
}
```

## Rate Limiting

Harvest enforces rate limits to prevent abuse of their API:

- **Standard limit**: 100 requests per 15-second window per account

Upstream implements the following strategies to handle rate limits:

1. **Request throttling**: Limits the number of requests to stay below limits
2. **Exponential backoff**: Retries failed requests with increasing delays
3. **Caching**: Caches responses to reduce the number of API calls
4. **Batch processing**: Combines multiple operations into single requests where possible

## Error Handling

Upstream implements robust error handling for Harvest API interactions:

### Common Error Types

1. **Authentication Errors**: Issues with tokens or permissions
2. **Validation Errors**: Issues with request data
3. **Rate Limiting Errors**: Too many requests
4. **API Errors**: Other API-specific errors

### Error Handling Strategy

```typescript
async function makeHarvestRequest(endpoint: string): Promise<any> {
  try {
    const response = await fetch(`https://api.harvestapp.com/v2/${endpoint}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.HARVEST_ACCESS_TOKEN}`,
        'Harvest-Account-Id': process.env.HARVEST_ACCOUNT_ID,
        'User-Agent': 'Upstream Financial Dashboard (<EMAIL>)',
        'Content-Type': 'application/json'
      }
    });
    
    if (response.status === 429) {
      // Rate limit exceeded, wait and retry
      const retryAfter = parseInt(response.headers.get('Retry-After') || '15', 10);
      await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
      return makeHarvestRequest(endpoint);
    }
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Harvest API error (${response.status}): ${errorData.message || response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    // Log the error
    console.error('Harvest API request failed:', error);
    
    // Return partial data or throw, depending on the context
    if (error.message.includes('Rate limit exceeded')) {
      return { partial: true, message: 'Rate limit exceeded, showing partial data' };
    }
    
    throw error;
  }
}
```

## Best Practices

When working with the Harvest API in Upstream, follow these best practices:

1. **Pagination Handling**:
   - Always handle pagination for endpoints that return large datasets
   - Use the `page` and `per_page` parameters to control pagination
   - Check for the `next_page` field in the response to determine if more pages exist

2. **Rate Limit Management**:
   - Implement request throttling
   - Use caching to reduce API calls
   - Batch requests where possible
   - Implement exponential backoff for retries

3. **Error Handling**:
   - Implement robust error handling
   - Provide meaningful error messages
   - Handle partial data scenarios
   - Log errors for troubleshooting

4. **Data Validation**:
   - Validate API responses before processing
   - Implement type checking for API responses
   - Handle missing or null data gracefully

5. **Performance Optimization**:
   - Use the reports API for aggregated data instead of fetching individual records
   - Limit date ranges to reduce response size
   - Use appropriate filters to minimize data transfer
   - Cache frequently accessed data

6. **Time Entry Limitations**:
   - Be aware that time report queries cannot exceed 1 year
   - Split long time ranges into multiple requests
