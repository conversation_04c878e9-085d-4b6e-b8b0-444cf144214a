import React, { forwardRef, useEffect } from "react";
import { X } from "lucide-react";
import { cn } from "../../../utils/component-variants";

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: "sm" | "default" | "lg" | "full";
  closeOnOverlayClick?: boolean;
  closeOnEsc?: boolean;
  showCloseButton?: boolean;
  className?: string;
  overlayClassName?: string;
}

/**
 * Mobile-friendly Modal component
 *
 * Features:
 * - Mobile-optimized sizing and scrolling
 * - Proper close button with 44px touch target
 * - Keyboard navigation (ESC to close)
 * - Focus trap
 * - Smooth animations
 * - Accessibility features
 * - Dark mode support
 */
export const Modal = forwardRef<HTMLDivElement, ModalProps>(
  (
    {
      isOpen,
      onClose,
      children,
      title,
      size = "default",
      closeOnOverlayClick = true,
      closeOnEsc = true,
      showCloseButton = true,
      className,
      overlayClassName,
    },
    ref
  ) => {
    // Handle ESC key press
    useEffect(() => {
      if (!isOpen || !closeOnEsc) return;

      const handleEsc = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          onClose();
        }
      };

      document.addEventListener("keydown", handleEsc);
      return () => document.removeEventListener("keydown", handleEsc);
    }, [isOpen, closeOnEsc, onClose]);

    // Prevent body scroll when modal is open
    useEffect(() => {
      if (isOpen) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "unset";
      }

      return () => {
        document.body.style.overflow = "unset";
      };
    }, [isOpen]);

    if (!isOpen) return null;

    const sizeClasses = {
      sm: "max-w-md",
      default: "max-w-2xl",
      lg: "max-w-4xl",
      full: "max-w-full",
    };

    return (
      <div
        className={cn(
          "fixed inset-0 z-50 flex items-center justify-center p-4 md:p-6",
          overlayClassName
        )}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? "modal-title" : undefined}
      >
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={closeOnOverlayClick ? onClose : undefined}
          aria-hidden="true"
        />

        {/* Modal container */}
        <div
          ref={ref}
          className={cn(
            // Base styles
            "relative bg-white dark:bg-gray-800 rounded-lg shadow-xl",
            // Mobile-specific styles
            "w-full mx-4 max-h-[calc(100vh-2rem)]",
            // Desktop styles
            "md:max-h-[calc(100vh-4rem)]",
            // Size variants
            sizeClasses[size],
            // Animation
            "animate-in fade-in-0 zoom-in-95 duration-300",
            className
          )}
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200 dark:border-gray-700">
              {title && (
                <h2
                  id="modal-title"
                  className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white"
                >
                  {title}
                </h2>
              )}
              {showCloseButton && (
                <button
                  onClick={onClose}
                  className={cn(
                    // Base styles
                    "flex items-center justify-center rounded-lg",
                    // Mobile touch target (44px minimum)
                    "min-w-[44px] min-h-[44px] p-2",
                    // Colors and hover
                    "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400",
                    "hover:bg-gray-100 dark:hover:bg-gray-700",
                    // Focus styles
                    "focus:outline-none focus:ring-2 focus:ring-primary-500",
                    // Position adjustment if no title
                    !title && "ml-auto"
                  )}
                  aria-label="Close modal"
                >
                  <X className="w-5 h-5 md:w-6 md:h-6" />
                </button>
              )}
            </div>
          )}

          {/* Content */}
          <div
            className={cn(
              // Base styles
              "overflow-y-auto",
              // Mobile padding
              "p-4",
              // Desktop padding
              "md:p-6",
              // Max height for scrolling
              title || showCloseButton
                ? "max-h-[calc(100vh-8rem)] md:max-h-[calc(100vh-10rem)]"
                : "max-h-[calc(100vh-4rem)] md:max-h-[calc(100vh-6rem)]",
              // Ensure proper scrolling on iOS
              "-webkit-overflow-scrolling-touch"
            )}
          >
            {children}
          </div>
        </div>
      </div>
    );
  }
);

Modal.displayName = "Modal";

/**
 * Modal Footer component for action buttons
 */
export const ModalFooter = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "flex flex-col-reverse sm:flex-row sm:justify-end gap-2 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700",
        className
      )}
    >
      {children}
    </div>
  );
};

ModalFooter.displayName = "ModalFooter";

export default Modal;