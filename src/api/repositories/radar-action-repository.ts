/**
 * Radar Action Repository
 * 
 * Handles database operations for radar action items (Question Marks section)
 */

import { BaseRepository } from './base-repository';
import { v4 as uuidv4 } from 'uuid';
import type {
  RadarAction,
  CreateRadarAction,
  UpdateRadarAction,
  RadarActionFilters,
  RadarActionStatus,
  RadarActionPriority
} from '../../types/radar-action-types';

interface DatabaseRow {
  id: string;
  company_id: string;
  action_type: string;
  title: string;
  description?: string;
  assigned_to: string;
  status: string;
  priority: string;
  due_date?: string;
  started_at?: string;
  completed_at?: string;
  notes?: string;
  completion_notes?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
  deleted_at?: string;
  // Joined company data
  company_name?: string;
  company_industry?: string;
  company_website?: string;
}

type QueryParam = string | number | boolean | null | undefined;

export class RadarActionRepository extends BaseRepository {
  constructor() {
    super('radar_action_items');
  }

  /**
   * Create a new radar action
   */
  async createAction(data: CreateRadarAction): Promise<RadarAction> {
    return this.transaction(() => {
      const id = uuidv4();
      const now = new Date().toISOString();

      const query = `
        INSERT INTO radar_action_items (
          id, company_id, action_type, title, description,
          assigned_to, status, priority, due_date, notes,
          created_at, updated_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      this.db.prepare(query).run(
        id,
        data.companyId,
        data.actionType,
        data.title,
        data.description || null,
        data.assignedTo,
        'pending', // Default status
        data.priority || 'normal',
        data.dueDate || null,
        data.notes || null,
        now,
        now,
        data.createdBy
      );

      // Return the created action
      const created = this.getById<DatabaseRow>(id);
      if (!created) {
        throw new Error('Failed to create radar action');
      }

      return this.mapDatabaseToRadarAction(created);
    });
  }

  /**
   * Get action by ID with optional company join
   */
  async getActionById(id: string, includeCompany: boolean = false): Promise<RadarAction | null> {
    try {
      const query = includeCompany
        ? `
          SELECT 
            rai.*,
            c.name as company_name,
            c.industry as company_industry,
            c.website as company_website
          FROM radar_action_items rai
          LEFT JOIN company c ON rai.company_id = c.id
          WHERE rai.id = ? AND rai.deleted_at IS NULL
        `
        : 'SELECT * FROM radar_action_items WHERE id = ? AND deleted_at IS NULL';

      const result = this.db.prepare(query).get(id) as DatabaseRow | undefined;
      return result ? this.mapDatabaseToRadarAction(result) : null;
    } catch (error) {
      console.error(`Error getting radar action by ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update a radar action
   */
  async updateAction(id: string, data: UpdateRadarAction): Promise<RadarAction | null> {
    return this.transaction(() => {
      const now = new Date().toISOString();
      const updateFields: string[] = [];
      const updateValues: QueryParam[] = [];

      // Build dynamic update query
      if (data.title !== undefined) {
        updateFields.push('title = ?');
        updateValues.push(data.title);
      }
      if (data.description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(data.description);
      }
      if (data.assignedTo !== undefined) {
        updateFields.push('assigned_to = ?');
        updateValues.push(data.assignedTo);
      }
      if (data.status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(data.status);
        
        // Handle status transitions
        if (data.status === 'in_progress' && !data.startedAt) {
          updateFields.push('started_at = ?');
          updateValues.push(now);
        }
        if (data.status === 'completed' && !data.completedAt) {
          updateFields.push('completed_at = ?');
          updateValues.push(now);
        }
      }
      if (data.priority !== undefined) {
        updateFields.push('priority = ?');
        updateValues.push(data.priority);
      }
      if (data.dueDate !== undefined) {
        updateFields.push('due_date = ?');
        updateValues.push(data.dueDate);
      }
      if (data.notes !== undefined) {
        updateFields.push('notes = ?');
        updateValues.push(data.notes);
      }
      if (data.startedAt !== undefined) {
        updateFields.push('started_at = ?');
        updateValues.push(data.startedAt);
      }
      if (data.completedAt !== undefined) {
        updateFields.push('completed_at = ?');
        updateValues.push(data.completedAt);
      }
      if (data.completionNotes !== undefined) {
        updateFields.push('completion_notes = ?');
        updateValues.push(data.completionNotes);
      }
      if (data.updatedBy !== undefined) {
        updateFields.push('updated_by = ?');
        updateValues.push(data.updatedBy);
      }

      updateFields.push('updated_at = ?');
      updateValues.push(now);

      updateValues.push(id);

      const query = `
        UPDATE radar_action_items 
        SET ${updateFields.join(', ')} 
        WHERE id = ? AND deleted_at IS NULL
      `;

      const result = this.db.prepare(query).run(...updateValues);

      if (result.changes === 0) {
        return null;
      }

      return this.getActionById(id, true);
    });
  }

  /**
   * Soft delete a radar action
   */
  async deleteAction(id: string, deletedBy: string): Promise<boolean> {
    try {
      const now = new Date().toISOString();
      const query = `
        UPDATE radar_action_items 
        SET deleted_at = ?, updated_by = ?, updated_at = ?
        WHERE id = ? AND deleted_at IS NULL
      `;
      
      const result = this.db.prepare(query).run(now, deletedBy, now, id);
      return result.changes > 0;
    } catch (error) {
      console.error(`Error deleting radar action ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get actions with filters
   */
  async getActions(filters: RadarActionFilters = {}): Promise<RadarAction[]> {
    try {
      let query = `
        SELECT 
          rai.*,
          c.name as company_name,
          c.industry as company_industry,
          c.website as company_website
        FROM radar_action_items rai
        LEFT JOIN company c ON rai.company_id = c.id
        WHERE rai.deleted_at IS NULL
      `;
      const params: QueryParam[] = [];

      // Apply filters
      if (filters.companyId) {
        query += ' AND rai.company_id = ?';
        params.push(filters.companyId);
      }

      if (filters.assignedTo) {
        query += ' AND rai.assigned_to = ?';
        params.push(filters.assignedTo);
      }

      if (filters.status) {
        if (Array.isArray(filters.status)) {
          const placeholders = filters.status.map(() => '?').join(',');
          query += ` AND rai.status IN (${placeholders})`;
          params.push(...filters.status);
        } else {
          query += ' AND rai.status = ?';
          params.push(filters.status);
        }
      }

      if (filters.priority) {
        if (Array.isArray(filters.priority)) {
          const placeholders = filters.priority.map(() => '?').join(',');
          query += ` AND rai.priority IN (${placeholders})`;
          params.push(...filters.priority);
        } else {
          query += ' AND rai.priority = ?';
          params.push(filters.priority);
        }
      }

      if (filters.actionType) {
        if (Array.isArray(filters.actionType)) {
          const placeholders = filters.actionType.map(() => '?').join(',');
          query += ` AND rai.action_type IN (${placeholders})`;
          params.push(...filters.actionType);
        } else {
          query += ' AND rai.action_type = ?';
          params.push(filters.actionType);
        }
      }

      if (filters.dueBefore) {
        query += ' AND rai.due_date <= ?';
        params.push(filters.dueBefore);
      }

      if (filters.dueAfter) {
        query += ' AND rai.due_date >= ?';
        params.push(filters.dueAfter);
      }

      // By default, exclude completed unless explicitly requested
      if (!filters.includeCompleted) {
        query += ' AND rai.status != ?';
        params.push('completed');
      }

      // Order by priority and due date
      query += ' ORDER BY ';
      query += "CASE rai.priority WHEN 'urgent' THEN 1 WHEN 'high' THEN 2 WHEN 'normal' THEN 3 WHEN 'low' THEN 4 END, ";
      query += 'rai.due_date ASC NULLS LAST, rai.created_at DESC';

      const results = this.db.prepare(query).all(...params) as DatabaseRow[];
      return results.map(row => this.mapDatabaseToRadarAction(row));
    } catch (error) {
      console.error('Error getting radar actions:', error);
      throw error;
    }
  }

  /**
   * Get actions for a specific company
   */
  async getActionsForCompany(companyId: string, includeCompleted: boolean = false): Promise<RadarAction[]> {
    return this.getActions({ 
      companyId, 
      includeCompleted 
    });
  }

  /**
   * Get actions assigned to a specific user
   */
  async getActionsForUser(assignedTo: string, includeCompleted: boolean = false): Promise<RadarAction[]> {
    return this.getActions({ 
      assignedTo, 
      includeCompleted 
    });
  }

  /**
   * Get overdue actions
   */
  async getOverdueActions(): Promise<RadarAction[]> {
    const now = new Date().toISOString();
    return this.getActions({
      dueBefore: now,
      status: ['pending', 'in_progress'] as RadarActionStatus[],
      includeCompleted: false
    });
  }

  /**
   * Complete an action with notes
   */
  async completeAction(id: string, completionNotes: string, completedBy: string): Promise<RadarAction | null> {
    return this.updateAction(id, {
      status: 'completed',
      completedAt: new Date().toISOString(),
      completionNotes,
      updatedBy: completedBy
    });
  }

  /**
   * Cancel an action
   */
  async cancelAction(id: string, notes: string, cancelledBy: string): Promise<RadarAction | null> {
    return this.updateAction(id, {
      status: 'cancelled',
      notes,
      updatedBy: cancelledBy
    });
  }

  /**
   * Count actions by status
   */
  async countActionsByStatus(): Promise<Record<RadarActionStatus, number>> {
    try {
      const query = `
        SELECT status, COUNT(*) as count
        FROM radar_action_items
        WHERE deleted_at IS NULL
        GROUP BY status
      `;

      const results = this.db.prepare(query).all() as Array<{ status: string; count: number }>;
      
      const counts: Record<RadarActionStatus, number> = {
        pending: 0,
        in_progress: 0,
        completed: 0,
        cancelled: 0
      };

      results.forEach(row => {
        if (row.status in counts) {
          counts[row.status as RadarActionStatus] = row.count;
        }
      });

      return counts;
    } catch (error) {
      console.error('Error counting actions by status:', error);
      throw error;
    }
  }

  /**
   * Map database row to RadarAction object
   */
  private mapDatabaseToRadarAction(row: DatabaseRow): RadarAction {
    const action: RadarAction = {
      id: row.id,
      companyId: row.company_id,
      actionType: row.action_type as any,
      title: row.title,
      description: row.description,
      assignedTo: row.assigned_to,
      status: row.status as RadarActionStatus,
      priority: row.priority as RadarActionPriority,
      dueDate: row.due_date,
      startedAt: row.started_at,
      completedAt: row.completed_at,
      notes: row.notes,
      completionNotes: row.completion_notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      createdBy: row.created_by,
      updatedBy: row.updated_by,
      deletedAt: row.deleted_at
    };

    // Add company data if available
    if (row.company_name) {
      action.company = {
        id: row.company_id,
        name: row.company_name,
        industry: row.company_industry,
        website: row.company_website
      };
    }

    return action;
  }
}