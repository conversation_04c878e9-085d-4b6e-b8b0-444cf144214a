/**
 * Enrichment Workflow Integration Tests
 * 
 * End-to-end tests for the complete enrichment workflow including:
 * - Database setup and teardown
 * - Real service integration
 * - API endpoint testing
 * - Data persistence validation
 * - Error scenarios
 * - Performance considerations
 */

import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeDatabase, closeDatabase } from '../../src/api/services/db-service';
import { Database } from '../../src/database';
import { ABNLookupService } from '../../src/api/services/enrichment/abn-lookup-service';
import { EnrichmentService } from '../../src/api/services/enrichment/enrichment-service';
import { EnrichmentRepository } from '../../src/api/repositories/enrichment-repository';
import { CompanyRepository } from '../../src/api/repositories/company-repository';

// Mock external APIs to avoid real API calls in tests
jest.mock('axios');
import axios from 'axios';
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Enrichment Workflow Integration', () => {
  let app: any;
  let db: Database;
  let companyRepository: CompanyRepository;
  let enrichmentRepository: EnrichmentRepository;
  let enrichmentService: EnrichmentService;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    process.env.ABN_LOOKUP_GUID = 'test-guid-12345'; // Set test GUID
    
    await initializeDatabase();
    
    // Import app after database initialization
    const { app: testApp } = await import('../../src/api/server');
    app = testApp;
    
    // Initialize repositories and services
    companyRepository = new CompanyRepository();
    enrichmentRepository = new EnrichmentRepository();
    enrichmentService = new EnrichmentService();
  });

  afterAll(async () => {
    await closeDatabase();
    delete process.env.ABN_LOOKUP_GUID;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Clear test data
    const db = (companyRepository as any).db;
    db.prepare('DELETE FROM company_enrichment').run();
    db.prepare('DELETE FROM enrichment_log').run();
    db.prepare('DELETE FROM company').run();
  });

  // Test data factories
  const createTestCompany = (overrides = {}) => {
    const companyData = {
      name: 'Test Company Pty Ltd',
      industry: 'Technology',
      website: 'https://test.com.au',
      address: '123 Collins St, Melbourne VIC 3000',
      source: 'manual',
      ...overrides
    };
    
    return companyRepository.createCompany(companyData, 'test-user');
  };

  const mockSuccessfulABNResponse = () => {
    const searchResponse = `
      <?xml version="1.0" encoding="utf-8"?>
      <ABRPayloadSearchResults>
        <response>
          <searchResultsList>
            <searchResultsRecord>
              <ABN>
                <identifierValue>***********</identifierValue>
                <identifierStatus>Active</identifierStatus>
              </ABN>
              <mainName>
                <organisationName>Test Company Pty Ltd</organisationName>
              </mainName>
              <relevancyScore>95.5</relevancyScore>
            </searchResultsRecord>
          </searchResultsList>
        </response>
      </ABRPayloadSearchResults>
    `;

    const detailResponse = `
      <?xml version="1.0" encoding="utf-8"?>
      <ABRPayloadSearchResults>
        <response>
          <businessEntity>
            <ABN>
              <identifierValue>***********</identifierValue>
              <identifierStatus>Active</identifierStatus>
            </ABN>
            <entityType>
              <entityTypeCode>PRV</entityTypeCode>
              <entityDescription>Australian Private Company</entityDescription>
            </entityType>
            <mainName>
              <organisationName>Test Company Pty Ltd</organisationName>
            </mainName>
            <goodsAndServicesTax>
              <effectiveFrom>2020-01-01</effectiveFrom>
            </goodsAndServicesTax>
            <mainBusinessPhysicalAddress>
              <stateCode>VIC</stateCode>
              <postcode>3000</postcode>
            </mainBusinessPhysicalAddress>
            <businessName>
              <organisationName>Test Trading Name</organisationName>
            </businessName>
            <ASICNumber>*********</ASICNumber>
          </businessEntity>
        </response>
      </ABRPayloadSearchResults>
    `;

    mockedAxios.get
      .mockResolvedValueOnce({ data: searchResponse })
      .mockResolvedValueOnce({ data: detailResponse });
  };

  describe('Complete Enrichment Workflow', () => {
    it('should successfully enrich a company end-to-end', async () => {
      // Create a test company
      const company = createTestCompany();
      
      // Mock successful ABN API responses
      mockSuccessfulABNResponse();

      // Trigger enrichment via API
      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      // Verify API response structure
      expect(response.body).toHaveProperty('company');
      expect(response.body).toHaveProperty('enrichment');
      expect(response.body).toHaveProperty('results');
      expect(response.body.results).toHaveLength(1);
      expect(response.body.results[0].success).toBe(true);
      expect(response.body.results[0].source).toBe('abn_lookup');

      // Verify enrichment data was saved to database
      const enrichments = await enrichmentRepository.getCompanyEnrichment(company.id);
      expect(enrichments).toHaveLength(1);
      expect(enrichments[0].source).toBe('abn_lookup');
      expect(enrichments[0].data).toHaveProperty('abn', '12 ***********');
      expect(enrichments[0].data).toHaveProperty('abnStatus', 'Active');
      expect(enrichments[0].data).toHaveProperty('entityType', 'Australian Private Company');

      // Verify company enrichment status was updated
      const updatedCompany = companyRepository.getCompanyById(company.id);
      expect(updatedCompany?.enrichmentStatus).toBeTruthy();
      expect(updatedCompany?.lastEnrichedAt).toBeTruthy();

      // Verify enrichment attempt was logged
      const db = (enrichmentRepository as any).db;
      const logs = db.prepare(`
        SELECT * FROM enrichment_log WHERE entity_id = ?
      `).all(company.id);
      expect(logs).toHaveLength(1);
      expect(logs[0].status).toBe('success');
      expect(logs[0].source).toBe('abn_lookup');
    });

    it('should handle enrichment failure gracefully', async () => {
      const company = createTestCompany();
      
      // Mock ABN API failure
      mockedAxios.get.mockRejectedValueOnce(new Error('ABN API timeout'));

      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      // Should return failure result
      expect(response.body.results).toHaveLength(1);
      expect(response.body.results[0].success).toBe(false);
      expect(response.body.results[0].error).toContain('ABN API timeout');

      // Should still log the attempt
      const db = (enrichmentRepository as any).db;
      const logs = db.prepare(`
        SELECT * FROM enrichment_log WHERE entity_id = ?
      `).all(company.id);
      expect(logs).toHaveLength(1);
      expect(logs[0].status).toBe('failed');
    });

    it('should handle partial enrichment results', async () => {
      const company = createTestCompany();
      
      // Mock search success but detail failure
      const searchResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>***********</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Test Company</organisationName>
                </mainName>
                <relevancyScore>95.5</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      const detailResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response />
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get
        .mockResolvedValueOnce({ data: searchResponse })
        .mockResolvedValueOnce({ data: detailResponse });

      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      expect(response.body.results[0].success).toBe(false);
      expect(response.body.results[0].error).toBe('Failed to get ABN details');
    });

    it('should support enrichment with specific sources', async () => {
      const company = createTestCompany();
      mockSuccessfulABNResponse();

      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send({ sources: ['abn_lookup'] })
        .expect(200);

      expect(response.body.results).toHaveLength(1);
      expect(response.body.results[0].source).toBe('abn_lookup');
    });
  });

  describe('Data Retrieval and Status Checking', () => {
    it('should retrieve saved enrichment data', async () => {
      const company = createTestCompany();
      mockSuccessfulABNResponse();

      // First enrich the company
      await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      // Then retrieve the enrichment data
      const response = await request(app)
        .get(`/api/enrichment/company/${company.id}`)
        .expect(200);

      expect(response.body.enrichment).toHaveLength(1);
      expect(response.body.enrichment[0]).toHaveProperty('source', 'abn_lookup');
      expect(response.body.enrichment[0]).toHaveProperty('data');
      expect(response.body.enrichment[0].data).toHaveProperty('abn');
    });

    it('should check enrichment status correctly', async () => {
      const company = createTestCompany();

      // Check status before enrichment
      let response = await request(app)
        .get(`/api/enrichment/company/${company.id}/check`)
        .expect(200);

      expect(response.body.needsEnrichment).toBe(true);
      expect(response.body.lastEnrichedAt).toBeNull();

      // Enrich the company
      mockSuccessfulABNResponse();
      await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      // Check status after enrichment
      response = await request(app)
        .get(`/api/enrichment/company/${company.id}/check`)
        .expect(200);

      expect(response.body.needsEnrichment).toBe(false);
      expect(response.body.lastEnrichedAt).toBeTruthy();
      expect(response.body.enrichmentStatus).toBeTruthy();
    });

    it('should filter enrichment data by source', async () => {
      const company = createTestCompany();
      mockSuccessfulABNResponse();

      // Enrich the company
      await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      // Get ABN enrichment only
      const abnResponse = await request(app)
        .get(`/api/enrichment/company/${company.id}?source=abn_lookup`)
        .expect(200);

      expect(abnResponse.body.enrichment).toHaveLength(1);
      expect(abnResponse.body.enrichment[0].source).toBe('abn_lookup');

      // Get non-existent source
      const emptyResponse = await request(app)
        .get(`/api/enrichment/company/${company.id}?source=clearbit`)
        .expect(200);

      expect(emptyResponse.body.enrichment).toHaveLength(0);
    });
  });

  describe('Company Discovery and Batch Operations', () => {
    it('should identify companies needing enrichment', async () => {
      // Create some companies
      const enrichedCompany = createTestCompany({ name: 'Enriched Company' });
      const unenrichedCompany1 = createTestCompany({ name: 'Unenriched Company 1' });
      const unenrichedCompany2 = createTestCompany({ name: 'Unenriched Company 2' });

      // Enrich one company
      mockSuccessfulABNResponse();
      await enrichmentService.enrichCompany(
        companyRepository.getCompanyById(enrichedCompany.id)!
      );

      // Get pending companies
      const response = await request(app)
        .get('/api/enrichment/companies/pending')
        .expect(200);

      expect(response.body.companies.length).toBeGreaterThanOrEqual(2);
      
      const pendingIds = response.body.companies.map((c: any) => c.id);
      expect(pendingIds).toContain(unenrichedCompany1.id);
      expect(pendingIds).toContain(unenrichedCompany2.id);
      expect(pendingIds).not.toContain(enrichedCompany.id);
    });

    it('should respect limit parameter for pending companies', async () => {
      // Create multiple companies
      for (let i = 0; i < 5; i++) {
        createTestCompany({ name: `Company ${i}` });
      }

      const response = await request(app)
        .get('/api/enrichment/companies/pending?limit=3')
        .expect(200);

      expect(response.body.companies.length).toBeLessThanOrEqual(3);
    });
  });

  describe('Statistics and Reporting', () => {
    it('should provide enrichment statistics', async () => {
      // Create and enrich some companies
      const company1 = createTestCompany({ name: 'Stats Company 1' });
      const company2 = createTestCompany({ name: 'Stats Company 2' });

      mockSuccessfulABNResponse();
      await request(app)
        .post(`/api/enrichment/company/${company1.id}`)
        .send()
        .expect(200);

      mockSuccessfulABNResponse();
      await request(app)
        .post(`/api/enrichment/company/${company2.id}`)
        .send()
        .expect(200);

      const response = await request(app)
        .get('/api/enrichment/stats')
        .expect(200);

      expect(response.body).toHaveProperty('overview');
      expect(response.body).toHaveProperty('bySource');
      expect(response.body.overview.enrichedCompanies).toBeGreaterThan(0);
      expect(response.body.overview.totalEnrichments).toBeGreaterThan(0);
      expect(response.body.bySource).toHaveLength(1);
      expect(response.body.bySource[0].source).toBe('abn_lookup');
    });
  });

  describe('Data Cleanup and Maintenance', () => {
    it('should clean up expired enrichment data', async () => {
      const company = createTestCompany();
      
      // Create expired enrichment manually
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);
      
      await enrichmentRepository.saveCompanyEnrichment({
        companyId: company.id,
        source: 'abn_lookup',
        data: { abn: '***********' },
        confidence: 0.9,
        expiresAt: pastDate
      });

      // Create non-expired enrichment
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);
      
      await enrichmentRepository.saveCompanyEnrichment({
        companyId: company.id,
        source: 'clearbit',
        data: { website: 'test.com' },
        confidence: 0.8,
        expiresAt: futureDate
      });

      // Verify we have 2 enrichments
      let enrichments = await enrichmentRepository.getCompanyEnrichment(company.id);
      expect(enrichments).toHaveLength(2);

      // Run cleanup
      const response = await request(app)
        .post('/api/enrichment/cleanup')
        .send()
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.deletedCount).toBe(1);

      // Verify only non-expired enrichment remains
      enrichments = await enrichmentRepository.getCompanyEnrichment(company.id);
      expect(enrichments).toHaveLength(1);
      expect(enrichments[0].source).toBe('clearbit');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle non-existent company ID', async () => {
      const response = await request(app)
        .post('/api/enrichment/company/non-existent-id')
        .send()
        .expect(404);

      expect(response.body.error).toBe('Company not found');
    });

    it('should handle malformed request body', async () => {
      const company = createTestCompany();

      await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });

    it('should handle ABN service not configured', async () => {
      // Temporarily remove the GUID
      delete process.env.ABN_LOOKUP_GUID;
      
      const company = createTestCompany();

      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      expect(response.body.results[0].success).toBe(false);
      expect(response.body.results[0].error).toBe('ABN Lookup service not configured');

      // Restore the GUID
      process.env.ABN_LOOKUP_GUID = 'test-guid-12345';
    });

    it('should handle database connection issues', async () => {
      const company = createTestCompany();
      
      // Mock database error
      const originalPrepare = (companyRepository as any).db.prepare;
      (companyRepository as any).db.prepare = jest.fn().mockImplementation(() => {
        throw new Error('Database connection lost');
      });

      await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(500);

      // Restore original function
      (companyRepository as any).db.prepare = originalPrepare;
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent enrichment requests', async () => {
      const companies = [];
      for (let i = 0; i < 5; i++) {
        companies.push(createTestCompany({ name: `Concurrent Company ${i}` }));
      }

      // Mock responses for all requests
      for (let i = 0; i < 10; i++) {
        mockSuccessfulABNResponse();
      }

      // Send concurrent requests
      const promises = companies.map(company =>
        request(app)
          .post(`/api/enrichment/company/${company.id}`)
          .send()
      );

      const responses = await Promise.all(promises);

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.results[0].success).toBe(true);
      });

      // Verify all enrichments were saved
      for (const company of companies) {
        const enrichments = await enrichmentRepository.getCompanyEnrichment(company.id);
        expect(enrichments).toHaveLength(1);
      }
    });

    it('should track response times', async () => {
      const company = createTestCompany();
      mockSuccessfulABNResponse();

      const startTime = Date.now();
      await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);
      const endTime = Date.now();

      // Verify response time was logged
      const db = (enrichmentRepository as any).db;
      const logs = db.prepare(`
        SELECT response_time_ms FROM enrichment_log WHERE entity_id = ?
      `).all(company.id);

      expect(logs).toHaveLength(1);
      expect(logs[0].response_time_ms).toBeGreaterThan(0);
      expect(logs[0].response_time_ms).toBeLessThan(endTime - startTime + 1000); // Allow for some overhead
    });
  });

  describe('Australian Company Detection', () => {
    it('should enrich Australian companies automatically', async () => {
      const ausCompany = createTestCompany({
        name: 'Australian Company Pty Ltd',
        website: 'https://example.com.au',
        address: 'Sydney, NSW 2000, Australia'
      });

      mockSuccessfulABNResponse();

      const response = await request(app)
        .post(`/api/enrichment/company/${ausCompany.id}`)
        .send()
        .expect(200);

      expect(response.body.results).toHaveLength(1);
      expect(response.body.results[0].source).toBe('abn_lookup');
    });

    it('should skip non-Australian companies for ABN lookup', async () => {
      const usCompany = createTestCompany({
        name: 'US Company Inc',
        website: 'https://example.com',
        address: 'New York, NY, USA'
      });

      const response = await request(app)
        .post(`/api/enrichment/company/${usCompany.id}`)
        .send()
        .expect(200);

      // Should not attempt ABN enrichment
      expect(response.body.results).toHaveLength(0);
    });
  });

  describe('Data Quality and Validation', () => {
    it('should validate enrichment data quality', async () => {
      const company = createTestCompany();
      mockSuccessfulABNResponse();

      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      expect(response.body.results[0].confidence).toBeGreaterThan(0);
      expect(response.body.results[0].confidence).toBeLessThanOrEqual(1);
      expect(response.body.results[0].data).toHaveProperty('dataQuality');
      expect(['high', 'medium', 'low']).toContain(response.body.results[0].data.dataQuality);
    });

    it('should handle low confidence matches appropriately', async () => {
      const company = createTestCompany({ name: 'Very Different Company Name' });
      
      // Mock a low-score match
      const lowScoreResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <searchResultsList>
              <searchResultsRecord>
                <ABN>
                  <identifierValue>***********</identifierValue>
                  <identifierStatus>Active</identifierStatus>
                </ABN>
                <mainName>
                  <organisationName>Completely Different Name Ltd</organisationName>
                </mainName>
                <relevancyScore>25.0</relevancyScore>
              </searchResultsRecord>
            </searchResultsList>
          </response>
        </ABRPayloadSearchResults>
      `;

      const detailResponse = `
        <?xml version="1.0" encoding="utf-8"?>
        <ABRPayloadSearchResults>
          <response>
            <businessEntity>
              <ABN>
                <identifierValue>***********</identifierValue>
                <identifierStatus>Active</identifierStatus>
              </ABN>
              <entityType>
                <entityTypeCode>PRV</entityTypeCode>
                <entityDescription>Australian Private Company</entityDescription>
              </entityType>
              <mainName>
                <organisationName>Completely Different Name Ltd</organisationName>
              </mainName>
            </businessEntity>
          </response>
        </ABRPayloadSearchResults>
      `;

      mockedAxios.get
        .mockResolvedValueOnce({ data: lowScoreResponse })
        .mockResolvedValueOnce({ data: detailResponse });

      const response = await request(app)
        .post(`/api/enrichment/company/${company.id}`)
        .send()
        .expect(200);

      expect(response.body.results[0].success).toBe(true);
      expect(response.body.results[0].confidence).toBeLessThan(0.5);
      expect(response.body.results[0].data.dataQuality).toBe('low');
    });
  });
});