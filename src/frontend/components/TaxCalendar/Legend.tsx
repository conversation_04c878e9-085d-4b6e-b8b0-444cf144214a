import React, { useState } from 'react'; const Legend: React.FC = () => { const [isGuideVisible, setIsGuideVisible] = useState(false); const toggleGuide = () => { setIsGuideVisible(!isGuideVisible); }; return ( <> <div className="tax-calendar-legend flex items-center justify-between flex-wrap mb-4 pb-3 border-b border-gray-200 dark:border-gray-700"> <div className="flex items-center flex-wrap gap-4"> <div className="tax-calendar-legend-item"> <div className="tax-calendar-legend-color-group"> <div className="tax-calendar-legend-color bg-blue-200"></div> <div className="tax-calendar-legend-color bg-green-200"></div> <div className="tax-calendar-legend-color bg-purple-200"></div> </div> <span>PAYGW (Monthly)</span> </div> <div className="tax-calendar-legend-item"> <div className="tax-calendar-legend-color-group"> <div className="tax-calendar-legend-color bg-yellow-200"></div> <div className="tax-calendar-legend-color bg-red-200"></div> <div className="tax-calendar-legend-color bg-magenta-300"></div> </div> <span>GST/BAS (Quarterly)</span> </div> <div className="tax-calendar-legend-item"> <div className="tax-calendar-legend-color bg-blue-200 border-2 border-blue-500"></div> <span>Payment Due Date</span> </div> </div> <div className="tax-calendar-guide mt-2 sm:mt-0"> <button className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center gap-1" onClick={toggleGuide} aria-expanded={isGuideVisible} > <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> <span>How to read this calendar</span> </button> </div> </div> {isGuideVisible && ( <div className="tax-calendar-guide-panel mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700 shadow-sm dark:shadow-gray-900/20"> <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">How to Read This Calendar</h4> <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> <div className="flex items-center h-full gap-3 p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"> <div className="flex-shrink-0 w-10"> <div className="tax-calendar-day tax-calendar-day-split w-10 h-10"> <div className="tax-calendar-day-half-wrapper tax-calendar-day-paygw-wrapper"> <div className="tax-calendar-day-paygw bg-blue-200"></div> </div> <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper"> <div className="tax-calendar-day-gst bg-yellow-200"></div> </div> <span className="tax-calendar-day-number">15</span> </div> </div> <div className="text-xs text-gray-600 dark:text-gray-400"> <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">Calendar Day</div> <p>• <span className="font-medium">Top:</span> PAYGW (monthly) accrual</p> <p>• <span className="font-medium">Bottom:</span> GST/BAS (quarterly) accrual</p> <p>• <span className="font-medium">Number:</span> Day of month</p> </div> </div> <div className="flex items-center h-full gap-3 p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"> <div className="flex-shrink-0 w-10"> <div className="tax-calendar-day tax-calendar-current-day tax-calendar-day-split w-10 h-10 border-2 border-green-400"> <div className="tax-calendar-day-half-wrapper tax-calendar-day-paygw-wrapper"> <div className="tax-calendar-day-paygw bg-blue-200"></div> </div> <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper"> <div className="tax-calendar-day-gst bg-yellow-200"></div> </div> <span className="tax-calendar-day-number">15</span> </div> </div> <div className="text-xs text-gray-600 dark:text-gray-400"> <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">Current Day</div> <p>• Today's date (highlighted with green border)</p> <p>• Shows your position in the tax cycle</p> </div> </div> <div className="flex items-center h-full gap-3 p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"> <div className="flex-shrink-0 w-24 flex justify-center"> <div className="flex items-center gap-2"> <div className="tax-calendar-day tax-calendar-payment-day w-10 h-10 bg-blue-200 border-2 border-blue-500"> <span className="tax-calendar-day-number">21</span> </div> <div className="tax-calendar-day tax-calendar-payment-day w-10 h-10 bg-yellow-200 border-2 border-yellow-500"> <span className="tax-calendar-day-number">26</span> </div> </div> </div> <div className="text-xs text-gray-600 dark:text-gray-400"> <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">Payment Due Dates</div> <p>• <span className="font-medium">PAYGW:</span> 21st of following month</p> <p>• <span className="font-medium">GST/BAS:</span> 26th of month after next</p> <p>• Highlighted with colored borders</p> </div> </div> <div className="flex items-center h-full gap-3 p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700"> <div className="flex-shrink-0 w-24 flex justify-center"> <div className="flex items-center gap-1"> <div className="tax-calendar-day tax-calendar-day-split w-10 h-10"> <div className="tax-calendar-day-half-wrapper"> <div className="tax-calendar-day-paygw bg-blue-200"></div> </div> <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper"> <div className="tax-calendar-day-gst bg-yellow-200"></div> </div> <span className="tax-calendar-day-number">15</span> </div> <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" viewBox="0 0 20 20" fill="currentColor"> <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" /> </svg> <div className="tax-calendar-day tax-calendar-day-split w-10 h-10"> <div className="tax-calendar-day-half-wrapper"> <div className="tax-calendar-day-paygw bg-blue-500"></div> </div> <div className="tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper"> <div className="tax-calendar-day-gst bg-yellow-200"></div> </div> <span className="tax-calendar-day-number">15</span> </div> </div> </div> <div className="text-xs text-gray-600 dark:text-gray-400"> <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">Interactive Features</div> <p>• <span className="font-medium">Hover:</span> See detailed information</p> <p>• <span className="font-medium">Highlighting:</span> Shows related accrual periods and payment dates</p> </div> </div> </div> <div className="text-xs text-gray-500 dark:text-gray-500 mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex justify-end"> <p><span className="font-medium">Quarters:</span> Q1 (Jul-Sep), Q2 (Oct-Dec), Q3 (Jan-Mar), Q4 (Apr-Jun)</p> </div> </div> )} </> ); }; export default Legend; 