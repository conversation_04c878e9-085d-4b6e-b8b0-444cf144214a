/**
 * Fresh Database Initialization Script - Unified Schema Version
 *
 * This script initializes a completely fresh database using the unified schema.
 * It drops all existing tables and creates new ones from 000_unified_schema.sql.
 *
 * IMPORTANT: This will delete all existing data. Only use when you want to start fresh.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get database path from environment or use default
const dbPath = process.env.DB_PATH || path.join(__dirname, '..', 'data', 'upstream.db');

// Create data directory if it doesn't exist
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  console.log(`Creating data directory: ${dataDir}`);
  fs.mkdirSync(dataDir, { recursive: true });
}

console.log(`Initializing fresh database at: ${dbPath}`);

// Initialize the database
const db = new BetterSqlite3(dbPath);

// Function to drop all existing tables
function dropAllTables() {
  console.log('Dropping all existing tables...');

  try {
    // Get all table names
    const tables = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();

    // Disable foreign key constraints temporarily
    db.pragma('foreign_keys = OFF');

    // Drop views first
    const views = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='view'
    `).all();

    for (const view of views) {
      try {
        console.log(`Dropping view: ${view.name}`);
        db.prepare(`DROP VIEW IF EXISTS "${view.name}"`).run();
      } catch (error) {
        console.error(`Error dropping view ${view.name}:`, error.message);
      }
    }

    // Drop triggers
    const triggers = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='trigger'
    `).all();

    for (const trigger of triggers) {
      try {
        console.log(`Dropping trigger: ${trigger.name}`);
        db.prepare(`DROP TRIGGER IF EXISTS "${trigger.name}"`).run();
      } catch (error) {
        console.error(`Error dropping trigger ${trigger.name}:`, error.message);
      }
    }

    // Drop each table
    for (const table of tables) {
      try {
        console.log(`Dropping table: ${table.name}`);
        db.prepare(`DROP TABLE IF EXISTS "${table.name}"`).run();
      } catch (error) {
        console.error(`Error dropping table ${table.name}:`, error.message);
        // Continue with other tables
      }
    }

    // Re-enable foreign key constraints
    db.pragma('foreign_keys = ON');

    console.log('All database objects dropped successfully');
  } catch (error) {
    console.error('Error during cleanup:', error.message);
    console.log('Continuing with fresh database creation...');
  }
}

// Function to create database from unified schema
function createUnifiedSchema() {
  console.log('\nCreating database from unified schema...');

  try {
    // Read the unified schema file
    const schemaPath = path.join(__dirname, '..', 'migrations', '000_unified_schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error(`Unified schema file not found at: ${schemaPath}`);
    }

    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema
    console.log('Executing unified schema...');
    db.exec(schema);
    
    console.log('✅ Unified schema created successfully!');
    
    // Show summary of created objects
    showDatabaseSummary();
    
  } catch (error) {
    console.error('Error creating unified schema:', error.message);
    throw error;
  }
}

// Function to show database summary
function showDatabaseSummary() {
  console.log('\n=== Database Summary ===');
  
  // Count tables
  const tables = db.prepare(`
    SELECT COUNT(*) as count FROM sqlite_master
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
  `).get();
  console.log(`Tables created: ${tables.count}`);
  
  // Count views
  const views = db.prepare(`
    SELECT COUNT(*) as count FROM sqlite_master
    WHERE type='view'
  `).get();
  console.log(`Views created: ${views.count}`);
  
  // Count indexes
  const indexes = db.prepare(`
    SELECT COUNT(*) as count FROM sqlite_master
    WHERE type='index' AND name NOT LIKE 'sqlite_%'
  `).get();
  console.log(`Indexes created: ${indexes.count}`);
  
  // Count triggers
  const triggers = db.prepare(`
    SELECT COUNT(*) as count FROM sqlite_master
    WHERE type='trigger'
  `).get();
  console.log(`Triggers created: ${triggers.count}`);
  
  // List key tables
  console.log('\nKey tables with soft delete support:');
  const keyTables = ['company', 'contact', 'deal', 'estimate', 'expense', 'project'];
  for (const tableName of keyTables) {
    const tableExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?").get(tableName);
    if (tableExists) {
      const columns = db.prepare(`PRAGMA table_info('${tableName}')`).all();
      const hasDeletedAt = columns.some(col => col.name === 'deleted_at');
      console.log(`  - ${tableName}: ${hasDeletedAt ? '✅ Soft delete enabled' : '❌ No soft delete'}`);
    }
  }
}

// Function to add sample data
function addSampleData() {
  console.log('\n=== Adding Sample Data ===');
  
  try {
    // Insert sample company
    const companyId = 'sample-company-1';
    db.prepare(`
      INSERT INTO company (id, name, industry, website, created_by)
      VALUES (?, ?, ?, ?, ?)
    `).run(companyId, 'Sample Company Inc.', 'Technology', 'https://example.com', 'system');
    
    console.log('✅ Created sample company');
    
    // Insert sample contact
    db.prepare(`
      INSERT INTO contact (id, first_name, last_name, email, created_by)
      VALUES (?, ?, ?, ?, ?)
    `).run('sample-contact-1', 'Jane', 'Doe', '<EMAIL>', 'system');
    
    console.log('✅ Created sample contact');
    
    // Insert sample deal
    db.prepare(`
      INSERT INTO deal (id, name, stage, company_id, value, created_by, updated_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).run('sample-deal-1', 'Sample Deal', 'Identified', companyId, 100000, 'system', 'system');
    
    console.log('✅ Created sample deal');
    
  } catch (error) {
    console.error('Error adding sample data:', error.message);
    // Not critical if sample data fails
  }
}

// Main function to initialize the database
function initializeDatabase() {
  try {
    // Enable foreign keys and set pragmas
    db.pragma('foreign_keys = ON');
    db.pragma('journal_mode = WAL');
    db.pragma('busy_timeout = 30000');
    
    // Drop all existing tables first
    dropAllTables();

    // Create schema from unified schema file
    createUnifiedSchema();
    
    // Optionally add sample data
    if (process.argv.includes('--with-sample-data')) {
      addSampleData();
    }
    
    console.log('\n✅ Database initialization completed successfully');
    console.log(`Database location: ${dbPath}`);
    
  } catch (error) {
    console.error('\n❌ Database initialization failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    db.close();
  }
}

// Run the initialization
console.log('=== Fresh Database Initialization (Unified Schema) ===\n');
console.log('WARNING: This will delete all existing data!\n');

if (process.argv.includes('--force') || process.env.NODE_ENV === 'test') {
  initializeDatabase();
} else {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('Are you sure you want to proceed? (yes/no): ', (answer) => {
    if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
      rl.close();
      initializeDatabase();
    } else {
      console.log('Initialization cancelled.');
      rl.close();
      process.exit(0);
    }
  });
}