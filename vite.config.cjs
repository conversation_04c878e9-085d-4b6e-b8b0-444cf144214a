const path = require('path');
const { defineConfig } = require('vite');
const react = require('@vitejs/plugin-react');

// https://vitejs.dev/config/
module.exports = defineConfig(({ mode }) => {
  // Set API URL based on mode
  const apiUrl = mode === 'production' 
    ? 'https://onbord-financial-dashboard.onrender.com/api'
    : `http://localhost:${process.env.API_PORT || '3002'}/api`;
    
  console.log(`Building with API_URL: ${apiUrl} in ${mode} mode`);

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    define: {
      'import.meta.env.VITE_API_URL': JSON.stringify(apiUrl)
    },
    server: {
      port: parseInt(process.env.FRONTEND_PORT || '5173'),
      proxy: {
        '/api': {
          target: `http://localhost:${process.env.API_PORT || '3002'}`,
          changeOrigin: true
        }
      }
    },
    build: {
      outDir: 'dist',
      emptyOutDir: false
    }
  };
});
