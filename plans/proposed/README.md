# Proposed Plans Directory

This directory contains active development roadmaps. Files are numbered by priority order.

## 🚀 Current Active Plans

### 🟢 01-current-mobile-functionality-fixes.md (ACTIVE NOW)
**Status:** Phase 2 In Progress  
**Goal:** Make ALL features work on mobile devices  
**Timeline:** 1-2 weeks

Current work:
- ✅ Phase 1: Navigation, forms, tables, kanban boards
- 🔄 Phase 2: Estimate tables, Knowledge Graph
- ⏳ Phase 3: Touch interactions, alternative views

### 🟡 02-future-performance-optimization.md (NEXT UP)
**Status:** Waiting for mobile fixes  
**Goal:** 70% bundle size reduction, faster load times  
**Timeline:** 1-3 months

Key improvements:
- Image optimization (2MB+ immediate savings)
- Code splitting and lazy loading
- TypeScript cleanup (120+ `any` types)
- Dependency modernization

### 🔵 03-future-mobile-ux-polish.md (LATER)
**Status:** Waiting for functionality + performance  
**Goal:** Transform mobile from "works" to "delightful"  
**Timeline:** 1-2 months

Polish items:
- Modern design system with smooth animations
- Swipe gestures and bottom sheets
- Offline support and PWA features
- Context-aware mobile dashboards

## 📋 Implementation Order

1. **Complete Mobile Functionality** → File 01
2. **Optimize Performance** → File 02  
3. **Polish Mobile UX** → File 03

## Completed Features

Features that have been implemented and moved to archive:
- ✅ Activity Feed System (see `/plans/archive/activity-feed/`)

## Guidelines for New Plans

When adding new plans to this directory:
1. Use clear, descriptive filenames
2. Include status, timeline, and goals in the document
3. Avoid creating overlapping or contradictory plans
4. Move completed plans to `/plans/archive/`
5. Keep this README updated with current priorities

## Current Focus

**Immediate Priority:** Complete Phase 2 of mobile-roadmap-consolidated.md to ensure all components work properly on mobile devices. Only after this is complete should we move to performance optimization or UX enhancements.