import React from 'react';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  breakpoint?: string; // Optional breakpoint for switching direction
}

/**
 * A layout component that acts as a flex container, switching its
 * flex-direction from column to row based on its own width using
 * container queries defined in the associated CSS file.
 */
export function ResponsiveContainer({
  children,
  className = '',
  breakpoint = '600px' // Default breakpoint for switching to row
}: ResponsiveContainerProps) {
  // The container query logic is primarily handled in CSS.
  // We can pass the breakpoint as a CSS variable if needed for dynamic control.
  const style = { '--responsive-breakpoint': breakpoint };

  return (
    <div
      className={`responsive-container ${className}`}
      style={style}
    >
      {children}
    </div>
  );
}
