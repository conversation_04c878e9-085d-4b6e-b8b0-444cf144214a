import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { jest } from '@jest/globals';
import HubSpotImport from '../../../../src/frontend/components/CRM/HubSpot/HubSpotImport';
import * as hubspotApi from '../../../../src/frontend/api/hubspot';

// Mock dependencies
jest.mock('../../../../src/frontend/api/hubspot');

// Mock EventSource
global.EventSource = jest.fn(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  close: jest.fn(),
  readyState: 0,
})) as any;

describe('HubSpotImport Component', () => {
  const mockConnectionStatus = {
    connected: true,
    lastSync: '2024-01-01T00:00:00Z',
    companyCount: 100,
    contactCount: 500,
    dealCount: 50,
  };

  const mockImportProgress = {
    importId: 'import-123',
    status: 'in_progress',
    progress: {
      companies: { total: 100, processed: 50, errors: 2 },
      contacts: { total: 500, processed: 250, errors: 5 },
      deals: { total: 50, processed: 25, errors: 0 },
    },
    startedAt: '2024-01-15T10:00:00Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (hubspotApi.getHubSpotStatus as jest.Mock).mockResolvedValue(mockConnectionStatus);
  });

  const renderComponent = () => {
    return render(<HubSpotImport />);
  };

  describe('Connection Status', () => {
    it('should display connection status when connected', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/connected to hubspot/i)).toBeInTheDocument();
        expect(screen.getByText(/100 companies/i)).toBeInTheDocument();
        expect(screen.getByText(/500 contacts/i)).toBeInTheDocument();
        expect(screen.getByText(/50 deals/i)).toBeInTheDocument();
      });
    });

    it('should display disconnected state', async () => {
      (hubspotApi.getHubSpotStatus as jest.Mock).mockResolvedValue({
        connected: false,
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/not connected to hubspot/i)).toBeInTheDocument();
        expect(screen.getByText(/connect hubspot/i)).toBeInTheDocument();
      });
    });

    it('should show last sync time', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/last sync.*jan 1, 2024/i)).toBeInTheDocument();
      });
    });
  });

  describe('Import Configuration', () => {
    it('should show entity selection checkboxes', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByLabelText(/companies/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/contacts/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/deals/i)).toBeInTheDocument();
      });
    });

    it('should enable/disable entities for import', async () => {
      renderComponent();

      await waitFor(() => {
        const companiesCheckbox = screen.getByLabelText(/companies/i);
        const contactsCheckbox = screen.getByLabelText(/contacts/i);
        
        expect(companiesCheckbox).toBeChecked();
        expect(contactsCheckbox).toBeChecked();
        
        fireEvent.click(contactsCheckbox);
        expect(contactsCheckbox).not.toBeChecked();
      });
    });

    it('should show full sync option', async () => {
      renderComponent();

      await waitFor(() => {
        const fullSyncCheckbox = screen.getByLabelText(/full sync/i);
        expect(fullSyncCheckbox).toBeInTheDocument();
        expect(fullSyncCheckbox).not.toBeChecked();
      });
    });

    it('should disable import button when no entities selected', async () => {
      renderComponent();

      await waitFor(() => {
        const companiesCheckbox = screen.getByLabelText(/companies/i);
        const contactsCheckbox = screen.getByLabelText(/contacts/i);
        const dealsCheckbox = screen.getByLabelText(/deals/i);
        
        fireEvent.click(companiesCheckbox);
        fireEvent.click(contactsCheckbox);
        fireEvent.click(dealsCheckbox);
        
        const importButton = screen.getByRole('button', { name: /start import/i });
        expect(importButton).toBeDisabled();
      });
    });
  });

  describe('Import Process', () => {
    it('should start import with selected entities', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(hubspotApi.startHubSpotImport).toHaveBeenCalledWith({
          entities: ['companies', 'contacts', 'deals'],
          fullSync: false,
        });
      });
    });

    it('should show confirmation dialog before full sync', async () => {
      window.confirm = jest.fn(() => true);
      
      renderComponent();

      await waitFor(() => {
        const fullSyncCheckbox = screen.getByLabelText(/full sync/i);
        fireEvent.click(fullSyncCheckbox);
        
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      expect(window.confirm).toHaveBeenCalledWith(
        expect.stringContaining('full sync will replace all existing data')
      );
    });

    it('should cancel import if confirmation declined', async () => {
      window.confirm = jest.fn(() => false);
      
      renderComponent();

      await waitFor(() => {
        const fullSyncCheckbox = screen.getByLabelText(/full sync/i);
        fireEvent.click(fullSyncCheckbox);
        
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      expect(hubspotApi.startHubSpotImport).not.toHaveBeenCalled();
    });
  });

  describe('Import Progress Tracking', () => {
    it('should display import progress', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue(mockImportProgress);

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/import in progress/i)).toBeInTheDocument();
        expect(screen.getByText(/companies.*50.*100/i)).toBeInTheDocument();
        expect(screen.getByText(/contacts.*250.*500/i)).toBeInTheDocument();
        expect(screen.getByText(/deals.*25.*50/i)).toBeInTheDocument();
      });
    });

    it('should show progress bars', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue(mockImportProgress);

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        const progressBars = screen.getAllByRole('progressbar');
        expect(progressBars).toHaveLength(3);
        expect(progressBars[0]).toHaveAttribute('aria-valuenow', '50'); // Companies
        expect(progressBars[1]).toHaveAttribute('aria-valuenow', '50'); // Contacts
        expect(progressBars[2]).toHaveAttribute('aria-valuenow', '50'); // Deals
      });
    });

    it('should display error counts', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue(mockImportProgress);

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/2 errors/i)).toBeInTheDocument(); // Companies
        expect(screen.getByText(/5 errors/i)).toBeInTheDocument(); // Contacts
      });
    });

    it('should poll for progress updates', async () => {
      jest.useFakeTimers();
      
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue(mockImportProgress);

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      expect(hubspotApi.getImportProgress).toHaveBeenCalledTimes(1);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      expect(hubspotApi.getImportProgress).toHaveBeenCalledTimes(2);

      jest.useRealTimers();
    });
  });

  describe('Import Completion', () => {
    it('should show success message on completion', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue({
        ...mockImportProgress,
        status: 'completed',
        completedAt: '2024-01-15T10:30:00Z',
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/import completed successfully/i)).toBeInTheDocument();
        expect(screen.getByText(/imported 100 companies/i)).toBeInTheDocument();
        expect(screen.getByText(/imported 500 contacts/i)).toBeInTheDocument();
        expect(screen.getByText(/imported 50 deals/i)).toBeInTheDocument();
      });
    });

    it('should show error summary if errors occurred', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue({
        ...mockImportProgress,
        status: 'completed',
        progress: {
          companies: { total: 100, processed: 100, errors: 10 },
          contacts: { total: 500, processed: 500, errors: 25 },
          deals: { total: 50, processed: 50, errors: 5 },
        },
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/import completed with errors/i)).toBeInTheDocument();
        expect(screen.getByText(/40 total errors/i)).toBeInTheDocument();
      });
    });

    it('should allow starting new import after completion', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue({
        ...mockImportProgress,
        status: 'completed',
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/import completed/i)).toBeInTheDocument();
        const newImportButton = screen.getByRole('button', { name: /start new import/i });
        expect(newImportButton).toBeInTheDocument();
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should connect to EventSource for real-time updates', async () => {
      const mockEventSource = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1,
      };
      
      (global.EventSource as jest.Mock).mockImplementation(() => mockEventSource);
      
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      expect(global.EventSource).toHaveBeenCalledWith('/api/hubspot/import/import-123/stream');
      expect(mockEventSource.addEventListener).toHaveBeenCalledWith('progress', expect.any(Function));
    });

    it('should update UI with real-time progress events', async () => {
      const mockEventSource = {
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        close: jest.fn(),
        readyState: 1,
      };
      
      let progressCallback: ((event: any) => void) | null = null;
      
      mockEventSource.addEventListener.mockImplementation((event, callback) => {
        if (event === 'progress') {
          progressCallback = callback;
        }
      });
      
      (global.EventSource as jest.Mock).mockImplementation(() => mockEventSource);
      
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      // Simulate progress event
      act(() => {
        if (progressCallback) {
          progressCallback({
            data: JSON.stringify({
              entity: 'companies',
              processed: 75,
              total: 100,
            }),
          });
        }
      });

      await waitFor(() => {
        expect(screen.getByText(/companies.*75.*100/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should show error when import fails to start', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockRejectedValue(
        new Error('Failed to start import')
      );

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/failed to start import/i)).toBeInTheDocument();
      });
    });

    it('should handle import failure during progress', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock).mockResolvedValue({
        importId: 'import-123',
        status: 'started',
      });
      (hubspotApi.getImportProgress as jest.Mock).mockResolvedValue({
        ...mockImportProgress,
        status: 'failed',
        error: 'Connection timeout',
      });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/import failed/i)).toBeInTheDocument();
        expect(screen.getByText(/connection timeout/i)).toBeInTheDocument();
      });
    });

    it('should allow retry after failure', async () => {
      (hubspotApi.startHubSpotImport as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ importId: 'import-456', status: 'started' });

      renderComponent();

      await waitFor(() => {
        const importButton = screen.getByRole('button', { name: /start import/i });
        fireEvent.click(importButton);
      });

      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
        const retryButton = screen.getByRole('button', { name: /retry/i });
        fireEvent.click(retryButton);
      });

      expect(hubspotApi.startHubSpotImport).toHaveBeenCalledTimes(2);
    });
  });
});