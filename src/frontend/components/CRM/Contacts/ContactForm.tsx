import React, { useState } from "react";
import { ContactCreate, ContactRole } from "../../../types/crm-types";
import { useMutation, useQueryClient, useQuery } from "react-query";
import { createContact, getCompanies } from "../../../api/crm";
import {
  Input,
  Select,
  Textarea,
  FormGrid,
  FormSection,
} from "../../shared/forms";
import { Button } from "../../shared/Button";
import { getRetryableErrorMessage } from "../../../utils/error-helpers";

interface ContactFormProps {
  onClose: () => void;
}

/**
 * Form component for creating a new contact
 */
const ContactForm = ({ onClose }: ContactFormProps) => {
  const [formData, setFormData] = useState<ContactCreate>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    jobTitle: "",
    companyId: undefined, // For backward compatibility
    notes: "",
  });

  // State for managing company relationships
  const [selectedCompanies, setSelectedCompanies] = useState<
    Array<{
      companyId: string;
      role?: ContactRole;
      isPrimary: boolean;
    }>
  >([]);

  const queryClient = useQueryClient();

  // Fetch companies for the dropdown
  const { data: companies = [] } = useQuery("companies", getCompanies);

  // Mutation for creating a contact
  const createContactMutation = useMutation(
    (data: ContactCreate) => createContact(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries("contacts");
        onClose();
      },
    }
  );

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value === "" ? undefined : value,
    });
  };

  // Handle company selection
  const handleCompanySelection = (companyId: string) => {
    // For backward compatibility, also set the companyId in the formData
    if (companyId) {
      setFormData({
        ...formData,
        companyId,
      });

      // Check if this company is already selected
      const exists = selectedCompanies.some(
        (company: any) => company.companyId === companyId
      );

      if (!exists) {
        // Add to selected companies with default values
        const isPrimary = selectedCompanies.length === 0; // First company is primary by default
        setSelectedCompanies([
          ...selectedCompanies,
          { companyId, isPrimary, role: undefined },
        ]);
      }
    } else {
      // If no company selected, clear the companyId in formData
      setFormData({
        ...formData,
        companyId: undefined,
      });
    }
  };

  // Handle role change
  const handleRoleChange = (index: number, role: ContactRole | undefined) => {
    const updatedCompanies = [...selectedCompanies];
    updatedCompanies[index] = {
      ...updatedCompanies[index],
      role,
    };
    setSelectedCompanies(updatedCompanies);
  };

  // Handle primary company change
  const handlePrimaryChange = (index: number) => {
    const updatedCompanies = selectedCompanies.map((company: any, idx: number) => ({
      ...company,
      isPrimary: idx === index,
    }));
    setSelectedCompanies(updatedCompanies);

    // Also update the formData.companyId for backward compatibility
    setFormData({
      ...formData,
      companyId: updatedCompanies[index].companyId,
    });
  };

  // Handle removing a company
  const handleRemoveCompany = (index: number) => {
    const updatedCompanies = [...selectedCompanies];
    const removedCompany = updatedCompanies.splice(index, 1)[0];

    // If we're removing the primary company, make the first remaining company primary
    if (removedCompany.isPrimary && updatedCompanies.length > 0) {
      updatedCompanies[0].isPrimary = true;
      // Update formData.companyId for backward compatibility
      setFormData({
        ...formData,
        companyId: updatedCompanies[0].companyId,
      });
    } else if (updatedCompanies.length === 0) {
      // No companies left
      setFormData({
        ...formData,
        companyId: undefined,
      });
    }

    setSelectedCompanies(updatedCompanies);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Prepare data for submission
    const contactData: ContactCreate = {
      ...formData,
      // Include the new relationship model data if available
      companies: selectedCompanies.length > 0 ? selectedCompanies : undefined,
    };

    createContactMutation.mutate(contactData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Create New Contact
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          <FormSection title="Contact Information">
            <FormGrid cols={2} gap="default">
              <Input
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
                loading={createContactMutation.isLoading}
              />

              <Input
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
                loading={createContactMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          <FormSection title="Contact Details">
            <FormGrid cols={1} gap="default">
              <Input
                label="Email"
                type="email"
                name="email"
                value={formData.email || ""}
                onChange={handleChange}
                loading={createContactMutation.isLoading}
              />

              <Input
                label="Phone"
                type="tel"
                name="phone"
                value={formData.phone || ""}
                onChange={handleChange}
                loading={createContactMutation.isLoading}
              />

              <Input
                label="Job Title"
                name="jobTitle"
                value={formData.jobTitle || ""}
                onChange={handleChange}
                loading={createContactMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          <FormSection title="Company Association">
            <FormGrid cols={1} gap="default">
              <Select
                label="Company"
                name="companyId"
                value={formData.companyId || ""}
                onChange={(e: any) => handleCompanySelection(e.target.value)}
                options={[
                  { value: "", label: "None" },
                  ...companies.map((company) => ({
                    value: company.id,
                    label: company.name,
                  })),
                ]}
                loading={createContactMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          {/* Selected Companies */}
          {selectedCompanies.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Company Relationships
              </label>
              <div className="space-y-3 max-h-60 overflow-y-auto">
                {selectedCompanies.map((selectedCompany: any, index: number) => {
                  const company = companies.find(
                    (c) => c.id === selectedCompany.companyId
                  );
                  return (
                    <div
                      key={`${selectedCompany.companyId}-${index}`}
                      className="p-3 border border-gray-200 dark:border-gray-600 rounded-md"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">
                          {company?.name || "Unknown Company"}
                        </span>
                        <div className="flex items-center space-x-2">
                          <label className="inline-flex items-center space-x-2">
                            <input
                              type="radio"
                              name="primaryCompany"
                              checked={selectedCompany.isPrimary}
                              onChange={() => handlePrimaryChange(index)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                            />
                            <span className="text-sm text-gray-700 dark:text-gray-300">
                              Primary
                            </span>
                          </label>
                          <button
                            type="button"
                            onClick={() => handleRemoveCompany(index)}
                            className="p-1 text-red-500 hover:text-red-700 focus:outline-none"
                          >
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M6 18L18 6M6 6l12 12"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div>
                        <Select
                          label="Role"
                          value={selectedCompany.role || ""}
                          onChange={(e: any) =>
                            handleRoleChange(
                              index,
                              (e.target.value as ContactRole) || undefined
                            )
                          }
                          options={[
                            { value: "", label: "Not specified" },
                            { value: "employee", label: "Employee" },
                            { value: "contractor", label: "Contractor" },
                            { value: "consultant", label: "Consultant" },
                            { value: "manager", label: "Manager" },
                            { value: "executive", label: "Executive" },
                            { value: "owner", label: "Owner" },
                            { value: "other", label: "Other" },
                          ]}
                          size="sm"
                          loading={createContactMutation.isLoading}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          <FormSection title="Additional Information">
            <FormGrid cols={1} gap="default">
              <Textarea
                label="Notes"
                name="notes"
                value={formData.notes || ""}
                onChange={handleChange}
                rows={3}
                loading={createContactMutation.isLoading}
              />
            </FormGrid>
          </FormSection>

          {/* Error message */}
          {createContactMutation.isError && (
            <div className="text-red-600 dark:text-red-400 text-sm">
              {getRetryableErrorMessage(createContactMutation.error, 'create contact')}
            </div>
          )}

          {/* Form actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={createContactMutation.isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              loading={createContactMutation.isLoading}
              loadingText="Creating..."
              disabled={createContactMutation.isLoading}
            >
              Create Contact
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactForm;
