import React, { useState } from "react"; import { DealEstimate } from "../../../types/crm-types"; import { useQuery, useMutation, useQueryClient } from "react-query"; import { linkDealEstimate } from "../../../api/crm"; import { getDraftEstimates } from "../../../api/estimates"; import { format, parseISO } from "date-fns"; import EstimateLinkModal from "./EstimateLinkModal"; interface DealEstimatesSectionProps { dealId: string; estimates: DealEstimate[]; dealName?: string; dealCompanyName?: string; } // Filter to only show draft estimates const filterDraftEstimates = (estimates: DealEstimate[]) => estimates.filter((e) => e.type === "internal"); /** * Component for managing estimates linked to a deal */ const DealEstimatesSection: React.FC<DealEstimatesSectionProps> = ({ dealId, estimates, dealName, dealCompanyName, }) => { const [isModalOpen, setIsModalOpen] = useState(false); const queryClient = useQueryClient(); // Fetch draft estimates (for displaying details of linked estimates) const { data: draftEstimates = [] } = useQuery( "draftEstimates", getDraftEstimates, { onSuccess: (data) => { console.log("DealEstimatesSection - Fetched draft estimates:", data); }, } ); // Mutation for linking an estimate to a deal (only internal estimates) const linkEstimateMutation = useMutation( (params: { estimateId: string; estimateType: "internal" }) => linkDealEstimate(dealId, params.estimateId, params.estimateType), { onSuccess: (response) => { console.log( "DealEstimatesSection - Link estimate success response:", response ); // If the response includes the updated deal, use it if (response && response.deal) { console.log("Setting deal data from response:", response.deal); queryClient.setQueryData(["deal", dealId], response.deal); } // Force refetch of deal data to get updated fields queryClient.invalidateQueries(["deal", dealId], { refetchActive: true, }); queryClient.invalidateQueries(["deal-estimates", dealId]); queryClient.invalidateQueries(["dealFieldOwnership", dealId]); // Also invalidate the entire deals list queryClient.invalidateQueries("deals"); // IMPORTANT: Also invalidate the batch estimates query used by EnhancedDealBoard queryClient.invalidateQueries(["all-deal-estimates-batch"]); setIsModalOpen(false); // Force a hard refresh of the deal data after a short delay setTimeout(() => { console.log("Force refetching deal data..."); queryClient.refetchQueries(["deal", dealId]); }, 500); }, } ); // Unlinking is no longer supported - linking is permanent // Handle linking an estimate (only internal estimates) const handleLinkEstimate = (estimateId: string, type: "internal") => { linkEstimateMutation.mutate({ estimateId, estimateType: type }); }; // Unlinking is no longer supported // Open modal for linking an estimate const openEstimateModal = () => { setIsModalOpen(true); }; // Format date to Australian format (DD/MM/YYYY) const formatDate = (dateString: string): string => { try { return format(parseISO(dateString), "dd/MM/yyyy"); } catch (error) { return "Invalid date"; } }; // Get estimate details const getEstimateDetails = (estimateId: string) => { const estimate = draftEstimates.find((e) => e.uuid === estimateId); if (estimate) { return { name: estimate.projectName || "Unnamed Project", client: estimate.clientName, date: formatDate(estimate.updatedAt), status: estimate.status, }; } return { name: "Unknown Estimate", client: "Unknown Client", date: "Unknown Date", status: "unknown", }; }; return ( <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden"> <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700"> <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white"> Linked Estimates </h3> <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400"> Estimates associated with this deal. </p> </div> <div className="px-4 py-5 sm:p-6"> {/* Action buttons */} <div className="mb-6 flex flex-col sm:flex-row gap-3"> <button type="button" onClick={openEstimateModal} className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200" > <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /> </svg> Link Estimate </button> </div> {/* Show info about estimate-controlled fields if any estimates are linked */} {filterDraftEstimates(estimates).length > 0 && ( <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md"> <h4 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2"> Estimate-Controlled Fields </h4> <p className="text-xs text-yellow-700 dark:text-yellow-300"> The following deal fields are managed by the linked estimate and cannot be edited directly: Value, Start Date, End Date, Invoice Frequency, and Payment Terms. </p> <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-2 font-medium"> Note: Once linked, estimates cannot be unlinked from deals to maintain data integrity. </p> </div> )} {/* Linked estimates list */} {filterDraftEstimates(estimates).length === 0 ? ( <div className="text-sm text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-6 text-center"> <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mx-auto mb-3 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /> </svg> <p>No estimates linked to this deal yet.</p> <p className="mt-2 text-xs text-gray-400 dark:text-gray-500"> Use the button above to link a draft estimate. </p> </div> ) : ( <div className="space-y-6"> {/* Draft Estimates */} <div> <div className="flex items-center mb-4"> <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /> </svg> <h4 className="text-sm font-medium text-gray-900 dark:text-white"> Draft Estimates </h4> </div> {estimates.filter((estimate) => estimate.type === "internal") .length === 0 ? ( <div className="py-4 text-sm text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md p-3 text-center"> No draft estimates linked. </div> ) : ( <div className="grid gap-4 sm:grid-cols-1 lg:grid-cols-2"> {filterDraftEstimates(estimates).map((estimate) => { const details = getEstimateDetails(estimate.id); return ( <div key={estimate.id} className="btn-modern--primary"dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-lg overflow-hidden hover:shadow-md transition-all duration-200" > <div className="border-b border-blue-100 dark:border-blue-800 bg-blue-100/50 dark:bg-blue-800/30 px-4 py-2 flex justify-between items-center"> <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate"> {details.name} </h5> <span className="text-xs px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200"> {details.status} </span> </div> <div className="p-4"> <div className="grid grid-cols-2 gap-2 text-xs mb-3"> <div> <p className="text-gray-500 dark:text-gray-400"> Client </p> <p className="font-medium text-gray-900 dark:text-white"> {details.client} </p> </div> <div> <p className="text-gray-500 dark:text-gray-400"> Updated </p> <p className="font-medium text-gray-900 dark:text-white"> {details.date} </p> </div> <div> <p className="text-gray-500 dark:text-gray-400"> Linked </p> <p className="font-medium text-gray-900 dark:text-white"> {estimate.linkedAt ? formatDate(estimate.linkedAt) : "Unknown"} </p> </div> </div> <div className="mt-3"> <a href={`/estimates/${estimate.id}`} target="_blank" rel="noopener noreferrer" className="inline-flex items-center justify-center w-full px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-200 dark: transition-colors duration-200" > <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /> </svg> View Estimate </a> </div> </div> </div> ); })} </div> )} </div> </div> )} </div> {/* Estimate Link Modal */} <EstimateLinkModal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} estimateType="internal" draftEstimates={draftEstimates} onLinkEstimate={handleLinkEstimate} isLoading={linkEstimateMutation.isLoading} dealName={dealName} dealCompanyName={dealCompanyName} /> </div> ); }; export default DealEstimatesSection; 