import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useOutletContext, useNavigate } from 'react-router-dom';
import { 
  ExclamationTriangleIcon, 
  ChartBarIcon, 
  WrenchScrewdriverIcon,
  PlusIcon 
} from '@heroicons/react/24/outline';
import { getDeals, updateDeal, getDealEstimates, linkDealEstimate, getBatchDealEstimates } from '../../../api/crm';
import { DealColumn } from './DealColumn';
import { DealCalendarView } from './DealCalendarView';
import type { Deal, DealEstimate } from '../../../types/crm-types';
import EstimateLinkModal from '../DealEdit/EstimateLinkModal';
import { getDraftEstimates } from '../../../api/estimates';
import SimpleLinkedEstimatesModal from './SimpleLinkedEstimatesModal';
import { useEvents, EVENTS } from '../../../contexts/EventContext';

type ViewMode = 'board' | 'table' | 'calendar' | 'forecast';
type GroupBy = 'none' | 'owner' | 'company' | 'priority';

interface CRMContext {
  openEntity: (type: 'deal' | 'contact' | 'company', id: string) => void;
}

const DEAL_STAGES = [
  { id: 'Identified', name: 'Identified', color: 'bg-gray-500' },
  { id: 'Qualified', name: 'Qualified', color: 'bg-blue-500' },
  { id: 'Solution proposal', name: 'Solution Proposal', color: 'bg-blue-500' },
  { id: 'Solution presentation', name: 'Solution Presentation', color: 'bg-purple-500' },
  { id: 'Objection handling', name: 'Objection Handling', color: 'bg-yellow-500' },
  { id: 'Finalising terms', name: 'Finalising Terms', color: 'bg-orange-500' },
  { id: 'Closed won', name: 'Closed Won', color: 'bg-green-500' },
  { id: 'Closed lost', name: 'Closed Lost', color: 'bg-red-500' },
  { id: 'Abandoned', name: 'Abandoned', color: 'bg-gray-400' }
];

const QUICK_FILTERS = [
  { id: 'all', label: 'All Deals', filter: () => true },
  { id: 'my-deals', label: 'My Deals', filter: (d: Deal) => d?.owner === 'current-user' },
  { id: 'high-value', label: 'High Value (>$199k)', filter: (d: Deal) => (d?.value || 0) > 199000 },
  { id: 'closing-soon', label: 'Closing This Month', filter: (d: Deal) => {
    if (!d?.expectedCloseDate) return false;
    const closeDate = new Date(d.expectedCloseDate);
    const now = new Date();
    return closeDate.getMonth() === now.getMonth() && closeDate.getFullYear() === now.getFullYear();
  }},
  { id: 'at-risk', label: 'At Risk', filter: (d: Deal) => false }
];

export const EnhancedDealBoard: React.FC = () => {
  const { openEntity } = useOutletContext<CRMContext>();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { publish, subscribe, unsubscribe } = useEvents();
  
  // View state
  const [viewMode, setViewMode] = useState<ViewMode>('board');
  const [groupBy, setGroupBy] = useState<GroupBy>('none');
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [compactView, setCompactView] = useState(false);

  // Estimate linking state
  const [estimateLinkModalOpen, setEstimateLinkModalOpen] = useState<{
    isOpen: boolean;
    dealId: string;
  }>({ isOpen: false, dealId: '' });

  const [linkedEstimateModalOpen, setLinkedEstimateModalOpen] = useState<{
    isOpen: boolean;
    dealId: string;
  }>({ isOpen: false, dealId: '' });

  // Store estimates for each deal
  const [dealEstimatesMap, setDealEstimatesMap] = useState<Record<string, DealEstimate[]>>({});
  const [estimatesLoading, setEstimatesLoading] = useState<Record<string, boolean>>({});

  // Fetch deals from the database
  const { data: deals = [], isLoading, error } = useQuery(
    'deals', 
    getDeals,
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      onError: (err) => {
        console.error('Failed to fetch deals:', err);
      },
      onSuccess: async (fetchedDeals) => {
        // Batch fetch estimates for all deals when deals are loaded
        if (fetchedDeals.length > 0) {
          try {
            const dealIds = fetchedDeals.map(deal => deal.id).filter(Boolean);
            console.log(`Fetching estimates for ${dealIds.length} deals...`);
            const batchEstimates = await getBatchDealEstimates(dealIds);
            setDealEstimatesMap(batchEstimates);
            console.log(`Successfully fetched estimates for ${Object.keys(batchEstimates).length} deals`);
          } catch (error) {
            console.error('Failed to batch fetch estimates:', error);
          }
        }
      }
    }
  );

  // Fetch draft estimates for the modal
  const { data: draftEstimates = [] } = useQuery(
    "draftEstimates",
    getDraftEstimates
  );

  // Set up a query to watch for estimate changes using batch API
  // This ensures the UI updates immediately when estimates are linked/unlinked
  useQuery(
    ['all-deal-estimates-batch', deals.map(d => d.id).join(',')],
    async () => {
      if (deals.length === 0) return {};
      
      // Use batch API to fetch all estimates (handles chunking automatically)
      try {
        const dealIds = deals.map(deal => deal.id).filter(Boolean);
        const batchEstimates = await getBatchDealEstimates(dealIds);
        return batchEstimates;
      } catch (error) {
        console.error('Failed to batch fetch estimates:', error);
        return {};
      }
    },
    {
      enabled: deals.length > 0,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      onSuccess: (newEstimatesMap) => {
        if (newEstimatesMap) {
          setDealEstimatesMap(newEstimatesMap);
        }
      },
      onError: (error) => {
        console.error('Error in batch estimates query:', error);
      }
    }
  );

  // Subscribe to deal estimates updates from other parts of the app
  useEffect(() => {
    const handleDealEstimatesUpdate = (data: unknown) => {
      const { dealId } = data as { dealId: string };
      if (dealId) {
        // Refresh estimates for the specific deal
        fetchDealEstimates(dealId);
        // Also invalidate React Query cache
        queryClient.invalidateQueries(['deal-estimates', dealId]);
      }
    };

    subscribe(EVENTS.DEAL_ESTIMATES_UPDATED, handleDealEstimatesUpdate);

    return () => {
      unsubscribe(EVENTS.DEAL_ESTIMATES_UPDATED, handleDealEstimatesUpdate);
    };
  }, [subscribe, unsubscribe, queryClient]);

  // Filter and search deals
  const filteredDeals = useMemo(() => {
    const filter = QUICK_FILTERS.find(f => f.id === activeFilter)?.filter || (() => true);
    
    return deals.filter(deal => {
      const matchesFilter = filter(deal);
      const matchesSearch = searchTerm ? 
        deal?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        deal?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) : true;
      
      return matchesFilter && matchesSearch;
    });
  }, [deals, activeFilter, searchTerm]);

  // Group deals by stage
  const dealsByStage = useMemo(() => {
    const grouped: Record<string, Deal[]> = {};
    DEAL_STAGES.forEach(stage => {
      grouped[stage.id] = filteredDeals.filter(deal => deal?.stage === stage.id);
    });
    return grouped;
  }, [filteredDeals]);

  // Calculate metrics
  const metrics = useMemo(() => {
    const totalValue = filteredDeals.reduce((sum, deal) => sum + (deal.value || 0), 0);
    const weightedValue = filteredDeals.reduce((sum, deal) => 
      sum + ((deal.value || 0) * (deal.probability || 0)), 0
    );
    const avgDealSize = filteredDeals.length > 0 ? totalValue / filteredDeals.length : 0;
    const winRate = filteredDeals.length > 0 
      ? (filteredDeals.filter(d => d.stage === 'Closed won').length / filteredDeals.length) * 100 
      : 0;

    return { totalValue, weightedValue, avgDealSize, winRate };
  }, [filteredDeals]);

  // Update deal mutation
  const updateDealMutation = useMutation(
    ({ id, updates }: { id: string; updates: Partial<Deal> }) => updateDeal(id, updates),
    {
      onSuccess: (updatedDeal, { id }) => {
        // Optimistic update instead of full invalidation
        queryClient.setQueryData('deals', (oldDeals: Deal[] | undefined) => {
          if (!oldDeals) return oldDeals;
          return oldDeals.map(deal => 
            deal.id === id ? { ...deal, ...updatedDeal } : deal
          );
        });
        // Only invalidate the specific deal query
        queryClient.invalidateQueries(['deal', id]);
      }
    }
  );

  // Fetch estimates for a specific deal
  const fetchDealEstimates = useCallback(async (dealId: string) => {
    // Prevent duplicate fetches using a ref to check loading state
    setEstimatesLoading(prev => {
      if (prev[dealId]) return prev; // Already loading
      return { ...prev, [dealId]: true };
    });
    
    try {
      const estimates = await getDealEstimates(dealId);
      setDealEstimatesMap(prev => ({
        ...prev,
        [dealId]: estimates
      }));
      return estimates;
    } catch (error) {
      console.error(`Failed to fetch estimates for deal ${dealId}:`, error);
      return [];
    } finally {
      setEstimatesLoading(prev => ({ ...prev, [dealId]: false }));
    }
  }, []);

  // Link estimate mutation
  const linkEstimateMutation = useMutation(
    ({ dealId, estimateId, estimateType }: { dealId: string; estimateId: string; estimateType: 'internal' }) =>
      linkDealEstimate(dealId, estimateId, estimateType),
    {
      onSuccess: (result, variables) => {
        if (result.success) {
          // Close the modal on success
          setEstimateLinkModalOpen({ isOpen: false, dealId: '' });
          
          // If the response includes the updated deal, use it to update the cache
          if (result.deal) {
            queryClient.setQueryData('deals', (oldDeals: Deal[] | undefined) => {
              if (!oldDeals) return oldDeals;
              return oldDeals.map(deal => 
                deal.id === variables.dealId ? { ...deal, ...result.deal } : deal
              );
            });
          }
          
          // Immediately update the local state with the new estimate
          // This provides instant feedback to the user
          fetchDealEstimates(variables.dealId).then(() => {
            // Publish event to notify other components
            publish(EVENTS.DEAL_ESTIMATES_UPDATED, { dealId: variables.dealId });
            
            // After fetching, also invalidate queries to ensure consistency
            queryClient.invalidateQueries(['deal', variables.dealId]);
            queryClient.invalidateQueries(['deal-estimates', variables.dealId]);
            queryClient.invalidateQueries(['dealFieldOwnership', variables.dealId]);
            queryClient.invalidateQueries(['all-deal-estimates-batch']);
            
            // Invalidate the deals list to get updated values
            queryClient.invalidateQueries('deals');
          });
        } else {
          // Show error message
          console.error('Failed to link estimate:', result.error);
          // You might want to show a toast or error message to the user here
        }
      },
      onError: (error) => {
        console.error('Error linking estimate:', error);
        // You might want to show a toast or error message to the user here
      }
    }
  );

  // Unlinking is no longer supported - linking is permanent

  // Handle deal movement
  const handleMoveDeal = useCallback((dealId: string, newStage: string) => {
    updateDealMutation.mutate({ id: dealId, updates: { stage: newStage } });
  }, [updateDealMutation]);

  // Handle deal click
  const handleDealClick = useCallback((deal: Deal) => {
    console.log('Deal clicked:', deal.id, deal.name);
    openEntity('deal', deal.id);
  }, [openEntity]);

  // Handle estimate linking
  const handleLinkEstimate = useCallback((dealId: string) => {
    setEstimateLinkModalOpen({ isOpen: true, dealId });
  }, []);

  // Handle view linked estimates
  const handleViewLinkedEstimates = useCallback((dealId: string) => {
    setLinkedEstimateModalOpen({ isOpen: true, dealId });
  }, []);

  return (
    <div className="min-h-[calc(100vh-200px)] flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Board Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div className="max-w-full md:max-w-[92rem] mx-auto px-4 md:px-6 py-4">
          {/* Top Row: Title + Actions */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 mb-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Deal Pipeline</h2>
              <div className="flex flex-wrap items-center gap-2 md:gap-4 mt-1 text-xs md:text-sm text-gray-600 dark:text-gray-400">
                <span>{filteredDeals.length} deals</span>
                <span className="hidden sm:inline">•</span>
                <span className="hidden sm:inline">${metrics.totalValue.toLocaleString()} total</span>
                <span className="hidden md:inline">•</span>
                <span className="text-purple-600 dark:text-purple-400 hidden md:inline">${metrics.weightedValue.toLocaleString()} weighted</span>
                {metrics.winRate > 0 && (
                  <>
                    <span className="hidden lg:inline">•</span>
                    <span className="text-green-600 dark:text-green-400 hidden lg:inline">{metrics.winRate.toFixed(0)}% win rate</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3 w-full md:w-auto overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">

              {/* View Options */}
              <div className="flex items-center gap-1 px-2 py-1.5 bg-gray-100 dark:bg-gray-700 rounded-lg flex-shrink-0">
                <button
                  onClick={() => setCompactView(!compactView)}
                  className={`p-1.5 rounded transition-colors ${
                    compactView ? 'bg-white dark:bg-gray-600 shadow-sm' : ''
                  }`}
                  title="Compact view"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
                <div className="w-px h-4 bg-gray-300 dark:bg-gray-600 mx-1" />
                {['board', 'table', 'calendar', 'forecast'].map(mode => (
                  <button
                    key={mode}
                    onClick={() => setViewMode(mode as ViewMode)}
                    className={`px-3 py-1 text-sm rounded transition-colors ${
                      viewMode === mode 
                        ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' 
                        : 'text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    {mode.charAt(0).toUpperCase() + mode.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Bottom Row: Filters + Search */}
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
            {/* Quick Filters */}
            <div className="flex gap-2 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none] pb-2 md:pb-0">
              {QUICK_FILTERS.map(filter => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-3 py-1.5 text-sm rounded-lg transition-colors flex-shrink-0 ${
                    activeFilter === filter.id
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>

            {/* Search */}
            <div className="w-full md:w-64">
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search deals..."
                  className="w-full pl-9 pr-3 py-1.5 text-sm bg-gray-100 dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
                <svg className="absolute left-3 top-2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Board Content */}
      <div className="flex-1 p-4 overflow-hidden">
        <div className="max-w-full md:max-w-[92rem] mx-auto">
          {/* HubSpot source of truth notice */}
          <div className="mb-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
            <p className="text-xs text-blue-700 dark:text-blue-300">
              <strong>Note:</strong> Deal names can only be updated in HubSpot, as it remains our source of truth for CRM data. 
              To update a deal name, make the change in HubSpot then run a quick sync on the <a href="/data-management" className="underline hover:text-blue-800 dark:hover:text-blue-200">data management page</a>.
            </p>
          </div>
          
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <ExclamationTriangleIcon className="w-16 h-16 mx-auto mb-4 text-red-500" />
              <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Error loading deals
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {error.message || 'Failed to fetch deals from the server'}
              </p>
            </div>
          </div>
        ) : deals.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center max-w-md">
              <ChartBarIcon className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No deals yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Get started by creating your first deal or importing from HubSpot
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={() => navigate('/crm/hubspot')}
                  className="px-4 md:px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center gap-2"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  Import from HubSpot
                </button>
              </div>
            </div>
          </div>
        ) : viewMode === 'board' ? (
          <DndProvider backend={HTML5Backend}>
            <div className="flex gap-4 overflow-x-auto -webkit-overflow-scrolling-touch pb-4 min-h-[600px]">
              {DEAL_STAGES.map(stage => (
                <DealColumn
                  key={stage.id}
                  stage={stage}
                  deals={dealsByStage[stage.id] || []}
                  onMoveDeal={handleMoveDeal}
                  onDealClick={handleDealClick}
                  compactView={compactView}
                  dealEstimatesMap={dealEstimatesMap}
                  onLinkEstimate={handleLinkEstimate}
                  onViewLinkedEstimates={handleViewLinkedEstimates}
                />
              ))}
            </div>
          </DndProvider>
        ) : viewMode === 'calendar' ? (
          <DealCalendarView 
            deals={filteredDeals} 
            onDealClick={handleDealClick}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <WrenchScrewdriverIcon className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium">{viewMode.charAt(0).toUpperCase() + viewMode.slice(1)} view coming soon!</p>
              <p className="text-sm mt-2">Switch back to board view to see your deals</p>
            </div>
          </div>
        )}
        </div>
      </div>


      {/* Estimate Link Modal */}
      <EstimateLinkModal
        isOpen={estimateLinkModalOpen.isOpen}
        onClose={() => {
          setEstimateLinkModalOpen({ isOpen: false, dealId: '' });
          // Refresh estimates for the deal after linking
          if (estimateLinkModalOpen.dealId) {
            fetchDealEstimates(estimateLinkModalOpen.dealId);
          }
        }}
        estimateType="internal"
        draftEstimates={draftEstimates}
        onLinkEstimate={(estimateId, estimateType) => {
          linkEstimateMutation.mutate({
            dealId: estimateLinkModalOpen.dealId,
            estimateId,
            estimateType
          });
        }}
        isLoading={linkEstimateMutation.isLoading}
      />

      {/* Linked Estimates Modal */}
      <SimpleLinkedEstimatesModal
        isOpen={linkedEstimateModalOpen.isOpen}
        onClose={() => {
          setLinkedEstimateModalOpen({ isOpen: false, dealId: '' });
          // Refresh estimates for the deal after unlinking
          if (linkedEstimateModalOpen.dealId) {
            fetchDealEstimates(linkedEstimateModalOpen.dealId);
          }
        }}
        dealId={linkedEstimateModalOpen.dealId}
        estimates={dealEstimatesMap[linkedEstimateModalOpen.dealId] || []}
      />
    </div>
  );
};

export default EnhancedDealBoard;