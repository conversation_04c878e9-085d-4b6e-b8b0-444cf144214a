# Test environment configuration for real-data tests
# Copy this file to .env.test and fill in the values

# Xero API credentials - use a test account
XERO_CLIENT_ID=your_test_account_client_id
XERO_CLIENT_SECRET=your_test_account_client_secret
XERO_REDIRECT_URI=http://localhost:3002/api/xero/callback
XERO_SCOPES=openid profile email accounting.transactions accounting.reports.read accounting.settings offline_access payroll.employees payroll.payruns

# Harvest API credentials - use a test account
HARVEST_ACCESS_TOKEN=your_test_account_token
HARVEST_ACCOUNT_ID=your_test_account_id

# Environment
NODE_ENV=test
API_PORT=3002
FRONTEND_URL=http://localhost:5173
