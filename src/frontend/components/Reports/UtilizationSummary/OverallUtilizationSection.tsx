import React from "react";
import { getUtilizationColor } from "./utils/utilizationHelpers";

interface OverallUtilizationSectionProps {
  overallUtilization: number;
  excludeLeave: boolean;
}

/**
 * Component for displaying overall utilization
 * Redesigned for more efficient space usage
 */
const OverallUtilizationSection: React.FC<OverallUtilizationSectionProps> = ({
  overallUtilization,
  excludeLeave,
}) => {
  return (
    <div className="mt-3 bg-gray-50 dark:bg-gray-750 rounded-lg p-3">
      <div className="flex justify-between items-center mb-1">
        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
          Overall utilisation{" "}
          {excludeLeave && (
            <span className="text-xs text-gray-400">(leave excluded)</span>
          )}
        </span>
        <span className="text-xl font-bold text-gray-900 dark:text-white">
          {overallUtilization}%
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700">
        <div
          className={`h-3 rounded-full ${getUtilizationColor(
            overallUtilization
          )}`}
          style={{
            width: `${Math.min(100, overallUtilization)}%`,
          }}
        ></div>
      </div>
    </div>
  );
};

export default OverallUtilizationSection;
