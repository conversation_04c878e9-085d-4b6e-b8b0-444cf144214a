# Data Enrichment System

## Overview

The Data Enrichment System enhances company and contact records with additional information from external data sources. This system provides a unified framework for integrating with various data providers to automatically augment business entities with valuable context and insights.

## Features

### Multi-Source Data Integration

- **ABN Lookup**: Australian Business Number validation and company information retrieval
- **Extensible architecture**: Easy addition of new enrichment sources
- **Confidence scoring**: Quality assessment of enriched data
- **Expiration management**: Automatic re-enrichment scheduling

### Data Quality Management

- **Confidence scores**: 0.0 to 1.0 rating for data reliability
- **Source tracking**: Clear attribution of data origins
- **Versioning**: Historical tracking of enrichment attempts
- **Error handling**: Comprehensive logging of failed enrichments

### Performance Optimization

- **Caching**: Intelligent caching with configurable TTL
- **Rate limiting**: Respect external API limitations
- **Batch processing**: Efficient bulk enrichment operations
- **Retry logic**: Automatic retry with exponential backoff

## Technical Implementation

### Architecture

The enrichment system follows a service-oriented architecture:

```
EnrichmentService (orchestrator)
├── ABNLookupService (source implementation)
├── EnrichmentRepository (data persistence)
└── External APIs (data sources)
```

### Database Schema

Three main tables support the enrichment system:

#### Company Enrichment
```sql
company_enrichment (
  id TEXT PRIMARY KEY,
  company_id TEXT NOT NULL,
  source TEXT NOT NULL,
  source_id TEXT,
  data JSON NOT NULL,
  confidence_score REAL DEFAULT 1.0,
  enriched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP,
  created_by TEXT DEFAULT 'system'
)
```

#### Contact Enrichment
```sql
contact_enrichment (
  id TEXT PRIMARY KEY,
  contact_id TEXT NOT NULL,
  source TEXT NOT NULL,
  source_id TEXT,
  data JSON NOT NULL,
  confidence_score REAL DEFAULT 1.0,
  enriched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP,
  created_by TEXT DEFAULT 'system'
)
```

#### Enrichment Log
```sql
enrichment_log (
  id TEXT PRIMARY KEY,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  source TEXT NOT NULL,
  status TEXT NOT NULL,
  error_message TEXT,
  attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  response_time_ms INTEGER,
  api_credits_used INTEGER DEFAULT 0
)
```

### Key Files

- **Core Service**: `src/api/services/enrichment/enrichment-service.ts`
- **ABN Service**: `src/api/services/enrichment/abn-lookup-service.ts`
- **Repository**: `src/api/repositories/enrichment-repository.ts`
- **API Routes**: `src/api/routes/enrichment.ts`
- **Migration**: `migrations/005_data_enrichment.sql`

## Current Data Sources

### ABN Lookup (Australian Business Number)

Provides comprehensive Australian business information:

- **Business name and trading names**
- **ABN status and registration date**
- **Business structure** (company, partnership, trust, etc.)
- **GST registration status**
- **Physical and postal addresses**
- **Industry classifications** (ANZSIC codes)

#### Usage Example

```typescript
import { ABNLookupService } from '../services/enrichment/abn-lookup-service';

const abnService = new ABNLookupService();
const result = await abnService.enrichByABN('***********');

if (result.success) {
  console.log('Business Name:', result.data.businessName);
  console.log('Status:', result.data.status);
  console.log('Industry:', result.data.industry);
}
```

## API Endpoints

### Enrich Company

```
POST /api/enrichment/companies/:companyId/enrich
```

Request body:
```json
{
  "sources": ["abn_lookup"],
  "options": {
    "force": false,
    "searchTerms": {
      "abn": "***********",
      "businessName": "Example Company Pty Ltd"
    }
  }
}
```

Response:
```json
{
  "success": true,
  "data": {
    "enrichments": [
      {
        "source": "abn_lookup",
        "status": "success",
        "confidence": 0.95,
        "data": {
          "businessName": "Example Company Pty Ltd",
          "abn": "***********",
          "status": "Active",
          "industry": "Software Development"
        }
      }
    ]
  }
}
```

### Get Company Enrichments

```
GET /api/enrichment/companies/:companyId
```

Query parameters:
- `source`: Filter by specific source (optional)
- `includeExpired`: Include expired enrichments (default: false)

### Bulk Enrichment

```
POST /api/enrichment/bulk
```

Request body:
```json
{
  "entityType": "company",
  "entityIds": ["company-1", "company-2", "company-3"],
  "sources": ["abn_lookup"],
  "options": {
    "force": false,
    "maxConcurrent": 5
  }
}
```

## Data Sources Integration

### Adding a New Source

1. **Create service class**:
```typescript
// src/api/services/enrichment/new-source-service.ts
export class NewSourceService extends BaseEnrichmentService {
  async enrich(entity: Company, options?: any): Promise<EnrichmentResult> {
    // Implement enrichment logic
    const externalData = await this.fetchFromAPI(entity);
    
    return {
      success: true,
      source: 'new_source',
      confidence: 0.8,
      data: externalData
    };
  }
}
```

2. **Register in main service**:
```typescript
// src/api/services/enrichment/enrichment-service.ts
import { NewSourceService } from './new-source-service';

const sources = {
  'abn_lookup': new ABNLookupService(),
  'new_source': new NewSourceService()
};
```

3. **Add configuration**:
```typescript
// Environment variables for API credentials
NEW_SOURCE_API_KEY=your_api_key
NEW_SOURCE_BASE_URL=https://api.newsource.com
```

### Source Requirements

Each enrichment source must implement:

- **Error handling**: Graceful failure with meaningful error messages
- **Rate limiting**: Respect API quotas and implement backoff
- **Data validation**: Validate and sanitize incoming data
- **Confidence scoring**: Provide quality assessment
- **Caching support**: Work with the repository caching layer

## Configuration

### Environment Variables

```bash
# ABN Lookup API (Australian Business Register)
ABN_LOOKUP_API_KEY=your_abr_api_key
ABN_LOOKUP_BASE_URL=https://abr.business.gov.au

# General enrichment settings
ENRICHMENT_DEFAULT_TTL=7776000  # 90 days in seconds
ENRICHMENT_MAX_RETRIES=3
ENRICHMENT_RETRY_DELAY=1000     # milliseconds
```

### Service Configuration

```typescript
const enrichmentConfig = {
  defaultTTL: 90 * 24 * 60 * 60,  // 90 days
  maxRetries: 3,
  retryDelay: 1000,
  maxConcurrent: 10,
  sources: {
    'abn_lookup': {
      enabled: true,
      priority: 1,
      confidenceThreshold: 0.7
    }
  }
};
```

## Usage Patterns

### Automatic Enrichment

Set up automatic enrichment for new companies:

```typescript
// On company creation
const companyId = await companyRepo.create(companyData);
await enrichmentService.enrichCompany(companyId, ['abn_lookup']);
```

### Manual Enrichment

Trigger enrichment from the UI:

```typescript
// User-initiated enrichment
const result = await enrichmentService.enrichCompany(
  companyId, 
  ['abn_lookup'], 
  { force: true }
);
```

### Batch Processing

Enrich multiple entities efficiently:

```typescript
// Bulk enrichment for data migration
const companyIds = await companyRepo.getUnenrichedCompanies();
await enrichmentService.bulkEnrich('company', companyIds, ['abn_lookup']);
```

## Monitoring and Analytics

### Enrichment Metrics

Track enrichment performance:

- **Success rate** by source
- **Response times** and API performance
- **Data quality** scores over time
- **API credit usage** and costs

### Logging

Comprehensive logging includes:

- **Enrichment attempts** with outcomes
- **API response times** and error rates
- **Data quality** assessments
- **Cost tracking** for paid APIs

### Health Checks

Monitor enrichment system health:

```typescript
GET /api/enrichment/health
```

Response includes:
- Source availability
- Recent error rates
- API quota usage
- Performance metrics

## Best Practices

### Data Quality

1. **Validate input data** before enrichment attempts
2. **Set appropriate confidence thresholds** for data acceptance
3. **Implement data reconciliation** for conflicting sources
4. **Regular quality audits** of enriched data

### Performance

1. **Batch operations** when possible
2. **Implement proper caching** with reasonable TTLs
3. **Use rate limiting** to avoid API penalties
4. **Monitor and optimize** API response times

### Cost Management

1. **Cache aggressively** to minimize API calls
2. **Implement smart retry logic** to avoid wasted credits
3. **Monitor usage patterns** and optimize accordingly
4. **Set up alerts** for unusual usage spikes

## Future Enhancements

### Planned Sources

- **Clearbit**: Company and contact enrichment
- **Apollo**: Sales intelligence data
- **LinkedIn**: Professional network information
- **Industry databases**: Specialized sector data

### Advanced Features

- **Machine learning** for data quality scoring
- **Real-time enrichment** as data changes
- **Predictive enrichment** based on usage patterns
- **Cross-source data fusion** for comprehensive profiles

## Troubleshooting

### Common Issues

1. **Rate limiting errors**: Reduce batch size or increase delays
2. **Low confidence scores**: Review data matching logic
3. **Expired enrichments**: Check TTL settings and refresh policies
4. **API failures**: Verify credentials and endpoint availability

### Debug Mode

Enable detailed logging:

```bash
DEBUG_ENRICHMENT=true npm run dev
```

This provides:
- Detailed API request/response logs
- Enrichment decision reasoning
- Performance timing information
- Data quality assessment details

## See Also

- [Knowledge Graph Visualization](knowledge-graph-visualization.md)
- [CRM Directory Enhancement](crm-directory-enhancements.md)
- [Database Schema Documentation](../technical/data-model/README.md)