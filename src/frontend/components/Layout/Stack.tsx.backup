import React from 'react';

interface StackProps {
  children: React.ReactNode;
  gap?: string;
  direction?: 'row' | 'column';
  alignItems?: string;
  justifyContent?: string;
  className?: string;
}

/**
 * A layout component that arranges children in a vertical or horizontal stack
 * with a specified gap between them. Uses Flexbox.
 */
export function Stack({
  children,
  gap = 'var(--space-md)', // Default gap using fluid spacing variable
  direction = 'column', // Default direction is vertical
  alignItems,
  justifyContent,
  className = ''
}: StackProps) {
  return (
    <div
      className={className}
      style={{
        display: 'flex',
        flexDirection: direction,
        gap, // Uses the provided gap value
        alignItems, // Optional alignment
        justifyContent // Optional justification
      }}
    >
      {children}
    </div>
  );
}
