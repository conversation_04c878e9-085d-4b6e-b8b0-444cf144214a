import React from 'react';
import { ClockIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { RecentSearch } from './types';

interface RecentSearchesProps {
  recentSearches: RecentSearch[];
  onSelectSearch: (query: string) => void;
  onClearHistory: () => void;
  isVisible: boolean;
}

export const RecentSearches: React.FC<RecentSearchesProps> = ({
  recentSearches,
  onSelectSearch,
  onClearHistory,
  isVisible
}) => {
  if (!isVisible || recentSearches.length === 0) return null;

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return 'just now';
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  return (
    <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
      <div className="p-2">
        <div className="flex items-center justify-between px-2 py-1 mb-1">
          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
            Recent Searches
          </span>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClearHistory();
            }}
            className="text-xs text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            Clear
          </button>
        </div>
        
        <div className="space-y-0.5">
          {recentSearches.map((search, index) => (
            <button
              key={index}
              onClick={() => onSelectSearch(search.query)}
              className="w-full flex items-center justify-between px-2 py-1.5 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <ClockIcon className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
                  {search.query}
                </span>
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400 ml-2">
                {search.resultCount !== undefined && (
                  <span>{search.resultCount} results</span>
                )}
                <span>{formatTimeAgo(search.timestamp)}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RecentSearches;