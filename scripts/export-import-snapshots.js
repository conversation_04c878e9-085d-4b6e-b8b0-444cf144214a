#!/usr/bin/env node

/**
 * Export/Import Cashflow Snapshots Script
 * 
 * This script allows you to export cashflow_snapshot data from one database
 * and import it into another. Perfect for preserving snapshots when recreating
 * the database.
 * 
 * Usage:
 *   Export: node export-import-snapshots.js export [output-file]
 *   Import: node export-import-snapshots.js import [input-file]
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get command line arguments
const [,, command, fileName] = process.argv;

// Validate command
if (!command || !['export', 'import'].includes(command)) {
  console.log('Usage:');
  console.log('  Export: node export-import-snapshots.js export [output-file]');
  console.log('  Import: node export-import-snapshots.js import [input-file]');
  process.exit(1);
}

// Determine database path based on environment
const isDevelopment = process.env.NODE_ENV !== 'production';
const dbPath = isDevelopment
  ? path.join(__dirname, '..', 'data', 'upstream.db')
  : '/data/upstream.db';

// Default file names
const defaultExportFile = `cashflow-snapshots-${new Date().toISOString().split('T')[0]}.json`;
const defaultImportFile = fileName || 'cashflow-snapshots.json';

console.log(`Database path: ${dbPath}`);

/**
 * Export snapshots from database
 */
function exportSnapshots() {
  const outputFile = fileName || defaultExportFile;
  
  try {
    // Connect to database
    const db = new BetterSqlite3(dbPath, { readonly: true });
    
    // Check if table exists
    const tableExists = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='cashflow_snapshot'
    `).get();
    
    if (!tableExists) {
      console.log('No cashflow_snapshot table found. Nothing to export.');
      db.close();
      return;
    }
    
    // Get all snapshots
    const snapshots = db.prepare(`
      SELECT * FROM cashflow_snapshot
      ORDER BY created_at DESC
    `).all();
    
    console.log(`Found ${snapshots.length} snapshots to export`);
    
    if (snapshots.length === 0) {
      console.log('No snapshots to export.');
      db.close();
      return;
    }
    
    // Parse JSON data for better readability
    const exportData = {
      exportDate: new Date().toISOString(),
      snapshotCount: snapshots.length,
      snapshots: snapshots.map(snapshot => ({
        ...snapshot,
        snapshot_data: JSON.parse(snapshot.snapshot_data)
      }))
    };
    
    // Write to file
    fs.writeFileSync(outputFile, JSON.stringify(exportData, null, 2));
    
    console.log(`✅ Exported ${snapshots.length} snapshots to ${outputFile}`);
    
    // Show summary
    const dates = [...new Set(snapshots.map(s => s.date))].sort();
    console.log(`\nSnapshot dates: ${dates[0]} to ${dates[dates.length - 1]}`);
    console.log(`Tenant IDs: ${[...new Set(snapshots.map(s => s.tenant_id))].join(', ')}`);
    
    db.close();
  } catch (error) {
    console.error('Error exporting snapshots:', error);
    process.exit(1);
  }
}

/**
 * Import snapshots to database
 */
function importSnapshots() {
  const inputFile = fileName || defaultImportFile;
  
  if (!fs.existsSync(inputFile)) {
    console.error(`File not found: ${inputFile}`);
    process.exit(1);
  }
  
  try {
    // Read export file
    const exportData = JSON.parse(fs.readFileSync(inputFile, 'utf8'));
    
    if (!exportData.snapshots || !Array.isArray(exportData.snapshots)) {
      console.error('Invalid export file format');
      process.exit(1);
    }
    
    console.log(`Found ${exportData.snapshots.length} snapshots to import`);
    console.log(`Export date: ${exportData.exportDate}`);
    
    // Connect to database
    const db = new BetterSqlite3(dbPath);
    
    // Check if table exists
    const tableExists = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='cashflow_snapshot'
    `).get();
    
    if (!tableExists) {
      console.error('cashflow_snapshot table does not exist in target database');
      console.error('Please ensure the database is properly initialized first');
      db.close();
      process.exit(1);
    }
    
    // Begin transaction
    db.prepare('BEGIN TRANSACTION').run();
    
    // Prepare insert statement
    const insert = db.prepare(`
      INSERT OR REPLACE INTO cashflow_snapshot (
        id, date, tenant_id, days_ahead, snapshot_data, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    let imported = 0;
    let skipped = 0;
    
    // Import each snapshot
    for (const snapshot of exportData.snapshots) {
      try {
        // Convert snapshot_data back to JSON string if it's an object
        const snapshotData = typeof snapshot.snapshot_data === 'string' 
          ? snapshot.snapshot_data 
          : JSON.stringify(snapshot.snapshot_data);
        
        insert.run(
          snapshot.id,
          snapshot.date,
          snapshot.tenant_id,
          snapshot.days_ahead,
          snapshotData,
          snapshot.created_at,
          snapshot.created_by
        );
        imported++;
      } catch (error) {
        console.warn(`Failed to import snapshot ${snapshot.id}: ${error.message}`);
        skipped++;
      }
    }
    
    // Commit transaction
    db.prepare('COMMIT').run();
    
    console.log(`\n✅ Import complete!`);
    console.log(`   Imported: ${imported} snapshots`);
    console.log(`   Skipped: ${skipped} snapshots`);
    
    db.close();
  } catch (error) {
    console.error('Error importing snapshots:', error);
    process.exit(1);
  }
}

// Execute command
if (command === 'export') {
  exportSnapshots();
} else if (command === 'import') {
  importSnapshots();
}