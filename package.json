{"name": "onbord-financial-dashboard-preview", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "nodemon", "dev:frontend": "vite --port 5173", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "echo 'No integration tests configured yet'", "test:real-data": "jest --config=tests/real-data-config.js", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:mock": "USE_MOCK_AUTH=true jest", "dev:mock": "USE_MOCK_AUTH=true npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:backend": "tsc --project tsconfig.backend.json --outDir dist --noEmitOnError false || echo 'TypeScript compilation failed, will use ts-node fallback'", "build:frontend": "vite build", "lint": "eslint src --ext ts,tsx", "migrate": "node scripts/simple-migrate.js", "migrate:status": "node scripts/simple-migrate.js status", "validate-schema": "node scripts/validate-schema.js", "test:schema": "jest tests/unit/database/schema-validation.test.ts", "start": "node server.js", "kill-ports": "bash scripts/kill-ports.sh", "preinstall": "npm config set legacy-peer-deps true", "dev:audit-dark-mode": "node scripts/dark-mode-audit.js", "dev:audit-dark-mode-all": "node scripts/dark-mode-audit.js src"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@heroicons/react": "^2.2.0", "@hubspot/api-client": "^12.1.0", "@types/xml2js": "^0.4.14", "axios": "^1.6.7", "better-sqlite3": "^11.10.0", "blueimp-md5": "^2.19.0", "cmdk": "^1.1.1", "connect-redis": "^8.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.3.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.511.0", "node-fetch": "^2.7.0", "react": "^18.2.0", "react-datepicker": "^4.25.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-force-graph-2d": "^1.27.1", "react-query": "^3.39.3", "react-router-dom": "^7.5.3", "recharts": "^2.12.1", "redis": "^4.7.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0", "xero-node": "^10.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@playwright/mcp": "^0.0.28", "@playwright/test": "^1.41.2", "@tailwindcss/container-queries": "^0.1.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/better-sqlite3": "^7.6.12", "@types/blueimp-md5": "^2.18.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.22", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.12", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-datepicker": "^4.19.5", "@types/socket.io": "^3.0.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^8.56.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nodemon": "^3.1.9", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5"}}