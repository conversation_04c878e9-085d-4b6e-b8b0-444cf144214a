import React, { useState } from "react";
import { Note } from "../../../types/crm-types";
import { Card } from "../../shared/Card";
import DealNotesSection from "./DealNotesSection";
import { ConversationThread } from "../Conversations";
import { DocumentTextIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

interface DealNotesCardProps {
  dealId: string;
  notes: Note[];
}

/**
 * Card wrapper for the DealNotesSection component with conversation threading support
 */
const DealNotesCard: React.FC<DealNotesCardProps> = ({ dealId, notes }) => {
  const [viewMode, setViewMode] = useState<'notes' | 'conversations'>('notes');

  return (
    <Card className="overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
              Notes & Conversations
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
              {viewMode === 'notes' 
                ? 'Simple notes and comments about this deal.'
                : 'Threaded conversations with participants and status tracking.'}
            </p>
          </div>
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setViewMode('notes')}
              className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
                viewMode === 'notes'
                  ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <DocumentTextIcon className="w-4 h-4" />
              Notes
            </button>
            <button
              onClick={() => setViewMode('conversations')}
              className={`flex items-center gap-2 px-3 py-1.5 text-sm rounded-md transition-colors ${
                viewMode === 'conversations'
                  ? 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <ChatBubbleLeftRightIcon className="w-4 h-4" />
              Conversations
            </button>
          </div>
        </div>
      </div>
      <div className="px-4 py-5 sm:p-6">
        {viewMode === 'notes' ? (
          <DealNotesSection dealId={dealId} notes={notes} />
        ) : (
          <ConversationThread 
            entityType="deal" 
            entityId={dealId}
            currentUserId="system" // TODO: Get from user context
          />
        )}
      </div>
    </Card>
  );
};

export default DealNotesCard;
