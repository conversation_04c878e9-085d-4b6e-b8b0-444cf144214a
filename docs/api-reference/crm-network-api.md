# CRM Network & Intelligence API

## Overview

The CRM Network API provides endpoints for relationship management, opportunity intelligence, and team coverage analysis. These endpoints power the advanced CRM features including opportunity detection, project history tracking, network visualization, and HubSpot notes/associations management.

## Base URL

All endpoints are prefixed with `/api/crm/network`

## Authentication

All endpoints require valid authentication. See the main API documentation for authentication details.

## Endpoints

### Opportunity Intelligence

#### Get Company Opportunities

**Endpoint**: `GET /api/crm/network/opportunities/:companyId`

Analyzes a company and returns AI-generated business opportunities including coverage risks, project renewals, and expansion possibilities.

**Parameters:**
- `companyId` (path, required): The unique identifier of the company

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "coverage-gap-123",
      "companyId": "123",
      "type": "risk",
      "title": "High Coverage Risk",
      "description": "Only 1 out of 4 key roles have team coverage. Consider assigning team members to uncovered roles.",
      "score": 85,
      "metadata": {
        "coverageGap": 75,
        "uncoveredRoles": 3
      },
      "detectedAt": "2024-01-15T10:30:00Z"
    },
    {
      "id": "project-ending-456",
      "companyId": "123",
      "type": "renewal",
      "title": "Project Ending Soon",
      "description": "Project \"Website Redesign\" is ending on 2024-02-01. Good time to discuss renewal or follow-up work.",
      "score": 75,
      "metadata": {
        "projectEndDate": "2024-02-01",
        "projectName": "Website Redesign",
        "projectBudget": 25000
      },
      "detectedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

**Opportunity Types:**
- `risk`: Coverage gaps or engagement risks
- `renewal`: Project endings requiring renewal discussions
- `expansion`: Network-based expansion opportunities
- `engagement`: Relationship maintenance needs

**Error Responses:**
- `404`: Company not found
- `500`: Server error during analysis

---

### Project History

#### Get Contact Project History

**Endpoint**: `GET /api/crm/network/contacts/:id/project-history`

Retrieves detailed project history for a contact based on their Harvest time entries over the last 12 months.

**Parameters:**
- `id` (path, required): The unique identifier of the contact

**Response:**
```json
{
  "success": true,
  "data": {
    "contactId": "456",
    "harvestUserId": "789",
    "projects": [
      {
        "projectId": "101",
        "projectName": "Website Redesign",
        "clientName": "ACME Corp",
        "totalHours": 120.5,
        "firstEntry": "2023-08-01",
        "lastEntry": "2023-12-15",
        "tasks": ["Development", "Design", "Testing"],
        "entries": [
          {
            "date": "2023-12-15",
            "hours": 8.0,
            "task": "Development",
            "notes": "Completed user authentication module"
          }
        ]
      }
    ],
    "totalProjects": 3,
    "dateRange": {
      "from": "2023-01-15",
      "to": "2024-01-15"
    }
  }
}
```

**Notes:**
- Only returns data for contacts linked to Harvest users
- Returns empty projects array if contact has no `harvestUserId`
- Entries are sorted by date (most recent first)

---

### Relationship Network Management

#### Get Relationship Network

**Endpoint**: `GET /api/crm/network/relationships/:entityId`

Retrieves the relationship network for a contact or company with specified depth.

**Parameters:**
- `entityId` (path, required): The unique identifier of the contact or company
- `type` (query, optional): Entity type - "contact" or "company" (default: "contact")
- `depth` (query, optional): Network depth 1-3 (default: 1)

**Example**: `GET /api/crm/network/relationships/123?type=company&depth=2`

**Response:**
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "123",
        "type": "company",
        "name": "ACME Corp",
        "metadata": {}
      },
      {
        "id": "456",
        "type": "contact", 
        "name": "John Smith",
        "metadata": {
          "role": "CEO",
          "email": "<EMAIL>"
        }
      }
    ],
    "links": [
      {
        "source": "123",
        "target": "456",
        "type": "employs",
        "strength": 5,
        "context": "Primary contact"
      }
    ]
  }
}
```

#### Get Contact Relationships

**Endpoint**: `GET /api/crm/network/contacts/:id/relationships`

Retrieves all direct relationships for a specific contact.

**Parameters:**
- `id` (path, required): The unique identifier of the contact

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "rel_123",
      "sourceContactId": "456",
      "targetContactId": "789",
      "relationshipType": "colleague",
      "strength": 4,
      "context": "Worked together on Project Alpha",
      "createdAt": "2024-01-01T00:00:00Z",
      "createdBy": "<EMAIL>"
    }
  ]
}
```

#### Create Contact Relationship

**Endpoint**: `POST /api/crm/network/contacts/relationships`

Creates a new relationship between two contacts.

**Request Body:**
```json
{
  "sourceContactId": "456",
  "targetContactId": "789",
  "relationshipType": "colleague",
  "strength": 4,
  "context": "Worked together on Project Alpha"
}
```

**Relationship Types:**
- `knows`: General acquaintance
- `reports_to`: Hierarchical reporting relationship
- `introduced_by`: Referral or introduction relationship
- `worked_with`: Professional collaboration
- `colleague`: Same organization

**Strength Scale:** 1-5 (1 = weak, 5 = strong)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "rel_124",
    "sourceContactId": "456",
    "targetContactId": "789",
    "relationshipType": "colleague",
    "strength": 4,
    "context": "Worked together on Project Alpha",
    "createdAt": "2024-01-15T10:30:00Z",
    "createdBy": "<EMAIL>"
  }
}
```

#### Update Contact Relationship

**Endpoint**: `PUT /api/crm/network/contacts/relationships/:id`

Updates an existing contact relationship.

**Parameters:**
- `id` (path, required): The unique identifier of the relationship

**Request Body:**
```json
{
  "strength": 5,
  "context": "Updated collaboration context"
}
```

#### Delete Contact Relationship

**Endpoint**: `DELETE /api/crm/network/contacts/relationships/:id`

Deletes a contact relationship.

**Parameters:**
- `id` (path, required): The unique identifier of the relationship

---

### Team Coverage Analytics

#### Get Company Coverage

**Endpoint**: `GET /api/crm/network/team/coverage/:companyId`

Analyzes team coverage for a specific company.

**Parameters:**
- `companyId` (path, required): The unique identifier of the company

**Response:**
```json
{
  "success": true,
  "data": {
    "companyId": "123",
    "totalRoles": 4,
    "coveredRoles": 2,
    "coveragePercentage": 50,
    "riskLevel": "medium",
    "teamMembers": [
      {
        "teamMemberId": "tm_1",
        "contactsCovered": 2,
        "relationshipStrength": "primary"
      }
    ],
    "recommendations": [
      "Assign team member to Finance Director role",
      "Strengthen relationship with IT Manager"
    ]
  }
}
```

**Risk Levels:**
- `low`: >80% coverage
- `medium`: 50-80% coverage  
- `high`: <50% coverage

#### Get Stale Relationships

**Endpoint**: `GET /api/crm/network/team/stale-relationships`

Identifies relationships that haven't been touched recently.

**Parameters:**
- `days` (query, optional): Number of days threshold (default: 30)

**Example**: `GET /api/crm/network/team/stale-relationships?days=60`

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "contactId": "456",
      "contactName": "John Smith",
      "companyName": "ACME Corp",
      "lastInteraction": "2023-11-15",
      "daysSinceInteraction": 61,
      "relationshipStrength": "primary",
      "riskLevel": "high"
    }
  ]
}
```

#### Create/Update Team Coverage

**Endpoint**: `POST /api/crm/network/team/coverage`

Creates or updates team coverage information for a contact.

**Request Body:**
```json
{
  "contactId": "456",
  "teamMemberId": "tm_1",
  "relationshipStrength": "primary",
  "lastInteractionDate": "2024-01-15",
  "lastInteractionType": "meeting",
  "notes": "Quarterly business review meeting"
}
```

**Relationship Strengths:**
- `primary`: Main point of contact
- `secondary`: Secondary relationship
- `minimal`: Limited interaction

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "coverage_123",
    "contactId": "456",
    "teamMemberId": "tm_1",
    "relationshipStrength": "primary",
    "lastInteractionDate": "2024-01-15",
    "lastInteractionType": "meeting",
    "notes": "Quarterly business review meeting",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message",
  "message": "Detailed error description"
}
```

**Common HTTP Status Codes:**
- `200`: Success
- `201`: Created (for POST endpoints)
- `400`: Bad Request (validation errors)
- `404`: Not Found (entity doesn't exist)
- `500`: Internal Server Error

## Rate Limiting

API endpoints are subject to rate limiting to ensure system stability:
- **Default Limit**: 100 requests per minute per user
- **Burst Limit**: 20 requests per 10 seconds
- **Headers**: Rate limit status included in response headers

## Integration Notes

### Harvest Integration
- Project history endpoints require valid Harvest integration
- Contact must have `harvestUserId` to retrieve project data
- Time entries are filtered to last 12 months for performance

### External System Links
- Opportunity detection leverages HubSpot and Harvest data when available
- Team coverage analysis can incorporate external system roles
- Network visualization shows cross-system relationship mapping

### Performance Considerations
- Relationship network queries are limited to depth 3 for performance
- Large networks may be paginated or truncated
- Caching is implemented for frequently accessed endpoints

## Notes Management API

### Get Entity Notes

**Endpoint**: `GET /api/notes/:entityType/:entityId`

Retrieves all notes for a specific entity (company, contact, or deal).

**Parameters:**
- `entityType` (path, required): Type of entity (`company`, `contact`, `deal`)
- `entityId` (path, required): The unique identifier of the entity

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "note_123",
      "entityType": "company",
      "entityId": "123",
      "noteType": "meeting",
      "content": "Discussed Q4 project roadmap",
      "isPrivate": false,
      "createdBy": "HubSpot Import",
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### Create Note

**Endpoint**: `POST /api/notes`

Creates a new note for an entity.

**Request Body:**
```json
{
  "entityType": "deal",
  "entityId": "deal_456",
  "noteType": "general",
  "content": "Follow up on contract negotiations",
  "isPrivate": false
}
```

**Note Types:**
- `general`: General notes
- `meeting`: Meeting notes
- `call`: Phone call notes
- `email`: Email correspondence
- `task`: Task-related notes
- `reminder`: Reminders

## Association Management

### Get Entity Associations

**Endpoint**: `GET /api/associations/:entityType/:entityId`

Retrieves all associations for an entity.

**Parameters:**
- `entityType` (path, required): Type of entity (`contact`, `deal`)
- `entityId` (path, required): The unique identifier of the entity

**Response:**
```json
{
  "success": true,
  "data": {
    "companies": [
      {
        "companyId": "comp_123",
        "companyName": "ACME Corp",
        "role": "Employee",
        "isPrimary": true
      }
    ],
    "contacts": [
      {
        "contactId": "cont_456",
        "contactName": "John Smith",
        "role": "Decision Maker",
        "isPrimary": false
      }
    ]
  }
}
```

### HubSpot Notes Import

Notes and activities imported from HubSpot are automatically mapped to the internal note types and linked to the appropriate entities. The import process preserves:
- Original timestamps
- Content and subject lines
- Engagement type mapping
- Entity relationships