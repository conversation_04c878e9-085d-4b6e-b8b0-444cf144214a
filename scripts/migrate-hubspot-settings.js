#!/usr/bin/env node

/**
 * Migration script to update the hubspot_settings table schema
 * This script migrates from the old schema (key, value, updated_at) 
 * to the new schema (id, key, value, created_at, updated_at)
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Determine the database path
const dbPath = process.env.NODE_ENV === 'production' 
  ? '/data/upstream.db' 
  : path.join(__dirname, '..', 'data', 'upstream.db');

console.log(`Connecting to database at: ${dbPath}`);

// Initialize the database
const db = new BetterSqlite3(dbPath);

function migrateHubSpotSettingsTable() {
  try {
    console.log('Starting HubSpot settings table migration...');

    // Check if the table exists and get its current schema
    const tableInfo = db.prepare("PRAGMA table_info(hubspot_settings)").all();
    
    if (tableInfo.length === 0) {
      console.log('hubspot_settings table does not exist. Creating new table with correct schema...');
      
      // Create the table with the new schema
      db.prepare(`
        CREATE TABLE hubspot_settings (
          id TEXT PRIMARY KEY,
          key TEXT NOT NULL UNIQUE,
          value TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      `).run();
      
      console.log('hubspot_settings table created successfully with new schema.');
      return true;
    }

    // Check if the table already has the new schema
    const hasIdColumn = tableInfo.some(column => column.name === 'id');
    const hasCreatedAtColumn = tableInfo.some(column => column.name === 'created_at');
    
    if (hasIdColumn && hasCreatedAtColumn) {
      console.log('hubspot_settings table already has the correct schema. No migration needed.');
      return true;
    }

    console.log('hubspot_settings table exists with old schema. Migrating...');
    
    // Get existing data
    const existingData = db.prepare('SELECT * FROM hubspot_settings').all();
    console.log(`Found ${existingData.length} existing records to migrate.`);

    // Create a backup of the old table
    db.prepare('ALTER TABLE hubspot_settings RENAME TO hubspot_settings_backup').run();
    console.log('Created backup of existing table.');

    // Create the new table with the correct schema
    db.prepare(`
      CREATE TABLE hubspot_settings (
        id TEXT PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `).run();
    console.log('Created new table with correct schema.');

    // Migrate existing data
    const now = new Date().toISOString();
    const insertStmt = db.prepare(`
      INSERT INTO hubspot_settings (id, key, value, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?)
    `);

    for (const row of existingData) {
      const id = uuidv4();
      const createdAt = row.updated_at || now; // Use existing updated_at as created_at if available
      const updatedAt = row.updated_at || now;
      
      insertStmt.run(id, row.key, row.value, createdAt, updatedAt);
      console.log(`Migrated record: ${row.key}`);
    }

    console.log(`Successfully migrated ${existingData.length} records.`);

    // Drop the backup table (optional - comment out if you want to keep it)
    // db.prepare('DROP TABLE hubspot_settings_backup').run();
    // console.log('Dropped backup table.');

    console.log('HubSpot settings table migration completed successfully.');
    return true;

  } catch (error) {
    console.error('Error during HubSpot settings table migration:', error);
    
    // Try to restore from backup if it exists
    try {
      const backupExists = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='hubspot_settings_backup'
      `).get();
      
      if (backupExists) {
        console.log('Attempting to restore from backup...');
        db.prepare('DROP TABLE IF EXISTS hubspot_settings').run();
        db.prepare('ALTER TABLE hubspot_settings_backup RENAME TO hubspot_settings').run();
        console.log('Restored from backup successfully.');
      }
    } catch (restoreError) {
      console.error('Error restoring from backup:', restoreError);
    }
    
    return false;
  }
}

// Run the migration
try {
  const success = migrateHubSpotSettingsTable();
  
  if (success) {
    console.log('Migration completed successfully.');
    process.exit(0);
  } else {
    console.error('Migration failed.');
    process.exit(1);
  }
} catch (error) {
  console.error('Unexpected error during migration:', error);
  process.exit(1);
} finally {
  // Close the database connection
  db.close();
}
