/**
 * Repository Pattern Tests
 * 
 * These tests validate the repository pattern implementation, ensuring that:
 * 1. Repositories follow the correct interface and inheritance
 * 2. Basic CRUD operations work correctly
 * 3. Repository methods handle errors properly
 * 4. Foreign key relationships are maintained
 */

import BetterSqlite3 from 'better-sqlite3';
import { BaseRepository } from '../../../../src/api/repositories/base-repository';
import { CompanyRepository } from '../../../../src/api/repositories/company-repository';
import { ContactRepository } from '../../../../src/api/repositories/contact-repository';
import { DealRepository } from '../../../../src/api/repositories/deal-repository';
import { NoteRepository } from '../../../../src/api/repositories/note-repository';
import { ContactRoleRepository } from '../../../../src/api/repositories/relationships/contact-role-repository';
import { v4 as uuidv4 } from 'uuid';
import { initializeSchema } from '../../../../src/database/schema';

// Mock the database service
jest.mock('../../../../src/api/services/db-service', () => {
  // Create an in-memory database for testing
  const db = new BetterSqlite3(':memory:');
  
  // Initialize the schema
  initializeSchema(db);
  
  return db;
});

describe('Repository Pattern Implementation', () => {
  describe('BaseRepository', () => {
    class TestRepository extends BaseRepository {
      constructor() {
        super('test_table');
      }
      
      // Expose protected methods for testing
      public testGetAll() {
        return this.getAll();
      }
      
      public testGetById(id: string) {
        return this.getById(id);
      }
      
      public testTransaction<T>(callback: () => T): T {
        return this.transaction(callback);
      }
      
      public testColumnExists(columnName: string): boolean {
        return this.columnExists(columnName);
      }
      
      public testGetColumns(): string[] {
        return this.getColumns();
      }
    }
    
    let testRepo: TestRepository;
    
    beforeAll(() => {
      // Create a test table for the repository
      const db = require('../../../../src/api/services/db-service');
      db.exec(`
        CREATE TABLE IF NOT EXISTS test_table (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          value INTEGER,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          deleted_at TEXT
        )
      `);
      
      testRepo = new TestRepository();
    });
    
    it('should correctly identify existing columns', () => {
      expect(testRepo.testColumnExists('id')).toBe(true);
      expect(testRepo.testColumnExists('name')).toBe(true);
      expect(testRepo.testColumnExists('value')).toBe(true);
      expect(testRepo.testColumnExists('nonexistent')).toBe(false);
    });
    
    it('should return all columns in a table', () => {
      const columns = testRepo.testGetColumns();
      expect(columns).toContain('id');
      expect(columns).toContain('name');
      expect(columns).toContain('value');
      expect(columns).toContain('created_at');
      expect(columns).toContain('updated_at');
      expect(columns).toContain('deleted_at');
      expect(columns.length).toBe(6);
    });
    
    it('should execute transactions properly', () => {
      const result = testRepo.testTransaction(() => {
        return 'success';
      });
      
      expect(result).toBe('success');
      
      // Test transaction rollback on error
      expect(() => {
        testRepo.testTransaction(() => {
          throw new Error('Test error');
        });
      }).toThrow('Test error');
    });
  });
  
  describe('Entity Repositories', () => {
    let companyRepo: CompanyRepository;
    let contactRepo: ContactRepository;
    let dealRepo: DealRepository;
    let noteRepo: NoteRepository;
    let contactRoleRepo: ContactRoleRepository;
    
    // Test data
    let companyId: string;
    let contactId: string;
    let dealId: string;
    
    beforeAll(() => {
      companyRepo = new CompanyRepository();
      contactRepo = new ContactRepository();
      dealRepo = new DealRepository();
      noteRepo = new NoteRepository();
      contactRoleRepo = new ContactRoleRepository();
      
      // Create test data
      companyId = uuidv4();
      contactId = uuidv4();
      dealId = uuidv4();
      
      // Seed the database with test data
      const db = require('../../../../src/api/services/db-service');
      
      // Create a test company
      db.prepare(`
        INSERT INTO company (
          id, name, industry, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        companyId,
        'Test Company',
        'Technology',
        new Date().toISOString(),
        new Date().toISOString(),
        'system',
        'system'
      );
      
      // Create a test contact
      db.prepare(`
        INSERT INTO contact (
          id, first_name, last_name, email, company_id, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        contactId,
        'John',
        'Doe',
        '<EMAIL>',
        companyId,
        new Date().toISOString(),
        new Date().toISOString(),
        'system',
        'system'
      );
      
      // Create a test deal
      db.prepare(`
        INSERT INTO deal (
          id, name, stage, value, probability, created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        dealId,
        'Test Deal',
        'discovery',
        10000,
        0.5,
        new Date().toISOString(),
        new Date().toISOString(),
        'system',
        'system'
      );
    });
    
    describe('CompanyRepository', () => {
      it('should retrieve a company by ID', () => {
        const company = companyRepo.getCompanyById(companyId);
        expect(company).toBeDefined();
        expect(company?.id).toBe(companyId);
        expect(company?.name).toBe('Test Company');
        expect(company?.industry).toBe('Technology');
      });
      
      it('should return null for non-existent company ID', () => {
        const company = companyRepo.getCompanyById('nonexistent-id');
        expect(company).toBeNull();
      });
      
      it('should create a new company', () => {
        const newCompany = {
          name: 'New Company',
          industry: 'Finance',
        };
        
        const created = companyRepo.createCompany(newCompany, 'test-user');
        expect(created).toBeDefined();
        expect(created.id).toBeDefined();
        expect(created.name).toBe('New Company');
        expect(created.industry).toBe('Finance');
        expect(created.createdBy).toBe('test-user');
        
        // Verify the company was saved to the database
        const retrieved = companyRepo.getCompanyById(created.id);
        expect(retrieved).toBeDefined();
        expect(retrieved?.name).toBe('New Company');
      });
      
      it('should update an existing company', () => {
        const updates = {
          name: 'Updated Company',
          website: 'https://example.com',
        };
        
        const updated = companyRepo.updateCompany(companyId, updates, 'test-user');
        expect(updated).toBeDefined();
        expect(updated.id).toBe(companyId);
        expect(updated.name).toBe('Updated Company');
        expect(updated.website).toBe('https://example.com');
        expect(updated.industry).toBe('Technology'); // Should retain original value
        expect(updated.updatedBy).toBe('test-user');
        
        // Verify the company was updated in the database
        const retrieved = companyRepo.getCompanyById(companyId);
        expect(retrieved?.name).toBe('Updated Company');
        expect(retrieved?.website).toBe('https://example.com');
      });
      
      it('should soft delete a company', () => {
        const result = companyRepo.deleteCompany(companyId, 'test-user');
        expect(result).toBe(true);
        
        // Verify the company was soft deleted
        const retrieved = companyRepo.getCompanyById(companyId);
        expect(retrieved?.deletedAt).toBeDefined();
      });
    });
    
    describe('ContactRepository', () => {
      it('should retrieve a contact by ID', () => {
        const contact = contactRepo.getContactById(contactId);
        expect(contact).toBeDefined();
        expect(contact?.id).toBe(contactId);
        expect(contact?.firstName).toBe('John');
        expect(contact?.lastName).toBe('Doe');
        expect(contact?.email).toBe('<EMAIL>');
        expect(contact?.companyId).toBe(companyId);
      });
      
      it('should return null for non-existent contact ID', () => {
        const contact = contactRepo.getContactById('nonexistent-id');
        expect(contact).toBeNull();
      });
      
      it('should create a new contact', () => {
        const newContact = {
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          companyId: companyId
        };
        
        const created = contactRepo.createContact(newContact, 'test-user');
        expect(created).toBeDefined();
        expect(created.id).toBeDefined();
        expect(created.firstName).toBe('Jane');
        expect(created.lastName).toBe('Smith');
        expect(created.email).toBe('<EMAIL>');
        expect(created.companyId).toBe(companyId);
        expect(created.createdBy).toBe('test-user');
        
        // Verify the contact was saved to the database
        const retrieved = contactRepo.getContactById(created.id);
        expect(retrieved).toBeDefined();
        expect(retrieved?.firstName).toBe('Jane');
      });
      
      it('should update an existing contact', () => {
        const updates = {
          firstName: 'Johnny',
          jobTitle: 'Developer'
        };
        
        const updated = contactRepo.updateContact(contactId, updates, 'test-user');
        expect(updated).toBeDefined();
        expect(updated.id).toBe(contactId);
        expect(updated.firstName).toBe('Johnny');
        expect(updated.lastName).toBe('Doe'); // Should retain original value
        expect(updated.jobTitle).toBe('Developer');
        expect(updated.updatedBy).toBe('test-user');
        
        // Verify the contact was updated in the database
        const retrieved = contactRepo.getContactById(contactId);
        expect(retrieved?.firstName).toBe('Johnny');
        expect(retrieved?.jobTitle).toBe('Developer');
      });
      
      it('should get contacts by company ID', () => {
        const contacts = contactRepo.getContactsByCompanyId(companyId);
        expect(contacts).toBeDefined();
        expect(contacts.length).toBeGreaterThanOrEqual(1);
        expect(contacts[0].companyId).toBe(companyId);
      });
    });
    
    describe('DealRepository', () => {
      it('should retrieve a deal by ID', () => {
        const deal = dealRepo.getDealById(dealId);
        expect(deal).toBeDefined();
        expect(deal?.id).toBe(dealId);
        expect(deal?.name).toBe('Test Deal');
        expect(deal?.stage).toBe('discovery');
        expect(deal?.value).toBe(10000);
        expect(deal?.probability).toBe(0.5);
      });
      
      it('should return null for non-existent deal ID', () => {
        const deal = dealRepo.getDealById('nonexistent-id');
        expect(deal).toBeNull();
      });
      
      it('should create a new deal', () => {
        const newDeal = {
          name: 'New Deal',
          stage: 'proposal',
          value: 20000,
          probability: 0.7,
          companyId: companyId
        };
        
        const created = dealRepo.createDeal(newDeal, 'test-user');
        expect(created).toBeDefined();
        expect(created.id).toBeDefined();
        expect(created.name).toBe('New Deal');
        expect(created.stage).toBe('proposal');
        expect(created.value).toBe(20000);
        expect(created.probability).toBe(0.7);
        expect(created.createdBy).toBe('test-user');
        
        // Verify the deal was saved to the database
        const retrieved = dealRepo.getDealById(created.id);
        expect(retrieved).toBeDefined();
        expect(retrieved?.name).toBe('New Deal');
      });
      
      it('should update an existing deal', () => {
        const updates = {
          name: 'Updated Deal',
          value: 15000
        };
        
        const updated = dealRepo.updateDeal(dealId, updates, 'test-user');
        expect(updated).toBeDefined();
        expect(updated.id).toBe(dealId);
        expect(updated.name).toBe('Updated Deal');
        expect(updated.value).toBe(15000);
        expect(updated.stage).toBe('discovery'); // Should retain original value
        expect(updated.updatedBy).toBe('test-user');
        
        // Verify the deal was updated in the database
        const retrieved = dealRepo.getDealById(dealId);
        expect(retrieved?.name).toBe('Updated Deal');
        expect(retrieved?.value).toBe(15000);
      });
      
      it('should get deals by stage', () => {
        const deals = dealRepo.getDealsByStage('discovery');
        expect(deals).toBeDefined();
        expect(deals.length).toBeGreaterThanOrEqual(1);
        expect(deals[0].stage).toBe('discovery');
      });
    });
    
    describe('ContactRoleRepository', () => {
      it('should add a contact to a deal with a role', () => {
        const role = 'decision_maker';
        const relationship = contactRoleRepo.addContactToDeal(dealId, contactId, role, 'test-user');
        
        expect(relationship).toBeDefined();
        expect(relationship.deal_id).toBe(dealId);
        expect(relationship.contact_id).toBe(contactId);
        expect(relationship.role).toBe(role);
        expect(relationship.created_by).toBe('test-user');
      });
      
      it('should update a contact\'s role in a deal', () => {
        const newRole = 'influencer';
        const relationship = contactRoleRepo.updateContactRole(dealId, contactId, newRole);
        
        expect(relationship).toBeDefined();
        expect(relationship.deal_id).toBe(dealId);
        expect(relationship.contact_id).toBe(contactId);
        expect(relationship.role).toBe(newRole);
      });
      
      it('should get all contacts for a deal', () => {
        const contacts = contactRoleRepo.getDealContacts(dealId);
        
        expect(contacts).toBeDefined();
        expect(contacts.length).toBeGreaterThanOrEqual(1);
        expect(contacts[0].id).toBe(contactId);
        expect(contacts[0].role).toBe('influencer'); // Updated role from previous test
      });
      
      it('should get contacts with a specific role in a deal', () => {
        const contacts = contactRoleRepo.getDealContactsByRole(dealId, 'influencer');
        
        expect(contacts).toBeDefined();
        expect(contacts.length).toBe(1);
        expect(contacts[0].id).toBe(contactId);
        expect(contacts[0].role).toBe('influencer');
      });
      
      it('should get all deals for a contact', () => {
        const deals = contactRoleRepo.getContactDeals(contactId);
        
        expect(deals).toBeDefined();
        expect(deals.length).toBeGreaterThanOrEqual(1);
        expect(deals[0].id).toBe(dealId);
        expect(deals[0].role).toBe('influencer');
      });
      
      it('should check if a contact is associated with a deal', () => {
        const isInDeal = contactRoleRepo.isContactInDeal(dealId, contactId);
        expect(isInDeal).toBe(true);
        
        const isNotInDeal = contactRoleRepo.isContactInDeal(dealId, 'nonexistent-id');
        expect(isNotInDeal).toBe(false);
      });
      
      it('should remove a contact from a deal', () => {
        const removed = contactRoleRepo.removeContactFromDeal(dealId, contactId);
        expect(removed).toBe(true);
        
        // Verify the contact was removed
        const isInDeal = contactRoleRepo.isContactInDeal(dealId, contactId);
        expect(isInDeal).toBe(false);
      });
    });
    
    describe('NoteRepository', () => {
      let noteId: string;
      
      it('should create a note for a deal', () => {
        const note = {
          dealId: dealId,
          content: 'Test note content',
        };
        
        const created = noteRepo.createNote(note, 'test-user');
        noteId = created.id;
        
        expect(created).toBeDefined();
        expect(created.id).toBeDefined();
        expect(created.dealId).toBe(dealId);
        expect(created.content).toBe('Test note content');
        expect(created.createdBy).toBe('test-user');
        expect(created.createdAt).toBeDefined();
      });
      
      it('should get all notes for a deal', () => {
        const notes = noteRepo.getNotesByDealId(dealId);
        
        expect(notes).toBeDefined();
        expect(notes.length).toBeGreaterThanOrEqual(1);
        expect(notes[0].dealId).toBe(dealId);
        expect(notes[0].content).toBe('Test note content');
      });
      
      it('should get a note by ID', () => {
        const note = noteRepo.getNoteById(noteId);
        
        expect(note).toBeDefined();
        expect(note?.id).toBe(noteId);
        expect(note?.dealId).toBe(dealId);
        expect(note?.content).toBe('Test note content');
      });
      
      it('should delete a note', () => {
        const deleted = noteRepo.deleteNote(noteId);
        expect(deleted).toBe(true);
        
        // Verify the note was deleted
        const note = noteRepo.getNoteById(noteId);
        expect(note).toBeNull();
      });
    });
  });
});