/**
 * Repository for managing field ownership tracking
 */

import { v4 as uuidv4 } from 'uuid';
import { BaseRepository } from './base-repository';

/**
 * Interface for field ownership entity
 */
export interface FieldOwnership {
  id: string;
  entityType: string;
  entityId: string;
  fieldName: string;
  owner: string;
  setAt: string;
  setBy?: string;
  deletedAt?: string;
}

/**
 * Interface for creating a new field ownership record
 */
export interface FieldOwnershipCreate {
  entityType: string;
  entityId: string;
  fieldName: string;
  owner: string;
  setBy?: string;
}

/**
 * Interface for updating field ownership
 */
export interface FieldOwnershipUpdate {
  owner?: string;
  setBy?: string;
}

/**
 * Repository for managing field ownership tracking
 */
export class FieldOwnershipRepository extends BaseRepository {
  /**
   * Constructor
   */
  constructor() {
    super('field_ownership');
  }

  /**
   * Get all field ownership records for an entity
   * @param entityType Entity type (e.g., 'deal', 'contact', 'company')
   * @param entityId Entity ID
   * @returns Array of field ownership records
   */
  getFieldOwnershipByEntity(entityType: string, entityId: string): FieldOwnership[] {
    try {
      const ownership = this.db.prepare(`
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          owner,
          set_at as setAt,
          set_by as setBy
        FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ?
        ORDER BY field_name
      `).all(entityType, entityId) as FieldOwnership[];

      return ownership;
    } catch (error) {
      console.error(`Error fetching field ownership for ${entityType} ${entityId}:`, error);
      return [];
    }
  }

  /**
   * Get field ownership for a specific field
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param fieldName Field name
   * @returns Field ownership record or null if not found
   */
  getFieldOwnership(entityType: string, entityId: string, fieldName: string): FieldOwnership | null {
    try {
      const ownership = this.db.prepare(`
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          owner,
          set_at as setAt,
          set_by as setBy
        FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ? AND field_name = ?
      `).get(entityType, entityId, fieldName) as FieldOwnership | undefined;

      return ownership || null;
    } catch (error) {
      console.error(`Error fetching field ownership for ${entityType} ${entityId} field ${fieldName}:`, error);
      return null;
    }
  }

  /**
   * Get all field ownership records by owner
   * @param owner Owner name (e.g., 'HubSpot', 'Harvest', 'Manual')
   * @returns Array of field ownership records
   */
  getFieldOwnershipByOwner(owner: string): FieldOwnership[] {
    try {
      const ownership = this.db.prepare(`
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          owner,
          set_at as setAt,
          set_by as setBy
        FROM ${this.tableName}
        WHERE owner = ?
        ORDER BY entity_type, entity_id, field_name
      `).all(owner) as FieldOwnership[];

      return ownership;
    } catch (error) {
      console.error(`Error fetching field ownership by owner ${owner}:`, error);
      return [];
    }
  }

  /**
   * Get all field ownership records by entity type
   * @param entityType Entity type
   * @returns Array of field ownership records
   */
  getFieldOwnershipByEntityType(entityType: string): FieldOwnership[] {
    try {
      const ownership = this.db.prepare(`
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          owner,
          set_at as setAt,
          set_by as setBy
        FROM ${this.tableName}
        WHERE entity_type = ?
        ORDER BY entity_id, field_name
      `).all(entityType) as FieldOwnership[];

      return ownership;
    } catch (error) {
      console.error(`Error fetching field ownership by entity type ${entityType}:`, error);
      return [];
    }
  }

  /**
   * Set field ownership (create if doesn't exist, update if exists)
   * @param ownershipData Field ownership data
   * @returns Field ownership record
   */
  setFieldOwnership(ownershipData: FieldOwnershipCreate): FieldOwnership {
    try {
      const existing = this.getFieldOwnership(
        ownershipData.entityType,
        ownershipData.entityId,
        ownershipData.fieldName
      );

      if (existing) {
        return this.updateFieldOwnership(existing.id, {
          owner: ownershipData.owner,
          setBy: ownershipData.setBy
        }) as FieldOwnership;
      } else {
        return this.createFieldOwnership(ownershipData);
      }
    } catch (error) {
      console.error('Error setting field ownership:', error);
      throw error;
    }
  }

  /**
   * Create a new field ownership record
   * @param ownershipData Field ownership data
   * @returns Created field ownership record
   */
  createFieldOwnership(ownershipData: FieldOwnershipCreate): FieldOwnership {
    try {
      // Check if ownership for this field already exists
      const existing = this.getFieldOwnership(
        ownershipData.entityType,
        ownershipData.entityId,
        ownershipData.fieldName
      );
      if (existing) {
        throw new Error(`Field ownership for ${ownershipData.entityType} ${ownershipData.entityId} field ${ownershipData.fieldName} already exists`);
      }

      const id = uuidv4();
      const now = new Date().toISOString();

      this.db.prepare(`
        INSERT INTO ${this.tableName} (
          id,
          entity_type,
          entity_id,
          field_name,
          owner,
          set_at,
          set_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        ownershipData.entityType,
        ownershipData.entityId,
        ownershipData.fieldName,
        ownershipData.owner,
        now,
        ownershipData.setBy || null
      );

      return this.getFieldOwnershipById(id) as FieldOwnership;
    } catch (error) {
      console.error('Error creating field ownership:', error);
      throw error;
    }
  }

  /**
   * Update an existing field ownership record
   * @param id Field ownership ID
   * @param updateData Update data
   * @returns Updated field ownership record or null if not found
   */
  updateFieldOwnership(id: string, updateData: FieldOwnershipUpdate): FieldOwnership | null {
    try {
      const existing = this.getFieldOwnershipById(id);
      if (!existing) {
        return null;
      }

      const now = new Date().toISOString();

      this.db.prepare(`
        UPDATE ${this.tableName}
        SET
          owner = ?,
          set_at = ?,
          set_by = ?
        WHERE id = ?
      `).run(
        updateData.owner !== undefined ? updateData.owner : existing.owner,
        now,
        updateData.setBy !== undefined ? updateData.setBy : existing.setBy,
        id
      );

      return this.getFieldOwnershipById(id);
    } catch (error) {
      console.error(`Error updating field ownership ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a field ownership record
   * @param id Field ownership ID
   * @returns Boolean indicating success
   */
  deleteFieldOwnership(id: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE id = ?
      `).run(id);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting field ownership ${id}:`, error);
      return false;
    }
  }

  /**
   * Delete all field ownership records for an entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @returns Number of deleted records
   */
  deleteFieldOwnershipByEntity(entityType: string, entityId: string): number {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ?
      `).run(entityType, entityId);

      return result.changes || 0;
    } catch (error) {
      console.error(`Error deleting field ownership for ${entityType} ${entityId}:`, error);
      return 0;
    }
  }

  /**
   * Delete field ownership for a specific field
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param fieldName Field name
   * @returns Boolean indicating success
   */
  deleteFieldOwnershipByField(entityType: string, entityId: string, fieldName: string): boolean {
    try {
      const result = this.db.prepare(`
        DELETE FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ? AND field_name = ?
      `).run(entityType, entityId, fieldName);

      return (result.changes || 0) > 0;
    } catch (error) {
      console.error(`Error deleting field ownership for ${entityType} ${entityId} field ${fieldName}:`, error);
      return false;
    }
  }

  /**
   * Bulk set field ownership for multiple fields
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param fieldOwnership Object mapping field names to owners
   * @param setBy User who is setting the ownership
   * @returns Array of field ownership records
   */
  bulkSetFieldOwnership(
    entityType: string,
    entityId: string,
    fieldOwnership: Record<string, string>,
    setBy?: string
  ): FieldOwnership[] {
    try {
      const results: FieldOwnership[] = [];
      
      const transaction = this.db.transaction(() => {
        for (const [fieldName, owner] of Object.entries(fieldOwnership)) {
          const result = this.setFieldOwnership({
            entityType,
            entityId,
            fieldName,
            owner,
            setBy
          });
          results.push(result);
        }
      });

      transaction();
      return results;
    } catch (error) {
      console.error(`Error bulk setting field ownership for ${entityType} ${entityId}:`, error);
      throw error;
    }
  }

  /**
   * Get ownership statistics by owner
   * @returns Object with ownership statistics
   */
  getOwnershipStatistics(): Record<string, {
    count: number;
    entityTypes: string[];
    mostRecentUpdate: string | null;
  }> {
    try {
      const stats = this.db.prepare(`
        SELECT 
          owner,
          COUNT(*) as count,
          MAX(set_at) as mostRecentUpdate
        FROM ${this.tableName}
        GROUP BY owner
        ORDER BY count DESC
      `).all() as Array<{ owner: string; count: number; mostRecentUpdate: string }>;

      const result: Record<string, any> = {};

      for (const stat of stats) {
        // Get entity types for this owner
        const entityTypes = this.db.prepare(`
          SELECT DISTINCT entity_type
          FROM ${this.tableName}
          WHERE owner = ?
          ORDER BY entity_type
        `).all(stat.owner) as Array<{ entity_type: string }>;

        result[stat.owner] = {
          count: stat.count,
          entityTypes: entityTypes.map(et => et.entity_type),
          mostRecentUpdate: stat.mostRecentUpdate
        };
      }

      return result;
    } catch (error) {
      console.error('Error fetching ownership statistics:', error);
      return {};
    }
  }

  /**
   * Get field ownership conflicts (same field owned by multiple systems)
   * @returns Array of fields with ownership conflicts
   */
  getOwnershipConflicts(): Array<{
    entityType: string;
    entityId: string;
    fieldName: string;
    owners: string[];
  }> {
    try {
      // This is a conceptual method - in practice, the UNIQUE constraint
      // prevents actual conflicts, but this could be useful for analysis
      const conflicts = this.db.prepare(`
        SELECT 
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          GROUP_CONCAT(DISTINCT owner) as owners
        FROM ${this.tableName}
        GROUP BY entity_type, entity_id, field_name
        HAVING COUNT(DISTINCT owner) > 1
        ORDER BY entity_type, entity_id, field_name
      `).all() as Array<{
        entityType: string;
        entityId: string;
        fieldName: string;
        owners: string;
      }>;

      return conflicts.map(conflict => ({
        ...conflict,
        owners: conflict.owners.split(',')
      }));
    } catch (error) {
      console.error('Error fetching ownership conflicts:', error);
      return [];
    }
  }

  /**
   * Get field ownership by ID
   * @param id Field ownership ID
   * @returns Field ownership record or null if not found
   */
  private getFieldOwnershipById(id: string): FieldOwnership | null {
    try {
      const ownership = this.db.prepare(`
        SELECT 
          id,
          entity_type as entityType,
          entity_id as entityId,
          field_name as fieldName,
          owner,
          set_at as setAt,
          set_by as setBy
        FROM ${this.tableName}
        WHERE id = ?
      `).get(id) as FieldOwnership | undefined;

      return ownership || null;
    } catch (error) {
      console.error(`Error fetching field ownership by ID ${id}:`, error);
      return null;
    }
  }

  /**
   * Check if a field is owned by a specific owner
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param fieldName Field name
   * @param owner Owner to check
   * @returns Boolean indicating if field is owned by the specified owner
   */
  isFieldOwnedBy(entityType: string, entityId: string, fieldName: string, owner: string): boolean {
    try {
      const ownership = this.getFieldOwnership(entityType, entityId, fieldName);
      return ownership?.owner === owner;
    } catch (error) {
      console.error(`Error checking field ownership for ${entityType} ${entityId} field ${fieldName}:`, error);
      return false;
    }
  }

  /**
   * Get all fields owned by a specific owner for an entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param owner Owner name
   * @returns Array of field names owned by the specified owner
   */
  getFieldsOwnedBy(entityType: string, entityId: string, owner: string): string[] {
    try {
      const fields = this.db.prepare(`
        SELECT field_name
        FROM ${this.tableName}
        WHERE entity_type = ? AND entity_id = ? AND owner = ?
        ORDER BY field_name
      `).all(entityType, entityId, owner) as Array<{ field_name: string }>;

      return fields.map(f => f.field_name);
    } catch (error) {
      console.error(`Error fetching fields owned by ${owner} for ${entityType} ${entityId}:`, error);
      return [];
    }
  }
}