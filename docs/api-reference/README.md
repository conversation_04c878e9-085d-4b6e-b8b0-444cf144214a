# API Reference Documentation

This section contains comprehensive API documentation for both internal application APIs and external service integrations.

## Documentation Structure

### Internal API Documentation
- [`company-linking-api.md`](./company-linking-api.md) - Company linking and management API
- [`knowledge-graph-api.md`](./knowledge-graph-api.md) - Knowledge graph visualization API
- [`data-enrichment-api.md`](./data-enrichment-api.md) - Data enrichment and external data integration API
- [`crm-network-api.md`](./crm-network-api.md) - CRM network and relationship API
- [`deal-estimate-api.md`](./deal-estimate-api.md) - Deal-estimate relationship management API (with batch endpoints)

### External API Integrations
- [`external/harvest/`](./external/harvest/) - Harvest API documentation and integration guides
- [`external/xero/`](./xero/) - Xero API documentation and integration guides

## External API Integration Overview

Upstream integrates with multiple external APIs to provide a comprehensive financial management platform:

### 🌾 Harvest Integration
**Purpose:** Time tracking and project management data

**Key Functions:**
- Retrieve project data for financial forecasting
- Fetch invoice information for cashflow projections  
- Access time entries for staff utilization reports
- Create and manage project estimates
- Sync client and project data

**Documentation:** [`external/harvest/README.md`](./external/harvest/README.md)

### 💼 Xero Integration
**Purpose:** Accounting and financial data

**Key Functions:**
- Retrieve bank account balances and transactions
- Fetch invoice and bill data for cashflow analysis
- Access payroll information for cost calculations
- Generate comprehensive financial reports
- Sync accounting data for real-time insights

**Documentation:** [`xero/README.md`](./xero/README.md)

### 🏢 HubSpot Integration
**Purpose:** CRM data and company relationships

**Key Functions:**
- Import company, contact, and deal data
- Import notes/activities (emails, calls, meetings, tasks) from HubSpot engagements
- Import associations (contact-company and deal-contact relationships)
- Maintain real association relationships
- Sync CRM data with external field ownership tracking
- Support bidirectional data flow

**Documentation:** [`../integrations/hubspot/README.md`](../integrations/hubspot/README.md)

## Authentication & Security

### OAuth2 Implementation
All external APIs use OAuth2 for secure authentication:

- **Automatic token refresh** handling
- **Secure credential storage** with environment variables
- **Scope-based permissions** for minimal access requirements
- **Session-based authentication** for internal APIs

### Rate Limiting & Performance

Each API has specific rate limits that are actively managed:

| API | Rate Limit | Handling Strategy |
|-----|------------|-------------------|
| **Harvest** | 100 requests/15 seconds | Exponential backoff with queue management |
| **Xero** | 60 requests/minute | Request throttling with retry logic |
| **HubSpot** | 10,000 requests/day | Batch processing with intelligent caching |

### Error Handling

All API integrations include:
- **Graceful degradation** when APIs are unavailable
- **Partial data return** during rate limiting
- **Comprehensive error logging** for debugging
- **Circuit breaker patterns** for external service failures
- **Offline mode support** using cached data

## Development Guidelines

### API Integration Best Practices
1. **Always check API availability** before making requests
2. **Implement proper error handling** for all external calls
3. **Use caching strategically** to minimize API calls
4. **Respect rate limits** with proper throttling
5. **Monitor API usage** and performance metrics

### Testing External APIs
- **Unit tests** with mocked API responses
- **Integration tests** with real API endpoints (when possible)
- **Rate limit testing** to ensure graceful handling
- **Error scenario testing** for network failures

### Adding New API Integrations
1. Create documentation in appropriate `external/` subdirectory
2. Implement OAuth2 authentication flow
3. Add rate limiting and error handling
4. Include comprehensive logging
5. Create integration tests
6. Update this README with new integration details

For detailed implementation guides, see the individual API documentation in their respective directories.