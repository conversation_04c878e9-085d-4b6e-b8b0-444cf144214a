import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { LoadingContext, LoadingProvider } from '../../../../src/frontend/contexts/LoadingContext';

describe('LoadingContext', () => {
  it('should provide loading context to children', () => {
    const TestComponent = () => {
      const context = React.useContext(LoadingContext);
      return (
        <div>
          <span>Global Loading: {context.isGlobalLoading.toString()}</span>
          <span>Loading Count: {Object.keys(context.loadingStates).length}</span>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Global Loading: false')).toBeInTheDocument();
    expect(screen.getByText('Loading Count: 0')).toBeInTheDocument();
  });

  it('should set and clear loading states', () => {
    const TestComponent = () => {
      const { setLoading, isLoading } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Task Loading: {isLoading('task1').toString()}</span>
          <button onClick={() => setLoading('task1', true)}>Start Loading</button>
          <button onClick={() => setLoading('task1', false)}>Stop Loading</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Task Loading: false')).toBeInTheDocument();

    act(() => {
      screen.getByText('Start Loading').click();
    });

    expect(screen.getByText('Task Loading: true')).toBeInTheDocument();

    act(() => {
      screen.getByText('Stop Loading').click();
    });

    expect(screen.getByText('Task Loading: false')).toBeInTheDocument();
  });

  it('should handle multiple loading states', () => {
    const TestComponent = () => {
      const { setLoading, isLoading, loadingStates } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Task1: {isLoading('task1').toString()}</span>
          <span>Task2: {isLoading('task2').toString()}</span>
          <span>Active Count: {Object.values(loadingStates).filter(Boolean).length}</span>
          <button onClick={() => {
            setLoading('task1', true);
            setLoading('task2', true);
          }}>Start Both</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Active Count: 0')).toBeInTheDocument();

    act(() => {
      screen.getByText('Start Both').click();
    });

    expect(screen.getByText('Task1: true')).toBeInTheDocument();
    expect(screen.getByText('Task2: true')).toBeInTheDocument();
    expect(screen.getByText('Active Count: 2')).toBeInTheDocument();
  });

  it('should set global loading state', () => {
    const TestComponent = () => {
      const { setGlobalLoading, isGlobalLoading } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Global: {isGlobalLoading.toString()}</span>
          <button onClick={() => setGlobalLoading(true)}>Enable Global</button>
          <button onClick={() => setGlobalLoading(false)}>Disable Global</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Global: false')).toBeInTheDocument();

    act(() => {
      screen.getByText('Enable Global').click();
    });

    expect(screen.getByText('Global: true')).toBeInTheDocument();

    act(() => {
      screen.getByText('Disable Global').click();
    });

    expect(screen.getByText('Global: false')).toBeInTheDocument();
  });

  it('should check if any loading state is active', () => {
    const TestComponent = () => {
      const { setLoading, isAnyLoading } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Any Loading: {isAnyLoading().toString()}</span>
          <button onClick={() => setLoading('task1', true)}>Start Task</button>
          <button onClick={() => setLoading('task1', false)}>Stop Task</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Any Loading: false')).toBeInTheDocument();

    act(() => {
      screen.getByText('Start Task').click();
    });

    expect(screen.getByText('Any Loading: true')).toBeInTheDocument();

    act(() => {
      screen.getByText('Stop Task').click();
    });

    expect(screen.getByText('Any Loading: false')).toBeInTheDocument();
  });

  it('should clear all loading states', () => {
    const TestComponent = () => {
      const { setLoading, clearAllLoading, loadingStates } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Count: {Object.keys(loadingStates).length}</span>
          <button onClick={() => {
            setLoading('task1', true);
            setLoading('task2', true);
            setLoading('task3', true);
          }}>Set Multiple</button>
          <button onClick={clearAllLoading}>Clear All</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    act(() => {
      screen.getByText('Set Multiple').click();
    });

    expect(screen.getByText('Count: 3')).toBeInTheDocument();

    act(() => {
      screen.getByText('Clear All').click();
    });

    expect(screen.getByText('Count: 0')).toBeInTheDocument();
  });

  it('should handle loading with message', () => {
    const TestComponent = () => {
      const { setLoadingWithMessage, getLoadingMessage } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Message: {getLoadingMessage('task1') || 'None'}</span>
          <button onClick={() => setLoadingWithMessage('task1', true, 'Processing...')}>
            Start with Message
          </button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    expect(screen.getByText('Message: None')).toBeInTheDocument();

    act(() => {
      screen.getByText('Start with Message').click();
    });

    expect(screen.getByText('Message: Processing...')).toBeInTheDocument();
  });

  it('should maintain loading state across re-renders', () => {
    const TestComponent = () => {
      const { setLoading, isLoading } = React.useContext(LoadingContext);
      const [count, setCount] = React.useState(0);
      
      return (
        <div>
          <span>Loading: {isLoading('persistent').toString()}</span>
          <span>Count: {count}</span>
          <button onClick={() => setLoading('persistent', true)}>Start Loading</button>
          <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    act(() => {
      screen.getByText('Start Loading').click();
    });

    expect(screen.getByText('Loading: true')).toBeInTheDocument();

    act(() => {
      screen.getByText('Increment').click();
    });

    expect(screen.getByText('Count: 1')).toBeInTheDocument();
    expect(screen.getByText('Loading: true')).toBeInTheDocument();
  });

  it('should isolate loading states between different keys', () => {
    const TestComponent = () => {
      const { setLoading, isLoading } = React.useContext(LoadingContext);
      
      return (
        <div>
          <span>Task A: {isLoading('taskA').toString()}</span>
          <span>Task B: {isLoading('taskB').toString()}</span>
          <button onClick={() => setLoading('taskA', true)}>Load A</button>
          <button onClick={() => setLoading('taskB', false)}>Stop B</button>
        </div>
      );
    };

    render(
      <LoadingProvider>
        <TestComponent />
      </LoadingProvider>
    );

    act(() => {
      screen.getByText('Load A').click();
    });

    expect(screen.getByText('Task A: true')).toBeInTheDocument();
    expect(screen.getByText('Task B: false')).toBeInTheDocument();
  });

  it('should handle context outside provider', () => {
    const TestComponent = () => {
      const context = React.useContext(LoadingContext);
      return <div>Context exists: {context ? 'Yes' : 'No'}</div>;
    };

    render(<TestComponent />);

    expect(screen.getByText('Context exists: No')).toBeInTheDocument();
  });
});