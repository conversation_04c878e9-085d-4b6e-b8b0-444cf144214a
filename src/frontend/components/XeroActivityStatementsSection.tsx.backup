import React, { useEffect, useState, useCallback } from "react";
import { format } from "date-fns";
import { useAuthStatus } from "../hooks/useAuthStatus";
import { useEvents } from "../contexts";
import { useLoading } from "../contexts/LoadingContext";
import {
  getXeroExpenseBreakdown,
  syncTaxExpense,
  getXeroGSTData,
  syncGSTExpense,
  debouncedPublishExpenseUpdated,
} from "../api/xero";
import { getExpenses } from "../api/expenses";
import { XeroExpenseBreakdown, XeroGSTData } from "../../api/types/xero";
import XeroExpenseCard from "./XeroExpenseCard";
import TaxCalendar from "./TaxCalendar";
import { LoadingIndicator } from "./ForwardProjection";
import { Card } from "./shared/Card";
import XeroBadge from "./shared/XeroBadge";
import { isXeroExpense } from "./Expense/utils";

/**
 * Component for tax statements and tax-related expenses from Xero
 */
export const XeroActivityStatementsSection: React.FC = () => {
  // Get authentication status
  const { isAuthenticated, isLoading: isAuthLoading } = useAuthStatus();
  const { setLoading } = useLoading(); // Use global loading context
  const events = useEvents();

  // State for tax data
  const [expenseBreakdown, setExpenseBreakdown] =
    useState<XeroExpenseBreakdown | null>(null);
  const [gstData, setGSTData] = useState<XeroGSTData | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [error, setError] = useState("");

  // Sync status for tax expenses
  const [syncingTax, setSyncingTax] = useState(false);
  const [syncingGST, setSyncingGST] = useState(false);

  // State for info accordion
  const [infoExpanded, setInfoExpanded] = useState(false);

  /**
   * Format currency for display
   */
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  /**
   * Format date for display
   */
  const formatDate = (date: Date): string => {
    return format(new Date(date), "dd/MM/yyyy");
  };

  // State for GST prediction selection
  const [useProjectedGST, setUseProjectedGST] = useState(false);
  const [syncedWithProjected, setSyncedWithProjected] = useState(false);

  /**
   * Get current quarter information
   */
  const getCurrentQuarterInfo = useCallback((): {
    quarter: number;
    quarterName: string;
    quarterLabel: string;
    financialYear: string;
    daysElapsed: number;
    daysInQuarter: number;
    percentComplete: number;
    quarterStartMonth: number;
    quarterEndMonth: number;
  } => {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0-indexed (0 = January)
    const currentYear = today.getFullYear();

    // Determine current quarter (1-4)
    let quarter: number;
    let quarterStartMonth: number;
    let quarterEndMonth: number;

    if (currentMonth >= 0 && currentMonth <= 2) {
      // Q3 of financial year (Jan-Mar)
      quarter = 3;
      quarterStartMonth = 0; // January
      quarterEndMonth = 2; // March
    } else if (currentMonth >= 3 && currentMonth <= 5) {
      // Q4 of financial year (Apr-Jun)
      quarter = 4;
      quarterStartMonth = 3; // April
      quarterEndMonth = 5; // June
    } else if (currentMonth >= 6 && currentMonth <= 8) {
      // Q1 of financial year (Jul-Sep)
      quarter = 1;
      quarterStartMonth = 6; // July
      quarterEndMonth = 8; // September
    } else {
      // Q2 of financial year (Oct-Dec)
      quarter = 2;
      quarterStartMonth = 9; // October
      quarterEndMonth = 11; // December
    }

    // Determine financial year (e.g., "2023-24")
    let financialYearStart: number;
    let financialYearEnd: number;

    if (currentMonth >= 6) {
      // July onwards
      financialYearStart = currentYear;
      financialYearEnd = currentYear + 1;
    } else {
      // Before July
      financialYearStart = currentYear - 1;
      financialYearEnd = currentYear;
    }

    const financialYear = `${financialYearStart}-${String(
      financialYearEnd
    ).slice(2)}`;

    // Calculate days elapsed in the quarter
    const quarterStart = new Date(currentYear, quarterStartMonth, 1);
    const quarterEnd = new Date(currentYear, quarterEndMonth + 1, 0); // Last day of end month

    const daysElapsed = Math.floor(
      (today.getTime() - quarterStart.getTime()) / (1000 * 60 * 60 * 24)
    );
    const daysInQuarter =
      Math.floor(
        (quarterEnd.getTime() - quarterStart.getTime()) / (1000 * 60 * 60 * 24)
      ) + 1;
    const percentComplete = (daysElapsed / daysInQuarter) * 100;

    // Quarter name mapping
    const quarterNames = ["Jul-Sep", "Oct-Dec", "Jan-Mar", "Apr-Jun"];
    const quarterName = quarterNames[quarter - 1];

    // Quarter label (Q1, Q2, etc.)
    const quarterLabel = `Q${quarter}`;

    return {
      quarter,
      quarterName,
      quarterLabel,
      financialYear,
      daysElapsed,
      daysInQuarter,
      percentComplete,
      quarterStartMonth,
      quarterEndMonth,
    };
  }, []);

  /**
   * Calculate projected GST amount based on current amount and days elapsed
   */
  const calculateProjectedGST = useCallback(
    (
      currentAmount: number,
      daysElapsed: number,
      daysInQuarter: number
    ): number => {
      // Only pro-rate if we're not at the very beginning or end of the quarter
      if (daysElapsed > 5 && daysElapsed < daysInQuarter - 5) {
        // Formula: (current amount / days elapsed) * total days in quarter
        return (currentAmount / daysElapsed) * daysInQuarter;
      }

      // Otherwise return the current amount
      return currentAmount;
    },
    []
  );

  /**
   * Load all tax-related data
   */
  const loadData = useCallback(async () => {
    setLocalLoading(true);
    setLoading(true, "xero"); // Set global loading state with Xero type
    setError("");

    /**
     * Load expense breakdown data for PAYGW tax
     */
    const loadExpenseBreakdown = async () => {
      try {
        const data = await getXeroExpenseBreakdown();

        if (Array.isArray(data) && data.length > 0) {
          const breakdown = data[0];

          // Check for existing expenses
          try {
            const existingExpenses = await getExpenses();

            // Check for tax expense - only consider expenses that are genuinely from Xero
            const existingTax = existingExpenses.find(
              (exp) =>
                exp.type === "Taxes" &&
                exp.frequency === "monthly" &&
                isXeroExpense(exp) && // First check if it's from Xero
                exp.source?.includes("xero-tax")
            );

            // Create a tax object if it doesn't exist in the breakdown
            const taxData = breakdown.tax || {
              amount: 0,
              paymentDate: new Date(),
              isAdded: false,
              hasRecurringExpense: false,
            };

            // Set the expense breakdown with tax data
            setExpenseBreakdown({
              ...breakdown,
              tax: {
                ...taxData,
                hasRecurringExpense: !!existingTax,
              },
            });
          } catch (expError) {
            console.error("Error checking for existing tax expense:", expError);
            // Continue without checking for existing expenses
            setExpenseBreakdown(breakdown);
          }
        } else {
          // Create a default tax object if no data is returned
          setExpenseBreakdown({
            id: "",
            payRunId: "",
            paymentDate: new Date(),
            periodStartDate: new Date(),
            periodEndDate: new Date(),
            employeeCount: 0,
            wages: {
              amount: 0,
              paymentDate: new Date(),
              isAdded: false,
              hasRecurringExpense: false,
            },
            tax: {
              amount: 0,
              paymentDate: new Date(),
              isAdded: false,
              hasRecurringExpense: false,
            },
            superannuation: {
              amount: 0,
              paymentDate: new Date(),
              isAdded: false,
              hasRecurringExpense: false,
            },
          });
        }
      } catch (err) {
        console.error("Error loading Xero expense breakdown:", err);
        // Don't set error state here to avoid duplicate error messages
      }
    };

    /**
     * Load GST data
     */
    const loadGSTData = async () => {
      try {
        const data = await getXeroGSTData();

        // Check for existing expenses
        try {
          const existingExpenses = await getExpenses();

          // Check for GST expense - only consider expenses that are genuinely from Xero
          const existingGST = existingExpenses.find(
            (exp) =>
              exp.type === "Taxes" &&
              isXeroExpense(exp) && // First check if it's from Xero
              exp.source?.includes("xero-gst")
          );

          // If we have an existing GST expense, determine if it was created with the predicted amount
          if (existingGST) {
            // First check if the expense has metadata indicating which amount type was used
            if (
              existingGST.metadata &&
              typeof existingGST.metadata === "object"
            ) {
              // Check for the usedProjectedAmount flag in metadata
              const wasCreatedWithPrediction =
                existingGST.metadata.usedProjectedAmount === true;
              console.log("Found metadata in expense:", {
                metadata: existingGST.metadata,
                wasCreatedWithPrediction,
              });

              // Update the syncedWithProjected state
              setSyncedWithProjected(wasCreatedWithPrediction);

              // Also update the UI selection to match what was previously synced
              setUseProjectedGST(wasCreatedWithPrediction);
            }
            // Fallback to checking the description if metadata is not available
            else if (
              existingGST.description &&
              existingGST.description.includes("predicted amount")
            ) {
              console.log("Found prediction indicator in description");
              setSyncedWithProjected(true);
              setUseProjectedGST(true);
            }
            // If no metadata or description indicator, use amount comparison as a last resort
            else {
              // Calculate what the predicted amount would have been when it was created
              const { daysElapsed, daysInQuarter } = getCurrentQuarterInfo();
              const currentProjectedAmount = calculateProjectedGST(
                data.amount,
                daysElapsed,
                daysInQuarter
              );

              // Check if the existing expense amount is closer to the projected amount than the accrued amount
              // We need to be more lenient with the comparison since the projected amount changes over time
              const accrualDiff = Math.abs(existingGST.amount - data.amount);
              const projectionDiff = Math.abs(
                existingGST.amount - currentProjectedAmount
              );

              // If the expense amount is significantly different from the accrued amount,
              // it was likely created with prediction
              const wasCreatedWithPrediction =
                accrualDiff > 1 &&
                (projectionDiff < accrualDiff ||
                  accrualDiff / data.amount > 0.05);

              console.log("Expense detection using amount comparison:", {
                existingAmount: existingGST.amount,
                accrualAmount: data.amount,
                projectedAmount: currentProjectedAmount,
                accrualDiff,
                projectionDiff,
                wasCreatedWithPrediction,
              });

              // Update the syncedWithProjected state
              setSyncedWithProjected(wasCreatedWithPrediction);

              // Also update the UI selection to match what was previously synced
              setUseProjectedGST(wasCreatedWithPrediction);
            }
          }

          setGSTData({
            ...data,
            hasRecurringExpense: !!existingGST,
            isAdded: false,
          });
        } catch (expError) {
          console.error("Error checking for existing GST expense:", expError);
          // Continue without checking for existing expenses
          setGSTData(data);
        }
      } catch (err) {
        console.error("Error loading Xero GST data:", err);
        // Don't set error state here to avoid duplicate error messages
      }
    };

    try {
      await Promise.all([loadExpenseBreakdown(), loadGSTData()]);
    } catch (err) {
      console.error("Error loading tax data:", err);
      setError(err instanceof Error ? err.message : "Failed to load tax data");
    } finally {
      setLocalLoading(false);
      setLoading(false); // Clear global loading state
    }
  }, [
    setExpenseBreakdown,
    setGSTData,
    setError,
    getCurrentQuarterInfo,
    calculateProjectedGST,
    setSyncedWithProjected,
  ]);

  // Load data when component mounts
  useEffect(() => {
    if (isAuthenticated) {
      loadData();
    }
  }, [isAuthenticated, loadData]);

  /**
   * Handle syncing tax expense
   */
  const handleSyncTax = async () => {
    if (!expenseBreakdown) return;

    setSyncingTax(true);
    try {
      await syncTaxExpense(expenseBreakdown);

      // Update the local state to show the expense as added
      setExpenseBreakdown((prev: XeroExpenseBreakdown | null) => {
        if (!prev) return null;
        return {
          ...prev,
          tax: {
            ...prev.tax,
            isAdded: true,
            hasRecurringExpense: true,
          },
        };
      });

      // Notify the app that expenses have been updated
      debouncedPublishExpenseUpdated(events);
    } catch (err) {
      console.error("Error syncing tax expense:", err);
      setError(
        err instanceof Error ? err.message : "Failed to sync tax expense"
      );
    } finally {
      setSyncingTax(false);
    }
  };

  /**
   * Handle syncing GST expense
   * @param usePredicted Whether to use the predicted amount instead of the accrued amount
   */
  const handleSyncGST = async (usePredicted: boolean = false) => {
    if (!gstData) return;

    setSyncingGST(true);
    try {
      // Create a modified version of gstData with the predicted amount if requested
      const gstDataToSync = { ...gstData };

      if (usePredicted) {
        // Use the new totalProjectedGST if available, otherwise fall back to the old calculation
        const { daysElapsed, daysInQuarter } = getCurrentQuarterInfo();
        const projectedAmount =
          gstData.totalProjectedGST !== undefined
            ? gstData.totalProjectedGST
            : calculateProjectedGST(gstData.amount, daysElapsed, daysInQuarter);

        // Override the amount with the projected value
        gstDataToSync.amount = projectedAmount;
      }

      // Add a custom field to track which amount type was used
      gstDataToSync.metadata = {
        ...gstDataToSync.metadata,
        usedProjectedAmount: usePredicted,
      };

      console.log("Syncing GST expense:", {
        accrualAmount: gstData.amount,
        projectedAmount: gstData.totalProjectedGST || "using old calculation",
        usingAmount: usePredicted ? gstDataToSync.amount : gstData.amount,
        usedProjectedAmount: usePredicted,
      });

      await syncGSTExpense(gstDataToSync);

      // Update the local state to show the expense as added
      setGSTData((prev: XeroGSTData | null) => {
        if (!prev) return null;
        return {
          ...prev,
          hasRecurringExpense: true,
        };
      });

      // Remember which type of amount was used for syncing
      setSyncedWithProjected(usePredicted);

      // Notify the app that expenses have been updated
      debouncedPublishExpenseUpdated(events);
    } catch (err) {
      console.error("Error syncing GST expense:", err);
      setError(
        err instanceof Error ? err.message : "Failed to sync GST expense"
      );
    } finally {
      setSyncingGST(false);
    }
  };

  // Authentication status is defined at the top of the component

  // Show authentication banner if not authenticated
  if (!isAuthenticated) {
    // Show a different message when authentication is still being checked
    if (isAuthLoading) {
      return (
        <div className="space-y-6">
          <Card className="text-center py-8 bg-blue-50/50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700">
            <div className="flex items-center justify-center mb-4">
              <svg
                className="animate-spin h-8 w-8 text-blue-500 mr-3"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              <h3 className="text-lg font-semibold text-blue-700">
                Checking Authentication Status
              </h3>
            </div>
            <p className="text-blue-600">
              Please wait while we verify your Xero connection...
            </p>
          </Card>
        </div>
      );
    }

    // Show the standard authentication required message
    return (
      <div className="space-y-6">
        <Card className="text-center py-8 bg-yellow-50/50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700">
          <div className="flex items-center justify-center mb-2">
            <svg
              className="w-6 h-6 text-yellow-500 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 15v2m0 0v2m0-2h2m-2 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="text-lg font-semibold text-yellow-700">
              Authentication Required
            </h3>
          </div>
          <p className="text-yellow-600">
            You need to connect to Xero to access Tax Statements.
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="tax-info-accordion border rounded-md overflow-hidden bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-700">
        <div
          className="tax-info-header cursor-pointer p-4 bg-blue-50/80 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-700"
          onClick={() => setInfoExpanded(!infoExpanded)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <svg
                className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
              <div>
                <div className="flex items-center">
                  <h3 className="font-medium text-blue-800 dark:text-blue-300">
                    Xero Tax Statements
                  </h3>
                  <XeroBadge className="ml-2" />
                </div>
                <p className="text-sm text-blue-700 dark:text-blue-400 mt-1">
                  This section shows tax-related expenses from your Xero
                  account. Click to learn more about taxation and how to use
                  this page.
                </p>
              </div>
            </div>
            <svg
              className={`w-5 h-5 text-blue-500 transform transition-transform duration-200 ${
                infoExpanded ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>

        {infoExpanded && (
          <div className="tax-info-content p-4 bg-blue-50/30 dark:bg-blue-900/10 text-sm">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-1">
                  Understanding Australian Business Taxation
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  Australian businesses typically deal with two main types of
                  tax obligations that affect cashflow:
                </p>
                <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700 dark:text-gray-300">
                  <li>
                    <span className="font-medium">
                      PAYGW (Pay As You Go Withholding):
                    </span>{" "}
                    Tax withheld from employee wages that must be paid to the
                    ATO monthly or quarterly.
                  </li>
                  <li>
                    <span className="font-medium">
                      GST (Goods and Services Tax):
                    </span>{" "}
                    A 10% tax on most goods and services, reported and paid
                    quarterly through Business Activity Statements (BAS).
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-1">
                  Accrual vs. Payment Periods
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  Understanding the difference between when tax obligations
                  accrue and when they're due for payment is crucial for
                  cashflow planning:
                </p>
                <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700 dark:text-gray-300">
                  <li>
                    <span className="font-medium">Accrual Period:</span> The
                    time when the tax liability is generated (e.g., when you pay
                    employees or collect GST).
                  </li>
                  <li>
                    <span className="font-medium">Payment Period:</span> The
                    deadline when you must pay the accrued tax to the ATO.
                  </li>
                  <li>
                    For <span className="font-medium">PAYGW</span>, tax accrues
                    monthly but is paid on the 21st of the following month.
                  </li>
                  <li>
                    For <span className="font-medium">GST</span>, tax accrues
                    throughout the quarter but is paid on the 28th of the month
                    following the quarter end.
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-1">
                  GST Accrued vs. Predicted Amounts
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  The GST Payable (BAS) card offers two amount options for
                  better cashflow planning:
                </p>
                <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700 dark:text-gray-300">
                  <li>
                    <span className="font-medium">Accrued Amount:</span> The
                    current GST liability based on sales recorded in Xero to
                    date.
                  </li>
                  <li>
                    <span className="font-medium">Predicted Amount:</span> A
                    projection of what your final GST liability will be for the
                    full quarter, calculated by adding the current accrued GST
                    amount to 10% of projected revenue from future invoices. The
                    system automatically filters out invoices that already exist
                    in Harvest/Xero to avoid double-counting.
                  </li>
                  <li>
                    <span className="font-medium">How it works:</span> The app
                    uses your project settings to generate projected invoices
                    for the remainder of the quarter, calculates 10% GST on
                    these future invoices, and adds this to your current accrued
                    GST amount from Xero.
                  </li>
                  <li>
                    <span className="font-medium">Expense Summary:</span> The
                    BAS card in the Expense Summary tab shows the same amount as
                    selected in the Xero Tax page, ensuring consistency across
                    the application.
                  </li>
                  <li>
                    Select either amount type by clicking on the corresponding
                    card before syncing.
                  </li>
                  <li>
                    The system remembers which amount type you last used when
                    syncing.
                  </li>
                  <li>
                    You can update the expense at any time with either the
                    latest accrued or predicted amount.
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-1">
                  Using the Tax Calendar
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  The tax calendar visualization helps you track both accrual
                  and payment periods:
                </p>
                <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700 dark:text-gray-300">
                  <li>
                    Each day is split into two halves: the top half represents
                    PAYGW and the bottom half represents GST.
                  </li>
                  <li>
                    Different colors indicate different accrual periods
                    (alternating months for PAYGW, quarters for GST).
                  </li>
                  <li>
                    Payment due dates are highlighted with a darker shade of the
                    corresponding accrual period color.
                  </li>
                  <li>
                    <span className="font-medium">Hover over any day</span> to
                    see detailed information about the accrual period or payment
                    due date.
                  </li>
                  <li>
                    When you hover over an accrual period, both the period and
                    its corresponding payment date will be highlighted.
                  </li>
                  <li>
                    Similarly, hovering over a payment date will highlight the
                    corresponding accrual period.
                  </li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-blue-800 dark:text-blue-300 mb-1">
                  Cashflow Planning with Tax Obligations
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  Use this information to better plan your business cashflow:
                </p>
                <ul className="list-disc pl-5 mt-2 space-y-1 text-gray-700 dark:text-gray-300">
                  <li>
                    Sync your PAYGW and GST/BAS payments from Xero to include
                    them in your cashflow projections.
                  </li>
                  <li>
                    Anticipate large tax payments by seeing when they&apos;ll be
                    due.
                  </li>
                  <li>
                    Understand the relationship between your business activities
                    (paying employees, making sales) and the resulting tax
                    obligations.
                  </li>
                  <li>
                    Plan for quarterly GST payments, which can be substantial
                    and impact your cashflow if not properly accounted for.
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {localLoading && !expenseBreakdown && !gstData ? (
        <div className="py-8 text-center text-gray-500 dark:text-gray-400">
          <p>Loading tax data...</p>
        </div>
      ) : error ? (
        <Card className="text-center py-6 bg-red-50/50 dark:bg-red-900/20 border-red-200 dark:border-red-700">
          <svg
            className="mx-auto h-10 w-10 text-red-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <h3 className="mt-2 text-lg font-medium text-red-800 dark:text-red-200">
            Error Loading Data
          </h3>
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{error}</p>
          <button
            onClick={loadData}
            className="mt-3 inline-flex items-center px-3 py-1.5 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800 dark:hover:bg-red-900/50"
          >
            <svg
              className="-ml-0.5 mr-1.5 h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Try Again
          </button>
        </Card>
      ) : !expenseBreakdown && !gstData ? (
        <Card className="text-center py-8 bg-gray-50/50 dark:bg-gray-800/50 border-gray-200/70 dark:border-gray-700/70">
          <svg
            className="mx-auto h-16 w-16 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">
            No Tax Data Found
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            No tax data was found in your Xero account.
          </p>
          <button
            onClick={loadData}
            className="mt-3 inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <svg
              className="-ml-0.5 mr-1.5 h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Refresh
          </button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* Tax (PAYGW) Card */}
          {expenseBreakdown && (
            <XeroExpenseCard
              title="Tax (PAYGW)"
              amount={expenseBreakdown.tax.amount}
              date={expenseBreakdown.tax.paymentDate}
              isAdded={expenseBreakdown.tax.isAdded || false}
              hasRecurringExpense={
                expenseBreakdown.tax.hasRecurringExpense || false
              }
              onSync={handleSyncTax}
              isLoading={syncingTax}
              description="Monthly PAYGW tax payment due on the 21st of the following month"
              type="tax"
            />
          )}

          {/* Combined GST/BAS Card */}
          {gstData && (
            <Card className="border-l-4 border-l-blue-500 hover:shadow-md transition-all duration-200 relative h-full flex flex-col justify-between md:col-span-2">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className="w-7 h-7 mr-2 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center text-blue-600 dark:text-blue-400 flex-shrink-0">
                    <img
                      src="/ATO_Logo.svg"
                      alt="ATO Logo"
                      className="w-5 h-5"
                    />
                  </div>
                  <div>
                    <div className="flex items-center">
                      <h3 className="text-sm font-medium text-gray-800 dark:text-gray-100">
                        {getCurrentQuarterInfo().quarterLabel} FY
                        {getCurrentQuarterInfo().financialYear} BAS (
                        {getCurrentQuarterInfo().quarterName})
                      </h3>
                      <div className="inline-flex items-center px-2 py-0.5 ml-2 rounded bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 text-xs">
                        <svg
                          className="w-3 h-3 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        {Math.round(getCurrentQuarterInfo().percentComplete)}%
                        through {getCurrentQuarterInfo().quarterName} (
                        {getCurrentQuarterInfo().quarterLabel})
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                      GST payment with selectable accrued or predicted amount
                      options
                    </p>
                  </div>
                </div>
                <XeroBadge className="mt-1" />
              </div>

              <div className="text-center mt-4 mb-2">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Click a card below to select amount type
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                {/* Accrued Amount Section */}
                <div
                  className={`bg-white dark:bg-gray-800 border ${
                    !useProjectedGST
                      ? "border-purple-300 dark:border-purple-700"
                      : "border-gray-200 dark:border-gray-700"
                  } rounded-md p-3 relative cursor-pointer transition-all duration-200 ${
                    !useProjectedGST
                      ? "ring-2 ring-purple-300 dark:ring-purple-700"
                      : "hover:border-purple-200 dark:hover:border-purple-800"
                  }`}
                  onClick={() => setUseProjectedGST(false)}
                >
                  <div className="absolute top-2 right-2 flex items-center space-x-2">
                    {/* Badge - only show when selected and has recurring expense */}
                    {!useProjectedGST &&
                      gstData.hasRecurringExpense &&
                      syncedWithProjected === false && (
                        <div className="bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 text-xs px-2 py-1 rounded flex items-center">
                          <svg
                            className="w-3 h-3 mr-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          Using accrued
                        </div>
                      )}
                    <div className="w-5 h-5 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 text-purple-600 dark:text-purple-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Accrued Amount
                  </h4>
                  <div className="text-xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                    {formatCurrency(gstData.amount)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Current GST liability based on sales to date
                  </div>

                  {/* Selected indicator moved to top right */}
                </div>

                {/* Predicted Amount Section */}
                <div
                  className={`bg-white dark:bg-gray-800 border ${
                    useProjectedGST
                      ? "border-yellow-300 dark:border-yellow-700"
                      : "border-gray-200 dark:border-gray-700"
                  } rounded-md p-3 relative cursor-pointer transition-all duration-200 ${
                    useProjectedGST
                      ? "ring-2 ring-yellow-300 dark:ring-yellow-700"
                      : "hover:border-yellow-200 dark:hover:border-yellow-800"
                  }`}
                  onClick={() => setUseProjectedGST(true)}
                >
                  <div className="absolute top-2 right-2 flex items-center space-x-2">
                    {/* Badge - only show when selected and has recurring expense */}
                    {useProjectedGST &&
                      gstData.hasRecurringExpense &&
                      syncedWithProjected === true && (
                        <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 text-xs px-2 py-1 rounded flex items-center">
                          <svg
                            className="w-3 h-3 mr-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          Using predicted
                        </div>
                      )}
                    <div className="w-5 h-5 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3 text-yellow-600 dark:text-yellow-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Predicted Amount
                  </h4>
                  <div className="text-xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">
                    {formatCurrency(
                      gstData.totalProjectedGST !== undefined
                        ? gstData.totalProjectedGST
                        : calculateProjectedGST(
                            gstData.amount,
                            getCurrentQuarterInfo().daysElapsed,
                            getCurrentQuarterInfo().daysInQuarter
                          )
                    )}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Accrued GST plus GST from projected future invoices
                  </div>

                  {/* Calculation details box */}
                  <div className="mt-2 p-2 bg-yellow-50/50 dark:bg-yellow-900/10 border border-yellow-100 dark:border-yellow-800/30 rounded-md text-xs">
                    <div className="font-medium text-yellow-700 dark:text-yellow-400 mb-1">
                      Calculation Details:
                    </div>
                    <div className="text-gray-600 dark:text-gray-400 space-y-0.5">
                      <div>
                        Period: {getCurrentQuarterInfo().quarterLabel} FY
                        {getCurrentQuarterInfo().financialYear} (
                        {getCurrentQuarterInfo().quarterName})
                      </div>
                      <div>Accrued GST: {formatCurrency(gstData.amount)}</div>
                      {gstData.projectedGST !== undefined && (
                        <>
                          <div>
                            Projected Invoices:{" "}
                            {gstData.projectedInvoiceCount || 0} (
                            {formatCurrency(gstData.projectedRevenue || 0)})
                          </div>
                          <div>
                            Projected GST (10%):{" "}
                            {formatCurrency(gstData.projectedGST)}
                          </div>
                          <div className="pt-1 border-t border-yellow-100 dark:border-yellow-800/30 mt-1 font-medium">
                            Total: {formatCurrency(gstData.amount)} +{" "}
                            {formatCurrency(gstData.projectedGST)} ={" "}
                            {formatCurrency(gstData.totalProjectedGST || 0)}
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  {/* Selected indicator moved to top right */}
                </div>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Due date
                  </div>
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {formatDate(gstData.dueDate)}
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => handleSyncGST(useProjectedGST)}
                  disabled={syncingGST}
                  className={`
                    flex items-center justify-center w-full px-4 py-2 text-sm font-medium transition-all duration-200 rounded-md
                    ${syncingGST ? "opacity-80 cursor-wait" : ""}
                    ${
                      useProjectedGST
                        ? "bg-yellow-50 text-yellow-700 border border-yellow-200 hover:bg-yellow-600 hover:text-white dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800/50 dark:hover:bg-yellow-700 dark:hover:text-white"
                        : "bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-600 hover:text-white dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800/50 dark:hover:bg-purple-700 dark:hover:text-white"
                    }
                  `}
                >
                  {syncingGST ? (
                    <>
                      <svg
                        className="w-4 h-4 mr-2 animate-spin"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Syncing...</span>
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4 mr-1.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                        />
                      </svg>
                      <span>
                        Sync {useProjectedGST ? "Predicted" : "Accrued"} Amount
                      </span>
                    </>
                  )}
                </button>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                  {gstData.hasRecurringExpense
                    ? `Last synced using ${
                        syncedWithProjected ? "predicted" : "accrued"
                      } amount. Click to update.`
                    : useProjectedGST
                    ? "This will create an expense using the predicted amount for the full quarter."
                    : "This will create an expense using the current accrued amount."}
                </p>
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Tax Calendar Visualization */}
      <TaxCalendar currentDate={new Date()} />
    </div>
  );
};

export default XeroActivityStatementsSection;
