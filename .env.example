# Development environment
NODE_ENV=development
API_PORT=3002
FRONTEND_PORT=5173

# Base URLs for development
FRONTEND_URL=http://localhost:5173

# Xero credentials
XERO_CLIENT_ID=your_client_id_here
XERO_CLIENT_SECRET=your_client_secret_here
XERO_REDIRECT_URI=http://localhost:3002/api/xero/callback
XERO_SCOPES=openid profile email accounting.transactions accounting.reports.read accounting.settings offline_access payroll.employees payroll.payruns

# Feedback email configuration
FEEDBACK_EMAIL=<EMAIL>
FEEDBACK_EMAIL_API_KEY=re_your_resend_api_key_here

# Harvest credentials
HARVEST_ACCESS_TOKEN=your_harvest_token_here
HARVEST_ACCOUNT_ID=your_harvest_account_id_here

# Session configuration
SESSION_SECRET=generate_a_secure_random_string_here

# Anthropic API for Claude integration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
