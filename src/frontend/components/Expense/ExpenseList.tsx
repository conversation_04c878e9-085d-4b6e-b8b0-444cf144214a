import React from "react"; import { CustomExpense } from "../../../types"; import { ExpenseForm } from "./ExpenseForm"; import { ExpenseListItem } from "./ExpenseListItem"; import { useIsDesktop } from "../../hooks/useMediaQuery"; import { Card } from "../shared/Card"; import { DataList } from "../shared/lists"; interface ExpenseListProps { expenses: CustomExpense[]; loading: boolean; onEdit: (expense: CustomExpense) => void; onDelete: (id: string) => void; isAddingNewRow: boolean; editingId: string | null; setIsAddingNewRow: (value: boolean) => void; formState: { name: string; type: CustomExpense["type"]; amount: string; date: string; frequency: CustomExpense["frequency"]; repeatCount?: number; }; formHandlers: { setName: (name: string) => void; setType: (type: CustomExpense["type"]) => void; setAmount: (amount: string) => void; setDate: (date: string) => void; setFrequency: (frequency: CustomExpense["frequency"]) => void; setRepeatCount?: (repeatCount: number) => void; }; resetForm: () => void; handleSubmit: () => void; savingId: string | null; deletingId: string | null; fieldErrors?: { name?: string; amount?: string; date?: string; general?: string; }; } /** * Component for displaying and managing expense list */ export const ExpenseList: React.FC<ExpenseListProps> = ({ expenses, loading, onEdit, onDelete, isAddingNewRow, editingId, setIsAddingNewRow, formState, formHandlers, resetForm, handleSubmit, savingId, deletingId, fieldErrors = {}, // Default to empty object if not provided }) => { const isDesktop = useIsDesktop(); return ( <div> <div className="flex justify-between items-center mb-5"> <h2 className="text-lg font-semibold text-primary">Expense List</h2> <button onClick={() => setIsAddingNewRow(true)} disabled={loading || isAddingNewRow || editingId !== null} className="onboarding-add-expense inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors" > <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"> <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" /> </svg> Add Expense </button> </div> {/* Add new expense form at the top when adding */} {isAddingNewRow && ( <Card className="mb-4 bg-blue-50/50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"> <ExpenseForm editingId={null} expense={formState} onChange={formHandlers} onSubmit={handleSubmit} onCancel={() => { setIsAddingNewRow(false); resetForm(); }} loading={loading} savingId={savingId} errors={fieldErrors} /> </Card> )} {/* Responsive expense display */} {!isDesktop ? ( // Mobile view with cards <DataList variant="default" density="default" data={expenses} loading={loading && expenses.length === 0 && !isAddingNewRow} empty={expenses.length === 0 && !isAddingNewRow} emptyMessage="No expenses found" emptyIcon={ <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" /> </svg> } emptyAction={ <button onClick={() => setIsAddingNewRow(true)} disabled={loading || isAddingNewRow || editingId !== null} className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors" > Add Your First Expense </button> } keyExtractor={(expense) => expense.id} renderItem={(expense) => { // Show editing form if this expense is being edited if (editingId === expense.id) { return ( <Card className="btn-modern--primary/50"dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"> <ExpenseForm editingId={editingId} expense={formState} onChange={formHandlers} onSubmit={handleSubmit} onCancel={resetForm} loading={loading} savingId={savingId} errors={fieldErrors} /> </Card> ); } // Render expense item for mobile return ( <ExpenseListItem expense={expense} onEdit={onEdit} onDelete={onDelete} loading={loading} isEditing={!!editingId} isAddingNew={isAddingNewRow} deletingId={deletingId} isMobile={true} /> ); }} /> ) : ( // Desktop view - proper table <div className="overflow-x-auto"> <table className="w-full"> <thead className="btn-modern--secondary"dark:bg-gray-700"> <tr> <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">Name</th> <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">Type</th> <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-right">Amount</th> <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">Date</th> <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">Frequency</th> <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-center">Actions</th> </tr> </thead> <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"> {expenses.length === 0 && !isAddingNewRow ? ( <tr> <td colSpan={6} className="px-4 py-12 text-center"> <div className="flex flex-col items-center"> <svg className="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" /> </svg> <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">No expenses found</p> <button onClick={() => setIsAddingNewRow(true)} disabled={loading || isAddingNewRow || editingId !== null} className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors" > Add Your First Expense </button> </div> </td> </tr> ) : ( expenses.map((expense) => { // Show editing form inline if this expense is being edited if (editingId === expense.id) { return ( <tr key={expense.id} className="btn-modern--primary/50"dark:bg-blue-900/20"> <td colSpan={6} className="p-4"> <ExpenseForm editingId={editingId} expense={formState} onChange={formHandlers} onSubmit={handleSubmit} onCancel={resetForm} loading={loading} savingId={savingId} errors={fieldErrors} /> </td> </tr> ); } return ( <ExpenseListItem key={expense.id} expense={expense} onEdit={onEdit} onDelete={onDelete} loading={loading} isEditing={!!editingId} isAddingNew={isAddingNewRow} deletingId={deletingId} isMobile={false} /> ); }) )} </tbody> </table> </div> )} </div> ); }; export default ExpenseList; 