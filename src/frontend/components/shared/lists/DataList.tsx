import React, { forwardRef } from "react"; import { cn, dataListVariants, DataListVariantProps, } from "../../../utils/component-variants"; export interface DataListProps<T = any> extends Omit<React.HTMLAttributes<HTMLDivElement>, "children">, DataListVariantProps { data: T[]; renderItem: (item: T, index: number) => React.ReactNode; keyExtractor?: (item: T, index: number) => string | number; loading?: boolean; empty?: boolean; emptyMessage?: string; emptyIcon?: React.ReactNode; emptyAction?: React.ReactNode; searchTerm?: string; filterFn?: (item: T, searchTerm: string) => boolean; sortFn?: (a: T, b: T) => number; } /** * Modern DataList component with variant system * * Features: * - Type-safe data rendering with generics * - Multiple variants (default, cards, table, grid) * - Density options (compact, default, comfortable) * - Built-in search and filtering * - Sorting capabilities * - Loading and empty states * - Container queries for responsive design * - Accessibility features * - Dark mode support */ export const DataList = forwardRef<HTMLDivElement, DataListProps>( ( { className, variant = "default", density = "default", loading = false, data, renderItem, keyExtractor = (item, index) => index, empty = false, emptyMessage = "No items found", emptyIcon, emptyAction, searchTerm = "", filterFn, sortFn, ...props }, ref ) => { // Process data: filter, then sort let processedData = [...data]; // Apply filtering if search term and filter function provided if (searchTerm && filterFn) { processedData = processedData.filter((item) => filterFn(item, searchTerm)); } // Apply sorting if sort function provided if (sortFn) { processedData.sort(sortFn); } // Determine if we should show empty state const showEmpty = empty || (!loading && processedData.length === 0); return ( <div ref={ref} className={cn( dataListVariants({ variant, density, loading, }), className )} role="list" aria-busy={loading} aria-label={`Data list with ${processedData.length} items`} {...props} > {loading ? ( <div className="flex items-center justify-center py-12"> <div className="flex items-center space-x-2"> <svg className="animate-spin h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" /> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" /> </svg> <span className="text-sm text-gray-500 dark:text-gray-400"> Loading... </span> </div> </div> ) : showEmpty ? ( <div className="flex flex-col items-center justify-center py-12 text-center"> {emptyIcon && ( <div className="mb-4 text-gray-400 dark:text-gray-500"> {emptyIcon} </div> )} <p className="text-sm text-gray-500 dark:text-gray-400 mb-4"> {searchTerm ? `No items match "${searchTerm}"` : emptyMessage} </p> {emptyAction && <div>{emptyAction}</div>} </div> ) : ( processedData.map((item, index) => ( <div key={keyExtractor(item, index)} role="listitem" className={variant === "table" ? "table-row" : undefined} > {renderItem(item, index)} </div> )) )} </div> ); } ); DataList.displayName = "DataList"; export default DataList; 