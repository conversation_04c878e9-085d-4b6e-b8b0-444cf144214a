# Estimates API

This directory contains documentation for working with estimates in the Harvest API v2:

1. [Estimate Item Categories](01_estimate_item_categories.md)
   - Managing estimate item categories
   - Category types and properties
   - CRUD operations

2. [Estimate Messages](02_estimate_messages.md)
   - Sending estimate messages
   - Message templates
   - State changes via messages
   - Email notifications

3. [Estimates](03_estimates.md)
   - Main estimate operations
   - Creating and managing estimates
   - Managing estimate states
   - Line items management
   - Estimate properties and attributes