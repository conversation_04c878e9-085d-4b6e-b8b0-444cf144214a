import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { DealEditPage } from '../../../../../../src/frontend/components/CRM/DealEdit/DealEditPage';
import * as api from '../../../../../../src/frontend/api/crm';
import * as estimatesApi from '../../../../../../src/frontend/api/estimates';
import { EventContext } from '../../../../../../src/frontend/contexts/EventContext';

// Mock API modules
jest.mock('../../../../../../src/frontend/api/crm');
jest.mock('../../../../../../src/frontend/api/estimates');

// Mock child components to reduce complexity
jest.mock('../../../../../../src/frontend/components/CRM/DealEdit/DealHeader', () => ({
  DealHeader: ({ deal }: any) => <div data-testid="deal-header">{deal.dealName}</div>
}));

jest.mock('../../../../../../src/frontend/components/CRM/DealEdit/DealInfoSection', () => ({
  DealInfoSection: ({ deal, onUpdate }: any) => (
    <div data-testid="deal-info-section">
      <button onClick={() => onUpdate({ stage: 'closed-won' })}>Update Stage</button>
    </div>
  )
}));

jest.mock('../../../../../../src/frontend/components/CRM/DealEdit/DealFinancialSection', () => ({
  DealFinancialSection: ({ deal }: any) => (
    <div data-testid="deal-financial-section">Amount: ${deal.amount}</div>
  )
}));

jest.mock('../../../../../../src/frontend/components/CRM/DealEdit/DealContactsSection', () => ({
  DealContactsSection: () => <div data-testid="deal-contacts-section">Contacts</div>
}));

jest.mock('../../../../../../src/frontend/components/CRM/DealEdit/DealEstimatesSection', () => ({
  DealEstimatesSection: () => <div data-testid="deal-estimates-section">Estimates</div>
}));

jest.mock('../../../../../../src/frontend/components/CRM/DealEdit/DealNotesSection', () => ({
  DealNotesSection: () => <div data-testid="deal-notes-section">Notes</div>
}));

describe('DealEditPage', () => {
  const mockDeal = {
    id: 'deal-1',
    dealName: 'Enterprise Deal',
    companyId: 'company-1',
    companyName: 'Acme Corp',
    stage: 'negotiation',
    amount: 50000,
    closeDate: '2024-03-31',
    probability: 0.75,
    owner: '<EMAIL>',
    status: 'open',
    notes: 'High priority deal',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  };

  const mockContacts = [
    {
      id: 'contact-1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      role: 'Decision Maker'
    }
  ];

  const mockEstimates = [
    {
      id: 'estimate-1',
      estimateName: 'Q1 Project',
      amount: 50000,
      status: 'sent'
    }
  ];

  const mockEventContext = {
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (api.getDealById as jest.Mock).mockResolvedValue(mockDeal);
    (api.getDealContacts as jest.Mock).mockResolvedValue(mockContacts);
    (api.getDealEstimates as jest.Mock).mockResolvedValue(mockEstimates);
    (api.updateDeal as jest.Mock).mockResolvedValue({ ...mockDeal, stage: 'closed-won' });
  });

  const renderComponent = (dealId = 'deal-1') => {
    return render(
      <EventContext.Provider value={mockEventContext}>
        <MemoryRouter initialEntries={[`/crm/deals/${dealId}/edit`]}>
          <Routes>
            <Route path="/crm/deals/:id/edit" element={<DealEditPage />} />
          </Routes>
        </MemoryRouter>
      </EventContext.Provider>
    );
  };

  describe('Initial Load', () => {
    it('should render loading state initially', () => {
      renderComponent();
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should load and display deal data', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toHaveTextContent('Enterprise Deal');
        expect(screen.getByTestId('deal-financial-section')).toHaveTextContent('Amount: $50000');
      });

      expect(api.getDealById).toHaveBeenCalledWith('deal-1');
      expect(api.getDealContacts).toHaveBeenCalledWith('deal-1');
      expect(api.getDealEstimates).toHaveBeenCalledWith('deal-1');
    });

    it('should handle deal not found', async () => {
      (api.getDealById as jest.Mock).mockRejectedValue(new Error('Deal not found'));

      renderComponent('non-existent');

      await waitFor(() => {
        expect(screen.getByText(/deal not found/i)).toBeInTheDocument();
      });
    });

    it('should handle loading errors', async () => {
      (api.getDealById as jest.Mock).mockRejectedValue(new Error('Network error'));

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/error loading deal/i)).toBeInTheDocument();
      });
    });
  });

  describe('Deal Updates', () => {
    it('should update deal and emit event', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-info-section')).toBeInTheDocument();
      });

      const updateButton = screen.getByText('Update Stage');
      fireEvent.click(updateButton);

      await waitFor(() => {
        expect(api.updateDeal).toHaveBeenCalledWith('deal-1', { stage: 'closed-won' });
        expect(mockEventContext.emit).toHaveBeenCalledWith('deal:updated', expect.objectContaining({
          id: 'deal-1',
          stage: 'closed-won'
        }));
      });
    });

    it('should show success message after update', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-info-section')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Update Stage'));

      await waitFor(() => {
        expect(screen.getByText(/deal updated successfully/i)).toBeInTheDocument();
      });
    });

    it('should handle update errors', async () => {
      (api.updateDeal as jest.Mock).mockRejectedValue(new Error('Update failed'));

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-info-section')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Update Stage'));

      await waitFor(() => {
        expect(screen.getByText(/failed to update deal/i)).toBeInTheDocument();
      });
    });
  });

  describe('Section Display', () => {
    it('should render all deal sections', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
        expect(screen.getByTestId('deal-info-section')).toBeInTheDocument();
        expect(screen.getByTestId('deal-financial-section')).toBeInTheDocument();
        expect(screen.getByTestId('deal-contacts-section')).toBeInTheDocument();
        expect(screen.getByTestId('deal-estimates-section')).toBeInTheDocument();
        expect(screen.getByTestId('deal-notes-section')).toBeInTheDocument();
      });
    });
  });

  describe('Navigation', () => {
    it('should have back button that navigates to deals list', async () => {
      const { container } = renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
      });

      const backButton = container.querySelector('a[href="/crm/deals"]');
      expect(backButton).toBeInTheDocument();
      expect(backButton).toHaveTextContent(/back to deals/i);
    });
  });

  describe('Auto-save', () => {
    it('should debounce updates to prevent excessive API calls', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-info-section')).toBeInTheDocument();
      });

      // Simulate multiple rapid updates
      const updateButton = screen.getByText('Update Stage');
      fireEvent.click(updateButton);
      fireEvent.click(updateButton);
      fireEvent.click(updateButton);

      // Wait for debounce
      await waitFor(() => {
        // Should only call update once due to debouncing
        expect(api.updateDeal).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should listen for external deal updates', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
      });

      // Verify event listener was registered
      expect(mockEventContext.on).toHaveBeenCalledWith('deal:updated', expect.any(Function));
    });

    it('should update UI when external update is received', async () => {
      let eventHandler: Function;
      (mockEventContext.on as jest.Mock).mockImplementation((event, handler) => {
        if (event === 'deal:updated') {
          eventHandler = handler;
        }
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
      });

      // Simulate external update
      const updatedDeal = { ...mockDeal, dealName: 'Updated Deal Name' };
      (api.getDealById as jest.Mock).mockResolvedValue(updatedDeal);
      
      // Trigger the event
      eventHandler!({ id: 'deal-1' });

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toHaveTextContent('Updated Deal Name');
      });
    });

    it('should clean up event listeners on unmount', async () => {
      const { unmount } = renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
      });

      unmount();

      expect(mockEventContext.off).toHaveBeenCalledWith('deal:updated', expect.any(Function));
    });
  });

  describe('Loading States', () => {
    it('should show section-specific loading states', async () => {
      // Delay contact loading
      (api.getDealContacts as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockContacts), 1000))
      );

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
      });

      // Main content loaded but contacts still loading
      expect(screen.getByTestId('deal-contacts-section')).toBeInTheDocument();
    });
  });

  describe('Error Recovery', () => {
    it('should allow retry on error', async () => {
      (api.getDealById as jest.Mock)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockDeal);

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/error loading deal/i)).toBeInTheDocument();
      });

      const retryButton = screen.getByText(/retry/i);
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toHaveTextContent('Enterprise Deal');
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-header')).toBeInTheDocument();
      });

      const mainContent = screen.getByRole('main');
      expect(mainContent).toBeInTheDocument();
    });

    it('should be keyboard navigable', async () => {
      const user = userEvent.setup();
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('deal-info-section')).toBeInTheDocument();
      });

      // Tab to update button
      await user.tab();
      await user.tab();
      
      const updateButton = screen.getByText('Update Stage');
      expect(updateButton).toHaveFocus();

      // Activate with keyboard
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(api.updateDeal).toHaveBeenCalled();
      });
    });
  });
});