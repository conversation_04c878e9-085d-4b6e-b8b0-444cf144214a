# Harvest API v2 Documentation

This repository contains the complete documentation for the Harvest API v2. Below is the structure of the documentation:

## Directory Structure

```
harvest_api_docs/
├── 01_introduction/
│   ├── 01_overview.md
│   ├── 02_postman_collection.md
│   ├── 03_code_samples.md
│   ├── 04_supported_timezones.md
│   ├── 05_supported_currencies.md
│   └── 06_pagination.md
├── 02_authentication/
│   └── 01_authentication.md
├── 03_clients_api/
│   ├── 01_clients.md
│   └── 02_client_contacts.md
├── 04_company/
│   └── 01_company.md
├── 05_invoices/
│   ├── 01_invoice_item_categories.md
│   ├── 02_invoice_messages.md
│   ├── 03_invoice_payments.md
│   └── 04_invoices.md
├── 06_estimates/
│   ├── 01_estimate_item_categories.md
│   ├── 02_estimate_messages.md
│   └── 03_estimates.md
├── 07_expenses/
│   ├── expense_categories.md
│   └── expenses.md
├── 08_tasks/
│   └── tasks.md
├── 09_timesheets/
│   └── time_entries.md
├── 10_projects/
│   ├── projects.md
│   ├── project_user_assignments.md
│   └── project_task_assignments.md
├── 11_roles/
│   └── roles.md
├── 12_users/
│   ├── users.md
│   ├── billable_rates.md
│   ├── cost_rates.md
│   ├── project_assignments.md
│   └── teammates.md
└── 13_reports/
    ├── time.md
    ├── uninvoiced.md
    ├── expenses.md
    └── project_budget.md
```

## Documentation Sections

1. **Introduction**
   - Overview of the API
   - Postman Collection
   - Code Samples
   - Supported Timezones
   - Supported Currencies
   - Pagination

2. **Authentication**
   - Authentication methods and security

3. **Clients API**
   - Client management
   - Client contacts

4. **Company API**
   - Company information and settings

5. **Invoices API**
   - Invoice management
   - Invoice item categories
   - Invoice messages
   - Invoice payments

6. **Estimates API**
   - Estimate management
   - Estimate item categories
   - Estimate messages

7. **Expenses API**
   - Expense management
   - Expense categories

8. **Tasks API**
   - Task management

9. **Timesheets API**
   - Time entry management

10. **Projects API**
    - Project management
    - Project user assignments
    - Project task assignments

11. **Roles API**
    - Role management

12. **Users API**
    - User management
    - Billable rates
    - Cost rates
    - Project assignments
    - Team management

13. **Reports API**
    - Time reports
    - Uninvoiced reports
    - Expense reports
    - Project budget reports

Each markdown file contains detailed documentation about its respective endpoint, including available methods, parameters, example requests and responses.