/**
 * Project Repository Test
 * 
 * Tests for the project repository focusing on project management operations.
 * These tests use an in-memory SQLite database for isolation.
 */
import { ProjectRepository } from '@/api/repositories/project-repository';
import { Database } from '@/database';
import { runMigrations } from '@/api/services/db-init';
import { v4 as uuidv4 } from 'uuid';

describe('ProjectRepository', () => {
  let repository: ProjectRepository;
  let db: Database;
  
  beforeAll(async () => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Run migrations to set up schema
    await runMigrations(db);
  });
  
  beforeEach(() => {
    repository = new ProjectRepository();
    
    // Clear data before each test
    try {
      db.prepare('DELETE FROM project').run();
      db.prepare('DELETE FROM project_contact').run();
      db.prepare('DELETE FROM project_dependency').run();
    } catch (error) {
      // Tables might not exist in older schemas
    }
  });
  
  afterAll(() => {
    db.close();
  });
  
  describe('createProject', () => {
    it('creates a project with all fields', () => {
      const projectData = {
        name: 'Test Project',
        description: 'A test project',
        status: 'active',
        project_type: 'development',
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        budget: 100000,
        spent: 25000,
        currency: 'USD',
        harvest_project_id: 'harvest-123',
        company_id: uuidv4(),
        deal_id: uuidv4(),
        tags: ['important', 'web'],
        custom_fields: { priority: 'high' },
        created_by: 'test-user',
        updated_by: 'test-user'
      };
      
      const result = repository.createProject(projectData);
      
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.name).toBe('Test Project');
      expect(result?.status).toBe('active');
      expect(result?.budget).toBe(100000);
      expect(result?.tags).toEqual(['important', 'web']);
      expect(result?.custom_fields).toEqual({ priority: 'high' });
    });
    
    it('creates a project with minimal fields', () => {
      const projectData = {
        name: 'Minimal Project',
        status: 'active',
        company_id: uuidv4()
      };
      
      const result = repository.createProject(projectData);
      
      expect(result).toBeDefined();
      expect(result?.name).toBe('Minimal Project');
      expect(result?.currency).toBe('AUD'); // Default value
    });
    
    it('handles table not existing gracefully', () => {
      // Mock checkTableExists to return false
      jest.spyOn(repository as any, 'checkTableExists').mockReturnValue(false);
      
      const result = repository.createProject({ name: 'Test' });
      
      expect(result).toBeNull();
    });
  });
  
  describe('getProjectById', () => {
    it('retrieves a project by ID', () => {
      const projectData = {
        name: 'Test Project',
        status: 'active',
        company_id: uuidv4()
      };
      
      const created = repository.createProject(projectData);
      const retrieved = repository.getProjectById(created!.id);
      
      expect(retrieved).toBeDefined();
      expect(retrieved?.id).toBe(created!.id);
      expect(retrieved?.name).toBe('Test Project');
    });
    
    it('returns null for non-existent project', () => {
      const result = repository.getProjectById('non-existent-id');
      
      expect(result).toBeNull();
    });
    
    it('excludes soft-deleted projects', () => {
      const created = repository.createProject({
        name: 'Test Project',
        status: 'active',
        company_id: uuidv4()
      });
      
      repository.deleteProject(created!.id, 'test-user');
      const retrieved = repository.getProjectById(created!.id);
      
      expect(retrieved).toBeNull();
    });
  });
  
  describe('getProjectByHarvestId', () => {
    it('retrieves a project by Harvest ID', () => {
      const harvestId = 'harvest-123';
      repository.createProject({
        name: 'Test Project',
        status: 'active',
        company_id: uuidv4(),
        harvest_project_id: harvestId
      });
      
      const retrieved = repository.getProjectByHarvestId(harvestId);
      
      expect(retrieved).toBeDefined();
      expect(retrieved?.harvest_project_id).toBe(harvestId);
    });
  });
  
  describe('getProjectsByCompany', () => {
    it('retrieves all projects for a company', () => {
      const companyId = uuidv4();
      const otherCompanyId = uuidv4();
      
      repository.createProject({
        name: 'Project 1',
        status: 'active',
        company_id: companyId
      });
      
      repository.createProject({
        name: 'Project 2',
        status: 'completed',
        company_id: companyId
      });
      
      repository.createProject({
        name: 'Other Project',
        status: 'active',
        company_id: otherCompanyId
      });
      
      const results = repository.getProjectsByCompany(companyId);
      
      expect(results).toHaveLength(2);
      expect(results.map(p => p.name)).toContain('Project 1');
      expect(results.map(p => p.name)).toContain('Project 2');
      expect(results.map(p => p.name)).not.toContain('Other Project');
    });
  });
  
  describe('updateProject', () => {
    it('updates project fields', () => {
      const created = repository.createProject({
        name: 'Original Name',
        status: 'active',
        company_id: uuidv4()
      });
      
      const updated = repository.updateProject(created!.id, {
        name: 'Updated Name',
        status: 'completed',
        budget: 150000
      });
      
      expect(updated).toBeDefined();
      expect(updated?.name).toBe('Updated Name');
      expect(updated?.status).toBe('completed');
      expect(updated?.budget).toBe(150000);
      expect(updated?.updated_at).not.toBe(created?.updated_at);
    });
    
    it('preserves unmodified fields', () => {
      const created = repository.createProject({
        name: 'Test Project',
        description: 'Original description',
        status: 'active',
        company_id: uuidv4()
      });
      
      const updated = repository.updateProject(created!.id, {
        name: 'Updated Name'
      });
      
      expect(updated?.description).toBe('Original description');
      expect(updated?.status).toBe('active');
    });
    
    it('returns null for non-existent project', () => {
      const result = repository.updateProject('non-existent-id', {
        name: 'Updated'
      });
      
      expect(result).toBeNull();
    });
  });
  
  describe('deleteProject', () => {
    it('soft deletes a project', () => {
      const created = repository.createProject({
        name: 'Test Project',
        status: 'active',
        company_id: uuidv4()
      });
      
      const result = repository.deleteProject(created!.id, 'test-user');
      
      expect(result).toBe(true);
      
      // Should not be retrievable normally
      const retrieved = repository.getProjectById(created!.id);
      expect(retrieved).toBeNull();
    });
    
    it('returns false for non-existent project', () => {
      const result = repository.deleteProject('non-existent-id', 'test-user');
      
      expect(result).toBe(false);
    });
  });
  
  describe('project contacts', () => {
    let projectId: string;
    let contactId: string;
    
    beforeEach(() => {
      const created = repository.createProject({
        name: 'Test Project',
        status: 'active',
        company_id: uuidv4()
      });
      projectId = created!.id;
      contactId = uuidv4();
    });
    
    it('adds a contact to a project', () => {
      const result = repository.addProjectContact({
        project_id: projectId,
        contact_id: contactId,
        role: 'Project Manager',
        allocation_percentage: 100,
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        created_by: 'test-user',
        updated_by: 'test-user'
      });
      
      expect(result).toBeDefined();
      expect(result.project_id).toBe(projectId);
      expect(result.contact_id).toBe(contactId);
      expect(result.role).toBe('Project Manager');
    });
    
    it('retrieves project contacts', () => {
      repository.addProjectContact({
        project_id: projectId,
        contact_id: contactId,
        role: 'Developer',
        allocation_percentage: 50,
        created_by: 'test-user',
        updated_by: 'test-user'
      });
      
      const contacts = repository.getProjectContacts(projectId);
      
      expect(contacts).toHaveLength(1);
      expect(contacts[0].contact_id).toBe(contactId);
      expect(contacts[0].role).toBe('Developer');
    });
    
    it('removes a contact from a project', () => {
      repository.addProjectContact({
        project_id: projectId,
        contact_id: contactId,
        role: 'Developer',
        created_by: 'test-user',
        updated_by: 'test-user'
      });
      
      const result = repository.removeProjectContact(projectId, contactId);
      expect(result).toBe(true);
      
      const contacts = repository.getProjectContacts(projectId);
      expect(contacts).toHaveLength(0);
    });
  });
  
  describe('project dependencies', () => {
    let project1Id: string;
    let project2Id: string;
    
    beforeEach(() => {
      const project1 = repository.createProject({
        name: 'Project 1',
        status: 'active',
        company_id: uuidv4()
      });
      const project2 = repository.createProject({
        name: 'Project 2',
        status: 'active',
        company_id: uuidv4()
      });
      
      project1Id = project1!.id;
      project2Id = project2!.id;
    });
    
    it('adds a project dependency', () => {
      const result = repository.addProjectDependency({
        predecessor_project_id: project1Id,
        successor_project_id: project2Id,
        dependency_type: 'finish_to_start',
        lag_days: 5,
        created_by: 'test-user'
      });
      
      expect(result).toBeDefined();
      expect(result.predecessor_project_id).toBe(project1Id);
      expect(result.successor_project_id).toBe(project2Id);
      expect(result.dependency_type).toBe('finish_to_start');
      expect(result.lag_days).toBe(5);
    });
    
    it('retrieves project dependencies', () => {
      repository.addProjectDependency({
        predecessor_project_id: project1Id,
        successor_project_id: project2Id,
        dependency_type: 'start_to_start',
        created_by: 'test-user'
      });
      
      const deps = repository.getProjectDependencies(project2Id);
      
      expect(deps.predecessors).toHaveLength(1);
      expect(deps.predecessors[0].predecessor_project_id).toBe(project1Id);
      expect(deps.successors).toHaveLength(0);
      
      const deps2 = repository.getProjectDependencies(project1Id);
      expect(deps2.predecessors).toHaveLength(0);
      expect(deps2.successors).toHaveLength(1);
      expect(deps2.successors[0].successor_project_id).toBe(project2Id);
    });
  });
  
  describe('knowledge graph methods', () => {
    it('gets projects for knowledge graph', () => {
      const companyId = uuidv4();
      const dealId = uuidv4();
      
      repository.createProject({
        name: 'KG Project',
        status: 'active',
        company_id: companyId,
        deal_id: dealId,
        harvest_project_id: 'harvest-123'
      });
      
      const projects = repository.getProjectsForKnowledgeGraph();
      
      expect(projects).toHaveLength(1);
      expect(projects[0].name).toBe('KG Project');
      expect(projects[0].type).toBe('project');
      expect(projects[0].companyId).toBe(companyId);
      expect(projects[0].dealId).toBe(dealId);
      expect(projects[0].harvestProjectId).toBe('harvest-123');
    });
    
    it('gets project relationships for knowledge graph', () => {
      const companyId = uuidv4();
      const contactId = uuidv4();
      const project1 = repository.createProject({
        name: 'Project 1',
        status: 'active',
        company_id: companyId
      });
      const project2 = repository.createProject({
        name: 'Project 2',
        status: 'active',
        company_id: companyId
      });
      
      // Add contact to project
      repository.addProjectContact({
        project_id: project1!.id,
        contact_id: contactId,
        role: 'manager',
        created_by: 'test-user',
        updated_by: 'test-user'
      });
      
      // Add dependency
      repository.addProjectDependency({
        predecessor_project_id: project1!.id,
        successor_project_id: project2!.id,
        dependency_type: 'finish_to_start',
        created_by: 'test-user'
      });
      
      const relationships = repository.getProjectRelationshipsForKnowledgeGraph();
      
      // Should have relationships for: company, contact, and dependency
      expect(relationships.length).toBeGreaterThanOrEqual(3);
      
      // Check company relationship
      const companyRel = relationships.find(r => 
        r.source === project1!.id && r.target === companyId
      );
      expect(companyRel).toBeDefined();
      expect(companyRel?.type).toBe('belongs_to');
      
      // Check contact relationship
      const contactRel = relationships.find(r => 
        r.source === project1!.id && r.target === contactId
      );
      expect(contactRel).toBeDefined();
      expect(contactRel?.type).toBe('manager');
      
      // Check dependency relationship
      const depRel = relationships.find(r => 
        r.source === project1!.id && r.target === project2!.id
      );
      expect(depRel).toBeDefined();
      expect(depRel?.type).toBe('finish_to_start');
    });
  });
});