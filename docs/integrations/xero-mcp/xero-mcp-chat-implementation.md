# Xero MCP Chat Implementation with <PERSON>

## Overview

This document describes the implementation of the Xero Model Context Protocol (MCP) chat interface in the Onbord Financial Dashboard. The feature provides the same experience as using the Xero MCP in Claude Desktop - Claude (via Anthropic API) acts as the intelligent assistant that uses MCP tools to interact with your Xero data.

## Architecture

```
User → Chat UI → Backend → Claude API → MCP Tools → Xero API
                     ↑                       ↓
                     └─── Response ←─────────┘
```

### How It Works

1. **User sends a message** in natural language (e.g., "Show me unpaid invoices")
2. **Backend sends to <PERSON>** along with available MCP tools
3. **<PERSON> interprets** the request and decides which tools to use
4. **<PERSON> calls tools** through the MCP protocol
5. **Backend executes** the Xero API calls
6. **<PERSON> formats** the response conversationally
7. **User sees** a natural, helpful response

### Backend Components

#### MCP Routes (`/src/api/routes/mcp.ts`)
- **POST /api/mcp/init** - Initialize a new MCP session
- **POST /api/mcp/chat** - Send message to <PERSON> and get response
- **GET /api/mcp/history/:sessionId** - Get chat history
- **POST /api/mcp/close** - Close an MCP session
- **GET /api/mcp/health** - Health check endpoint

#### Key Features
- Uses Anthropic's Claude 3.5 Sonnet model
- Full MCP protocol implementation
- Session management with conversation history
- Automatic tool execution based on Claude's decisions

#### Available MCP Tools
Claude has access to **46 comprehensive Xero operations** covering all major business functions:

**Core Accounting - Read Operations (14 commands):**
1. **list_invoices** - List sales invoices with optional status filter
2. **list_contacts** - List contacts/customers with pagination
3. **list_accounts** - List chart of accounts
4. **list_payments** - List payments with pagination
5. **list_bank_transactions** - List bank transactions
6. **list_bills** - List bills (accounts payable)
7. **list_credit_notes** - List credit notes with pagination
8. **list_items** - List products/services catalog
9. **list_quotes** - List quotes with pagination
10. **list_tax_rates** - List tax rates configuration
11. **get_organisation_info** - Get organization details
12. **get_balance_sheet** - Get balance sheet for a specific date
13. **get_profit_loss** - Get P&L report for a date range
14. **get_trial_balance** - Get trial balance report

**Payroll Operations (13 commands):**
15. **list_payroll_employees** - Get all payroll employees
16. **list_payroll_employee_leave** - Get employee leave records
17. **list_payroll_employee_leave_balances** - Get employee leave balances
18. **list_payroll_employee_leave_types** - Get employee leave types
19. **list_payroll_leave_periods** - Get employee leave periods
20. **list_payroll_leave_types** - Get all available leave types
21. **get_payroll_timesheet** - Get specific timesheet
22. **create_payroll_timesheet** - Create new timesheet
23. **update_payroll_timesheet_line** - Update timesheet line
24. **add_payroll_timesheet_line** - Add new timesheet line
25. **approve_payroll_timesheet** - Approve timesheet
26. **revert_payroll_timesheet** - Revert approved timesheet
27. **delete_payroll_timesheet** - Delete timesheet

**Advanced Reports (2 commands):**
28. **list_aged_receivables_by_contact** - Get aged receivables for specific contact
29. **list_aged_payables_by_contact** - Get aged payables for specific contact

**Contact Management (1 command):**
30. **list_contact_groups** - Get all contact groups

**Write Operations (16 commands):**
*Create Operations:*
31. **create_contact** - Create new contact with email/phone
32. **create_invoice** - Create new invoice with line items
33. **create_payment** - Create payment for invoice
34. **create_quote** - Create new quote with expiry date
35. **create_credit_note** - Create credit note

*Update Operations:*
36. **update_contact** - Update existing contact details
37. **update_invoice** - Update draft invoice line items
38. **update_quote** - Update draft quote details
39. **update_credit_note** - Update draft credit note

### Frontend Components

#### XeroChat Component (`/src/frontend/components/XeroChat/index.tsx`)
- Floating chat button (bottom right)
- Natural conversation interface
- Real-time streaming responses
- Tool usage indicators
- Suggested queries for new users
- Connection status indicator

## Setup Requirements

### Environment Variables
Add to your `.env` file:
```bash
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

Get your API key from: https://console.anthropic.com/

### Existing Requirements
- Xero OAuth2 authentication (already configured)
- Express session management (already configured)

## User Experience

### Natural Language Interaction
Users can ask questions naturally across all business functions:

**Financial Reporting:**
- "What are my unpaid invoices?"
- "Show me the balance sheet"
- "How much did we make last month?"
- "What bills are due this week?"
- "Show me the trial balance for December"

**Contact & Customer Management:**
- "List all my customers"
- "Create a new contact for ABC Company"
- "Update John Smith's email address"
- "Show me aged receivables for Acme Corp"

**Invoice & Quote Management:**
- "Create an invoice for $5000 to ABC Company"
- "Update the due date on invoice INV-123"
- "Show me all draft quotes"
- "Create a quote for consulting services"

**Payroll Operations:**
- "List all employees"
- "Show me John's leave balance"
- "Create a timesheet for this week"
- "Approve timesheet TS-456"
- "What leave types are available?"

**Advanced Analytics:**
- "Show me aged payables for all suppliers"
- "List all contact groups"
- "What tax rates are configured?"

### Claude's Responses
Claude provides:
- Natural, conversational responses
- Formatted data (tables, lists, summaries)
- Context-aware follow-up suggestions
- Error handling with helpful explanations

### Example Conversation
```
User: Hey, can you show me what invoices are still unpaid?

Claude: I'll check your unpaid invoices for you.

[Uses list_invoices tool with status="AUTHORISED"]

I found 3 unpaid invoices totaling $24,350:

1. **INV-0145** - Acme Corporation
   Amount: $12,500
   Due: 5 days ago (overdue)

2. **INV-0148** - Tech Solutions Ltd
   Amount: $8,350
   Due: In 10 days

3. **INV-0152** - Global Services Inc
   Amount: $3,500
   Due: In 25 days

You have $12,500 in overdue invoices. Would you like me to show you the contact details for Acme Corporation so you can follow up?
```

## Technical Implementation

### MCP Protocol Flow
1. Frontend sends user message to `/api/mcp/chat`
2. Backend constructs Anthropic API request with:
   - System prompt defining Claude's role
   - Conversation history
   - Available MCP tools with schemas
3. Claude responds with:
   - Text response
   - Tool use requests
4. Backend executes requested tools
5. Results sent back to Claude
6. Claude formats final response
7. Frontend displays the response

### Security
- All endpoints require Xero authentication
- Anthropic API key stored securely
- Sessions isolated per user
- Automatic session cleanup after 30 minutes
- No sensitive data stored in conversation history

## Deployment Considerations

### API Keys
- **Anthropic API Key**: Required for Claude integration
- **Xero OAuth**: Already configured in your app

### Rate Limits
- Anthropic API: Depends on your plan
- Xero API: Handled by existing integration

### Costs
- Anthropic API charges per token (input/output)
- Claude 3.5 Sonnet pricing: ~$3 per million input tokens, ~$15 per million output tokens
- Typical conversation: ~$0.01-0.05

## Next Steps

### Immediate Enhancements
1. Add streaming responses for better UX
2. Implement conversation export
3. Add user preferences (e.g., date formats)

### Advanced Features
1. **Multi-step Workflows**: Complex operations spanning multiple tools
2. **Scheduled Reports**: Daily/weekly automated summaries
3. **Voice Input**: Speech-to-text integration
4. **Bulk Operations**: Process multiple records simultaneously
5. **Custom Workflows**: Industry-specific business processes

### Additional MCP Servers
1. **Harvest MCP**: Time tracking integration
2. **HubSpot MCP**: CRM data access
3. **Custom MCPs**: Internal tools

## Troubleshooting

### "Anthropic API not configured"
- Add `ANTHROPIC_API_KEY` to your `.env` file
- Restart the server

### No response from Claude
- Check Anthropic API key is valid
- Verify network connectivity
- Check browser console for errors

### Tool execution errors
- Verify Xero authentication is active
- Check Xero API permissions
- Review server logs for details

### Rate limit errors
- Implement request queuing
- Add retry logic with backoff
- Consider upgrading Anthropic plan

## Benefits of MCP Architecture

1. **Natural Interaction**: Users don't need to learn commands
2. **Intelligent Tool Use**: Claude decides when and how to use tools
3. **Context Awareness**: Follows conversation flow naturally
4. **Extensibility**: Easy to add new tools
5. **Consistency**: Same experience as Claude Desktop
6. **Future-Proof**: Compatible with MCP ecosystem growth

## Conclusion

This implementation brings the power of Claude Desktop's MCP experience directly into your web application. Users can interact with their Xero data through natural conversation, with Claude intelligently managing the technical details of API calls and data formatting.
