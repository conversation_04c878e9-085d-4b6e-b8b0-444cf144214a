import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import LeadsTabs from "./LeadsTabs";
import RadarPage from "./Radar/RadarPage";
import RelationshipsPage from "./Relationships/RelationshipsPage";
import WAGovtPage from "./WAGovt/WAGovtPage";
import { KnowledgeGraph } from "./KnowledgeGraph";
import { useFeatureFlag } from "../../api/feature-flags";
import { FeatureFlag } from "../../../types/feature-flags";

/**
 * Main Leads page component with tabs for different sections
 */
const LeadsPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Check if knowledge graph feature is enabled
  const { enabled: knowledgeGraphEnabled } = useFeatureFlag(FeatureFlag.KNOWLEDGE_GRAPH);

  // Determine active tab based on URL
  const getActiveTab = () => {
    const path = location.pathname;
    if (path.includes("/leads/relationships")) return "relationships";
    if (path.includes("/leads/wagov")) return "wagov";
    if (path.includes("/leads/knowledge-graph")) {
      // If knowledge graph is disabled, redirect to radar
      if (!knowledgeGraphEnabled) {
        return "radar";
      }
      return "knowledge-graph";
    }
    return "radar"; // Default tab
  };

  const [activeTab, setActiveTab] = useState(getActiveTab());
  
  // Redirect if trying to access disabled knowledge graph
  React.useEffect(() => {
    if (activeTab === "knowledge-graph" && !knowledgeGraphEnabled) {
      navigate("/leads");
    }
  }, [activeTab, knowledgeGraphEnabled, navigate]);

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    navigate(`/leads/${tab === "radar" ? "" : tab}`);
  };

  // Render the appropriate content based on the active tab
  const renderContent = () => {
    switch (activeTab) {
      case "relationships":
        return <RelationshipsPage />;
      case "wagov":
        return <WAGovtPage />;
      case "knowledge-graph":
        return <KnowledgeGraph />;
      default:
        return <RadarPage />;
    }
  };

  return (
    <div className="leads-page">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
        Radar
      </h1>

      {/* Tabs navigation */}
      <LeadsTabs activeTab={activeTab} onTabChange={handleTabChange} showKnowledgeGraph={knowledgeGraphEnabled} />

      {/* Tab content */}
      <div className="mt-4">{renderContent()}</div>
    </div>
  );
};

export default LeadsPage;
