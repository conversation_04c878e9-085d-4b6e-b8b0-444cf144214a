import { useMemo } from "react";
import { StaffUtilization } from "../../../../../services/harvest/time-report-service";
import { FixedPriceProject } from "../../../../api/harvest";
import { 
  calculateBillableAmountsByCurrency,
  calculateFixedPriceIncomeByCurrency,
  calculateStaffCost,
  calculateNonBillableCost
} from "../utils/financialCalculations";
import { calculateOverallUtilization } from "../utils/utilizationHelpers";

/**
 * Hook to calculate utilization metrics
 */
export const useUtilizationCalculations = (
  utilizationData: StaffUtilization[],
  fixedPriceProjects: FixedPriceProject[],
  fromDate?: string,
  toDate?: string,
  excludeLeave: boolean = false
) => {
  // Calculate total hours
  const totalHours = useMemo(() => 
    utilizationData.reduce(
      (sum: number, staff: StaffUtilization) => sum + staff.totalHours,
      0
    ),
    [utilizationData]
  );

  // Calculate total billable hours
  const totalBillableHours = useMemo(() => 
    utilizationData.reduce(
      (sum: number, staff: StaffUtilization) => sum + staff.billableHours,
      0
    ),
    [utilizationData]
  );

  // Calculate total leave hours
  const totalLeaveHours = useMemo(() => 
    utilizationData.reduce(
      (sum: number, staff: StaffUtilization) => sum + staff.leaveHours,
      0
    ),
    [utilizationData]
  );

  // Calculate overall utilization
  const overallUtilization = useMemo(() => 
    calculateOverallUtilization(
      totalBillableHours,
      totalHours,
      totalLeaveHours,
      excludeLeave
    ),
    [totalBillableHours, totalHours, totalLeaveHours, excludeLeave]
  );

  // Calculate billable amounts by currency
  const billableAmountsByCurrency = useMemo(() => 
    calculateBillableAmountsByCurrency(utilizationData),
    [utilizationData]
  );

  // Calculate fixed price income by currency
  const fixedPriceIncomeByCurrency = useMemo(() => 
    calculateFixedPriceIncomeByCurrency(fixedPriceProjects),
    [fixedPriceProjects]
  );

  // Calculate available capacity
  const availableCapacity = useMemo(() => 
    utilizationData.reduce((sum, staff) => {
      // Calculate period capacity for each staff member
      let periodCapacity = 0;
      const fromDateObj = fromDate ? new Date(fromDate) : null;
      const toDateObj = toDate ? new Date(toDate) : null;

      if (fromDateObj && toDateObj) {
        const daysDifference =
          Math.ceil(
            (toDateObj.getTime() - fromDateObj.getTime()) /
              (1000 * 60 * 60 * 24)
          ) + 1;
        const weeksDifference = daysDifference / 7;
        periodCapacity = staff.weeklyCapacity * weeksDifference;
      } else {
        periodCapacity = staff.weeklyCapacity;
      }

      // If excluding leave, subtract leave hours from capacity
      return (
        sum +
        (excludeLeave
          ? periodCapacity - staff.leaveHours
          : periodCapacity)
      );
    }, 0),
    [utilizationData, fromDate, toDate, excludeLeave]
  );

  // Calculate total capacity
  const totalCapacity = useMemo(() => 
    utilizationData.reduce((sum, staff) => {
      // Calculate total period capacity for each staff member
      let periodCapacity = 0;
      const fromDateObj = fromDate ? new Date(fromDate) : null;
      const toDateObj = toDate ? new Date(toDate) : null;

      if (fromDateObj && toDateObj) {
        const daysDifference =
          Math.ceil(
            (toDateObj.getTime() - fromDateObj.getTime()) /
              (1000 * 60 * 60 * 24)
          ) + 1;
        const weeksDifference = daysDifference / 7;
        periodCapacity = staff.weeklyCapacity * weeksDifference;
      } else {
        periodCapacity = staff.weeklyCapacity;
      }

      return sum + periodCapacity;
    }, 0),
    [utilizationData, fromDate, toDate]
  );

  // Calculate staff costs and profit margins for each currency
  const financialMetricsByCurrency = useMemo(() => {
    return Object.entries(billableAmountsByCurrency).map(([currency, amount]) => {
      // Calculate staff cost
      const staffCost = calculateStaffCost(utilizationData, fromDate, toDate);
      
      // Calculate non-billable cost
      const nonBillableCost = calculateNonBillableCost(
        utilizationData,
        fromDate,
        toDate,
        excludeLeave
      );

      // Calculate profit and profit margin
      const profit = amount - staffCost;
      const profitMargin = amount > 0 ? (profit / amount) * 100 : 0;

      // Log the final total staff cost
      console.log(
        `SUMMARY: Total staff cost calculation complete:`,
        {
          totalStaffCost: staffCost,
          nonBillableCost,
          currency: currency,
          totalBillableAmount: amount,
          profit: profit,
          profitMargin: `${Math.round(profitMargin)}%`,
        }
      );

      return {
        currency,
        billableAmount: amount,
        staffCost,
        nonBillableCost,
        profit,
        profitMargin: Math.round(profitMargin)
      };
    });
  }, [billableAmountsByCurrency, utilizationData, fromDate, toDate, excludeLeave]);

  return {
    totalHours,
    totalBillableHours,
    totalLeaveHours,
    overallUtilization,
    billableAmountsByCurrency,
    fixedPriceIncomeByCurrency,
    availableCapacity,
    totalCapacity,
    financialMetricsByCurrency
  };
};
