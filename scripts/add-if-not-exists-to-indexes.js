#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Path to the migration file
const migrationPath = path.join(__dirname, '..', 'migrations', '000_unified_schema.sql');

// Read the migration file
console.log('Reading migration file...');
let content = fs.readFileSync(migrationPath, 'utf8');

// Count existing CREATE INDEX statements
const createIndexMatches = content.match(/CREATE INDEX/gi) || [];
const existingCount = createIndexMatches.length;

// Replace CREATE INDEX with CREATE INDEX IF NOT EXISTS
// Using a regex that handles various formatting
const updatedContent = content.replace(
  /CREATE\s+INDEX\s+(?!IF\s+NOT\s+EXISTS)/gi,
  'CREATE INDEX IF NOT EXISTS '
);

// Count how many were updated
const updatedMatches = updatedContent.match(/CREATE INDEX IF NOT EXISTS/gi) || [];
const updatedCount = updatedMatches.length - (content.match(/CREATE INDEX IF NOT EXISTS/gi) || []).length;

// Write the updated content back
fs.writeFileSync(migrationPath, updatedContent);

console.log(`\n✅ Migration file updated successfully!`);
console.log(`📊 Statistics:`);
console.log(`   - Total CREATE INDEX statements found: ${existingCount}`);
console.log(`   - Statements updated with IF NOT EXISTS: ${updatedCount}`);
console.log(`   - Statements already had IF NOT EXISTS: ${existingCount - updatedCount}`);
console.log(`\n📁 Updated file: ${migrationPath}`);