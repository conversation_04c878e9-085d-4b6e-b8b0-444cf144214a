# Upstream Technical Documentation

This section contains technical documentation for developers working on maintaining or extending the Upstream financial dashboard application. It provides detailed information about the implementation of features, the data model, and integration with external services.

## Technical Documentation Structure

The technical documentation is organized by component:

### 1. [Architecture Overview](./architecture/README.md)

High-level overview of the system architecture and design principles:

- Technology stack
- Application structure
- Key design patterns
- Data flow diagrams

### 2. [Data Model](./data-model/README.md)

Detailed information about the database schema and entity relationships:

- Database schema
- Entity relationships
- Field ownership rules
- Data flows between components

### 3. Component Documentation

Technical details for specific system components:

- [Cashflow System](./components/cashflow/README.md) - Cashflow projection and Smart Forecast implementation
- [Expenses System](./components/expenses/README.md) - Expenses management implementation
- [Estimates System](./components/estimates/README.md) - Estimate feature implementation
- [CRM System](./components/crm/README.md) - CRM and Deal Management implementation
- [Reports System](./components/reports/README.md) - Reports feature implementation

### 4. [Integrations](./integrations/README.md)

Details about integration with external services:

- Xero integration
- Harvest integration
- HubSpot integration

### 5. [Development Practices](./development/README.md)

Guidelines and audits for development practices:

- CLAUDE.md audit and recommendations
- Quick wins and improvement opportunities
- Code conventions and standards

### 6. [Code Patterns](./patterns/README.md)

Best practices and patterns used throughout the codebase:

- Error handling patterns
- Async/await usage guidelines
- Common implementation patterns

## Architecture Overview

Upstream is built with the following technology stack:

- **Frontend:**
  - React (Functional components with Hooks)
  - TypeScript
  - Tailwind CSS
  - Various libraries (date-fns, recharts, etc.)

- **Backend:**
  - Node.js with Express
  - SQLite database (via better-sqlite3)
  - OAuth2 for authentication

- **External Integrations:**
  - Xero API for accounting data
  - Harvest API for project management data
  - HubSpot API for CRM data

The application follows a component-based architecture with React Hooks for state management and business logic encapsulation. The backend provides RESTful API endpoints for data persistence and proxy endpoints for interacting with external services.

## Key Concepts

### Smart Forecast System

The Smart Forecast system automates financial projections by analyzing project budgets in Harvest. It eliminates the need for manual draft invoice creation while providing more accurate projections. For details, see the [Cashflow System documentation](./components/cashflow/README.md).

### Deal-Estimate Integration

The application includes bidirectional integration between deals in the CRM and estimates, allowing changes in either to update the other based on field ownership rules. For details, see the [CRM System documentation](./components/crm/README.md).

### Xero Integration

The application integrates with Xero for accounting data, bank balances, transactions, bills, and payroll information. For details, see the [Integrations documentation](./integrations/README.md).

## Development Guidelines

For information about the project's development standards, coding conventions, and best practices, see the main [README.md](../../README.md) file and the `CLAUDE.md` file in the root directory.