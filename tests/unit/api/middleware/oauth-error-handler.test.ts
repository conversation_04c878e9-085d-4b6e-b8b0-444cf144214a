import { Request, Response, NextFunction } from 'express';
import { oauthErrorHandler } from '../../../../src/api/middleware/oauth-error-handler';

describe('OAuth Error Handler Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/api/xero/auth/callback'
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      redirect: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  it('should handle OAuth token expired error', () => {
    const error = new Error('Token expired');
    (error as any).statusCode = 401;
    (error as any).code = 'TOKEN_EXPIRED';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Authentication token expired',
      code: 'TOKEN_EXPIRED',
      requiresReauth: true
    });
  });

  it('should handle OAuth invalid grant error', () => {
    const error = new Error('Invalid grant');
    (error as any).error = 'invalid_grant';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'OAuth grant invalid or expired',
      code: 'INVALID_GRANT',
      requiresReauth: true
    });
  });

  it('should handle OAuth access denied error', () => {
    const error = new Error('Access denied');
    (error as any).error = 'access_denied';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(403);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Access denied by user',
      code: 'ACCESS_DENIED'
    });
  });

  it('should handle OAuth rate limit error', () => {
    const error = new Error('Rate limit exceeded');
    (error as any).statusCode = 429;
    (error as any).headers = { 'retry-after': '60' };

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(429);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Rate limit exceeded',
      code: 'RATE_LIMIT',
      retryAfter: 60
    });
  });

  it('should redirect on OAuth callback errors', () => {
    mockRequest.originalUrl = '/api/xero/auth/callback?error=access_denied';
    const error = new Error('Access denied');
    (error as any).error = 'access_denied';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.redirect).toHaveBeenCalledWith(
      '/?auth_error=access_denied&message=Access%20denied%20by%20user'
    );
  });

  it('should handle HubSpot OAuth errors', () => {
    mockRequest.originalUrl = '/api/hubspot/auth/callback';
    const error = new Error('Invalid client');
    (error as any).error = 'invalid_client';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.redirect).toHaveBeenCalledWith(
      '/crm?auth_error=invalid_client&message=Invalid%20OAuth%20client%20configuration'
    );
  });

  it('should handle Xero-specific errors', () => {
    const error = new Error('Organisation offline');
    (error as any).response = {
      body: {
        ErrorNumber: 10,
        Type: 'ORGANISATION_OFFLINE',
        Message: 'The organisation is offline'
      }
    };

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(503);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'The organisation is offline',
      code: 'ORGANISATION_OFFLINE',
      xeroErrorNumber: 10
    });
  });

  it('should pass non-OAuth errors to next handler', () => {
    const error = new Error('Generic error');

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(error);
    expect(mockResponse.status).not.toHaveBeenCalled();
  });

  it('should handle missing scopes error', () => {
    const error = new Error('Insufficient scope');
    (error as any).error = 'insufficient_scope';
    (error as any).scope = 'accounting.transactions.read';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(403);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Insufficient permissions',
      code: 'INSUFFICIENT_SCOPE',
      requiredScope: 'accounting.transactions.read'
    });
  });

  it('should handle network errors during OAuth', () => {
    const error = new Error('Network error');
    (error as any).code = 'ECONNREFUSED';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(503);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'OAuth service unavailable',
      code: 'SERVICE_UNAVAILABLE'
    });
  });

  it('should sanitize error messages for security', () => {
    const error = new Error('Invalid client_secret: abc123secret');
    (error as any).error = 'invalid_client';

    oauthErrorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Invalid OAuth client configuration',
        code: 'INVALID_CLIENT'
      })
    );
    expect(mockResponse.json).not.toHaveBeenCalledWith(
      expect.objectContaining({
        error: expect.stringContaining('abc123secret')
      })
    );
  });
});