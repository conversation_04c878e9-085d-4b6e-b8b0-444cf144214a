-- Migration 002: Add billing type to estimates
-- This migration adds support for both daily and hourly billing models
-- 
-- Context: Currently all rates are stored as daily rates and "hourly mode" is just visual.
-- This change enables true dual billing where:
-- - Daily billing: Fixed rate per day (hours/day doesn't affect price)
-- - Hourly billing: Rate × hours (hours/day directly affects price)

-- Add billing_type column to estimate table
ALTER TABLE estimate ADD COLUMN billing_type TEXT DEFAULT 'daily' 
  CHECK(billing_type IN ('daily', 'hourly'));

-- Add hours_per_day to estimate table (currently hardcoded to 7.5 in utils)
-- This will allow per-estimate configuration of standard working hours
ALTER TABLE estimate ADD COLUMN hours_per_day REAL DEFAULT 8.0
  CHECK(hours_per_day > 0 AND hours_per_day <= 24);

-- Update existing estimates to have explicit billing_type
-- All existing estimates are daily billing (current behavior)
UPDATE estimate SET billing_type = 'daily' WHERE billing_type IS NULL;

-- Note: We are NOT adding rate_type to allocations because we're using global billing type
-- All allocations within an estimate share the same billing type