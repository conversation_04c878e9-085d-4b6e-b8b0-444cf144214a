/**
 * Activity Timeline Component
 *
 * This component displays a chronological list of activities with infinite scroll
 * and date grouping, following the NotesTimeline pattern.
 */

import React from "react";
import { Activity } from "../../types/activity-types";
import ActivityItem from "./ActivityItem";

interface ActivityTimelineProps {
  activities: Activity[];
  isLoading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

/**
 * Activity Timeline component
 */
const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  activities,
  isLoading,
  hasMore,
  onLoadMore,
}) => {
  // Group activities by date
  const groupedActivities = React.useMemo(() => {
    const groups: { [date: string]: Activity[] } = {};

    activities.forEach((activity) => {
      const date = new Date(activity.createdAt).toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(activity);
    });

    return groups;
  }, [activities]);

  // Format date for display
  const formatDateGroup = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString("en-AU", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  };

  // Handle scroll to load more
  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

      // Load more when scrolled to bottom
      if (
        scrollHeight - scrollTop <= clientHeight + 100 &&
        hasMore &&
        !isLoading
      ) {
        onLoadMore();
      }
    },
    [hasMore, isLoading, onLoadMore]
  );

  if (activities.length === 0 && !isLoading) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No activities to show
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          Activities like deal updates, sync operations, and estimate creation will appear here
        </p>
        <div className="text-sm text-gray-400 dark:text-gray-500">
          Try creating a deal, syncing with Xero, or adding an estimate to see activities
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 h-[calc(100vh-24rem)] overflow-y-auto" onScroll={handleScroll}>
      {Object.entries(groupedActivities)
        .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
        .map(([dateString, dateActivities]) => (
          <div key={dateString} className="space-y-4">
            {/* Date Header */}
            <div className="sticky top-0 bg-white dark:bg-gray-800 py-2 z-10">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                {formatDateGroup(dateString)}
              </h3>
            </div>

            {/* Activities for this date */}
            <div className="space-y-3">
              {(dateActivities as Activity[])
                .sort(
                  (a, b) =>
                    new Date(b.createdAt).getTime() -
                    new Date(a.createdAt).getTime()
                )
                .map((activity, index) => (
                  <ActivityItem
                    key={activity.id}
                    activity={activity}
                    showConnector={
                      index < (dateActivities as Activity[]).length - 1
                    }
                  />
                ))}
            </div>
          </div>
        ))}

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex justify-center py-4">
          <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <svg
              className="animate-spin h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <span className="text-sm">Loading more activities...</span>
          </div>
        </div>
      )}

      {/* Load more button */}
      {hasMore && !isLoading && activities.length > 0 && (
        <div className="flex justify-center py-4">
          <button
            onClick={onLoadMore}
            className="px-4 py-2 text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 border border-blue-300 dark:border-blue-600 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
          >
            Load more activities
          </button>
        </div>
      )}

      {/* End of activities indicator */}
      {!hasMore && activities.length > 0 && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            You've reached the end of the activity feed
          </p>
        </div>
      )}
    </div>
  );
};

export default ActivityTimeline;
