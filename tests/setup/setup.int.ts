/**
 * Setup file for integration tests
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.PORT = '3002';

// Global test helpers
global.testUtils = {
  // Mock timeout for testing async functions
  mockTimeout: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
};

// Higher timeout for integration tests
jest.setTimeout(60000); // 60 seconds

// Mock console methods to reduce noise in tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Only log in verbose mode or when explicitly requested
const isVerbose = process.env.VERBOSE === 'true';

if (!isVerbose) {
  console.log = (...args: any[]) => {
    if (args.length > 0 && args[0] === 'TEST_LOG') {
      originalConsoleLog(...args.slice(1));
    }
  };
  
  console.error = (...args: any[]) => {
    if (args.length > 0 && args[0] === 'TEST_ERROR') {
      originalConsoleError(...args.slice(1));
    }
  };
  
  console.warn = (...args: any[]) => {
    if (args.length > 0 && args[0] === 'TEST_WARN') {
      originalConsoleWarn(...args.slice(1));
    }
  };
}

// Restore console methods after tests complete
afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});