# Opportunity Intelligence

## Overview

The Opportunity Intelligence system is an AI-powered feature that automatically analyzes CRM data to identify business opportunities, risks, and strategic insights. It helps financial services businesses proactively manage client relationships and maximize revenue potential.

## Key Features

### 1. Automated Opportunity Detection

The system continuously analyzes your CRM data to identify opportunities across four key areas:

#### Coverage Risk Assessment
- **Purpose**: Identify companies with insufficient team coverage
- **Analysis**: Evaluates relationship strength and team member distribution
- **Scoring**: High risk (85+), Medium risk (60-84), Low risk (<60)
- **Action Items**: Team member assignment recommendations

#### Project Renewal Detection
- **Purpose**: Identify upcoming project renewal opportunities
- **Analysis**: Monitors Harvest projects approaching completion (30-day window)
- **Data Sources**: Project end dates, budgets, completion rates
- **Action Items**: Client meeting scheduling, renewal discussions

#### Network Expansion Analysis
- **Purpose**: Identify referral and expansion opportunities
- **Analysis**: Relationship network size and connection strength
- **Threshold**: Companies with 5+ relationship connections
- **Action Items**: Referral strategies, service expansion

#### Engagement Risk Monitoring (Planned)
- **Purpose**: Identify relationships at risk of going stale
- **Analysis**: Interaction frequency and recency patterns
- **Action Items**: Proactive engagement strategies

### 2. Intelligent Scoring System

Each opportunity receives a priority score (1-100) based on:
- **Business Impact**: Revenue potential and strategic value
- **Urgency**: Time sensitivity of the opportunity
- **Feasibility**: Likelihood of successful action
- **Risk Level**: Potential negative consequences of inaction

### 3. Contextual Metadata

Every opportunity includes relevant context:
- **Detection Timestamp**: When the opportunity was identified
- **Supporting Data**: Specific metrics and measurements
- **Related Entities**: Linked companies, contacts, projects
- **Historical Context**: Previous interactions and outcomes

## Technical Implementation

### Backend Architecture

#### API Endpoints

**Primary Endpoint**: `/api/crm/network/opportunities/:companyId`
- **Method**: GET
- **Purpose**: Retrieve opportunity intelligence for a specific company
- **Response**: Array of opportunity objects with metadata

#### Data Flow

```mermaid
graph TD
    A[Company Analysis Request] --> B[Team Coverage Repository]
    A --> C[Harvest Service Integration]
    A --> D[Relationship Network Repository]
    
    B --> E[Coverage Risk Assessment]
    C --> F[Project Renewal Detection]
    D --> G[Network Expansion Analysis]
    
    E --> H[Opportunity Scoring Engine]
    F --> H
    G --> H
    
    H --> I[Contextual Metadata Assembly]
    I --> J[API Response]
```

#### Repository Integration

- **TeamCoverageRepository**: Analyzes team coverage across companies
- **ContactRelationshipsRepository**: Maps relationship networks
- **HarvestService**: Fetches project data and timelines
- **CompanyRepository**: Provides company context and details

### Frontend Implementation

#### Component Architecture

**Primary Component**: `OpportunityIntelligence.tsx`
- **Props**: `{ companyId: string }`
- **State Management**: React Query for data fetching
- **UI Framework**: Tailwind CSS with Heroicons

#### Visual Design

```typescript
// Opportunity Type Icons
renewal: ClockIcon          // Project endings
expansion: ArrowTrendingUpIcon  // Network opportunities  
risk: ExclamationTriangleIcon   // Coverage gaps
engagement: UserGroupIcon       // Relationship risks
```

#### Caching Strategy

- **Stale Time**: 5 minutes (300,000ms)
- **Cache Time**: 10 minutes (600,000ms)
- **Background Refetch**: Enabled for real-time updates
- **Error Handling**: Graceful degradation with retry logic

## Data Model

### Opportunity Object Structure

```typescript
interface Opportunity {
  id: string;                    // Unique identifier
  companyId: string;            // Associated company
  type: OpportunityType;        // Category of opportunity
  title: string;                // Human-readable title
  description: string;          // Detailed explanation
  score: number;                // Priority score (1-100)
  metadata: Record<string, any>; // Context-specific data
  detectedAt: string;           // ISO timestamp
}

type OpportunityType = 
  | 'renewal' 
  | 'expansion' 
  | 'engagement' 
  | 'risk' 
  | 'relationship';
```

### Metadata Examples

#### Coverage Risk Metadata
```json
{
  "coverageGap": 75,           // Percentage uncovered
  "uncoveredRoles": 3,         // Number of roles needing coverage
  "totalRoles": 4,             // Total roles identified
  "riskLevel": "high"          // Risk assessment
}
```

#### Project Renewal Metadata
```json
{
  "projectEndDate": "2024-01-15",  // Project completion date
  "projectName": "Website Redesign", // Project title
  "projectBudget": 25000,          // Project value
  "daysUntilEnd": 28               // Urgency indicator
}
```

#### Network Expansion Metadata
```json
{
  "networkSize": 7,                    // Number of connections
  "connectionTypes": ["colleague", "reports_to"], // Relationship types
  "strongConnections": 4,              // High-strength relationships
  "referralPotential": "high"          // Expansion assessment
}
```

## Integration Points

### Harvest Integration

- **Project Monitoring**: Real-time tracking of project timelines
- **Budget Analysis**: Revenue and completion rate assessment
- **Client Portfolio**: Cross-referencing with company data
- **User Mapping**: Linking contacts to Harvest users for project history

### HubSpot Integration

- **Deal Correlation**: Connecting opportunities to existing deals
- **Contact Enrichment**: Enhanced relationship mapping
- **Activity Tracking**: Engagement pattern analysis
- **Pipeline Integration**: Opportunity-to-deal conversion

### Team Coverage System

- **Role Definition**: Mapping team members to client roles
- **Coverage Assessment**: Quantifying relationship strength
- **Risk Calculation**: Multi-factor risk scoring
- **Assignment Recommendations**: AI-driven team optimization

## Usage Guidelines

### Best Practices

1. **Regular Review**: Check opportunity intelligence weekly
2. **Action Prioritization**: Focus on high-score opportunities first
3. **Team Coordination**: Share insights across relevant team members
4. **Historical Tracking**: Monitor opportunity conversion rates
5. **Feedback Loop**: Track action outcomes to improve AI accuracy

### Common Use Cases

#### Business Development
- Identify warm referral opportunities through network analysis
- Plan expansion strategies based on relationship mapping
- Prioritize client outreach based on engagement risk scores

#### Account Management
- Proactive renewal discussions for ending projects
- Team coverage optimization for key accounts
- Risk mitigation for high-value relationships

#### Strategic Planning
- Portfolio analysis for growth opportunities
- Resource allocation based on opportunity scoring
- Long-term relationship development strategies

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Opportunities loaded on-demand per company
2. **Background Processing**: Analysis runs asynchronously
3. **Caching**: Intelligent caching with appropriate TTL
4. **Batch Processing**: Efficient database queries
5. **Error Resilience**: Graceful degradation when services unavailable

### Scalability

- **Horizontal Scaling**: Stateless analysis engine
- **Database Optimization**: Indexed queries for fast retrieval
- **API Rate Limiting**: Prevents system overload
- **Memory Management**: Efficient data structures

## Future Enhancements

### Planned Features

1. **Machine Learning Integration**
   - Predictive scoring based on historical outcomes
   - Pattern recognition for opportunity identification
   - Automated action recommendations

2. **Advanced Analytics**
   - Opportunity conversion tracking
   - ROI analysis for different opportunity types
   - Team performance metrics

3. **Workflow Automation**
   - Automated task creation for high-priority opportunities
   - Email notifications for urgent actions
   - Integration with calendar systems

4. **Custom Opportunity Types**
   - User-defined opportunity categories
   - Custom scoring algorithms
   - Industry-specific analysis models

### Technical Roadmap

- **Real-time Processing**: Stream-based opportunity detection
- **External Data Sources**: Integration with additional business systems
- **Mobile Optimization**: Native mobile app support
- **API Extensibility**: Public API for third-party integrations

## Troubleshooting

### Common Issues

1. **No Opportunities Detected**
   - Verify company has sufficient relationship data
   - Check Harvest integration status
   - Ensure team coverage data is populated

2. **Inaccurate Scoring**
   - Review underlying data quality
   - Check for stale or outdated information
   - Validate relationship strength inputs

3. **Performance Issues**
   - Monitor API response times
   - Check database query efficiency
   - Verify caching effectiveness

### Debugging Tools

- **Development Mode**: Additional error details in dev environment
- **Logging**: Comprehensive opportunity detection logging
- **Health Checks**: System status monitoring
- **Performance Metrics**: Response time and accuracy tracking