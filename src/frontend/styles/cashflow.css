/* --- Hybrid Card/Table View for Transactions List --- */

/* Default: Mobile-first (Card view visible) */
.table-view-wrapper {
  display: none;
}
.card-view {
  display: block; /* Default display for the card container */
}

/* Tablet/Desktop view (using 1024px breakpoint) */
@media (min-width: 1024px) {
  .card-view {
    display: none; /* Hide cards on larger screens */
  }
  .table-view-wrapper {
    display: block; /* Show the table wrapper */
    overflow-x: auto; /* Add horizontal scroll if table becomes too wide */
  }
  .table-view {
    width: 100%; /* Ensure table takes full width */
    border-collapse: collapse; /* Standard table styling */
  }
  .table-view th,
  .table-view td {
    padding: 0.5rem; /* Tailwind's p-2 equivalent */
    text-align: left; /* Default alignment */
    border-bottom-width: 1px;
    border-color: theme("colors.gray.200");
    /* vertical-align is handled by align-middle in JSX */
  }
  .dark .table-view th,
  .dark .table-view td {
    border-color: theme("colors.gray.600"); /* Adjusted dark mode border */
  }
  .table-view thead th {
    /* Target thead specifically for sticky header */
    background-color: theme("colors.gray.50");
    font-weight: 600; /* Semibold */
    position: sticky; /* Keep header visible */
    top: 0; /* Stick to the top of the scrollable container */
    z-index: 10; /* Ensure header stays above body content */
  }
  .dark .table-view thead th {
    background-color: rgba(
      87,
      86,
      83,
      0.75
    ); /* Flexoki base-700 with slight transparency */
    backdrop-filter: blur(
      4px
    ); /* Optional: blur background behind sticky header */
  }
  .table-view tbody tr:hover {
    background-color: theme("colors.gray.50 / 50%"); /* Lighter hover effect */
  }
  .dark .table-view tbody tr:hover {
    background-color: rgba(87, 86, 83, 0.5); /* Flexoki base-700 with 50% opacity */
  }

  /* Align specific column headers */
  .table-view th:nth-child(1) {
    /* Flow header */
    text-align: center;
  }
  /* Amount (th:nth-child(5)) & Running Balance (th:nth-child(6)) header alignment handled by Tailwind */

  /* Align specific column cells */
  .table-view td:nth-child(1) {
    /* Flow cell */
    text-align: center;
  }
  /* Amount (td:nth-child(5)) & Running Balance (td:nth-child(6)) cell alignment handled by Tailwind */

  /* Badge styling within table */
  .table-view .inline-flex {
    /* Target the badge component */
    /* Base badge styles are likely handled by Tailwind classes applied in JSX */
    /* Add minor adjustments if needed */
  }
  .table-view .group:hover .opacity-70 {
    /* Ensure link icon hover works */
    opacity: 1;
  }

  /* Flow column width (already set in JSX, CSS ensures consistency if needed) */
  /* .table-view th:first-child, */
  /* .table-view td:first-child { */
  /* width: 3rem; */ /* w-12 */
  /* } */

  /* Description column - ensure block elements inside work */
  .table-view td:nth-child(3) span {
    display: block; /* Ensure spans stack */
  }
  .table-view td:nth-child(3) .font-medium {
    /* Style for primary description line if needed */
  }
  .table-view td:nth-child(3) .text-xs {
    /* Style for secondary description line if needed */
  }

  /* Running Balance column - flex container alignment is handled by Tailwind */
  /* .table-view td:nth-child(6) .flex { } */
}

/* Ensure the grid layout for cards is only applied in card view */
/* Note: Tailwind classes like 'grid' or 'space-y-*' applied directly in JSX handle this */
/* .card-view .transactions-grid { */
/* display: grid; gap: var(--space-xs); */ /* Example if needed */
/* } */
