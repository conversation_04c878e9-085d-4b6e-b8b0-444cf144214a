/**
 * Deal Projection Service Test
 * 
 * Critical business logic tests for deal-to-transaction conversion.
 * This service is crucial for financial forecasting based on CRM data.
 */
import { DealProjectionService } from '@/services/cashflow/deal-projection-service';
import { Deal } from '@/frontend/types/crm-types';
import { PotentialTransaction } from '@/types/financial';

describe('DealProjectionService', () => {
  let service: DealProjectionService;
  
  beforeEach(() => {
    service = new DealProjectionService();
  });
  
  describe('transformDealsToTransactions', () => {
    const baseDate = new Date('2025-01-01');
    const endDate = new Date('2025-12-31');
    
    const createDeal = (overrides: Partial<Deal> = {}): Deal => ({
      id: 'deal-1',
      name: 'Test Deal',
      stage: 'Proposal',
      value: 10000,
      probability: 0.7,
      startDate: '2025-02-01',
      endDate: '2025-04-30',
      invoiceFrequency: 'monthly',
      paymentTerms: 14,
      company: { id: 'comp-1', name: 'Test Company' },
      ...overrides
    } as Deal);
    
    it('transforms valid deals into transactions', () => {
      const deals = [createDeal()];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      // Should create 3 monthly transactions (Feb, Mar, Apr)
      expect(result).toHaveLength(3);
      
      // Each transaction should be 10000 / 3 = 3333.33
      expect(result[0].amount).toBeCloseTo(3333.33, 2);
      
      // Check first transaction details
      const firstTransaction = result[0];
      expect(firstTransaction.type).toBe('invoice');
      expect(firstTransaction.dealId).toBe('deal-1');
      expect(firstTransaction.probability).toBe(0.7);
      expect(firstTransaction.description).toContain('Test Deal');
      expect(firstTransaction.description).toContain('Invoice 1/3');
      
      // Payment date should be invoice date + 14 days
      const expectedPaymentDate = new Date('2025-02-01');
      expectedPaymentDate.setDate(expectedPaymentDate.getDate() + 14);
      expect(firstTransaction.date).toEqual(expectedPaymentDate);
    });
    
    it('excludes deals explicitly marked as excluded from projections', () => {
      const deals = [
        createDeal({ includeInProjections: 0 } as any)
      ];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      expect(result).toHaveLength(0);
    });
    
    it('excludes closed deals (won, lost, abandoned)', () => {
      const deals = [
        createDeal({ stage: 'Closed won' }),
        createDeal({ stage: 'Closed lost' }),
        createDeal({ stage: 'Abandoned' })
      ];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      expect(result).toHaveLength(0);
    });
    
    it('handles different invoice frequencies correctly', () => {
      const testCases = [
        { frequency: 'weekly', expectedInvoices: 12 }, // ~4 weeks * 3 months
        { frequency: 'fortnightly', expectedInvoices: 6 }, // ~2 fortnights * 3 months
        { frequency: 'monthly', expectedInvoices: 3 },
        { frequency: 'quarterly', expectedInvoices: 1 },
        { frequency: 'upfront', expectedInvoices: 1 },
        { frequency: 'completion', expectedInvoices: 1 }
      ];
      
      testCases.forEach(({ frequency, expectedInvoices }) => {
        const deals = [createDeal({ invoiceFrequency: frequency })];
        const result = service.transformDealsToTransactions(deals, baseDate, endDate);
        
        expect(result).toHaveLength(expectedInvoices);
        
        // Total amount should always equal deal value
        const totalAmount = result.reduce((sum, t) => sum + t.amount, 0);
        expect(totalAmount).toBeCloseTo(10000, 2);
      });
    });
    
    it('applies payment terms correctly', () => {
      const deals = [createDeal({ 
        invoiceFrequency: 'upfront',
        paymentTerms: 30 
      })];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      expect(result).toHaveLength(1);
      
      // Payment date should be start date + 30 days
      const expectedDate = new Date('2025-02-01');
      expectedDate.setDate(expectedDate.getDate() + 30);
      expect(result[0].date).toEqual(expectedDate);
    });
    
    it('handles deals without required fields', () => {
      const invalidDeals = [
        createDeal({ value: undefined }),
        createDeal({ startDate: undefined }),
        createDeal({ endDate: undefined }),
        createDeal({ probability: undefined })
      ];
      
      const result = service.transformDealsToTransactions(invalidDeals, baseDate, endDate);
      
      expect(result).toHaveLength(0);
    });
    
    it('filters transactions outside forecast period', () => {
      const deals = [createDeal({
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      })];
      
      const result = service.transformDealsToTransactions(
        deals, 
        new Date('2025-01-01'), 
        new Date('2025-12-31')
      );
      
      expect(result).toHaveLength(0);
    });
    
    it('calculates scenario types based on probability', () => {
      const deals = [
        createDeal({ probability: 0.9, id: 'high-prob' }),
        createDeal({ probability: 0.5, id: 'med-prob' }),
        createDeal({ probability: 0.1, id: 'low-prob' })
      ];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      // All deals should generate transactions
      expect(result.length).toBeGreaterThan(0);
      
      // Check that probability is preserved
      const highProbTransactions = result.filter(t => t.dealId === 'high-prob');
      expect(highProbTransactions.every(t => t.probability === 0.9)).toBe(true);
    });
    
    it('handles upfront payment correctly', () => {
      const deals = [createDeal({ 
        invoiceFrequency: 'upfront',
        startDate: '2025-03-15'
      })];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      expect(result).toHaveLength(1);
      expect(result[0].amount).toBe(10000);
      
      // Payment should be at start + payment terms
      const expectedDate = new Date('2025-03-15');
      expectedDate.setDate(expectedDate.getDate() + 14);
      expect(result[0].date).toEqual(expectedDate);
    });
    
    it('handles completion payment correctly', () => {
      const deals = [createDeal({ 
        invoiceFrequency: 'completion',
        endDate: '2025-06-30'
      })];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      expect(result).toHaveLength(1);
      expect(result[0].amount).toBe(10000);
      
      // Payment should be at end + payment terms
      const expectedDate = new Date('2025-06-30');
      expectedDate.setDate(expectedDate.getDate() + 14);
      expect(result[0].date).toEqual(expectedDate);
    });
    
    it('includes comprehensive metadata', () => {
      const deals = [createDeal()];
      
      const result = service.transformDealsToTransactions(deals, baseDate, endDate);
      
      expect(result[0].metadata).toEqual({
        dealName: 'Test Deal',
        dealStage: 'Proposal',
        invoiceNumber: 1,
        totalInvoices: 3,
        originalAmount: 10000,
        company: 'Test Company'
      });
    });
  });
});