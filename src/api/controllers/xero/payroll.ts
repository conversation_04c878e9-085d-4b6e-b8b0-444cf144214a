import { Request, Response } from 'express';
import {
  getXeroService,
  PayrollService,
  SuperannuationService
} from '../../../services/xero';
import { ExpensesRepository } from '../../repositories/expenses-repository';
import { CustomExpense } from '../../../types';
import { XERO_SOURCES } from '../../../constants/xero-backend';
import activityLogger from '../../../utils/activity-logger';

export class XeroPayrollController {
  /**
   * Get payroll expenses from Xero
   */
  public getPayrollExpenses = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Fixed value of 90 days for payroll lookback
      const days = 90;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create payroll service instance
      const payrollService = new PayrollService(client);

      try {
        // Fetch payroll expenses from Xero - with NO fallback to sample data
        const expenses = await payrollService.getPayrollExpensesForDisplay(tenantId, sinceDate);

        res.json({
          success: true,
          data: expenses
        });
      } catch (payrollError) {
        // Handle specific payroll service errors with appropriate status codes
        console.error('Error in Xero payroll service:', payrollError);

        if (payrollError instanceof Error) {
          // Differentiate between different error types
          if (payrollError.message.includes('Missing Xero Payroll API scopes')) {
            // Missing scopes - return 403 Forbidden with clear message
            res.status(403).json({
              success: false,
              message: payrollError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required payroll scopes. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (payrollError.message.includes('No payroll data available')) {
            // No data - return 404 Not Found
            res.status(404).json({
              success: false,
              message: payrollError.message,
              code: 'NO_DATA',
              details: 'No payroll data was found in your Xero account. Please make sure you have payroll setup in Xero.'
            });
            return;
          } else if (payrollError.message.includes('Payroll API not available')) {
            // API not available - return 501 Not Implemented
            res.status(501).json({
              success: false,
              message: payrollError.message,
              code: 'API_UNAVAILABLE',
              details: 'The Xero Payroll API is not available for your account type or region.'
            });
            return;
          }
        }

        // Generic payroll service error - return 500 Internal Server Error
        res.status(500).json({
          success: false,
          message: payrollError instanceof Error ? payrollError.message : 'Unknown payroll service error',
          code: 'PAYROLL_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting payroll expenses from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get payroll expenses from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Get payroll expense breakdown from Xero
   */
  public getPayrollExpenseBreakdown = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Fixed value of 90 days for lookback
      const days = 90;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create payroll service instance
      const payrollService = new PayrollService(client);

      try {
        // Fetch payroll expense breakdown
        const expenseBreakdown = await payrollService.getPayrollExpenseBreakdown(tenantId, sinceDate);

        res.json({
          success: true,
          data: expenseBreakdown
        });
      } catch (payrollError) {
        // Handle specific payroll service errors with appropriate status codes
        console.error('Error in Xero payroll expense breakdown service:', payrollError);

        if (payrollError instanceof Error) {
          // Differentiate between different error types
          if (payrollError.message.includes('Missing Xero Payroll API scopes')) {
            // Missing scopes - return 403 Forbidden with clear message
            res.status(403).json({
              success: false,
              message: payrollError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required payroll scopes. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (payrollError.message.includes('No payroll data available')) {
            // No data - return 404 Not Found
            res.status(404).json({
              success: false,
              message: payrollError.message,
              code: 'NO_DATA',
              details: 'No payroll data was found in your Xero account. Please make sure you have payroll setup in Xero.'
            });
            return;
          } else if (payrollError.message.includes('Payroll API not available')) {
            // API not available - return 501 Not Implemented
            res.status(501).json({
              success: false,
              message: payrollError.message,
              code: 'API_UNAVAILABLE',
              details: 'The Xero Payroll API is not available for your account type or region.'
            });
            return;
          }
        }

        // Generic payroll service error - return 500 Internal Server Error
        res.status(500).json({
          success: false,
          message: payrollError instanceof Error ? payrollError.message : 'Unknown payroll service error',
          code: 'PAYROLL_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting payroll expense breakdown from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get payroll expense breakdown from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Convert a payroll expense to a custom expense
   */
  public convertPayrollToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { expense } = req.body;

      if (!expense) {
        res.status(400).json({
          success: false,
          message: 'Missing expense data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate type and name
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: expense.description || `Payroll: ${new Date(expense.paymentDate).toLocaleDateString()}`,
        type: 'Monthly Payroll', // Use predefined type for payroll
        amount: expense.amount,
        date: new Date(expense.paymentDate), // Use payment date
        frequency: expense.frequency as CustomExpense['frequency'], // Map to expected frequency
        source: XERO_SOURCES.PAYROLL, // Mark as coming from Xero
        description: `Payroll expense from Xero for period ending ${new Date(expense.periodEndDate).toLocaleDateString()}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero payroll sync activity...');
        const activity = await activityLogger.logXeroSyncCompleted('payroll', 1);
        console.log('Successfully logged Xero payroll sync activity:', activity);
      } catch (activityError) {
        console.error('Error logging Xero payroll sync activity:', activityError);
        console.error('Activity error details:', {
          message: activityError?.message,
          stack: activityError?.stack,
          name: activityError?.name
        });
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting payroll to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert payroll to expense'
      });
    }
  };

  /**
   * Sync an expense type from the expense breakdown
   */
  public syncExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { expense } = req.body;

      if (!expense) {
        res.status(400).json({
          success: false,
          message: 'Missing expense data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate type and name
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: expense.name,
        type: expense.type,
        amount: expense.amount,
        date: new Date(expense.date),
        frequency: expense.frequency || 'monthly',
        source: expense.source || 'xero',
        metadata: {
          ...expense.metadata,
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero expense sync activity...');
        await activityLogger.logXeroSyncCompleted('expenses', 1);
        console.log('Successfully logged Xero expense sync activity');
      } catch (activityError) {
        console.error('Error logging Xero expense sync activity:', activityError);
        console.error('Activity error stack:', activityError.stack);
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error syncing expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to sync expense'
      });
    }
  };

  /**
   * Get superannuation expenses from Xero
   */
  public getSuperannuationExpenses = async (req: Request, res: Response): Promise<void> => {
    try {
      const xeroService = getXeroService();

      // Check authentication
      if (!await xeroService.isAuthenticated()) {
        res.status(401).json({
          success: false,
          message: 'Xero not authenticated'
        });
        return;
      }

      const client = xeroService.getClient();
      const tenantId = xeroService.getActiveTenantId();

      if (!client || !tenantId) {
        res.status(400).json({
          success: false,
          message: 'Missing Xero client or tenant ID'
        });
        return;
      }

      // Get days parameter from query (defaults to 90)
      const days = parseInt(req.query.days as string) || 90;

      // Calculate since date based on days parameter
      const sinceDate = new Date();
      sinceDate.setDate(sinceDate.getDate() - days);

      // Create superannuation service instance
      const superannuationService = new SuperannuationService(client);

      try {
        // Fetch superannuation expenses from Xero - with NO fallback to sample data
        const expenses = await superannuationService.getSuperannuationExpensesForDisplay(tenantId, sinceDate);

        res.json({
          success: true,
          data: expenses
        });
      } catch (superError) {
        // Handle specific service errors with appropriate status codes
        console.error('Error in Xero superannuation service:', superError);

        if (superError instanceof Error) {
          // Differentiate between different error types
          if (superError.message.includes('Missing Xero Payroll API scopes')) {
            res.status(403).json({
              success: false,
              message: superError.message,
              code: 'MISSING_SCOPES',
              details: 'Your Xero connection is missing the required payroll scopes. Please reconnect with the appropriate permissions.'
            });
            return;
          } else if (superError.message.includes('No superannuation data available')) {
            res.status(404).json({
              success: false,
              message: superError.message,
              code: 'NO_DATA',
              details: 'No superannuation data was found in your Xero account.'
            });
            return;
          } else if (superError.message.includes('Payroll API not initialized')) {
            res.status(501).json({
              success: false,
              message: superError.message,
              code: 'API_UNAVAILABLE',
              details: 'The Xero Payroll API is not available for your account type or region.'
            });
            return;
          }
        }

        // Generic service error
        res.status(500).json({
          success: false,
          message: superError instanceof Error ? superError.message : 'Unknown superannuation service error',
          code: 'SUPER_ERROR'
        });
      }
    } catch (error) {
      console.error('Error getting superannuation expenses from Xero:', error);

      // For authentication errors, return a specific error code
      if (error instanceof Error && error.message.includes('authentication')) {
        res.status(401).json({
          success: false,
          message: 'Authentication error',
          code: 'AUTH_ERROR'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: 'Failed to get superannuation expenses from Xero',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  /**
   * Convert a superannuation expense to a custom expense
   */
  public convertSuperannuationToExpense = async (req: Request, res: Response): Promise<void> => {
    try {
      const { expense } = req.body;

      if (!expense) {
        res.status(400).json({
          success: false,
          message: 'Missing expense data'
        });
        return;
      }

      // Create expenses repository
      const expensesRepository = new ExpensesRepository();

      // Convert to expense format with appropriate type
      const customExpense: Omit<CustomExpense, 'id'> = {
        name: `Superannuation: ${expense.provider || 'Payment'}`,
        type: 'Superannuation', // Use predefined type for superannuation
        amount: expense.amount,
        date: new Date(expense.paymentDate),
        frequency: expense.frequency as CustomExpense['frequency'],
        source: XERO_SOURCES.SUPERANNUATION, // Mark as coming from Xero
        description: `Superannuation payment from Xero for ${expense.provider || 'employees'}`,
        metadata: {
          isFromXero: true
        }
      };

      // Normalize the date
      const normalizedDate = new Date(customExpense.date);
      normalizedDate.setHours(0, 0, 0, 0);
      customExpense.date = normalizedDate;

      // Create the expense
      const createdExpense = expensesRepository.create(customExpense);

      // Log Xero sync activity
      try {
        console.log('Attempting to log Xero superannuation sync activity...');
        const activity = await activityLogger.logXeroSyncCompleted('superannuation', 1);
        console.log('Successfully logged Xero superannuation sync activity:', activity);
      } catch (activityError) {
        console.error('Error logging Xero superannuation sync activity:', activityError);
        console.error('Activity error details:', {
          message: activityError?.message,
          stack: activityError?.stack,
          name: activityError?.name
        });
        // Don't fail the request if activity logging fails
      }

      res.json({
        success: true,
        data: createdExpense
      });
    } catch (error) {
      console.error('Error converting superannuation to expense:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to convert superannuation to expense'
      });
    }
  };
}