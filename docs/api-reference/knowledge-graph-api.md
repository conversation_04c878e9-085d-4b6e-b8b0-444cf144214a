# Knowledge Graph API

## Overview

The Knowledge Graph API provides access to relationship visualization data for the business network. It aggregates data from multiple repositories to create an interactive graph of companies, contacts, deals, projects, and estimates.

## Base URL

```
/api/knowledge-graph
```

## Authentication

All endpoints require session-based authentication. Users must be logged in to access knowledge graph data.

## Endpoints

### Get Complete Knowledge Graph

Retrieves the complete knowledge graph with all entities and relationships.

```http
GET /api/knowledge-graph
```

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `includeDeleted` | boolean | `false` | Include soft-deleted entities |
| `entityTypes` | string | `"company,contact,deal,project"` | Comma-separated list of entity types to include |
| `maxNodes` | integer | `1000` | Maximum number of nodes to return (max: 2000) |

#### Entity Types

- `company` - Business companies
- `contact` - Individual contacts
- `deal` - Sales opportunities
- `project` - Harvest projects
- `estimate` - Project estimates

#### Example Request

```http
GET /api/knowledge-graph?entityTypes=company,contact&maxNodes=500&includeDeleted=false
```

#### Example Response

```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "company-123",
        "label": "Acme Corp",
        "type": "company",
        "metadata": {
          "industry": "Technology",
          "size": "Medium",
          "radarState": "Strategy",
          "priority": "High"
        }
      },
      {
        "id": "contact-456",
        "label": "John Smith",
        "type": "contact",
        "metadata": {
          "email": "<EMAIL>",
          "jobTitle": "CEO"
        }
      }
    ],
    "links": [
      {
        "source": "contact-456",
        "target": "company-123",
        "type": "works_at",
        "strength": 5,
        "label": "works at",
        "metadata": {
          "role": "CEO",
          "isPrimary": true
        }
      }
    ],
    "stats": {
      "totalNodes": 2,
      "totalLinks": 1,
      "nodeTypes": {
        "company": 1,
        "contact": 1
      },
      "linkTypes": {
        "works_at": 1
      }
    }
  }
}
```

### Get Entity Subgraph

Retrieves a focused subgraph centered on a specific entity.

```http
GET /api/knowledge-graph/:entityId
```

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `entityId` | string | The ID of the central entity |

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `entityType` | string | `"company"` | Type of the central entity |
| `depth` | integer | `2` | Relationship depth to explore |

#### Example Request

```http
GET /api/knowledge-graph/company-123?entityType=company&depth=2
```

#### Example Response

```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "company-123",
        "label": "Acme Corp",
        "type": "company",
        "metadata": {
          "industry": "Technology",
          "size": "Medium"
        }
      }
    ],
    "links": [],
    "stats": {
      "totalNodes": 1,
      "totalLinks": 0,
      "nodeTypes": {
        "company": 1
      },
      "linkTypes": {}
    }
  }
}
```

## Data Structure

### Node Object

```typescript
interface KnowledgeGraphNode {
  id: string;                    // Unique identifier
  label: string;                 // Display name
  type: 'company' | 'contact' | 'deal' | 'project' | 'estimate';
  group?: string;                // Optional grouping
  metadata?: Record<string, any>; // Type-specific data
}
```

### Link Object

```typescript
interface KnowledgeGraphLink {
  source: string;                // Source node ID
  target: string;                // Target node ID
  type: string;                  // Relationship type
  strength: number;              // Relationship strength (1-5)
  label?: string;                // Display label
  metadata?: Record<string, any>; // Additional data
}
```

### Stats Object

```typescript
interface GraphStats {
  totalNodes: number;
  totalLinks: number;
  nodeTypes: Record<string, number>;  // Count by node type
  linkTypes: Record<string, number>;  // Count by link type
}
```

## Relationship Types

### Company Relationships

- `parent_company` - Parent-child company hierarchy
- `subsidiary` - Subsidiary relationships
- `partner` - Business partnerships

### Contact Relationships

- `works_at` - Employment relationship
- `reports_to` - Management hierarchy
- `colleague` - Professional relationships
- `knows` - General professional connections

### Deal Relationships

- `belongs_to` - Deal belongs to company
- `decision_maker` - Contact is decision maker for deal
- `influencer` - Contact influences deal
- `champion` - Contact champions deal

### Project Relationships

- `belongs_to` - Project belongs to company
- `project_manager` - Contact manages project
- `team_member` - Contact is team member
- `originated_from` - Project originated from deal

### Estimate Relationships

- `has_estimate` - Deal has estimate
- `estimate_for` - Estimate is for deal

## Performance Considerations

### Node Limits

- Default limit: 1,000 nodes
- Maximum limit: 2,000 nodes
- Large graphs may impact browser performance

### Caching

- Response cached for 5 minutes
- Cache invalidated when underlying data changes
- Use browser caching for better performance

### Filtering

- Use entity type filtering to reduce data volume
- Subgraph queries provide focused views
- Consider depth limitations for large networks

## Error Responses

### Standard Error Format

```json
{
  "success": false,
  "error": "Error description",
  "message": "Detailed error message"
}
```

### Common Error Codes

| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | Bad Request | Invalid query parameters |
| 401 | Unauthorized | Authentication required |
| 404 | Not Found | Entity not found |
| 500 | Internal Server Error | Server processing error |

## Rate Limiting

- No specific rate limits
- Uses standard application session limits
- Large queries may be slower due to data aggregation

## Usage Examples

### Frontend Integration

```typescript
import { fetchFromApi } from '../api/utils';

// Get complete graph
const graphData = await fetchFromApi('/api/knowledge-graph?entityTypes=company,contact');

// Get entity subgraph
const subgraph = await fetchFromApi('/api/knowledge-graph/company-123?depth=2');
```

### React Query Integration

```typescript
import { useQuery } from 'react-query';

function useKnowledgeGraph(entityTypes: string[]) {
  return useQuery(
    ['knowledgeGraph', entityTypes],
    () => fetchFromApi(`/api/knowledge-graph?entityTypes=${entityTypes.join(',')}`),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000 // 10 minutes
    }
  );
}
```

## Technical Implementation

### Repository Pattern

The Knowledge Graph API uses specialized repositories that don't extend BaseRepository:

- `KnowledgeGraphRepository` - Main aggregation logic
- Entity repositories - Source data providers
- Relationship repositories - Connection data

### Data Aggregation

1. **Entity Collection** - Gather entities from multiple repositories
2. **Relationship Mapping** - Build connections between entities
3. **Filtering** - Apply node limits and entity type filters
4. **Statistics** - Calculate graph metrics

### Performance Optimization

- Lazy loading of relationships
- Efficient SQL queries with proper indexing
- Memory-conscious data structures
- Strategic caching at multiple levels

## See Also

- [Knowledge Graph Visualization Feature](../features/knowledge-graph-visualization.md)
- [CRM Network API](./crm-network-api.md)
- [Data Model Documentation](../technical/data-model/README.md)