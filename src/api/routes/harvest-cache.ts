/**
 * Harvest Invoice Cache API Routes
 *
 * Provides endpoints for managing Harvest invoice cache data.
 */

import { Router, Request, Response } from 'express';
import { getHarvestService } from '../../services/harvest';

const router = Router();

/**
 * GET /api/harvest-cache/stats
 * Get cache statistics
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const harvestService = getHarvestService();
    const stats = harvestService.invoiceCache.getCacheStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error getting cache stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cache statistics'
    });
  }
});

/**
 * GET /api/harvest-cache/totals
 * Get all cached invoice totals
 */
router.get('/totals', async (req: Request, res: Response) => {
  try {
    const harvestService = getHarvestService();
    const totals = harvestService.invoiceCache.getAllCachedInvoiceTotals();
    res.json({
      success: true,
      data: totals
    });
  } catch (error) {
    console.error('Error getting cached totals:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cached invoice totals'
    });
  }
});

/**
 * GET /api/harvest-cache/client/:clientId
 * Get cached invoice total for specific client
 */
router.get('/client/:clientId', async (req: Request, res: Response) => {
  try {
    const clientId = parseInt(req.params.clientId);
    if (isNaN(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID'
      });
    }

    const harvestService = getHarvestService();
    const total = harvestService.invoiceCache.getCachedInvoiceTotal(clientId);
    res.json({
      success: true,
      data: total
    });
  } catch (error) {
    console.error('Error getting cached total for client:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get cached invoice total'
    });
  }
});

/**
 * POST /api/harvest-cache/refresh
 * Refresh invoice cache from Harvest API
 */
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { clientId } = req.body;

    // Validate clientId if provided
    if (clientId !== undefined && (typeof clientId !== 'number' || isNaN(clientId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID'
      });
    }

    console.log(`Refreshing Harvest invoice cache${clientId ? ` for client ${clientId}` : ''}`);

    const harvestService = getHarvestService();
    const refreshed = await harvestService.invoiceCache.refreshInvoiceCache(clientId);

    res.json({
      success: true,
      message: `Cache refreshed for ${refreshed.length} client${refreshed.length !== 1 ? 's' : ''}`,
      data: refreshed
    });
  } catch (error) {
    console.error('Error refreshing cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to refresh invoice cache'
    });
  }
});

/**
 * DELETE /api/harvest-cache/expired
 * Clear expired cache entries
 */
router.delete('/expired', async (req: Request, res: Response) => {
  try {
    const harvestService = getHarvestService();
    const cleared = harvestService.invoiceCache.clearExpiredCache();
    res.json({
      success: true,
      message: `Cleared ${cleared} expired cache entries`,
      data: { entriesCleared: cleared }
    });
  } catch (error) {
    console.error('Error clearing expired cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear expired cache'
    });
  }
});

/**
 * GET /api/harvest-cache/client/:clientId/fresh
 * Get fresh invoice total for specific client (bypasses cache)
 */
router.get('/client/:clientId/fresh', async (req: Request, res: Response) => {
  try {
    const clientId = parseInt(req.params.clientId);
    if (isNaN(clientId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid client ID'
      });
    }

    const harvestService = getHarvestService();
    const total = await harvestService.invoiceCache.getInvoiceTotal(clientId, true);
    res.json({
      success: true,
      data: total
    });
  } catch (error) {
    console.error('Error getting fresh total for client:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get fresh invoice total'
    });
  }
});

export default router;
