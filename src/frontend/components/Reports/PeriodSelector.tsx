import React, { useState } from "react";
import { format } from "date-fns";
import { useLoading } from "../../contexts/LoadingContext";

interface PeriodSelectorProps {
  timePeriod: string;
  setTimePeriod: (period: string) => void;
  isDropdownOpen: boolean;
  setIsDropdownOpen: (isOpen: boolean) => void;
  goToPreviousPeriod: () => void;
  goToNextPeriod: () => void;
  getPeriodTitle: () => string;
  customStartDate: string;
  setCustomStartDate: (date: string) => void;
  customEndDate: string;
  setCustomEndDate: (date: string) => void;
  fetchUtilizationData: () => void;
  TIME_PERIODS: Record<string, string>;
}

/**
 * A component for selecting and navigating time periods
 * Styled to match Harvest's UI
 */
const PeriodSelector: React.FC<PeriodSelectorProps> = ({
  timePeriod,
  setTimePeriod,
  isDropdownOpen,
  setIsDropdownOpen,
  goToPreviousPeriod,
  goToNextPeriod,
  getPeriodTitle,
  customStartDate,
  setCustomStartDate,
  customEndDate,
  setCustomEndDate,
  fetchUtilizationData,
  TIME_PERIODS,
}) => {
  // Access the loading context to know when we're making API calls
  const { isLoading, loadingType } = useLoading();

  // Check if we're specifically loading data from Harvest
  const isLoadingHarvestData = isLoading && loadingType === "harvest";

  // State for period dropdown
  const [isPeriodDropdownOpen, setIsPeriodDropdownOpen] = useState(false);

  // Get the display name for the current time period
  const getTimeFrameDisplayName = () => {
    switch (timePeriod) {
      case TIME_PERIODS.WEEK:
        return "Week";
      case TIME_PERIODS.SEMIMONTH:
        return "Semimonth";
      case TIME_PERIODS.MONTH:
        return "Month";
      case TIME_PERIODS.QUARTER:
        return "Quarter";
      case TIME_PERIODS.FISCAL_YEAR:
        return "Fiscal year";
      case TIME_PERIODS.YEAR:
        return "Year";
      case TIME_PERIODS.ALL_TIME:
        return "All time";
      case TIME_PERIODS.CUSTOM:
        return "Custom";
      default:
        return "Select period";
    }
  };

  return (
    <div className="space-y-4">
      {/* Top row with period title and navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <button
            onClick={goToPreviousPeriod}
            disabled={
              timePeriod === TIME_PERIODS.CUSTOM ||
              timePeriod === TIME_PERIODS.ALL_TIME ||
              isLoadingHarvestData
            }
            className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Previous period"
          >
            <svg
              className="w-5 h-5 text-gray-600 dark:text-gray-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>

          <button
            onClick={goToNextPeriod}
            disabled={
              timePeriod === TIME_PERIODS.CUSTOM ||
              timePeriod === TIME_PERIODS.ALL_TIME ||
              isLoadingHarvestData
            }
            className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Next period"
          >
            <svg
              className="w-5 h-5 text-gray-600 dark:text-gray-300"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {isLoadingHarvestData ? (
              <span className="flex items-center">
                <span className="animate-spin h-4 w-4 mr-2 border-t-2 border-b-2 border-blue-500 rounded-full"></span>
                Loading...
              </span>
            ) : (
              getPeriodTitle()
            )}
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <button
            className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700 relative flex items-center justify-center"
            onClick={fetchUtilizationData}
            disabled={isLoadingHarvestData}
          >
            {isLoadingHarvestData ? (
              <>
                <span className="animate-spin h-3 w-3 mr-1 border-t-2 border-b-2 border-white rounded-full"></span>
                <span>Loading...</span>
              </>
            ) : (
              "Update report"
            )}
          </button>

          {/* Time period dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsPeriodDropdownOpen(!isPeriodDropdownOpen)}
              disabled={isLoadingHarvestData}
              className="px-3 py-1.5 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-md border border-gray-300 dark:border-gray-600 flex items-center space-x-1 disabled:opacity-70 disabled:cursor-not-allowed"
            >
              <span>{getTimeFrameDisplayName()}</span>
              <svg
                className="w-3 h-3 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {isPeriodDropdownOpen && (
              <div className="absolute right-0 z-10 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700">
                <ul className="py-1">
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.WEEK);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Week
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.SEMIMONTH);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Semimonth
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.MONTH);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Month
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.QUARTER);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Quarter
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.FISCAL_YEAR);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Fiscal year
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.YEAR);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Year
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.ALL_TIME);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      All time
                    </button>
                  </li>
                  <li>
                    <button
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                      onClick={() => {
                        setTimePeriod(TIME_PERIODS.CUSTOM);
                        setIsPeriodDropdownOpen(false);
                      }}
                    >
                      Custom
                    </button>
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Custom date range inputs - only shown when Custom is selected */}
      {timePeriod === TIME_PERIODS.CUSTOM && (
        <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-700 dark:text-gray-300">
              View report from
            </span>
            <input
              type="date"
              className="rounded-md border-gray-300 shadow-sm focus:border-secondary focus:ring focus:ring-secondary focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed text-sm py-1 px-2"
              value={customStartDate}
              onChange={(e) => setCustomStartDate(e.target.value)}
              disabled={isLoadingHarvestData}
            />
            <span className="text-gray-700 dark:text-gray-300">to</span>
            <input
              type="date"
              className="rounded-md border-gray-300 shadow-sm focus:border-secondary focus:ring focus:ring-secondary focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed text-sm py-1 px-2"
              value={customEndDate}
              onChange={(e) => setCustomEndDate(e.target.value)}
              disabled={isLoadingHarvestData}
            />
            <button
              className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-600 dark:hover:bg-blue-700"
              onClick={fetchUtilizationData}
              disabled={
                isLoadingHarvestData || !customStartDate || !customEndDate
              }
            >
              {isLoadingHarvestData ? (
                <span className="flex items-center">
                  <span className="animate-spin h-3 w-3 mr-1 border-t-2 border-b-2 border-white rounded-full"></span>
                  Loading...
                </span>
              ) : (
                "Update report"
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PeriodSelector;
