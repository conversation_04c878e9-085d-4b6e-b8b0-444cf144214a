import express from 'express';
import dealsRouter from './deals';
import contactsRouter from './contacts';
import companiesRouter from './companies';
import relationshipsRouter from './relationships';
import networkRouter from './network';
import conversationsRouter from './conversations';

const router = express.Router();

// Mount sub-routers
router.use('/deals', dealsRouter);
router.use('/contacts', contactsRouter);
router.use('/companies', companiesRouter);
router.use('/network', networkRouter);
router.use('/notes', conversationsRouter);

// Mount relationship routes at the root level to maintain existing API structure
router.use('/', relationshipsRouter);

export default router;