# Invoices API

This directory contains documentation for working with invoices in the Harvest API v2:

1. [Invoice Item Categories](01_invoice_item_categories.md)
   - Managing invoice item categories
   - Category types and properties
   - CRUD operations

2. [Invoice Messages](02_invoice_messages.md)
   - Sending invoice messages
   - Message templates
   - State changes via messages
   - Email notifications

3. [Invoice Payments](03_invoice_payments.md)
   - Recording payments
   - Payment options
   - Payment management
   - Thank you messages

4. [Invoices](04_invoices.md)
   - Main invoice operations
   - Creating invoices (free-form and from time/expenses)
   - Managing invoice states
   - Line items management
   - Invoice properties and attributes