import { KnowledgeGraphRepository } from '../../../../src/api/repositories/knowledge-graph-repository';

describe('Knowledge Graph Performance Tests', () => {
  let repo: KnowledgeGraphRepository;

  beforeEach(() => {
    repo = new KnowledgeGraphRepository();
  });

  describe('Caching Functionality', () => {
    it('should cache knowledge graph results', async () => {
      // First call - should hit database
      const startTime1 = Date.now();
      const result1 = await repo.getKnowledgeGraph({
        entityTypes: ['company', 'contact'],
        maxNodes: 100
      });
      const duration1 = Date.now() - startTime1;

      // Second call with same parameters - should hit cache
      const startTime2 = Date.now();
      const result2 = await repo.getKnowledgeGraph({
        entityTypes: ['company', 'contact'],
        maxNodes: 100
      });
      const duration2 = Date.now() - startTime2;

      // Results should be identical
      expect(result1).toEqual(result2);
      
      // Second call should be significantly faster (at least 50% faster)
      expect(duration2).toBeLessThan(duration1 * 0.5);
      
      console.log(`First call: ${duration1}ms, Cached call: ${duration2}ms`);
    });

    it('should cache entity subgraph results', async () => {
      // Create a test company to fetch
      const testEntityId = 'test-company-id';
      
      // First call - should hit database
      const startTime1 = Date.now();
      const result1 = await repo.getEntitySubgraph(testEntityId, 'company', 2);
      const duration1 = Date.now() - startTime1;

      // Second call with same parameters - should hit cache
      const startTime2 = Date.now();
      const result2 = await repo.getEntitySubgraph(testEntityId, 'company', 2);
      const duration2 = Date.now() - startTime2;

      // Results should be identical
      expect(result1).toEqual(result2);
      
      // Second call should be significantly faster (or both instant if no data)
      if (duration1 > 0) {
        expect(duration2).toBeLessThan(duration1 * 0.5);
      } else {
        // Both were instant (likely empty result), which is fine
        expect(duration2).toBeLessThanOrEqual(duration1);
      }
    });

    it('should handle concurrent requests efficiently', async () => {
      const options = {
        entityTypes: ['company', 'deal'] as Array<'company' | 'deal'>,
        maxNodes: 50
      };

      // Make 5 concurrent requests with same parameters
      const promises = Array(5).fill(null).map(() => 
        repo.getKnowledgeGraph(options)
      );

      const startTime = Date.now();
      const results = await Promise.all(promises);
      const duration = Date.now() - startTime;

      // All results should be identical
      for (let i = 1; i < results.length; i++) {
        expect(results[i]).toEqual(results[0]);
      }

      // Should be fast due to request deduplication
      console.log(`5 concurrent requests completed in ${duration}ms`);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });

  describe('Error Handling', () => {
    it('should handle missing tables gracefully', async () => {
      // Even if some tables don't exist, should return valid structure
      const result = await repo.getKnowledgeGraph({
        entityTypes: ['company', 'contact', 'deal', 'project', 'estimate'],
        includeDeleted: false
      });

      expect(result).toBeDefined();
      expect(result.nodes).toBeInstanceOf(Array);
      expect(result.links).toBeInstanceOf(Array);
      expect(result.stats).toBeDefined();
      expect(result.stats.totalNodes).toBeGreaterThanOrEqual(0);
      expect(result.stats.totalLinks).toBeGreaterThanOrEqual(0);
    });

    it('should handle search queries without errors', async () => {
      const result = await repo.getKnowledgeGraph({
        searchTerm: 'test',
        entityTypes: ['company', 'contact']
      });

      expect(result).toBeDefined();
      expect(result.nodes).toBeInstanceOf(Array);
      // Should only include nodes that match search term (if any nodes exist)
      if (result.nodes.length > 0) {
        result.nodes.forEach(node => {
          expect(
            node.label.toLowerCase().includes('test') ||
            (node.metadata && Object.values(node.metadata).some(v => 
              typeof v === 'string' && v.toLowerCase().includes('test')
            ))
          ).toBeTruthy();
        });
      }
    });

    it('should handle node degree filtering', async () => {
      const result = await repo.getKnowledgeGraph({
        minNodeDegree: 2,
        entityTypes: ['company', 'contact', 'deal']
      });

      expect(result).toBeDefined();
      
      // Calculate node degrees from links
      const nodeDegrees = new Map<string, number>();
      result.links.forEach(link => {
        nodeDegrees.set(link.source, (nodeDegrees.get(link.source) || 0) + 1);
        nodeDegrees.set(link.target, (nodeDegrees.get(link.target) || 0) + 1);
      });

      // All nodes should have degree >= 2 (if any nodes exist after filtering)
      if (result.nodes.length > 0) {
        result.nodes.forEach(node => {
          const degree = nodeDegrees.get(node.id) || 0;
          expect(degree).toBeGreaterThanOrEqual(2);
        });
      } else {
        // No nodes met the criteria, which is valid
        expect(result.nodes.length).toBe(0);
      }
    });
  });

  describe('Query Optimization', () => {
    it('should execute queries efficiently with indexes', async () => {
      // Test that indexed queries are fast
      const startTime = Date.now();
      
      const result = await repo.getKnowledgeGraph({
        searchTerm: 'tech',
        entityTypes: ['company'],
        includeDeleted: false,
        maxNodes: 1000
      });
      
      const duration = Date.now() - startTime;
      
      console.log(`Indexed search query completed in ${duration}ms`);
      expect(duration).toBeLessThan(500); // Should be fast with indexes
      expect(result.nodes).toBeInstanceOf(Array);
    });

    it('should handle pagination efficiently', async () => {
      // Get first page
      const page1 = await repo.getKnowledgeGraph({
        entityTypes: ['company', 'contact'],
        pagination: { page: 1, pageSize: 10 }
      });

      expect(page1.nodes.length).toBeLessThanOrEqual(10);
      
      // Get second page
      const page2 = await repo.getKnowledgeGraph({
        entityTypes: ['company', 'contact'],
        pagination: { page: 2, pageSize: 10 }
      });

      // Pages should have different content
      const page1Ids = new Set(page1.nodes.map(n => n.id));
      const page2Ids = new Set(page2.nodes.map(n => n.id));
      
      // No overlap between pages
      page2Ids.forEach(id => {
        expect(page1Ids.has(id)).toBeFalsy();
      });
    });
  });
});