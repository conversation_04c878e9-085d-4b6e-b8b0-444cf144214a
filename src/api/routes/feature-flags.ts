import express from 'express';
import { featureFlagsService } from '../../services/feature-flags-service';
import { FeatureFlag } from '../../types/feature-flags';

const router = express.Router();

/**
 * @route GET /api/feature-flags
 * @desc Get all feature flags and their current status
 * @access Private
 */
router.get('/', async (req, res) => {
  try {
    const flags = featureFlagsService.getAllFlags();
    
    res.json({
      success: true,
      data: flags
    });
  } catch (error) {
    console.error('Error fetching feature flags:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch feature flags',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route GET /api/feature-flags/:flag
 * @desc Get a specific feature flag status
 * @access Private
 */
router.get('/:flag', async (req, res) => {
  try {
    const { flag } = req.params;
    
    // Validate flag exists
    if (!(flag in FeatureFlag)) {
      return res.status(404).json({
        success: false,
        error: 'Feature flag not found',
        message: `Unknown feature flag: ${flag}`
      });
    }
    
    const enabled = featureFlagsService.isEnabled(flag as FeatureFlag);
    const config = featureFlagsService.getConfig(flag as FeatureFlag);
    
    res.json({
      success: true,
      data: {
        flag,
        enabled,
        config
      }
    });
  } catch (error) {
    console.error('Error fetching feature flag:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch feature flag',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route PUT /api/feature-flags/:flag
 * @desc Update a feature flag status
 * @access Private (Admin only in production)
 */
router.put('/:flag', async (req, res) => {
  try {
    const { flag } = req.params;
    const { enabled } = req.body;
    const updatedBy = req.user?.email || 'system';
    
    // Validate flag exists
    if (!(flag in FeatureFlag)) {
      return res.status(404).json({
        success: false,
        error: 'Feature flag not found',
        message: `Unknown feature flag: ${flag}`
      });
    }
    
    // Validate enabled is boolean
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request',
        message: 'The "enabled" field must be a boolean value'
      });
    }
    
    // Update the flag
    if (enabled) {
      featureFlagsService.enable(flag as FeatureFlag, updatedBy);
    } else {
      featureFlagsService.disable(flag as FeatureFlag, updatedBy);
    }
    
    res.json({
      success: true,
      data: {
        flag,
        enabled,
        updatedBy,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating feature flag:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update feature flag',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route POST /api/feature-flags/:flag/toggle
 * @desc Toggle a feature flag
 * @access Private (Admin only in production)
 */
router.post('/:flag/toggle', async (req, res) => {
  try {
    const { flag } = req.params;
    const updatedBy = req.user?.email || 'system';
    
    // Validate flag exists
    if (!(flag in FeatureFlag)) {
      return res.status(404).json({
        success: false,
        error: 'Feature flag not found',
        message: `Unknown feature flag: ${flag}`
      });
    }
    
    // Toggle the flag
    const newValue = featureFlagsService.toggle(flag as FeatureFlag, updatedBy);
    
    res.json({
      success: true,
      data: {
        flag,
        enabled: newValue,
        updatedBy,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error toggling feature flag:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to toggle feature flag',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route POST /api/feature-flags/:flag/reset
 * @desc Reset a feature flag to its default value
 * @access Private (Admin only in production)
 */
router.post('/:flag/reset', async (req, res) => {
  try {
    const { flag } = req.params;
    const updatedBy = req.user?.email || 'system';
    
    // Validate flag exists
    if (!(flag in FeatureFlag)) {
      return res.status(404).json({
        success: false,
        error: 'Feature flag not found',
        message: `Unknown feature flag: ${flag}`
      });
    }
    
    // Reset the flag
    featureFlagsService.reset(flag as FeatureFlag, updatedBy);
    
    const enabled = featureFlagsService.isEnabled(flag as FeatureFlag);
    const config = featureFlagsService.getConfig(flag as FeatureFlag);
    
    res.json({
      success: true,
      data: {
        flag,
        enabled,
        config,
        updatedBy,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error resetting feature flag:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset feature flag',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route POST /api/feature-flags/reset-all
 * @desc Reset all feature flags to their default values
 * @access Private (Admin only in production)
 */
router.post('/reset-all', async (req, res) => {
  try {
    const updatedBy = req.user?.email || 'system';
    
    // Reset all flags
    featureFlagsService.resetAll(updatedBy);
    
    // Get updated flags
    const flags = featureFlagsService.getAllFlags();
    
    res.json({
      success: true,
      data: {
        flags,
        updatedBy,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error resetting all feature flags:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset all feature flags',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route GET /api/feature-flags/category/:category
 * @desc Get feature flags by category
 * @access Private
 */
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    
    // Validate category
    if (!['experimental', 'beta', 'stable'].includes(category)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid category',
        message: 'Category must be one of: experimental, beta, stable'
      });
    }
    
    const flags = featureFlagsService.getFlagsByCategory(category as any);
    
    res.json({
      success: true,
      data: {
        category,
        flags
      }
    });
  } catch (error) {
    console.error('Error fetching feature flags by category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch feature flags by category',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route POST /api/feature-flags/experimental/:action
 * @desc Enable or disable all experimental features
 * @access Private (Admin only in production)
 */
router.post('/experimental/:action', async (req, res) => {
  try {
    const { action } = req.params;
    const updatedBy = req.user?.email || 'system';
    
    // Validate action
    if (!['enable', 'disable'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid action',
        message: 'Action must be either "enable" or "disable"'
      });
    }
    
    // Perform action
    if (action === 'enable') {
      featureFlagsService.enableExperimental(updatedBy);
    } else {
      featureFlagsService.disableExperimental(updatedBy);
    }
    
    // Get updated experimental flags
    const flags = featureFlagsService.getFlagsByCategory('experimental');
    
    res.json({
      success: true,
      data: {
        action,
        category: 'experimental',
        flags,
        updatedBy,
        updatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error updating experimental features:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update experimental features',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;