import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/repositories/deal-repository');
jest.mock('../../../../src/api/repositories/relationships/deal-estimate-repository');
jest.mock('../../../../src/api/repositories/relationships/contact-role-repository');
jest.mock('../../../../src/api/repositories/note-repository');
jest.mock('../../../../src/api/services/activity-service');
jest.mock('../../../../src/utils/backend-logger');

describe('CRM Deals API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      user: { id: 'user-123', email: '<EMAIL>' },
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/crm/deals', () => {
    it('should fetch all deals with optional filters', async () => {
      mockRequest.query = {
        stage: 'negotiation',
        company_id: '1',
        min_amount: '10000',
        max_amount: '100000',
        limit: '50',
        offset: '0',
      };
      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      const mockDeals = [
        {
          id: 1,
          name: 'Enterprise Deal',
          amount: 50000,
          stage: 'negotiation',
          probability: 0.75,
          close_date: '2024-03-31',
          company_id: 1,
          company_name: 'Acme Corp',
          owner_id: 'user-123',
          hubspot_id: 'hs-deal-123',
          created_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 2,
          name: 'Mid-Market Deal',
          amount: 25000,
          stage: 'negotiation',
          probability: 0.6,
          close_date: '2024-02-28',
          company_id: 1,
          company_name: 'Acme Corp',
          owner_id: 'user-456',
          hubspot_id: 'hs-deal-456',
          created_at: '2024-01-05T00:00:00Z',
        },
      ];
      DealRepository.prototype.getAllDeals = jest.fn().mockResolvedValue({
        deals: mockDeals,
        total: 2,
      });

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealRepository.prototype.getAllDeals).toHaveBeenCalledWith({
        stage: 'negotiation',
        company_id: 1,
        min_amount: 10000,
        max_amount: 100000,
        limit: 50,
        offset: 0,
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        deals: mockDeals,
        total: 2,
      });
    });

    it('should fetch deals by pipeline stage', async () => {
      mockRequest.query = { pipeline_view: 'true' };
      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      const mockPipeline = {
        qualified: [
          { id: 3, name: 'Small Deal', amount: 10000, stage: 'qualified' },
        ],
        proposal: [
          { id: 4, name: 'Medium Deal', amount: 30000, stage: 'proposal' },
        ],
        negotiation: [
          { id: 1, name: 'Enterprise Deal', amount: 50000, stage: 'negotiation' },
        ],
        closed_won: [
          { id: 5, name: 'Completed Deal', amount: 75000, stage: 'closed_won' },
        ],
        closed_lost: [],
      };
      DealRepository.prototype.getDealsByStage = jest.fn().mockResolvedValue(mockPipeline);

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealRepository.prototype.getDealsByStage).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockPipeline);
    });
  });

  describe('GET /api/crm/deals/:id', () => {
    it('should fetch a deal by ID with relationships', async () => {
      mockRequest.params = { id: '1' };
      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      const DealEstimateRepository = (await import('../../../../src/api/repositories/relationships/deal-estimate-repository')).default;
      const ContactRoleRepository = (await import('../../../../src/api/repositories/relationships/contact-role-repository')).default;
      const NoteRepository = (await import('../../../../src/api/repositories/note-repository')).default;
      
      const mockDeal = {
        id: 1,
        name: 'Enterprise Deal',
        amount: 50000,
        stage: 'negotiation',
        probability: 0.75,
        close_date: '2024-03-31',
        company_id: 1,
        company_name: 'Acme Corp',
        description: 'Large enterprise software deal',
        next_steps: 'Schedule final negotiation meeting',
      };
      const mockEstimates = [
        {
          estimate_id: 1,
          estimate_name: 'Q1 2024 Implementation',
          estimate_amount: 50000,
          is_primary: true,
        },
      ];
      const mockContacts = [
        {
          contact_id: 1,
          first_name: 'John',
          last_name: 'Doe',
          role: 'Decision Maker',
          permissions: ['view', 'edit', 'approve'],
        },
      ];
      const mockNotes = [
        {
          id: 1,
          content: 'Initial meeting went well',
          created_at: '2024-01-01T00:00:00Z',
          created_by: 'user-123',
        },
      ];

      DealRepository.prototype.getDealById = jest.fn().mockResolvedValue(mockDeal);
      DealEstimateRepository.prototype.getDealEstimates = jest.fn().mockResolvedValue(mockEstimates);
      ContactRoleRepository.prototype.getDealContacts = jest.fn().mockResolvedValue(mockContacts);
      NoteRepository.prototype.getNotesByTarget = jest.fn().mockResolvedValue(mockNotes);

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealRepository.prototype.getDealById).toHaveBeenCalledWith(1);
      expect(DealEstimateRepository.prototype.getDealEstimates).toHaveBeenCalledWith(1);
      expect(ContactRoleRepository.prototype.getDealContacts).toHaveBeenCalledWith(1);
      expect(NoteRepository.prototype.getNotesByTarget).toHaveBeenCalledWith('deal', 1);
      expect(mockResponse.json).toHaveBeenCalledWith({
        ...mockDeal,
        estimates: mockEstimates,
        contacts: mockContacts,
        notes: mockNotes,
      });
    });

    it('should handle not found errors', async () => {
      mockRequest.params = { id: '999' };
      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      DealRepository.prototype.getDealById = jest.fn().mockResolvedValue(null);

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Deal not found' });
    });
  });

  describe('POST /api/crm/deals', () => {
    it('should create a new deal', async () => {
      mockRequest.body = {
        name: 'New Enterprise Deal',
        amount: 100000,
        stage: 'qualified',
        probability: 0.25,
        close_date: '2024-06-30',
        company_id: 1,
        description: 'Potential large enterprise deal',
        next_steps: 'Schedule discovery call',
        custom_fields: {
          deal_source: 'Referral',
          competitor: 'CompetitorX',
        },
      };

      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockCreatedDeal = { id: 6, ...mockRequest.body, owner_id: 'user-123' };
      DealRepository.prototype.createDeal = jest.fn().mockResolvedValue(mockCreatedDeal);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealRepository.prototype.createDeal).toHaveBeenCalledWith({
        ...mockRequest.body,
        owner_id: 'user-123',
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'deal_created',
        target_type: 'deal',
        target_id: '6',
        metadata: {
          deal_name: 'New Enterprise Deal',
          amount: 100000,
          stage: 'qualified',
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockCreatedDeal);
    });

    it('should handle validation errors', async () => {
      mockRequest.body = {
        // Missing required fields: name, amount, stage
        close_date: '2024-06-30',
      };

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing required fields: name, amount, stage',
      });
    });
  });

  describe('PUT /api/crm/deals/:id', () => {
    it('should update a deal', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        amount: 75000,
        stage: 'proposal',
        probability: 0.9,
        next_steps: 'Send final proposal',
      };

      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockOriginalDeal = {
        id: 1,
        amount: 50000,
        stage: 'negotiation',
        probability: 0.75,
      };
      const mockUpdatedDeal = { id: 1, ...mockRequest.body };
      DealRepository.prototype.getDealById = jest.fn().mockResolvedValue(mockOriginalDeal);
      DealRepository.prototype.updateDeal = jest.fn().mockResolvedValue(mockUpdatedDeal);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.put
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealRepository.prototype.updateDeal).toHaveBeenCalledWith(1, mockRequest.body);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'deal_updated',
        target_type: 'deal',
        target_id: '1',
        metadata: {
          changes: ['amount', 'stage', 'probability', 'next_steps'],
          stage_change: { from: 'negotiation', to: 'proposal' },
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUpdatedDeal);
    });
  });

  describe('DELETE /api/crm/deals/:id', () => {
    it('should delete a deal', async () => {
      mockRequest.params = { id: '1' };

      const DealRepository = (await import('../../../../src/api/repositories/deal-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      DealRepository.prototype.deleteDeal = jest.fn().mockResolvedValue(true);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealRepository.prototype.deleteDeal).toHaveBeenCalledWith(1);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'deal_deleted',
        target_type: 'deal',
        target_id: '1',
        metadata: {},
      });
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
    });
  });

  describe('POST /api/crm/deals/:id/estimates', () => {
    it('should link an estimate to a deal', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        estimate_id: 2,
        is_primary: true,
      };

      const DealEstimateRepository = (await import('../../../../src/api/repositories/relationships/deal-estimate-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockRelationship = {
        deal_id: 1,
        estimate_id: 2,
        is_primary: true,
      };
      DealEstimateRepository.prototype.linkEstimateToDeal = jest.fn().mockResolvedValue(mockRelationship);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/estimates' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(DealEstimateRepository.prototype.linkEstimateToDeal).toHaveBeenCalledWith({
        deal_id: 1,
        estimate_id: 2,
        is_primary: true,
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_linked_to_deal',
        target_type: 'deal',
        target_id: '1',
        metadata: {
          estimate_id: 2,
          is_primary: true,
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockRelationship);
    });
  });

  describe('POST /api/crm/deals/:id/contacts', () => {
    it('should add a contact to a deal', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        contact_id: 3,
        role: 'Technical Evaluator',
        permissions: ['view', 'comment'],
      };

      const ContactRoleRepository = (await import('../../../../src/api/repositories/relationships/contact-role-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockRole = {
        deal_id: 1,
        contact_id: 3,
        role: 'Technical Evaluator',
        permissions: ['view', 'comment'],
      };
      ContactRoleRepository.prototype.addContactToDeal = jest.fn().mockResolvedValue(mockRole);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/contacts' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRoleRepository.prototype.addContactToDeal).toHaveBeenCalledWith({
        deal_id: 1,
        contact_id: 3,
        role: 'Technical Evaluator',
        permissions: ['view', 'comment'],
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'contact_added_to_deal',
        target_type: 'deal',
        target_id: '1',
        metadata: {
          contact_id: 3,
          role: 'Technical Evaluator',
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockRole);
    });
  });

  describe('POST /api/crm/deals/:id/notes', () => {
    it('should add a note to a deal', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        content: 'Customer requested additional features',
        is_private: false,
      };

      const NoteRepository = (await import('../../../../src/api/repositories/note-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockNote = {
        id: 2,
        target_type: 'deal',
        target_id: 1,
        content: 'Customer requested additional features',
        is_private: false,
        created_by: 'user-123',
        created_at: '2024-01-15T00:00:00Z',
      };
      NoteRepository.prototype.createNote = jest.fn().mockResolvedValue(mockNote);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/notes' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(NoteRepository.prototype.createNote).toHaveBeenCalledWith({
        target_type: 'deal',
        target_id: 1,
        content: 'Customer requested additional features',
        is_private: false,
        created_by: 'user-123',
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'note_added',
        target_type: 'deal',
        target_id: '1',
        metadata: {
          note_preview: 'Customer requested additional features',
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockNote);
    });
  });

  describe('GET /api/crm/deals/:id/timeline', () => {
    it('should fetch deal timeline activities', async () => {
      mockRequest.params = { id: '1' };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      
      const mockTimeline = [
        {
          id: 1,
          action: 'deal_created',
          created_at: '2024-01-01T00:00:00Z',
          metadata: {},
        },
        {
          id: 2,
          action: 'deal_updated',
          created_at: '2024-01-05T00:00:00Z',
          metadata: { stage_change: { from: 'qualified', to: 'negotiation' } },
        },
        {
          id: 3,
          action: 'estimate_linked_to_deal',
          created_at: '2024-01-10T00:00:00Z',
          metadata: { estimate_id: 1 },
        },
      ];
      ActivityService.prototype.getTargetTimeline = jest.fn().mockResolvedValue(mockTimeline);

      const dealsRoutes = await import('../../../../src/api/routes/crm/deals');
      const handler = dealsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/timeline'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getTargetTimeline).toHaveBeenCalledWith('deal', '1');
      expect(mockResponse.json).toHaveBeenCalledWith(mockTimeline);
    });
  });
});