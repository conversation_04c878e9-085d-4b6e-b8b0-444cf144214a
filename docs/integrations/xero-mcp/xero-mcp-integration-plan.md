# Xero MCP Integration Plan for Onbord Financial Dashboard

## Overview
This document outlines how to integrate the Xero MCP (Model Context Protocol) into the Onbord Financial Dashboard web application to provide a chat interface similar to Claude Desktop.

## Architecture

### 1. Frontend Components

#### Chat Interface
- Floating button that expands to chat window
- Similar to popular website chat widgets (Intercom, Drift style)
- Real-time message streaming
- Command suggestions and autocomplete

#### MCP Client
- Uses `@modelcontextprotocol/sdk` TypeScript SDK
- Connects via SSE (Server-Sent Events) or Streamable HTTP
- Handles real-time bidirectional communication

### 2. Backend Components

#### MCP Server Wrapper
- Express endpoints for MCP communication
- Session management for multiple users
- Bridges between web client and Xero MCP

#### Xero MCP Integration
- Two options:
  1. **Direct Integration**: Run Xero MCP server as a child process
  2. **Remote Integration**: Connect to a deployed Xero MCP server

### 3. Implementation Steps

#### Step 1: Install Dependencies
```bash
npm install @modelcontextprotocol/sdk
npm install @xeroapi/xero-mcp-server
```

#### Step 2: Create MCP Server Endpoint
Create a new file `src/api/routes/mcp.ts`:

```typescript
import { Router } from 'express';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { spawn } from 'child_process';

const router = Router();
const transports: Record<string, any> = {};

// SSE endpoint for legacy support
router.get('/sse', async (req, res) => {
  const transport = new SSEServerTransport('/api/mcp/messages', res);
  const sessionId = transport.sessionId;
  transports[sessionId] = transport;
  
  // Connect to Xero MCP server
  const xeroMcp = spawn('node', [
    'node_modules/@xeroapi/xero-mcp-server/dist/index.js'
  ], {
    env: {
      ...process.env,
      XERO_CLIENT_ID: process.env.XERO_CLIENT_ID,
      XERO_CLIENT_SECRET: process.env.XERO_CLIENT_SECRET
    }
  });
  
  // Bridge communication
  // ... implementation details
  
  res.on('close', () => {
    delete transports[sessionId];
    xeroMcp.kill();
  });
});

// Messages endpoint
router.post('/messages', async (req, res) => {
  const sessionId = req.query.sessionId as string;
  const transport = transports[sessionId];
  if (transport) {
    await transport.handlePostMessage(req, res, req.body);
  } else {
    res.status(400).send('No transport found');
  }
});

export default router;
```

#### Step 3: Create Chat Component
Create `src/frontend/components/XeroChat/index.tsx`:

```typescript
import React, { useState, useEffect } from 'react';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';

export const XeroChat: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [client, setClient] = useState<Client | null>(null);
  
  useEffect(() => {
    const initializeClient = async () => {
      const mcpClient = new Client({
        name: 'xero-chat-client',
        version: '1.0.0'
      });
      
      const transport = new SSEClientTransport(
        new URL('/api/mcp/sse', window.location.origin)
      );
      
      await mcpClient.connect(transport);
      setClient(mcpClient);
      
      // List available tools
      const tools = await mcpClient.listTools();
      console.log('Available Xero tools:', tools);
    };
    
    if (isOpen && !client) {
      initializeClient();
    }
  }, [isOpen]);
  
  // ... rest of chat implementation
};
```

#### Step 4: Available Xero MCP Tools
The Xero MCP provides these tools you can use:

**Listing Tools:**
- `list-accounts` - Retrieve chart of accounts
- `list-contacts` - Get contacts/customers
- `list-invoices` - Get invoices
- `list-quotes` - Get quotes
- `list-credit-notes` - Get credit notes
- `list-payments` - Get payments
- `list-bank-transactions` - Get bank transactions
- `list-tax-rates` - Get tax rates
- `list-items` - Get inventory items

**Creating Tools:**
- `create-contact` - Create new contact
- `create-invoice` - Create new invoice
- `create-quote` - Create new quote
- `create-payment` - Record payment

**Updating Tools:**
- `update-contact` - Update contact details
- `update-invoice` - Update draft invoice
- `update-quote` - Update draft quote

### 4. Advanced Features

#### Natural Language Processing
Instead of calling MCP tools directly, integrate with an LLM to:
1. Parse user's natural language queries
2. Determine which MCP tools to call
3. Format the response in conversational style

Example flow:
```
User: "Show me unpaid invoices for Acme Corp"
  ↓
LLM: Interprets and calls `list-invoices` with contact filter
  ↓
MCP: Returns invoice data
  ↓
LLM: Formats response: "You have 3 unpaid invoices for Acme Corp totaling $15,420..."
```

#### Authentication & Security
- Leverage existing Xero OAuth2 tokens from your current integration
- Each chat session uses the authenticated user's Xero credentials
- No additional authentication needed

### 5. Deployment Options

#### Option 1: Embedded MCP Server
- Run Xero MCP server as part of your Node.js backend
- Direct communication, lower latency
- More control over the integration

#### Option 2: Remote MCP Server
- Deploy Xero MCP server separately (e.g., on Cloudflare Workers)
- Better scalability
- Easier to update independently

### 6. Example Implementation Timeline

**Week 1:**
- Set up basic MCP server endpoint
- Create minimal chat UI component
- Test connection between frontend and backend

**Week 2:**
- Integrate Xero MCP server
- Implement core tool calling functionality
- Add message streaming

**Week 3:**
- Add LLM integration for natural language
- Implement chat history/context
- Polish UI/UX

**Week 4:**
- Testing and error handling
- Performance optimization
- Deploy to preview environment

## Benefits of This Approach

1. **Familiar Interface** - Users get a Claude-like experience within your app
2. **Contextual Actions** - Perform Xero operations without leaving the dashboard
3. **Natural Language** - Users don't need to know specific commands
4. **Extensible** - Easy to add more MCP servers (Harvest, HubSpot, etc.)

## Challenges & Solutions

1. **Rate Limiting** - Implement caching and request queuing
2. **Error Handling** - Graceful fallbacks for MCP connection issues
3. **Performance** - Use SSE for real-time updates without polling
4. **Security** - Ensure proper session isolation between users
