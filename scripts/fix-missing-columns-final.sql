-- Script to add all missing columns found during code analysis
-- Run this on production database to ensure compatibility

-- Add missing columns to company table
ALTER TABLE company ADD COLUMN IF NOT EXISTS size TEXT;
ALTER TABLE company ADD COLUMN IF NOT EXISTS address TEXT;
ALTER TABLE company ADD COLUMN IF NOT EXISTS radar_state TEXT;
ALTER TABLE company ADD COLUMN IF NOT EXISTS priority TEXT;
ALTER TABLE company ADD COLUMN IF NOT EXISTS current_spend REAL;
ALTER TABLE company ADD COLUMN IF NOT EXISTS potential_spend REAL;
ALTER TABLE company ADD COLUMN IF NOT EXISTS last_interaction_date TEXT;
ALTER TABLE company ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add missing columns to contact table
ALTER TABLE contact ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add missing columns to deal table
ALTER TABLE deal ADD COLUMN IF NOT EXISTS harvest_id INTEGER;
CREATE INDEX IF NOT EXISTS idx_deal_harvest_id ON deal(harvest_id);

-- Add missing columns to expense table
ALTER TABLE expense ADD COLUMN IF NOT EXISTS name TEXT;
ALTER TABLE expense ADD COLUMN IF NOT EXISTS type TEXT;
ALTER TABLE expense ADD COLUMN IF NOT EXISTS frequency TEXT;
ALTER TABLE expense ADD COLUMN IF NOT EXISTS repeat_count INTEGER;
ALTER TABLE expense ADD COLUMN IF NOT EXISTS source TEXT DEFAULT 'manual';
ALTER TABLE expense ADD COLUMN IF NOT EXISTS editable INTEGER DEFAULT 1 CHECK(editable IN (0, 1));
ALTER TABLE expense ADD COLUMN IF NOT EXISTS metadata TEXT DEFAULT '{}';

-- Add missing columns to deal_estimate table
ALTER TABLE deal_estimate ADD COLUMN IF NOT EXISTS harvest_estimate_id TEXT;

-- Fix activity_feed table structure to match repository expectations
-- First check if we need to rename columns
CREATE TABLE IF NOT EXISTS activity_feed_new (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  subject TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL,
  entity_type TEXT CHECK(entity_type IN ('company', 'contact', 'deal', 'project', 'estimate', 'expense', NULL)),
  entity_id TEXT,
  due_date TEXT,
  completed_date TEXT,
  company_id TEXT,
  contact_id TEXT,
  deal_id TEXT,
  metadata TEXT DEFAULT '{}',
  is_read INTEGER DEFAULT 0 CHECK(is_read IN (0, 1)),
  importance TEXT DEFAULT 'normal' CHECK(importance IN ('low', 'normal', 'high', 'critical')),
  source TEXT CHECK(source IN ('ui', 'api', 'webhook', 'system', 'import')),
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE SET NULL,
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE SET NULL,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE SET NULL
);

-- Copy data from old table if it exists with different structure
INSERT OR IGNORE INTO activity_feed_new 
SELECT 
  id,
  COALESCE(type, activity_type) as type,
  COALESCE(subject, description) as subject,
  description,
  COALESCE(status, 'completed') as status,
  COALESCE(entity_type, target_type) as entity_type,
  COALESCE(entity_id, target_id) as entity_id,
  due_date,
  completed_date,
  company_id,
  contact_id,
  deal_id,
  metadata,
  COALESCE(is_read, 0) as is_read,
  importance,
  source,
  created_at,
  updated_at,
  created_by,
  updated_by,
  deleted_at
FROM activity_feed;

-- Drop old table and rename new one
DROP TABLE IF EXISTS activity_feed;
ALTER TABLE activity_feed_new RENAME TO activity_feed;

-- Recreate indexes
CREATE INDEX IF NOT EXISTS idx_activity_feed_type ON activity_feed(type);
CREATE INDEX IF NOT EXISTS idx_activity_feed_entity ON activity_feed(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_company_id ON activity_feed(company_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_contact_id ON activity_feed(contact_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_deal_id ON activity_feed(deal_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_at ON activity_feed(created_at);
CREATE INDEX IF NOT EXISTS idx_activity_feed_importance ON activity_feed(importance);
CREATE INDEX IF NOT EXISTS idx_activity_feed_status ON activity_feed(status);
CREATE INDEX IF NOT EXISTS idx_activity_feed_is_read ON activity_feed(is_read);
CREATE INDEX IF NOT EXISTS idx_activity_feed_deleted_at ON activity_feed(deleted_at);

-- Ensure change_log has all required columns
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS change_type TEXT DEFAULT 'update';
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS change_reason TEXT;
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS batch_id TEXT;
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS created_at TEXT DEFAULT (datetime('now'));
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS updated_at TEXT DEFAULT (datetime('now'));
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS created_by TEXT;
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS updated_by TEXT;
ALTER TABLE change_log ADD COLUMN IF NOT EXISTS deleted_at TEXT;

-- Update any NULL change_type values to 'update'
UPDATE change_log SET change_type = 'update' WHERE change_type IS NULL;

-- Fix any invalid change_source values
UPDATE change_log SET change_source = 'sync' WHERE change_source IN ('HubSpot', 'Harvest', 'Xero');
UPDATE change_log SET change_source = 'ui' WHERE change_source IN ('Manual', 'User');
UPDATE change_log SET change_source = 'system' WHERE change_source = 'System';
UPDATE change_log SET change_source = 'import' WHERE change_source = 'Import';
UPDATE change_log SET change_source = 'api' WHERE change_source = 'API';
UPDATE change_log SET change_source = 'system' WHERE change_source NOT IN ('ui', 'api', 'sync', 'import', 'system');

-- Drop and recreate CHECK constraints to accept both lowercase and capitalized source values
-- This requires SQLite 3.35.0+ for ALTER TABLE DROP CONSTRAINT support
-- If your SQLite version doesn't support this, you'll need to recreate the tables

-- For company table - try to drop constraint if SQLite version supports it
-- Otherwise, the constraint will be enforced by the application layer

-- Fix estimate_time_allocation table to accept date format instead of week format
-- Since there's no data in this table, we can safely recreate it
DROP TABLE IF EXISTS estimate_time_allocation;
CREATE TABLE estimate_time_allocation (
  id TEXT PRIMARY KEY,
  allocation_id TEXT NOT NULL,
  week_identifier TEXT NOT NULL, -- Format: 'YYYY-MM-DD' (ISO date of week start)
  days REAL NOT NULL CHECK(days >= 0 AND days <= 5),
  hours REAL GENERATED ALWAYS AS (days * 8) STORED,
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT NOT NULL DEFAULT (datetime('now')),
  created_by TEXT,
  updated_by TEXT,
  deleted_at TEXT,
  FOREIGN KEY (allocation_id) REFERENCES estimate_allocation(id) ON DELETE RESTRICT,
  UNIQUE(allocation_id, week_identifier),
  CHECK(week_identifier GLOB '[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]')
);
CREATE INDEX idx_time_allocation_allocation_id ON estimate_time_allocation(allocation_id);
CREATE INDEX idx_time_allocation_week_identifier ON estimate_time_allocation(week_identifier);
CREATE INDEX idx_time_allocation_deleted_at ON estimate_time_allocation(deleted_at);