{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "allowJs": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src", "tests", "src/vite-env.d.ts", "src/types/*.d.ts", "src/frontend/types/*.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}