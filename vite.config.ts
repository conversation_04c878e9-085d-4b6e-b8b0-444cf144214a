import path from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Set API URL based on mode and environment
  let apiUrl;

  if (mode === 'production') {
    // Check if this is a preview deployment
    if (process.env.RENDER_EXTERNAL_HOSTNAME?.includes('preview')) {
      // For preview environment, use the custom domain
      apiUrl = 'https://upstream-preview.onbord.au/api';
      console.log('Using preview deployment API URL: upstream-preview.onbord.au');
    } else {
      // For production environment
      apiUrl = 'https://upstream.onbord.au/api';
    }
  } else {
    // For local development
    apiUrl = `http://localhost:${process.env.API_PORT || '3002'}/api`;
  }

  console.log(`Building with API_URL: ${apiUrl} in ${mode} mode`);

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    define: {
      'import.meta.env.VITE_API_URL': JSON.stringify(apiUrl)
    },
    server: {
      port: parseInt(process.env.FRONTEND_PORT || '5173'),
      proxy: {
        '/api': {
          target: `http://localhost:${process.env.API_PORT || '3002'}`,
          changeOrigin: true
        }
      },
      hmr: {
        protocol: 'ws',
        host: 'localhost',
        port: parseInt(process.env.FRONTEND_PORT || '5173'),
        timeout: 5000,
        overlay: true
      }
    },
    build: {
      outDir: 'dist',
      emptyOutDir: false
    },
    css: {
      // Ensure postcss processes all CSS files
      postcss: './postcss.config.js',
      // Enable module support for .module.css files only
      modules: {
        // Default pattern for CSS modules
        localsConvention: 'camelCase',
        generateScopedName: '[local]_[hash:base64:5]'
      }
    }
  };
});