/**
 * Knowledge Graph Component Test
 * 
 * Tests for the Knowledge Graph visualization component.
 */
import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { jest } from '@jest/globals';
import KnowledgeGraph from '../../../../src/frontend/components/Leads/KnowledgeGraph/KnowledgeGraph';

// Mock dependencies
jest.mock('react-force-graph-2d', () => ({
  __esModule: true,
  default: React.forwardRef((props: any, ref: any) => {
    // Mock ForceGraph2D component
    const { graphData, nodeCanvasObject, onNodeClick } = props;
    
    React.useImperativeHandle(ref, () => ({
      d3Force: jest.fn(),
      zoomToFit: jest.fn(),
      centerAt: jest.fn()
    }));
    
    return (
      <div data-testid="force-graph">
        {graphData.nodes.map((node: any) => (
          <div
            key={node.id}
            data-testid={`node-${node.id}`}
            onClick={() => onNodeClick?.(node)}
          >
            {node.label} ({node.type})
          </div>
        ))}
        {graphData.links.map((link: any, index: number) => (
          <div key={index} data-testid={`link-${index}`}>
            {link.source} → {link.target} ({link.type})
          </div>
        ))}
      </div>
    );
  })
}));

// Mock API
const mockFetch = jest.fn();
global.fetch = mockFetch as any;

// Mock file download
const mockCreateObjectURL = jest.fn();
const mockRevokeObjectURL = jest.fn();
global.URL.createObjectURL = mockCreateObjectURL;
global.URL.revokeObjectURL = mockRevokeObjectURL;

describe('KnowledgeGraph Component', () => {
  const mockGraphData = {
    nodes: [
      { id: '1', label: 'Acme Corp', type: 'company', metadata: { industry: 'Tech' } },
      { id: '2', label: 'John Doe', type: 'contact', metadata: { jobTitle: 'CEO' } },
      { id: '3', label: 'Big Deal', type: 'deal', metadata: { value: 100000 } },
      { id: '4', label: 'Project Alpha', type: 'project', metadata: { status: 'active' } }
    ],
    links: [
      { source: '2', target: '1', type: 'works_at', strength: 5 },
      { source: '3', target: '1', type: 'belongs_to', strength: 5 },
      { source: '4', target: '1', type: 'belongs_to', strength: 5 }
    ],
    stats: {
      totalNodes: 4,
      totalLinks: 3,
      nodeTypes: { company: 1, contact: 1, deal: 1, project: 1 },
      linkTypes: { works_at: 1, belongs_to: 2 }
    }
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ success: true, data: mockGraphData })
    });
    mockCreateObjectURL.mockReturnValue('blob:mock-url');
  });
  
  afterEach(() => {
    jest.restoreAllMocks();
  });
  
  it('renders loading state initially', () => {
    render(<KnowledgeGraph />);
    
    expect(screen.getByText(/Loading knowledge graph/i)).toBeInTheDocument();
  });
  
  it('fetches and displays graph data', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith('/api/knowledge-graph?maxNodes=1000');
    });
    
    await waitFor(() => {
      expect(screen.getByText('Acme Corp (company)')).toBeInTheDocument();
      expect(screen.getByText('John Doe (contact)')).toBeInTheDocument();
      expect(screen.getByText('Big Deal (deal)')).toBeInTheDocument();
      expect(screen.getByText('Project Alpha (project)')).toBeInTheDocument();
    });
  });
  
  it('displays statistics', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByText(/4 nodes/i)).toBeInTheDocument();
      expect(screen.getByText(/3 links/i)).toBeInTheDocument();
    });
  });
  
  it('filters nodes by type', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    // Find and click the contact filter checkbox
    const contactFilter = screen.getByLabelText(/Contacts/i);
    fireEvent.click(contactFilter);
    
    // The component should re-render with filtered data
    await waitFor(() => {
      expect(screen.queryByText('John Doe (contact)')).not.toBeInTheDocument();
      expect(screen.getByText('Acme Corp (company)')).toBeInTheDocument();
    });
  });
  
  it('handles search functionality', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    const searchInput = screen.getByPlaceholderText(/Search nodes/i);
    fireEvent.change(searchInput, { target: { value: 'Acme' } });
    
    // Should highlight matching nodes
    await waitFor(() => {
      const acmeNode = screen.getByTestId('node-1');
      expect(acmeNode).toBeInTheDocument();
    });
  });
  
  it('handles node click to show details', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    const companyNode = screen.getByTestId('node-1');
    fireEvent.click(companyNode);
    
    await waitFor(() => {
      expect(screen.getByText(/Selected: Acme Corp/i)).toBeInTheDocument();
      expect(screen.getByText(/Industry: Tech/i)).toBeInTheDocument();
    });
  });
  
  it('exports graph data as JSON', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    const exportButton = screen.getByText(/Export/i);
    fireEvent.click(exportButton);
    
    const jsonOption = screen.getByText(/Export as JSON/i);
    fireEvent.click(jsonOption);
    
    expect(mockCreateObjectURL).toHaveBeenCalled();
    
    // Check that a download link was created
    const downloadLink = document.querySelector('a[download]');
    expect(downloadLink).toBeDefined();
  });
  
  it('exports graph data as CSV', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    const exportButton = screen.getByText(/Export/i);
    fireEvent.click(exportButton);
    
    const csvOption = screen.getByText(/Export as CSV/i);
    fireEvent.click(csvOption);
    
    expect(mockCreateObjectURL).toHaveBeenCalled();
  });
  
  it('handles API errors gracefully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error'
    });
    
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByText(/Failed to load knowledge graph/i)).toBeInTheDocument();
    });
  });
  
  it('displays empty state when no data', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: {
          nodes: [],
          links: [],
          stats: {
            totalNodes: 0,
            totalLinks: 0,
            nodeTypes: {},
            linkTypes: {}
          }
        }
      })
    });
    
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByText(/No data available/i)).toBeInTheDocument();
    });
  });
  
  it('fetches subgraph when entity ID provided', async () => {
    render(<KnowledgeGraph entityId="company-123" entityType="company" />);
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(
        '/api/knowledge-graph/company-123?entityType=company&depth=2'
      );
    });
  });
  
  it('handles depth control for subgraphs', async () => {
    render(<KnowledgeGraph entityId="company-123" entityType="company" />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    // Find depth slider
    const depthSlider = screen.getByLabelText(/Depth/i);
    fireEvent.change(depthSlider, { target: { value: '3' } });
    
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledWith(
        '/api/knowledge-graph/company-123?entityType=company&depth=3'
      );
    });
  });
  
  it('resets zoom on button click', async () => {
    const graphRef = React.createRef<any>();
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    const resetButton = screen.getByText(/Reset Zoom/i);
    fireEvent.click(resetButton);
    
    // The zoomToFit method should be called on the graph instance
    // This is mocked in our ForceGraph2D mock
  });
  
  it('displays legend', async () => {
    render(<KnowledgeGraph />);
    
    await waitFor(() => {
      expect(screen.getByTestId('force-graph')).toBeInTheDocument();
    });
    
    expect(screen.getByText(/Companies/i)).toBeInTheDocument();
    expect(screen.getByText(/Contacts/i)).toBeInTheDocument();
    expect(screen.getByText(/Deals/i)).toBeInTheDocument();
    expect(screen.getByText(/Projects/i)).toBeInTheDocument();
    expect(screen.getByText(/Estimates/i)).toBeInTheDocument();
  });
});