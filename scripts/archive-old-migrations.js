#!/usr/bin/env node
/**
 * Archive old migration files in preparation for clean slate migration
 * 
 * This script:
 * 1. Creates an archive directory for old migrations
 * 2. Moves all numbered migration files to the archive
 * 3. Keeps only the unified schema file (000_unified_schema.sql)
 */

const fs = require('fs');
const path = require('path');

const MIGRATIONS_DIR = path.join(__dirname, '..', 'migrations');
const ARCHIVE_DIR = path.join(MIGRATIONS_DIR, 'archive');
const UNIFIED_SCHEMA = '000_unified_schema.sql';

// Ensure migrations directory exists
if (!fs.existsSync(MIGRATIONS_DIR)) {
  console.error('Migrations directory not found:', MIGRATIONS_DIR);
  process.exit(1);
}

// Create archive directory if it doesn't exist
if (!fs.existsSync(ARCHIVE_DIR)) {
  console.log('Creating archive directory:', ARCHIVE_DIR);
  fs.mkdirSync(ARCHIVE_DIR, { recursive: true });
}

// Get all migration files
const files = fs.readdirSync(MIGRATIONS_DIR);
const migrationFiles = files.filter(file => {
  // Match files like 001_*.sql, 002_*.sql, etc.
  return /^\d{3}_.*\.sql$/.test(file) && file !== UNIFIED_SCHEMA;
});

console.log(`Found ${migrationFiles.length} migration files to archive`);

// Archive each migration file
let archivedCount = 0;
let errorCount = 0;

migrationFiles.forEach(file => {
  const sourcePath = path.join(MIGRATIONS_DIR, file);
  const destPath = path.join(ARCHIVE_DIR, file);
  
  try {
    // Check if file already exists in archive
    if (fs.existsSync(destPath)) {
      console.log(`⚠️  Skipping ${file} - already exists in archive`);
      return;
    }
    
    // Move the file
    fs.renameSync(sourcePath, destPath);
    console.log(`✓ Archived: ${file}`);
    archivedCount++;
  } catch (error) {
    console.error(`✗ Error archiving ${file}:`, error.message);
    errorCount++;
  }
});

// Summary
console.log('\n=== Archive Summary ===');
console.log(`Total migration files found: ${migrationFiles.length}`);
console.log(`Successfully archived: ${archivedCount}`);
console.log(`Errors: ${errorCount}`);

// List remaining files in migrations directory
const remainingFiles = fs.readdirSync(MIGRATIONS_DIR)
  .filter(file => file.endsWith('.sql') && !fs.statSync(path.join(MIGRATIONS_DIR, file)).isDirectory());

console.log('\n=== Remaining Migration Files ===');
if (remainingFiles.length === 0) {
  console.log('No SQL files remaining in migrations directory');
} else {
  remainingFiles.forEach(file => {
    console.log(`- ${file}`);
  });
}

// Verify unified schema exists
if (fs.existsSync(path.join(MIGRATIONS_DIR, UNIFIED_SCHEMA))) {
  console.log(`\n✅ Unified schema file present: ${UNIFIED_SCHEMA}`);
} else {
  console.log(`\n⚠️  Warning: Unified schema file not found: ${UNIFIED_SCHEMA}`);
}

console.log('\n✅ Archive process complete');