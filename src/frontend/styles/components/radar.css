/* Radar Component Styles */

/* Radar Page Container */
.radar-page {
  @apply relative;
}

/* Radar Grid Layout */
.radar-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  position: relative;
}

/* Add visual dividers between quadrants */
.radar-grid::before,
.radar-grid::after {
  content: '';
  position: absolute;
  background: linear-gradient(to right, transparent, rgba(var(--color-border), 0.5), transparent);
  pointer-events: none;
}

.radar-grid::before {
  /* Vertical divider */
  width: 1px;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.radar-grid::after {
  /* Horizontal divider */
  width: 100%;
  height: 1px;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(to bottom, transparent, rgba(var(--color-border), 0.5), transparent);
}

/* Dark mode dividers */
.dark .radar-grid::before,
.dark .radar-grid::after {
  background: linear-gradient(to right, transparent, rgba(var(--color-border), 0.5), transparent);
}

.dark .radar-grid::after {
  background: linear-gradient(to bottom, transparent, rgba(var(--color-border), 0.5), transparent);
}

/* Radar Quadrant */
.radar-quadrant {
  @apply relative overflow-hidden p-6 rounded-lg border flex flex-col;
  min-height: 450px;
  background: linear-gradient(135deg, rgba(var(--color-bg), 0.9), rgba(var(--color-surface), 0.9));
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-color: var(--color-border);
}

.dark .radar-quadrant {
  background: linear-gradient(135deg, rgba(var(--color-surface-alt), 0.9), rgba(var(--color-surface), 0.9));
  border-color: var(--color-border-strong);
}

/* Quadrant hover effect */
.radar-quadrant:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Quadrant drop states */
.radar-quadrant.can-drop {
  @apply border-blue-400 dark:border-blue-500;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.2);
}

.radar-quadrant.is-over {
  @apply border-purple-500 bg-purple-50 dark:bg-purple-900 dark:bg-opacity-20;
  box-shadow: 0 0 0 3px rgba(var(--color-purple-rgb), 0.3);
}

/* Quadrant header */
.quadrant-header {
  @apply mb-4 pb-3 border-b border-gray-200 dark:border-gray-700;
}

.quadrant-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quadrant-icon {
  @apply w-5 h-5 text-gray-400 dark:text-gray-500;
}

/* Quadrant badges for spend indicators */
.quadrant-badges {
  @apply flex gap-2 mt-2;
}

.spend-badge {
  @apply text-xs px-2 py-1 rounded-full font-medium;
}

.spend-badge.high {
  @apply bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:bg-opacity-30 dark:text-emerald-400;
}

.spend-badge.low {
  @apply bg-amber-100 text-amber-700 dark:bg-amber-900 dark:bg-opacity-30 dark:text-amber-400;
}

/* Company Grid */
.company-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

/* Radar Company Card */
.radar-company-card {
  @apply relative overflow-hidden p-4 rounded-lg shadow-sm border cursor-grab;
  background: linear-gradient(135deg, var(--color-bg), var(--color-surface));
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  border-color: var(--color-border);
}

.dark .radar-company-card {
  background: linear-gradient(135deg, var(--color-border-strong), var(--color-surface-alt));
  border-color: var(--color-border-strong);
}

/* Card hover and drag states */
.radar-company-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.radar-company-card.is-dragging {
  opacity: 0.5;
  transform: scale(0.95);
  cursor: grabbing;
}

.radar-company-card:active {
  cursor: grabbing;
}

/* Company Logo */
.company-logo {
  @apply flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center text-white font-semibold text-lg shadow-sm;
  transition: transform 0.2s ease;
}

.radar-company-card:hover .company-logo {
  transform: scale(1.05);
}

/* Company Info Section */
.company-name {
  @apply text-sm font-semibold text-gray-900 dark:text-white truncate;
  line-height: 1.25rem;
}

.company-industry {
  @apply text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5;
  line-height: 1rem;
}


/* Remove Drop Zone */
.remove-drop-zone {
  @apply mt-8 p-8 border-2 border-dashed rounded-xl text-center transition-all duration-200;
  @apply border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800 dark:bg-opacity-50;
}

.remove-drop-zone.is-over {
  @apply border-red-500 bg-red-50 dark:bg-red-900 dark:bg-opacity-20;
  transform: scale(1.02);
}

.remove-drop-zone-content {
  @apply flex items-center justify-center text-gray-500 dark:text-gray-400;
}

.remove-drop-zone.is-over .remove-drop-zone-content {
  @apply text-red-600 dark:text-red-400;
}

/* Add Company Button */
.add-company-button {
  @apply px-4 py-2.5 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg text-sm font-medium;
  @apply hover:from-purple-700 hover:to-purple-800 transition-all duration-200;
  @apply shadow-md hover:shadow-lg transform hover:scale-105;
  @apply flex items-center gap-2;
}

.add-company-button svg {
  @apply w-4 h-4;
}

/* Header Stats */
.radar-stats {
  @apply flex flex-wrap items-center gap-6 text-sm;
}

.stat-item {
  @apply flex flex-col;
}

.stat-label {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-0.5;
}

.stat-value {
  @apply text-base font-semibold text-gray-900 dark:text-white;
}

/* Loading State */
.radar-loading {
  @apply flex flex-col items-center justify-center h-96;
}

.radar-loading-spinner {
  @apply animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-t-transparent;
}

.radar-loading-text {
  @apply mt-4 text-gray-500 dark:text-gray-400;
}

/* Empty State */
.quadrant-empty {
  @apply flex flex-col items-center justify-center py-12 text-gray-400 dark:text-gray-500;
}

.quadrant-empty-icon {
  @apply w-12 h-12 mb-2 opacity-50;
}

.quadrant-empty-text {
  @apply text-sm italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .radar-page {
    @apply p-4;
  }
  
  .radar-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .radar-grid::before,
  .radar-grid::after {
    display: none;
  }
  
  .company-grid {
    grid-template-columns: 1fr;
  }
  
  .radar-stats {
    @apply text-xs;
  }
  
  .add-company-button {
    @apply px-3 py-1.5 text-sm;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

/* Quadrant-specific colors */
.quadrant-strategy {
  border-top: 3px solid #8b5cf6;
}

.quadrant-transformation {
  border-top: 3px solid #10b981;
}

.quadrant-bau {
  border-top: 3px solid #3b82f6;
}

.quadrant-transition {
  border-top: 3px solid #f59e0b;
}

/* Question Marks Section */
.question-marks-section {
  @apply mt-8 p-6 rounded-lg border bg-white dark:bg-gray-800;
  border-color: var(--color-border);
  transition: all 0.3s ease;
}

.dark .question-marks-section {
  border-color: var(--color-border-strong);
}

.question-marks-section.can-drop {
  @apply border-purple-400 dark:border-purple-500;
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.2);
}

.question-marks-section.is-over {
  @apply border-purple-500 bg-purple-50 dark:bg-purple-900 dark:bg-opacity-20;
  box-shadow: 0 0 0 3px rgba(var(--color-purple-rgb), 0.3);
}

.question-marks-header {
  @apply flex items-start justify-between mb-6 pb-4 border-b border-gray-200 dark:border-gray-700;
}

.question-marks-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2;
}

.question-marks-subtitle {
  @apply text-sm text-gray-500 dark:text-gray-400 mt-1;
}

.question-marks-stats {
  @apply flex gap-3;
}

.stat-badge {
  @apply px-3 py-1 text-xs font-medium rounded-full;
  @apply bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300;
}

.question-marks-content {
  @apply min-h-[200px];
}

.question-marks-loading {
  @apply flex flex-col items-center justify-center py-12;
}

.question-marks-empty {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.question-marks-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

/* Question Mark Card */
.question-mark-card {
  @apply p-4 rounded-lg border bg-white dark:bg-gray-800 hover:shadow-md transition-all;
  border-color: var(--color-border);
}

.dark .question-mark-card {
  border-color: var(--color-border-strong);
}

.question-mark-card-header {
  @apply flex items-start justify-between mb-3;
}

.add-action-button {
  @apply p-1.5 rounded-md text-gray-400 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 transition-colors;
}

.action-summary {
  @apply flex items-center gap-2 mb-3 text-xs;
}

.action-count {
  @apply px-2 py-0.5 rounded-full font-medium;
}

.action-count.in-progress {
  @apply bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 flex items-center gap-1;
}

.action-count.pending {
  @apply bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300;
}

.actions-list {
  @apply space-y-2;
}

.action-item {
  @apply p-2 rounded-md bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors;
}

.action-header {
  @apply flex items-center gap-2 mb-1;
}

.action-icon {
  @apply text-lg flex-shrink-0;
}

.action-title {
  @apply text-sm font-medium text-gray-900 dark:text-white truncate;
}

.action-meta {
  @apply flex items-center gap-2 text-xs;
}

.priority-badge {
  @apply px-1.5 py-0.5 rounded-full font-medium;
}

.due-date {
  @apply text-gray-500 dark:text-gray-400;
}

.due-date.overdue {
  @apply text-red-600 dark:text-red-400 font-medium;
}

.assignee {
  @apply text-gray-500 dark:text-gray-400 flex items-center gap-1;
}

.more-actions {
  @apply text-center text-sm text-gray-500 dark:text-gray-400 italic py-2;
}