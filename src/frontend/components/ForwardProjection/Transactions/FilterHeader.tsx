import React from "react"; import { FilterHeaderProps } from "../types/transactions-list-types"; /** * Component for transaction filter controls */ const FilterHeader: React.FC<FilterHeaderProps> = ({ filtersExpanded, toggleFilters, filters, clearFilters, }) => { // Check if any filters are active const hasActiveFilters = filters.types.length > 0 || filters.sources.length > 0 || filters.searchText || filters.amountRange[0] !== null || filters.amountRange[1] !== null; return ( <div className="px-2 md:px-3 mb-4"> <div className="border rounded-lg overflow-hidden shadow-sm" style={{ borderColor: "var(--color-border)" }} > {/* Accordion header */} <div className="flex items-center justify-between w-full"> <button type="button" onClick={toggleFilters} className="flex items-center grow justify-between px-3 py-2 text-sm font-medium text-left focus:outline-none focus-visible:ring focus-visible:ring-secondary focus-visible:ring-opacity-50" style={{ backgroundColor: "var(--color-surface)", color: "var(--color-text)", transition: "background-color 0.2s ease", }} onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = "var(--color-surface-alt)") } onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = "var(--color-surface)") } aria-expanded={filtersExpanded} aria-controls="transaction-filters-panel" > <div className="flex items-center"> <svg className="w-4 h-4 mr-2" style={{ color: "var(--color-text-muted)" }} fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" /> </svg> <span className="font-medium">Transaction Filters</span> {hasActiveFilters && ( <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded border text-xs font-medium whitespace-nowrap" style={{ backgroundColor: "var(--color-primary-50)", color: "var(--color-primary-text)", borderColor: "var(--color-primary-200)", }} > Active </span> )} </div> <svg className={`w-5 h-5 transition-transform duration-200 ${ filtersExpanded ? "transform rotate-180" : "" }`} fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /> </svg> </button> {hasActiveFilters && ( <button onClick={clearFilters} className="px-3 py-2 text-sm font-medium border-l" style={{ color: "var(--color-primary)", backgroundColor: "var(--color-surface)", borderColor: "var(--color-border)", transition: "all 0.2s ease", }} onMouseEnter={(e) => { e.currentTarget.style.color = "var(--color-primary-hover)"; e.currentTarget.style.backgroundColor = "var(--color-surface-alt)"; }} onMouseLeave={(e) => { e.currentTarget.style.color = "var(--color-primary)"; e.currentTarget.style.backgroundColor = "var(--color-surface)"; }} > Clear All </button> )} </div> </div> </div> ); }; export default FilterHeader; 