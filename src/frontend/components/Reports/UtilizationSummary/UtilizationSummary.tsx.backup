import React from "react";
import { StaffUtilization } from "../../../../services/harvest/time-report-service";
import NonBillableCostBreakdown from "../NonBillableCostBreakdown";
import { useFixedPriceProjects } from "./hooks/useFixedPriceProjects";
import { useUtilizationCalculations } from "./hooks/useUtilizationCalculations";
import TimeCapacitySection from "./TimeCapacitySection";
import FinancialSummarySection from "./FinancialSummarySection";

interface UtilizationSummaryProps {
  utilizationData: StaffUtilization[];
  fromDate?: string; // Optional date range for cost calculations
  toDate?: string;
  excludeLeave?: boolean; // Whether to exclude leave from capacity calculations
}

/**
 * A component that displays summary cards for overall utilization
 * Redesigned for more efficient space usage
 */
const UtilizationSummary: React.FC<UtilizationSummaryProps> = ({
  utilizationData,
  fromDate,
  toDate,
  excludeLeave = false,
}) => {
  // If no utilization data, return null
  if (!utilizationData || utilizationData.length === 0) {
    return null;
  }

  // Use the fixed price projects hook
  const { fixedPriceProjects, isLoadingFixedPrice } = useFixedPriceProjects(
    fromDate,
    toDate
  );

  // Use the utilization calculations hook
  const {
    totalHours,
    totalBillableHours,
    totalLeaveHours,
    overallUtilization,
    billableAmountsByCurrency,
    fixedPriceIncomeByCurrency,
    availableCapacity,
    totalCapacity,
    financialMetricsByCurrency,
  } = useUtilizationCalculations(
    utilizationData,
    fixedPriceProjects,
    fromDate,
    toDate,
    excludeLeave
  );

  // Calculate non-billable hours
  const totalNonBillableHours = excludeLeave
    ? totalHours - totalBillableHours - totalLeaveHours
    : totalHours - totalBillableHours;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-3 h-full">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Summary
      </h3>
      <div>
        {/* Hours - Total, Billable, Non-billable, Available Capacity, and Total Capacity */}
        <TimeCapacitySection
          totalHours={totalHours}
          totalBillableHours={totalBillableHours}
          totalNonBillableHours={totalNonBillableHours}
          availableCapacity={availableCapacity}
          totalCapacity={totalCapacity}
          excludeLeave={excludeLeave}
        />

        {/* Billable amount, Fixed Price Income, and Staff cost */}
        <FinancialSummarySection
          billableAmountsByCurrency={billableAmountsByCurrency}
          fixedPriceIncomeByCurrency={fixedPriceIncomeByCurrency}
          fixedPriceProjects={fixedPriceProjects}
          isLoadingFixedPrice={isLoadingFixedPrice}
          financialMetricsByCurrency={financialMetricsByCurrency}
        />

        {/* Non-billable cost breakdown */}
        <div className="mt-3">
          {Object.entries(billableAmountsByCurrency).map(([currency]) => {
            // Get the non-billable cost for this currency
            const financialMetric = financialMetricsByCurrency.find(
              (metric) => metric.currency === currency
            );

            // Only show breakdown if there's a non-billable cost
            return financialMetric && financialMetric.nonBillableCost > 0 ? (
              <NonBillableCostBreakdown
                key={`nonbillable-${currency}`}
                utilizationData={utilizationData}
                fromDate={fromDate}
                toDate={toDate}
                excludeLeave={excludeLeave}
                currency={currency}
              />
            ) : null;
          })}
        </div>
      </div>
    </div>
  );
};

export default UtilizationSummary;
