import React, { forwardRef } from "react"; import { cn, selectVariants, SelectVariantProps, } from "../../../utils/component-variants"; export interface SelectOption { value: string; label: string; disabled?: boolean; } export interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, "size">, SelectVariantProps { label?: string; error?: string; helpText?: string; options?: SelectOption[]; placeholder?: string; required?: boolean; loading?: boolean; } /** * Modern Select component with variant system * * Features: * - Type-safe variants (default, error, success) * - Multiple sizes (sm, default, lg) * - Options array support with disabled states * - Error states with validation styling * - Loading states * - Accessibility features (ARIA labels, error announcements) * - Dark mode support * - Placeholder support */ export const Select = forwardRef<HTMLSelectElement, SelectProps>( ( { className, variant = "default", size = "default", label, error, helpText, options = [], placeholder, required = false, loading = false, id, children, ...props }, ref ) => { // Generate unique ID if not provided const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`; // Determine variant based on error state const effectiveVariant = error ? "error" : variant; return ( <div className="form-field"> {/* Label */} {label && ( <label htmlFor={selectId} className="form-label"> {label} {required && ( <span className="form-required" aria-label="required"> * </span> )} </label> )} {/* Select container */} <div className="relative"> {/* Select field */} <select ref={ref} id={selectId} className={cn( "input-modern", effectiveVariant === "error" && "input-modern--error", effectiveVariant === "success" && "input-modern--success", loading && "opacity-50 cursor-not-allowed", "focus-smooth spring-smooth", className )} aria-invalid={error ? "true" : "false"} aria-describedby={ error ? `${selectId}-error` : helpText ? `${selectId}-help` : undefined } disabled={loading || props.disabled} {...props} > {/* Placeholder option */} {placeholder && ( <option value="" disabled> {placeholder} </option> )} {/* Options from array */} {options.map((option) => ( <option key={option.value} value={option.value} disabled={option.disabled} > {option.label} </option> ))} {/* Children options (for custom usage) */} {children} </select> {/* Error icon */} {error && ( <div className="form-error-icon"> <svg className="h-5 w-5 text-accent-500" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true" > <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" /> </svg> </div> )} {/* Loading spinner */} {loading && ( <div className="absolute inset-y-0 right-8 pr-3 flex items-center"> <span className="spinner" aria-hidden="true" /> </div> )} </div> {/* Error message */} {error && ( <div id={`${selectId}-error`} className="form-error" role="alert" aria-live="polite" > {error} </div> )} {/* Help text */} {helpText && !error && ( <div id={`${selectId}-help`} className="form-help"> {helpText} </div> )} </div> ); } ); Select.displayName = "Select"; export default Select; 