# Repository Async/Await Usage Guide

This guide clarifies when to use async/await in repository methods.

## Key Principle

**Better-sqlite3 is synchronous** - Our database operations are synchronous by design, so repository methods should generally NOT be async unless they perform actual asynchronous operations.

## When to Use Async

Repository methods should ONLY be async when they:
1. Call external APIs
2. Use an async database driver (we don't)
3. Perform file I/O operations
4. Need to use Promise-based utilities

## Current State

### Correct Pattern (Synchronous)
```typescript
export class ContactRepository extends BaseRepository {
  getAllContacts(): Contact[] {
    // Synchronous database operations
    const contacts = this.db.prepare('SELECT * FROM contact').all();
    return contacts;
  }
}
```

### Incorrect Pattern (Unnecessary Async)
```typescript
export class EstimateDraftsRepository {
  async findAll(): Promise<DraftEstimateSummary[]> {
    // This is still synchronous despite async keyword!
    const drafts = db.prepare('SELECT * FROM estimate').all();
    return drafts;
  }
}
```

## Migration Plan

1. **Keep repositories synchronous** - Since better-sqlite3 is synchronous
2. **Services can be async** - Services often coordinate multiple operations
3. **Controllers should be async** - For proper Express error handling

## Benefits of Synchronous Repositories

1. **Performance** - No unnecessary Promise overhead
2. **Simplicity** - Easier to understand and debug
3. **Consistency** - All repositories follow the same pattern
4. **Type safety** - Better TypeScript inference without Promise wrappers

## Example Architecture

```typescript
// Repository (synchronous)
class UserRepository extends BaseRepository {
  getUserById(id: string): User | null {
    return this.db.prepare('SELECT * FROM users WHERE id = ?').get(id);
  }
}

// Service (can be async if needed)
class UserService {
  async getUserWithDetails(id: string) {
    const repo = new UserRepository();
    const user = repo.getUserById(id);
    
    // Maybe call external API here
    const externalData = await fetchExternalUserData(id);
    
    return { ...user, ...externalData };
  }
}

// Controller (async for Express)
async function getUser(req: Request, res: Response) {
  try {
    const service = new UserService();
    const user = await service.getUserWithDetails(req.params.id);
    res.json(user);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch user' });
  }
}
```

## Action Items

1. Remove unnecessary async keywords from EstimateDraftsRepository
2. Ensure all other repositories remain synchronous
3. Only add async when actually needed for asynchronous operations
4. Update any new repositories to follow this pattern

## Note on Transactions

Better-sqlite3 transactions are also synchronous:

```typescript
db.transaction(() => {
  // All operations here are synchronous
  const result1 = db.prepare('INSERT...').run();
  const result2 = db.prepare('UPDATE...').run();
})(); // Execute immediately
```

No need for async/await with transactions!