# CRM System Technical Documentation

## 1. Overview

This document provides technical details about the implementation of the CRM and Deal Management system in Upstream. The CRM has undergone a complete architectural overhaul to provide a modern, efficient interface with enhanced navigation, search capabilities, and contextual information display.

## 2. System Architecture

### 2.1 New CRM Architecture Overview

The CRM system has been redesigned with a three-section architecture that emphasizes:
- **Modular Organization**: Pipeline, Directory, and Intelligence sections
- **Keyboard-First Navigation**: Command palette with Cmd+K access
- **Contextual Information**: Side panels for viewing details without losing context
- **Unified Search**: Single search interface for all entities
- **Modern UI Patterns**: Responsive design with dark mode support

### 2.2 Frontend Architecture

```mermaid
graph TD
    A[CRMPage.tsx] --> B[layouts/CRMLayout.tsx]
    B --> C[pipeline/EnhancedDealBoard.tsx]
    B --> D[directory/UnifiedSearch.tsx]
    B --> E[intelligence/IntelligenceDashboard.tsx]
    
    B --> F[shared/CommandPalette.tsx]
    B --> G[shared/ContextualSidePanel.tsx]
    
    C --> H[pipeline/DealColumn.tsx]
    C --> I[pipeline/DealCard.tsx]
    C --> J[pipeline/QuickCreateDeal.tsx]
    
    K[HubSpot/HubSpotIntegration.tsx] --> B
    L[DataManagement/DataManagementPage.tsx] --> B
    
    M[config/crm-routes.tsx] --> A
```

### 2.3 Backend Architecture

The backend architecture remains largely unchanged but serves the new frontend components:

```mermaid
graph TD
    A[routes/crm.ts] --> B[repositories/deal-repository.ts]
    A --> C[repositories/company-repository.ts]
    A --> D[repositories/contact-repository.ts]
    A --> E[repositories/note-repository.ts]
    
    B --> F[base-repository.ts]
    C --> F
    D --> F
    E --> F
    
    F --> G[SQLite Database]
    
    H[frontend/api/crm.ts] --> A
    
    I[routes/hubspot.ts] --> J[services/hubspot-service.ts]
    J --> K[HubSpot API]
```

### 2.4 Key Components

#### New Frontend Components

1. **CRMLayout.tsx**: Main layout component managing the three-section architecture
   - Handles navigation between Pipeline, Directory, and Intelligence
   - Manages command palette and side panel states
   - Provides context to child components

2. **EnhancedDealBoard.tsx**: Modern Kanban board for deal pipeline
   - Multiple view modes (board, table, calendar, forecast)
   - Quick filters and search
   - Real-time metrics display
   - Compact view toggle
   - Only supports linking internal estimates (Harvest estimates are read-only)

3. **UnifiedSearch.tsx**: Universal search interface
   - Search across contacts and companies (deals hidden for focus)
   - Visual results grid with full data loading (up to 1000 records)
   - Entity type filtering (contacts, companies)
   - Instant navigation to details
   - Optimized performance: removed pagination, removed saved searches panel

4. **CommandPalette.tsx**: Keyboard-driven navigation
   - Global search with Cmd+K
   - Quick actions (create deal, create contact)
   - Navigation shortcuts
   - AI-powered search (placeholder)

5. **ContextualSidePanel.tsx**: Slide-in detail views
   - Inline editing capabilities
   - Multiple tabs (Overview, Activity, Contacts, Notes)
   - Quick actions
   - Link to full page view

6. **DealCard.tsx & DealColumn.tsx**: Enhanced pipeline components
   - Visual indicators for deal status
   - Drag and drop functionality
   - Quick actions on hover
   - Responsive design

7. **QuickCreateDeal.tsx**: Streamlined deal creation
   - Modal interface
   - Auto-probability calculation
   - Field validation
   - Keyboard navigation

8. **OpportunityIntelligence.tsx**: AI-driven business insights
   - Automated opportunity detection
   - Risk assessment (coverage gaps, project endings)
   - Expansion opportunities (network analysis)
   - Renewal tracking for Harvest projects

9. **ProjectHistory.tsx**: Contact project timeline
   - Harvest integration for time tracking data
   - Project-based activity summaries
   - Historical work visualization
   - Performance analytics

10. **NetworkVisualization.tsx**: Relationship mapping
    - Visual network graphs for contact relationships
    - Company coverage analysis
    - Team collaboration insights
    - Relationship strength indicators

#### Backend Components

1. **routes/crm.ts**: Standard API routes for CRM operations
2. **routes/crm/network.ts**: Network analysis and relationship routes
   - `/opportunities/:companyId`: Opportunity intelligence endpoint
   - `/contacts/:id/project-history`: Contact project timeline
   - `/relationships/*`: Relationship management endpoints
   - `/team/coverage/*`: Team coverage analysis endpoints
3. **repositories/**: Data access layer with type-safe interfaces
   - **contact-relationships-repository.ts**: Relationship network management
   - **team-coverage-repository.ts**: Team coverage analytics
4. **services/hubspot-service.ts**: HubSpot integration logic
   - Now imports notes/activities from HubSpot engagements
   - Imports contact-company and deal-contact associations
   - Maintains strict import order for data integrity
5. **utils/deal-tracking.ts**: Field ownership management

## 3. Data Model

### 3.1 Field Mapping Issues Fixed

The CRM overhaul addressed critical field mapping issues between the mock data structure and actual database schema:

#### Deal Field Corrections

| Mock Field | Database Field | Description |
|------------|----------------|-------------|
| amount | value | Deal monetary value |
| company | companyName | Associated company name (from JOIN) |
| closeDate | expectedCloseDate | Expected deal close date |
| ownerId | owner | Deal owner identifier |
| isAtRisk | (removed) | Non-existent field removed |

#### Stage Name Corrections

| Mock Stage | Database Stage | Description |
|------------|----------------|-------------|
| identified | Identified | Initial stage (proper case) |
| qualified | Qualified | Qualified opportunity |
| solution_proposal | Solution proposal | Proposal stage |
| closed_won | Closed won | Successfully closed |
| closed_lost | Closed lost | Lost opportunity |

### 3.2 Database Schema (Unchanged)

The underlying database schema remains the same, with proper field usage now enforced in the UI components. Key tables include:

- `deal`: Core deal information with corrected field references
- `company`: Company data with linking capabilities
- `contact`: Contact information
- `deal_estimate`: Links between deals and estimates (only internal estimates allowed)
- `contact_role`: Deal-contact relationships (imports HubSpot associations)
- `note`: Deal notes and activities (imports HubSpot engagements)
- `contact_company`: Contact-company relationships (syncs with HubSpot)

## 4. Navigation Structure

### 4.1 Route Configuration

The new CRM uses a nested routing structure defined in `config/crm-routes.tsx`:

```typescript
<Routes>
  <Route path="/" element={<CRMLayout />}>
    <Route index element={<Navigate to="pipeline" />} />
    <Route path="pipeline" element={<EnhancedDealBoard />} />
    <Route path="directory" element={<UnifiedSearch />} />
    <Route path="intelligence" element={<CRMIntelligence />} />
    <Route path="hubspot" element={<HubSpotIntegration />} />
    <Route path="data-management" element={<DataManagementPage />} />
  </Route>
</Routes>
```

### 4.2 Navigation Types

1. **Primary Navigation**: Pipeline, Directory, Intelligence
2. **Secondary Navigation**: HubSpot Sync, Data Management
3. **Quick Navigation**: Command Palette (Cmd+K)
4. **Contextual Navigation**: Side panels and inline links

## 5. State Management

### 5.1 Component State

Each major component manages its own state:

- **EnhancedDealBoard**: View mode, filters, search, compact view
- **CommandPalette**: Search query, results, open/closed state
- **ContextualSidePanel**: Entity type, entity ID, active tab
- **UnifiedSearch**: Search term, entity filter, results

### 5.2 Data Fetching

- Uses React Query for data fetching and caching
- Queries are properly typed with TypeScript
- Error handling with fallback UI
- Loading states with skeleton screens

## 6. HubSpot Integration

### 6.1 Import Process Updates

The import process now correctly handles field mapping:

```typescript
// HubSpot field mapping in hubspot-service.ts
const dealData = {
  name: hubspotDeal.properties.dealname,
  stage: stageMapping[hubspotDeal.properties.dealstage] || 'Identified',
  value: parseFloat(hubspotDeal.properties.amount) || 0,
  companyId: hubspotDeal.associations?.company?.results[0]?.id,
  // ... other fields
};
```

### 6.2 Enhanced Import Capabilities (January 2025)

The HubSpot integration now imports additional data types:

#### Notes and Activities
- Imports all HubSpot engagements (notes, emails, calls, meetings, tasks)
- Maps engagement types to internal note types
- Preserves original timestamps and content
- Links notes to correct entities using polymorphic associations

#### Association Import
- **Contact-Company Associations**: Uses `contact_company` junction table
- **Deal-Contact Associations**: Uses `contact_role` table
- Preserves HubSpot relationship metadata (roles, primary status)
- Prevents duplicate associations

### 6.3 Reset Functionality

Enhanced CRM data reset with proper foreign key handling:

1. Delete junction tables first (no foreign keys)
2. Delete dependent records in order
3. Handle all relationship tables
4. Transaction support for atomicity

## 7. Component Communication

### 7.1 Context Usage

The CRMLayout provides context to child components:

```typescript
interface CRMContext {
  openEntity: (type: 'deal' | 'contact' | 'company', id: string) => void;
}
```

### 7.2 Event Handling

- Keyboard events: Handled by useHotkeys hook
- Drag and drop: React DnD for pipeline
- Click events: Navigation and actions
- Form submissions: Controlled components

## 8. Performance Optimizations

### 8.1 Code Splitting

- Lazy loading of route components
- Dynamic imports for heavy components
- Separate bundles for each major section

### 8.2 Data Optimization

- Memoized calculations for metrics
- Debounced search inputs
- Virtual scrolling for large lists (planned)
- Efficient re-renders with React.memo

## 9. Error Handling

### 9.1 Frontend Error Handling

- Try-catch blocks in async operations
- Error boundaries for component failures
- User-friendly error messages
- Fallback UI for failed data loads

### 9.2 Backend Error Handling

- Consistent error response format
- Transaction rollback on failures
- Detailed error logging
- Foreign key constraint handling

## 10. Testing Considerations

### 10.1 Component Testing

Key areas to test:
- Field mapping correctness
- Stage filtering logic
- Search functionality
- Navigation flows
- Error states

### 10.2 Integration Testing

- HubSpot import with field mapping
- Deal creation with proper fields
- Company linking functionality
- Data reset operations

## 11. Migration Notes

### 11.1 From Old to New CRM

When migrating from the old CRM to the new architecture:

1. **Update imports**: Change from Board/DealBoard to pipeline/EnhancedDealBoard
2. **Fix field references**: Update all uses of old field names
3. **Update stage values**: Use proper case stage names
4. **Remove mock data**: Delete mockDealData.ts and references
5. **Update navigation**: Use new route structure

### 11.2 Breaking Changes

- Field name changes (amount → value, etc.)
- Stage name format (lowercase → proper case)
- Route structure (/crm/deals → /crm/pipeline)
- Component locations (new directory structure)

## 12. Network Intelligence & Opportunity Detection

### 12.1 Opportunity Intelligence System

The system now includes automated opportunity detection through AI-powered analysis:

#### Opportunity Types Detected

1. **Coverage Risk Assessment**
   - Analyzes team coverage gaps across companies
   - Identifies companies with insufficient relationship coverage
   - Provides recommendations for team member assignments

2. **Project Renewal Detection**
   - Monitors Harvest projects approaching end dates
   - Identifies renewal opportunities 30 days in advance
   - Tracks project budgets and completion rates

3. **Network Expansion Analysis**
   - Analyzes relationship network size and strength
   - Identifies companies with strong referral potential
   - Maps connection types and relationship paths

4. **Engagement Risk Monitoring**
   - Tracks interaction frequency and recency
   - Identifies relationships at risk of going stale
   - Provides proactive engagement recommendations

#### Implementation Architecture

```typescript
// Opportunity Intelligence Flow
1. Company → Opportunity Analysis
2. Risk Assessment → Coverage Repository
3. Harvest Integration → Project Timeline Analysis
4. Network Analysis → Relationship Mapping
5. Scoring → Priority Recommendations
```

### 12.2 Relationship Network Management

#### Contact Relationships
- Many-to-many relationship tracking between contacts
- Relationship types: knows, reports_to, introduced_by, worked_with, colleague
- Strength scoring (1-5 scale)
- Context and history tracking

#### Team Coverage Analytics
- Company-level coverage assessment
- Role-based coverage mapping
- Risk level calculations (high, medium, low)
- Team member assignment recommendations

#### Network Visualization
- Visual relationship graphs using D3.js integration
- Interactive node exploration
- Relationship strength indicators
- Team coverage heat maps

## 13. Harvest Estimate Handling (January 2025 Update)

### 13.1 Overview

The CRM system has been simplified to make Harvest estimates read-only:

- **Internal estimates only**: Only internal (draft) estimates can be linked to deals
- **Harvest estimates are read-only**: Visible in the system but cannot be linked/unlinked
- **Existing links preserved**: Historical Harvest estimate links remain visible
- **Simplified codebase**: Removed complex Harvest estimate synchronization logic

### 13.2 Component Changes

#### Frontend Components Updated

1. **DealEstimatesSection.tsx**
   - Removed Harvest estimate linking capability
   - Shows "Read-only" badge for Harvest estimates
   - Unlink button disabled for Harvest estimates

2. **EstimateLinkModal.tsx**
   - Only displays internal estimates
   - Removed Harvest estimate selection option
   - Enhanced UI with dropdown select and estimate details preview
   - Only handles internal estimate linking
   - Used throughout the application (SimpleEstimateLinkModal removed)

4. **EstimatesList.tsx**
   - Harvest estimates remain visible
   - "View in Harvest" button for external access
   - No linking functionality for Harvest estimates

#### Backend Changes

1. **API Endpoints**
   - `POST /api/crm/deals/:id/estimates` - Only accepts 'draft' type
   - `DELETE /api/crm/deals/:id/estimates/:estimateId` - Only works for draft estimates

2. **Repository Updates**
   - `DealEstimateRepository.linkDealToEstimate()` - Validates estimate type
   - Throws error if attempting to link Harvest estimate

3. **Utility Functions**
   - `updateDealsFromEstimate()` - Only processes internal estimates
   - Removed Harvest estimate synchronization logic

### 13.3 Migration Impact

For existing systems with Harvest estimate links:

1. **Data Preservation**: Existing links remain in the database
2. **UI Display**: Harvest estimates show as read-only
3. **No Modifications**: Cannot unlink or modify Harvest estimate relationships
4. **New Workflow**: Must create internal estimates for new deal linking

### 13.4 Field Ownership System

When an estimate is linked to a deal, certain fields become controlled by the estimate:

1. **Controlled Fields**:
   - `value` - Deal value matches estimate total
   - `startDate` - Project start date from estimate
   - `endDate` - Project end date from estimate
   - `invoiceFrequency` - How often to invoice
   - `paymentTerms` - Payment terms (Net 7, 14, 20, 30)
   - `probability` - Set based on deal stage when value is set

2. **UI Behavior**:
   - Controlled fields show "Linked to estimate" hint
   - Fields are disabled and use special styling
   - LinkedEstimateModal displays which fields are controlled
   - DealEstimatesSection shows info banner about controlled fields

3. **Backend Behavior**:
   - Field ownership tracked in `field_ownership` table
   - Owner set to 'Estimate' when estimate is linked
   - Ownership cleared when estimate is unlinked
   - Fields become editable again after unlinking

### 13.5 Developer Guidelines

When working with estimates in the CRM:

1. **Always check estimate type** before attempting operations
2. **Use 'draft' type** for all new estimate-deal links
3. **Handle read-only state** in UI components
4. **Provide clear messaging** about Harvest estimate limitations
5. **Pass fieldOwnership prop** to components that display controlled fields
6. **Check field ownership** using `isFieldControlledByEstimate` utility

## 14. Future Enhancements

### 14.1 Planned Features

1. **Advanced Intelligence Dashboard**: Enhanced analytics and insights
2. **Table View**: Spreadsheet-like deal management
3. **Calendar View**: Timeline-based visualization
4. **Forecast View**: Revenue projections
5. **Activity Feed**: Real-time activity tracking
6. **Advanced Filtering**: Multi-criteria filters
7. **Bulk Operations**: Multi-select and batch updates
8. **Predictive Scoring**: ML-based opportunity scoring
9. **Automated Workflows**: Trigger-based actions

### 14.2 Technical Debt

- Complete TypeScript coverage
- Unit test coverage
- Performance monitoring
- Accessibility improvements
- Mobile optimization

## 15. Developer Guidelines

### 15.1 Adding New Features

1. Follow the established component structure
2. Use the correct field names from the database
3. Implement proper error handling
4. Add loading and error states
5. Consider keyboard navigation
6. Test with real data, not mocks

### 15.2 Common Pitfalls

- Using old field names (amount, company, closeDate)
- Hardcoding stage values in lowercase
- Not handling null/undefined values
- Forgetting to check table existence
- Not following the routing structure