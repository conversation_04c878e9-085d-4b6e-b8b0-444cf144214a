/**
 * Production Server Entry Point
 * 
 * Simple, reliable entry point for Render.com deployment.
 * Handles both compiled JavaScript and TypeScript source.
 */

const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Upstream Financial Dashboard');
console.log('Environment:', process.env.NODE_ENV || 'production');
console.log('Port:', process.env.PORT || 3002);

// Determine server file location
const serverPaths = [
  // Compiled JavaScript (preferred)
  path.join(__dirname, 'dist', 'src', 'api', 'server.js'),
  path.join(__dirname, 'dist', 'api', 'server.js'),
  
  // TypeScript source (fallback)
  path.join(__dirname, 'src', 'api', 'server.ts')
];

let serverFile = null;
for (const serverPath of serverPaths) {
  if (fs.existsSync(serverPath)) {
    serverFile = serverPath;
    console.log('Found server file:', serverFile);
    break;
  }
}

if (!serverFile) {
  console.error('❌ No server file found. Checked paths:', serverPaths);
  process.exit(1);
}

try {
  if (serverFile.endsWith('.ts')) {
    // Register ts-node for TypeScript execution
    require('ts-node').register({
      project: path.join(__dirname, 'tsconfig.backend.json'),
      transpileOnly: true,
      compilerOptions: {
        module: 'commonjs',
        target: 'es2020',
        skipLibCheck: true,
        esModuleInterop: true
      }
    });
  }
  
  // Start the server
  require(serverFile);
  
} catch (error) {
  console.error('❌ Failed to start server:', error.message);
  console.error(error.stack);
  process.exit(1);
}