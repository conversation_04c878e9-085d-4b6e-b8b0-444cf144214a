import React from 'react'; import { useDrop } from 'react-dnd'; import { DealCard } from './DealCard'; import type { Deal, DealEstimate } from '../../../types/crm-types'; interface DealColumnProps { stage: { id: string; name: string; color: string; }; deals: Deal[]; onMoveDeal: (dealId: string, newStage: string) => void; onDealClick: (deal: Deal) => void; compactView: boolean; dealEstimatesMap: Record<string, DealEstimate[]>; onLinkEstimate: (dealId: string) => void; onViewLinkedEstimates: (dealId: string) => void; } export const DealColumn: React.FC<DealColumnProps> = ({ stage, deals, onMoveDeal, onDealClick, compactView, dealEstimatesMap, onLinkEstimate, onViewLinkedEstimates }) => { const [{ isOver, canDrop }, drop] = useDrop({ accept: 'deal', drop: (item: { id: string }) => { onMoveDeal(item.id, stage.id); }, canDrop: (item: { id: string }) => { // Don't allow dropping on the same column const deal = deals.find(d => d.id === item.id); return !deal; }, collect: (monitor) => ({ isOver: !!monitor.isOver(), canDrop: !!monitor.canDrop() }) }); // Calculate column metrics const totalValue = deals.reduce((sum, deal) => sum + (deal.value || 0), 0); const weightedValue = deals.reduce((sum, deal) => sum + ((deal.value || 0) * (deal.probability || 0) / 100), 0 ); // Determine if this is a final stage const isFinalStage = ['Closed won', 'Closed lost', 'Abandoned'].includes(stage.id); return ( <div ref={drop} className={`flex-shrink-0 w-80 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden transition-all duration-200 ${ isOver && canDrop ? 'ring-2 ring-purple-500 bg-purple-50 dark:bg-purple-900/20' : '' }`} > {/* Column Header */} <div className="p-3 border-b border-gray-200 dark:border-gray-700"> <div className="flex items-center justify-between mb-2"> <div className="flex items-center gap-2"> <div className={`w-3 h-3 rounded-full ${stage.color}`} /> <h3 className="font-medium text-gray-900 dark:text-white"> {stage.name} </h3> <span className="text-sm text-gray-500 dark:text-gray-400"> {deals.length} </span> </div> {!isFinalStage && ( <button className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"> <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /> </svg> </button> )} </div> {/* Column Metrics */} <div className="grid grid-cols-2 gap-2 text-xs"> <div> <span className="text-gray-500 dark:text-gray-400">Total</span> <div className="font-medium text-gray-900 dark:text-white"> ${totalValue.toLocaleString()} </div> </div> <div> <span className="text-gray-500 dark:text-gray-400">Weighted</span> <div className="font-medium text-purple-600 dark:text-purple-400"> ${weightedValue.toLocaleString()} </div> </div> </div> </div> {/* Deals Container */} <div className="p-2 space-y-2 overflow-y-auto overflow-x-hidden" style={{ maxHeight: 'calc(100vh - 300px)' }}> {deals.length === 0 ? ( <div className="text-center py-8 text-gray-400 dark:text-gray-500"> <div className="text-sm italic"> {isOver && canDrop ? 'Drop deal here' : 'No deals'} </div> </div> ) : ( deals.map(deal => ( <DealCard key={deal.id} deal={deal} onClick={() => onDealClick(deal)} compact={compactView} estimates={dealEstimatesMap[deal.id] || []} onLinkEstimate={() => onLinkEstimate(deal.id)} onViewLinkedEstimates={() => onViewLinkedEstimates(deal.id)} /> )) )} </div> {/* Quick Add Deal */} {!isFinalStage && ( <div className="p-2 border-t border-gray-200 dark:border-gray-700"> <button className="w-full py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"> + Add deal </button> </div> )} </div> ); }; export default DealColumn;