# Onbord Data Model Reference

## Overview

Onbord uses a relational database with **31 core tables** defined in `/migrations/000_unified_schema.sql`.

## Key Design Principles

1. **Soft Delete**: All tables have `deleted_at` field
2. **Audit Trail**: created_at, updated_at, created_by, updated_by on all tables
3. **Safe Foreign Keys**: ON DELETE SET NULL (except junction tables which use CASCADE)
4. **External IDs**: hubspot_id, harvest_id, xero_id for integration
5. **Repository Pattern**: All data access through `/src/api/repositories/`

## Core Tables (By Category)

### CRM (8 tables)
- `company` - Organizations
- `contact` - People
- `deal` - Sales opportunities
- `note` - Threaded conversations (supports HubSpot engagement imports: notes, emails, calls, meetings, tasks)
- `contact_company` - Contact-company relationships (syncs with HubSpot associations)
- `contact_role` - Contact roles in deals (imports HubSpot deal-contact associations)
- `deal_estimate` - Deal-estimate links
- `radar_action_items` - Action tracking for companies needing investigation

### Financial (6 tables)
- `estimate` - Project proposals
- `estimate_allocation` - Staff assignments
- `estimate_time_allocation` - Weekly planning
- `expense` - Expense tracking
- `project` - Active projects
- `project_contact` - Project team members

### Integration (4 tables)
- `harvest_invoice_cache` - Cached invoice data
- `hubspot_settings` - HubSpot configuration (tracks import history and progress)
- `field_ownership` - System field control
  - Tracks which system owns each field (Manual, HubSpot, Estimate, Deal)
  - Database stores lowercase values ('manual', 'hubspot', 'estimate', 'deal')
  - Prevents conflicting updates from different systems
  - Automatically normalized for case-insensitive comparisons
- `settings` - Application settings

### Enrichment (3 tables)
- `company_enrichment` - External company data
- `contact_enrichment` - External contact data
- `enrichment_log` - Enrichment attempts

### Audit/System (4 tables)
- `activity_feed` - Activity tracking
- `change_log` - Field-level changes
- `migrations` - Schema versioning
- `cashflow_snapshot` - Financial snapshots

### Potentially Unused (2 tables)
These exist but have no active UI:
- `company_relationship` - Backend ready but UI disabled
- `opportunity_intelligence` - No implementation

**Total: 31 tables** (29 after removing unused)

## Integration Mappings

- Companies: `hubspot_id`, `harvest_id`, `xero_id`
- Contacts: `hubspot_id`, `harvest_user_id`
- Deals: `hubspot_id`
- Projects: `harvest_project_id`

## Next Steps

1. Fix CASCADE DELETE → SET NULL on non-junction tables
2. Remove 4 unused tables
3. Ensure all queries use soft delete (`WHERE deleted_at IS NULL`)

See [`/plans/proposed/database-clean-slate-migration.md`](../plans/proposed/database-clean-slate-migration.md) for optimization plan.