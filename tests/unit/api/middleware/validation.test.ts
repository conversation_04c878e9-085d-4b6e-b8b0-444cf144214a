import { Request, Response, NextFunction } from 'express';
import { validateRequest, validateBody, validateQuery, validateParams } from '../../../../src/api/middleware/validation';
import { z } from 'zod';

describe('Validation Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      body: {},
      query: {},
      params: {}
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    mockNext = jest.fn();
  });

  describe('validateBody', () => {
    it('should pass valid body data', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number()
      });

      mockRequest.body = { name: '<PERSON>', age: 30 };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockResponse.status).not.toHaveBeenCalled();
    });

    it('should reject invalid body data', () => {
      const schema = z.object({
        name: z.string(),
        age: z.number()
      });

      mockRequest.body = { name: 'John', age: 'thirty' };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid request body',
        details: expect.arrayContaining([
          expect.objectContaining({
            path: ['age'],
            message: expect.any(String)
          })
        ])
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle missing required fields', () => {
      const schema = z.object({
        name: z.string(),
        email: z.string().email()
      });

      mockRequest.body = { name: 'John' };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid request body',
        details: expect.arrayContaining([
          expect.objectContaining({
            path: ['email'],
            message: expect.any(String)
          })
        ])
      });
    });
  });

  describe('validateQuery', () => {
    it('should pass valid query parameters', () => {
      const schema = z.object({
        page: z.string().transform(Number),
        limit: z.string().transform(Number).optional()
      });

      mockRequest.query = { page: '1', limit: '10' };

      const middleware = validateQuery(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockRequest.query).toEqual({ page: 1, limit: 10 });
    });

    it('should reject invalid query parameters', () => {
      const schema = z.object({
        page: z.string().transform(Number),
        sortBy: z.enum(['name', 'date'])
      });

      mockRequest.query = { page: 'abc', sortBy: 'invalid' };

      const middleware = validateQuery(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid query parameters',
        details: expect.any(Array)
      });
    });

    it('should handle optional query parameters', () => {
      const schema = z.object({
        search: z.string().optional(),
        filter: z.string().optional()
      });

      mockRequest.query = { search: 'test' };

      const middleware = validateQuery(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect(mockRequest.query).toEqual({ search: 'test' });
    });
  });

  describe('validateParams', () => {
    it('should pass valid route parameters', () => {
      const schema = z.object({
        id: z.string().uuid(),
        type: z.enum(['user', 'admin'])
      });

      mockRequest.params = { 
        id: '123e4567-e89b-12d3-a456-************',
        type: 'user'
      };

      const middleware = validateParams(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should reject invalid route parameters', () => {
      const schema = z.object({
        id: z.string().uuid()
      });

      mockRequest.params = { id: 'not-a-uuid' };

      const middleware = validateParams(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid route parameters',
        details: expect.any(Array)
      });
    });
  });

  describe('validateRequest', () => {
    it('should validate all request parts', () => {
      const schema = {
        body: z.object({ name: z.string() }),
        query: z.object({ page: z.string() }),
        params: z.object({ id: z.string() })
      };

      mockRequest = {
        body: { name: 'John' },
        query: { page: '1' },
        params: { id: '123' }
      };

      const middleware = validateRequest(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should fail if any part is invalid', () => {
      const schema = {
        body: z.object({ name: z.string() }),
        query: z.object({ page: z.string().transform(Number) })
      };

      mockRequest = {
        body: { name: 123 }, // Invalid
        query: { page: '1' }
      };

      const middleware = validateRequest(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('Custom validation', () => {
    it('should handle custom validation rules', () => {
      const schema = z.object({
        password: z.string().min(8).regex(/[A-Z]/, 'Must contain uppercase'),
        confirmPassword: z.string()
      }).refine(data => data.password === data.confirmPassword, {
        message: 'Passwords do not match',
        path: ['confirmPassword']
      });

      mockRequest.body = {
        password: 'ValidPass123',
        confirmPassword: 'DifferentPass'
      };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid request body',
        details: expect.arrayContaining([
          expect.objectContaining({
            path: ['confirmPassword'],
            message: 'Passwords do not match'
          })
        ])
      });
    });

    it('should handle date validation', () => {
      const schema = z.object({
        startDate: z.string().datetime(),
        endDate: z.string().datetime()
      }).refine(data => new Date(data.endDate) > new Date(data.startDate), {
        message: 'End date must be after start date',
        path: ['endDate']
      });

      mockRequest.body = {
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2023-12-31T00:00:00Z'
      };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
    });
  });

  describe('Error formatting', () => {
    it('should format nested errors properly', () => {
      const schema = z.object({
        user: z.object({
          profile: z.object({
            email: z.string().email()
          })
        })
      });

      mockRequest.body = {
        user: {
          profile: {
            email: 'not-an-email'
          }
        }
      };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid request body',
        details: expect.arrayContaining([
          expect.objectContaining({
            path: ['user', 'profile', 'email'],
            message: expect.any(String)
          })
        ])
      });
    });

    it('should handle array validation errors', () => {
      const schema = z.object({
        items: z.array(z.object({
          name: z.string(),
          quantity: z.number().positive()
        }))
      });

      mockRequest.body = {
        items: [
          { name: 'Item 1', quantity: 5 },
          { name: 'Item 2', quantity: -1 }
        ]
      };

      const middleware = validateBody(schema);
      middleware(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Invalid request body',
        details: expect.arrayContaining([
          expect.objectContaining({
            path: ['items', 1, 'quantity'],
            message: expect.any(String)
          })
        ])
      });
    });
  });
});