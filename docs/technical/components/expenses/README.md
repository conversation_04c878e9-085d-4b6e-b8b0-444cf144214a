# Expenses System Technical Documentation

## 1. Overview

This document provides technical details about the implementation of the Expenses Management system in Upstream. It covers the architecture, data model, key components, and integration with external systems like Xero.

## 2. System Architecture

The Expenses system consists of several frontend and backend components that work together to provide a comprehensive expense management solution:

### 2.1 Frontend Architecture

```mermaid
graph TD
    A[ExpensePage.tsx] --> B[ExpenseList.tsx]
    A --> C[ExpenseForm.tsx]
    A --> D[ExpenseFilters.tsx]
    A --> E[XeroExpenseImport.tsx]
    B --> F[ExpenseCard.tsx]
    E --> G[XeroBillsList.tsx]
    E --> H[XeroPayrollImport.tsx]
    E --> I[XeroGSTImport.tsx]

    J[hooks/useExpenses.ts] --> B
    K[hooks/useExpenseForm.ts] --> C
    L[hooks/useXeroBills.ts] --> G
    M[hooks/useXeroPayroll.ts] --> H
    N[hooks/useXeroGST.ts] --> I
```

### 2.2 Backend Architecture

```mermaid
graph TD
    A[routes/expenses.ts] --> B[controllers/expenses.ts]
    B --> C[repositories/expense-repository.ts]
    C --> D[SQLite Database]

    E[frontend/api/expenses.ts] --> A

    F[routes/xero.ts] --> G[integrations/xero.ts]
    G --> H[Xero API]

    I[services/xero/bills-service.ts] --> G
    J[services/xero/payroll-service.ts] --> G
    K[services/xero/gst-service.ts] --> G
```

### 2.3 Key Components

#### Frontend

1. **ExpensePage.tsx**: Main component for the Expenses feature
2. **ExpenseList.tsx**: List of custom expenses with filtering and sorting
3. **ExpenseForm.tsx**: Form for creating and editing expenses
4. **ExpenseFilters.tsx**: Filters for expense type and frequency
5. **XeroExpenseImport.tsx**: Interface for importing expenses from Xero
6. **XeroBillsList.tsx**: List of bills from Xero with import functionality
7. **XeroPayrollImport.tsx**: Interface for importing payroll expenses from Xero
8. **XeroGSTImport.tsx**: Interface for importing GST/BAS expenses from Xero

#### Backend

1. **routes/expenses.ts**: API routes for expense operations
2. **expense-repository.ts**: Data access layer for expense data
3. **routes/xero.ts**: API routes for Xero integration
4. **bills-service.ts**: Service for fetching and processing Xero bills
5. **payroll-service.ts**: Service for fetching and processing Xero payroll data
6. **gst-service.ts**: Service for calculating GST/BAS payments

## 3. Data Model

### 3.1 Database Schema

The Expenses feature uses several tables in the SQLite database:

#### Expense

```sql
CREATE TABLE expense (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  amount REAL NOT NULL,
  frequency TEXT CHECK(frequency IN ('weekly', 'fortnightly', 'monthly', 'quarterly', 'one-off')) NOT NULL,
  type TEXT CHECK(type IN ('Monthly Payroll', 'Superannuation', 'Insurances', 'Taxes', 'Subcontractor Fees', 'Rent', 'Reimbursements', 'Professional Fees', 'General Expenses', 'Director Distributions', 'Hardware', 'Subscriptions', 'Other Fees', 'Other')) NOT NULL,
  date TEXT NOT NULL,              -- Primary expense date (NOT start_date)
  repeat_count INTEGER,            -- Number of times to repeat
  source TEXT DEFAULT 'manual',    -- Source of expense (manual, xero-bill, etc.)
  description TEXT,                -- Expense description
  metadata TEXT,                   -- JSON metadata from external systems
  editable INTEGER DEFAULT 1,      -- 1 = editable, 0 = read-only
  end_date TEXT,                   -- Optional end date for recurring expenses
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  deleted_at TEXT
)
```

**Key Schema Notes:**
- The table uses `date` (not `start_date`) as the primary expense date
- `type` field has specific allowed values matching the expense type constants
- `frequency` includes 'fortnightly' option
- `metadata` stores JSON data from external systems like Xero
- `source` tracks where the expense came from (manual entry, Xero bill conversion, etc.)
- Supports soft delete with `deleted_at` field

### 3.2 TypeScript Types

The Expenses feature uses several TypeScript interfaces to represent the data:

```typescript
// Core expense model
interface Expense {
  id: string;
  name: string;
  amount: number;
  frequency: ExpenseFrequency;
  type: ExpenseType;
  date: string;           // ISO date string (NOT startDate)
  repeatCount?: number;
  source?: string;        // 'manual', 'xero-bill', etc.
  description?: string;
  metadata?: Record<string, any>;
  editable?: boolean;     // true = can be edited, false = read-only
  endDate?: string;       // ISO date string
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

// Expense frequency enum
type ExpenseFrequency = 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'one-off';

// Expense type enum - matches database CHECK constraint
type ExpenseType = 
  | 'Monthly Payroll' 
  | 'Superannuation' 
  | 'Insurances' 
  | 'Taxes' 
  | 'Subcontractor Fees' 
  | 'Rent' 
  | 'Reimbursements' 
  | 'Professional Fees' 
  | 'General Expenses' 
  | 'Director Distributions' 
  | 'Hardware' 
  | 'Subscriptions' 
  | 'Other Fees' 
  | 'Other';

// Expense source enum
type ExpenseSource = 'manual' | 'xero_bill' | 'xero_payroll' | 'xero_tax' | 'xero_super';

// Xero bill model
interface XeroBill {
  id: string;
  number: string;
  reference?: string;
  date: string;
  dueDate: string;
  amount: number;
  status: string;
  contact: {
    id: string;
    name: string;
  };
  lineItems: XeroBillLineItem[];
  imported: boolean;
}

// Xero payroll model
interface XeroPayroll {
  id: string;
  payPeriod: string;
  paymentDate: string;
  netPay: number;
  tax: number;
  superannuation: number;
  reimbursements: number;
  totalCost: number;
  employees: number;
}

// Xero GST model
interface XeroGST {
  quarter: string;
  startDate: string;
  endDate: string;
  dueDate: string;
  totalSales: number;
  gstAmount: number;
  status: 'accrued' | 'predicted';
}
```

## 4. Expense Management

### 4.1 Creating Expenses

The system allows users to create custom expenses with various types and frequencies:

#### Expense Types

- Monthly Payroll (blue)
- Software (purple)
- Superannuation (amber)
- Insurances (pink)
- Taxes (indigo)
- Fees (rose)
- Other (gray)

#### Expense Frequencies

- Weekly (blue)
- Monthly (violet)
- Quarterly (orange)
- One-off (slate)

Implementation:

```typescript
// Create a new expense
function createExpense(expense: Expense): string {
  // Validate required fields
  if (!expense.name || !expense.type || expense.amount === undefined || !expense.date || !expense.frequency) {
    throw new Error('Missing required fields for expense creation');
  }

  const id = uuidv4();
  const now = new Date().toISOString();

  // Convert metadata to JSON string if it exists
  const metadataJson = expense.metadata ? JSON.stringify(expense.metadata) : null;

  // Store date as ISO string
  db.prepare(`
    INSERT INTO expense (
      id, name, type, amount, date, frequency, repeat_count,
      source, description, metadata, editable, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `).run(
    id,
    expense.name,
    expense.type,
    expense.amount,
    expense.date.toISOString(),
    expense.frequency,
    expense.repeatCount || null,
    expense.source || 'manual', // Default to 'manual' if source is not provided
    expense.description || null,
    metadataJson,
    1, // editable = true for manually created expenses
    now,
    now
  );

  return id;
}

// Calculate monthly equivalent amount
function calculateMonthlyEquivalent(amount: number, frequency: ExpenseFrequency): number {
  switch (frequency) {
    case 'weekly':
      return amount * 4.33; // Average weeks per month
    case 'monthly':
      return amount;
    case 'quarterly':
      return amount / 3;
    case 'one-off':
      return 0; // One-off expenses don't have a monthly equivalent
    default:
      return 0;
  }
}
```

### 4.2 Generating Occurrences

For recurring expenses, the system generates occurrences based on the frequency:

```typescript
// Generate expense occurrences
function generateExpenseOccurrences(expenseId: number, expense: Expense): void {
  const startDate = new Date(expense.date);
  const endDate = expense.endDate ? new Date(expense.endDate) : addYears(startDate, 1);

  let currentDate = startDate;

  while (currentDate <= endDate) {
    db.prepare(`
      INSERT INTO expense_occurrences (expense_id, date, amount, status)
      VALUES (?, ?, ?, ?)
    `).run(
      expenseId,
      currentDate.toISOString().split('T')[0],
      expense.amount,
      'pending'
    );

    // Calculate next occurrence date based on frequency
    switch (expense.frequency) {
      case 'weekly':
        currentDate = addWeeks(currentDate, 1);
        break;
      case 'monthly':
        currentDate = addMonths(currentDate, 1);
        break;
      case 'quarterly':
        currentDate = addMonths(currentDate, 3);
        break;
      case 'one-off':
        // One-off expenses only have a single occurrence
        currentDate = new Date(endDate.getTime() + 1);
        break;
    }
  }
}
```

## 5. Xero Integration

### 5.1 Bills Integration

The system allows users to view and selectively import bills from Xero:

```typescript
// Fetch bills from Xero
async function getXeroBills(accessToken: string, tenantId: string, days: number = 90): Promise<XeroBill[]> {
  const fromDate = subDays(new Date(), days).toISOString().split('T')[0];

  const response = await fetch(
    `https://api.xero.com/api.xro/2.0/Invoices?where=Type=="ACCPAY" AND Date>="${fromDate}"`,
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Xero-Tenant-Id': tenantId,
        'Accept': 'application/json'
      }
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch Xero bills: ${response.statusText}`);
  }

  const data = await response.json();

  // Transform Xero bills to internal format
  return data.Invoices.map((invoice: any) => ({
    id: invoice.InvoiceID,
    number: invoice.InvoiceNumber,
    reference: invoice.Reference,
    date: invoice.Date,
    dueDate: invoice.DueDate,
    amount: invoice.Total,
    status: invoice.Status,
    contact: {
      id: invoice.Contact.ContactID,
      name: invoice.Contact.Name
    },
    lineItems: invoice.LineItems.map((item: any) => ({
      description: item.Description,
      quantity: item.Quantity,
      unitAmount: item.UnitAmount,
      taxAmount: item.TaxAmount,
      lineAmount: item.LineAmount
    })),
    // Check if this bill has already been imported
    imported: checkIfBillImported(invoice.InvoiceID)
  }));
}

// Import a bill as an expense
function importBillAsExpense(bill: XeroBill): string {
  return createExpense({
    name: `Bill: ${bill.contact.name} - ${bill.number}`,
    amount: bill.amount,
    date: new Date(bill.dueDate),
    frequency: 'one-off',
    type: 'Other',
    source: 'xero_bill',
    externalId: bill.id,
    reference: bill.number,
    description: bill.reference || `Xero bill #${bill.number}`,
    metadata: {
      contactId: bill.contact.id,
      contactName: bill.contact.name,
      lineItems: bill.lineItems
    }
  });
}
```

### 5.2 Payroll Integration

The system allows users to view and create recurring expenses from Xero payroll data:

```typescript
// Fetch payroll data from Xero
async function getXeroPayroll(accessToken: string, tenantId: string): Promise<XeroPayroll> {
  // Get the most recent pay run
  const response = await fetch(
    'https://api.xero.com/payroll.xro/1.0/PayRuns?status=posted',
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Xero-Tenant-Id': tenantId,
        'Accept': 'application/json'
      }
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch Xero payroll: ${response.statusText}`);
  }

  const data = await response.json();

  // Get the most recent pay run
  const latestPayRun = data.PayRuns.sort((a: any, b: any) =>
    new Date(b.PaymentDate).getTime() - new Date(a.PaymentDate).getTime()
  )[0];

  // Get the details of the pay run
  const detailsResponse = await fetch(
    `https://api.xero.com/payroll.xro/1.0/PayRuns/${latestPayRun.PayRunID}`,
    {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Xero-Tenant-Id': tenantId,
        'Accept': 'application/json'
      }
    }
  );

  if (!detailsResponse.ok) {
    throw new Error(`Failed to fetch Xero payroll details: ${detailsResponse.statusText}`);
  }

  const detailsData = await detailsResponse.json();

  // Calculate totals
  const netPay = detailsData.PayRun.Wages;
  const tax = detailsData.PayRun.PAYE;
  const superannuation = detailsData.PayRun.Super;
  const reimbursements = detailsData.PayRun.Reimbursements;
  const totalCost = netPay + tax + superannuation + reimbursements;

  return {
    id: latestPayRun.PayRunID,
    payPeriod: `${latestPayRun.PayPeriodStartDate} to ${latestPayRun.PayPeriodEndDate}`,
    paymentDate: latestPayRun.PaymentDate,
    netPay,
    tax,
    superannuation,
    reimbursements,
    totalCost,
    employees: detailsData.PayRun.Employees.length
  };
}

// Create payroll expenses
function createPayrollExpenses(payroll: XeroPayroll): string[] {
  const expenseIds = [];

  // Create net pay expense
  expenseIds.push(createExpense({
    name: 'Monthly Payroll - Net Pay',
    amount: payroll.netPay,
    date: new Date(payroll.paymentDate),
    frequency: 'monthly',
    type: 'Monthly Payroll',
    source: 'xero_payroll',
    externalId: payroll.id,
    description: `Monthly payroll for ${payroll.employees} employees`,
    metadata: {
      payPeriod: payroll.payPeriod,
      employees: payroll.employees,
      totalCost: payroll.totalCost
    }
  }));

  // Create tax expense (PAYGW)
  const taxDate = new Date(payroll.paymentDate);
  taxDate.setDate(21); // Tax is due on the 21st of the following month
  taxDate.setMonth(taxDate.getMonth() + 1);

  expenseIds.push(createExpense({
    name: 'Monthly PAYGW Tax',
    amount: payroll.tax,
    date: taxDate,
    frequency: 'monthly',
    type: 'Tax',
    source: 'xero_tax',
    externalId: payroll.id,
    description: 'Monthly PAYGW tax payment',
    metadata: {
      payPeriod: payroll.payPeriod,
      taxType: 'PAYGW'
    }
  }));

  // Create superannuation expense
  const superDate = new Date(payroll.paymentDate);
  superDate.setDate(28); // Super is typically due on the 28th
  superDate.setMonth(superDate.getMonth() + 1);

  expenseIds.push(createExpense({
    name: 'Monthly Superannuation',
    amount: payroll.superannuation,
    date: superDate,
    frequency: 'monthly',
    type: 'Superannuation',
    source: 'xero_super',
    externalId: payroll.id,
    description: 'Monthly superannuation payment',
    metadata: {
      payPeriod: payroll.payPeriod,
      employees: payroll.employees
    }
  }));

  return expenseIds;
}
```
