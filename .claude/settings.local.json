{"permissions": {"allow": ["Bash(grep:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(find:*)", "mcp__playwright__browser_click", "mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "Bash(find:*)", "Bash(ls:*)", "mcp__playwright__browser_drag", "<PERSON><PERSON>(mv:*)", "Bash(npx tsc:*)", "Bash(npm run lint:*)", "Bash(rg:*)", "<PERSON><PERSON>(sed:*)", "mcp__context7__resolve-library-id", "Bash(npm test:*)", "mcp__context7__get-library-docs", "Bash(npm test:*)", "<PERSON><PERSON>(diff:*)", "Bash(rm:*)", "Bash(sqlite3:*)", "Bash(npm run build:*)", "WebFetch(domain:help.getharvest.com)", "WebFetch(domain:github.com)", "Bash(npm run kill-ports:*)", "mcp__zen__thinkdeep", "mcp__zen__chat", "mcp__zen__thinkdeep", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)"], "deny": []}}