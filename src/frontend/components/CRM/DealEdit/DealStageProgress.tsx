import React from "react";
import { DealStage } from "../../../types/crm-types";

interface DealStageProgressProps {
  currentStage: DealStage;
}

/**
 * Component for showing the deal stage and its progress with a beautiful visual indicator
 */
const DealStageProgress: React.FC<DealStageProgressProps> = ({ currentStage }) => {
  // Define all stages in order
  const DEAL_STAGES: DealStage[] = [
    "Identified",
    "Qualified",
    "Solution proposal",
    "Solution presentation",
    "Objection handling",
    "Finalising terms",
    "Closed won",
  ];

  // Exclude "Closed lost" and "Abandoned" from the progression
  // but handle them specially in the UI
  const isNegativeOutcome = currentStage === "Closed lost" || currentStage === "Abandoned";

  // Get current stage index, or -1 if not found
  const currentStageIndex = DEAL_STAGES.indexOf(currentStage);

  // Calculate progress percentage for the progress bar
  const getProgressPercentage = (): number => {
    if (isNegativeOutcome) return 0;
    if (currentStageIndex === -1) return 0;
    return (currentStageIndex / (DEAL_STAGES.length - 1)) * 100;
  };

  // Get color class based on stage
  const getColorClass = (): string => {
    if (currentStage === "Closed won") return "bg-green-500 dark:bg-green-600";
    if (currentStage === "Closed lost") return "bg-red-500 dark:bg-red-600";
    if (currentStage === "Abandoned") return "bg-gray-500 dark:bg-gray-600";
    return "bg-blue-500 dark:bg-blue-600";
  };

  return (
    <div className="mt-2 mb-6">
      {/* Stage Label */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Stage:
          </span>
          <span 
            className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
              currentStage === "Closed won" 
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                : currentStage === "Closed lost" || currentStage === "Abandoned"
                ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            }`}
          >
            {currentStage}
          </span>
        </div>
        
        {/* Progress percentage (only show for positive stages in progress) */}
        {!isNegativeOutcome && currentStage !== "Closed won" && (
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {Math.round(getProgressPercentage())}% Complete
          </div>
        )}
      </div>

      {/* Progress Bar */}
      <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div
          className={`h-full ${getColorClass()} rounded-full transition-all duration-500 ease-in-out`}
          style={{ width: `${getProgressPercentage()}%` }}
        ></div>
      </div>

      {/* Stage Markers */}
      <div className="relative w-full mt-1">
        <div className="flex justify-between">
          {DEAL_STAGES.map((stage, index) => {
            const isActive = !isNegativeOutcome && index <= currentStageIndex;
            const isCurrentStage = stage === currentStage;
            
            return (
              <div 
                key={stage} 
                className="flex flex-col items-center"
                style={{ width: `${100 / DEAL_STAGES.length}%` }}
              >
                {/* Stage Dot */}
                <div 
                  className={`
                    w-3 h-3 rounded-full mb-1
                    ${isActive 
                      ? getColorClass() 
                      : "bg-gray-300 dark:bg-gray-600"}
                    ${isCurrentStage 
                      ? "ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-800" 
                      : ""}
                  `}
                ></div>
                
                {/* Stage Label (only show first, last, and current) */}
                {(index === 0 || index === DEAL_STAGES.length - 1 || isCurrentStage) && (
                  <span 
                    className={`
                      text-xs whitespace-nowrap 
                      ${isActive 
                        ? "font-medium text-gray-700 dark:text-gray-300" 
                        : "text-gray-500 dark:text-gray-400"}
                      ${isCurrentStage 
                        ? "font-bold" 
                        : ""}
                    `}
                  >
                    {stage}
                  </span>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Special indicator for negative outcomes */}
      {isNegativeOutcome && (
        <div className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd"></path>
          </svg>
          Deal has been {currentStage.toLowerCase()} and is no longer active
        </div>
      )}
    </div>
  );
};

export default DealStageProgress;