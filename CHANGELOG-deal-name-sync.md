# Deal Name Sync Update - December 2025

## Overview
Fixed an issue where HubSpot-synced deals with linked estimates couldn't have their names edited, creating a confusing split state between the actual deal name and the HubSpot name.

## Changes Made

### Backend Changes
1. **Removed deal name from estimate-controlled fields** (`/src/api/routes/crm/deals.ts`)
   - Deal names are no longer locked when estimates are linked
   - Only financial fields (value, dates, payment terms, invoice frequency) are controlled by estimates

2. **Simplified field ownership logic** (`/src/api/routes/crm/deals.ts`)
   - When linking an estimate, only the estimate's project_name is marked as Deal-controlled
   - Deal name field ownership is never set to 'Estimate'

### Frontend Changes
1. **Fixed deal name editing** (`/src/frontend/components/CRM/DealEdit/DealHeader.tsx`)
   - Deal name field now checks actual field ownership instead of assuming HubSpot controls it
   - Users can edit deal names even when linked to estimates

2. **Clarified UI text** (`/src/frontend/components/Estimate/EstimateConfigurationForm.tsx`)
   - Changed estimate project name label from "(syncs with deal name)" to "(follows deal name)"
   - Makes the one-way relationship clearer to users

### Documentation Updates
1. **Updated CLAUDE.md**
   - Changed from "Bidirectional Sync" to "One-Way Name Sync"
   - Added HubSpot Sync Fix section explaining the changes

2. **Updated API documentation** (`docs/api-reference/deal-estimate-api.md`)
   - Clarified that deal names are NOT controlled by estimates
   - Added new section on Name Synchronization explaining the one-way flow

3. **Updated HubSpot integration docs** (`docs/technical/integrations/hubspot/README.md`)
   - Added note that deal name field is always editable and can always be updated by HubSpot

## Result
- Deal names can always be edited by users
- HubSpot can always update deal names during sync
- Estimate project names automatically follow deal names (one-way flow)
- No circular update risks
- Clear, predictable behavior

## Migration Notes
No database migration required. Existing field ownership records will be respected, and the issue self-corrects as users interact with deals.