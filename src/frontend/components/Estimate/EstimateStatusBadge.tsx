import React from "react";
import { getStatusStyles } from "./utils";

interface EstimateStatusBadgeProps {
  status: string;
  className?: string;
}

/**
 * A reusable badge component for displaying estimate status
 *
 * Supported statuses: 'draft', 'sent', 'accepted', 'declined'
 * Each status has appropriate styling based on its meaning
 */
const EstimateStatusBadge: React.FC<EstimateStatusBadgeProps> = ({
  status,
  className = "",
}) => {
  const { className: statusClass, label } = getStatusStyles(status);

  return (
    <span
      className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded ${statusClass} ${className}`}
    >
      {label}
    </span>
  );
};

export default EstimateStatusBadge;
