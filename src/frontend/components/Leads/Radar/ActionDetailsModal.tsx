/**
 * Action Details Modal Component
 * 
 * Modal for viewing and updating radar action details
 */

import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { XMarkIcon, CheckCircleIcon, XCircleIcon, PlayIcon } from '@heroicons/react/24/outline';
import type { RadarAction, UpdateRadarAction } from '../../../../types/radar-action-types';
import { RADAR_ACTION_TYPE_META, RADAR_ACTION_PRIORITY_META } from '../../../../types/radar-action-types';
import { updateRadarAction, completeRadarAction, cancelRadarAction } from '../../../api/radar-actions';

interface ActionDetailsModalProps {
  action: RadarAction;
  onClose: () => void;
  onUpdate: () => void;
}

const ActionDetailsModal: React.FC<ActionDetailsModalProps> = ({
  action,
  onClose,
  onUpdate
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [completionNotes, setCompletionNotes] = useState('');
  const [cancellationNotes, setCancellationNotes] = useState('');
  const [showCompleteForm, setShowCompleteForm] = useState(false);
  const [showCancelForm, setShowCancelForm] = useState(false);
  
  const queryClient = useQueryClient();
  const typeMeta = RADAR_ACTION_TYPE_META[action.actionType];
  const priorityMeta = RADAR_ACTION_PRIORITY_META[action.priority];

  // Edit form state
  const [editData, setEditData] = useState({
    title: action.title,
    description: action.description || '',
    assignedTo: action.assignedTo,
    priority: action.priority,
    dueDate: action.dueDate || '',
    notes: action.notes || ''
  });

  // Mutations
  const updateMutation = useMutation(
    (updates: Omit<UpdateRadarAction, 'updatedBy'>) => updateRadarAction(action.id, updates),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        setIsEditing(false);
        onUpdate();
      }
    }
  );

  const completeMutation = useMutation(
    (notes: string) => completeRadarAction(action.id, notes),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        onClose();
        onUpdate();
      }
    }
  );

  const cancelMutation = useMutation(
    (notes: string) => cancelRadarAction(action.id, notes),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        onClose();
        onUpdate();
      }
    }
  );

  const startMutation = useMutation(
    () => updateRadarAction(action.id, { status: 'in_progress' }),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        onUpdate();
      }
    }
  );

  const handleSaveEdit = () => {
    updateMutation.mutate({
      title: editData.title.trim(),
      description: editData.description.trim() || undefined,
      assignedTo: editData.assignedTo.trim(),
      priority: editData.priority,
      dueDate: editData.dueDate || undefined,
      notes: editData.notes.trim() || undefined
    });
  };

  const handleComplete = () => {
    completeMutation.mutate(completionNotes);
  };

  const handleCancel = () => {
    cancelMutation.mutate(cancellationNotes);
  };

  const handleStart = () => {
    startMutation.mutate();
  };

  const formatDate = (date: string | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString();
  };

  const formatDateTime = (date: string | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleString();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl" title={typeMeta.label}>
                {typeMeta.icon}
              </span>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {isEditing ? (
                  <input
                    type="text"
                    value={editData.title}
                    onChange={(e) => setEditData(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
                  />
                ) : (
                  action.title
                )}
              </h2>
            </div>
            
            <div className="flex items-center gap-4 text-sm">
              <span className={`px-2 py-1 rounded-full ${priorityMeta.bgColor} ${priorityMeta.textColor}`}>
                {priorityMeta.label} Priority
              </span>
              <span className="text-gray-500 dark:text-gray-400">
                Status: {action.status.replace('_', ' ')}
              </span>
              {action.company && (
                <span className="text-gray-500 dark:text-gray-400">
                  {action.company.name}
                </span>
              )}
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 ml-4"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </h3>
            {isEditing ? (
              <textarea
                value={editData.description}
                onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                rows={3}
              />
            ) : (
              <p className="text-gray-600 dark:text-gray-400">
                {action.description || 'No description provided'}
              </p>
            )}
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Assigned To
              </h3>
              {isEditing ? (
                <input
                  type="email"
                  value={editData.assignedTo}
                  onChange={(e) => setEditData(prev => ({ ...prev, assignedTo: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                />
              ) : (
                <p className="text-gray-600 dark:text-gray-400">{action.assignedTo}</p>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Due Date
              </h3>
              {isEditing ? (
                <input
                  type="date"
                  value={editData.dueDate}
                  onChange={(e) => setEditData(prev => ({ ...prev, dueDate: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                />
              ) : (
                <p className="text-gray-600 dark:text-gray-400">{formatDate(action.dueDate)}</p>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Created
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {formatDateTime(action.createdAt)} by {action.createdBy}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Started
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {action.startedAt ? formatDateTime(action.startedAt) : 'Not started'}
              </p>
            </div>
          </div>

          {/* Notes */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Notes
            </h3>
            {isEditing ? (
              <textarea
                value={editData.notes}
                onChange={(e) => setEditData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700"
                rows={3}
              />
            ) : (
              <p className="text-gray-600 dark:text-gray-400">
                {action.notes || 'No notes added'}
              </p>
            )}
          </div>

          {/* Completion Notes */}
          {action.completionNotes && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Completion Notes
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {action.completionNotes}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">
                Completed {formatDateTime(action.completedAt)}
              </p>
            </div>
          )}

          {/* Complete Form */}
          {showCompleteForm && (
            <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Complete Action
              </h3>
              <textarea
                value={completionNotes}
                onChange={(e) => setCompletionNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 mb-3"
                rows={3}
                placeholder="Add completion notes..."
              />
              <div className="flex gap-2">
                <button
                  onClick={handleComplete}
                  disabled={completeMutation.isLoading}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium transition-colors disabled:opacity-50"
                >
                  {completeMutation.isLoading ? 'Completing...' : 'Mark Complete'}
                </button>
                <button
                  onClick={() => {
                    setShowCompleteForm(false);
                    setCompletionNotes('');
                  }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md font-medium transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}

          {/* Cancel Form */}
          {showCancelForm && (
            <div className="border-t pt-4">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cancel Action
              </h3>
              <textarea
                value={cancellationNotes}
                onChange={(e) => setCancellationNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 mb-3"
                rows={3}
                placeholder="Reason for cancellation..."
              />
              <div className="flex gap-2">
                <button
                  onClick={handleCancel}
                  disabled={cancelMutation.isLoading}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium transition-colors disabled:opacity-50"
                >
                  {cancelMutation.isLoading ? 'Cancelling...' : 'Cancel Action'}
                </button>
                <button
                  onClick={() => {
                    setShowCancelForm(false);
                    setCancellationNotes('');
                  }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md font-medium transition-colors"
                >
                  Keep Action
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center mt-6 pt-6 border-t">
          <div className="flex gap-2">
            {action.status === 'pending' && (
              <button
                onClick={handleStart}
                disabled={startMutation.isLoading}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors disabled:opacity-50"
              >
                <PlayIcon className="w-4 h-4" />
                {startMutation.isLoading ? 'Starting...' : 'Start Work'}
              </button>
            )}
            
            {action.status !== 'completed' && action.status !== 'cancelled' && !showCompleteForm && !showCancelForm && (
              <>
                <button
                  onClick={() => setShowCompleteForm(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md font-medium transition-colors"
                >
                  <CheckCircleIcon className="w-4 h-4" />
                  Complete
                </button>
                <button
                  onClick={() => setShowCancelForm(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-medium transition-colors"
                >
                  <XCircleIcon className="w-4 h-4" />
                  Cancel
                </button>
              </>
            )}
          </div>

          <div className="flex gap-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSaveEdit}
                  disabled={updateMutation.isLoading}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md font-medium transition-colors disabled:opacity-50"
                >
                  {updateMutation.isLoading ? 'Saving...' : 'Save Changes'}
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditData({
                      title: action.title,
                      description: action.description || '',
                      assignedTo: action.assignedTo,
                      priority: action.priority,
                      dueDate: action.dueDate || '',
                      notes: action.notes || ''
                    });
                  }}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md font-medium transition-colors"
                >
                  Cancel Edit
                </button>
              </>
            ) : (
              action.status !== 'completed' && action.status !== 'cancelled' && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md font-medium transition-colors"
                >
                  Edit
                </button>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActionDetailsModal;