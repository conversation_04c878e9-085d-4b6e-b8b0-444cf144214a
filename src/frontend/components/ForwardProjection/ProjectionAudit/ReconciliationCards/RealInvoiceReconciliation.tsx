import React from 'react'; import { ProcessedDecision } from '../../types/projection-audit-types'; import { formatDate, formatCurrency } from '../../utils'; import { InvoiceDisplay } from '../../../shared/InvoiceDisplay'; interface RealInvoiceReconciliationProps { decision: ProcessedDecision; } const RealInvoiceReconciliation: React.FC<RealInvoiceReconciliationProps> = ({ decision }) => { return ( <div className="flex flex-col space-y-2 p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"> {/* Title and explanation */} <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-2"> <div className="flex items-center"> <svg className="w-3.5 h-3.5 text-yellow-500 dark:text-yellow-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> <div className="text-[11px] font-medium text-gray-700 dark:text-gray-300"> Real Invoice Reconciliation </div> </div> </div> {/* Main content - Adjusted layout for better spacing and responsiveness */} {/* Stacks vertically on small screens, row on medium+ */} <div className="flex flex-col md:flex-row items-stretch md:items-center justify-between gap-2"> {/* Left side - Excluded projected income */} <div className="flex-1 p-2 bg-gray-50 dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-700 space-y-1"> <div className="flex items-center gap-1"> <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"> <span className="line-through">Projected</span> </span> <div className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate"> {decision.formattedDescription} </div> </div> <div className="flex justify-between items-end"> {/* Align date/amount */} <div className="text-xs text-gray-500 dark:text-gray-400"> <span className="font-bold uppercase tracking-wide block">Date</span> {/* Use block */} {formatDate(decision.paymentDate)} </div> <div className="text-xs text-gray-500 dark:text-gray-400 text-right"> <span className="font-bold uppercase tracking-wide block">Amount</span> {/* Use block */} {formatCurrency(decision.amount)} </div> </div> </div> {/* Center connector - Adjusted padding and rotation for responsiveness */} <div className="flex-shrink-0 py-1 md:py-0 md:px-1 self-center"> {/* Center arrow vertically when stacked */} <div className="btn-modern--primary"dark:bg-green-900/30 rounded-full p-1.5"> {/* Rotates arrow based on screen size */} <svg className="w-5 h-5 text-green-500 dark:text-green-400 transform rotate-90 md:rotate-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M14 5l7 7m0 0l-7 7m7-7H3" /> </svg> </div> </div> {/* Right side - Real invoice */} <div className="flex-1 p-2 bg-orange-50/50 dark:bg-orange-900/5 rounded border border-orange-100 dark:border-orange-900/20 space-y-1"> {decision.relatedInvoiceId && ( <div className="mb-1"> {/* Keep margin for InvoiceDisplay */} <InvoiceDisplay invoiceId={decision.relatedInvoiceId} invoiceNumber={decision.relatedInvoiceNumber} issueDate={decision.relatedInvoiceDate} /> </div> )} <div className="flex justify-between items-end"> {/* Align date/amount */} <div className="text-xs text-gray-500 dark:text-gray-400"> <span className="font-bold uppercase tracking-wide block">Issue</span> {/* Use block */} {formatDate(decision.relatedInvoiceDate || decision.invoiceDate)} </div> <div className="text-xs text-gray-500 dark:text-gray-400"> <span className="font-bold uppercase tracking-wide block">Due</span> {/* Use block */} {formatDate(decision.relatedInvoiceDueDate || decision.paymentDate)} </div> <div className="text-xs text-gray-500 dark:text-gray-400 text-right"> <span className="font-bold uppercase tracking-wide block">Amount</span> {/* Use block */} {formatCurrency(decision.amount)} </div> </div> </div> </div> {/* Note at the bottom */} <div className="mt-1 text-xs text-gray-500 dark:text-gray-400 pl-2 border-l-2 border-yellow-300/50 dark:border-yellow-700"> {/* Increased size */} <span className="font-medium text-yellow-600 dark:text-yellow-400">Note:</span> This projected invoice was excluded because a real invoice exists in the system for this project. </div> </div> ); }; export default RealInvoiceReconciliation; 