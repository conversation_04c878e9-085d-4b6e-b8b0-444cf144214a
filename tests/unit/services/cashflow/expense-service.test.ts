import { ExpenseService } from '../../../../src/services/cashflow/expense-service';
import { ExpensesRepository } from '../../../../src/api/repositories/expenses-repository';
import type { Expense } from '../../../../src/types/financial';

jest.mock('../../../../src/api/repositories/expenses-repository');

describe('ExpenseService', () => {
  let service: ExpenseService;
  let mockRepository: jest.Mocked<ExpensesRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockRepository = new ExpensesRepository(null as any) as jest.Mocked<ExpensesRepository>;
    service = new ExpenseService(mockRepository);
  });

  describe('getExpensesByDateRange', () => {
    const mockExpenses: Expense[] = [
      {
        id: '1',
        date: '2024-01-15',
        amount: 500,
        category: 'Supplies',
        vendor: 'Office Depot',
        description: 'Office supplies',
        paymentMethod: 'Credit Card',
        isRecurring: false,
        recurringFrequency: null,
        status: 'paid',
        attachments: [],
        taxAmount: 50,
        project: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: '2',
        date: '2024-01-20',
        amount: 1200,
        category: 'Software',
        vendor: 'Adobe',
        description: 'Creative Suite subscription',
        paymentMethod: 'Bank Transfer',
        isRecurring: true,
        recurringFrequency: 'monthly',
        status: 'pending',
        attachments: [],
        taxAmount: 120,
        project: 'Project A',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    it('should fetch expenses for date range', async () => {
      mockRepository.getByDateRange.mockResolvedValue(mockExpenses);

      const result = await service.getExpensesByDateRange('2024-01-01', '2024-01-31');

      expect(result).toEqual(mockExpenses);
      expect(mockRepository.getByDateRange).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
    });

    it('should handle empty results', async () => {
      mockRepository.getByDateRange.mockResolvedValue([]);

      const result = await service.getExpensesByDateRange('2024-01-01', '2024-01-31');

      expect(result).toEqual([]);
    });

    it('should handle repository errors', async () => {
      mockRepository.getByDateRange.mockRejectedValue(new Error('Database error'));

      await expect(service.getExpensesByDateRange('2024-01-01', '2024-01-31'))
        .rejects.toThrow('Database error');
    });
  });

  describe('getExpensesByCategory', () => {
    it('should group expenses by category', async () => {
      const expenses: Expense[] = [
        {
          id: '1',
          date: '2024-01-15',
          amount: 500,
          category: 'Supplies',
          vendor: 'Vendor A',
          description: 'Item 1',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 50,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          date: '2024-01-16',
          amount: 300,
          category: 'Supplies',
          vendor: 'Vendor B',
          description: 'Item 2',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 30,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          date: '2024-01-20',
          amount: 1000,
          category: 'Software',
          vendor: 'Vendor C',
          description: 'Software license',
          paymentMethod: 'Bank Transfer',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 100,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      mockRepository.getByDateRange.mockResolvedValue(expenses);

      const result = await service.getExpensesByCategory('2024-01-01', '2024-01-31');

      expect(result).toHaveProperty('Supplies');
      expect(result).toHaveProperty('Software');
      expect(result['Supplies']).toHaveLength(2);
      expect(result['Software']).toHaveLength(1);
      expect(result['Supplies'][0]).toEqual(expenses[0]);
      expect(result['Supplies'][1]).toEqual(expenses[1]);
      expect(result['Software'][0]).toEqual(expenses[2]);
    });

    it('should handle empty categories', async () => {
      mockRepository.getByDateRange.mockResolvedValue([]);

      const result = await service.getExpensesByCategory('2024-01-01', '2024-01-31');

      expect(result).toEqual({});
    });
  });

  describe('getRecurringExpenses', () => {
    it('should filter only recurring expenses', async () => {
      const allExpenses: Expense[] = [
        {
          id: '1',
          date: '2024-01-15',
          amount: 500,
          category: 'Supplies',
          vendor: 'Vendor A',
          description: 'One-time purchase',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 50,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          date: '2024-01-20',
          amount: 1000,
          category: 'Software',
          vendor: 'Adobe',
          description: 'Monthly subscription',
          paymentMethod: 'Bank Transfer',
          isRecurring: true,
          recurringFrequency: 'monthly',
          status: 'paid',
          attachments: [],
          taxAmount: 100,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          date: '2024-01-25',
          amount: 5000,
          category: 'Rent',
          vendor: 'Landlord',
          description: 'Office rent',
          paymentMethod: 'Bank Transfer',
          isRecurring: true,
          recurringFrequency: 'monthly',
          status: 'paid',
          attachments: [],
          taxAmount: 0,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      mockRepository.getRecurring.mockResolvedValue(
        allExpenses.filter(e => e.isRecurring)
      );

      const result = await service.getRecurringExpenses();

      expect(result).toHaveLength(2);
      expect(result.every(e => e.isRecurring)).toBe(true);
      expect(mockRepository.getRecurring).toHaveBeenCalled();
    });

    it('should handle no recurring expenses', async () => {
      mockRepository.getRecurring.mockResolvedValue([]);

      const result = await service.getRecurringExpenses();

      expect(result).toEqual([]);
    });
  });

  describe('getTotalExpensesByPeriod', () => {
    it('should calculate total expenses correctly', async () => {
      const expenses: Expense[] = [
        {
          id: '1',
          date: '2024-01-15',
          amount: 500,
          category: 'Supplies',
          vendor: 'Vendor A',
          description: 'Item 1',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 50,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          date: '2024-01-20',
          amount: 1000,
          category: 'Software',
          vendor: 'Vendor B',
          description: 'Software',
          paymentMethod: 'Bank Transfer',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 100,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          date: '2024-01-25',
          amount: 300,
          category: 'Travel',
          vendor: 'Airlines',
          description: 'Flight',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'pending',
          attachments: [],
          taxAmount: 30,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      mockRepository.getByDateRange.mockResolvedValue(expenses);

      const result = await service.getTotalExpensesByPeriod('2024-01-01', '2024-01-31');

      expect(result).toBe(1800); // 500 + 1000 + 300
    });

    it('should return 0 for no expenses', async () => {
      mockRepository.getByDateRange.mockResolvedValue([]);

      const result = await service.getTotalExpensesByPeriod('2024-01-01', '2024-01-31');

      expect(result).toBe(0);
    });
  });

  describe('getExpenseStatus', () => {
    it('should count expenses by status', async () => {
      const expenses: Expense[] = [
        {
          id: '1',
          date: '2024-01-15',
          amount: 500,
          category: 'Supplies',
          vendor: 'Vendor A',
          description: 'Item 1',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 50,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '2',
          date: '2024-01-20',
          amount: 1000,
          category: 'Software',
          vendor: 'Vendor B',
          description: 'Software',
          paymentMethod: 'Bank Transfer',
          isRecurring: false,
          recurringFrequency: null,
          status: 'paid',
          attachments: [],
          taxAmount: 100,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '3',
          date: '2024-01-25',
          amount: 300,
          category: 'Travel',
          vendor: 'Airlines',
          description: 'Flight',
          paymentMethod: 'Credit Card',
          isRecurring: false,
          recurringFrequency: null,
          status: 'pending',
          attachments: [],
          taxAmount: 30,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: '4',
          date: '2024-01-26',
          amount: 200,
          category: 'Other',
          vendor: 'Vendor C',
          description: 'Misc',
          paymentMethod: 'Cash',
          isRecurring: false,
          recurringFrequency: null,
          status: 'cancelled',
          attachments: [],
          taxAmount: 20,
          project: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      mockRepository.getByDateRange.mockResolvedValue(expenses);

      const result = await service.getExpenseStatus('2024-01-01', '2024-01-31');

      expect(result).toEqual({
        paid: 2,
        pending: 1,
        cancelled: 1
      });
    });

    it('should handle empty status counts', async () => {
      mockRepository.getByDateRange.mockResolvedValue([]);

      const result = await service.getExpenseStatus('2024-01-01', '2024-01-31');

      expect(result).toEqual({
        paid: 0,
        pending: 0,
        cancelled: 0
      });
    });
  });

  describe('createExpense', () => {
    it('should create new expense', async () => {
      const newExpense = {
        date: '2024-01-30',
        amount: 750,
        category: 'Equipment',
        vendor: 'Tech Store',
        description: 'New laptop',
        paymentMethod: 'Credit Card' as const,
        isRecurring: false,
        recurringFrequency: null,
        status: 'pending' as const,
        attachments: [],
        taxAmount: 75,
        project: 'Project X'
      };

      const createdExpense = {
        id: '5',
        ...newExpense,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      mockRepository.create.mockResolvedValue(createdExpense);

      const result = await service.createExpense(newExpense);

      expect(result).toEqual(createdExpense);
      expect(mockRepository.create).toHaveBeenCalledWith(newExpense);
    });
  });

  describe('updateExpense', () => {
    it('should update existing expense', async () => {
      const updatedExpense = {
        id: '1',
        date: '2024-01-30',
        amount: 800,
        category: 'Equipment',
        vendor: 'Tech Store',
        description: 'Updated laptop order',
        paymentMethod: 'Bank Transfer' as const,
        isRecurring: false,
        recurringFrequency: null,
        status: 'paid' as const,
        attachments: [],
        taxAmount: 80,
        project: 'Project X',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: new Date().toISOString()
      };

      mockRepository.update.mockResolvedValue(updatedExpense);

      const result = await service.updateExpense('1', {
        amount: 800,
        description: 'Updated laptop order',
        paymentMethod: 'Bank Transfer',
        status: 'paid'
      });

      expect(result).toEqual(updatedExpense);
      expect(mockRepository.update).toHaveBeenCalledWith('1', {
        amount: 800,
        description: 'Updated laptop order',
        paymentMethod: 'Bank Transfer',
        status: 'paid'
      });
    });
  });

  describe('deleteExpense', () => {
    it('should delete expense', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const result = await service.deleteExpense('1');

      expect(result).toBe(true);
      expect(mockRepository.delete).toHaveBeenCalledWith('1');
    });

    it('should return false if expense not found', async () => {
      mockRepository.delete.mockResolvedValue(false);

      const result = await service.deleteExpense('999');

      expect(result).toBe(false);
    });
  });
});