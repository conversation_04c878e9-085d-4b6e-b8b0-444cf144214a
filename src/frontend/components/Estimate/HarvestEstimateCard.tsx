import React from "react"; import { formatCurrency } from "./utils"; import { format, parseISO } from "date-fns"; import { Card } from "../shared/Card"; interface HarvestEstimateCardProps { estimate: any; StatusBadge: React.FC<{ status: string }>; } export const HarvestEstimateCard: React.FC<HarvestEstimateCardProps> = ({ estimate, StatusBadge, }) => { return ( <Card className="p-4"> {/* Header */} <div className="flex justify-between items-start mb-3"> <div className="flex-1 min-w-0"> <h3 className="font-medium text-gray-900 dark:text-gray-100"> #{estimate.number} </h3> <p className="text-sm text-gray-600 dark:text-gray-400"> {estimate.client?.name || "N/A"} </p> </div> <StatusBadge status={estimate.state} /> </div> {/* Subject */} {estimate.subject && ( <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 truncate"> {estimate.subject} </p> )} {/* Amount and Date */} <div className="flex justify-between items-center mb-4"> <span className="font-medium text-gray-900 dark:text-gray-100"> {formatCurrency(estimate.amount)} </span> <span className="text-sm text-gray-600 dark:text-gray-400"> {estimate.issue_date ? format(parseISO(estimate.issue_date), "MMM d, yyyy") : "-"} </span> </div> {/* Actions */} <div className="flex justify-center"> <a href={`https://onbord.harvestapp.com/estimates/${estimate.id}`} target="_blank" rel="noopener noreferrer" className="inline-flex items-center px-3 py-1.5 rounded text-xs font-medium border border-[#f36c21] text-[#f36c21] hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors" > <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" /> </svg> View in Harvest </a> </div> </Card> ); };