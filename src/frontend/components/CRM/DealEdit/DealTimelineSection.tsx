import React from "react"; import { Deal, DealUpdate } from "../../../types/crm-types"; import { Card } from "../../shared/Card"; import { format, parseISO } from "date-fns"; import { LinkIcon } from "@heroicons/react/24/outline"; import { isFieldControlledByEstimate } from "./utils"; interface DealTimelineSectionProps { deal: Deal; isEditing: boolean; formData: DealUpdate; onChange: (field: keyof DealUpdate, value: any) => void; fieldOwnership?: Record<string, string>; } /** * Component for displaying and editing deal timeline information */ const DealTimelineSection: React.FC<DealTimelineSectionProps> = ({ deal, isEditing, formData, onChange, fieldOwnership, }) => { // Format date to Australian format (DD/MM/YYYY) const formatDate = (dateString?: string): string => { if (!dateString) return "No date set"; try { return format(parseISO(dateString), "dd/MM/yyyy"); } catch (error) { return "Invalid date"; } }; return ( <Card className="overflow-hidden"> <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700"> <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white"> Timeline Information </h3> <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400"> Important dates for this deal. </p> </div> <div className="px-4 py-5 sm:p-6"> <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6"> {/* Expected Close Date */} <div className="sm:col-span-2"> <label htmlFor="expectedCloseDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300" > Expected Close Date </label> <div className="mt-1"> {isEditing ? ( <input type="date" id="expectedCloseDate" value={ formData.expectedCloseDate ? formData.expectedCloseDate.split("T")[0] : "" } onChange={(e) => onChange("expectedCloseDate", e.target.value) } className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white" /> ) : ( <div className="text-sm text-gray-900 dark:text-gray-200"> {formatDate(deal.expectedCloseDate)} </div> )} </div> </div> {/* Project Start Date */} <div className="sm:col-span-2"> <label htmlFor="startDate" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center" > Project Start Date {isFieldControlledByEstimate(deal, "startDate", fieldOwnership) && ( <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" title="This field is controlled by the linked estimate" > <LinkIcon className="w-3 h-3 mr-1" /> Estimate </span> )} </label> <div className="mt-1"> {isEditing ? ( <div className="relative"> <input type="date" id="startDate" value={ formData.startDate ? formData.startDate.split("T")[0] : "" } onChange={(e) => onChange("startDate", e.target.value)} className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${ isFieldControlledByEstimate(deal, "startDate", fieldOwnership) ? "border-blue-300 dark:border-blue-700" : "" }`} disabled={isFieldControlledByEstimate(deal, "startDate", fieldOwnership)} /> {isFieldControlledByEstimate(deal, "startDate", fieldOwnership) && ( <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"> <span className="text-blue-500 dark:text-blue-400" title="This field is controlled by the linked estimate" > <LinkIcon className="w-4 h-4" /> </span> </div> )} </div> ) : ( <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center"> {formatDate(deal.startDate)} {isFieldControlledByEstimate(deal, "startDate", fieldOwnership) && ( <span className="ml-2 text-blue-500 dark:text-blue-400" title="This field is controlled by the linked estimate" > <LinkIcon className="w-4 h-4" /> </span> )} </div> )} {isFieldControlledByEstimate(deal, "startDate", fieldOwnership) && isEditing && ( <p className="mt-1 text-xs text-blue-500 dark:text-blue-400"> This field is controlled by the linked estimate. Changes may be overwritten. </p> )} </div> </div> {/* Project End Date */} <div className="sm:col-span-2"> <label htmlFor="endDate" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center" > Project End Date {isFieldControlledByEstimate(deal, "endDate", fieldOwnership) && ( <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" title="This field is controlled by the linked estimate" > <LinkIcon className="w-3 h-3 mr-1" /> Estimate </span> )} </label> <div className="mt-1"> {isEditing ? ( <div className="relative"> <input type="date" id="endDate" value={ formData.endDate ? formData.endDate.split("T")[0] : "" } onChange={(e) => onChange("endDate", e.target.value)} className={`shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${ isFieldControlledByEstimate(deal, "endDate", fieldOwnership) ? "border-blue-300 dark:border-blue-700" : "" }`} disabled={isFieldControlledByEstimate(deal, "endDate", fieldOwnership)} /> {isFieldControlledByEstimate(deal, "endDate", fieldOwnership) && ( <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"> <span className="text-blue-500 dark:text-blue-400" title="This field is controlled by the linked estimate" > <LinkIcon className="w-4 h-4" /> </span> </div> )} </div> ) : ( <div className="text-sm text-gray-900 dark:text-gray-200 flex items-center"> {formatDate(deal.endDate)} {isFieldControlledByEstimate(deal, "endDate", fieldOwnership) && ( <span className="ml-2 text-blue-500 dark:text-blue-400" title="This field is controlled by the linked estimate" > <LinkIcon className="w-4 h-4" /> </span> )} </div> )} {isFieldControlledByEstimate(deal, "endDate", fieldOwnership) && isEditing && ( <p className="mt-1 text-xs text-blue-500 dark:text-blue-400"> This field is controlled by the linked estimate. Changes may be overwritten. </p> )} </div> </div> </div> </div> </Card> ); }; export default DealTimelineSection; 