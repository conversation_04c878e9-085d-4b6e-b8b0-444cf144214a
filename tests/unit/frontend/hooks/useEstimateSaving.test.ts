import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEstimateSaving } from '../../../../src/frontend/hooks/useEstimateSaving';
import * as api from '../../../../src/frontend/api/estimates';

jest.mock('../../../../src/frontend/api/estimates');

describe('useEstimateSaving', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  it('should save estimate successfully', async () => {
    const mockEstimate = { id: '1', estimate_name: 'Test' };
    (api.updateEstimate as jest.Mock).mockResolvedValue(mockEstimate);

    const { result } = renderHook(() => useEstimateSaving(), { wrapper });

    act(() => {
      result.current.saveEstimate('1', { estimate_name: 'Test' });
    });

    await waitFor(() => {
      expect(result.current.isSaving).toBe(false);
    });

    expect(result.current.saveError).toBeNull();
    expect(api.updateEstimate).toHaveBeenCalledWith('1', { estimate_name: 'Test' });
  });

  it('should handle save errors', async () => {
    const error = new Error('Save failed');
    (api.updateEstimate as jest.Mock).mockRejectedValue(error);

    const { result } = renderHook(() => useEstimateSaving(), { wrapper });

    act(() => {
      result.current.saveEstimate('1', { estimate_name: 'Test' });
    });

    await waitFor(() => {
      expect(result.current.saveError).toBe('Save failed');
    });

    expect(result.current.isSaving).toBe(false);
  });

  it('should auto-save after delay', async () => {
    jest.useFakeTimers();
    const mockEstimate = { id: '1', estimate_name: 'Test' };
    (api.updateEstimate as jest.Mock).mockResolvedValue(mockEstimate);

    const { result } = renderHook(() => useEstimateSaving({ autoSaveDelay: 1000 }), { wrapper });

    act(() => {
      result.current.queueAutoSave('1', { estimate_name: 'Test' });
    });

    expect(api.updateEstimate).not.toHaveBeenCalled();

    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(api.updateEstimate).toHaveBeenCalledWith('1', { estimate_name: 'Test' });
    });

    jest.useRealTimers();
  });

  it('should debounce multiple auto-save calls', async () => {
    jest.useFakeTimers();
    const mockEstimate = { id: '1', estimate_name: 'Final' };
    (api.updateEstimate as jest.Mock).mockResolvedValue(mockEstimate);

    const { result } = renderHook(() => useEstimateSaving({ autoSaveDelay: 1000 }), { wrapper });

    act(() => {
      result.current.queueAutoSave('1', { estimate_name: 'First' });
    });

    act(() => {
      jest.advanceTimersByTime(500);
      result.current.queueAutoSave('1', { estimate_name: 'Second' });
    });

    act(() => {
      jest.advanceTimersByTime(500);
      result.current.queueAutoSave('1', { estimate_name: 'Final' });
    });

    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(api.updateEstimate).toHaveBeenCalledTimes(1);
      expect(api.updateEstimate).toHaveBeenCalledWith('1', { estimate_name: 'Final' });
    });

    jest.useRealTimers();
  });

  it('should track last saved timestamp', async () => {
    const mockEstimate = { id: '1', estimate_name: 'Test' };
    (api.updateEstimate as jest.Mock).mockResolvedValue(mockEstimate);

    const { result } = renderHook(() => useEstimateSaving(), { wrapper });

    expect(result.current.lastSaved).toBeNull();

    act(() => {
      result.current.saveEstimate('1', { estimate_name: 'Test' });
    });

    await waitFor(() => {
      expect(result.current.lastSaved).toBeInstanceOf(Date);
    });
  });

  it('should invalidate queries after successful save', async () => {
    const mockEstimate = { id: '1', estimate_name: 'Test' };
    (api.updateEstimate as jest.Mock).mockResolvedValue(mockEstimate);

    const invalidateQueriesSpy = jest.spyOn(queryClient, 'invalidateQueries');

    const { result } = renderHook(() => useEstimateSaving(), { wrapper });

    act(() => {
      result.current.saveEstimate('1', { estimate_name: 'Test' });
    });

    await waitFor(() => {
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: ['estimates'] });
    });
  });
});