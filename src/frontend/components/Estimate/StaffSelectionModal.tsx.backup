import React, { useState, useEffect, FormEvent } from "react";
import { User, getUsers } from "../../api/harvest";
import { convertHourlyToDailyRate, formatCurrency } from "./utils";

interface StaffSelectionModalProps {
  isOpen: boolean;
  onSelect: (selectedUser: {
    harvestUserId: number;
    firstName: string;
    lastName: string;
    projectRole: string;
    level: string;
    onbordTargetRateDaily: number;
    onbordCostRateDaily: number;
    rateProposedDaily: number;
    isPlaceholder?: boolean; // Flag to identify placeholder staff
    avatarUrl?: string; // Profile photo URL from Harvest
  }) => void;
  onClose: () => void;
  existingUserIds?: number[]; // Optional: To disable selection of already added users
  users?: User[] | null;
  isLoading?: boolean;
  error?: string | null;
}

const StaffSelectionModal: React.FC<StaffSelectionModalProps> = ({
  isOpen,
  onSelect,
  onClose,
  existingUserIds = [],
  users: initialUsers = null,
  isLoading: initialIsLoading = false,
  error: initialError = null,
}) => {
  // State for user list
  const [users, setUsers] = useState<User[]>(initialUsers || []);
  const [filteredUsers, setFilteredUsers] = useState<User[]>(
    initialUsers || []
  );
  const [isLoading, setIsLoading] = useState(initialIsLoading);
  const [error, setError] = useState<string | null>(initialError);
  const [searchTerm, setSearchTerm] = useState("");

  // State for placeholder form
  const [showPlaceholderForm, setShowPlaceholderForm] = useState(false);
  const [placeholderName, setPlaceholderName] = useState("");
  const [placeholderRole, setPlaceholderRole] = useState("");
  const [placeholderLevel, setPlaceholderLevel] = useState("");
  const [placeholderTargetRate, setPlaceholderTargetRate] = useState<number>(0);
  const [placeholderCostRate, setPlaceholderCostRate] = useState<number>(0);
  const [placeholderFormError, setPlaceholderFormError] = useState<
    string | null
  >(null);

  // Fetch users when the modal opens
  useEffect(() => {
    if (isOpen && !initialUsers) {
      const fetchUsers = async () => {
        setIsLoading(true);
        setError(null);
        try {
          const fetchedUsers = await getUsers({ is_active: true });
          setUsers(fetchedUsers);
          setFilteredUsers(fetchedUsers);
        } catch (err: any) {
          console.error("Error fetching users:", err);
          setError(err.message || "Failed to fetch team members");
        } finally {
          setIsLoading(false);
        }
      };

      fetchUsers();
    } else if (initialUsers) {
      setUsers(initialUsers);
      setFilteredUsers(initialUsers);
    }
  }, [isOpen, initialUsers]);

  // Filter users when search term changes
  useEffect(() => {
    if (users.length > 0) {
      const filtered = users.filter((user) => {
        const fullName = `${user.first_name || ""} ${
          user.last_name || ""
        }`.toLowerCase();
        return (
          fullName.includes(searchTerm.toLowerCase()) ||
          (user.email &&
            user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (user.roles &&
            user.roles.some((role) =>
              role.toLowerCase().includes(searchTerm.toLowerCase())
            ))
        );
      });
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  if (!isOpen) {
    return null;
  }

  const handleUserSelect = (user: User) => {
    // Ensure firstName and lastName are present
    const firstName = user.first_name || "Unknown";
    const lastName = user.last_name || "";

    // Determine the primary role or use first available role
    const primaryRole =
      user.roles && user.roles.length > 0 ? user.roles[0] : "Consultant";

    // Safely convert hourly rates to daily with fallbacks
    const defaultHourlyRate = user.default_hourly_rate || 0;
    const costRate = user.cost_rate || 0;

    const targetRateDaily = convertHourlyToDailyRate(defaultHourlyRate);
    const costRateDaily = convertHourlyToDailyRate(costRate);

    // Log selection to verify data
    console.log("Adding team member:", {
      name: `${firstName} ${lastName}`,
      role: primaryRole,
      targetRate: targetRateDaily,
      costRate: costRateDaily,
    });

    onSelect({
      harvestUserId: user.id,
      firstName: firstName,
      lastName: lastName,
      projectRole: primaryRole,
      level: primaryRole, // Default to using role as level
      onbordTargetRateDaily: targetRateDaily,
      onbordCostRateDaily: costRateDaily,
      rateProposedDaily: targetRateDaily, // Default proposed rate to target rate
      avatarUrl: user.avatar_url, // Pass through the avatar URL from Harvest
    });

    // Keep the modal open to allow multiple selections
  };

  // Handle placeholder staff form submission
  const handlePlaceholderSubmit = (e: FormEvent) => {
    e.preventDefault();
    setPlaceholderFormError(null);

    // Validate form fields
    if (!placeholderName.trim()) {
      setPlaceholderFormError("Name is required");
      return;
    }

    if (!placeholderRole.trim()) {
      setPlaceholderFormError("Role is required");
      return;
    }

    if (placeholderTargetRate <= 0) {
      setPlaceholderFormError("Target rate must be greater than 0");
      return;
    }

    if (placeholderCostRate < 0) {
      setPlaceholderFormError("Cost rate cannot be negative");
      return;
    }

    // Generate a negative ID for the placeholder to avoid conflicts with real Harvest IDs
    // Use current timestamp as part of the ID to ensure uniqueness
    const placeholderId = -1 * Math.floor(Date.now() / 1000);

    // Submit the placeholder staff
    onSelect({
      harvestUserId: placeholderId,
      firstName: placeholderName,
      lastName: "", // Leave last name empty for placeholders
      projectRole: placeholderRole,
      level: placeholderLevel || placeholderRole, // Use role as level if level not provided
      onbordTargetRateDaily: placeholderTargetRate,
      onbordCostRateDaily: placeholderCostRate,
      rateProposedDaily: placeholderTargetRate, // Default proposed rate to target rate
      isPlaceholder: true,
    });

    // Reset form fields
    setPlaceholderName("");
    setPlaceholderRole("");
    setPlaceholderLevel("");
    setPlaceholderTargetRate(0);
    setPlaceholderCostRate(0);

    // Optionally close the form after submission
    setShowPlaceholderForm(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
            Select Team Member
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            aria-label="Close"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Search bar */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <input
              type="text"
              placeholder="Search by name, email, or role..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full p-2 border border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Placeholder Staff Form */}
        {showPlaceholderForm && (
          <div className="flex-1 p-4 bg-purple-50 dark:bg-purple-900/10">
            <form onSubmit={handlePlaceholderSubmit}>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-base font-medium text-gray-800 dark:text-gray-200">
                  Add Placeholder Staff Member
                </h3>
                <button
                  type="button"
                  onClick={() => setShowPlaceholderForm(false)}
                  className="text-sm text-purple-600 dark:text-purple-400 hover:underline flex items-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M10 19l-7-7m0 0l7-7m-7 7h18"
                    />
                  </svg>
                  Back to staff list
                </button>
              </div>
              {placeholderFormError && (
                <div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-sm text-red-600 dark:text-red-400">
                  {placeholderFormError}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={placeholderName}
                    onChange={(e) => setPlaceholderName(e.target.value)}
                    placeholder="e.g., Senior Developer"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-primary focus:border-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Role <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={placeholderRole}
                    onChange={(e) => setPlaceholderRole(e.target.value)}
                    placeholder="e.g., Developer"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-primary focus:border-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Level
                  </label>
                  <input
                    type="text"
                    value={placeholderLevel}
                    onChange={(e) => setPlaceholderLevel(e.target.value)}
                    placeholder="e.g., Senior (optional)"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-primary focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Target Rate (Daily) <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={placeholderTargetRate || ""}
                    onChange={(e) =>
                      setPlaceholderTargetRate(Number(e.target.value))
                    }
                    placeholder="1500"
                    min="0"
                    step="10"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-primary focus:border-primary"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Cost Rate (Daily) <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="number"
                    value={placeholderCostRate || ""}
                    onChange={(e) =>
                      setPlaceholderCostRate(Number(e.target.value))
                    }
                    placeholder="800"
                    min="0"
                    step="10"
                    className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-primary focus:border-primary"
                    required
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowPlaceholderForm(false)}
                  className="px-3 py-2 mr-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-3 py-2 bg-primary text-white rounded hover:bg-primary-dark"
                >
                  Add Placeholder Staff
                </button>
              </div>
            </form>
          </div>
        )}

        {/* User list */}
        <div
          className={`flex-1 overflow-y-auto p-4 ${
            showPlaceholderForm ? "hidden" : "block"
          }`}
        >
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <svg
                className="animate-spin h-8 w-8 text-primary"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : error ? (
            <div className="bg-red-50 dark:bg-red-900/20 rounded-md p-4 text-center">
              <p className="text-red-600 dark:text-red-400">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-2 text-sm text-primary hover:text-primary-dark dark:hover:text-primary-light"
              >
                Retry
              </button>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <p className="mt-2 text-gray-500 dark:text-gray-400">
                {searchTerm
                  ? "No team members match your search"
                  : "No team members available"}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {/* Placeholder Staff Box */}
              <div
                className="border border-dashed border-purple-300 dark:border-purple-700 rounded-md p-3 hover:border-purple-500 dark:hover:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/10 cursor-pointer"
                onClick={() => setShowPlaceholderForm(true)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-purple-800 dark:text-purple-300">
                      Add Placeholder Staff
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Create a staff entry with your own values
                    </p>
                  </div>
                  <svg
                    className="w-6 h-6 text-purple-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                </div>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-1.5">
                    <span className="block text-xs text-purple-500 dark:text-purple-400 mb-0.5">
                      Custom Rates
                    </span>
                    <span className="font-medium text-sm text-purple-700 dark:text-purple-300">
                      Set your own values
                    </span>
                  </div>
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded p-1.5">
                    <span className="block text-xs text-purple-500 dark:text-purple-400 mb-0.5">
                      No Harvest ID
                    </span>
                    <span className="font-medium text-sm text-purple-700 dark:text-purple-300">
                      Unnamed staff
                    </span>
                  </div>
                </div>
              </div>

              {/* Real User Boxes */}
              {filteredUsers.map((user) => {
                const isAlreadyAdded = existingUserIds.includes(user.id);
                const targetRateDaily = convertHourlyToDailyRate(
                  user.default_hourly_rate || 0
                );
                const costRateDaily = convertHourlyToDailyRate(
                  user.cost_rate || 0
                );

                return (
                  <div
                    key={user.id}
                    className={`border rounded-md p-3 ${
                      isAlreadyAdded
                        ? "border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700"
                        : "border-gray-200 hover:border-primary dark:border-gray-700 dark:hover:border-primary-light"
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-3">
                        {/* Avatar */}
                        {user.avatar_url ? (
                          <img
                            className="h-10 w-10 rounded-full flex-shrink-0"
                            src={user.avatar_url}
                            alt={`${user.first_name} ${user.last_name}`}
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-sm font-medium text-gray-600 dark:text-gray-300 flex-shrink-0">
                            {(user.first_name?.[0] || "") +
                              (user.last_name?.[0] || "")}
                          </div>
                        )}
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {user.first_name || "Unknown"}{" "}
                            {user.last_name || ""}
                          </h3>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {user.roles && user.roles.length > 0 ? (
                              user.roles.map((role, idx) => (
                                <span
                                  key={idx}
                                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                >
                                  {role}
                                </span>
                              ))
                            ) : (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                Consultant
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleUserSelect(user)}
                        disabled={isAlreadyAdded}
                        className={`px-3 py-1 rounded text-sm ${
                          isAlreadyAdded
                            ? "bg-gray-300 text-gray-600 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400"
                            : "btn-modern btn-modern--primary"
                        }`}
                      >
                        {isAlreadyAdded ? "Added" : "Add"}
                      </button>
                    </div>
                    <div className="mt-2 grid grid-cols-2 gap-2">
                      <div className="bg-gray-50 dark:bg-gray-700 rounded p-1.5">
                        <span className="block text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                          Target Rate
                        </span>
                        <span className="font-medium text-sm text-gray-800 dark:text-gray-200">
                          {formatCurrency(targetRateDaily)}/day
                        </span>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-700 rounded p-1.5">
                        <span className="block text-xs text-gray-500 dark:text-gray-400 mb-0.5">
                          Cost Rate
                        </span>
                        <span className="font-medium text-sm text-gray-800 dark:text-gray-200">
                          {formatCurrency(costRateDaily)}/day
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
};

export default StaffSelectionModal;
