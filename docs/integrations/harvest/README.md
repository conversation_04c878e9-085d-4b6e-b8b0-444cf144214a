# Harvest Integration

## Overview

The Harvest integration provides access to time tracking, project management, and invoicing data. The integration uses API token authentication and provides both read and write capabilities.

## Key Changes (January 2025)

### Harvest Estimate Handling

As of January 2025, the handling of Harvest estimates has been simplified:

- **Harvest estimates are now read-only** throughout the system
- **Cannot be linked to deals** - only internal estimates can be linked
- **Existing links preserved** - historical Harvest estimate links remain visible but cannot be modified
- **Simplified codebase** - removed complex synchronization logic

### What This Means

1. **For Users**:
   - Harvest estimates remain visible in the Estimates list
   - Can view Harvest estimates with "View in Harvest" button
   - Cannot link new Harvest estimates to deals
   - Must create internal estimates for deal linking

2. **For Developers**:
   - No Harvest estimate sync logic in deal updates
   - Simplified estimate linking APIs
   - Cleaner separation between external and internal data

## Integration Features

### Data Types Accessed

1. **Projects**
   - Project details and budgets
   - Time tracking data
   - Project team members

2. **Invoices**
   - Invoice details and line items
   - Payment status
   - Client information

3. **Estimates** (Read-Only)
   - Estimate details
   - Line items and amounts
   - Client associations

4. **Clients**
   - Client contact information
   - Project associations
   - Invoice history

5. **Time Entries**
   - Daily time tracking
   - Task breakdowns
   - Billable/non-billable hours

### API Endpoints

#### Internal API Routes

```
GET /api/harvest/projects          - List all projects
GET /api/harvest/projects/:id      - Get project details
GET /api/harvest/invoices          - List invoices with filters
GET /api/harvest/estimates         - List estimates (read-only)
GET /api/harvest/estimates/:id     - Get estimate details (read-only)
GET /api/harvest/time-entries      - Get time entries
GET /api/harvest/clients           - List clients
```

#### Harvest API Client

The `HarvestApiClient` class provides methods for interacting with the Harvest API:

```typescript
class HarvestApiClient {
  // Projects
  async getProjects(params?: ProjectParams)
  async getProject(id: number)
  
  // Invoices
  async getInvoices(params?: InvoiceParams)
  async getInvoice(id: number)
  
  // Estimates (Read-Only)
  async getEstimates(params?: EstimateParams)
  async getEstimate(id: number)
  
  // Time Tracking
  async getTimeEntries(params?: TimeEntryParams)
}
```

## Configuration

### Environment Variables

```bash
# Required
HARVEST_ACCESS_TOKEN=your_harvest_token
HARVEST_ACCOUNT_ID=your_account_id

# Optional
HARVEST_API_BASE_URL=https://api.harvestapp.com/v2
HARVEST_RATE_LIMIT=100
```

### Authentication

Harvest uses Personal Access Token authentication:

1. Generate token at: https://id.getharvest.com/developers
2. Add token to environment variables
3. Include Account ID in requests

## Data Synchronization

### Automated Sync

The system automatically syncs:
- Active projects on dashboard load
- Invoice cache every 6 hours
- Time entries for reports

### Manual Sync

Users can trigger manual sync through:
- Refresh buttons in UI
- API endpoints
- Background jobs

## Caching Strategy

### Invoice Cache

- **Purpose**: Performance optimization for spend calculations
- **TTL**: 6 hours
- **Storage**: SQLite table `harvest_invoice_cache`
- **Refresh**: Automatic on expiry or manual trigger

### Project Data

- **Cache Duration**: 1 hour
- **Invalidation**: On project updates
- **Storage**: In-memory cache

## Error Handling

### Rate Limiting

- **Limit**: 100 requests per 15 seconds
- **Strategy**: Exponential backoff
- **Queue**: Request queuing for batch operations

### Common Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| 401 | Invalid token | Check HARVEST_ACCESS_TOKEN |
| 403 | Access denied | Verify account permissions |
| 429 | Rate limited | Wait and retry with backoff |
| 500 | Server error | Retry with exponential backoff |

## Best Practices

### Performance

1. **Use Invoice Cache**: Always use cached invoice totals for calculations
2. **Batch Requests**: Group related API calls
3. **Filter Results**: Use date ranges and filters to reduce payload
4. **Background Jobs**: Process heavy operations asynchronously

### Data Integrity

1. **Read-Only Estimates**: Never attempt to modify Harvest estimates
2. **Field Ownership**: Respect Harvest as source of truth for its data
3. **Sync Order**: Projects → Clients → Invoices → Time Entries
4. **Error Recovery**: Implement retry logic with backoff

## Migration Notes

### From Legacy System

If migrating from a system that allowed Harvest estimate linking:

1. **Existing Links**: Will remain visible but read-only
2. **New Workflow**: Create internal estimates for deal linking
3. **Data Export**: Can export Harvest estimate data for reference
4. **User Training**: Inform users of the new read-only status

### Database Changes

No database schema changes required. The `deal_estimate` table structure remains the same, but business logic enforces:
- `estimate_type` must be 'internal' for new links
- Validation prevents 'harvest' type in new operations

## Troubleshooting

### Common Issues

1. **"Cannot link Harvest estimate"**
   - This is expected behavior
   - Create an internal estimate instead

2. **Missing Harvest estimates**
   - Check API token permissions
   - Verify account ID is correct
   - Ensure estimates exist in Harvest

3. **Slow performance**
   - Check invoice cache status
   - Verify background jobs are running
   - Monitor API rate limit usage

### Debug Mode

Enable debug logging for Harvest integration:

```javascript
// In backend logger configuration
{
  harvest: {
    level: 'debug',
    includeApiCalls: true
  }
}
```

## Related Documentation

- [Harvest API Reference](https://help.getharvest.com/api-v2/)
- [Deal-Estimate API](../../api-reference/deal-estimate-api.md)
- [Data Model](../../technical/data-model/unified-data-model.md)
- [Invoice Cache Feature](../../features/harvest-invoice-cache.md)