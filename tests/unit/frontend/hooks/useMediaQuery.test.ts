import { renderHook, act } from '@testing-library/react';
import { useMediaQuery } from '../../../../src/frontend/hooks/useMediaQuery';

describe('useMediaQuery', () => {
  let mockMatchMedia: jest.Mock;

  beforeEach(() => {
    mockMatchMedia = jest.fn();
    window.matchMedia = mockMatchMedia;
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should return true when media query matches', () => {
    const listeners: ((e: MediaQueryListEvent) => void)[] = [];
    
    mockMatchMedia.mockReturnValue({
      matches: true,
      addEventListener: jest.fn((_, listener) => listeners.push(listener)),
      removeEventListener: jest.fn()
    });

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(true);
    expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 768px)');
  });

  it('should return false when media query does not match', () => {
    mockMatchMedia.mockReturnValue({
      matches: false,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    });

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(false);
  });

  it('should update when media query changes', () => {
    const listeners: ((e: MediaQueryListEvent) => void)[] = [];
    
    const mediaQueryList = {
      matches: false,
      addEventListener: jest.fn((_, listener) => listeners.push(listener)),
      removeEventListener: jest.fn()
    };

    mockMatchMedia.mockReturnValue(mediaQueryList);

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(false);

    // Simulate media query change
    act(() => {
      mediaQueryList.matches = true;
      listeners.forEach(listener => 
        listener({ matches: true } as MediaQueryListEvent)
      );
    });

    expect(result.current).toBe(true);
  });

  it('should clean up event listeners on unmount', () => {
    const removeEventListener = jest.fn();
    
    mockMatchMedia.mockReturnValue({
      matches: true,
      addEventListener: jest.fn(),
      removeEventListener
    });

    const { unmount } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    unmount();

    expect(removeEventListener).toHaveBeenCalled();
  });

  it('should handle multiple media queries', () => {
    mockMatchMedia.mockImplementation((query) => ({
      matches: query === '(min-width: 768px)',
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }));

    const { result: result1 } = renderHook(() => useMediaQuery('(min-width: 768px)'));
    const { result: result2 } = renderHook(() => useMediaQuery('(min-width: 1024px)'));

    expect(result1.current).toBe(true);
    expect(result2.current).toBe(false);
  });

  it('should work with common breakpoint queries', () => {
    const breakpoints = {
      mobile: '(max-width: 767px)',
      tablet: '(min-width: 768px) and (max-width: 1023px)',
      desktop: '(min-width: 1024px)'
    };

    mockMatchMedia.mockImplementation((query) => ({
      matches: query === breakpoints.tablet,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }));

    const { result: mobileResult } = renderHook(() => useMediaQuery(breakpoints.mobile));
    const { result: tabletResult } = renderHook(() => useMediaQuery(breakpoints.tablet));
    const { result: desktopResult } = renderHook(() => useMediaQuery(breakpoints.desktop));

    expect(mobileResult.current).toBe(false);
    expect(tabletResult.current).toBe(true);
    expect(desktopResult.current).toBe(false);
  });

  it('should handle server-side rendering', () => {
    const originalMatchMedia = window.matchMedia;
    // @ts-ignore
    delete window.matchMedia;

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    expect(result.current).toBe(false);

    window.matchMedia = originalMatchMedia;
  });

  it('should debounce rapid changes', () => {
    jest.useFakeTimers();
    const listeners: ((e: MediaQueryListEvent) => void)[] = [];
    
    const mediaQueryList = {
      matches: false,
      addEventListener: jest.fn((_, listener) => listeners.push(listener)),
      removeEventListener: jest.fn()
    };

    mockMatchMedia.mockReturnValue(mediaQueryList);

    const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

    // Simulate rapid changes
    act(() => {
      for (let i = 0; i < 10; i++) {
        mediaQueryList.matches = i % 2 === 0;
        listeners.forEach(listener => 
          listener({ matches: mediaQueryList.matches } as MediaQueryListEvent)
        );
      }
      jest.runAllTimers();
    });

    expect(result.current).toBe(false);
    jest.useRealTimers();
  });
});