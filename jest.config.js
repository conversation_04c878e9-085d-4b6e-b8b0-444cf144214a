/** @type {import('jest').Config} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  roots: ['<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/?(*.)+(spec|test).+(ts|tsx|js)'
  ],
  // Exclude Playwright test files
  testPathIgnorePatterns: [
    '/node_modules/',
    '/tests/backups/playwright/',
    '/tests/backups/e2e/',
    '/tests/e2e/'
  ],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup/setup.unit.ts'],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/types/**/*.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  // Add transformIgnorePatterns to handle ESM in node_modules
  transformIgnorePatterns: [
    '/node_modules/(?!(openid-client|jose|xero-node|oidc-token-hash)/)'
  ],
  // Set a higher default timeout for all tests to prevent timeout failures
  testTimeout: 30000
};
