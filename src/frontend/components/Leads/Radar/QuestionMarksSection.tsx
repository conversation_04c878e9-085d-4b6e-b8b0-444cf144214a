/**
 * Question Marks Section Component
 * 
 * A section for companies that need investigation with action tracking
 */

import React, { useState, useCallback, useEffect } from 'react';
import { useDrop } from 'react-dnd';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { PlusIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';
import QuestionMarkCard from './QuestionMarkCard';
import CreateActionModal from './CreateActionModal';
import ActionDetailsModal from './ActionDetailsModal';
import { getRadarActions, createRadarAction, updateRadarAction } from '../../../api/radar-actions';
import { updateRadarCompany } from '../../../api/leads';
import type { Company } from '../../../../types/company-types';
import type { RadarAction, CreateRadarAction } from '../../../../types/radar-action-types';

interface QuestionMarksSectionProps {
  className?: string;
}

const QuestionMarksSection: React.FC<QuestionMarksSectionProps> = ({ className = '' }) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [selectedAction, setSelectedAction] = useState<RadarAction | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  
  const queryClient = useQueryClient();

  // Fetch all pending/in-progress actions
  const { data: actions = [], isLoading } = useQuery(
    ['radar-actions', 'question-marks'],
    () => getRadarActions({ includeCompleted: false })
  );

  // Get unique companies from actions
  const companiesWithActions = React.useMemo(() => {
    const companyMap = new Map<string, Company & { actions: RadarAction[] }>();
    
    actions.forEach((action) => {
      if (action.company) {
        const existing = companyMap.get(action.companyId);
        if (existing) {
          existing.actions.push(action);
        } else {
          companyMap.set(action.companyId, {
            ...action.company,
            id: action.companyId,
            actions: [action]
          } as Company & { actions: RadarAction[] });
        }
      }
    });
    
    return Array.from(companyMap.values());
  }, [actions]);

  // Mutation for creating actions when company is dropped
  const createActionMutation = useMutation(
    (data: Omit<CreateRadarAction, 'createdBy'>) => createRadarAction(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['radar-actions']);
        setShowCreateModal(false);
        setSelectedCompany(null);
      }
    }
  );

  // Drop handling for companies
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'company',
    drop: (item: { company: Company }) => {
      // Show create action modal for the dropped company
      setSelectedCompany(item.company);
      setShowCreateModal(true);
    },
    canDrop: (item: { company: Company }) => {
      // Don't allow dropping if company already has actions
      return !companiesWithActions.some(c => c.id === item.company.id);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop()
    })
  }), [companiesWithActions]);

  const handleCreateAction = useCallback((actionData: Omit<CreateRadarAction, 'createdBy' | 'companyId'>) => {
    if (!selectedCompany) return;
    
    createActionMutation.mutate({
      ...actionData,
      companyId: selectedCompany.id
    });
  }, [selectedCompany, createActionMutation]);

  const handleActionClick = useCallback((action: RadarAction) => {
    setSelectedAction(action);
    setShowDetailsModal(true);
  }, []);

  const handleAddActionToCompany = useCallback((company: Company) => {
    setSelectedCompany(company);
    setShowCreateModal(true);
  }, []);

  const getDropZoneClasses = () => {
    let classes = 'question-marks-section';
    if (canDrop) classes += ' can-drop';
    if (isOver) classes += ' is-over';
    return classes;
  };

  return (
    <div className={`${className} ${getDropZoneClasses()}`} ref={drop}>
      {/* Header */}
      <div className="question-marks-header">
        <div>
          <h2 className="question-marks-title">
            <QuestionMarkCircleIcon className="w-6 h-6" />
            Question Marks
          </h2>
          <p className="question-marks-subtitle">We don't know and we should find out</p>
        </div>
        
        <div className="question-marks-stats">
          <span className="stat-badge">
            {companiesWithActions.length} {companiesWithActions.length === 1 ? 'company' : 'companies'}
          </span>
          <span className="stat-badge">
            {actions.length} active {actions.length === 1 ? 'action' : 'actions'}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="question-marks-content">
        {isLoading ? (
          <div className="question-marks-loading">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500 border-t-transparent" />
            <p className="text-gray-500 dark:text-gray-400 mt-2">Loading actions...</p>
          </div>
        ) : companiesWithActions.length === 0 ? (
          <div className="question-marks-empty">
            <QuestionMarkCircleIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mb-2" />
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              {canDrop ? 'Drop a company here to create an action' : 'No companies need investigation'}
            </p>
          </div>
        ) : (
          <div className="question-marks-grid">
            {companiesWithActions.map((company) => (
              <QuestionMarkCard
                key={company.id}
                company={company}
                actions={company.actions}
                onActionClick={handleActionClick}
                onAddAction={() => handleAddActionToCompany(company)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      {showCreateModal && selectedCompany && (
        <CreateActionModal
          company={selectedCompany}
          onClose={() => {
            setShowCreateModal(false);
            setSelectedCompany(null);
          }}
          onCreate={handleCreateAction}
        />
      )}

      {showDetailsModal && selectedAction && (
        <ActionDetailsModal
          action={selectedAction}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedAction(null);
          }}
          onUpdate={() => {
            queryClient.invalidateQueries(['radar-actions']);
          }}
        />
      )}
    </div>
  );
};

export default QuestionMarksSection;