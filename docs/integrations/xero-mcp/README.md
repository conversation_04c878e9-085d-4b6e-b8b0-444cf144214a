# Xero MCP Integration

This directory contains documentation for the Xero Model Context Protocol (MCP) integration, which enables chat-based interaction with Xero accounting data.

## Documentation Files

- [`xero-mcp-quick-start.md`](./xero-mcp-quick-start.md) - Quick setup guide
- [`xero-mcp-integration-plan.md`](./xero-mcp-integration-plan.md) - Integration planning and architecture
- [`xero-mcp-implementation-guide.md`](./xero-mcp-implementation-guide.md) - Complete implementation guide
- [`xero-mcp-chat-implementation.md`](./xero-mcp-chat-implementation.md) - Chat interface implementation
- [`xero-mcp-demo.md`](./xero-mcp-demo.md) - Demo and usage examples

## Overview

The Xero MCP integration allows users to:
- Query Xero accounting data using natural language
- Access financial reports through chat interface
- Perform real-time account lookups
- Get insights from financial data

For implementation details, start with the [Quick Start Guide](./xero-mcp-quick-start.md).