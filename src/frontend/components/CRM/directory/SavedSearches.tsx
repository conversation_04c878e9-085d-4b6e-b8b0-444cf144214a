import React, { useState } from 'react'; import { StarIcon, ClockIcon, CurrencyDollarIcon, LinkIcon, UserIcon, PlusIcon, TrashIcon, PencilIcon } from '@heroicons/react/24/outline'; import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'; import type { SavedSearch } from './types'; interface SavedSearchesProps { savedSearches: SavedSearch[]; onSelectSearch: (search: SavedSearch) => void; onSaveCurrentSearch: (name: string) => void; onDeleteSearch: (id: string) => void; onUpdateSearch: (id: string, updates: Partial<SavedSearch>) => void; currentSearchParams: { query?: string; filters: any; }; } const DEFAULT_SEARCHES: Omit<SavedSearch, 'id'>[] = [ { name: 'Recent Activity', icon: 'clock', filters: { quickFilter: 'recently-updated' }, isDefault: true, isPinned: false }, { name: 'High Value Deals', icon: 'currency', filters: { entityType: 'deals', quickFilter: 'high-value' }, isDefault: true, isPinned: false }, { name: 'Setup Needed', icon: 'link', filters: { quickFilter: 'unlinked' }, isDefault: true, isPinned: false }, { name: 'My Items', icon: 'user', filters: { quickFilter: 'my-items' }, isDefault: true, isPinned: false } ]; const ICON_MAP = { clock: ClockIcon, currency: CurrencyDollarIcon, link: LinkIcon, user: UserIcon, star: StarIcon }; export const SavedSearches: React.FC<SavedSearchesProps> = ({ savedSearches, onSelectSearch, onSaveCurrentSearch, onDeleteSearch, onUpdateSearch, currentSearchParams }) => { const [showSaveDialog, setShowSaveDialog] = useState(false); const [searchName, setSearchName] = useState(''); const [editingId, setEditingId] = useState<string | null>(null); const [editingName, setEditingName] = useState(''); const handleSave = () => { if (searchName.trim()) { onSaveCurrentSearch(searchName.trim()); setSearchName(''); setShowSaveDialog(false); } }; const handleEdit = (search: SavedSearch) => { setEditingId(search.id); setEditingName(search.name); }; const handleEditSave = (id: string) => { if (editingName.trim()) { onUpdateSearch(id, { name: editingName.trim() }); setEditingId(null); setEditingName(''); } }; const handleTogglePin = (search: SavedSearch) => { onUpdateSearch(search.id, { isPinned: !search.isPinned }); }; // Combine default searches with user searches const allSearches = [ ...DEFAULT_SEARCHES.map((s, i) => ({ ...s, id: `default-${i}` })), ...savedSearches ]; // Sort: pinned first, then by name const sortedSearches = allSearches.sort((a, b) => { if (a.isPinned && !b.isPinned) return -1; if (!a.isPinned && b.isPinned) return 1; return a.name.localeCompare(b.name); }); return ( <div className="saved-searches"> <div className="mb-3"> <button onClick={() => setShowSaveDialog(true)} className="w-full px-2 py-1.5 text-xs bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors flex items-center justify-center gap-1" > <PlusIcon className="w-3 h-3" /> Save Current </button> {showSaveDialog && ( <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-900 rounded-md"> <input type="text" value={searchName} onChange={(e) => setSearchName(e.target.value)} onKeyDown={(e) => e.key === 'Enter' && handleSave()} placeholder="Name..." className="w-full px-2 py-1 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-purple-500" autoFocus /> <div className="flex gap-1 mt-1"> <button onClick={handleSave} className="flex-1 px-2 py-0.5 text-xs bg-purple-600 text-white rounded hover:bg-purple-700" > Save </button> <button onClick={() => { setShowSaveDialog(false); setSearchName(''); }} className="flex-1 px-2 py-0.5 text-xs bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-400 dark:hover:bg-gray-600" > Cancel </button> </div> </div> )} </div> <div className="space-y-0.5"> {sortedSearches.map((search) => { const IconComponent = ICON_MAP[search.icon as keyof typeof ICON_MAP] || StarIcon; const isEditing = editingId === search.id; return ( <div key={search.id} className="group flex items-center gap-1.5 px-2 py-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors" > <IconComponent className="w-3.5 h-3.5 text-gray-500 dark:text-gray-400 flex-shrink-0" /> {isEditing ? ( <input type="text" value={editingName} onChange={(e) => setEditingName(e.target.value)} onKeyDown={(e) => { if (e.key === 'Enter') handleEditSave(search.id); if (e.key === 'Escape') { setEditingId(null); setEditingName(''); } }} onBlur={() => handleEditSave(search.id)} className="flex-1 px-1 py-0.5 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-purple-500" autoFocus /> ) : ( <span onClick={() => onSelectSearch(search)} className="flex-1 text-xs text-gray-700 dark:text-gray-300 truncate" title={search.name} > {search.name} </span> )} {!search.isDefault && ( <div className="flex items-center gap-0.5 opacity-0 group-hover:opacity-100 transition-opacity"> {search.isPinned && ( <StarSolidIcon className="w-2.5 h-2.5 text-yellow-500" /> )} <button onClick={(e) => { e.stopPropagation(); if (!search.isDefault) { onDeleteSearch(search.id); } }} className="p-0.5 hover:bg-gray-200 dark:hover:bg-gray-600 rounded" title="Delete" > <TrashIcon className="w-2.5 h-2.5 text-gray-400" /> </button> </div> )} </div> ); })} </div> </div> ); }; export default SavedSearches;