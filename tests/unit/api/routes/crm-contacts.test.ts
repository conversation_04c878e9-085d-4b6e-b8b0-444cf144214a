import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/repositories/contact-repository');
jest.mock('../../../../src/api/repositories/contact-company-repository');
jest.mock('../../../../src/api/services/activity-service');
jest.mock('../../../../src/utils/backend-logger');

describe('CRM Contacts API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      user: { id: 'user-123', email: '<EMAIL>' },
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/crm/contacts', () => {
    it('should fetch all contacts with optional filters', async () => {
      mockRequest.query = {
        search: 'john',
        company_id: '1',
        limit: '25',
        offset: '0',
      };
      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      const mockContacts = [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          position: 'CEO',
          hubspot_id: 'hs-contact-123',
          created_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 2,
          first_name: 'John',
          last_name: 'Smith',
          email: '<EMAIL>',
          phone: '+0987654321',
          position: 'CTO',
          hubspot_id: 'hs-contact-456',
          created_at: '2024-01-02T00:00:00Z',
        },
      ];
      ContactRepository.prototype.getAllContacts = jest.fn().mockResolvedValue({
        contacts: mockContacts,
        total: 2,
      });

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRepository.prototype.getAllContacts).toHaveBeenCalledWith({
        search: 'john',
        company_id: 1,
        limit: 25,
        offset: 0,
      });
      expect(mockResponse.json).toHaveBeenCalledWith({
        contacts: mockContacts,
        total: 2,
      });
    });
  });

  describe('GET /api/crm/contacts/:id', () => {
    it('should fetch a contact by ID with companies', async () => {
      mockRequest.params = { id: '1' };
      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      const ContactCompanyRepository = (await import('../../../../src/api/repositories/contact-company-repository')).default;
      
      const mockContact = {
        id: 1,
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        position: 'CEO',
        hubspot_id: 'hs-contact-123',
      };
      const mockCompanies = [
        {
          company_id: 1,
          company_name: 'Acme Corp',
          role: 'CEO',
          is_primary: true,
        },
        {
          company_id: 2,
          company_name: 'Beta Inc',
          role: 'Advisor',
          is_primary: false,
        },
      ];

      ContactRepository.prototype.getContactById = jest.fn().mockResolvedValue(mockContact);
      ContactCompanyRepository.prototype.getContactCompanies = jest.fn().mockResolvedValue(mockCompanies);

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRepository.prototype.getContactById).toHaveBeenCalledWith(1);
      expect(ContactCompanyRepository.prototype.getContactCompanies).toHaveBeenCalledWith(1);
      expect(mockResponse.json).toHaveBeenCalledWith({
        ...mockContact,
        companies: mockCompanies,
      });
    });

    it('should handle not found errors', async () => {
      mockRequest.params = { id: '999' };
      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      ContactRepository.prototype.getContactById = jest.fn().mockResolvedValue(null);

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Contact not found' });
    });
  });

  describe('POST /api/crm/contacts', () => {
    it('should create a new contact', async () => {
      mockRequest.body = {
        first_name: 'Jane',
        last_name: 'Johnson',
        email: '<EMAIL>',
        phone: '+1122334455',
        position: 'CFO',
        linkedin: 'https://linkedin.com/in/janejohnson',
        notes: 'Key decision maker',
      };

      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockCreatedContact = { id: 3, ...mockRequest.body };
      ContactRepository.prototype.createContact = jest.fn().mockResolvedValue(mockCreatedContact);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRepository.prototype.createContact).toHaveBeenCalledWith(mockRequest.body);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'contact_created',
        target_type: 'contact',
        target_id: '3',
        metadata: {
          contact_name: 'Jane Johnson',
          email: '<EMAIL>',
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockCreatedContact);
    });

    it('should handle validation errors', async () => {
      mockRequest.body = {
        first_name: 'Jane',
        // Missing required fields: last_name, email
      };

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing required fields: last_name, email',
      });
    });
  });

  describe('PUT /api/crm/contacts/:id', () => {
    it('should update a contact', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        position: 'President',
        phone: '+9876543210',
        linkedin: 'https://linkedin.com/in/johndoe',
      };

      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockUpdatedContact = { id: 1, ...mockRequest.body };
      ContactRepository.prototype.updateContact = jest.fn().mockResolvedValue(mockUpdatedContact);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.put
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRepository.prototype.updateContact).toHaveBeenCalledWith(1, mockRequest.body);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'contact_updated',
        target_type: 'contact',
        target_id: '1',
        metadata: {
          changes: ['position', 'phone', 'linkedin'],
        },
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUpdatedContact);
    });
  });

  describe('DELETE /api/crm/contacts/:id', () => {
    it('should delete a contact', async () => {
      mockRequest.params = { id: '1' };

      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      ContactRepository.prototype.deleteContact = jest.fn().mockResolvedValue(true);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRepository.prototype.deleteContact).toHaveBeenCalledWith(1);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'contact_deleted',
        target_type: 'contact',
        target_id: '1',
        metadata: {},
      });
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
    });
  });

  describe('POST /api/crm/contacts/:id/companies', () => {
    it('should add a contact to a company', async () => {
      mockRequest.params = { id: '1' };
      mockRequest.body = {
        company_id: 5,
        role: 'Board Member',
        is_primary: false,
      };

      const ContactCompanyRepository = (await import('../../../../src/api/repositories/contact-company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      const mockRelationship = {
        contact_id: 1,
        company_id: 5,
        role: 'Board Member',
        is_primary: false,
      };
      ContactCompanyRepository.prototype.addContactToCompany = jest.fn().mockResolvedValue(mockRelationship);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/companies' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactCompanyRepository.prototype.addContactToCompany).toHaveBeenCalledWith({
        contact_id: 1,
        company_id: 5,
        role: 'Board Member',
        is_primary: false,
      });
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'contact_linked_to_company',
        target_type: 'contact',
        target_id: '1',
        metadata: {
          company_id: 5,
          role: 'Board Member',
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockRelationship);
    });
  });

  describe('DELETE /api/crm/contacts/:id/companies/:companyId', () => {
    it('should remove a contact from a company', async () => {
      mockRequest.params = { id: '1', companyId: '5' };

      const ContactCompanyRepository = (await import('../../../../src/api/repositories/contact-company-repository')).default;
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;

      ContactCompanyRepository.prototype.removeContactFromCompany = jest.fn().mockResolvedValue(true);
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue({});

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/companies/:companyId' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactCompanyRepository.prototype.removeContactFromCompany).toHaveBeenCalledWith(1, 5);
      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'contact_unlinked_from_company',
        target_type: 'contact',
        target_id: '1',
        metadata: {
          company_id: 5,
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
    });
  });

  describe('GET /api/crm/contacts/:id/deals', () => {
    it('should fetch deals associated with a contact', async () => {
      mockRequest.params = { id: '1' };
      const ContactRepository = (await import('../../../../src/api/repositories/contact-repository')).default;
      
      const mockDeals = [
        {
          id: 1,
          name: 'Enterprise Deal',
          amount: 100000,
          stage: 'proposal',
          role: 'Decision Maker',
        },
        {
          id: 2,
          name: 'Small Project',
          amount: 25000,
          stage: 'qualified',
          role: 'Technical Advisor',
        },
      ];
      ContactRepository.prototype.getContactDeals = jest.fn().mockResolvedValue(mockDeals);

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/deals'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ContactRepository.prototype.getContactDeals).toHaveBeenCalledWith(1);
      expect(mockResponse.json).toHaveBeenCalledWith(mockDeals);
    });
  });

  describe('GET /api/crm/contacts/:id/activities', () => {
    it('should fetch activities for a contact', async () => {
      mockRequest.params = { id: '1' };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      
      const mockActivities = [
        {
          id: 1,
          action: 'contact_created',
          created_at: '2024-01-01T00:00:00Z',
          metadata: {},
        },
        {
          id: 2,
          action: 'contact_updated',
          created_at: '2024-01-02T00:00:00Z',
          metadata: { changes: ['position'] },
        },
      ];
      ActivityService.prototype.getTargetTimeline = jest.fn().mockResolvedValue(mockActivities);

      const contactsRoutes = await import('../../../../src/api/routes/crm/contacts');
      const handler = contactsRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id/activities'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getTargetTimeline).toHaveBeenCalledWith('contact', '1');
      expect(mockResponse.json).toHaveBeenCalledWith(mockActivities);
    });
  });
});