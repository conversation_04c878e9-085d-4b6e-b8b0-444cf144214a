import React, { useState } from "react";
import {
  AllocationWithTotals,
  ProjectTotals,
} from "../../hooks/useEstimateStaffManagement"; // Adjust path as needed
import { formatCurrency } from "./utils";

interface BudgetSummaryProps {
  allocationsWithTotals: AllocationWithTotals[];
  projectTotals: ProjectTotals;
  onDiscountTypeChange?: (type: "percentage" | "amount" | "none") => void;
  onDiscountValueChange?: (value: number) => void;
}

const BudgetSummary: React.FC<BudgetSummaryProps> = ({
  allocationsWithTotals,
  projectTotals,
  onDiscountTypeChange,
  onDiscountValueChange,
}) => {
  const [showMargin, setShowMargin] = useState(false); // Internal state for GM toggle - default to collapsed

  // Calculate final totals for display with validation to prevent NaN
  const totalRevenue = isNaN(projectTotals.totalRevenue)
    ? 0
    : projectTotals.totalRevenue;

  // Ensure discountedRevenue is valid
  const discountedRevenue = isNaN(projectTotals.discountedRevenue)
    ? totalRevenue
    : projectTotals.discountedRevenue;

  // Calculate GST and grand total
  const gstAmount = discountedRevenue * 0.1;
  const grandTotal = discountedRevenue + gstAmount;

  // Use totalCost from props directly with validation
  const totalCost = isNaN(projectTotals.totalCost)
    ? 0
    : projectTotals.totalCost;

  // Log values for debugging
  console.log("BudgetSummary - Totals:", {
    totalRevenue,
    discountedRevenue,
    gstAmount,
    grandTotal,
    totalCost,
    projectTotals,
  });

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mt-4">
      <div className="flex justify-between items-center mb-3">
        <h4 className="font-medium text-gray-800 dark:text-gray-200">
          Budget Summary
        </h4>
        <div className="flex items-center gap-2">
          {!showMargin && (
            <span className="text-xs text-gray-500 dark:text-gray-400 italic">
              Click GM to show gross margin
            </span>
          )}
          <button
            onClick={() => setShowMargin(!showMargin)}
            className={`px-3 py-1 text-xs font-medium rounded flex items-center transition-colors ${
              showMargin
                ? "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300"
                : "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
          >
            <span className="mr-1">GM</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={showMargin ? "M15 12H9" : "M12 6v6m0 0v6m0-6h6m-6 0H6"}
              />
            </svg>
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full table-fixed border-separate border-spacing-0">
          <colgroup>
            <col className="w-[15%]" /> {/* Staff name */}
            <col className="w-[13%]" /> {/* Role */}
            <col className="w-[6%]" /> {/* Days */}
            <col className="w-[8%]" /> {/* Daily Cost */}
            <col className="w-[12%]" /> {/* Total Cost */}
            <col className="w-[8%]" /> {/* Daily Revenue */}
            <col className="w-[16%]" /> {/* Total Revenue */}
            {showMargin && (
              <>
                <col className="w-[12%]" /> {/* Margin $ */}
                <col className="w-[8%]" /> {/* Margin % */}
              </>
            )}
          </colgroup>

          <thead>
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Team Member
              </th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Role
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Days
              </th>
              <th
                colSpan={2}
                className="px-4 py-1 text-center text-xs font-medium bg-red-50 dark:bg-red-900/10 text-red-700 dark:text-red-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700"
              >
                Cost
              </th>
              <th
                colSpan={2}
                className="px-4 py-1 text-center text-xs font-medium bg-green-50 dark:bg-green-900/10 text-green-700 dark:text-green-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700"
              >
                Revenue (Fees)
              </th>
              {showMargin && (
                <th
                  colSpan={2}
                  className="px-4 py-1 text-center text-xs font-medium bg-purple-50 dark:bg-purple-900/10 text-purple-700 dark:text-purple-300 uppercase tracking-wider border-b border-gray-200 dark:border-gray-700"
                >
                  GM
                </th>
              )}
            </tr>
            <tr>
              <th className="border-b-2 border-gray-200 dark:border-gray-700"></th>
              <th className="border-b-2 border-gray-200 dark:border-gray-700"></th>
              <th className="border-b-2 border-gray-200 dark:border-gray-700"></th>
              <th className="px-4 py-2 text-center text-xs font-medium bg-red-50 dark:bg-red-900/10 text-red-600 dark:text-red-300 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Daily
              </th>
              <th className="px-4 py-2 text-right text-xs font-medium bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Total
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium bg-green-50 dark:bg-green-900/10 text-green-600 dark:text-green-300 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Daily
              </th>
              <th className="px-4 py-2 text-right text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                Total
              </th>
              {showMargin && (
                <>
                  <th className="px-4 py-2 text-right text-xs font-medium bg-purple-50 dark:bg-purple-900/10 text-purple-600 dark:text-purple-300 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                    $
                  </th>
                  <th className="px-4 py-2 text-right text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 uppercase tracking-wider border-b-2 border-gray-200 dark:border-gray-700">
                    %
                  </th>
                </>
              )}
            </tr>
          </thead>

          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {allocationsWithTotals.map((staff, index) => {
              const staffMargin = staff.totalFees - staff.totalCost;
              const staffMarginPercent =
                staff.totalFees > 0 ? (staffMargin / staff.totalFees) * 100 : 0;

              return (
                <tr
                  key={index}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700/50 border-b border-gray-200 dark:border-gray-700"
                >
                  <td className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 font-medium">
                    {staff.firstName || ""} {staff.lastName || ""}
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400">
                    {staff.projectRole || "Staff"}
                  </td>
                  <td className="px-4 py-2 text-center text-sm text-gray-600 dark:text-gray-400">
                    {staff.totalDays.toFixed(1)}
                  </td>
                  <td className="px-4 py-2 text-center text-sm text-red-600 dark:text-red-400 bg-red-50/50 dark:bg-red-900/5">
                    {formatCurrency(staff.onbordCostRateDaily)}
                  </td>
                  <td className="px-4 py-2 text-right text-sm text-red-700 dark:text-red-400 bg-red-50/80 dark:bg-red-900/10">
                    {formatCurrency(staff.totalCost)}
                  </td>
                  <td className="px-4 py-2 text-center text-sm text-green-600 dark:text-green-400 bg-green-50/50 dark:bg-green-900/5">
                    {formatCurrency(staff.rateProposedDaily)}
                  </td>
                  <td className="px-4 py-2 text-right text-sm text-green-700 dark:text-green-400 bg-green-50/80 dark:bg-green-900/10">
                    {formatCurrency(staff.totalFees)}
                  </td>
                  {showMargin && (
                    <>
                      <td className="px-4 py-2 text-right text-sm bg-purple-50/50 dark:bg-purple-900/5">
                        <span className="text-purple-700 dark:text-purple-400">
                          {formatCurrency(staffMargin)}
                        </span>
                      </td>
                      <td className="px-4 py-2 text-right text-sm bg-purple-50/80 dark:bg-purple-900/10">
                        <span className="text-purple-700 dark:text-purple-400">
                          {staffMarginPercent.toFixed(1)}%
                        </span>
                      </td>
                    </>
                  )}
                </tr>
              );
            })}
          </tbody>

          <tfoot>
            {/* Subtotal Row */}
            <tr className="bg-gray-50 dark:bg-gray-700/50 font-medium border-t-2 border-gray-300 dark:border-gray-600">
              <td
                colSpan={2}
                className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 text-right font-semibold"
              >
                Subtotals
              </td>
              <td className="px-4 py-2 text-center text-sm text-gray-700 dark:text-gray-300 font-semibold">
                {projectTotals.totalDays.toFixed(1)}
              </td>
              <td className="px-4 py-2 text-center text-sm text-red-600 dark:text-red-400 bg-red-50/50 dark:bg-red-900/5 font-semibold">
                {projectTotals.totalDays > 0
                  ? formatCurrency(totalCost / projectTotals.totalDays)
                  : formatCurrency(0)}
              </td>
              <td className="px-4 py-2 text-right text-sm text-red-700 dark:text-red-400 font-semibold bg-red-100 dark:bg-red-900/20">
                {formatCurrency(totalCost)}
              </td>
              <td className="px-4 py-2 text-center text-sm text-green-600 dark:text-green-400 bg-green-50/50 dark:bg-green-900/5 font-semibold">
                {projectTotals.totalDays > 0
                  ? formatCurrency(totalRevenue / projectTotals.totalDays)
                  : formatCurrency(0)}
              </td>
              <td className="px-4 py-2 text-right text-sm text-green-700 dark:text-green-400 font-semibold bg-green-100 dark:bg-green-900/20">
                {formatCurrency(totalRevenue)}
              </td>
              {showMargin && (
                <>
                  <td className="px-4 py-2 text-right text-sm bg-purple-100 dark:bg-purple-900/20 font-semibold">
                    <span className="text-purple-700 dark:text-purple-400 font-semibold">
                      {formatCurrency(projectTotals.marginAmount)}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-right text-sm bg-purple-100 dark:bg-purple-900/20 font-semibold">
                    <span className="text-purple-700 dark:text-purple-400 font-semibold">
                      {projectTotals.marginPercentage.toFixed(1)}%
                    </span>
                  </td>
                </>
              )}
            </tr>

            {/* Separator */}
            <tr>
              <td
                colSpan={showMargin ? 9 : 7}
                className="py-2 border-b-2 border-gray-300 dark:border-gray-600"
              ></td>
            </tr>

            {/* Discount Row */}
            <tr className="bg-blue-50 dark:bg-blue-900/10">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 text-right"
              >
                <div className="flex items-center justify-end gap-3 w-full">
                  <span className="font-medium text-blue-600 dark:text-blue-400">
                    Discount
                  </span>

                  <div className="flex items-center gap-1">
                    <input
                      type="text"
                      inputMode="decimal"
                      value={
                        projectTotals.discountType === "percentage"
                          ? isNaN(projectTotals.discountValue)
                            ? ""
                            : projectTotals.discountValue
                          : totalRevenue > 0 && projectTotals.discountAmount > 0
                          ? parseFloat(
                              (
                                (projectTotals.discountAmount / totalRevenue) *
                                100
                              ).toFixed(2)
                            )
                          : ""
                      }
                      onChange={(e) => {
                        // Allow empty string
                        if (e.target.value === "") {
                          onDiscountTypeChange?.("percentage");
                          onDiscountValueChange?.(0);
                          return;
                        }

                        // Only allow numbers and decimal points
                        const numericValue = e.target.value.replace(
                          /[^0-9.]/g,
                          ""
                        );

                        // Handle multiple decimal points
                        const decimalCount = (numericValue.match(/\./g) || [])
                          .length;
                        let sanitizedValue = numericValue;
                        if (decimalCount > 1) {
                          const parts = numericValue.split(".");
                          sanitizedValue =
                            parts[0] + "." + parts.slice(1).join("");
                        }

                        // Try to parse as float
                        const value = parseFloat(sanitizedValue);

                        // Update if it's a valid number
                        if (!isNaN(value)) {
                          onDiscountTypeChange?.("percentage");
                          onDiscountValueChange?.(value);
                        }
                      }}
                      className="text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-2 py-1.5 w-16 text-right dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 dark:focus:border-blue-500 appearance-none shadow-sm"
                      placeholder="0"
                    />
                    <span className="text-gray-500 dark:text-gray-400">%</span>
                  </div>

                  <span className="text-blue-500 dark:text-blue-400 font-medium">
                    or
                  </span>

                  <div className="flex items-center gap-1">
                    <span className="text-gray-500 dark:text-gray-400">$</span>
                    <input
                      type="text"
                      inputMode="decimal"
                      value={
                        projectTotals.discountType === "amount"
                          ? isNaN(projectTotals.discountValue)
                            ? ""
                            : projectTotals.discountValue
                          : projectTotals.discountAmount > 0
                          ? parseFloat(projectTotals.discountAmount.toFixed(2))
                          : ""
                      }
                      onChange={(e) => {
                        // Allow empty string
                        if (e.target.value === "") {
                          onDiscountTypeChange?.("amount");
                          onDiscountValueChange?.(0);
                          return;
                        }

                        // Only allow numbers and decimal points
                        const numericValue = e.target.value.replace(
                          /[^0-9.]/g,
                          ""
                        );

                        // Handle multiple decimal points
                        const decimalCount = (numericValue.match(/\./g) || [])
                          .length;
                        let sanitizedValue = numericValue;
                        if (decimalCount > 1) {
                          const parts = numericValue.split(".");
                          sanitizedValue =
                            parts[0] + "." + parts.slice(1).join("");
                        }

                        // Try to parse as float
                        const value = parseFloat(sanitizedValue);

                        // Update if it's a valid number
                        if (!isNaN(value)) {
                          onDiscountTypeChange?.("amount");
                          onDiscountValueChange?.(value);
                        }
                      }}
                      className="text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-2 py-1.5 w-24 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 dark:focus:border-blue-500 appearance-none shadow-sm"
                      placeholder="0"
                    />
                  </div>
                </div>
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-2 text-right text-sm font-medium bg-blue-100 dark:bg-blue-900/20"
              >
                {projectTotals.discountAmount > 0 ? (
                  <span className="text-blue-700 dark:text-blue-400">
                    -{formatCurrency(projectTotals.discountAmount)}
                  </span>
                ) : (
                  <span className="text-gray-500 dark:text-gray-400">
                    -{formatCurrency(0)}
                  </span>
                )}
              </td>
            </tr>

            {/* Total Fees (ex. GST) Row */}
            <tr className="bg-green-50 dark:bg-green-900/20 font-semibold">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-3 text-sm text-green-800 dark:text-green-300 text-right font-semibold"
              >
                Total Fees (ex. GST)
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-3 text-right text-lg text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/30 font-semibold"
              >
                {formatCurrency(discountedRevenue)}
              </td>
            </tr>

            {/* GST Row */}
            <tr className="bg-gray-50/80 dark:bg-gray-700/30">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 text-right"
              >
                GST (10%)
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-2 text-right text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700/50"
              >
                {formatCurrency(gstAmount)}
              </td>
            </tr>

            {/* Total Row */}
            <tr className="bg-gray-100 dark:bg-gray-700 font-bold">
              <td
                colSpan={showMargin ? 6 : 4}
                className="px-4 py-3 text-sm text-gray-800 dark:text-gray-200 text-right font-bold"
              >
                TOTAL (Inc. GST)
              </td>
              <td
                colSpan={showMargin ? 3 : 3}
                className="px-4 py-3 text-right text-lg text-gray-800 dark:text-gray-200 bg-gray-200 dark:bg-gray-600 font-bold"
              >
                {formatCurrency(grandTotal)}
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      <div className="mt-4 text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-2">
        <p>
          All prices are in Australian Dollars (AUD). GST is applied at the
          standard rate of 10%.
        </p>
      </div>
    </div>
  );
};

export default BudgetSummary;
