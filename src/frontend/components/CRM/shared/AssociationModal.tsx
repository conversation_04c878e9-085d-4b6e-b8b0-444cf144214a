import React, { useState } from 'react'; import { XMarkIcon, UserCircleIcon, BuildingOfficeIcon, LinkIcon } from '@heroicons/react/24/outline'; import { Contact, Company, ContactRole } from '../../../types/crm-types'; import { linkContactToCompany, createContactRelationship } from '../../../api/crm'; import { useMutation, useQueryClient } from 'react-query'; export type AssociationType = 'contact-company' | 'contact-contact'; export interface AssociationModalProps { isOpen: boolean; onClose: () => void; sourceEntity: Contact | Company; targetEntity: Contact | Company; associationType: AssociationType; } const relationshipTypes = [ { value: 'colleague', label: 'Colleague', description: 'Works in the same organization' }, { value: 'reports_to', label: 'Reports To', description: 'Hierarchical reporting relationship' }, { value: 'worked_with', label: 'Worked With', description: 'Professional collaboration' }, { value: 'introduced_by', label: 'Introduced By', description: 'Referral or introduction' }, { value: 'knows', label: 'Knows', description: 'General professional acquaintance' }, ]; const contactRoles: { value: ContactRole; label: string; description: string }[] = [ { value: 'decision_maker', label: 'Decision Maker', description: 'Makes final decisions' }, { value: 'influencer', label: 'Influencer', description: 'Influences decision making' }, { value: 'champion', label: 'Champion', description: 'Advocates for our solution' }, { value: 'user', label: 'User', description: 'Uses our products/services' }, { value: 'technical', label: 'Technical Contact', description: 'Technical point of contact' }, { value: 'executive', label: 'Executive', description: 'Executive level contact' }, { value: 'other', label: 'Other', description: 'Other role' }, ]; const AssociationModal: React.FC<AssociationModalProps> = ({ isOpen, onClose, sourceEntity, targetEntity, associationType, }) => { const queryClient = useQueryClient(); const [role, setRole] = useState<ContactRole>('user'); const [relationshipType, setRelationshipType] = useState('colleague'); const [strength, setStrength] = useState(3); const [context, setContext] = useState(''); const [isPrimary, setIsPrimary] = useState(false); // Contact-Company association const linkContactCompanyMutation = useMutation( async () => { const contact = sourceEntity as Contact; const company = targetEntity as Company; console.log('AssociationModal: Calling linkContactToCompany', { contactId: contact.id, companyId: company.id, role, isPrimary }); await linkContactToCompany(contact.id, company.id, role, isPrimary); }, { onSuccess: () => { console.log('AssociationModal: Contact-Company link successful'); queryClient.invalidateQueries(['contacts']); queryClient.invalidateQueries(['companies']); onClose(); }, onError: (error) => { console.error('AssociationModal: Contact-Company link failed', error); }, } ); // Contact-Contact relationship const createRelationshipMutation = useMutation( async () => { const sourceContact = sourceEntity as Contact; const targetContact = targetEntity as Contact; console.log('AssociationModal: Calling createContactRelationship', { sourceContactId: sourceContact.id, targetContactId: targetContact.id, relationshipType, strength, context: context || undefined, }); await createContactRelationship({ sourceContactId: sourceContact.id, targetContactId: targetContact.id, relationshipType, strength, context: context || undefined, }); }, { onSuccess: () => { console.log('AssociationModal: Contact-Contact relationship successful'); queryClient.invalidateQueries(['contacts']); onClose(); }, onError: (error) => { console.error('AssociationModal: Contact-Contact relationship failed', error); }, } ); const handleSubmit = (e: React.FormEvent) => { e.preventDefault(); console.log('AssociationModal: handleSubmit called', { associationType, sourceEntity: sourceEntity?.id, targetEntity: targetEntity?.id, role, relationshipType, isPrimary }); if (associationType === 'contact-company') { console.log('AssociationModal: Starting contact-company link mutation'); linkContactCompanyMutation.mutate(); } else { console.log('AssociationModal: Starting contact-contact relationship mutation'); createRelationshipMutation.mutate(); } }; const getEntityName = (entity: Contact | Company) => { if ('firstName' in entity) { return `${entity.firstName} ${entity.lastName}`; } return entity.name; }; const getEntityIcon = (entity: Contact | Company) => { if ('firstName' in entity) { return <UserCircleIcon className="h-6 w-6" />; } return <BuildingOfficeIcon className="h-6 w-6" />; }; const isLoading = linkContactCompanyMutation.isLoading || createRelationshipMutation.isLoading; if (!isOpen) return null; return ( <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"> <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden"> {/* Header */} <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between"> <div className="flex items-center gap-2"> <LinkIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" /> <h2 className="text-lg font-semibold text-gray-900 dark:text-white"> Create Association </h2> </div> <button onClick={onClose} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors" > <XMarkIcon className="h-6 w-6" /> </button> </div> {/* Content */} <form onSubmit={handleSubmit} className="p-6 space-y-4"> {/* Entities being linked */} <div className="btn-modern--secondary"dark:bg-gray-700/50 rounded-lg p-4"> <div className="flex items-center justify-between"> <div className="flex items-center gap-3"> <div className="text-blue-600 dark:text-blue-400"> {getEntityIcon(sourceEntity)} </div> <div> <p className="font-medium text-gray-900 dark:text-white"> {getEntityName(sourceEntity)} </p> <p className="text-sm text-gray-500 dark:text-gray-400"> {'firstName' in sourceEntity ? 'Contact' : 'Company'} </p> </div> </div> <div className="flex items-center gap-2"> <div className="h-px bg-gray-300 dark:bg-gray-600 w-8"></div> <LinkIcon className="h-4 w-4 text-gray-400" /> <div className="h-px bg-gray-300 dark:bg-gray-600 w-8"></div> </div> <div className="flex items-center gap-3"> <div className="text-green-600 dark:text-green-400"> {getEntityIcon(targetEntity)} </div> <div> <p className="font-medium text-gray-900 dark:text-white"> {getEntityName(targetEntity)} </p> <p className="text-sm text-gray-500 dark:text-gray-400"> {'firstName' in targetEntity ? 'Contact' : 'Company'} </p> </div> </div> </div> </div> {/* Contact-Company specific fields */} {associationType === 'contact-company' && ( <> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"> Role at Company </label> <div className="grid grid-cols-2 gap-2"> {contactRoles.map((roleOption) => ( <button key={roleOption.value} type="button" onClick={() => setRole(roleOption.value)} className={` px-4 py-3 rounded-lg border text-left transition-all ${role === roleOption.value ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700' } `} > <div className="font-medium text-sm">{roleOption.label}</div> <div className="text-xs mt-1 opacity-70">{roleOption.description}</div> </button> ))} </div> </div> <div className="flex items-center"> <input id="isPrimary" type="checkbox" checked={isPrimary} onChange={(e) => setIsPrimary(e.target.checked)} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" /> <label htmlFor="isPrimary" className="ml-2 block text-sm text-gray-700 dark:text-gray-300"> Primary company for this contact </label> </div> </> )} {/* Contact-Contact specific fields */} {associationType === 'contact-contact' && ( <> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3"> Relationship Type </label> <div className="space-y-2"> {relationshipTypes.map((type) => ( <button key={type.value} type="button" onClick={() => setRelationshipType(type.value)} className={` w-full px-4 py-3 rounded-lg border text-left transition-all ${relationshipType === type.value ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700' } `} > <div className="font-medium text-sm">{type.label}</div> <div className="text-xs mt-1 opacity-70">{type.description}</div> </button> ))} </div> </div> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> Relationship Strength </label> <div className="flex items-center gap-4"> <input type="range" min="1" max="5" value={strength} onChange={(e) => setStrength(parseInt(e.target.value))} className="flex-1" /> <span className="text-sm font-medium text-gray-700 dark:text-gray-300 w-8"> {strength} </span> </div> <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1"> <span>Weak</span> <span>Strong</span> </div> </div> <div> <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"> Context (Optional) </label> <textarea value={context} onChange={(e) => setContext(e.target.value)} placeholder="How do they know each other? What's the context of their relationship?" rows={3} className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white" /> </div> </> )} {/* Actions */} <div className="flex justify-end gap-3 pt-4"> <button type="button" onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" > Cancel </button> <button type="submit" disabled={isLoading} className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors" > {isLoading ? 'Creating...' : 'Create Association'} </button> </div> </form> </div> </div> ); }; export default AssociationModal;