import React from 'react';
import { TOCItem } from '../constants/helpContent';

interface TableOfContentsProps {
  sections: TOCItem[];
  activeSection: string;
  onSectionClick: (sectionId: string) => void;
}

/**
 * Table of contents sidebar component
 */
export const TableOfContents: React.FC<TableOfContentsProps> = ({
  sections,
  activeSection,
  onSectionClick
}) => {
  return (
    <div className="md:w-1/4 bg-white dark:bg-gray-800 rounded-lg shadow p-4 h-fit sticky top-4">
      <h3 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-3">
        Contents
      </h3>
      <nav>
        <ul className="space-y-2">
          {sections.map((section) => (
            <li key={section.id}>
              <button
                onClick={() => onSectionClick(section.id)}
                className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeSection === section.id
                    ? "bg-secondary/10 text-secondary dark:bg-secondary/20 dark:text-secondary-light"
                    : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                {section.title}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};