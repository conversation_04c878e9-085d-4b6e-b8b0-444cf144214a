/**
 * Harvest Invoice Cache Repository Test
 * 
 * Tests for the Harvest invoice cache repository focusing on business behavior.
 */
import { HarvestInvoiceCacheRepository } from '../../../../src/api/repositories/harvest-invoice-cache-repository';

// Mock the database module
jest.mock('../../../../src/api/services/db-service', () => ({
  default: {
    prepare: jest.fn(),
    exec: jest.fn(),
    pragma: jest.fn(),
    transaction: jest.fn(),
    close: jest.fn(),
  }
}));

describe('HarvestInvoiceCacheRepository - Business Behavior', () => {
  let repository: HarvestInvoiceCacheRepository;
  let mockDb: any;

  beforeEach(() => {
    // Get the mocked db service
    mockDb = require('../../../../src/api/services/db-service').default;
    
    // Clear all mocks
    jest.clearAllMocks();
    
    repository = new HarvestInvoiceCacheRepository();
  });

  describe('Business Rule: Invoice Cache Management', () => {
    it('should cache invoice data from Harvest for performance', () => {
      // Business requirement: Reduce API calls to Harvest by caching invoice data
      const invoiceData = {
        harvest_client_id: 12345,
        client_name: 'Acme Corp',
        total_invoiced: 50000,
        total_paid: 35000,
        total_outstanding: 15000,
        invoice_count: 10,
        overdue_count: 2,
        last_invoice_date: '2024-03-15',
        last_payment_date: '2024-03-01'
      };

      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.updateClientCache(invoiceData);

      // Verify cache is updated with aggregated data
      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('INSERT OR REPLACE INTO harvest_invoice_cache')
      );
      expect(mockStatement.run).toHaveBeenCalledWith(
        expect.objectContaining({
          harvest_client_id: 12345,
          total_invoiced: 50000,
          total_outstanding: 15000
        })
      );
    });

    it('should track cache freshness for data validity', () => {
      // Business requirement: Know when cached data needs refreshing
      const clientId = 12345;
      const now = new Date();
      const staleDate = new Date(now.getTime() - 25 * 60 * 60 * 1000); // 25 hours ago

      const mockStatement = {
        get: jest.fn().mockReturnValue({
          last_updated: staleDate.toISOString(),
          cache_version: 1
        })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const cacheInfo = repository.getCacheInfo(clientId);

      // Business expectation: Cache older than 24 hours is considered stale
      expect(cacheInfo.isStale).toBe(true);
      expect(cacheInfo.ageInHours).toBeGreaterThan(24);
    });

    it('should provide aggregated financial metrics per client', () => {
      // Business requirement: Quick access to client financial summary
      const mockData = {
        harvest_client_id: 12345,
        client_name: 'Test Client',
        total_invoiced: 100000,
        total_paid: 75000,
        total_outstanding: 25000,
        invoice_count: 20,
        overdue_count: 3,
        last_invoice_date: '2024-03-20',
        last_payment_date: '2024-03-15',
        last_updated: new Date().toISOString()
      };

      const mockStatement = {
        get: jest.fn().mockReturnValue(mockData)
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const summary = repository.getClientSummary(12345);

      // Verify financial metrics are calculated correctly
      expect(summary).toMatchObject({
        clientName: 'Test Client',
        totalInvoiced: 100000,
        totalPaid: 75000,
        totalOutstanding: 25000,
        paidPercentage: 75,
        overdueCount: 3,
        hasOverdueInvoices: true
      });
    });
  });

  describe('Business Rule: Bulk Cache Updates', () => {
    it('should efficiently update multiple client caches in batch', () => {
      // Business requirement: Minimize database operations during sync
      const clientUpdates = [
        { harvest_client_id: 1, client_name: 'Client A', total_invoiced: 10000 },
        { harvest_client_id: 2, client_name: 'Client B', total_invoiced: 20000 },
        { harvest_client_id: 3, client_name: 'Client C', total_invoiced: 30000 }
      ];

      const mockTransaction = jest.fn((callback) => callback());
      mockDb.transaction.mockReturnValue(mockTransaction);

      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.bulkUpdateCache(clientUpdates);

      // Verify transaction is used for batch updates
      expect(mockTransaction).toHaveBeenCalled();
      expect(mockStatement.run).toHaveBeenCalledTimes(3);
    });

    it('should handle cache version updates for schema changes', () => {
      // Business requirement: Support cache invalidation on schema changes
      const newVersion = 2;
      
      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.updateCacheVersion(newVersion);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE harvest_invoice_cache SET cache_version = ?')
      );
      expect(mockStatement.run).toHaveBeenCalledWith(newVersion);
    });
  });

  describe('Business Rule: Cache Query and Analysis', () => {
    it('should identify clients with high outstanding balances', () => {
      // Business requirement: Risk assessment for collections
      const mockClients = [
        { client_name: 'High Risk Client', total_outstanding: 50000, total_invoiced: 60000 },
        { client_name: 'Low Risk Client', total_outstanding: 1000, total_invoiced: 50000 },
        { client_name: 'Medium Risk Client', total_outstanding: 15000, total_invoiced: 30000 }
      ];

      const mockStatement = {
        all: jest.fn().mockReturnValue(mockClients)
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const highRiskClients = repository.getHighRiskClients(10000); // Threshold: $10k

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('WHERE total_outstanding > ?')
      );
      expect(highRiskClients).toHaveLength(2); // High Risk and Medium Risk
    });

    it('should calculate aging metrics for overdue invoices', () => {
      // Business requirement: Track invoice aging for cash flow management
      const mockStatement = {
        all: jest.fn().mockReturnValue([
          { client_name: 'Client A', overdue_count: 3, total_outstanding: 15000 },
          { client_name: 'Client B', overdue_count: 1, total_outstanding: 5000 }
        ])
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const agingReport = repository.getAgingReport();

      expect(agingReport).toMatchObject({
        totalOverdueClients: 2,
        totalOverdueInvoices: 4,
        totalOverdueAmount: 20000
      });
    });
  });

  describe('Business Rule: Cache Maintenance', () => {
    it('should clean up stale cache entries', () => {
      // Business requirement: Prevent unlimited cache growth
      const daysToKeep = 30;
      
      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 5 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const deletedCount = repository.cleanupOldEntries(daysToKeep);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM harvest_invoice_cache WHERE last_updated < ?')
      );
      expect(deletedCount).toBe(5);
    });

    it('should reset cache for specific client when data issues detected', () => {
      // Business requirement: Data integrity recovery mechanism
      const clientId = 12345;
      
      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.invalidateClientCache(clientId);

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM harvest_invoice_cache WHERE harvest_client_id = ?')
      );
      expect(mockStatement.run).toHaveBeenCalledWith(clientId);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing client gracefully', () => {
      // Business expectation: Return null for non-existent clients
      const mockStatement = {
        get: jest.fn().mockReturnValue(undefined)
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const result = repository.getClientSummary(99999);
      expect(result).toBeNull();
    });

    it('should handle database errors during cache update', () => {
      // Business expectation: Log error but don't crash
      mockDb.prepare.mockImplementation(() => {
        throw new Error('Database locked');
      });

      expect(() => {
        repository.updateClientCache({ harvest_client_id: 1, client_name: 'Test' });
      }).toThrow(/Database locked/);
    });
  });
});