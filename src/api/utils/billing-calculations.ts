/**
 * Billing calculation utilities for backend
 */

/**
 * Calculate total fees based on billing type
 * @param rate The rate (stored as daily rate)
 * @param totalDays Total days allocated
 * @param billingType 'daily' or 'hourly'
 * @param hoursPerDay Hours per day for hourly billing
 * @param rateType How the rate was originally entered ('daily' or 'hourly')
 * @param rateAsEntered The original rate value as entered
 * @returns Total fees
 */
export function calculateTotalFeesWithBilling(
  rate: number, // ALWAYS a daily rate as stored in database
  totalDays: number,
  billingType: 'daily' | 'hourly',
  hoursPerDay: number,
  rateType?: 'daily' | 'hourly',
  rateAsEntered?: number
): number {
  // If we have the original rate information and it matches the billing type,
  // use the original rate for more accurate calculations
  if (rateType && rateAsEntered !== undefined && rateType === billingType) {
    if (billingType === 'daily') {
      return rateAsEntered * totalDays;
    } else {
      // For hourly billing with original hourly rate
      const totalHours = totalDays * hoursPerDay;
      return rateAsEntered * totalHours;
    }
  }
  
  // Otherwise fall back to the current behavior
  if (billingType === 'daily') {
    // Daily billing: rate * days
    return rate * totalDays;
  } else {
    // For hourly billing with daily rate storage:
    // 1. Convert stored daily rate to hourly rate
    const hourlyRate = rate / hoursPerDay;
    // 2. Calculate total hours
    const totalHours = totalDays * hoursPerDay;
    // 3. Calculate total fees
    return hourlyRate * totalHours;
    // Note: This equals rate * totalDays, which seems odd but is correct
    // because we're storing rates that were already converted from hourly input
  }
}

/**
 * Convert rate between daily and hourly
 * @param currentRate The current rate
 * @param fromType Current billing type
 * @param toType Target billing type
 * @param hoursPerDay Hours per day for conversion
 * @returns Converted rate
 */
export function convertRate(
  currentRate: number,
  fromType: 'daily' | 'hourly',
  toType: 'daily' | 'hourly',
  hoursPerDay: number
): number {
  if (fromType === toType) return currentRate;
  
  if (toType === 'hourly') {
    // Converting from daily to hourly
    return currentRate / hoursPerDay;
  } else {
    // Converting from hourly to daily
    return currentRate * hoursPerDay;
  }
}