# Data Enrichment API

## Overview

The Data Enrichment API provides endpoints for augmenting company and contact records with external data sources. It supports multiple enrichment sources with confidence scoring, caching, and comprehensive logging.

## Base URL

```
/api/enrichment
```

## Authentication

All endpoints require session-based authentication. Users must be logged in to access enrichment functionality.

## Endpoints

### Enrich Company

Enriches a company record with data from external sources.

```http
POST /api/enrichment/companies/:companyId/enrich
```

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `companyId` | string | The ID of the company to enrich |

#### Request Body

```json
{
  "sources": ["abn_lookup"],
  "options": {
    "force": false,
    "searchTerms": {
      "abn": "***********",
      "businessName": "Example Company Pty Ltd"
    }
  }
}
```

#### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `sources` | string[] | Yes | Array of enrichment sources to use |
| `options.force` | boolean | No | Force re-enrichment even if recent data exists |
| `options.searchTerms` | object | No | Source-specific search parameters |

#### Example Response

```json
{
  "success": true,
  "data": {
    "enrichments": [
      {
        "source": "abn_lookup",
        "status": "success",
        "confidence": 0.95,
        "enrichedAt": "2025-01-06T10:30:00Z",
        "expiresAt": "2025-04-06T10:30:00Z",
        "data": {
          "businessName": "Example Company Pty Ltd",
          "abn": "***********",
          "status": "Active",
          "registrationDate": "2010-05-15",
          "businessType": "Australian Private Company",
          "gstStatus": "Registered",
          "industry": "Software Development",
          "address": {
            "street": "123 Business St",
            "city": "Sydney",
            "state": "NSW",
            "postcode": "2000"
          }
        }
      }
    ],
    "summary": {
      "successful": 1,
      "failed": 0,
      "totalSources": 1
    }
  }
}
```

### Get Company Enrichments

Retrieves existing enrichment data for a company.

```http
GET /api/enrichment/companies/:companyId
```

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `companyId` | string | The ID of the company |

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `source` | string | All sources | Filter by specific enrichment source |
| `includeExpired` | boolean | `false` | Include expired enrichment data |

#### Example Response

```json
{
  "success": true,
  "data": [
    {
      "id": "enrichment-123",
      "source": "abn_lookup",
      "sourceId": "***********",
      "confidence": 0.95,
      "enrichedAt": "2025-01-06T10:30:00Z",
      "expiresAt": "2025-04-06T10:30:00Z",
      "data": {
        "businessName": "Example Company Pty Ltd",
        "abn": "***********",
        "status": "Active"
      }
    }
  ]
}
```

### Enrich Contact

Enriches a contact record with data from external sources.

```http
POST /api/enrichment/contacts/:contactId/enrich
```

#### Path Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `contactId` | string | The ID of the contact to enrich |

#### Request Body

```json
{
  "sources": ["clearbit", "apollo"],
  "options": {
    "force": false,
    "searchTerms": {
      "email": "<EMAIL>",
      "fullName": "John Smith",
      "company": "Example Company"
    }
  }
}
```

### Get Contact Enrichments

Retrieves existing enrichment data for a contact.

```http
GET /api/enrichment/contacts/:contactId
```

Similar to company enrichments with contact-specific data.

### Bulk Enrichment

Enriches multiple entities in a batch operation.

```http
POST /api/enrichment/bulk
```

#### Request Body

```json
{
  "entityType": "company",
  "entityIds": ["company-1", "company-2", "company-3"],
  "sources": ["abn_lookup"],
  "options": {
    "force": false,
    "maxConcurrent": 5,
    "delay": 1000
  }
}
```

#### Request Parameters

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `entityType` | string | Yes | "company" or "contact" |
| `entityIds` | string[] | Yes | Array of entity IDs to enrich |
| `sources` | string[] | Yes | Array of enrichment sources |
| `options.force` | boolean | No | Force re-enrichment |
| `options.maxConcurrent` | number | No | Maximum concurrent requests (default: 3) |
| `options.delay` | number | No | Delay between requests in ms (default: 500) |

#### Example Response

```json
{
  "success": true,
  "data": {
    "jobId": "bulk-job-123",
    "status": "started",
    "totalEntities": 3,
    "progress": {
      "completed": 0,
      "failed": 0,
      "remaining": 3
    }
  }
}
```

### Get Bulk Job Status

Retrieves the status of a bulk enrichment job.

```http
GET /api/enrichment/bulk/:jobId
```

#### Example Response

```json
{
  "success": true,
  "data": {
    "jobId": "bulk-job-123",
    "status": "completed",
    "startedAt": "2025-01-06T10:00:00Z",
    "completedAt": "2025-01-06T10:05:00Z",
    "progress": {
      "completed": 2,
      "failed": 1,
      "remaining": 0
    },
    "results": [
      {
        "entityId": "company-1",
        "status": "success",
        "enrichments": 1
      },
      {
        "entityId": "company-2",
        "status": "success",
        "enrichments": 1
      },
      {
        "entityId": "company-3",
        "status": "failed",
        "error": "No matching data found"
      }
    ]
  }
}
```

### Get Enrichment Log

Retrieves enrichment attempt logs for monitoring and debugging.

```http
GET /api/enrichment/logs
```

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `entityType` | string | All types | Filter by entity type |
| `entityId` | string | All entities | Filter by specific entity |
| `source` | string | All sources | Filter by enrichment source |
| `status` | string | All statuses | Filter by status (success, failed, etc.) |
| `limit` | number | 100 | Maximum number of logs to return |
| `offset` | number | 0 | Number of logs to skip |

#### Example Response

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": "log-123",
        "entityType": "company",
        "entityId": "company-123",
        "source": "abn_lookup",
        "status": "success",
        "attemptedAt": "2025-01-06T10:30:00Z",
        "responseTimeMs": 245,
        "apiCreditsUsed": 1
      }
    ],
    "pagination": {
      "total": 150,
      "limit": 100,
      "offset": 0,
      "hasMore": true
    }
  }
}
```

### Health Check

Checks the health of enrichment sources.

```http
GET /api/enrichment/health
```

#### Example Response

```json
{
  "success": true,
  "data": {
    "overall": "healthy",
    "sources": {
      "abn_lookup": {
        "status": "healthy",
        "lastCheck": "2025-01-06T10:30:00Z",
        "responseTime": 245,
        "errorRate": 0.02,
        "quotaUsage": {
          "used": 150,
          "limit": 1000,
          "resetAt": "2025-01-07T00:00:00Z"
        }
      }
    },
    "statistics": {
      "totalEnrichments": 1250,
      "successRate": 0.95,
      "averageResponseTime": 320
    }
  }
}
```

## Data Sources

### ABN Lookup

Australian Business Number lookup service.

**Source ID:** `abn_lookup`

**Search Terms:**
- `abn` - Australian Business Number
- `businessName` - Company name for search

**Response Data:**
```json
{
  "businessName": "Example Company Pty Ltd",
  "abn": "***********",
  "status": "Active|Cancelled",
  "registrationDate": "2010-05-15",
  "businessType": "Australian Private Company",
  "gstStatus": "Registered|Not Registered",
  "industry": "Software Development",
  "tradingNames": ["Example Corp", "ExampleCo"],
  "address": {
    "street": "123 Business St",
    "city": "Sydney",
    "state": "NSW",
    "postcode": "2000"
  }
}
```

### Future Sources

Additional sources planned for implementation:

- **Clearbit** - Company and contact enrichment
- **Apollo** - Sales intelligence data
- **LinkedIn** - Professional network information

## Data Quality

### Confidence Scoring

Each enrichment includes a confidence score (0.0 to 1.0):

- **0.9-1.0** - High confidence, verified data
- **0.7-0.8** - Good confidence, likely accurate
- **0.5-0.6** - Medium confidence, may need verification
- **0.0-0.4** - Low confidence, use with caution

### Data Expiration

Enriched data includes expiration timestamps:

- **ABN Lookup** - 90 days
- **Contact data** - 30 days
- **Industry data** - 180 days

### Caching Strategy

- Automatic caching based on expiration dates
- Force refresh with `force: true` option
- Intelligent cache invalidation

## Error Handling

### Standard Error Format

```json
{
  "success": false,
  "error": "Error description",
  "message": "Detailed error message",
  "code": "ERROR_CODE"
}
```

### Error Codes

| Code | Description |
|------|-------------|
| `ENRICHMENT_SOURCE_UNAVAILABLE` | External API is unavailable |
| `ENRICHMENT_QUOTA_EXCEEDED` | API quota limit exceeded |
| `ENRICHMENT_NO_MATCH` | No matching data found |
| `ENRICHMENT_INVALID_SEARCH_TERMS` | Invalid search parameters |
| `ENRICHMENT_RATE_LIMITED` | Rate limit exceeded |

### Graceful Degradation

- Partial results returned when some sources fail
- Fallback to cached data when APIs are unavailable
- Detailed error information for debugging

## Rate Limiting

### External API Limits

- **ABN Lookup** - 1000 requests/day
- **Source-specific throttling** - Automatic backoff
- **Queue management** - Requests queued during high load

### Internal Limits

- **100 requests/hour** per user for manual enrichment
- **Bulk operations** limited to 100 entities per job
- **Concurrent limit** of 5 requests per user

## Usage Examples

### Frontend Integration

```typescript
import { fetchFromApi } from '../api/utils';

// Enrich company
const result = await fetchFromApi('/api/enrichment/companies/company-123/enrich', {
  method: 'POST',
  body: {
    sources: ['abn_lookup'],
    options: {
      searchTerms: {
        abn: '***********'
      }
    }
  }
});

// Get enrichments
const enrichments = await fetchFromApi('/api/enrichment/companies/company-123');
```

### Bulk Processing

```typescript
// Start bulk enrichment
const bulkJob = await fetchFromApi('/api/enrichment/bulk', {
  method: 'POST',
  body: {
    entityType: 'company',
    entityIds: ['company-1', 'company-2'],
    sources: ['abn_lookup']
  }
});

// Check status
const status = await fetchFromApi(`/api/enrichment/bulk/${bulkJob.data.jobId}`);
```

## Configuration

### Environment Variables

```bash
# ABN Lookup API
ABN_LOOKUP_API_KEY=your_api_key
ABN_LOOKUP_BASE_URL=https://abr.business.gov.au

# Enrichment settings
ENRICHMENT_DEFAULT_TTL=7776000  # 90 days
ENRICHMENT_MAX_RETRIES=3
ENRICHMENT_RATE_LIMIT_WINDOW=3600  # 1 hour
ENRICHMENT_RATE_LIMIT_MAX=100
```

### Database Tables

- `company_enrichment` - Enriched company data
- `contact_enrichment` - Enriched contact data
- `enrichment_log` - Enrichment attempt logs

## Monitoring

### Metrics Tracked

- Enrichment success/failure rates
- API response times
- Quota usage by source
- Cache hit/miss ratios
- Data quality scores

### Logging

- All enrichment attempts logged
- Performance metrics collected
- Error details for debugging
- API usage tracking

## Best Practices

### Performance

1. Use bulk operations for multiple entities
2. Leverage caching to minimize API calls
3. Implement proper retry logic
4. Monitor API quota usage

### Data Quality

1. Set appropriate confidence thresholds
2. Validate enriched data before use
3. Handle missing or incomplete data gracefully
4. Regular quality audits

### Cost Management

1. Cache aggressively with reasonable TTLs
2. Use force refresh sparingly
3. Monitor API credit usage
4. Implement smart retry strategies

## See Also

- [Data Enrichment Feature](../features/data-enrichment.md)
- [Data Model Documentation](../technical/data-model/README.md)
- [Company Linking API](./company-linking-api.md)