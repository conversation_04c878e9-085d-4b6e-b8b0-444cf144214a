/**
 * Unified Data Model Validation Tests
 * 
 * These tests verify the integrity and functionality of the unified data model with
 * proper company relationships and contact-company relationships.
 */
import { 
  Company, 
  CompanyRelationship, 
  CompanyRelationshipType,
  Contact,
  ContactCompanyRelationship, 
  ContactRole,
  Deal
} from '@/frontend/types/crm-types';
import { v4 as uuidv4 } from 'uuid';

describe('Unified Data Model Validation', () => {
  describe('Company Relationships', () => {
    it('creates valid company relationships', () => {
      const parentCompany: Company = {
        id: uuidv4(),
        name: 'Parent Company Inc.',
        industry: 'Technology',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      const childCompany: Company = {
        id: uuidv4(),
        name: 'Child Company LLC',
        industry: 'Software',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      const partnerCompany: Company = {
        id: uuidv4(),
        name: 'Partner Company Ltd',
        industry: 'Consulting',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // Create relationship objects
      const subsidiaryRelationship: CompanyRelationship = {
        company: childCompany,
        relationshipType: 'subsidiary'
      };
      
      const parentRelationship: CompanyRelationship = {
        company: parentCompany,
        relationshipType: 'parent'
      };
      
      const partnerRelationship: CompanyRelationship = {
        company: partnerCompany,
        relationshipType: 'partner'
      };
      
      // Assign relationships
      parentCompany.childCompanies = [subsidiaryRelationship];
      childCompany.parentCompanies = [parentRelationship];
      
      // Partner relationships
      parentCompany.childCompanies?.push(partnerRelationship);
      partnerCompany.parentCompanies = [
        {
          company: parentCompany,
          relationshipType: 'partner'
        }
      ];
      
      // Validate parent-child relationship
      expect(parentCompany.childCompanies?.length).toBe(2);
      expect(parentCompany.childCompanies?.[0].company.id).toBe(childCompany.id);
      expect(parentCompany.childCompanies?.[0].relationshipType).toBe('subsidiary');
      
      expect(childCompany.parentCompanies?.length).toBe(1);
      expect(childCompany.parentCompanies?.[0].company.id).toBe(parentCompany.id);
      expect(childCompany.parentCompanies?.[0].relationshipType).toBe('parent');
      
      // Validate partner relationship
      expect(parentCompany.childCompanies?.[1].company.id).toBe(partnerCompany.id);
      expect(parentCompany.childCompanies?.[1].relationshipType).toBe('partner');
      
      expect(partnerCompany.parentCompanies?.length).toBe(1);
      expect(partnerCompany.parentCompanies?.[0].company.id).toBe(parentCompany.id);
      expect(partnerCompany.parentCompanies?.[0].relationshipType).toBe('partner');
    });
    
    it('validates relationship types', () => {
      // Valid relationship types
      const validTypes: CompanyRelationshipType[] = [
        'parent',
        'subsidiary',
        'partner',
        'acquisition'
      ];
      
      // Test each type
      validTypes.forEach(type => {
        const relationship: CompanyRelationship = {
          company: {
            id: uuidv4(),
            name: 'Test Company',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          relationshipType: type
        };
        
        expect(relationship.relationshipType).toBe(type);
      });
      
      // Invalid type would cause TypeScript error
      // This test verifies compile-time type safety
      // @ts-expect-error - Testing invalid relationship type
      const invalidRelationship: CompanyRelationship = {
        company: {
          id: uuidv4(),
          name: 'Test Company',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        relationshipType: 'invalid-type' // This should cause a TypeScript error
      };
      
      // Even though TypeScript would catch this at compile time,
      // we still want to validate at runtime that only allowed types are present
      const allowedTypes = new Set(validTypes);
      // @ts-ignore - We're testing the invalid type here
      expect(allowedTypes.has(invalidRelationship.relationshipType)).toBe(false);
    });
  });
  
  describe('Contact-Company Relationships', () => {
    it('creates valid contact-company relationships', () => {
      // Create companies
      const company1: Company = {
        id: uuidv4(),
        name: 'Primary Company Inc.',
        industry: 'Technology',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      const company2: Company = {
        id: uuidv4(),
        name: 'Secondary Company LLC',
        industry: 'Software',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // Create contact with multiple company relationships
      const contact: Contact = {
        id: uuidv4(),
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Legacy company relationship for backward compatibility
        company: company1,
        // New relationship model
        companies: [
          {
            company: company1,
            role: 'executive',
            isPrimary: true
          },
          {
            company: company2,
            role: 'consultant',
            isPrimary: false
          }
        ]
      };
      
      // Validate contact-company relationships
      expect(contact.companies?.length).toBe(2);
      
      // Primary company relationship
      const primaryRelationship = contact.companies?.find(rel => rel.isPrimary);
      expect(primaryRelationship).toBeDefined();
      expect(primaryRelationship?.company.id).toBe(company1.id);
      expect(primaryRelationship?.role).toBe('executive');
      
      // Secondary company relationship
      const secondaryRelationship = contact.companies?.find(rel => !rel.isPrimary);
      expect(secondaryRelationship).toBeDefined();
      expect(secondaryRelationship?.company.id).toBe(company2.id);
      expect(secondaryRelationship?.role).toBe('consultant');
      
      // Legacy relationship still works
      expect(contact.company?.id).toBe(company1.id);
    });
    
    it('validates contact roles', () => {
      // Valid contact roles
      const validRoles: ContactRole[] = [
        'employee',
        'contractor',
        'consultant',
        'manager',
        'executive',
        'owner',
        'other'
      ];
      
      // Test each role
      validRoles.forEach(role => {
        const relationship: ContactCompanyRelationship = {
          company: {
            id: uuidv4(),
            name: 'Test Company',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          role: role,
          isPrimary: true
        };
        
        expect(relationship.role).toBe(role);
      });
      
      // Role is optional
      const noRoleRelationship: ContactCompanyRelationship = {
        company: {
          id: uuidv4(),
          name: 'Test Company',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        isPrimary: true
      };
      
      expect(noRoleRelationship.role).toBeUndefined();
      
      // Invalid role would cause TypeScript error
      // This test verifies compile-time type safety
      // @ts-expect-error - Testing invalid role
      const invalidRoleRelationship: ContactCompanyRelationship = {
        company: {
          id: uuidv4(),
          name: 'Test Company',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        role: 'invalid-role', // This should cause a TypeScript error
        isPrimary: true
      };
      
      // Even though TypeScript would catch this at compile time,
      // we still want to validate at runtime that only allowed roles are present
      const allowedRoles = new Set(validRoles);
      // @ts-ignore - We're testing the invalid role here
      expect(allowedRoles.has(invalidRoleRelationship.role)).toBe(false);
    });
    
    it('ensures only one primary company per contact', () => {
      // Create companies
      const companies = Array.from({ length: 3 }, (_, i) => ({
        id: uuidv4(),
        name: `Company ${i + 1}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));
      
      // Create contact with multiple companies, but only one primary
      const contact: Contact = {
        id: uuidv4(),
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        companies: [
          {
            company: companies[0],
            role: 'manager',
            isPrimary: true
          },
          {
            company: companies[1],
            role: 'consultant',
            isPrimary: false
          },
          {
            company: companies[2],
            role: 'employee',
            isPrimary: false
          }
        ]
      };
      
      // Check that there's only one primary company
      const primaryCompanies = contact.companies?.filter(rel => rel.isPrimary) || [];
      expect(primaryCompanies.length).toBe(1);
      expect(primaryCompanies[0].company.id).toBe(companies[0].id);
      
      // Helper function to simulate changing primary company
      function setPrimaryCompany(relationships: ContactCompanyRelationship[], index: number): ContactCompanyRelationship[] {
        return relationships.map((rel, i) => ({
          ...rel,
          isPrimary: i === index
        }));
      }
      
      // Change primary company to the second one
      if (contact.companies) {
        contact.companies = setPrimaryCompany(contact.companies, 1);
        
        // Verify the change
        const newPrimaryCompanies = contact.companies.filter(rel => rel.isPrimary);
        expect(newPrimaryCompanies.length).toBe(1);
        expect(newPrimaryCompanies[0].company.id).toBe(companies[1].id);
      }
    });
  });
  
  describe('Deal-Company Relationships', () => {
    it('creates valid deal with company relationships', () => {
      // Create companies
      const primaryCompany: Company = {
        id: uuidv4(),
        name: 'Primary Client Inc.',
        industry: 'Finance',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      const relatedCompany1: Company = {
        id: uuidv4(),
        name: 'Related Company A',
        industry: 'Insurance',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      const relatedCompany2: Company = {
        id: uuidv4(),
        name: 'Related Company B',
        industry: 'Real Estate',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // Create deal with primary and related companies
      const deal: Deal = {
        id: uuidv4(),
        name: 'Financial Services Deal',
        stage: 'Qualified',
        value: 75000,
        currency: 'AUD',
        probability: 0.7,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        // Primary company
        company: primaryCompany,
        // Related companies
        relatedCompanies: [
          {
            company: relatedCompany1,
            relationshipType: 'subsidiary'
          },
          {
            company: relatedCompany2,
            relationshipType: 'partner'
          }
        ]
      };
      
      // Validate deal-company relationships
      expect(deal.company?.id).toBe(primaryCompany.id);
      expect(deal.relatedCompanies?.length).toBe(2);
      
      // Check each related company
      const subsidiaryCompany = deal.relatedCompanies?.find(
        rel => rel.relationshipType === 'subsidiary'
      );
      expect(subsidiaryCompany).toBeDefined();
      expect(subsidiaryCompany?.company.id).toBe(relatedCompany1.id);
      
      const partnerCompany = deal.relatedCompanies?.find(
        rel => rel.relationshipType === 'partner'
      );
      expect(partnerCompany).toBeDefined();
      expect(partnerCompany?.company.id).toBe(relatedCompany2.id);
    });
  });
});