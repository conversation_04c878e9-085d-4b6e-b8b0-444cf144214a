#!/bin/bash
set -e

# Simple Render Deployment Script
# Usage: ./scripts/deploy.sh [preview|production]

ENVIRONMENT=${1:-preview}

if [[ "$ENVIRONMENT" != "preview" && "$ENVIRONMENT" != "production" ]]; then
  echo "❌ Usage: ./scripts/deploy.sh [preview|production]"
  exit 1
fi

# Set branch mapping
if [ "$ENVIRONMENT" == "preview" ]; then
  SOURCE_BRANCH="preview"
  TARGET_BRANCH="pre-built-preview"
else
  SOURCE_BRANCH="main"
  TARGET_BRANCH="pre-built-production"
fi

echo "🚀 Deploying $ENVIRONMENT environment"
echo "   Source: $SOURCE_BRANCH → Target: $TARGET_BRANCH"

# Verify current branch
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" != "$SOURCE_BRANCH" ]; then
  echo "❌ Switch to $SOURCE_BRANCH branch first"
  echo "   Current: $CURRENT_BRANCH"
  exit 1
fi

# Check for uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
  echo "❌ Commit your changes first"
  exit 1
fi

# Pull latest changes
echo "📥 Pulling latest changes..."
git pull origin $SOURCE_BRANCH

# Build for deployment
echo "🔨 Building application..."
npm ci --legacy-peer-deps
npm run build

# Deploy to Render branch
echo "📤 Deploying to Render..."
COMMIT_MSG=$(git log -1 --pretty=%B | head -1)
TEMP_DIR=$(mktemp -d)

# Copy built files and ensure proper structure
cp -r dist/* $TEMP_DIR/
cp render.yaml $TEMP_DIR/ 2>/dev/null || echo "No render.yaml found"

# Ensure migrations are at root level (not inside dist)
if [ -d "migrations" ]; then
  cp -r migrations $TEMP_DIR/
  echo "✅ Copied migrations to deployment"
fi

# Ensure package.json is at root (should be created by build script)
if [ ! -f "$TEMP_DIR/package.json" ]; then
  echo "❌ No package.json found in dist/"
  exit 1
fi

# Ensure scripts directory exists at root level
if [ ! -d "$TEMP_DIR/scripts" ]; then
  echo "❌ No scripts directory found in dist/"
  exit 1
fi

# Switch to deployment branch
git fetch origin $TARGET_BRANCH 2>/dev/null || true
git checkout $TARGET_BRANCH 2>/dev/null || git checkout -b $TARGET_BRANCH

# Replace content (exclude node_modules)
find . -mindepth 1 -maxdepth 1 -not -name ".git" -exec rm -rf {} \;
cp -r $TEMP_DIR/* .
# Remove any node_modules that got copied - Render will install fresh
rm -rf node_modules src/node_modules

# Deploy
git add .
if git commit -m "Deploy $ENVIRONMENT: $COMMIT_MSG"; then
  git push -f origin $TARGET_BRANCH
  echo "✅ Deployed successfully!"
else
  echo "⚠️  No changes to deploy"
fi

# Return to source branch
git checkout $SOURCE_BRANCH
rm -rf $TEMP_DIR

echo ""
echo "🎉 Deployment complete!"
echo "   Check Render dashboard for status"
echo "   URL: https://upstream${ENVIRONMENT:+"-$ENVIRONMENT"}.onbord.au"