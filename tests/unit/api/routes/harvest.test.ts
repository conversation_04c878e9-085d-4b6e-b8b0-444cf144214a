import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/integrations/harvest');
jest.mock('../../../../src/services/harvest/invoice-service');
jest.mock('../../../../src/services/harvest/project-budget-service');
jest.mock('../../../../src/services/harvest/time-report-service');
jest.mock('../../../../src/utils/backend-logger');

describe('Harvest API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/harvest/projects', () => {
    it('should fetch active projects', async () => {
      const { getProjects } = await import('../../../../src/api/integrations/harvest');
      const mockProjects = [
        { id: 1, name: 'Project 1', is_active: true },
        { id: 2, name: 'Project 2', is_active: true },
      ];
      (getProjects as jest.Mock).mockResolvedValue(mockProjects);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/projects'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getProjects).toHaveBeenCalledWith({ is_active: true });
      expect(mockResponse.json).toHaveBeenCalledWith(mockProjects);
    });

    it('should handle errors', async () => {
      const { getProjects } = await import('../../../../src/api/integrations/harvest');
      const error = new Error('API Error');
      (getProjects as jest.Mock).mockRejectedValue(error);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/projects'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('GET /api/harvest/invoices', () => {
    it('should fetch invoices with date filter', async () => {
      mockRequest.query = { from: '2024-01-01', to: '2024-12-31' };
      const { getInvoices } = await import('../../../../src/api/integrations/harvest');
      const mockInvoices = [
        { id: 1, number: 'INV-001', amount: 1000 },
        { id: 2, number: 'INV-002', amount: 2000 },
      ];
      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/invoices'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getInvoices).toHaveBeenCalledWith({
        from: '2024-01-01',
        to: '2024-12-31',
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockInvoices);
    });
  });

  describe('GET /api/harvest/time_entries', () => {
    it('should fetch time entries with filters', async () => {
      mockRequest.query = {
        from: '2024-01-01',
        to: '2024-01-31',
        project_id: '123',
      };
      const { getTimeEntries } = await import('../../../../src/api/integrations/harvest');
      const mockTimeEntries = [
        { id: 1, hours: 8, project: { id: 123 } },
        { id: 2, hours: 6, project: { id: 123 } },
      ];
      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/time_entries'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getTimeEntries).toHaveBeenCalledWith({
        from: '2024-01-01',
        to: '2024-01-31',
        project_id: '123',
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockTimeEntries);
    });
  });

  describe('GET /api/harvest/users', () => {
    it('should fetch active users', async () => {
      const { getUsers } = await import('../../../../src/api/integrations/harvest');
      const mockUsers = [
        { id: 1, first_name: 'John', last_name: 'Doe', is_active: true },
        { id: 2, first_name: 'Jane', last_name: 'Smith', is_active: true },
      ];
      (getUsers as jest.Mock).mockResolvedValue(mockUsers);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/users'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getUsers).toHaveBeenCalledWith({ is_active: true });
      expect(mockResponse.json).toHaveBeenCalledWith(mockUsers);
    });
  });

  describe('GET /api/harvest/clients', () => {
    it('should fetch active clients', async () => {
      const { getClients } = await import('../../../../src/api/integrations/harvest');
      const mockClients = [
        { id: 1, name: 'Client 1', is_active: true },
        { id: 2, name: 'Client 2', is_active: true },
      ];
      (getClients as jest.Mock).mockResolvedValue(mockClients);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/clients'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getClients).toHaveBeenCalledWith({ is_active: true });
      expect(mockResponse.json).toHaveBeenCalledWith(mockClients);
    });
  });

  describe('GET /api/harvest/late-invoices', () => {
    it('should fetch late invoices', async () => {
      const { getLateInvoices } = await import('../../../../src/services/harvest/late-invoice-service');
      const mockLateInvoices = [
        { id: 1, number: 'INV-001', daysOverdue: 30 },
        { id: 2, number: 'INV-002', daysOverdue: 15 },
      ];
      (getLateInvoices as jest.Mock).mockResolvedValue(mockLateInvoices);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/late-invoices'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getLateInvoices).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockLateInvoices);
    });
  });

  describe('GET /api/harvest/project-budgets', () => {
    it('should fetch project budgets', async () => {
      const { getProjectBudgets } = await import('../../../../src/services/harvest/project-budget-service');
      const mockBudgets = [
        { projectId: 1, budget: 10000, spent: 5000, remaining: 5000 },
        { projectId: 2, budget: 20000, spent: 8000, remaining: 12000 },
      ];
      (getProjectBudgets as jest.Mock).mockResolvedValue(mockBudgets);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/project-budgets'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getProjectBudgets).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockBudgets);
    });
  });

  describe('GET /api/harvest/time-reports', () => {
    it('should fetch time reports with filters', async () => {
      mockRequest.query = {
        from: '2024-01-01',
        to: '2024-01-31',
        groupBy: 'project',
      };
      const { getTimeReports } = await import('../../../../src/services/harvest/time-report-service');
      const mockReports = {
        summary: { totalHours: 160, totalValue: 16000 },
        breakdown: [
          { projectId: 1, hours: 80, value: 8000 },
          { projectId: 2, hours: 80, value: 8000 },
        ],
      };
      (getTimeReports as jest.Mock).mockResolvedValue(mockReports);

      const harvestRoutes = await import('../../../../src/api/routes/harvest');
      const handler = harvestRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/time-reports'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(getTimeReports).toHaveBeenCalledWith({
        from: '2024-01-01',
        to: '2024-01-31',
        groupBy: 'project',
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockReports);
    });
  });
});