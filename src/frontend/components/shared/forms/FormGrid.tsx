import React from "react"; import { cn } from "../../../utils/component-variants"; export interface FormGridProps extends React.HTMLAttributes<HTMLDivElement> { cols?: 1 | 2 | 3 | 4; gap?: "sm" | "default" | "lg"; children: React.ReactNode; } /** * FormGrid component for responsive form layouts * * Features: * - Responsive grid layouts (1-4 columns) * - Configurable gap sizes * - Mobile-first responsive design * - Consistent spacing * - Container query support */ export const FormGrid: React.FC<FormGridProps> = ({ className, cols = 2, gap = "default", children, ...props }) => { const gridClasses = cn( "form-grid", { "form-grid--cols-1": cols === 1, "form-grid--cols-2": cols === 2, "form-grid--cols-3": cols === 3, "form-grid--cols-4": cols === 4, }, { "gap-2": gap === "sm", "gap-4": gap === "default", "gap-6": gap === "lg", }, className ); return ( <div className={gridClasses} {...props}> {children} </div> ); }; export default FormGrid; 