import { ProjectSettingsService } from '../../../../src/services/cashflow/project-settings-service';
import { SettingsRepository } from '../../../../src/api/repositories/settings-repository';
import type { ProjectSettings } from '../../../../src/types/financial';

jest.mock('../../../../src/api/repositories/settings-repository');

describe('ProjectSettingsService', () => {
  let service: ProjectSettingsService;
  let mockSettingsRepository: jest.Mocked<SettingsRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSettingsRepository = new SettingsRepository(null as any) as jest.Mocked<SettingsRepository>;
    service = new ProjectSettingsService(mockSettingsRepository);
  });

  describe('getProjectSettings', () => {
    const defaultSettings: ProjectSettings = {
      enableProjectedInvoices: true,
      defaultPaymentTerms: 30,
      includeExpectedScenarios: true,
      defaultInvoiceFrequency: 'monthly',
      workingDaysPerWeek: 5,
      hoursPerDay: 8,
      cashBufferDays: 7,
      worstCaseScenarioFactor: 0.8,
      bestCaseScenarioFactor: 1.2
    };

    it('should return stored settings for project', async () => {
      const storedSettings = {
        enableProjectedInvoices: false,
        defaultPaymentTerms: 45,
        includeExpectedScenarios: false,
        defaultInvoiceFrequency: 'weekly',
        workingDaysPerWeek: 4,
        hoursPerDay: 7,
        cashBufferDays: 14,
        worstCaseScenarioFactor: 0.7,
        bestCaseScenarioFactor: 1.3
      };

      mockSettingsRepository.get.mockResolvedValue({
        key: 'project_settings_project123',
        value: storedSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      const result = await service.getProjectSettings('project123');

      expect(result).toEqual(storedSettings);
      expect(mockSettingsRepository.get).toHaveBeenCalledWith('project_settings_project123');
    });

    it('should return default settings if none stored', async () => {
      mockSettingsRepository.get.mockResolvedValue(null);

      const result = await service.getProjectSettings('project123');

      expect(result).toEqual(defaultSettings);
    });

    it('should merge partial settings with defaults', async () => {
      const partialSettings = {
        enableProjectedInvoices: false,
        defaultPaymentTerms: 60
      };

      mockSettingsRepository.get.mockResolvedValue({
        key: 'project_settings_project123',
        value: partialSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      const result = await service.getProjectSettings('project123');

      expect(result).toEqual({
        ...defaultSettings,
        ...partialSettings
      });
    });

    it('should handle repository errors', async () => {
      mockSettingsRepository.get.mockRejectedValue(new Error('Database error'));

      // Should return defaults on error
      const result = await service.getProjectSettings('project123');

      expect(result).toEqual(defaultSettings);
    });
  });

  describe('updateProjectSettings', () => {
    it('should update existing settings', async () => {
      const existingSettings = {
        enableProjectedInvoices: true,
        defaultPaymentTerms: 30,
        includeExpectedScenarios: true,
        defaultInvoiceFrequency: 'monthly',
        workingDaysPerWeek: 5,
        hoursPerDay: 8,
        cashBufferDays: 7,
        worstCaseScenarioFactor: 0.8,
        bestCaseScenarioFactor: 1.2
      };

      const updates = {
        defaultPaymentTerms: 45,
        includeExpectedScenarios: false
      };

      const expectedSettings = {
        ...existingSettings,
        ...updates
      };

      mockSettingsRepository.get.mockResolvedValue({
        key: 'project_settings_project123',
        value: existingSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      mockSettingsRepository.upsert.mockResolvedValue({
        key: 'project_settings_project123',
        value: expectedSettings,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      const result = await service.updateProjectSettings('project123', updates);

      expect(result).toEqual(expectedSettings);
      expect(mockSettingsRepository.upsert).toHaveBeenCalledWith(
        'project_settings_project123',
        expectedSettings
      );
    });

    it('should create new settings if none exist', async () => {
      const newSettings = {
        defaultPaymentTerms: 45,
        cashBufferDays: 14
      };

      mockSettingsRepository.get.mockResolvedValue(null);
      mockSettingsRepository.upsert.mockResolvedValue({
        key: 'project_settings_project123',
        value: expect.any(Object),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      await service.updateProjectSettings('project123', newSettings);

      expect(mockSettingsRepository.upsert).toHaveBeenCalledWith(
        'project_settings_project123',
        expect.objectContaining(newSettings)
      );
    });

    it('should validate payment terms', async () => {
      const invalidSettings = {
        defaultPaymentTerms: -10
      };

      await expect(service.updateProjectSettings('project123', invalidSettings))
        .rejects.toThrow('Invalid payment terms');
    });

    it('should validate scenario factors', async () => {
      const invalidSettings = {
        worstCaseScenarioFactor: 1.5, // Should be < 1
        bestCaseScenarioFactor: 0.5   // Should be > 1
      };

      await expect(service.updateProjectSettings('project123', invalidSettings))
        .rejects.toThrow('Invalid scenario factors');
    });
  });

  describe('getAllProjectSettings', () => {
    it('should return all project settings', async () => {
      const allSettings = [
        {
          key: 'project_settings_project1',
          value: { enableProjectedInvoices: true, defaultPaymentTerms: 30 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          key: 'project_settings_project2',
          value: { enableProjectedInvoices: false, defaultPaymentTerms: 45 },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      mockSettingsRepository.getByPrefix.mockResolvedValue(allSettings);

      const result = await service.getAllProjectSettings();

      expect(result).toEqual({
        project1: { enableProjectedInvoices: true, defaultPaymentTerms: 30 },
        project2: { enableProjectedInvoices: false, defaultPaymentTerms: 45 }
      });
      expect(mockSettingsRepository.getByPrefix).toHaveBeenCalledWith('project_settings_');
    });

    it('should handle empty results', async () => {
      mockSettingsRepository.getByPrefix.mockResolvedValue([]);

      const result = await service.getAllProjectSettings();

      expect(result).toEqual({});
    });
  });

  describe('deleteProjectSettings', () => {
    it('should delete project settings', async () => {
      mockSettingsRepository.delete.mockResolvedValue(true);

      const result = await service.deleteProjectSettings('project123');

      expect(result).toBe(true);
      expect(mockSettingsRepository.delete).toHaveBeenCalledWith('project_settings_project123');
    });

    it('should return false if settings not found', async () => {
      mockSettingsRepository.delete.mockResolvedValue(false);

      const result = await service.deleteProjectSettings('nonexistent');

      expect(result).toBe(false);
    });
  });

  describe('getDefaultSettings', () => {
    it('should return default settings', () => {
      const defaults = service.getDefaultSettings();

      expect(defaults).toEqual({
        enableProjectedInvoices: true,
        defaultPaymentTerms: 30,
        includeExpectedScenarios: true,
        defaultInvoiceFrequency: 'monthly',
        workingDaysPerWeek: 5,
        hoursPerDay: 8,
        cashBufferDays: 7,
        worstCaseScenarioFactor: 0.8,
        bestCaseScenarioFactor: 1.2
      });
    });
  });

  describe('validateSettings', () => {
    it('should validate valid settings', () => {
      const validSettings = {
        defaultPaymentTerms: 45,
        workingDaysPerWeek: 4,
        hoursPerDay: 7,
        cashBufferDays: 14,
        worstCaseScenarioFactor: 0.7,
        bestCaseScenarioFactor: 1.3
      };

      expect(() => service.validateSettings(validSettings)).not.toThrow();
    });

    it('should reject negative payment terms', () => {
      const invalidSettings = {
        defaultPaymentTerms: -30
      };

      expect(() => service.validateSettings(invalidSettings))
        .toThrow('Payment terms must be non-negative');
    });

    it('should reject invalid working days', () => {
      const invalidSettings = {
        workingDaysPerWeek: 8
      };

      expect(() => service.validateSettings(invalidSettings))
        .toThrow('Working days per week must be between 1 and 7');
    });

    it('should reject invalid hours per day', () => {
      const invalidSettings = {
        hoursPerDay: 25
      };

      expect(() => service.validateSettings(invalidSettings))
        .toThrow('Hours per day must be between 1 and 24');
    });

    it('should reject worst case factor >= 1', () => {
      const invalidSettings = {
        worstCaseScenarioFactor: 1.2
      };

      expect(() => service.validateSettings(invalidSettings))
        .toThrow('Worst case scenario factor must be less than 1');
    });

    it('should reject best case factor <= 1', () => {
      const invalidSettings = {
        bestCaseScenarioFactor: 0.8
      };

      expect(() => service.validateSettings(invalidSettings))
        .toThrow('Best case scenario factor must be greater than 1');
    });
  });
});