import { Router } from 'express';
import { sendEmail, formatFeedbackHtml } from '../../utils/email';

const router = Router();

// Email address to receive feedback notifications
const FEEDBACK_RECIPIENT = process.env.FEEDBACK_EMAIL || '<EMAIL>';

/**
 * @route POST /api/feedback
 * @desc Submit feedback (bug report or feature request)
 * @access Private
 */
router.post('/', async (req, res) => {
  try {
    const { type, title, description, email, priority } = req.body;

    // Validate required fields
    if (!type || !title || !description) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['type', 'title', 'description']
      });
    }

    // Validate type
    if (type !== 'bug' && type !== 'feature') {
      return res.status(400).json({
        success: false,
        error: 'Invalid feedback type',
        validTypes: ['bug', 'feature']
      });
    }

    // Prepare feedback data
    const feedbackData = {
      type,
      title,
      description,
      email: email || 'Not provided',
      priority: type === 'bug' ? priority || 'medium' : 'N/A',
      timestamp: new Date().toISOString(),
    };

    // Log the feedback
    console.log('Feedback received:', feedbackData);

    // Send email notification
    const emailSubject = `${type === 'bug' ? 'Bug Report' : 'Feature Request'}: ${title}`;
    const emailText = `
Type: ${type}
Title: ${title}
Description: ${description}
Priority: ${type === 'bug' ? priority || 'medium' : 'N/A'}
Email: ${email || 'Not provided'}
Timestamp: ${new Date().toISOString()}
    `;

    // Format HTML version of the email
    const emailHtml = formatFeedbackHtml(feedbackData);

    try {
      // Send the email
      const emailResult = await sendEmail(
        FEEDBACK_RECIPIENT,
        emailSubject,
        emailText,
        emailHtml
      );

      if (!emailResult.success) {
        console.error('Failed to send feedback email:', emailResult.error);
      }
    } catch (emailError) {
      console.error('Error sending feedback email:', emailError);
      // Continue processing - don't fail the request if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Feedback submitted successfully'
    });
  } catch (error: any) {
    console.error('Error submitting feedback:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to submit feedback',
      message: error.message
    });
  }
});

export default router;
