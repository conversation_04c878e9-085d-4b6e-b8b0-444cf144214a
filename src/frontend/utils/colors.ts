/**
 * @file Centralized color constants and utilities
 * 
 * This file provides a comprehensive color system for the application,
 * using the Flexoki theme as the single source of truth.
 * All colors are imported from flexoki-theme.ts to ensure consistency.
 */

import { flexokiPalette, flexokiChartColors, getFinancialColors as getFlexokiFinancialColors } from '../../styles/flexoki-theme';

/**
 * Core color palette using Flexoki values
 * These colors are used consistently across different features
 */
export const coreColors = {
  // Primary blues
  blue: {
    100: flexokiPalette.blue[100],
    200: flexokiPalette.blue[200], 
    300: flexokiPalette.blue[300],
    500: flexokiPalette.blue[500],
    600: flexokiPalette.blue[600],
    800: flexokiPalette.blue[800]
  },
  // Secondary colors (mapped to Flexoki)
  emerald: {
    100: flexokiPalette.green[100],
    200: flexokiPalette.green[200],
    300: flexokiPalette.green[300],
    500: flexokiPalette.green[500],
    600: flexokiPalette.green[600]
  },
  purple: {
    100: flexokiPalette.purple[100],
    200: flexokiPalette.purple[200],
    300: flexokiPalette.purple[300],
    500: flexokiPalette.purple[500],
    600: flexokiPalette.purple[600]
  },
  // Additional colors
  teal: {
    200: flexokiPalette.cyan[200],
    300: flexokiPalette.cyan[300],
    500: flexokiPalette.cyan[500],
    600: flexokiPalette.cyan[600]
  },
  amber: {
    100: flexokiPalette.orange[100],
    200: flexokiPalette.orange[200],
    500: flexokiPalette.orange[500],
    600: flexokiPalette.orange[600],
    800: flexokiPalette.orange[800]
  },
  // Status colors
  red: {
    100: flexokiPalette.red[100],
    200: flexokiPalette.red[200],
    500: flexokiPalette.red[500],
    800: flexokiPalette.red[800]
  },
  green: {
    100: flexokiPalette.green[100],
    200: flexokiPalette.green[200], 
    500: flexokiPalette.green[500],
    800: flexokiPalette.green[800]
  },
  // Neutral colors
  gray: {
    100: flexokiPalette.base[100],
    200: flexokiPalette.base[200],
    600: flexokiPalette.base[600],
    800: flexokiPalette.base[800]
  },
  slate: {
    100: flexokiPalette.base[100],
    200: flexokiPalette.base[200],
    700: flexokiPalette.base[700],
    800: flexokiPalette.base[800]
  }
};

/**
 * Task visualization colors (hex format for charts)
 * Using Flexoki chart colors for consistency
 */
export const taskColors: string[] = [
  flexokiPalette.blue[600],    // Billable tasks
  flexokiPalette.orange[600],  // Public Holiday  
  flexokiPalette.purple[600],  // Annual Leave
  flexokiPalette.cyan[600],    // Other Unpaid Leave
  flexokiPalette.yellow[600],  // Salesforce
  flexokiPalette.magenta[600], // Interviews
  flexokiPalette.green[600],   // Additional colors
  flexokiPalette.blue[400],
  flexokiPalette.orange[400],
  flexokiPalette.purple[400],
  flexokiPalette.cyan[400],
  flexokiPalette.yellow[400],
  flexokiPalette.magenta[400],
  flexokiPalette.green[400],
  flexokiPalette.red[600],
  flexokiPalette.base[500],
  flexokiPalette.base[600],
  flexokiPalette.base[700],
  flexokiPalette.red[400],
  flexokiPalette.red[700],
];

/**
 * Expense type color mapping
 * Used for expense categorization badges
 */
export const expenseTypeColors = {
  'Monthly Payroll': 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30',
  'Superannuation': 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:border-yellow-800/30',
  'Insurances': 'bg-magenta-100 text-magenta-800 border-magenta-200 dark:border-magenta-800/30',
  'Taxes': 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30',
  'Subcontractor Fees': 'bg-purple-100 text-purple-800 border-purple-200 dark:border-purple-800/30',
  'Rent': 'bg-green-100 text-green-800 border-green-200 dark:border-green-800/30',
  'Reimbursements': 'bg-cyan-100 text-cyan-800 border-cyan-200 dark:border-cyan-800/30',
  'Professional Fees': 'bg-cyan-100 text-cyan-800 border-cyan-200 dark:border-cyan-800/30',
  'General Expenses': 'bg-cyan-100 text-cyan-800 border-cyan-200 dark:border-cyan-800/30',
  'Director Distributions': 'bg-purple-100 text-purple-800 border-purple-200 dark:border-purple-800/30',
  'Hardware': 'bg-magenta-100 text-magenta-800 border-magenta-200 dark:border-magenta-800/30',
  'Subscriptions': 'bg-red-100 text-red-800 border-red-200 dark:border-red-800/30',
  'Other Fees': 'bg-orange-100 text-orange-800 border-orange-200 dark:border-orange-800/30',
  'Other': 'bg-gray-100 text-gray-800 border-gray-200 dark:border-gray-600'
} as const;

/**
 * Expense frequency color mapping
 * Used for frequency badges
 */
export const expenseFrequencyColors = {
  'one-off': 'bg-slate-100 text-slate-800 border-slate-200 dark:border-slate-700',
  'weekly': 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30',
  'monthly': 'bg-purple-100 text-purple-800 border-purple-200 dark:border-purple-800/30',
  'quarterly': 'bg-orange-100 text-orange-800 border-orange-200 dark:border-orange-800/30'
} as const;

/**
 * Tax calendar color schemes
 * Used for PAYGW and GST calendar displays
 */
export const taxCalendarColors = {
  paygw: {
    light: [
      'bg-blue-200', 'bg-green-200', 'bg-purple-200', 'bg-cyan-200',
      'bg-purple-200', 'bg-green-200', 'bg-blue-200', 'bg-cyan-200',
      'bg-blue-300', 'bg-green-300', 'bg-purple-300', 'bg-cyan-300'
    ],
    dark: [
      'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-cyan-500',
      'bg-purple-500', 'bg-green-500', 'bg-blue-500', 'bg-cyan-500',
      'bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-cyan-600'
    ],
    payment: {
      light: [
        'bg-blue-200 border-2 border-blue-500',
        'bg-green-200 border-2 border-green-500',
        'bg-purple-200 border-2 border-purple-500',
        'bg-cyan-200 border-2 border-cyan-500',
        'bg-purple-200 border-2 border-purple-500',
        'bg-green-200 border-2 border-green-500',
        'bg-blue-200 border-2 border-blue-500',
        'bg-cyan-200 border-2 border-cyan-500',
        'bg-blue-300 border-2 border-blue-600',
        'bg-green-300 border-2 border-green-600',
        'bg-purple-300 border-2 border-purple-600',
        'bg-cyan-300 border-2 border-cyan-600'
      ],
      dark: [
        'bg-blue-500 border-2 border-blue-300',
        'bg-green-500 border-2 border-green-300',
        'bg-purple-500 border-2 border-purple-300',
        'bg-cyan-500 border-2 border-cyan-300',
        'bg-purple-500 border-2 border-purple-300',
        'bg-green-500 border-2 border-green-300',
        'bg-blue-500 border-2 border-blue-300',
        'bg-cyan-500 border-2 border-cyan-300',
        'bg-blue-600 border-2 border-blue-200',
        'bg-green-600 border-2 border-green-200',
        'bg-purple-600 border-2 border-purple-200',
        'bg-cyan-600 border-2 border-cyan-200'
      ]
    }
  },
  gst: {
    light: ['bg-yellow-200', 'bg-red-200', 'bg-orange-300', 'bg-magenta-300'],
    dark: ['bg-yellow-500', 'bg-red-500', 'bg-orange-600', 'bg-magenta-600'],
    payment: {
      light: [
        'bg-yellow-200 border-2 border-yellow-600',
        'bg-red-200 border-2 border-red-600',
        'bg-orange-300 border-2 border-orange-600',
        'bg-magenta-300 border-2 border-magenta-600'
      ],
      dark: [
        'bg-yellow-500 border-2 border-yellow-300',
        'bg-red-500 border-2 border-red-300',
        'bg-orange-600 border-2 border-orange-300',
        'bg-magenta-600 border-2 border-magenta-300'
      ]
    }
  }
};

/**
 * Financial color standards
 * Using Flexoki financial colors for consistency
 */
export const financialColors = {
  income: {
    light: 'text-green-600',
    dark: 'text-green-400',
    bg: 'bg-green-50 dark:bg-green-900/30'
  },
  expense: {
    light: 'text-red-600', 
    dark: 'text-red-400',
    bg: 'bg-red-50 dark:bg-red-900/30'
  },
  neutral: {
    light: 'text-base-600',
    dark: 'text-base-400',
    bg: 'bg-base-100 dark:bg-base-900/30'
  }
};

/**
 * Status color scheme
 * For various status indicators throughout the app
 */
export const statusColors = {
  success: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',
  warning: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',
  error: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',
  info: 'bg-cyan-100 text-cyan-800 border-cyan-200 dark:bg-cyan-900/30 dark:text-cyan-300',
  neutral: 'bg-base-100 text-base-800 border-base-200 dark:bg-base-700 dark:text-base-300'
};

/**
 * Utility functions for color management
 */

/**
 * Get a color for a task based on its ID and properties
 * Ensures consistent colors across different views
 */
export const getTaskColor = (taskId: number, taskName: string, isBillable: boolean): string => {
  // Billable tasks are always blue
  if (isBillable) {
    return taskColors[0];
  }

  // Map common task names to specific colors
  const commonTaskColors: Record<string, string> = {
    'Public Holiday': taskColors[1],
    'Annual Leave': taskColors[2], 
    'Other Unpaid Leave': taskColors[3],
    'Salesforce': taskColors[4],
    'Interviews': taskColors[5],
  };

  // Check if this is a common task with a predefined color
  if (commonTaskColors[taskName]) {
    return commonTaskColors[taskName];
  }

  // For other non-billable tasks, use a hash of the task ID to pick a consistent color
  const hash = taskId % (taskColors.length - 1);
  return taskColors[hash + 1]; // +1 to skip the billable color
};

/**
 * Get CSS class for expense type
 */
export const getExpenseTypeClass = (type: keyof typeof expenseTypeColors | string): string => {
  return expenseTypeColors[type as keyof typeof expenseTypeColors] || expenseTypeColors['Other'];
};

/**
 * Get CSS class for expense frequency
 */
export const getExpenseFrequencyClass = (frequency: keyof typeof expenseFrequencyColors | string): string => {
  return expenseFrequencyColors[frequency as keyof typeof expenseFrequencyColors] || statusColors.neutral;
};

/**
 * Get tax calendar color class
 */
export const getTaxCalendarColor = (
  type: 'paygw' | 'gst',
  index: number,
  isPayment: boolean,
  isDarkMode: boolean,
  isHighlighted: boolean
): string => {
  const colorSet = taxCalendarColors[type];
  
  // Determine which color variant to use based on theme and highlight state
  const variant = isDarkMode 
    ? (isHighlighted ? 'light' : 'dark')
    : (isHighlighted ? 'dark' : 'light');
  
  if (isPayment) {
    return colorSet.payment[variant][index % colorSet.payment[variant].length];
  }
  
  return colorSet[variant][index % colorSet[variant].length];
};

// Legacy tax calendar colors for backward compatibility
export const paygwColors = {
  normal: '#4385BE',    // Flexoki blue-400
  bg_normal: '#E1ECEB', // Light blue background approximation
  important: '#DA702C', // Flexoki orange-400
  bg_important: '#FFE7CE', // Flexoki orange-50
  urgent: '#AF3029',    // Flexoki red-600
  bg_urgent: '#FFE1D5'  // Flexoki red-50
};

export const gstColors = {
  normal: '#66800B',    // Flexoki green-600
  bg_normal: '#EDEECF', // Flexoki green-50
  important: '#DA702C', // Flexoki orange-400
  bg_important: '#FFE7CE', // Flexoki orange-50
  urgent: '#AF3029',    // Flexoki red-600
  bg_urgent: '#FFE1D5'  // Flexoki red-50
};

/**
 * Legacy getColorClass function for backward compatibility
 * @deprecated Use getTaxCalendarColor instead for new code
 */
export const getColorClass = (
  type: 'paygw' | 'gst',
  importance: 'normal' | 'important' | 'urgent',
  background: boolean = false
): string => {
  const colors = type === 'paygw' ? paygwColors : gstColors;
  const key = background ? `bg_${importance}` : importance;
  return colors[key as keyof typeof colors];
};

/**
 * Get financial color based on value (positive/negative)
 * Delegates to Flexoki theme for consistency
 */
export const getFinancialColor = (value: number, isDarkMode = false): string => {
  const flexokiColors = getFlexokiFinancialColors(isDarkMode);
  
  if (value > 0) {
    return isDarkMode ? 'text-green-400' : 'text-green-600';
  } else if (value < 0) {
    return isDarkMode ? 'text-red-400' : 'text-red-600';
  }
  return isDarkMode ? 'text-base-400' : 'text-base-600';
};

/**
 * Get a set of colors for a collection of tasks
 * This ensures each task gets a unique color when possible
 */
export const getTaskColorMap = (
  tasks: Array<{ taskId: number; taskName: string; isBillable: boolean }>
): Record<number, string> => {
  const colorMap: Record<number, string> = {};
  const usedColors = new Set<string>();

  // Common task name to color mapping for consistency
  const commonTaskColors: Record<string, string> = {
    'Billable': taskColors[0],
    'Public Holiday': taskColors[1],
    'Annual Leave': taskColors[2],
    'Other Unpaid Leave': taskColors[3],
    'Salesforce': taskColors[4],
    'Interviews': taskColors[5],
  };

  // First, assign colors to billable tasks (all blue)
  tasks.filter(t => t.isBillable).forEach(task => {
    colorMap[task.taskId] = taskColors[0];
  });

  // Then, assign unique colors to non-billable tasks
  const nonBillableTasks = tasks.filter(t => !t.isBillable);

  // Sort by task ID to ensure consistent color assignment
  nonBillableTasks.sort((a, b) => a.taskId - b.taskId);

  nonBillableTasks.forEach(task => {
    // First check if this is a common task with a predefined color
    if (commonTaskColors[task.taskName]) {
      colorMap[task.taskId] = commonTaskColors[task.taskName];
      usedColors.add(commonTaskColors[task.taskName]);
      return;
    }

    // Try to get a color based on task ID
    let colorIndex = (task.taskId % (taskColors.length - 1)) + 1; // +1 to skip billable color
    let color = taskColors[colorIndex];

    // If this color is already used, find another one
    if (usedColors.has(color)) {
      // Find the first unused color
      for (let i = 1; i < taskColors.length; i++) {
        if (!usedColors.has(taskColors[i])) {
          color = taskColors[i];
          break;
        }
      }

      // If all colors are used, fall back to the original algorithm
      if (usedColors.has(color)) {
        color = taskColors[colorIndex];
      }
    }

    colorMap[task.taskId] = color;
    usedColors.add(color);
  });

  return colorMap;
};

/**
 * Company color palette (hex format for inline styles)
 * Using Flexoki colors for consistency
 */
export const companyColors = [
  flexokiPalette.blue[600],
  flexokiPalette.green[600],
  flexokiPalette.purple[600],
  flexokiPalette.orange[600],
  flexokiPalette.magenta[600],
  flexokiPalette.cyan[600],
  flexokiPalette.red[600],
  flexokiPalette.yellow[600],
];

/**
 * Generate consistent colors for a company based on its name
 * Returns an object with backgroundColor and other color properties
 */
export const generateCompanyColors = (companyName: string): { backgroundColor: string } => {
  // Simple hash function to get consistent color for the same company name
  const hash = companyName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const backgroundColor = companyColors[hash % companyColors.length];

  return {
    backgroundColor
  };
};