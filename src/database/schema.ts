/**
 * Database schema initialization
 *
 * This file contains the functions to initialize the database schema.
 */

import BetterSqlite3 from 'better-sqlite3';

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;
import { createFinancialTables } from './schema/financial';
import { createEstimateTables } from './schema/estimates';
import { createCRMTables } from './schema/crm';
import { createAuditTables } from './schema/audit';
import { createIntegrationTables } from './schema/integration';

/**
 * Initialize the schema version table
 * @param db Database instance
 */
export function initializeSchemaVersionTable(db: Database): void {
  db.prepare(`
    CREATE TABLE IF NOT EXISTS schema_version (
      version INTEGER PRIMARY KEY,
      applied_at TEXT NOT NULL,
      description TEXT NOT NULL
    )
  `).run();
}

/**
 * Initialize the complete database schema
 * @param db Database instance
 */
export function initializeSchema(db: Database): void {
  console.log('Initializing complete database schema...');

  // Create schema version table first
  initializeSchemaVersionTable(db);

  // Create domain-specific tables
  createFinancialTables(db);
  createEstimateTables(db);
  createCRMTables(db);
  createAuditTables(db);
  createIntegrationTables(db);

  console.log('Schema initialization complete');
}
