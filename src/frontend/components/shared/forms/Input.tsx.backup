import React, { forwardRef } from "react";
import {
  cn,
  inputVariants,
  InputVariantProps,
} from "../../../utils/component-variants";

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size">,
    InputVariantProps {
  label?: string;
  error?: string;
  helpText?: string;
  icon?: React.ReactNode;
  required?: boolean;
  loading?: boolean;
}

/**
 * Modern Input component with variant system
 *
 * Features:
 * - Type-safe variants (default, error, success)
 * - Multiple sizes (sm, default, lg)
 * - Icon support with proper spacing
 * - Error states with validation styling
 * - Loading states
 * - Accessibility features (ARIA labels, error announcements)
 * - Dark mode support
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant = "default",
      size = "default",
      withIcon = false,
      withError = false,
      label,
      error,
      helpText,
      icon,
      required = false,
      loading = false,
      id,
      type,
      ...props
    },
    ref
  ) => {
    // Generate unique ID if not provided
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    // Determine variant based on error state
    const effectiveVariant = error ? "error" : variant;
    const effectiveWithError = !!error;
    const effectiveWithIcon = !!icon;

    // Determine inputMode for mobile keyboards
    const inputMode = type === 'number' ? 'numeric' : 
                     type === 'email' ? 'email' : 
                     type === 'tel' ? 'tel' : 
                     type === 'url' ? 'url' :
                     type === 'search' ? 'search' : undefined;

    return (
      <div className="form-field">
        {/* Input container with floating label */}
        <div className="input-group relative">
          {/* Icon */}
          {icon && <div className="form-input-icon">{icon}</div>}

          {/* Input field with modern styling */}
          <input
            ref={ref}
            id={inputId}
            type={type}
            inputMode={inputMode}
            placeholder=" "
            className={cn(
              "input-modern",
              effectiveVariant === "error" && "input-modern--error",
              effectiveVariant === "success" && "input-modern--success",
              loading && "opacity-50 cursor-not-allowed",
              effectiveWithIcon && "pl-10",
              effectiveWithError && "pr-10",
              "focus-smooth spring-smooth",
              className
            )}
            aria-invalid={error ? "true" : "false"}
            aria-describedby={
              error
                ? `${inputId}-error`
                : helpText
                ? `${inputId}-help`
                : undefined
            }
            disabled={loading || props.disabled}
            {...props}
          />

          {/* Floating label */}
          {label && (
            <label htmlFor={inputId} className="input-label">
              {label}
              {required && (
                <span className="text-error-500 ml-1" aria-label="required">
                  *
                </span>
              )}
            </label>
          )}

          {/* Error icon */}
          {error && (
            <div className="form-error-icon">
              <svg
                className="h-5 w-5 text-accent-500"
                fill="currentColor"
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          )}

          {/* Loading spinner */}
          {loading && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <span className="spinner" aria-hidden="true" />
            </div>
          )}
        </div>

        {/* Error message */}
        {error && (
          <div
            id={`${inputId}-error`}
            className="form-error"
            role="alert"
            aria-live="polite"
          >
            {error}
          </div>
        )}

        {/* Help text */}
        {helpText && !error && (
          <div id={`${inputId}-help`} className="form-help">
            {helpText}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
