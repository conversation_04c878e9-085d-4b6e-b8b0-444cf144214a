/**
 * Note Repository Test
 * 
 * Tests for the note repository focusing on business behavior of notes and comments.
 * Uses an in-memory SQLite database for realistic testing without mocks.
 */
import NoteRepository from '../../../../src/api/repositories/note-repository';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

describe('NoteRepository - Business Behavior', () => {
  let repository: NoteRepository;
  let db: Database.Database;
  
  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Initialize schema using the unified migration
    const migrationPath = path.join(__dirname, '../../../../migrations/000_unified_schema.sql');
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    } else {
      throw new Error(`Migration file not found at ${migrationPath}`);
    }
  });
  
  beforeEach(() => {
    repository = new NoteRepository();
    
    // Clear data before each test
    db.prepare('DELETE FROM note').run();
    db.prepare('DELETE FROM company').run();
    db.prepare('DELETE FROM contact').run();
    db.prepare('DELETE FROM deal').run();
    
    // Insert test data
    db.prepare(`
      INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
      VALUES ('company-1', 'Test Company', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO contact (id, first_name, last_name, email, created_at, updated_at, created_by, updated_by)
      VALUES ('contact-1', 'John', 'Doe', '<EMAIL>', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO deal (id, deal_name, company_id, stage, amount, created_at, updated_at, created_by, updated_by)
      VALUES ('deal-1', 'Test Deal', 'company-1', 'negotiation', 50000, datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
  });
  
  afterAll(() => {
    db.close();
  });

  describe('Business Rule: Note Creation and Management', () => {
    it('should create notes for deals to track interactions', () => {
      const noteData = {
        target_type: 'deal',
        target_id: 'deal-1',
        content: 'Had initial call with client. They are interested in our premium package.',
        is_private: false,
        created_by: 'sales-user'
      };
      
      const result = repository.createNote(noteData);
      
      // Business expectation: Sales activities are documented
      expect(result.id).toBeDefined();
      expect(result.content).toBe(noteData.content);
      expect(result.target_type).toBe('deal');
      expect(result.target_id).toBe('deal-1');
      expect(result.created_by).toBe('sales-user');
    });

    it('should create private notes for sensitive information', () => {
      const privateNote = {
        target_type: 'company',
        target_id: 'company-1',
        content: 'Financial situation looks unstable. Proceed with caution.',
        is_private: true,
        created_by: 'manager-user'
      };
      
      const result = repository.createNote(privateNote);
      
      // Business requirement: Sensitive information can be kept private
      expect(result.is_private).toBe(true);
      expect(result.content).toContain('Financial situation');
    });

    it('should support notes on different entity types', () => {
      const entityTypes = [
        { type: 'company', id: 'company-1', content: 'Company evaluation notes' },
        { type: 'contact', id: 'contact-1', content: 'Contact preferences' },
        { type: 'deal', id: 'deal-1', content: 'Deal negotiation points' }
      ];
      
      const notes = entityTypes.map(entity => 
        repository.createNote({
          target_type: entity.type,
          target_id: entity.id,
          content: entity.content,
          is_private: false,
          created_by: 'test-user'
        })
      );
      
      // Business requirement: Any CRM entity can have notes
      expect(notes).toHaveLength(3);
      expect(notes.map(n => n.target_type)).toEqual(['company', 'contact', 'deal']);
    });
  });

  describe('Business Rule: Note Retrieval', () => {
    beforeEach(() => {
      // Create test notes
      const testNotes = [
        {
          target_type: 'deal',
          target_id: 'deal-1',
          content: 'Initial contact made',
          is_private: false,
          created_by: 'user-1'
        },
        {
          target_type: 'deal',
          target_id: 'deal-1',
          content: 'Follow-up call scheduled',
          is_private: false,
          created_by: 'user-2'
        },
        {
          target_type: 'deal',
          target_id: 'deal-1',
          content: 'Budget concerns raised privately',
          is_private: true,
          created_by: 'manager-1'
        }
      ];
      
      testNotes.forEach(note => repository.createNote(note));
    });

    it('should retrieve all notes for a specific entity', () => {
      const dealNotes = repository.getNotesByTarget('deal', 'deal-1');
      
      // Business requirement: View complete interaction history
      expect(dealNotes.length).toBeGreaterThanOrEqual(3);
      expect(dealNotes.every(n => n.target_id === 'deal-1')).toBe(true);
    });

    it('should retrieve notes in chronological order', () => {
      const notes = repository.getNotesByTarget('deal', 'deal-1');
      
      // Business requirement: See interaction timeline
      for (let i = 1; i < notes.length; i++) {
        const prevTime = new Date(notes[i-1].created_at).getTime();
        const currTime = new Date(notes[i].created_at).getTime();
        expect(currTime).toBeGreaterThanOrEqual(prevTime);
      }
    });

    it('should filter private notes based on permissions', () => {
      // This would typically be handled at the service layer
      const allNotes = repository.getNotesByTarget('deal', 'deal-1');
      const publicNotes = allNotes.filter(n => !n.is_private);
      const privateNotes = allNotes.filter(n => n.is_private);
      
      // Business requirement: Control access to sensitive information
      expect(publicNotes.length).toBe(2);
      expect(privateNotes.length).toBe(1);
      expect(privateNotes[0].content).toContain('Budget concerns');
    });
  });

  describe('Business Rule: Note Updates', () => {
    let testNote: any;
    
    beforeEach(() => {
      testNote = repository.createNote({
        target_type: 'contact',
        target_id: 'contact-1',
        content: 'Initial contact notes',
        is_private: false,
        created_by: 'user-1'
      });
    });

    it('should update note content when information changes', () => {
      const updated = repository.updateNote(testNote.id, {
        content: 'Initial contact notes\n\nUPDATE: Preferred communication is email, not phone.'
      }, 'user-1');
      
      // Business requirement: Keep notes current and accurate
      expect(updated.content).toContain('UPDATE:');
      expect(updated.content).toContain('email, not phone');
      expect(new Date(updated.updated_at).getTime()).toBeGreaterThan(
        new Date(testNote.updated_at).getTime()
      );
    });

    it('should track who updates notes', () => {
      const updated = repository.updateNote(testNote.id, {
        content: 'Updated information'
      }, 'different-user');
      
      // Business requirement: Audit trail for changes
      expect(updated.updated_by).toBe('different-user');
      expect(updated.created_by).toBe('user-1'); // Original creator unchanged
    });

    it('should allow changing privacy settings', () => {
      const updated = repository.updateNote(testNote.id, {
        is_private: true
      }, 'manager-1');
      
      // Business requirement: Reclassify information as needed
      expect(updated.is_private).toBe(true);
    });
  });

  describe('Business Rule: Threaded Conversations', () => {
    it('should support replies to create conversation threads', () => {
      const originalNote = repository.createNote({
        target_type: 'deal',
        target_id: 'deal-1',
        content: 'Question: What is their budget range?',
        is_private: false,
        created_by: 'sales-1'
      });
      
      const reply = repository.createNote({
        target_type: 'deal',
        target_id: 'deal-1',
        content: 'Response: Budget is $50-75k based on initial discussion',
        is_private: false,
        created_by: 'sales-2',
        parent_id: originalNote.id
      });
      
      // Business requirement: Support team collaboration
      expect(reply.parent_id).toBe(originalNote.id);
      
      // Retrieve thread
      const thread = db.prepare(`
        SELECT * FROM note 
        WHERE id = ? OR parent_id = ? 
        ORDER BY created_at
      `).all(originalNote.id, originalNote.id);
      
      expect(thread).toHaveLength(2);
    });
  });

  describe('Business Rule: Note Deletion', () => {
    it('should soft delete notes to maintain audit trail', () => {
      const note = repository.createNote({
        target_type: 'company',
        target_id: 'company-1',
        content: 'To be deleted',
        is_private: false,
        created_by: 'user-1'
      });
      
      repository.deleteNote(note.id, 'user-1');
      
      // Business requirement: Maintain history even for deleted content
      const deletedNote = db.prepare('SELECT * FROM note WHERE id = ?').get(note.id) as any;
      expect(deletedNote.deleted_at).not.toBeNull();
      
      // Should not appear in normal queries
      const activeNotes = repository.getNotesByTarget('company', 'company-1');
      expect(activeNotes.find(n => n.id === note.id)).toBeUndefined();
    });
  });

  describe('Business Rule: Note Search and Filtering', () => {
    beforeEach(() => {
      // Create various notes for searching
      const notes = [
        { content: 'Discussed pricing options', target_type: 'deal', target_id: 'deal-1' },
        { content: 'Technical requirements gathered', target_type: 'deal', target_id: 'deal-1' },
        { content: 'Pricing seems too high for them', target_type: 'company', target_id: 'company-1' },
        { content: 'Very technical team', target_type: 'contact', target_id: 'contact-1' }
      ];
      
      notes.forEach(n => repository.createNote({
        ...n,
        is_private: false,
        created_by: 'test-user'
      }));
    });

    it('should search notes by content keywords', () => {
      const searchResults = repository.searchNotes('pricing');
      
      // Business requirement: Find relevant information quickly
      expect(searchResults.length).toBe(2);
      expect(searchResults.every(n => n.content.toLowerCase().includes('pricing'))).toBe(true);
    });

    it('should filter notes by date range', () => {
      const today = new Date().toISOString().split('T')[0];
      const recentNotes = repository.getNotesByDateRange(today, today);
      
      // Business requirement: Review recent activities
      expect(recentNotes.length).toBeGreaterThan(0);
      expect(recentNotes.every(n => n.created_at.startsWith(today))).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should validate note content is not empty', () => {
      // Business expectation: Notes must have meaningful content
      expect(() => repository.createNote({
        target_type: 'deal',
        target_id: 'deal-1',
        content: '',
        is_private: false,
        created_by: 'user-1'
      })).toThrow();
    });

    it('should validate target entity exists', () => {
      // Business expectation: Notes must be attached to valid entities
      expect(() => repository.createNote({
        target_type: 'deal',
        target_id: 'non-existent',
        content: 'Test note',
        is_private: false,
        created_by: 'user-1'
      })).toThrow(/constraint/i);
    });
  });
});