import React from "react";
import { Badge } from './Badge';
import type { BadgeVariantProps } from '../../utils/component-variants';

interface AccruedBadgeProps extends Partial<BadgeVariantProps> {
  className?: string;
  quarterInfo?: {
    quarterName: string;
    quarterLabel: string;
    percentComplete: number;
  };
}

/**
 * Clock icon component for accrued badges
 */
const ClockIcon: React.FC<{ className?: string }> = ({ className = "w-3 h-3" }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    aria-hidden="true"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);

/**
 * Accrued badge component with enhanced styling and accessibility.
 * 
 * Features:
 * - Uses design system accrued variant  
 * - Consistent icon and spacing
 * - Enhanced accessibility with proper ARIA attributes
 * - Optional quarter information display
 * - Semantic purple color scheme
 * 
 * @example
 * ```tsx
 * <AccruedBadge 
 *   quarterInfo={{
 *     quarterName: "Q2",
 *     quarterLabel: "Q2 2024", 
 *     percentComplete: 75
 *   }} 
 * />
 * ```
 */
const AccruedBadge: React.FC<AccruedBadgeProps> = ({
  className,
  quarterInfo,
  size = 'default',
  ...props
}) => {
  // Format quarter text if provided
  const quarterText = quarterInfo
    ? `${Math.round(quarterInfo.percentComplete)}% | ${quarterInfo.quarterLabel}`
    : "";

  const badgeText = `Accrued${quarterInfo ? ` | ${quarterText}` : ""}`;

  return (
    <Badge
      variant="accrued"
      size={size}
      withIcon={true}
      icon={<ClockIcon />}
      title="Using accrued amount (current GST liability)"
      aria-label={`Accrued amount${quarterInfo ? ` - ${quarterText}` : ""}`}
      className={className}
      {...props}
    >
      {badgeText}
    </Badge>
  );
};

export default AccruedBadge;