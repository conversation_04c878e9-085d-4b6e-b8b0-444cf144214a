import React from 'react';

interface ImportResult {
  success: boolean;
  count: number;
  errors: Array<{ item: string; error: string }>;
  updates: Array<{ item: string; changes: string[] }>;
  created: Array<{ item: string }>;
  deleted?: Array<{ item: string }>;
  error?: string;
}

interface ImportSummaryProps {
  results: {
    companies: ImportResult;
    deals: ImportResult;
    contacts: ImportResult;
    notes?: ImportResult;
    associations?: ImportResult;
  };
  totalCount: number;
  onClose?: () => void;
  onRetry?: () => void;
}

const ImportSummary: React.FC<ImportSummaryProps> = ({ results, totalCount, onClose, onRetry }) => {
  const totalErrors = results.companies.errors.length + 
                     results.deals.errors.length + 
                     results.contacts.errors.length +
                     (results.notes?.errors.length || 0) +
                     (results.associations?.errors.length || 0);
  
  const totalSuccessful = results.companies.count + 
                         results.deals.count + 
                         results.contacts.count +
                         (results.notes?.count || 0) +
                         (results.associations?.count || 0);
  
  const totalAttempted = totalSuccessful + totalErrors;
  
  const overallSuccess = results.companies.success && 
                        results.deals.success && 
                        results.contacts.success &&
                        (results.notes?.success ?? true) &&
                        (results.associations?.success ?? true);

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    ) : (
      <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
    );
  };

  return (
    <div className="space-y-4">
      {/* Overall Summary */}
      <div className={`
        p-4 rounded-lg border
        ${overallSuccess && totalErrors === 0
          ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
          : totalErrors > 0
            ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
        }
      `}>
        <div className="flex items-center space-x-3">
          {getStatusIcon(overallSuccess && totalErrors === 0)}
          <div>
            <h3 className={`
              text-lg font-medium
              ${overallSuccess && totalErrors === 0
                ? 'text-green-800 dark:text-green-200'
                : totalErrors > 0
                  ? 'text-yellow-800 dark:text-yellow-200'
                  : 'text-red-800 dark:text-red-200'
              }
            `}>
              {overallSuccess && totalErrors === 0
                ? 'Import Completed Successfully!'
                : totalErrors > 0
                  ? 'Import Completed with Issues'
                  : 'Import Failed'
              }
            </h3>
            <p className={`
              text-sm mt-1
              ${overallSuccess && totalErrors === 0
                ? 'text-green-700 dark:text-green-300'
                : totalErrors > 0
                  ? 'text-yellow-700 dark:text-yellow-300'
                  : 'text-red-700 dark:text-red-300'
              }
            `}>
              {totalErrors === 0
                ? `All ${totalSuccessful} items imported successfully`
                : `${totalSuccessful} of ${totalAttempted} items imported successfully, ${totalErrors} failed`
              }
            </p>
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {/* Companies */}
        <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Companies</h4>
            {getStatusIcon(results.companies.success)}
          </div>
          <div className="space-y-1">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {results.companies.count}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              total processed
            </p>
            <div className="mt-2 space-y-1">
              {results.companies.created.length > 0 && (
                <p className="text-xs text-green-600 dark:text-green-400">
                  ✓ {results.companies.created.length} created
                </p>
              )}
              {results.companies.updates.length > 0 && (
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  ↻ {results.companies.updates.length} updated
                </p>
              )}
              {results.companies.deleted && results.companies.deleted.length > 0 && (
                <p className="text-xs text-orange-600 dark:text-orange-400">
                  🗑 {results.companies.deleted.length} deleted
                </p>
              )}
              {results.companies.errors.length > 0 && (
                <p className="text-xs text-red-600 dark:text-red-400">
                  ✗ {results.companies.errors.length} failed
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Deals */}
        <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Deals</h4>
            {getStatusIcon(results.deals.success)}
          </div>
          <div className="space-y-1">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {results.deals.count}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              total processed
            </p>
            <div className="mt-2 space-y-1">
              {results.deals.created.length > 0 && (
                <p className="text-xs text-green-600 dark:text-green-400">
                  ✓ {results.deals.created.length} created
                </p>
              )}
              {results.deals.updates.length > 0 && (
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  ↻ {results.deals.updates.length} updated
                </p>
              )}
              {results.deals.deleted && results.deals.deleted.length > 0 && (
                <p className="text-xs text-orange-600 dark:text-orange-400">
                  🗑 {results.deals.deleted.length} deleted
                </p>
              )}
              {results.deals.errors.length > 0 && (
                <p className="text-xs text-red-600 dark:text-red-400">
                  ✗ {results.deals.errors.length} failed
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Contacts */}
        <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Contacts</h4>
            {getStatusIcon(results.contacts.success)}
          </div>
          <div className="space-y-1">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {results.contacts.count}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              total processed
            </p>
            <div className="mt-2 space-y-1">
              {results.contacts.created.length > 0 && (
                <p className="text-xs text-green-600 dark:text-green-400">
                  ✓ {results.contacts.created.length} created
                </p>
              )}
              {results.contacts.updates.length > 0 && (
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  ↻ {results.contacts.updates.length} updated
                </p>
              )}
              {results.contacts.deleted && results.contacts.deleted.length > 0 && (
                <p className="text-xs text-orange-600 dark:text-orange-400">
                  🗑 {results.contacts.deleted.length} deleted
                </p>
              )}
              {results.contacts.errors.length > 0 && (
                <p className="text-xs text-red-600 dark:text-red-400">
                  ✗ {results.contacts.errors.length} failed
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Notes/Activities */}
        {results.notes && (
          <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Notes/Activities</h4>
              {getStatusIcon(results.notes.success)}
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {results.notes.count}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                total processed
              </p>
              <div className="mt-2 space-y-1">
                {results.notes.created.length > 0 && (
                  <p className="text-xs text-green-600 dark:text-green-400">
                    ✓ {results.notes.created.length} created
                  </p>
                )}
                {results.notes.updates.length > 0 && (
                  <p className="text-xs text-blue-600 dark:text-blue-400">
                    ↻ {results.notes.updates.length} updated
                  </p>
                )}
                {results.notes.errors.length > 0 && (
                  <p className="text-xs text-red-600 dark:text-red-400">
                    ✗ {results.notes.errors.length} failed
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Associations */}
        {results.associations && (
          <div className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Associations</h4>
              {getStatusIcon(results.associations.success)}
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {results.associations.count}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                total processed
              </p>
              <div className="mt-2 space-y-1">
                {results.associations.created.length > 0 && (
                  <p className="text-xs text-green-600 dark:text-green-400">
                    ✓ {results.associations.created.length} created
                  </p>
                )}
                {results.associations.updates.length > 0 && (
                  <p className="text-xs text-blue-600 dark:text-blue-400">
                    ↻ {results.associations.updates.length} updated
                  </p>
                )}
                {results.associations.errors.length > 0 && (
                  <p className="text-xs text-red-600 dark:text-red-400">
                    ✗ {results.associations.errors.length} failed
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Error Details */}
      {totalErrors > 0 && (
        <div className={`rounded-lg p-4 ${
          overallSuccess 
            ? 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
            : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
        }`}>
          <div className="flex items-center justify-between mb-3">
            <h4 className={`text-sm font-medium ${
              overallSuccess
                ? 'text-yellow-800 dark:text-yellow-200'
                : 'text-red-800 dark:text-red-200'
            }`}>
              {overallSuccess ? `Exceptions (${totalErrors})` : `Failed Items (${totalErrors})`}
            </h4>
            <button
              className={`text-xs hover:underline ${
                overallSuccess
                  ? 'text-yellow-700 dark:text-yellow-300 hover:text-yellow-800 dark:hover:text-yellow-200'
                  : 'text-red-700 dark:text-red-300 hover:text-red-800 dark:hover:text-red-200'
              }`}
              onClick={() => {
                const errors = [
                  ...results.companies.errors.map(e => ({ ...e, type: 'Company' })),
                  ...results.deals.errors.map(e => ({ ...e, type: 'Deal' })),
                  ...results.contacts.errors.map(e => ({ ...e, type: 'Contact' })),
                  ...(results.notes?.errors.map(e => ({ ...e, type: 'Note' })) || []),
                  ...(results.associations?.errors.map(e => ({ ...e, type: 'Association' })) || [])
                ];
                const errorSummary = errors.reduce((acc, error) => {
                  const key = error.error;
                  if (!acc[key]) {
                    acc[key] = { error: key, count: 0, examples: [] };
                  }
                  acc[key].count++;
                  if (acc[key].examples.length < 3) {
                    acc[key].examples.push(`${error.type}: ${error.item}`);
                  }
                  return acc;
                }, {} as Record<string, { error: string; count: number; examples: string[] }>);
                
                console.log('Error Summary:', errorSummary);
              }}
            >
              View Error Summary
            </button>
          </div>
          
          {/* Group errors by common reasons */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {(() => {
              // Analyze errors to find common patterns
              const errorPatterns = new Map<string, { items: string[]; type: string }[]>();
              
              // Process all errors
              const allErrors = [
                ...results.companies.errors.map(e => ({ ...e, type: 'Company' })),
                ...results.deals.errors.map(e => ({ ...e, type: 'Deal' })),
                ...results.contacts.errors.map(e => ({ ...e, type: 'Contact' })),
                ...(results.notes?.errors.map(e => ({ ...e, type: 'Note' })) || []),
                ...(results.associations?.errors.map(e => ({ ...e, type: 'Association' })) || [])
              ];
              
              allErrors.forEach(error => {
                // Simplify error message for grouping
                let errorGroup = error.error;
                if (error.error.includes('duplicate')) {
                  errorGroup = 'Duplicate record';
                } else if (error.error.includes('missing') || error.error.includes('required')) {
                  errorGroup = 'Missing required fields';
                } else if (error.error.includes('invalid') || error.error.includes('format')) {
                  errorGroup = 'Invalid data format';
                } else if (error.error.includes('not found')) {
                  errorGroup = 'Referenced record not found';
                }
                
                if (!errorPatterns.has(errorGroup)) {
                  errorPatterns.set(errorGroup, []);
                }
                
                const existing = errorPatterns.get(errorGroup)!.find(g => g.type === error.type);
                if (existing) {
                  existing.items.push(error.item);
                } else {
                  errorPatterns.get(errorGroup)!.push({ type: error.type, items: [error.item] });
                }
              });
              
              // Display grouped errors
              return Array.from(errorPatterns.entries()).map(([errorGroup, groups], index) => {
                const totalForError = groups.reduce((sum, g) => sum + g.items.length, 0);
                
                return (
                  <details key={index} className="group">
                    <summary className={`cursor-pointer text-sm ${
                      overallSuccess
                        ? 'text-yellow-700 dark:text-yellow-300 hover:text-yellow-800 dark:hover:text-yellow-200'
                        : 'text-red-700 dark:text-red-300 hover:text-red-800 dark:hover:text-red-200'
                    }`}>
                      <span className="font-medium">{errorGroup}</span>
                      <span className={`ml-2 text-xs ${
                        overallSuccess ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        ({totalForError} {totalForError === 1 ? 'item' : 'items'})
                      </span>
                    </summary>
                    <div className="mt-2 ml-4 space-y-2">
                      {groups.map((group, gIndex) => (
                        <div key={gIndex}>
                          <p className={`text-xs font-medium ${
                            overallSuccess ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                          }`}>
                            {group.type}s ({group.items.length}):
                          </p>
                          <ul className={`ml-2 text-xs max-h-32 overflow-y-auto ${
                            overallSuccess ? 'text-yellow-500 dark:text-yellow-500' : 'text-red-500 dark:text-red-500'
                          }`}>
                            {group.items.map((item, iIndex) => (
                              <li key={iIndex}>• {item}</li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </details>
                );
              });
            })()}
          </div>
          
          {/* Helpful tips */}
          <div className={`mt-3 pt-3 border-t ${
            overallSuccess ? 'border-yellow-200 dark:border-yellow-800' : 'border-red-200 dark:border-red-800'
          }`}>
            <p className={`text-xs font-medium mb-1 ${
              overallSuccess ? 'text-yellow-700 dark:text-yellow-300' : 'text-red-700 dark:text-red-300'
            }`}>
              {overallSuccess ? 'Note: Most items imported successfully. For the exceptions:' : 'Common solutions:'}
            </p>
            <ul className={`text-xs space-y-1 ${
              overallSuccess ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
            }`}>
              <li>• <strong>Duplicates:</strong> Check if records already exist in the system</li>
              <li>• <strong>Missing fields:</strong> Ensure all required fields are filled in HubSpot</li>
              <li>• <strong>Invalid format:</strong> Verify email addresses and phone numbers are correctly formatted</li>
              {overallSuccess && (
                <li className="mt-2 text-yellow-700 dark:text-yellow-300">
                  • These exceptions don't affect the successfully imported items
                </li>
              )}
            </ul>
          </div>
        </div>
      )}

      {/* Created Records Details */}
      {(() => {
        const hasCreatedRecords = 
          results.companies.created.length > 0 ||
          results.deals.created.length > 0 ||
          results.contacts.created.length > 0 ||
          (results.notes?.created.length || 0) > 0 ||
          (results.associations?.created.length || 0) > 0;
          
        if (!hasCreatedRecords) return null;
        
        return (
          <div className="mt-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-green-800 dark:text-green-200 mb-3">
              Created Records
            </h4>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {/* Companies Created */}
              {results.companies.created.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200">
                    <span className="font-medium">Companies</span>
                    <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                      ({results.companies.created.length} created)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.companies.created.map((creation, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {creation.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}

              {/* Deals Created */}
              {results.deals.created.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200">
                    <span className="font-medium">Deals</span>
                    <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                      ({results.deals.created.length} created)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.deals.created.map((creation, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {creation.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}

              {/* Contacts Created */}
              {results.contacts.created.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200">
                    <span className="font-medium">Contacts</span>
                    <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                      ({results.contacts.created.length} created)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.contacts.created.map((creation, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {creation.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}

              {/* Notes Created */}
              {results.notes && results.notes.created.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200">
                    <span className="font-medium">Notes/Activities</span>
                    <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                      ({results.notes.created.length} created)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.notes.created.map((creation, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {creation.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}

              {/* Associations Created */}
              {results.associations && results.associations.created.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200">
                    <span className="font-medium">Associations</span>
                    <span className="ml-2 text-xs text-green-600 dark:text-green-400">
                      ({results.associations.created.length} created)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.associations.created.map((creation, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {creation.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}
            </div>
          </div>
        );
      })()}

      {/* Update Details */}
      {(() => {
        const hasRealUpdates = 
          results.companies.updates.filter(u => !u.changes.some(c => c.includes('Deleted - no longer exists in HubSpot'))).length > 0 ||
          results.deals.updates.filter(u => !u.changes.some(c => c.includes('Deleted - no longer exists in HubSpot'))).length > 0 ||
          results.contacts.updates.filter(u => !u.changes.some(c => c.includes('Deleted - no longer exists in HubSpot'))).length > 0 ||
          (results.notes?.updates.length || 0) > 0 ||
          (results.associations?.updates.length || 0) > 0;
          
        if (!hasRealUpdates) return null;
        
        return (
        <div className="mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">
            Updated Records
          </h4>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {/* Companies Updates */}
            {results.companies.updates.length > 0 && (
              <details className="group">
                <summary className="cursor-pointer text-sm text-blue-700 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-200">
                  <span className="font-medium">Companies</span>
                  <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                    ({results.companies.updates.length} updated)
                  </span>
                </summary>
                <div className="mt-2 ml-4 space-y-2 max-h-48 overflow-y-auto">
                  {results.companies.updates
                    .filter(update => !update.changes.some(change => change.includes('Deleted - no longer exists in HubSpot')))
                    .map((update, index) => (
                      <div key={index} className="text-xs">
                        <div className="font-medium text-gray-700 dark:text-gray-300">{update.item}</div>
                        <ul className="ml-2 text-gray-500 dark:text-gray-400">
                          {update.changes.map((change, cIndex) => (
                            <li key={cIndex}>• {change}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                </div>
              </details>
            )}

            {/* Deals Updates */}
            {results.deals.updates.length > 0 && (
              <details className="group">
                <summary className="cursor-pointer text-sm text-blue-700 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-200">
                  <span className="font-medium">Deals</span>
                  <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                    ({results.deals.updates.filter(update => !update.changes.some(change => change.includes('Deleted - no longer exists in HubSpot'))).length} updated)
                  </span>
                </summary>
                <div className="mt-2 ml-4 space-y-2 max-h-48 overflow-y-auto">
                  {results.deals.updates
                    .filter(update => !update.changes.some(change => change.includes('Deleted - no longer exists in HubSpot')))
                    .map((update, index) => (
                      <div key={index} className="text-xs">
                        <div className="font-medium text-gray-700 dark:text-gray-300">{update.item}</div>
                        <ul className="ml-2 text-gray-500 dark:text-gray-400">
                          {update.changes.map((change, cIndex) => (
                            <li key={cIndex}>• {change}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                </div>
              </details>
            )}

            {/* Contacts Updates */}
            {results.contacts.updates.length > 0 && (
              <details className="group">
                <summary className="cursor-pointer text-sm text-blue-700 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-200">
                  <span className="font-medium">Contacts</span>
                  <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                    ({results.contacts.updates.length} updated)
                  </span>
                </summary>
                <div className="mt-2 ml-4 space-y-2 max-h-48 overflow-y-auto">
                  {results.contacts.updates
                    .filter(update => !update.changes.some(change => change.includes('Deleted - no longer exists in HubSpot')))
                    .map((update, index) => (
                      <div key={index} className="text-xs">
                        <div className="font-medium text-gray-700 dark:text-gray-300">{update.item}</div>
                        <ul className="ml-2 text-gray-500 dark:text-gray-400">
                          {update.changes.map((change, cIndex) => (
                            <li key={cIndex}>• {change}</li>
                          ))}
                        </ul>
                      </div>
                    ))}
                </div>
              </details>
            )}

            {/* Notes Updates */}
            {results.notes && results.notes.updates.length > 0 && (
              <details className="group">
                <summary className="cursor-pointer text-sm text-blue-700 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-200">
                  <span className="font-medium">Notes/Activities</span>
                  <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                    ({results.notes.updates.length} updated)
                  </span>
                </summary>
                <div className="mt-2 ml-4 space-y-2 max-h-48 overflow-y-auto">
                  {results.notes.updates.map((update, index) => (
                    <div key={index} className="text-xs">
                      <div className="font-medium text-gray-700 dark:text-gray-300">{update.item}</div>
                      <ul className="ml-2 text-gray-500 dark:text-gray-400">
                        {update.changes.map((change, cIndex) => (
                          <li key={cIndex}>• {change}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </details>
            )}

            {/* Associations Updates */}
            {results.associations && results.associations.updates.length > 0 && (
              <details className="group">
                <summary className="cursor-pointer text-sm text-blue-700 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-200">
                  <span className="font-medium">Associations</span>
                  <span className="ml-2 text-xs text-blue-600 dark:text-blue-400">
                    ({results.associations.updates.length} updated)
                  </span>
                </summary>
                <div className="mt-2 ml-4 space-y-2 max-h-48 overflow-y-auto">
                  {results.associations.updates.map((update, index) => (
                    <div key={index} className="text-xs">
                      <div className="font-medium text-gray-700 dark:text-gray-300">{update.item}</div>
                      <ul className="ml-2 text-gray-500 dark:text-gray-400">
                        {update.changes.map((change, cIndex) => (
                          <li key={cIndex}>• {change}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </details>
            )}
          </div>
        </div>
        );
      })()}

      {/* Deleted Records Details */}
      {(() => {
        const hasDeletedRecords = 
          (results.companies.deleted?.length || 0) > 0 ||
          (results.deals.deleted?.length || 0) > 0 ||
          (results.contacts.deleted?.length || 0) > 0;
          
        if (!hasDeletedRecords) return null;
        
        return (
          <div className="mt-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
            <h4 className="text-sm font-medium text-orange-800 dark:text-orange-200 mb-3">
              Deleted Records (No Longer in HubSpot)
            </h4>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {/* Companies Deletions */}
              {results.companies.deleted && results.companies.deleted.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-orange-700 dark:text-orange-300 hover:text-orange-800 dark:hover:text-orange-200">
                    <span className="font-medium">Companies</span>
                    <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                      ({results.companies.deleted.length} deleted)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.companies.deleted.map((deletion, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {deletion.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}

              {/* Deals Deletions */}
              {results.deals.deleted && results.deals.deleted.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-orange-700 dark:text-orange-300 hover:text-orange-800 dark:hover:text-orange-200">
                    <span className="font-medium">Deals</span>
                    <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                      ({results.deals.deleted.length} deleted)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.deals.deleted.map((deletion, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {deletion.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}

              {/* Contacts Deletions */}
              {results.contacts.deleted && results.contacts.deleted.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-orange-700 dark:text-orange-300 hover:text-orange-800 dark:hover:text-orange-200">
                    <span className="font-medium">Contacts</span>
                    <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                      ({results.contacts.deleted.length} deleted)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.contacts.deleted.map((deletion, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {deletion.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}
              
              {/* Notes Deletions */}
              {results.notes?.deleted && results.notes.deleted.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-orange-700 dark:text-orange-300 hover:text-orange-800 dark:hover:text-orange-200">
                    <span className="font-medium">Notes/Activities</span>
                    <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                      ({results.notes.deleted.length} deleted)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.notes.deleted.map((deletion, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {deletion.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}
              
              {/* Associations Deletions */}
              {results.associations?.deleted && results.associations.deleted.length > 0 && (
                <details className="group" open>
                  <summary className="cursor-pointer text-sm text-orange-700 dark:text-orange-300 hover:text-orange-800 dark:hover:text-orange-200">
                    <span className="font-medium">Associations</span>
                    <span className="ml-2 text-xs text-orange-600 dark:text-orange-400">
                      ({results.associations.deleted.length} deleted)
                    </span>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1 max-h-48 overflow-y-auto">
                    {results.associations.deleted.map((deletion, index) => (
                      <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                        • {deletion.item}
                      </div>
                    ))}
                  </div>
                </details>
              )}
            </div>
            <div className="mt-3 pt-3 border-t border-orange-200 dark:border-orange-800">
              <p className="text-xs text-orange-600 dark:text-orange-400">
                These records have been soft-deleted in your system because they no longer exist in HubSpot.
                They remain recoverable if needed.
              </p>
            </div>
          </div>
        );
      })()}

      {/* Actions */}
      {(onClose || onRetry) && (
        <div className="mt-6 flex justify-end space-x-3">
          {totalErrors > 0 && onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Retry Failed Items
            </button>
          )}
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Done
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ImportSummary;
