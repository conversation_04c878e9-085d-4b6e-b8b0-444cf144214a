# Database Optimization Plan

**Status**: Active  
**Approach**: Optimize existing 30-table schema (NOT a rewrite)

## Overview

Based on comprehensive analysis, we're taking a targeted optimization approach rather than a complete schema rewrite. The current schema serves real business needs - it just needs specific fixes.

## Current State

- **30 core tables** supporting active features
- **CASCADE DELETE issues** causing data loss  
- **Inconsistent soft delete** implementation
- **15+ migration files** accumulated over time
- **Schema is fundamentally sound** but needs optimization

## Target State

- **28 tables** after removing unused ones (from 30)
- **Safe foreign keys** (SET NULL instead of CASCADE)
- **Consistent soft delete** across all queries
- **Single unified schema file** for clarity
- **Same features, better implementation**

## Implementation Plan

### Phase 1: Critical Fixes (Week 1)

#### Fix Foreign Key Constraints
```sql
-- Change all dangerous CASCADE DELETE to SET NULL
-- Example for deals table:
ALTER TABLE deal DROP FOREIGN KEY fk_company;
ALTER TABLE deal ADD FOREIGN KEY (company_id) 
  REFERENCES company(id) ON DELETE SET NULL;
```

Apply similar changes to:
- deal → company (SET NULL)
- estimate → company (SET NULL)
- project → company (SET NULL)
- project → deal (SET NULL)
- note → parent_note (SET NULL)
- All non-junction tables

Keep CASCADE only for true junction tables:
- contact_company
- contact_role
- deal_estimate
- project_contact

#### Ensure Soft Delete Consistency
1. Audit all repository classes
2. Add `WHERE deleted_at IS NULL` to all queries
3. Update any missing soft delete implementations
4. Test thoroughly

#### Create Unified Schema
1. Export current schema structure
2. Create new `000_unified_schema.sql` with all fixes
3. This becomes the single source of truth
4. Archive old migration files

### Phase 2: Remove Unused Tables (Week 2)

Based on code analysis, safely remove:
- `company_relationship` - Backend ready but UI disabled
- `opportunity_intelligence` - No active code

Keep these (they have active dependencies):
- `note` - Used by conversations
- `estimate_allocation` & `estimate_time_allocation` - Used by reports
- All enrichment tables - Active features

### Phase 3: Testing & Documentation (Week 3)

1. Comprehensive testing on preview environment
2. Update all documentation
3. Train development team
4. Create rollback procedures

## Risk Mitigation

1. **Incremental Approach** - Fix issues without breaking features
2. **Preview Testing** - Full validation before production
3. **Backup Everything** - Complete data export before changes
4. **Rollback Ready** - Keep old schema for 30 days

## Why This Approach?

### What We Considered
- **14-table rewrite**: Beautiful but breaks core features
- **Keep everything**: Maintains unnecessary complexity
- **This approach**: Best of both worlds

### Why It Works
- **Low Risk**: No feature changes
- **High Impact**: Fixes critical issues
- **Fast**: 3 weeks vs 3-6 months
- **Practical**: Solves real problems

## Success Criteria

- ✅ No more CASCADE DELETE data loss
- ✅ All soft deletes work consistently
- ✅ Schema simplified to 28 tables
- ✅ All features continue working
- ✅ Performance maintained or improved
- ✅ Single schema file for clarity

## Next Steps

1. Review and approve this plan
2. Create detailed fix scripts
3. Schedule preview deployment
4. Execute phase by phase

Total effort: 3 weeks
Risk level: Low
Business impact: Minimal