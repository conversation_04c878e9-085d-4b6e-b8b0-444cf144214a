# Xero MCP Chat Implementation Guide

## Overview

This guide provides a complete implementation of a Xero MCP-powered chat interface for your Onbord Financial Dashboard. The solution recreates the Claude Desktop MCP experience as an embedded chat widget in your web application.

## Is It Possible?

**Yes, absolutely!** Here's why:

1. **MC<PERSON> Works in Browsers** - The Model Context Protocol has web-compatible transports (SSE and Streamable HTTP)
2. **Remote MCP Servers** - Recent developments (March 2025) enable MCP servers to be deployed and accessed remotely
3. **TypeScript SDK Support** - Full browser support via `@modelcontextprotocol/sdk`
4. **Existing Integration** - You already have Xero OAuth2 authentication, which can be reused

## Architecture

```
┌─────────────────────────────────┐
│   Browser (React App)           │
│  ┌───────────────────────────┐  │
│  │   XeroChat Component      │  │
│  │  ┌────────────────────┐   │  │
│  │  │  MCP Client (SDK)  │   │  │
│  │  └────────┬───────────┘   │  │
│  └───────────┼───────────────┘  │
└──────────────┼──────────────────┘
               │ SSE/HTTP
┌──────────────┼──────────────────┐
│   Backend (Express)             │
│  ┌───────────▼───────────────┐  │
│  │   MCP Server Endpoint     │  │
│  │  ┌────────────────────┐   │  │
│  │  │  Session Manager   │   │  │
│  │  └────────┬───────────┘   │  │
│  └───────────┼───────────────┘  │
│              │                   │
│  ┌───────────▼───────────────┐  │
│  │   Xero MCP Server         │  │
│  │  (subprocess or remote)   │  │
│  └───────────┬───────────────┘  │
└──────────────┼──────────────────┘
               │
        ┌──────▼──────┐
        │  Xero API   │
        └─────────────┘
```

## Implementation Steps

### Step 1: Install Dependencies

```bash
# MCP TypeScript SDK
npm install @modelcontextprotocol/sdk

# Xero MCP Server (optional if running locally)
npm install @xeroapi/xero-mcp-server

# Additional dependencies for chat UI
npm install lucide-react
```

### Step 2: Backend Setup

#### Add MCP routes to your server

```typescript
// src/api/server.ts
import mcpRoutes from './routes/mcp';

// Add this after other routes
router.use('/mcp', mcpRoutes);
```

#### MCP Route Implementation

The `/api/routes/mcp.ts` file implements:

1. **SSE Endpoint** (`/api/mcp/sse`) - For real-time bidirectional communication
2. **Message Endpoint** (`/api/mcp/messages`) - For client-to-server messages
3. **Session Management** - Each chat session spawns its own Xero MCP subprocess
4. **Streamable HTTP** (optional) - Modern transport for better performance

Key features:
- Automatic Xero MCP server spawning
- Session isolation for multiple users
- Graceful cleanup on disconnect
- Support for both SSE and Streamable HTTP transports

### Step 3: Frontend Implementation

#### Basic Chat Component

The `XeroChat.tsx` component provides:
- Floating button that expands to chat window
- MCP client initialization
- Tool discovery and display
- Message sending/receiving
- Loading states and error handling

#### Advanced LLM Integration

The `XeroChatWithLLM.tsx` component adds:
- Natural language understanding
- Automatic tool selection based on user intent
- Conversational response formatting
- Context awareness
- Suggested queries

### Step 4: Add Chat to Your App

```typescript
// src/frontend/components/App.tsx
import { XeroChat } from './XeroChat';

// Add inside your main layout
<XeroChat />
```

### Step 5: LLM Integration (Optional but Recommended)

For natural language processing, create an API endpoint:

```typescript
// src/api/routes/chat.ts
router.post('/process', async (req, res) => {
  const { message, tools, context } = req.body;
  
  // Send to your LLM (Claude, GPT, etc.)
  const response = await callLLM({
    system: `You are a Xero assistant. Available tools: ${JSON.stringify(tools)}`,
    messages: [...context, { role: 'user', content: message }]
  });
  
  // Parse LLM response for tool calls
  const toolCalls = extractToolCalls(response);
  
  res.json({ toolCalls, message: response });
});
```

## Available Xero MCP Tools

The Xero MCP server provides 30+ tools:

### Listing/Reading Tools
- `list-accounts` - Chart of accounts
- `list-contacts` - Customers/suppliers
- `list-invoices` - Sales invoices
- `list-bills` - Purchase invoices
- `list-quotes` - Sales quotes
- `list-credit-notes` - Credit notes
- `list-payments` - Payment records
- `list-bank-transactions` - Bank transactions
- `list-items` - Inventory items
- `list-tax-rates` - Tax configurations
- `list-trial-balance` - Trial balance report
- `list-profit-and-loss` - P&L report
- `list-payroll-employees` - Employee list
- `list-organisation-details` - Company info

### Creating Tools
- `create-contact` - New customer/supplier
- `create-invoice` - New sales invoice
- `create-quote` - New quote
- `create-credit-note` - New credit note
- `create-payment` - Record payment
- `create-bank-transaction` - New transaction
- `create-item` - New inventory item

### Updating Tools
- `update-contact` - Modify contact
- `update-invoice` - Edit draft invoice
- `update-quote` - Edit draft quote
- `update-credit-note` - Edit draft credit
- `update-bank-transaction` - Edit transaction

## Example User Interactions

### Simple Query
```
User: "Show me unpaid invoices"
Assistant: "I found 5 unpaid invoices totaling $23,450:
- INV-001: Acme Corp - $5,200 (Due 15 days ago)
- INV-002: Tech Solutions - $8,750 (Due in 5 days)
..."
```

### Complex Action
```
User: "Create an invoice for John Smith for consulting services, $2,500"
Assistant: "I'll create that invoice for you. Let me find John Smith first...
Found John Smith. Creating invoice...
✓ Invoice INV-045 created successfully for John Smith
- Amount: $2,500.00
- Due date: 30 days from today
- Status: Draft

Would you like me to send this invoice?"
```

### Multi-step Workflow
```
User: "What's the total owed by Acme Corp and can you show their payment history?"
Assistant: "Let me check Acme Corp's account...

Current balance: $15,750
- 3 unpaid invoices totaling $15,750
- 2 overdue by 30+ days

Payment history (last 6 months):
- March 15: $5,000 (Invoice INV-033)
- February 1: $3,200 (Invoice INV-028)
- January 10: $7,500 (Invoice INV-024)

Would you like me to send a payment reminder?"
```

## Security Considerations

1. **Authentication** - Uses existing Xero OAuth2 tokens
2. **Session Isolation** - Each user gets their own MCP subprocess
3. **Rate Limiting** - Implement request throttling
4. **Data Privacy** - No chat history stored by default
5. **Error Handling** - Graceful fallbacks for API failures

## Deployment Options

### Option 1: Embedded (Recommended for Start)
- Run Xero MCP as subprocess
- Easier to implement
- Lower latency
- Single deployment

### Option 2: Remote MCP Server
- Deploy Xero MCP separately
- Better scalability
- Independent updates
- Requires additional infrastructure

Example remote deployment:
```typescript
// Connect to remote MCP instead of subprocess
const transport = new StreamableHTTPClientTransport(
  new URL('https://your-mcp-server.com/xero')
);
```

## Performance Optimization

1. **Connection Pooling** - Reuse MCP connections
2. **Response Caching** - Cache frequent queries
3. **Lazy Loading** - Only initialize when chat opens
4. **Tool Prefetching** - Cache available tools
5. **Message Batching** - Group multiple tool calls

## Testing

```typescript
// Test MCP connection
describe('XeroChat', () => {
  it('should connect to MCP server', async () => {
    const client = new Client({ name: 'test', version: '1.0.0' });
    const transport = new SSEClientTransport(testUrl);
    await expect(client.connect(transport)).resolves.not.toThrow();
  });
  
  it('should list available tools', async () => {
    const tools = await client.listTools();
    expect(tools.tools).toContainEqual(
      expect.objectContaining({ name: 'list-invoices' })
    );
  });
});
```

## Troubleshooting

### Connection Issues
- Check if Xero MCP server is installed
- Verify environment variables are set
- Ensure proper CORS configuration
- Check browser console for SSE errors

### Authentication Problems
- Verify Xero OAuth tokens are valid
- Check session management
- Ensure cookies are properly configured

### Performance Issues
- Implement connection pooling
- Add response caching
- Use Streamable HTTP instead of SSE
- Consider remote MCP deployment

## Next Steps

1. **Start Simple** - Implement basic chat without LLM
2. **Add Intelligence** - Integrate LLM for natural language
3. **Enhance UX** - Add typing indicators, read receipts
4. **Extend Tools** - Add more MCP servers (Harvest, HubSpot)
5. **Analytics** - Track usage and popular queries

## Resources

- [Model Context Protocol Docs](https://modelcontextprotocol.io)
- [Xero MCP Server](https://github.com/XeroAPI/xero-mcp-server)
- [MCP TypeScript SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- [Remote MCP Deployment](https://blog.cloudflare.com/remote-model-context-protocol-servers-mcp/)

## Conclusion

Implementing Xero MCP in your web application is not only possible but straightforward with the right approach. The combination of MCP's standardized protocol, Xero's comprehensive tool set, and your existing authentication makes this a powerful addition to your financial dashboard.

The chat interface provides users with a natural way to interact with their financial data, performing complex queries and actions through simple conversation. This matches the Claude Desktop experience while being fully integrated into your web application.
