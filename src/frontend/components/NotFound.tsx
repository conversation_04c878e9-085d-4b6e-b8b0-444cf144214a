import React from 'react'; import { Link } from 'react-router-dom'; /** * NotFound component displayed when a user navigates to an invalid route */ const NotFound: React.FC = () => { return ( <div className="flex flex-col items-center justify-center p-8 text-center"> <h1 className="text-4xl font-bold text-gray-800 dark:text-gray-200 mb-4"> 404 - Page Not Found </h1> <p className="text-lg text-gray-600 dark:text-gray-400 mb-8"> The page you are looking for doesn't exist or has been moved. </p> <Link to="/" className="px-4 py-2 bg-primary text-white rounded-lg transition-colors" > Return to Dashboard </Link> </div> ); }; export default NotFound; 