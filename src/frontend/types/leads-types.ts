/**
 * Leads Types
 *
 * This file defines the types used for the Leads feature.
 */

import type {
  Company
} from '../../types/company-types';
import type { CompanySource, CompanyPriority, RadarState } from '../../types/shared-types';

// Type alias for backward compatibility
export type RadarCompany = Company;

// Import RadarCompanyUpdate directly from company-types to avoid circular references
import type { RadarCompanyUpdate as OriginalRadarCompanyUpdate } from '../../types/company-types';

// Re-export types for backward compatibility
export type { CompanyPriority, RadarState, CompanySource };

// Re-export RadarCompanyUpdate with a type alias to avoid circular references
export type RadarCompanyUpdate = OriginalRadarCompanyUpdate;

/**
 * Quadrant definition
 */
export interface Quadrant {
  id: RadarState;
  title: string;
  description: string;
  currentSpend: 'Low' | 'High';
  potentialSpend: 'Low' | 'High';
}

/**
 * Predefined quadrants for the Radar
 */
export const RADAR_QUADRANTS: Quadrant[] = [
  {
    id: 'Strategy',
    title: 'Strategy',
    description: 'Low current spend, high potential spend',
    currentSpend: 'Low',
    potentialSpend: 'High'
  },
  {
    id: 'Transformation',
    title: 'Transformation',
    description: 'High current spend, high potential spend',
    currentSpend: 'High',
    potentialSpend: 'High'
  },
  {
    id: 'BAU',
    title: 'BAU',
    description: 'Low current spend, low potential spend',
    currentSpend: 'Low',
    potentialSpend: 'Low'
  },
  {
    id: 'Transition out',
    title: 'Transition out',
    description: 'High current spend, low potential spend',
    currentSpend: 'High',
    potentialSpend: 'Low'
  }
];

// PRIORITY_COLORS and PRIORITY_DESCRIPTIONS are now imported from '../../types/company-types'
