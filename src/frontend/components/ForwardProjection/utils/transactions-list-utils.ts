import { Transaction } from '../../../../types/financial'; import { isXeroTransaction } from './xero-utils'; import { XERO_SOURCES } from '../../../../constants/xero'; /** * Gets a formatted description for a transaction * For invoices, displays Project on line 1, Client on line 2 */ export function getFormattedDescription(transaction: Transaction): string { const isRealInvoice = transaction.source === 'harvest' && transaction.type === 'invoice' && !transaction.id.startsWith('future-work-') && !transaction.id.startsWith('uninvoiced-'); const isProjectedIncome = transaction.source === 'harvest' && transaction.type === 'invoice' && (transaction.id.startsWith('future-work-') || transaction.id.startsWith('uninvoiced-')); if (isRealInvoice || isProjectedIncome) { // For invoices, display Project on line 1, Client on line 2 const clientName = transaction.metadata?.clientName || 'Unknown Client'; const projectName = transaction.metadata?.projectName || 'Unknown Project'; return `${projectName}\n${clientName}`; } else { // For all other types, return the original description return transaction.description || 'Unknown'; } } /** * Determines if a transaction is a real invoice */ export function isRealInvoice(transaction: Transaction): boolean { return transaction.source === 'harvest' && transaction.type === 'invoice' && !transaction.id.startsWith('future-work-') && !transaction.id.startsWith('uninvoiced-'); } /** * Determines if a transaction is a projected invoice */ export function isProjectedInvoice(transaction: Transaction): boolean { return transaction.source === 'harvest' && transaction.type === 'invoice' && (transaction.id.startsWith('future-work-') || transaction.id.startsWith('uninvoiced-')); } /** * Gets the appropriate color class for a transaction type */ export function getTypeColor(transaction: Transaction): string { const isExpense = transaction.amount < 0; if (transaction.type === 'bill' && transaction.metadata?.originalType === 'repeating_bill') { return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'; } else if (transaction.type.startsWith('custom_expense_')) { if (transaction.type.includes('Monthly Payroll')) { return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'; } else if (transaction.type.includes('Software')) { return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'; } else if (transaction.type.includes('Superannuation')) { return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'; } else if (transaction.type.includes('Insurances')) { return 'bg-magenta-100 text-magenta-800 dark:bg-magenta-900/30 dark:text-magenta-300'; } else if (transaction.type.includes('Taxes')) { return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'; } else if (transaction.type.includes('Fees')) { return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'; } else { return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'; } } else if (isRealInvoice(transaction)) { return 'bg-green-200 text-green-800 dark:bg-green-900/50 dark:text-green-200'; } else if (isProjectedInvoice(transaction)) { return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'; } else if (isExpense) { return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'; } else { return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'; } } /** * Determines if a transaction should be highlighted */ export function isTransactionHighlighted(transaction: Transaction, hoveredDate: string | null): boolean { if (!hoveredDate) return false; const transactionDateString = new Date(transaction.date).toISOString().split('T')[0]; return hoveredDate === transactionDateString; } /** * Gets the background color class for a transaction based on index and highlight state */ export function getBackgroundColorClass(index: number, isHighlighted: boolean): string { if (isHighlighted) { return 'bg-blue-50 dark:bg-blue-900/20'; } return index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50/50 dark:bg-gray-700/50'; } /** * Extracts invoice ID from Harvest invoice ID string */ export function extractInvoiceId(id: string): string { return id.startsWith("harvest-invoice-") ? id.replace("harvest-invoice-", "") : id.replace("outstanding-invoice-", ""); } /** * Check if a transaction is a GST Payment (BAS) expense from Xero * * This function identifies GST/BAS transactions in the transaction list * to display the appropriate badge (Predicted or Accrued) * * Uses the same logic as isGSTExpense in Expense/utils.ts * * @param transaction Transaction to check * @returns True if transaction is a GST Payment (BAS) expense from Xero */ export function isGSTTransaction(transaction: Transaction): boolean { // First check if it's from Xero if (!isXeroTransaction(transaction)) { return false; } // Check if it's a transaction with source 'xero-gst' if (transaction.source === XERO_SOURCES.GST) { return true; } // Check if it has metadata with source 'xero-gst' if (transaction.metadata?.source === XERO_SOURCES.GST) { return true; } return false; }