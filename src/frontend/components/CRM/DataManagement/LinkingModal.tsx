import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '../../shared/Button';
import type { Company } from '../../../types/crm-types';

interface LinkingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (targetId: string | number) => void;
  companyName: string;
  linkType: 'hubspot' | 'harvest' | null;
  hubspotCompanies: Company[];
  harvestCompanies: any[];
  isLoading: boolean;
}

/**
 * Calculate similarity score between two strings
 * Returns a score between 0 and 1, where 1 is an exact match
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  const s1 = str1.toLowerCase().trim();
  const s2 = str2.toLowerCase().trim();
  
  // Exact match
  if (s1 === s2) return 1;
  
  // Check if one contains the other
  if (s1.includes(s2) || s2.includes(s1)) return 0.8;
  
  // Check for common words
  const words1 = s1.split(/\s+/);
  const words2 = s2.split(/\s+/);
  const commonWords = words1.filter(word => words2.includes(word));
  const commonScore = commonWords.length / Math.max(words1.length, words2.length);
  
  // Simple character-based similarity
  const maxLen = Math.max(s1.length, s2.length);
  let matches = 0;
  for (let i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) matches++;
  }
  const charScore = matches / maxLen;
  
  // Return weighted average
  return commonScore * 0.7 + charScore * 0.3;
};

/**
 * Modal for linking companies to external systems
 */
const LinkingModal: React.FC<LinkingModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  companyName,
  linkType,
  hubspotCompanies,
  harvestCompanies,
  isLoading
}) => {
  const [selectedId, setSelectedId] = useState<string | number>('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (isOpen) {
      setSelectedId('');
      setSearchTerm('');
    }
  }, [isOpen]);

  const isHubSpotLink = linkType === 'hubspot';
  const companies = isHubSpotLink ? hubspotCompanies : harvestCompanies;
  
  // Calculate suggested matches based on similarity
  const suggestedMatches = useMemo(() => {
    if (!companyName || companies.length === 0) return [];
    
    const companiesWithScores = companies.map(company => ({
      company,
      score: calculateSimilarity(companyName, company.name)
    }));
    
    // Sort by score and take top 3 matches with score > 0.3
    return companiesWithScores
      .filter(item => item.score > 0.3)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(item => item.company);
  }, [companyName, companies]);
  
  const filteredCompanies = useMemo(() => {
    return companies.filter(company =>
      company.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [companies, searchTerm]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedId) {
      onSubmit(selectedId);
    }
  };

  const modalTitle = isHubSpotLink ? 'Link to HubSpot Company' : 'Link to Harvest Client';
  
  // Early return AFTER all hooks
  if (!isOpen || !linkType) return null;
  const systemName = isHubSpotLink ? 'HubSpot' : 'Harvest';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {modalTitle}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="p-4 overflow-y-auto max-h-[calc(90vh-8rem)]">
            {/* Company being linked */}
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                Company to link:
              </div>
              <div className="font-medium text-gray-900 dark:text-white">
                {companyName}
              </div>
            </div>

            {/* Suggested matches */}
            {suggestedMatches.length > 0 && !searchTerm && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Suggested Matches
                </label>
                <div className="space-y-2">
                  {suggestedMatches.map((company) => {
                    const companyId = isHubSpotLink ? company.hubspotId : company.id;
                    const displayId = isHubSpotLink ? company.hubspotId : company.id;
                    
                    return (
                      <div
                        key={companyId}
                        className={`p-3 border rounded-md cursor-pointer transition-colors relative ${
                          selectedId === companyId
                            ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                            : "border-purple-300 dark:border-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/10"
                        }`}
                        onClick={() => setSelectedId(companyId)}
                      >
                        <div className="absolute top-2 right-2">
                          <span className="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded">
                            Suggested
                          </span>
                        </div>
                        <div
                          className={`font-medium ${
                            selectedId === companyId
                              ? "text-purple-700 dark:text-purple-400"
                              : "text-gray-900 dark:text-white"
                          }`}
                        >
                          {company.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {systemName} ID: {displayId}
                        </div>
                        {company.industry && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Industry: {company.industry}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 italic">
                  These suggestions are based on name similarity
                </div>
              </div>
            )}

            {/* Search input */}
            <div className="mb-4">
              <label
                htmlFor="search"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                Search {systemName} Companies
              </label>
              <input
                type="text"
                id="search"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white"
                placeholder={`Search ${systemName} companies...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Company list */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {searchTerm ? 'Search Results' : `All ${systemName} Companies`}
              </label>
              <div className="border border-gray-300 dark:border-gray-600 rounded-md h-64 overflow-y-auto p-2">
                {filteredCompanies.length === 0 ? (
                  <div className="text-center py-6 text-gray-500 dark:text-gray-400 text-sm italic">
                    No companies found
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredCompanies.map((company) => {
                      const companyId = isHubSpotLink ? company.hubspotId : company.id;
                      const displayId = isHubSpotLink ? company.hubspotId : company.id;
                      
                      return (
                        <div
                          key={companyId}
                          className={`p-3 border rounded-md cursor-pointer transition-colors ${
                            selectedId === companyId
                              ? "border-purple-500 bg-purple-50 dark:bg-purple-900/20"
                              : "border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                          }`}
                          onClick={() => setSelectedId(companyId)}
                        >
                          <div
                            className={`font-medium ${
                              selectedId === companyId
                                ? "text-purple-700 dark:text-purple-400"
                                : "text-gray-900 dark:text-white"
                            }`}
                          >
                            {company.name}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {systemName} ID: {displayId}
                          </div>
                          {company.industry && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Industry: {company.industry}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Warning about existing links */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-3">
              <div className="flex">
                <svg className="h-5 w-5 text-yellow-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div className="text-sm text-yellow-800 dark:text-yellow-300">
                  <strong>Note:</strong> This will create a permanent link between the companies. 
                  Make sure you've selected the correct {systemName} company.
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!selectedId || isLoading}
            >
              {isLoading ? 'Linking...' : `Link to ${systemName}`}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LinkingModal;