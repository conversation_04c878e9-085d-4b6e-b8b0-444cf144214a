/**
 * Schema Validation Script
 * 
 * This script validates that all repository queries will work against the current database schema.
 * Run this script to catch schema mismatches before they cause runtime errors.
 */

const BetterSqlite3 = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// Database setup
const dbPath = process.env.DB_PATH || path.join(__dirname, '../data/upstream.db');

console.log('🔍 Starting comprehensive schema validation...');
console.log(`Database: ${dbPath}`);

if (!fs.existsSync(dbPath)) {
  console.error('❌ Database file not found. Run migrations first.');
  process.exit(1);
}

const db = new BetterSqlite3(dbPath);

/**
 * Get all columns for a table
 */
function getTableColumns(tableName) {
  try {
    const columns = db.prepare(`PRAGMA table_info(${tableName})`).all();
    return columns.map(col => col.name);
  } catch (error) {
    return [];
  }
}

/**
 * Check if table exists
 */
function tableExists(tableName) {
  const result = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name=?
  `).get(tableName);
  return !!result;
}

/**
 * Validate a SQL query can be prepared
 */
function validateQuery(query, description) {
  try {
    db.prepare(query);
    return { valid: true, error: null };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

/**
 * Schema validation results
 */
const results = {
  missingTables: [],
  missingColumns: [],
  invalidQueries: [],
  validTables: [],
  validQueries: []
};

console.log('\n📋 Validating required tables...');

// Required tables and their expected columns
const tableSchema = {
  company: [
    'id', 'name', 'industry', 'size', 'website', 'address', 'description',
    'hubspot_id', 'harvest_id', 'linking_status', 'source', 'radar_state', 
    'priority', 'current_spend', 'potential_spend', 'last_interaction_date',
    'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at', 'notes', 'contacts'
  ],
  contact: [
    'id', 'first_name', 'last_name', 'email', 'phone', 'job_title',
    'hubspot_id', 'harvest_user_id', 'source', 'created_at', 'updated_at',
    'created_by', 'updated_by', 'notes', 'deleted_at'
  ],
  deal: [
    'id', 'name', 'stage', 'status', 'value', 'amount', 'currency', 'probability',
    'expected_close_date', 'close_date', 'start_date', 'end_date', 'invoice_frequency',
    'payment_terms', 'company_id', 'hubspot_id', 'harvest_project_id', 'description',
    'source', 'priority', 'owner', 'custom_fields', 'include_in_projections',
    'pipeline', 'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at'
  ],
  activity_feed: [
    'id', 'type', 'subject', 'description', 'status', 'entity_type', 'entity_id',
    'due_date', 'completed_date', 'company_id', 'contact_id', 'deal_id',
    'metadata', 'is_read', 'importance', 'created_by', 'source', 'created_at', 'updated_at'
  ],
  hubspot_settings: ['id', 'key', 'value', 'created_at', 'updated_at'],
  field_ownership: ['id', 'entity_type', 'entity_id', 'field_name', 'owner', 'set_at', 'set_by'],
  change_log: ['id', 'entity_type', 'entity_id', 'field_name', 'old_value', 'new_value', 'change_source', 'changed_at', 'changed_by'],
  cashflow_snapshot: ['id', 'date', 'tenant_id', 'days_ahead', 'snapshot_data', 'created_at', 'created_by']
};

// Validate each table and its columns
for (const [tableName, expectedColumns] of Object.entries(tableSchema)) {
  if (!tableExists(tableName)) {
    results.missingTables.push(tableName);
    console.log(`❌ Missing table: ${tableName}`);
    continue;
  }

  results.validTables.push(tableName);
  console.log(`✅ Table exists: ${tableName}`);

  const actualColumns = getTableColumns(tableName);
  const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));
  
  if (missingColumns.length > 0) {
    results.missingColumns.push({ table: tableName, columns: missingColumns });
    console.log(`  ⚠️  Missing columns in ${tableName}: ${missingColumns.join(', ')}`);
  } else {
    console.log(`  ✅ All columns present in ${tableName}`);
  }
}

console.log('\n🔧 Validating common repository queries...');

// Common queries that repositories use
const testQueries = [
  {
    description: 'Contact with job_title',
    query: 'SELECT id, first_name, last_name, email, job_title FROM contact WHERE id = ?'
  },
  {
    description: 'Company basic fields',
    query: 'SELECT id, name, hubspot_id, harvest_id, linking_status FROM company WHERE id = ?'
  },
  {
    description: 'Deal with expected_close_date',
    query: 'SELECT id, name, stage, value, expected_close_date FROM deal WHERE id = ?'
  },
  {
    description: 'Activity feed insert',
    query: 'INSERT INTO activity_feed (id, type, subject, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?)'
  },
  {
    description: 'HubSpot settings',
    query: 'SELECT key, value FROM hubspot_settings WHERE key = ?'
  },
  {
    description: 'Field ownership',
    query: 'SELECT * FROM field_ownership WHERE entity_type = ? AND entity_id = ?'
  },
  {
    description: 'Change log',
    query: 'INSERT INTO change_log (id, entity_type, entity_id, field_name, old_value, new_value, change_source, changed_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
  },
  {
    description: 'Cashflow snapshot',
    query: 'SELECT * FROM cashflow_snapshot WHERE date = ? AND tenant_id = ?'
  }
];

for (const { description, query } of testQueries) {
  const result = validateQuery(query, description);
  if (result.valid) {
    results.validQueries.push(description);
    console.log(`✅ Query valid: ${description}`);
  } else {
    results.invalidQueries.push({ description, query, error: result.error });
    console.log(`❌ Query invalid: ${description}`);
    console.log(`   Error: ${result.error}`);
  }
}

console.log('\n📊 Validation Summary');
console.log('==================');
console.log(`✅ Valid tables: ${results.validTables.length}`);
console.log(`❌ Missing tables: ${results.missingTables.length}`);
console.log(`⚠️  Tables with missing columns: ${results.missingColumns.length}`);
console.log(`✅ Valid queries: ${results.validQueries.length}`);
console.log(`❌ Invalid queries: ${results.invalidQueries.length}`);

if (results.missingTables.length > 0) {
  console.log('\n❌ Missing Tables:');
  results.missingTables.forEach(table => console.log(`   - ${table}`));
}

if (results.missingColumns.length > 0) {
  console.log('\n⚠️  Missing Columns:');
  results.missingColumns.forEach(({ table, columns }) => {
    console.log(`   ${table}: ${columns.join(', ')}`);
  });
}

if (results.invalidQueries.length > 0) {
  console.log('\n❌ Invalid Queries:');
  results.invalidQueries.forEach(({ description, error }) => {
    console.log(`   ${description}: ${error}`);
  });
}

// Exit with error code if there are issues
const hasIssues = results.missingTables.length > 0 || 
                  results.missingColumns.length > 0 || 
                  results.invalidQueries.length > 0;

if (hasIssues) {
  console.log('\n🚨 Schema validation failed! Run migration 003 to fix these issues.');
  console.log('   Command: npm run migrate');
  process.exit(1);
} else {
  console.log('\n🎉 Schema validation passed! All tables and queries are valid.');
  process.exit(0);
}

db.close();