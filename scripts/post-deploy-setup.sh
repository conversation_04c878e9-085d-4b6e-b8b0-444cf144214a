#!/bin/bash
set -e # Exit on error

# Post-deployment setup script to restore development environment
echo "=== RESTORING DEVELOPMENT ENVIRONMENT AFTER DEPLOYMENT ==="

# Check if .env file exists, if not copy from example
if [ ! -f .env ]; then
  echo "📄 Creating .env file from example..."
  cp .env.example .env
  echo "✅ Created .env file. You may need to customize it with your credentials."
else
  echo "ℹ️ .env file already exists, keeping it."
fi

# Install dependencies
echo "📦 Installing development dependencies..."
npm ci --legacy-peer-deps

# Install concurrently explicitly in case it's missing
echo "📦 Ensuring concurrently is installed..."
npm install --no-save concurrently

echo "✅ Development environment restored! You can now run:"
echo "   npm run dev"