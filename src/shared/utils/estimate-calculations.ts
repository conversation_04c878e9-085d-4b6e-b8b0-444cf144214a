/**
 * Shared utility functions for estimate calculations
 * Used by both frontend and backend code
 */

/**
 * Calculates the project totals from staff allocations.
 */
export const calculateProjectTotals = (
  staffAllocations: { 
    totalCost: number; 
    totalFees: number; 
    totalDays: number;
    onbordTargetRateDaily?: number;
  }[]
): {
  totalRevenue: number;
  totalCosts: number;
  marginAmount: number;
  marginPercentage: number;
  subTotal: number;
  gstAmount: number;
  grandTotal: number;
  totalDays: number;
  targetTotalRevenue: number;
  targetRateDifference: number;
  targetRatePercentageDifference: number;
  averageDailyRate: number;
} => {
  const totalRevenue = staffAllocations.reduce((sum, alloc) => sum + alloc.totalFees, 0);
  const totalCosts = staffAllocations.reduce((sum, alloc) => sum + alloc.totalCost, 0);
  const marginAmount = totalRevenue - totalCosts;
  const marginPercentage = totalRevenue > 0 ? (marginAmount / totalRevenue) * 100 : 0;
  
  // Calculate total days - use integer arithmetic to avoid floating point errors
  const totalDaysTenths = staffAllocations.reduce((sum, alloc) => {
    // Convert to tenths and round to ensure integer arithmetic
    return sum + Math.round((alloc.totalDays || 0) * 10);
  }, 0);
  const totalDays = Math.round(totalDaysTenths) / 10;
  
  // Calculate average daily rate
  const averageDailyRate = totalDays > 0 ? totalRevenue / totalDays : 0;
  
  // Calculate target revenue based on target rates
  const targetTotalRevenue = staffAllocations.reduce((sum, alloc) => {
    const targetRate = alloc.onbordTargetRateDaily || 0;
    return sum + (targetRate * alloc.totalDays);
  }, 0);
  
  // Calculate rate difference from target
  const targetRateDifference = totalRevenue - targetTotalRevenue;
  const targetRatePercentageDifference = targetTotalRevenue > 0 
    ? (targetRateDifference / targetTotalRevenue) * 100 
    : 0;
  
  // Australian GST calculation (10%)
  const subTotal = totalRevenue;
  const gstAmount = subTotal * 0.1; // 10% GST
  const grandTotal = subTotal + gstAmount;

  return {
    totalRevenue,
    totalCosts,
    marginAmount,
    marginPercentage,
    subTotal,
    gstAmount,
    grandTotal,
    totalDays,
    targetTotalRevenue,
    targetRateDifference,
    targetRatePercentageDifference,
    averageDailyRate
  };
};

/**
 * Calculates discount amount and discounted total based on the discount type and value
 * @param total - The total amount before discount
 * @param discountType - The type of discount ('percentage', 'amount', or 'none')
 * @param discountValue - The discount value (percentage or fixed amount)
 * @returns Object containing calculated discount amount and discounted total
 */
export const calculateDiscount = (
  total: number,
  discountType: 'percentage' | 'amount' | 'none',
  discountValue: number
): { discountAmount: number, discountedTotal: number } => {
  if (discountType === 'none' || discountValue <= 0) {
    return { discountAmount: 0, discountedTotal: total };
  }
  
  let discountAmount = 0;
  if (discountType === 'percentage') {
    // Cap percentage at 100
    const cappedPercentage = Math.min(discountValue, 100);
    discountAmount = total * (cappedPercentage / 100);
  } else {
    // Cap amount at total
    discountAmount = Math.min(discountValue, total);
  }
  
  return {
    discountAmount,
    discountedTotal: total - discountAmount
  };
};
