# Error Handling Utilities

This module provides a standardized approach to error handling across the Onbord Financial Dashboard application.

## Key Features

- **Error Class Hierarchy**: TypeScript-first approach with proper error inheritance
- **Utility Functions**: Common patterns for error handling, retries, and timeouts
- **Consistent Fallbacks**: Standardized approach to providing fallback values
- **Rate Limit Handling**: Sophisticated rate limit detection and backoff
- **Request Deduplication**: Prevent duplicate in-flight requests

## Usage Examples

### Basic Error Handling

```typescript
import { withErrorHandling } from '../utils/error';

async function fetchData() {
  return withErrorHandling(
    async () => {
      // Your API call here
      const response = await api.getData();
      return response.data;
    },
    {
      operationName: 'fetchData',
      fallbackValue: [], // Return empty array on error
    }
  );
}
```

### With Retry and Custom Error Handling

```typescript
import { withErrorHandling, ApiError } from '../utils/error';

async function complexOperation() {
  return withErrorHandling(
    async () => {
      // Your complex operation here
      return await processData();
    },
    {
      operationName: 'complexOperation',
      maxRetries: 3,
      retryableErrors: (error) => {
        // Only retry network errors, not validation errors
        return error instanceof ApiError && error.isServerError();
      },
      onError: (error) => {
        // Additional error handling, e.g., logging to a service
        logErrorToService(error);
      }
    }
  );
}
```

### With Timeout

```typescript
import { withTimeout } from '../utils/error';

async function fetchWithTimeout() {
  return withTimeout(
    async () => {
      // Your API call that might timeout
      return await api.getLargeDataset();
    },
    5000, // 5 second timeout
    'fetchLargeDataset'
  );
}
```

### Request Deduplication

```typescript
import { RequestDeduplicator } from '../utils/error';

async function getUserProfile(userId: string) {
  // This will prevent multiple simultaneous requests for the same user
  return RequestDeduplicator.execute(
    `get-user-${userId}`,
    () => api.getUserProfile(userId)
  );
}
```

## Error Class Hierarchy

- `AppError`: Base error class for all application errors
- `ApiError`: For API-related errors
- `ValidationError`: For validation failures
- `TimeoutError`: For operation timeouts

## Migration from Previous Approaches

This module replaces three inconsistent error handling approaches:

1. **Circuit breaker pattern** (previously in cashflow-service.ts)
2. **Catch-and-return-empty-arrays** (previously in service files)
3. **Try/catch with fallback** (previously in various places)

The `withErrorHandling` function provides a unified approach that combines the strengths of all three patterns.
