# Onbord Financial Dashboard - Development Guide

This guide provides comprehensive information for developers working on the Onbord Financial Dashboard, covering development workflow, testing strategies, code style guidelines, and detailed technical documentation of key systems.

**Note on Recent Refactoring (March 2025):** This codebase recently underwent a significant refactoring effort focused on simplification, code reduction, and improved maintainability. Key areas like type definitions, error handling, and several large components/services were decomposed into smaller, more focused modules. While functionality remains the same, developers may notice newer patterns and structures compared to older versions. See `refactor.md` for historical details of this effort.

## Table of Contents

1. [Development Workflow](#development-workflow)
   - [Branch Strategy](#branch-strategy)
   - [Pull Request Process](#pull-request-process)
   - [Dependencies Management](#dependencies-management)
   - [Version Management](#version-management)

2. [Build and Test Commands](#build-and-test-commands)
   - [Development Commands](#development-commands)
   - [Build Commands](#build-commands)
   - [Test Commands](#test-commands)

3. [Testing Strategy](#testing-strategy)
   - [Test Types](#test-types)
   - [Authentication Strategy for Testing](#authentication-strategy-for-testing)
   - [Screenshot Testing](#screenshot-testing)

4. [Code Style Guidelines](#code-style-guidelines)
   - [General Conventions](#general-conventions)
   - [Frontend Guidelines](#frontend-guidelines)
   - [Backend Guidelines](#backend-guidelines)
   - [UI Guidelines](#ui-guidelines)

5. [Project Architecture](#project-architecture)
   - [Frontend Structure](#frontend-structure)
   - [Backend Structure](#backend-structure)
   - [Data Flow](#data-flow)

6. [Smart Forecast System](#smart-forecast-system)
   - [System Overview](#system-overview)
   - [Core Projection Principles](#core-projection-principles)
   - [Invoice Generation Algorithm](#invoice-generation-algorithm)
   - [Filtering Rules](#filtering-rules)
   - [Projection Audit Log](#projection-audit-log)

7. [Database & Data Model](#database--data-model)
   - [Migration System](#migration-system)
   - [Repository Pattern](#repository-pattern)
   - [Schema Management](#schema-management)
   - [Data Model Documentation](#data-model-documentation)

8. [Common Development Tasks](#common-development-tasks)
   - [Adding a New API Endpoint](#adding-a-new-api-endpoint)
   - [Adding a New UI Component](#adding-a-new-ui-component)
   - [Working with Environment Variables](#working-with-environment-variables)

9. [Deployment](#deployment)
   - [Branch-Based Deployment Strategy](#branch-based-deployment-strategy)
   - [Deployment Process](#deployment-process)
   - [Environment Configuration](#environment-configuration)
   - [Render.com Setup](#rendercom-setup)

10. [Troubleshooting](#troubleshooting)
   - [Common Development Issues](#common-development-issues)
   - [Smart Forecast Issues](#smart-forecast-issues)
   - [API Rate Limiting](#api-rate-limiting)
   - [Authentication Issues](#authentication-issues)

## Development Workflow

For comprehensive information about branch strategy, deployment, and build processes, please refer to the [DEPLOYMENT-GUIDE.md](DEPLOYMENT-GUIDE.md) document.

### Pull Request Process

1. Create a feature branch from preview
2. Implement changes with appropriate tests
3. Submit a pull request back to preview
4. Ensure all tests pass
5. Get approval from at least one reviewer
6. Merge to preview (NOT directly to main)

### Dependencies Management

Before adding a new dependency, consider:

- Is it actively maintained?
- Does it have a compatible license?
- Is it worth the added bundle size?
- Could we implement the functionality ourselves?

### Version Management

The application maintains a detailed version history in `src/constants/versionHistory.ts`, which follows a hierarchical structure with major releases, minor releases, and patches.

To update the version history based on git commits:

1. Make your changes and commit them with descriptive messages
   - Use prefixes like `feat:`, `fix:`, `docs:` for better categorization
   - Example: `feat: Add new expense categorization feature`

2. Run the version update script:
   ```bash
   npm run update-version
   ```

3. The script will:
   - Analyze git commits since the last version update
   - Suggest an appropriate version bump (major, minor, or patch)
   - Generate a structured entry based on commit messages
   - Allow you to review and confirm before updating

4. After confirming, the script updates `versionHistory.ts` with:
   - A new version number
   - Categorized changes based on commit messages
   - Appropriate minor releases and patches based on feature grouping
   - Today's date as the release date

5. Review the changes, make any manual adjustments if needed, and commit:
   ```bash
   git add src/constants/versionHistory.ts
   git commit -m "chore: Update version history to x.y.z"
   ```

This version history is displayed to users in the application footer and provides a complete changelog of all features and fixes through the user interface.

## Development Commands

- `npm run dev`: Start frontend and backend concurrently
- `npm run dev:frontend`: Vite frontend on port 5173
- `npm run dev:backend`: NodeJS backend with nodemon
- `./kill-ports.sh`: Kill processes on ports 3002,5173

For build commands and deployment instructions, please refer to [DEPLOYMENT-GUIDE.md](DEPLOYMENT-GUIDE.md).

### Test Commands

- `npm test`: Run all tests with Jest
- `npm test:unit`: Run unit tests only
- `npm test:integration`: Run integration tests
- `npm test:e2e`: Run Playwright tests
- `npm test:e2e:ui`: Run Playwright tests with UI debugger
- `npm test:e2e:headed`: Run Playwright tests in headed mode
- `npm test:e2e:visual`: Run visual tests
- `npm test:e2e:load`: Run load tests
- `npm test:e2e:accessibility`: Run accessibility tests
- `npx jest tests/unit/path/to/file.test.ts`: Run single test file
- `npx jest -t "test description"`: Run tests matching description

## Testing Strategy

The Onbord Financial Dashboard includes a comprehensive test suite with 60+ test files covering all major functionality. Our testing philosophy focuses on catching real bugs and ensuring business-critical functionality works correctly.

### Test Types & Tools

#### Unit Tests
- **Purpose:** Test business behavior and logic, not implementation details
- **Tools:** Jest, ts-jest, React Testing Library
- **Location:** `tests/unit/`
- **Coverage:** 
  - Business logic services (cashflow, projections, estimates)
  - React components (user-visible behavior, interactions)
  - Utilities and helpers
  - API controllers and middleware
- **Testing Philosophy:**
  - Focus on what the system does, not how it does it
  - Test business rules and validation logic
  - Avoid testing framework behavior or DOM structure
  - See `TESTING-BEST-PRACTICES.md` for guidelines
- **Architecture Limitations:**
  - Repository layer testing limited by global database singleton
  - Cannot use in-memory databases for isolated testing
  - Prefer integration tests for data access layer
  - See `docs/technical/testing-architecture-limitations.md`

#### Integration Tests
- **Purpose:** Test interactions between modules and API endpoints
- **Tools:** Jest, Supertest
- **Location:** `tests/integration/`
- **Coverage:**
  - API endpoint integration
  - Service orchestration
  - Database transactions
  - External API mocking
- **Key Tests:**
  - HubSpot sync workflow
  - Deal pipeline operations
  - Estimate publishing process
  - Cashflow calculations

#### End-to-End Tests
- **Tool:** Playwright
- **Location:** `tests/e2e/`
- **Coverage:**
  - Critical user workflows
  - Visual regression testing
  - Responsive design validation
  - Basic authentication flows
- **Screenshots:** Saved to `tests/e2e/screenshots/`

#### Schema Validation Tests
- **Purpose:** Ensure database integrity
- **Location:** `tests/unit/database/`
- **Coverage:**
  - Migration system validation
  - Foreign key relationships
  - Index optimization
  - Schema consistency

### Authentication Strategy for Testing

Due to Xero's MFA requirements, we use a multi-pronged approach:

1. **Mock Authentication Mode:**
   - Run with `npm run dev:mock` or `npm run test:mock`
   - Bypasses OAuth at the server level
   - Safe guards prevent production deployment
   - Enables automated testing of authenticated features

2. **Manual Authentication for E2E:**
   - Use Playwright's UI mode (`npm run test:e2e:ui`)
   - Save authentication state for test sessions
   - Focus on non-authenticated flows where possible

3. **API Mocking:**
   - Mock external APIs for unit/integration tests
   - Use realistic test data
   - Simulate error conditions and edge cases

### Test Coverage

#### Current Coverage
- **Statements:** ~85%
- **Branches:** ~80%
- **Functions:** ~90%
- **Lines:** ~85%

#### Target Coverage
- **Statements:** 90%+
- **Branches:** 85%+
- **Functions:** 95%+
- **Lines:** 90%+

### Priority Test Areas

1. **Critical Business Logic (HIGH PRIORITY)**
   - Deal projection calculations
   - Smart Forecast filtering
   - Transaction transformations
   - Estimate allocations

2. **Data Layer (HIGH PRIORITY)**
   - Repository CRUD operations
   - Field ownership tracking
   - Relationship management
   - Audit trail integrity

3. **API Endpoints (MEDIUM PRIORITY)**
   - Request validation
   - Error handling
   - Response formatting
   - Authentication

4. **React Components (MEDIUM PRIORITY)**
   - User interactions
   - State management
   - Rendering logic
   - Accessibility

### Running Tests

```bash
# All tests
npm test

# Specific suites
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:schema

# With options
npm test -- --coverage        # Coverage report
npm test -- --watch          # Watch mode
npm test -- [file]           # Specific file
npm test -- -t "pattern"     # Pattern matching

# Critical tests only
npm run test:critical        # Runs high-priority tests
```

### Writing New Tests

#### Test File Naming
- Unit tests: `[module].test.ts`
- Integration tests: `[feature]-integration.test.ts`
- Component tests: `[Component].test.tsx`

#### Test Structure
```typescript
describe('ModuleName', () => {
  // Setup
  beforeEach(() => {
    // Test setup
  });

  // Cleanup
  afterEach(() => {
    // Clean up mocks
  });

  describe('methodName', () => {
    it('should handle normal case', () => {
      // Arrange, Act, Assert
    });

    it('should handle edge case', () => {
      // Boundary conditions
    });

    it('should handle error case', () => {
      // Error scenarios
    });
  });
});
```

### Common Testing Patterns

1. **Repository Testing**
   ```typescript
   const db = new Database(':memory:');
   await runMigrations(db);
   const repo = new CompanyRepository(db);
   ```

2. **Service Mocking**
   ```typescript
   jest.mock('@/api/integrations/harvest');
   const mockHarvest = harvest as jest.Mocked<typeof harvest>;
   ```

3. **Component Testing**
   ```typescript
   render(<Component {...props} />);
   expect(screen.getByText('Expected')).toBeInTheDocument();
   ```

4. **Async Testing**
   ```typescript
   await waitFor(() => {
     expect(mockFn).toHaveBeenCalled();
   });
   ```

### Test Data Management

- Use factories for consistent test data
- Keep test data minimal and focused
- Test edge cases and invalid inputs
- Use realistic data structures

### Debugging Tests

```bash
# Debug mode
node --inspect-brk node_modules/.bin/jest --runInBand

# Verbose output
npm test -- --verbose

# Single test
npm test -- --testNamePattern="specific test"

# No coverage (faster)
npm test -- --no-coverage
```

### Known Issues & Solutions

1. **TypeScript Errors in Tests**
   - Check for untyped imports
   - Ensure mock types match
   - Use proper type assertions

2. **Timeout Issues**
   - Increase Jest timeout
   - Check for missing async/await
   - Verify cleanup in afterEach

3. **Mock Conflicts**
   - Clear mocks between tests
   - Use jest.isolateModules
   - Check mock restoration

## Code Style Guidelines

### General Conventions

- Imports: Group external/internal, use aliases (@/*for src/*)
- Types: Strong typing with TypeScript interfaces in dedicated files
- Error handling: Try/catch with specific error types and retry mechanisms
- Naming: PascalCase (Components, Types), camelCase (functions, variables)

### Frontend Guidelines

- Components: React functional components with prop interfaces
- State management: Use React hooks and context where appropriate
- Styling: Use Tailwind CSS for styling components. Component-specific styles are organized in `src/frontend/styles/components/` and imported centrally.
- UI components: Follow established patterns in the codebase.

#### Adaptive Responsive Design

The application employs a modern, adaptive approach to responsive design, moving away from device detection and separate mobile/desktop components. Key principles include:

- **Unified Components:** Components are designed to work across all screen sizes, adapting their layout based on available space rather than device type. The `useMobileView` hook and device-specific conditional rendering have been removed.
- **Container Queries:** Used extensively for component-level adaptivity, allowing components like `TransactionCard` and `ChartTooltip` to adjust their internal layout based on their container's width.
- **Fluid Typography & Spacing:** Utilizes CSS `clamp()` for fluid text sizes and spacing units (`--text-sm`, `--space-md`, etc.) that scale smoothly with the viewport.
- **Adaptive Layout Primitives:** Core layout components like `AdaptiveGrid`, `Stack`, and `ResponsiveContainer` provide flexible and adaptive structures.
- **Strategic Media Queries:** Media queries are used primarily for major layout shifts, such as switching between card and table views for `TransactionsList` and `DecisionTable` at specific breakpoints (e.g., 1024px).
- **Capability-Based Design:** Focuses on detecting features (touch support, screen real estate) rather than specific devices.

This approach simplifies the codebase, improves maintainability by having a single source of truth for components, and provides a more consistent user experience across devices. Refer to component-specific CSS files for container query implementations.

### Backend Guidelines

- Architecture: Controller-based API, Service layer, Repository pattern
- Error Handling: Utilize the standardized error handling utilities found in `src/utils/error.ts`. Refer to `src/utils/README.md` for detailed usage and patterns. Avoid custom try/catch blocks where the standard utilities suffice.
- Authentication: Properly validate and handle authentication tokens
- Data validation: Validate request data before processing

### UI Guidelines

- **Green**: ONLY for income/positive transactions
- **Red/accent**: ONLY for expenses/negative values
- Financial icons: Dollar sign (balance), chart (projections), arrows (income/expense)
- Color-code categories: Payroll=blue, Software=purple, Taxes=indigo
- Color-code frequencies: Weekly=blue, Monthly=violet, Quarterly=orange

## Project Architecture

### Frontend Structure

```
src/frontend/
├── api/          # Frontend API clients
├── components/   # React components
│   ├── App.tsx   # Main application component
│   ├── AuthScreen.tsx  # Authentication screen
│   ├── ForwardProjection/  # Cashflow projection components
│   │   ├── CashflowChart.tsx
│   │   ├── ProjectedInvoices.tsx
│   │   ├── ProjectionSettings.tsx
│   │   └── ...
│   └── ...
├── events/       # Event handling
├── hooks/        # Custom React hooks
├── styles/       # CSS styles
└── types/        # TypeScript type definitions
```

### Backend Structure

```
src/api/
├── controllers/    # Request handlers
├── integrations/   # External API clients
├── repositories/   # Data access layer
├── routes/         # API routes definitions
├── services/       # Business logic services
├── types/          # TypeScript type definitions
└── server.ts       # Express server setup

src/services/
├── cashflow/       # Cashflow related services (refactored)
│   ├── daily-cashflow-service.ts # Core daily balance calculation
│   ├── projection-filter-service.ts # Smart Forecast filtering logic
│   ├── transaction-service.ts # Data transformation
│   └── ...
├── xero/           # Xero integration services
└── harvest/        # Harvest integration services
```

### Data Flow

1. Frontend components make API requests through frontend API clients
2. Requests are routed through Express routes to controllers
3. Controllers use services (e.g., from `src/services/cashflow/`, `src/services/xero/`) to process business logic
4. Services may use repositories to access persistent data
5. Integration services communicate with external APIs (Xero, Harvest)
6. Data flows back through the stack to the frontend components

## Smart Forecast System

### System Overview

The Smart Forecast system automatically generates projected income based on:

- Remaining project budgets from Harvest
- Uninvoiced work already completed
- Outstanding invoices already sent to clients
- Project-specific invoice frequency configurations
- Payment terms settings
- Project status (active vs. inactive)

This approach provides a more accurate and automated way to project future income without requiring manual draft invoice creation.

### Core Projection Principles

The Smart Forecast projection system works on the following key principles:

1. **Retrospective Billing Model**: Invoices are generated *after* work periods are complete, not in advance. The first invoice is scheduled after the first work period (start date + invoice frequency).

2. **Budget Distribution**: Remaining project budget is evenly distributed across all invoice periods through project completion.

3. **Distinct Project Settings**: Each project maintains its own invoice frequency (weekly/biweekly/monthly) and payment terms (7/14/20/30 days).

4. **Timeline Alignment**: The system respects project start and end dates, aligning the final invoice with the project end date.

5. **Projected vs. Real Invoices**: The system distinguishes between projected (future) and real (outstanding) invoices to avoid duplication.

### Invoice Generation Algorithm

The system calculates projected invoices for each project using the following logic:

1. **Initialization**:
   - Retrieve project data (name, client, budget, timeline)
   - Get project settings (invoice frequency, payment terms)
   - Determine uninvoiced amounts for work already completed

2. **Calculate Invoice Dates**:
   - **First Invoice**: Project start date + invoice frequency (representing first complete work period)
   - **Subsequent Invoices**: Each previous invoice date + invoice frequency
   - **Final Invoice**: Aligned with project end date

3. **Calculate Invoice Amounts**:
   - Divide remaining budget evenly across all projected invoices
   - For the first invoice, add any uninvoiced work amounts

4. **Calculate Payment Dates**:
   - For each invoice date, add the project's payment terms to determine when payment is expected

5. **Filter Out Invalid Projections**:
   - Remove historical invoice projections (before today)
   - Exclude projections for completed projects
   - Filter out projections that would already have been invoiced

### Filtering Rules

The system applies several important filtering rules to ensure accuracy:

1. **Payment Terms Filter**:
   - Never show a projected invoice payment if it falls within the payment terms period from today
   - Example: If today is March 16 and payment terms are 20 days, exclude any projected payments before April 5
   - This prevents projecting income that should already have been invoiced and prevents double-counting

2. **Real Invoice Duplicate Check**:
   - Exclude projected invoices when a real invoice from the same project exists within 5 days
   - This prevents showing both the projected invoice and the real invoice it was replaced with
   - Recently created invoices (created within last 5 days) are visually indicated with a blue dot

3. **Timeframe Filtering**:
   - Only show projected invoices within the selected timeframe (30/60/90 days)
   - This keeps the projection focused on the relevant time period

### Smart Forecast Decisions (Projection Audit Log)

The Smart Forecast Decisions (Projection Audit Log) is a transparency feature that allows users to understand exactly how the projection system makes filtering decisions.

#### Audit Log Features

1. **Filtering Rules Visualization**: Shows the three primary filtering rules with counts:
   - **Uninvoiced Work Rule**: Uninvoiced work excluded when projected income exists within ±3 days
   - **Real Invoice Rule**: Projected income excluded when real invoice exists within ±5 days. Any exclusion with a related invoice is automatically categorized under this rule.
   - **Payment Terms Rule**: Projected income excluded if within payment terms from today

2. **Decision Log**: Detailed chronological list of all projection filtering decisions, including:
   - Project name and client name
   - Invoice type (Projected Income or Uninvoiced Work)
   - Invoice and payment dates
   - Amount
   - Status (Included or Excluded)
   - Reason for the decision
   - Related invoice information (when applicable)

3. **Interactive Filtering**: Users can:
   - Filter by status (All, Included, Excluded, Uninvoiced)
   - Search by project name, client name, or exclusion reason
   - View expandable reconciliation details for excluded items
   - See direct links to associated Harvest invoices

4. **Uninvoiced Work Tracking**: Special handling for uninvoiced work:
   - Dedicated "Uninvoiced" tab to filter specifically for uninvoiced work decisions
   - Both included and excluded uninvoiced work items are shown
   - For excluded uninvoiced work, the log shows which projected income replaced it
   - Cyan styling consistently identifies uninvoiced work across the application

#### Technical Implementation

The Projection Audit Log is implemented through the following components:

1. **Backend Filtering Logic**:
   - In `src/services/cashflow/projection-filter-service.ts`, the `filterAlreadyInvoicedProjections` method makes decisions about which projections to include or exclude
   - Each decision is logged with detailed information using the `emitFilterEvent` function within the same service
   - The controller in `src/api/routes/cashflow.ts` captures these events and passes them to the frontend

2. **API Response Enhancement**:
   - The `getForwardProjection` endpoint in `cashflow.ts` captures filter decisions during projection generation
   - Filter decisions are included in the API response as a `filterDecisions` array

3. **Frontend Visualization**:
   - The `ProjectionAuditPage` component in `ProjectionAuditPage.tsx` displays the filtering decisions
   - The component organizes decisions by rules and provides interactive filtering
   - Data is presented in both summary form (rule cards) and detail form (decision table)

## Database & Data Model

The application uses a unified data model with a proper versioned migration system and repository pattern for data access.

### Migration System

The application implements a modern migration system with the following features:

- **Versioned migrations** with timestamped IDs
- **Up/down methods** for safe rollback capability
- **CLI tools** for migration management
- **Automatic execution** during deployment

#### Migration Commands

```bash
# Run pending migrations
npm run migrate

# Check migration status
npm run migrate:status

# Rollback last batch of migrations
npm run migrate:rollback
```

#### Creating New Migrations

1. **Generate migration ID**:
   ```typescript
   import { createMigrationId } from './src/database/migrations/index';
   const id = createMigrationId(); // e.g., "20241201_143022"
   ```

2. **Create migration file**:
   ```typescript
   // src/database/migrations/20241201_143022_description.ts
   export const myMigration: Migration = {
     id: '20241201_143022',
     name: 'add_new_feature',
     
     up: (db: BetterSqlite3.Database) => {
       // Forward migration logic
       db.exec(`CREATE TABLE new_feature (...)`);
     },
     
     down: (db: BetterSqlite3.Database) => {
       // Rollback migration logic
       db.exec('DROP TABLE IF EXISTS new_feature');
     }
   };
   ```

3. **Test migration**:
   ```bash
   npm run migrate        # Apply
   npm run migrate:rollback  # Test rollback
   npm run migrate        # Re-apply
   ```

### Repository Pattern

The application uses a three-tier repository pattern:

#### Base Repository
- **SQL injection protection** with table/column validation
- **Transaction management** with automatic rollback
- **Error handling** with comprehensive logging
- **Common CRUD operations** for all entities

#### Entity Repositories
- `CompanyRepository` - Company management with external system linking
- `ContactRepository` - Contact management with relationship queries
- `DealRepository` - Deal lifecycle with validation and tracking
- `ActivityRepository` - Activity logging and retrieval
- `NoteRepository` - Polymorphic note management

#### Relationship Repositories
- `ContactRoleRepository` - Contact roles in deals
- `DealEstimateRepository` - Deal-estimate linking
- `ContactCompanyRepository` - Contact-company associations
- `CompanyRelationshipRepository` - Company hierarchies

#### Specialized Repositories
- `KnowledgeGraphRepository` - Network visualization data aggregation (does not extend BaseRepository)
- `EnrichmentRepository` - External data enrichment storage (ABN Lookup, etc.)

#### Usage Example

```typescript
import { CompanyRepository } from '../api/repositories/company-repository';

const companyRepo = new CompanyRepository();

// Create company with audit trail
const company = companyRepo.create({
  name: 'New Company',
  source: 'Manual',
  createdBy: 'user-id'
});

// Get with relationships
const companyWithDeals = companyRepo.getCompanyById(company.id);
```

### Schema Management

The database schema is organized into domain-specific modules:

- **`crm.ts`** - Core CRM entities (company, contact, deal)
- **`financial.ts`** - Financial tracking (expense, cashflow_snapshot)
- **`estimates.ts`** - Project estimates and allocations
- **`audit.ts`** - Activity tracking and change logs
- **`integration.ts`** - External system integration tables

#### Current Schema Overview

**21 tables** organized by domain:
- **Core CRM**: company, contact, deal, note
- **Relationships**: contact_company, contact_role, deal_estimate
- **Financial**: expense, cashflow_snapshot, estimate_allocation
- **Audit**: activity_feed, field_ownership, change_log
- **Integration**: hubspot_settings, harvest_invoice_cache, settings

**63+ strategic indexes** for optimal performance on all query patterns.

### Data Model Documentation

For comprehensive data model documentation, see:

- [`docs/technical/data-model/README.md`](docs/technical/data-model/README.md) - Overview and implementation status
- [`docs/technical/data-model/CLEAN-SLATE-DATA-MODEL-ANALYSIS.md`](docs/technical/data-model/CLEAN-SLATE-DATA-MODEL-ANALYSIS.md) - Complete schema design rationale
- [`docs/technical/data-model/repository-pattern.md`](docs/technical/data-model/repository-pattern.md) - Repository implementation guide
- [`docs/technical/data-model/database-initialization.md`](docs/technical/data-model/database-initialization.md) - Database setup and migration system
- [`migrations/000_unified_schema.sql`](migrations/000_unified_schema.sql) - Complete database schema definition (31 tables)

#### External System Integration

The data model supports integration with multiple external systems:

- **HubSpot**: OAuth-based with association validation and field ownership tracking
- **Harvest**: Invoice caching with 6-hour TTL and project budget integration
- **Xero**: Comprehensive API coverage with rate limiting and error recovery

#### Data Validation

All repositories include:
- **Table existence checking** to prevent runtime errors
- **Column validation** to ensure queries match schema
- **Type safety** with TypeScript interfaces
- **Audit trail** for all changes with user attribution

#### Production Deployment

Database changes are deployed safely through:
- **Automatic migration execution** during application startup
- **Graceful degradation** if migrations fail
- **Transaction safety** with automatic rollback on errors
- **Backward compatibility** with legacy database states

## Common Development Tasks

### Adding a New API Endpoint

1. Create a new route file or add to existing file in `src/api/routes/`
2. Implement controller functions in `src/api/controllers/`
3. Add any needed service functions in `src/services/`
4. Create corresponding frontend API client function in `src/frontend/api/`
5. Add tests for the new endpoint

### Adding a New UI Component

1. Create component file in `src/frontend/components/`
2. Add styles using Tailwind CSS classes
3. Implement component functionality
4. Create tests in `tests/unit/frontend/components/`
5. Use the component in the application

### Working with Knowledge Graph

The Knowledge Graph provides network visualization of business relationships:

1. **Adding entities to the graph**:
   - Entities are automatically included from existing repositories
   - Supported types: company, contact, deal, project, estimate
   
2. **Customizing visualization**:
   - Update `KnowledgeGraph.tsx` for visual styling
   - Modify `knowledge-graph-repository.ts` for data aggregation
   
3. **Performance considerations**:
   - Default limit is 1000 nodes (configurable via `maxNodes`)
   - Use entity type filtering to reduce data volume
   - Subgraph queries available for focused views

### Working with Data Enrichment

The data enrichment system allows augmenting entities with external data:

1. **Adding a new enrichment source**:
   ```typescript
   // Create service in src/api/services/enrichment/
   export class NewSourceService extends BaseEnrichmentService {
     async enrich(entity: Company): Promise<EnrichmentData> {
       // Implement enrichment logic
     }
   }
   ```

2. **Storing enriched data**:
   - Use `EnrichmentRepository` to store results
   - Data stored as JSON in `company_enrichment` or `contact_enrichment` tables
   - Include confidence scores and expiration dates

3. **Current enrichment sources**:
   - ABN Lookup (Australian Business Number lookup)
   - Additional sources can be added following the same pattern

### Working with Environment Variables

For detailed information about environment variables, configuration, and deployment, please refer to the [DEPLOYMENT-GUIDE.md](DEPLOYMENT-GUIDE.md) document.

For local development:

1. Create a `.env` file by copying `.env.example`
2. Fill in your actual API credentials and secrets
3. NEVER commit this file (it's already in .gitignore)

## Troubleshooting

### Common Development Issues

#### Port Conflicts

If you encounter "Port already in use" errors:

1. Run the kill-ports script: `./kill-ports.sh`
2. Manually kill processes: `lsof -ti:3002,5173 | xargs kill -9`

#### Build Issues

If you encounter build errors:

1. Check for TypeScript errors: `npm run tsc`
2. Ensure all dependencies are installed: `npm ci`
3. Clear node_modules and reinstall if needed: `rm -rf node_modules && npm ci`

#### Build and Deployment Issues

For issues related to building and deploying the application, please refer to the [DEPLOYMENT-GUIDE.md](DEPLOYMENT-GUIDE.md) document.

### Smart Forecast Issues

#### Missing Project Data

- Check that project has a budget amount set in Harvest
- Verify project is active in Harvest (archived projects are excluded)
- Ensure project settings are configured in the dashboard
- Check the project has time entries or budget data in Harvest
- For future projects, verify both start date and budget are properly set in Harvest
- Try refreshing the project data using the refresh button
- Note that by default, only Active projects are shown in the settings table - check the status filter

#### Projection Accuracy

- Review invoice frequency settings for each project
- Adjust payment terms to match typical client payment behavior
- Consider setting more realistic project start and end dates in Harvest
- Check that uninvoiced amounts are correctly calculated
- Verify that project budgets in Harvest match expected values
- For future projects, ensure start dates are correctly set in Harvest
- Ensure outstanding invoices are properly displaying (they are fetched with direct API calls)
- Check that all project status values in Harvest are correctly set (Active/Inactive)
- Verify that remaining budget (Budget - Uninvoiced amount) is greater than zero

#### Smart Forecast Decisions Issues

- If entries are incorrectly categorized under Rule 1 (Uninvoiced Work) instead of Rule 2 (Real Invoice):
  - Check if each entry has the `relatedInvoice` property, which should automatically place it under Rule 2
  - Entries with links to real invoices should always be categorized under Rule 2
  - Look for improper source identification ('harvest' vs 'Harvest') - Note: Source is normalized in `projection-filter-service.ts`.
  - Examine the decision normalization logic in `src/frontend/components/ForwardProjection/utils/projection-audit-utils.ts` which determines rule categorization

- If uninvoiced work items appear in the cashflow projection but not in the Decision Log:
  - Check that the filter events are being properly accumulated and not overwritten
  - In `src/services/cashflow/projection-filter-service.ts`, verify that the `emitFilterEvent` method stores events in the `window.__projectionEvents` array
  - Look at browser console logs for "PROJECTION_FILTERED" messages containing the event data
  - Verify in `src/frontend/api/cashflow.ts` that all events from `window.__projectionEvents` are added to the `enhancedDecisions` array
  - In `src/frontend/components/ForwardProjection/hooks/useProjectionAudit.ts`, check that the fallback mechanism is capturing uninvoiced work transactions
  - Ensure tab filtering in `src/frontend/components/ForwardProjection/ProjectionAudit/DecisionTable.tsx` properly shows all decisions (verify counts in UI with actual array lengths)
  
- If any items are missing from the Decision log or tab counts are inconsistent:
  - Check browser console for any errors related to event processing
  - Verify that events are being properly emitted in `src/services/cashflow/projection-filter-service.ts` (look for "PROJECTION_FILTERED" logs)
  - Ensure the `PROJECTION_FILTERED` event is being correctly published and received (check `window.__projectionEvents` and `window.__dispatchProjectionEvent` usage)
  - See if `window.__projectionEvents` array contains expected decisions
  - Check that filtering isn't being applied redundantly in `src/frontend/components/ForwardProjection/ProjectionAudit/ProjectionAuditPage.tsx` and `DecisionTable.tsx`
  - Verify that tab counts match the actual number of decisions in each category
  - Look for detailed logging like "FILTER DECISION STATS" in the console
  - For troubleshooting, you can bypass the filtering in `ProjectionAuditPage.tsx` by directly passing all decisions to the table component

#### Common Misunderstandings

- Projections don't show invoices within the payment terms period (this is by design)
- First invoice is always after the first completed work period, not on project start date
- Real invoices replace projected invoices (marked with a blue dot when recently created)
- Projected invoices follow a specific frequency pattern from the start date, which may not align with calendar months
- Projections are based on remaining budget distributed across remaining time, not historical invoice patterns
- Only Active projects are shown by default in the settings table
- In the Smart Forecast Decisions page, all entries with a related invoice are automatically categorized under Rule 2 (Real Invoice Rule), regardless of their original invoice type

### API Rate Limiting

The application includes robust handling for API rate limits:

- If you see "API rate limit exceeded" or "Request has been throttled" errors:
  - The application will gracefully handle rate limits without failing completely
  - The system will continue to work with partial data rather than crashing
  - Enhanced error handling ensures meaningful error messages are displayed
  - If rate limiting persists, the application will automatically adjust and continue with available data
  - Harvest API rate limits are handled with graceful degradation, returning empty results when throttled

### Authentication Issues

If you're having Xero authentication problems:

1. Ensure the application is running on the correct ports:
   - In development: API on port 3002, Frontend on port 5173
   - In production (Render): Your Render app URL using HTTPS

2. Verify your Xero client ID and client secret in the environment file or Render environment variables

3. Make sure the callback URL in the Xero Developer Portal exactly matches your environment:
   - Development: `http://localhost:3002/api/xero/callback`
   - Production (Render): `https://your-render-app-name.onrender.com/api/xero/callback`

4. If you see "Token Refresh Failed" errors:
   - Try re-authenticating by clicking the "Connect to Xero" button
   - Xero tokens expire after 60 days, requiring re-authentication

5. If you need to force re-authentication:
   - Click the Logout button in the top-right corner of the dashboard
   - You'll be returned to the authentication screen
   - Click "Connect to Xero" to start a fresh authentication flow
