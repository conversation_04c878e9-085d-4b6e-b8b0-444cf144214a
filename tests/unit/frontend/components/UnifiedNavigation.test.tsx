import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import { UnifiedNavigation } from '../../../../src/frontend/components/Navigation/UnifiedNavigation';
import { UserContext } from '../../../../src/frontend/contexts/UserContext';

describe('UnifiedNavigation', () => {
  const mockUser = {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin'
  };

  const renderWithRouter = (initialRoute = '/') => {
    return render(
      <MemoryRouter initialEntries={[initialRoute]}>
        <UserContext.Provider value={{ user: mockUser, loading: false, setUser: jest.fn() }}>
          <Routes>
            <Route path="*" element={<UnifiedNavigation />} />
          </Routes>
        </UserContext.Provider>
      </MemoryRouter>
    );
  };

  it('should render navigation menu', () => {
    renderWithRouter();

    expect(screen.getByRole('navigation')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Cash Flow')).toBeInTheDocument();
    expect(screen.getByText('CRM')).toBeInTheDocument();
    expect(screen.getByText('Estimates')).toBeInTheDocument();
  });

  it('should highlight active route', () => {
    renderWithRouter('/cashflow');

    const cashFlowLink = screen.getByText('Cash Flow').closest('a');
    expect(cashFlowLink).toHaveClass('active');
  });

  it('should navigate on link click', () => {
    renderWithRouter();

    const crmLink = screen.getByText('CRM');
    fireEvent.click(crmLink);

    expect(window.location.pathname).toBe('/');
  });

  it('should show user menu when authenticated', () => {
    renderWithRouter();

    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should show mobile menu toggle on small screens', () => {
    renderWithRouter();

    const menuButton = screen.getByLabelText('Toggle menu');
    expect(menuButton).toBeInTheDocument();

    fireEvent.click(menuButton);
    expect(screen.getByRole('navigation')).toHaveClass('mobile-open');
  });

  it('should close mobile menu on navigation', () => {
    renderWithRouter();

    const menuButton = screen.getByLabelText('Toggle menu');
    fireEvent.click(menuButton);

    const crmLink = screen.getByText('CRM');
    fireEvent.click(crmLink);

    expect(screen.getByRole('navigation')).not.toHaveClass('mobile-open');
  });

  it('should render sub-navigation for CRM', () => {
    renderWithRouter('/crm');

    expect(screen.getByText('Pipeline')).toBeInTheDocument();
    expect(screen.getByText('Companies')).toBeInTheDocument();
    expect(screen.getByText('Contacts')).toBeInTheDocument();
  });

  it('should show notification badge for updates', () => {
    renderWithRouter();

    const notificationBadge = screen.getByTestId('notification-badge');
    expect(notificationBadge).toBeInTheDocument();
    expect(notificationBadge).toHaveTextContent('3');
  });

  it('should open command palette on shortcut', () => {
    renderWithRouter();

    fireEvent.keyDown(document, { key: 'k', metaKey: true });

    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('should render integration status indicators', () => {
    renderWithRouter();

    expect(screen.getByTestId('xero-status')).toBeInTheDocument();
    expect(screen.getByTestId('harvest-status')).toBeInTheDocument();
    expect(screen.getByTestId('hubspot-status')).toBeInTheDocument();
  });

  it('should collapse navigation on toggle', () => {
    renderWithRouter();

    const collapseButton = screen.getByLabelText('Collapse navigation');
    fireEvent.click(collapseButton);

    expect(screen.getByRole('navigation')).toHaveClass('collapsed');
  });

  it('should persist navigation state', () => {
    const { rerender } = renderWithRouter();

    const collapseButton = screen.getByLabelText('Collapse navigation');
    fireEvent.click(collapseButton);

    // Simulate component remount
    rerender(
      <MemoryRouter>
        <UserContext.Provider value={{ user: mockUser, loading: false, setUser: jest.fn() }}>
          <UnifiedNavigation />
        </UserContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByRole('navigation')).toHaveClass('collapsed');
  });

  it('should show role-based menu items', () => {
    const adminUser = { ...mockUser, role: 'admin' };
    
    render(
      <MemoryRouter>
        <UserContext.Provider value={{ user: adminUser, loading: false, setUser: jest.fn() }}>
          <UnifiedNavigation />
        </UserContext.Provider>
      </MemoryRouter>
    );

    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });

  it('should hide admin items for regular users', () => {
    const regularUser = { ...mockUser, role: 'user' };
    
    render(
      <MemoryRouter>
        <UserContext.Provider value={{ user: regularUser, loading: false, setUser: jest.fn() }}>
          <UnifiedNavigation />
        </UserContext.Provider>
      </MemoryRouter>
    );

    expect(screen.queryByText('Admin')).not.toBeInTheDocument();
  });

  it('should handle logout action', async () => {
    const mockLogout = jest.fn();
    global.fetch = jest.fn(() => Promise.resolve({ ok: true } as Response));

    renderWithRouter();

    const userMenu = screen.getByText('Test User');
    fireEvent.click(userMenu);

    const logoutButton = screen.getByText('Logout');
    fireEvent.click(logoutButton);

    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/auth/logout', expect.any(Object));
    });
  });
});