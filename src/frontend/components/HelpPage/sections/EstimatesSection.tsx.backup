import React from 'react';

/**
 * Estimates section content for the help page
 */
export const EstimatesSection: React.FC = () => {
  return (
    <>
      <p>
        Create detailed project estimates with staff allocations, visual planning tools,
        and automatic pricing calculations. Save drafts in Upstream and publish to Harvest
        when ready for client presentation.
      </p>
      
      <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-4">
        Estimation Process
      </h3>
      <div className="space-y-3 mt-2">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
          <p className="text-sm">Create new estimate with client and project details</p>
        </div>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
          <p className="text-sm">Add staff members and allocate time across project phases</p>
        </div>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
          <p className="text-sm">Review visual timeline and automatic cost calculations</p>
        </div>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">4</div>
          <p className="text-sm">Save as draft or publish directly to Harvest</p>
        </div>
      </div>

      <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <h4 className="font-medium text-green-800 dark:text-green-300 mb-2">
          ✨ New Feature
        </h4>
        <p className="text-sm text-green-700 dark:text-green-300">
          Estimates can now be linked to CRM deals for better project tracking and sales pipeline management.
        </p>
      </div>
    </>
  );
};