/**
 * Utilities for testing with real API data
 */
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Try to load test environment variables from .env.test if it exists
const envTestPath = path.resolve(process.cwd(), '.env.test');
if (fs.existsSync(envTestPath)) {
  dotenv.config({ path: envTestPath });
  console.log('Loaded test environment variables from .env.test');
} else {
  // Fall back to regular .env file
  dotenv.config();
  console.log('No .env.test found, using default .env file');
}

/**
 * Check if test environment is properly configured
 */
export function isTestEnvironmentConfigured(): boolean {
  const requiredVars = [
    'XERO_CLIENT_ID', 
    'XERO_CLIENT_SECRET',
    'HARVEST_ACCESS_TOKEN',
    'HARVEST_ACCOUNT_ID'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn(`Missing required environment variables for testing: ${missingVars.join(', ')}`);
    return false;
  }
  
  return true;
}

/**
 * Skip tests that require a working API connection if environment is not configured
 */
export function skipIfMissingEnv(jest: any): void {
  if (!isTestEnvironmentConfigured()) {
    jest.setTimeout(1);
    jest.beforeEach(() => {
      // Skip all tests in the file if environment is not configured
      throw new Error('API credentials not configured. Skipping tests. Create a .env.test file with API credentials.');
    });
  }
}

/**
 * Creates a safe test date range (last 30 days to next 90 days)
 */
export function getTestDateRange() {
  const now = new Date();
  
  // Start 30 days ago
  const startDate = new Date(now);
  startDate.setDate(now.getDate() - 30);
  
  // End 90 days from now
  const endDate = new Date(now);
  endDate.setDate(now.getDate() + 90);
  
  return { startDate, endDate, now };
}
