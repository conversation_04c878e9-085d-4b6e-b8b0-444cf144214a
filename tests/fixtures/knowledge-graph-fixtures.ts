/**
 * Knowledge Graph Test Fixtures
 * 
 * Centralized test data for knowledge graph testing.
 */
import { v4 as uuidv4 } from 'uuid';

export interface KnowledgeGraphTestData {
  companies: Array<{
    id: string;
    name: string;
    industry?: string;
    size?: string;
    website?: string;
  }>;
  contacts: Array<{
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    job_title?: string;
  }>;
  deals: Array<{
    id: string;
    name: string;
    company_id: string;
    stage: string;
    value: number;
    probability: number;
  }>;
  projects: Array<{
    id: string;
    name: string;
    company_id: string;
    deal_id?: string;
    status: string;
    budget?: number;
  }>;
  estimates: Array<{
    uuid: string;
    clientId: string;
    clientName: string;
    projectName: string;
    startDate: string;
    endDate: string;
    userId: string;
    allocations: Array<{
      harvestUserId: string;
      firstName: string;
      lastName: string;
      onbordTargetRateDaily: number;
      onbordCostRateDaily: number;
      rateProposedDaily: number;
      weeklyAllocation: Record<string, number>;
    }>;
  }>;
  relationships: {
    companyRelationships: Array<{
      parent_company_id: string;
      child_company_id: string;
      relationship_type: string;
    }>;
    contactCompanyRelationships: Array<{
      contact_id: string;
      company_id: string;
      role: string;
      is_primary: boolean;
    }>;
    contactRoles: Array<{
      deal_id: string;
      contact_id: string;
      role: string;
    }>;
    dealEstimates: Array<{
      deal_id: string;
      estimate_id: string;
      is_primary: boolean;
    }>;
    projectContacts: Array<{
      project_id: string;
      contact_id: string;
      role: string;
      allocation_percentage?: number;
    }>;
  };
}

export function createKnowledgeGraphTestData(): KnowledgeGraphTestData {
  // Generate company IDs
  const acmeCorpId = uuidv4();
  const megaHoldingsId = uuidv4();
  const techStartupId = uuidv4();
  const consultingFirmId = uuidv4();
  
  // Generate contact IDs
  const johnCeoId = uuidv4();
  const janeCtoId = uuidv4();
  const bobManagerId = uuidv4();
  const aliceDevId = uuidv4();
  const charlieSalesId = uuidv4();
  
  // Generate deal IDs
  const enterpriseDealId = uuidv4();
  const startupDealId = uuidv4();
  const consultingDealId = uuidv4();
  
  // Generate project IDs
  const digitalTransformId = uuidv4();
  const websiteRebuildId = uuidv4();
  const mobileAppId = uuidv4();
  
  // Generate estimate IDs
  const enterpriseEstimateId = uuidv4();
  const startupEstimateId = uuidv4();
  const consultingEstimateId = uuidv4();
  
  return {
    companies: [
      {
        id: acmeCorpId,
        name: 'Acme Corporation',
        industry: 'Technology',
        size: '100-500',
        website: 'https://acme.com'
      },
      {
        id: megaHoldingsId,
        name: 'Mega Holdings',
        industry: 'Investment',
        size: '1000+'
      },
      {
        id: techStartupId,
        name: 'Tech Startup Inc',
        industry: 'Software',
        size: '10-50'
      },
      {
        id: consultingFirmId,
        name: 'Elite Consulting',
        industry: 'Consulting',
        size: '50-100'
      }
    ],
    
    contacts: [
      {
        id: johnCeoId,
        first_name: 'John',
        last_name: 'Smith',
        email: '<EMAIL>',
        job_title: 'CEO'
      },
      {
        id: janeCtoId,
        first_name: 'Jane',
        last_name: 'Doe',
        email: '<EMAIL>',
        job_title: 'CTO'
      },
      {
        id: bobManagerId,
        first_name: 'Bob',
        last_name: 'Johnson',
        email: '<EMAIL>',
        job_title: 'Product Manager'
      },
      {
        id: aliceDevId,
        first_name: 'Alice',
        last_name: 'Williams',
        email: '<EMAIL>',
        job_title: 'Lead Developer'
      },
      {
        id: charlieSalesId,
        first_name: 'Charlie',
        last_name: 'Brown',
        email: '<EMAIL>',
        job_title: 'Sales Director'
      }
    ],
    
    deals: [
      {
        id: enterpriseDealId,
        name: 'Enterprise Software Solution',
        company_id: acmeCorpId,
        stage: 'negotiation',
        value: 500000,
        probability: 0.8
      },
      {
        id: startupDealId,
        name: 'Mobile App Development',
        company_id: techStartupId,
        stage: 'proposal',
        value: 150000,
        probability: 0.6
      },
      {
        id: consultingDealId,
        name: 'Digital Strategy Consulting',
        company_id: consultingFirmId,
        stage: 'qualified',
        value: 75000,
        probability: 0.4
      }
    ],
    
    projects: [
      {
        id: digitalTransformId,
        name: 'Digital Transformation Initiative',
        company_id: acmeCorpId,
        deal_id: enterpriseDealId,
        status: 'active',
        budget: 400000
      },
      {
        id: websiteRebuildId,
        name: 'Website Rebuild',
        company_id: acmeCorpId,
        status: 'planning',
        budget: 50000
      },
      {
        id: mobileAppId,
        name: 'iOS/Android App',
        company_id: techStartupId,
        deal_id: startupDealId,
        status: 'discovery',
        budget: 120000
      }
    ],
    
    estimates: [
      {
        uuid: enterpriseEstimateId,
        clientId: 'harvest-123',
        clientName: 'Acme Corporation',
        projectName: 'Enterprise Software Solution',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        userId: 'test-user',
        allocations: [
          {
            harvestUserId: 'harvest-senior-dev',
            firstName: 'Senior',
            lastName: 'Developer',
            onbordTargetRateDaily: 1200,
            onbordCostRateDaily: 800,
            rateProposedDaily: 1300,
            weeklyAllocation: {
              '2024-W01': 5,
              '2024-W02': 5,
              '2024-W03': 4,
              '2024-W04': 5
            }
          },
          {
            harvestUserId: 'harvest-project-manager',
            firstName: 'Project',
            lastName: 'Manager',
            onbordTargetRateDaily: 1000,
            onbordCostRateDaily: 700,
            rateProposedDaily: 1100,
            weeklyAllocation: {
              '2024-W01': 3,
              '2024-W02': 3,
              '2024-W03': 3,
              '2024-W04': 3
            }
          }
        ]
      },
      {
        uuid: startupEstimateId,
        clientId: 'harvest-456',
        clientName: 'Tech Startup Inc',
        projectName: 'Mobile App Development',
        startDate: '2024-03-01',
        endDate: '2024-08-31',
        userId: 'test-user',
        allocations: [
          {
            harvestUserId: 'harvest-mobile-dev',
            firstName: 'Mobile',
            lastName: 'Developer',
            onbordTargetRateDaily: 1000,
            onbordCostRateDaily: 650,
            rateProposedDaily: 1100,
            weeklyAllocation: {
              '2024-W09': 5,
              '2024-W10': 5,
              '2024-W11': 4
            }
          }
        ]
      },
      {
        uuid: consultingEstimateId,
        clientId: 'harvest-789',
        clientName: 'Elite Consulting',
        projectName: 'Digital Strategy Consulting',
        startDate: '2024-02-01',
        endDate: '2024-05-31',
        userId: 'test-user',
        allocations: [
          {
            harvestUserId: 'harvest-consultant',
            firstName: 'Strategy',
            lastName: 'Consultant',
            onbordTargetRateDaily: 1500,
            onbordCostRateDaily: 1000,
            rateProposedDaily: 1600,
            weeklyAllocation: {
              '2024-W05': 3,
              '2024-W06': 3,
              '2024-W07': 3
            }
          }
        ]
      }
    ],
    
    relationships: {
      companyRelationships: [
        {
          parent_company_id: megaHoldingsId,
          child_company_id: acmeCorpId,
          relationship_type: 'subsidiary'
        },
        {
          parent_company_id: acmeCorpId,
          child_company_id: techStartupId,
          relationship_type: 'investment'
        }
      ],
      
      contactCompanyRelationships: [
        {
          contact_id: johnCeoId,
          company_id: acmeCorpId,
          role: 'CEO',
          is_primary: true
        },
        {
          contact_id: janeCtoId,
          company_id: acmeCorpId,
          role: 'CTO',
          is_primary: false
        },
        {
          contact_id: bobManagerId,
          company_id: techStartupId,
          role: 'Product Manager',
          is_primary: true
        },
        {
          contact_id: aliceDevId,
          company_id: techStartupId,
          role: 'Lead Developer',
          is_primary: false
        },
        {
          contact_id: charlieSalesId,
          company_id: consultingFirmId,
          role: 'Sales Director',
          is_primary: true
        }
      ],
      
      contactRoles: [
        {
          deal_id: enterpriseDealId,
          contact_id: johnCeoId,
          role: 'decision_maker'
        },
        {
          deal_id: enterpriseDealId,
          contact_id: janeCtoId,
          role: 'technical_evaluator'
        },
        {
          deal_id: startupDealId,
          contact_id: bobManagerId,
          role: 'champion'
        },
        {
          deal_id: startupDealId,
          contact_id: aliceDevId,
          role: 'technical_evaluator'
        },
        {
          deal_id: consultingDealId,
          contact_id: charlieSalesId,
          role: 'decision_maker'
        }
      ],
      
      dealEstimates: [
        {
          deal_id: enterpriseDealId,
          estimate_id: enterpriseEstimateId,
          is_primary: true
        },
        {
          deal_id: startupDealId,
          estimate_id: startupEstimateId,
          is_primary: true
        },
        {
          deal_id: consultingDealId,
          estimate_id: consultingEstimateId,
          is_primary: true
        }
      ],
      
      projectContacts: [
        {
          project_id: digitalTransformId,
          contact_id: johnCeoId,
          role: 'sponsor',
          allocation_percentage: 25
        },
        {
          project_id: digitalTransformId,
          contact_id: janeCtoId,
          role: 'technical_lead',
          allocation_percentage: 75
        },
        {
          project_id: mobileAppId,
          contact_id: bobManagerId,
          role: 'product_owner',
          allocation_percentage: 50
        },
        {
          project_id: mobileAppId,
          contact_id: aliceDevId,
          role: 'lead_developer',
          allocation_percentage: 100
        }
      ]
    }
  };
}

export function createMinimalKnowledgeGraphData(): KnowledgeGraphTestData {
  const companyId = uuidv4();
  const contactId = uuidv4();
  const dealId = uuidv4();
  
  return {
    companies: [
      {
        id: companyId,
        name: 'Test Company',
        industry: 'Technology'
      }
    ],
    contacts: [
      {
        id: contactId,
        first_name: 'Test',
        last_name: 'Contact',
        email: '<EMAIL>',
        job_title: 'Manager'
      }
    ],
    deals: [
      {
        id: dealId,
        name: 'Test Deal',
        company_id: companyId,
        stage: 'proposal',
        value: 50000,
        probability: 0.5
      }
    ],
    projects: [],
    estimates: [],
    relationships: {
      companyRelationships: [],
      contactCompanyRelationships: [
        {
          contact_id: contactId,
          company_id: companyId,
          role: 'Manager',
          is_primary: true
        }
      ],
      contactRoles: [
        {
          deal_id: dealId,
          contact_id: contactId,
          role: 'decision_maker'
        }
      ],
      dealEstimates: [],
      projectContacts: []
    }
  };
}

export function createLargeKnowledgeGraphData(scale: number = 100): KnowledgeGraphTestData {
  const companies = Array.from({ length: scale }, (_, i) => ({
    id: uuidv4(),
    name: `Company ${i + 1}`,
    industry: ['Technology', 'Finance', 'Healthcare', 'Retail'][i % 4],
    size: ['1-10', '10-50', '50-200', '200+'][i % 4]
  }));
  
  const contacts = Array.from({ length: scale * 2 }, (_, i) => ({
    id: uuidv4(),
    first_name: `First${i + 1}`,
    last_name: `Last${i + 1}`,
    email: `contact${i + 1}@company.com`,
    job_title: ['CEO', 'CTO', 'Manager', 'Developer', 'Sales'][i % 5]
  }));
  
  const deals = Array.from({ length: scale }, (_, i) => ({
    id: uuidv4(),
    name: `Deal ${i + 1}`,
    company_id: companies[i % companies.length].id,
    stage: ['qualified', 'proposal', 'negotiation', 'closed_won'][i % 4],
    value: (i + 1) * 10000,
    probability: Math.random()
  }));
  
  // Create relationships
  const contactCompanyRelationships = contacts.slice(0, scale).map((contact, i) => ({
    contact_id: contact.id,
    company_id: companies[i % companies.length].id,
    role: ['CEO', 'Manager', 'Employee'][i % 3],
    is_primary: i % 2 === 0
  }));
  
  const contactRoles = deals.map((deal, i) => ({
    deal_id: deal.id,
    contact_id: contacts[i % contacts.length].id,
    role: ['decision_maker', 'champion', 'technical_evaluator'][i % 3]
  }));
  
  return {
    companies,
    contacts,
    deals,
    projects: [],
    estimates: [],
    relationships: {
      companyRelationships: [],
      contactCompanyRelationships,
      contactRoles,
      dealEstimates: [],
      projectContacts: []
    }
  };
}

export function createDisconnectedKnowledgeGraphData(): KnowledgeGraphTestData {
  // Create entities with no relationships between them
  return {
    companies: [
      { id: uuidv4(), name: 'Isolated Company 1', industry: 'Tech' },
      { id: uuidv4(), name: 'Isolated Company 2', industry: 'Finance' }
    ],
    contacts: [
      {
        id: uuidv4(),
        first_name: 'Isolated',
        last_name: 'Contact1',
        email: '<EMAIL>'
      },
      {
        id: uuidv4(),
        first_name: 'Isolated',
        last_name: 'Contact2',
        email: '<EMAIL>'
      }
    ],
    deals: [
      {
        id: uuidv4(),
        name: 'Isolated Deal 1',
        company_id: uuidv4(), // Non-existent company
        stage: 'proposal',
        value: 25000,
        probability: 0.3
      }
    ],
    projects: [],
    estimates: [],
    relationships: {
      companyRelationships: [],
      contactCompanyRelationships: [],
      contactRoles: [],
      dealEstimates: [],
      projectContacts: []
    }
  };
}

export const MOCK_KNOWLEDGE_GRAPH_RESPONSE = {
  success: true,
  data: {
    nodes: [
      { id: '1', label: 'Acme Corp', type: 'company', metadata: { industry: 'Tech' } },
      { id: '2', label: 'John Doe', type: 'contact', metadata: { jobTitle: 'CEO' } },
      { id: '3', label: 'Big Deal', type: 'deal', metadata: { value: 100000 } }
    ],
    links: [
      { source: '2', target: '1', type: 'works_at', strength: 5 },
      { source: '3', target: '1', type: 'belongs_to', strength: 5 }
    ],
    stats: {
      totalNodes: 3,
      totalLinks: 2,
      nodeTypes: { company: 1, contact: 1, deal: 1 },
      linkTypes: { works_at: 1, belongs_to: 1 }
    }
  }
};