import { SnapshotService } from '../../../../src/services/cashflow/snapshot-service';
import { CashflowSnapshotRepository } from '../../../../src/api/repositories/cashflow-snapshot-repository';
import { DailyCashflowService } from '../../../../src/services/cashflow/daily-cashflow-service';
import type { CashflowSnapshot, DailyCashflow } from '../../../../src/types/financial';

jest.mock('../../../../src/api/repositories/cashflow-snapshot-repository');
jest.mock('../../../../src/services/cashflow/daily-cashflow-service');

describe('SnapshotService', () => {
  let service: SnapshotService;
  let mockRepository: jest.Mocked<CashflowSnapshotRepository>;
  let mockDailyCashflowService: jest.Mocked<DailyCashflowService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockRepository = new CashflowSnapshotRepository(null as any) as jest.Mocked<CashflowSnapshotRepository>;
    mockDailyCashflowService = new DailyCashflowService(
      null as any, null as any, null as any, null as any, 
      null as any, null as any, null as any, null as any, null as any
    ) as jest.Mocked<DailyCashflowService>;
    service = new SnapshotService(mockRepository, mockDailyCashflowService);
  });

  describe('createSnapshot', () => {
    const mockDailyCashflow: DailyCashflow = {
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      dailyBalances: [
        { date: '2024-01-01', balance: 10000, income: 0, expenses: 0 },
        { date: '2024-01-15', balance: 15000, income: 5000, expenses: 0 },
        { date: '2024-01-31', balance: 12000, income: 0, expenses: 3000 }
      ],
      transactions: [],
      totalIncome: 5000,
      totalExpenses: 3000,
      projectedInvoices: [],
      projectedBudgets: [],
      scenarios: {
        worstCase: { dailyBalances: [] },
        expected: { dailyBalances: [] },
        bestCase: { dailyBalances: [] }
      }
    };

    it('should create a snapshot with current cashflow data', async () => {
      const expectedSnapshot = {
        id: 1,
        date: new Date().toISOString().split('T')[0],
        startingBalance: 10000,
        endingBalance: 12000,
        totalIncome: 5000,
        totalExpenses: 3000,
        netCashflow: 2000,
        metadata: {
          transactionCount: 0,
          projectedInvoiceCount: 0,
          projectedBudgetCount: 0
        },
        createdAt: expect.any(String)
      };

      mockDailyCashflowService.getDailyCashflow.mockResolvedValue(mockDailyCashflow);
      mockRepository.create.mockResolvedValue(expectedSnapshot as CashflowSnapshot);

      const result = await service.createSnapshot('2024-01-01', '2024-01-31', 'test-tenant');

      expect(result).toEqual(expectedSnapshot);
      expect(mockDailyCashflowService.getDailyCashflow).toHaveBeenCalledWith(
        '2024-01-01',
        '2024-01-31',
        'test-tenant'
      );
      expect(mockRepository.create).toHaveBeenCalledWith(expect.objectContaining({
        date: expect.any(String),
        startingBalance: 10000,
        endingBalance: 12000,
        totalIncome: 5000,
        totalExpenses: 3000,
        netCashflow: 2000
      }));
    });

    it('should include transaction counts in metadata', async () => {
      const cashflowWithTransactions: DailyCashflow = {
        ...mockDailyCashflow,
        transactions: [
          { id: '1', date: '2024-01-15', amount: 5000, type: 'income' } as any,
          { id: '2', date: '2024-01-31', amount: -3000, type: 'expense' } as any
        ],
        projectedInvoices: [
          { id: 'proj-1', amount: 2000 } as any
        ],
        projectedBudgets: [
          { id: 'budget-1', remainingBudget: 10000 } as any,
          { id: 'budget-2', remainingBudget: 5000 } as any
        ]
      };

      mockDailyCashflowService.getDailyCashflow.mockResolvedValue(cashflowWithTransactions);
      mockRepository.create.mockResolvedValue({} as CashflowSnapshot);

      await service.createSnapshot('2024-01-01', '2024-01-31', 'test-tenant');

      expect(mockRepository.create).toHaveBeenCalledWith(expect.objectContaining({
        metadata: {
          transactionCount: 2,
          projectedInvoiceCount: 1,
          projectedBudgetCount: 2
        }
      }));
    });

    it('should handle empty cashflow data', async () => {
      const emptyCashflow: DailyCashflow = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        dailyBalances: [],
        transactions: [],
        totalIncome: 0,
        totalExpenses: 0,
        projectedInvoices: [],
        projectedBudgets: [],
        scenarios: {
          worstCase: { dailyBalances: [] },
          expected: { dailyBalances: [] },
          bestCase: { dailyBalances: [] }
        }
      };

      mockDailyCashflowService.getDailyCashflow.mockResolvedValue(emptyCashflow);
      mockRepository.create.mockResolvedValue({} as CashflowSnapshot);

      await service.createSnapshot('2024-01-01', '2024-01-31', 'test-tenant');

      expect(mockRepository.create).toHaveBeenCalledWith(expect.objectContaining({
        startingBalance: 0,
        endingBalance: 0,
        totalIncome: 0,
        totalExpenses: 0,
        netCashflow: 0
      }));
    });

    it('should handle service errors', async () => {
      mockDailyCashflowService.getDailyCashflow.mockRejectedValue(new Error('Service error'));

      await expect(service.createSnapshot('2024-01-01', '2024-01-31', 'test-tenant'))
        .rejects.toThrow('Service error');
    });
  });

  describe('getSnapshots', () => {
    const mockSnapshots: CashflowSnapshot[] = [
      {
        id: 1,
        date: '2024-01-31',
        startingBalance: 10000,
        endingBalance: 12000,
        totalIncome: 5000,
        totalExpenses: 3000,
        netCashflow: 2000,
        metadata: {
          transactionCount: 10,
          projectedInvoiceCount: 2,
          projectedBudgetCount: 3
        },
        createdAt: '2024-02-01T00:00:00Z'
      },
      {
        id: 2,
        date: '2024-02-29',
        startingBalance: 12000,
        endingBalance: 15000,
        totalIncome: 6000,
        totalExpenses: 3000,
        netCashflow: 3000,
        metadata: {
          transactionCount: 15,
          projectedInvoiceCount: 3,
          projectedBudgetCount: 4
        },
        createdAt: '2024-03-01T00:00:00Z'
      }
    ];

    it('should return all snapshots in date range', async () => {
      mockRepository.getByDateRange.mockResolvedValue(mockSnapshots);

      const result = await service.getSnapshots('2024-01-01', '2024-02-29');

      expect(result).toEqual(mockSnapshots);
      expect(mockRepository.getByDateRange).toHaveBeenCalledWith('2024-01-01', '2024-02-29');
    });

    it('should return empty array if no snapshots found', async () => {
      mockRepository.getByDateRange.mockResolvedValue([]);

      const result = await service.getSnapshots('2024-01-01', '2024-01-31');

      expect(result).toEqual([]);
    });

    it('should handle repository errors', async () => {
      mockRepository.getByDateRange.mockRejectedValue(new Error('Database error'));

      await expect(service.getSnapshots('2024-01-01', '2024-01-31'))
        .rejects.toThrow('Database error');
    });
  });

  describe('getLatestSnapshot', () => {
    it('should return the most recent snapshot', async () => {
      const latestSnapshot: CashflowSnapshot = {
        id: 3,
        date: '2024-03-31',
        startingBalance: 15000,
        endingBalance: 18000,
        totalIncome: 7000,
        totalExpenses: 4000,
        netCashflow: 3000,
        metadata: {
          transactionCount: 20,
          projectedInvoiceCount: 4,
          projectedBudgetCount: 5
        },
        createdAt: '2024-04-01T00:00:00Z'
      };

      mockRepository.getLatest.mockResolvedValue(latestSnapshot);

      const result = await service.getLatestSnapshot();

      expect(result).toEqual(latestSnapshot);
      expect(mockRepository.getLatest).toHaveBeenCalled();
    });

    it('should return null if no snapshots exist', async () => {
      mockRepository.getLatest.mockResolvedValue(null);

      const result = await service.getLatestSnapshot();

      expect(result).toBeNull();
    });
  });

  describe('deleteSnapshot', () => {
    it('should delete snapshot by id', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const result = await service.deleteSnapshot(1);

      expect(result).toBe(true);
      expect(mockRepository.delete).toHaveBeenCalledWith(1);
    });

    it('should return false if snapshot not found', async () => {
      mockRepository.delete.mockResolvedValue(false);

      const result = await service.deleteSnapshot(999);

      expect(result).toBe(false);
    });
  });

  describe('compareSnapshots', () => {
    it('should compare two snapshots and return differences', async () => {
      const snapshot1: CashflowSnapshot = {
        id: 1,
        date: '2024-01-31',
        startingBalance: 10000,
        endingBalance: 12000,
        totalIncome: 5000,
        totalExpenses: 3000,
        netCashflow: 2000,
        metadata: {
          transactionCount: 10,
          projectedInvoiceCount: 2,
          projectedBudgetCount: 3
        },
        createdAt: '2024-02-01T00:00:00Z'
      };

      const snapshot2: CashflowSnapshot = {
        id: 2,
        date: '2024-02-29',
        startingBalance: 12000,
        endingBalance: 15000,
        totalIncome: 6000,
        totalExpenses: 3500,
        netCashflow: 2500,
        metadata: {
          transactionCount: 15,
          projectedInvoiceCount: 3,
          projectedBudgetCount: 4
        },
        createdAt: '2024-03-01T00:00:00Z'
      };

      mockRepository.getById.mockResolvedValueOnce(snapshot1);
      mockRepository.getById.mockResolvedValueOnce(snapshot2);

      const result = await service.compareSnapshots(1, 2);

      expect(result).toEqual({
        balanceChange: 3000, // 15000 - 12000
        incomeChange: 1000,  // 6000 - 5000
        expenseChange: 500,  // 3500 - 3000
        netCashflowChange: 500, // 2500 - 2000
        transactionCountChange: 5,
        projectedInvoiceCountChange: 1,
        projectedBudgetCountChange: 1
      });
    });

    it('should throw error if snapshots not found', async () => {
      mockRepository.getById.mockResolvedValueOnce(null);

      await expect(service.compareSnapshots(1, 2))
        .rejects.toThrow('Snapshot not found');
    });
  });

  describe('getTrends', () => {
    it('should calculate trends from snapshots', async () => {
      const snapshots: CashflowSnapshot[] = [
        {
          id: 1,
          date: '2024-01-31',
          startingBalance: 10000,
          endingBalance: 12000,
          totalIncome: 5000,
          totalExpenses: 3000,
          netCashflow: 2000,
          metadata: { transactionCount: 10, projectedInvoiceCount: 2, projectedBudgetCount: 3 },
          createdAt: '2024-02-01T00:00:00Z'
        },
        {
          id: 2,
          date: '2024-02-29',
          startingBalance: 12000,
          endingBalance: 15000,
          totalIncome: 6000,
          totalExpenses: 3500,
          netCashflow: 2500,
          metadata: { transactionCount: 15, projectedInvoiceCount: 3, projectedBudgetCount: 4 },
          createdAt: '2024-03-01T00:00:00Z'
        },
        {
          id: 3,
          date: '2024-03-31',
          startingBalance: 15000,
          endingBalance: 18000,
          totalIncome: 7000,
          totalExpenses: 4000,
          netCashflow: 3000,
          metadata: { transactionCount: 20, projectedInvoiceCount: 4, projectedBudgetCount: 5 },
          createdAt: '2024-04-01T00:00:00Z'
        }
      ];

      mockRepository.getByDateRange.mockResolvedValue(snapshots);

      const result = await service.getTrends('2024-01-01', '2024-03-31');

      expect(result).toEqual({
        averageMonthlyIncome: 6000, // (5000 + 6000 + 7000) / 3
        averageMonthlyExpenses: 3500, // (3000 + 3500 + 4000) / 3
        averageNetCashflow: 2500, // (2000 + 2500 + 3000) / 3
        incomeGrowthRate: 0.4, // (7000 - 5000) / 5000
        expenseGrowthRate: 0.333, // (4000 - 3000) / 3000
        balanceGrowthRate: 0.5 // (18000 - 12000) / 12000
      });
    });

    it('should handle single snapshot', async () => {
      const singleSnapshot: CashflowSnapshot[] = [
        {
          id: 1,
          date: '2024-01-31',
          startingBalance: 10000,
          endingBalance: 12000,
          totalIncome: 5000,
          totalExpenses: 3000,
          netCashflow: 2000,
          metadata: { transactionCount: 10, projectedInvoiceCount: 2, projectedBudgetCount: 3 },
          createdAt: '2024-02-01T00:00:00Z'
        }
      ];

      mockRepository.getByDateRange.mockResolvedValue(singleSnapshot);

      const result = await service.getTrends('2024-01-01', '2024-01-31');

      expect(result).toEqual({
        averageMonthlyIncome: 5000,
        averageMonthlyExpenses: 3000,
        averageNetCashflow: 2000,
        incomeGrowthRate: 0,
        expenseGrowthRate: 0,
        balanceGrowthRate: 0
      });
    });

    it('should handle no snapshots', async () => {
      mockRepository.getByDateRange.mockResolvedValue([]);

      const result = await service.getTrends('2024-01-01', '2024-01-31');

      expect(result).toEqual({
        averageMonthlyIncome: 0,
        averageMonthlyExpenses: 0,
        averageNetCashflow: 0,
        incomeGrowthRate: 0,
        expenseGrowthRate: 0,
        balanceGrowthRate: 0
      });
    });
  });
});