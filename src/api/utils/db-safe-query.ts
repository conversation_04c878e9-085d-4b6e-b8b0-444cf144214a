import { Database } from 'better-sqlite3';

/**
 * Safe database query utility for handling potential errors gracefully
 * Particularly useful for knowledge graph queries where tables might not exist
 */

/**
 * Execute a database query safely with error handling
 * @param db Database instance
 * @param query SQL query string
 * @param params Query parameters
 * @param defaultValue Default value to return on error
 * @returns Query result or default value
 */
export function safeTableQuery<T>(
  db: Database,
  query: string,
  params: any[] = [],
  defaultValue: T
): T {
  try {
    const statement = db.prepare(query);
    
    // Check if it's a SELECT query
    if (query.trim().toUpperCase().startsWith('SELECT')) {
      // For SELECT queries that return multiple rows
      if (query.includes('*') || query.includes(',')) {
        return statement.all(...params) as T;
      }
      // For SELECT queries that return a single value
      return statement.get(...params) as T;
    }
    
    // For INSERT, UPDATE, DELETE queries
    const result = statement.run(...params);
    return result as any as T;
  } catch (error) {
    // Log the error but don't throw
    console.warn(`Query failed: ${query}`, error);
    return defaultValue;
  }
}

/**
 * Check if a table exists in the database
 * @param db Database instance
 * @param tableName Name of the table to check
 * @returns True if table exists, false otherwise
 */
export function tableExists(db: Database, tableName: string): boolean {
  try {
    const result = db.prepare(
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
    ).get(tableName);
    return !!result;
  } catch (error) {
    console.warn(`Error checking if table ${tableName} exists:`, error);
    return false;
  }
}

/**
 * Execute a query only if the table exists
 * @param db Database instance
 * @param tableName Table name to check
 * @param query SQL query string
 * @param params Query parameters
 * @param defaultValue Default value to return if table doesn't exist
 * @returns Query result or default value
 */
export function queryIfTableExists<T>(
  db: Database,
  tableName: string,
  query: string,
  params: any[] = [],
  defaultValue: T
): T {
  if (!tableExists(db, tableName)) {
    console.warn(`Table ${tableName} does not exist, returning default value`);
    return defaultValue;
  }
  
  return safeTableQuery(db, query, params, defaultValue);
}

/**
 * Wrap a repository method with error handling
 * @param fn Repository method to wrap
 * @param defaultValue Default value to return on error
 * @param operationName Name of the operation for logging
 * @returns Wrapped function with error handling
 */
export function withRepositoryErrorHandling<T extends (...args: any[]) => any>(
  fn: T,
  defaultValue: ReturnType<T>,
  operationName: string
): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    try {
      return fn(...args);
    } catch (error) {
      console.error(`Repository operation '${operationName}' failed:`, error);
      return defaultValue;
    }
  }) as T;
}