import React, { useMemo, useState } from 'react'; import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, parseISO, startOfWeek, endOfWeek } from 'date-fns'; import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'; import { Deal } from '../../../types/crm-types'; interface DealCalendarViewProps { deals: Deal[]; onDealClick: (deal: Deal) => void; } // Stage colors matching the Kanban board const STAGE_COLORS: Record<string, { bg: string; text: string; border: string }> = { 'Prospecting': { bg: 'bg-slate-100', text: 'text-slate-700', border: 'border-slate-300' }, 'Qualification': { bg: 'bg-blue-100', text: 'text-blue-700', border: 'border-blue-300' }, 'Proposal': { bg: 'bg-yellow-100', text: 'text-yellow-700', border: 'border-yellow-300' }, 'Negotiation': { bg: 'bg-orange-100', text: 'text-orange-700', border: 'border-orange-300' }, 'Closed won': { bg: 'bg-green-100', text: 'text-green-700', border: 'border-green-300' }, 'Closed lost': { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-300' }, 'Abandoned': { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-300' } }; interface CalendarDeal { deal: Deal; type: 'created' | 'closing'; date: Date; } export const DealCalendarView: React.FC<DealCalendarViewProps> = ({ deals, onDealClick }) => { const [currentDate, setCurrentDate] = useState(new Date()); const [viewMode, setViewMode] = useState<'month' | 'week'>('month'); // Process deals to get calendar events const calendarDeals = useMemo(() => { const events: CalendarDeal[] = []; deals.forEach(deal => { // Add creation date event if (deal.createdAt) { events.push({ deal, type: 'created', date: parseISO(deal.createdAt) }); } // Add expected close date event if (deal.expectedCloseDate) { events.push({ deal, type: 'closing', date: parseISO(deal.expectedCloseDate) }); } }); return events; }, [deals]); // Get calendar days based on view mode const calendarDays = useMemo(() => { if (viewMode === 'month') { const start = startOfWeek(startOfMonth(currentDate)); const end = endOfWeek(endOfMonth(currentDate)); return eachDayOfInterval({ start, end }); } else { const start = startOfWeek(currentDate); const end = endOfWeek(currentDate); return eachDayOfInterval({ start, end }); } }, [currentDate, viewMode]); // Group deals by date const dealsByDate = useMemo(() => { const grouped = new Map<string, CalendarDeal[]>(); calendarDeals.forEach(calendarDeal => { const dateKey = format(calendarDeal.date, 'yyyy-MM-dd'); if (!grouped.has(dateKey)) { grouped.set(dateKey, []); } grouped.get(dateKey)!.push(calendarDeal); }); return grouped; }, [calendarDeals]); // Navigation handlers const navigatePrevious = () => { if (viewMode === 'month') { setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1)); } else { setCurrentDate(prev => new Date(prev.getTime() - 7 * 24 * 60 * 60 * 1000)); } }; const navigateNext = () => { if (viewMode === 'month') { setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1)); } else { setCurrentDate(prev => new Date(prev.getTime() + 7 * 24 * 60 * 60 * 1000)); } }; const navigateToday = () => { setCurrentDate(new Date()); }; // Get stage color configuration const getStageColor = (stage: string) => { return STAGE_COLORS[stage] || STAGE_COLORS['Abandoned']; }; return ( <div className="h-full flex flex-col bg-white dark:bg-gray-800 rounded-lg shadow-sm"> {/* Calendar Header */} <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"> <div className="flex items-center gap-4"> <h2 className="text-xl font-semibold text-gray-900 dark:text-white"> {format(currentDate, viewMode === 'month' ? 'MMMM yyyy' : "'Week of' MMM d, yyyy")} </h2> <div className="flex items-center gap-1"> <button onClick={navigatePrevious} className="p-1.5 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors" > <ChevronLeftIcon className="w-5 h-5" /> </button> <button onClick={navigateToday} className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors" > Today </button> <button onClick={navigateNext} className="p-1.5 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors" > <ChevronRightIcon className="w-5 h-5" /> </button> </div> </div> {/* View Mode Toggle */} <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1"> <button onClick={() => setViewMode('week')} className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all ${ viewMode === 'week' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white' }`} > Week </button> <button onClick={() => setViewMode('month')} className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all ${ viewMode === 'month' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white' }`} > Month </button> </div> </div> {/* Calendar Grid */} <div className="flex-1 overflow-auto"> <div className="min-h-full"> {/* Day Headers */} <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-700"> {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => ( <div key={day} className="p-2 text-center text-sm font-medium text-gray-700 dark:text-gray-300 border-r border-gray-200 dark:border-gray-700 last:border-r-0" > {day} </div> ))} </div> {/* Calendar Days */} <div className={`grid grid-cols-7 ${viewMode === 'month' ? 'grid-rows-6' : ''}`}> {calendarDays.map((day, index) => { const dateKey = format(day, 'yyyy-MM-dd'); const dayDeals = dealsByDate.get(dateKey) || []; const isCurrentMonth = isSameMonth(day, currentDate); const isToday = isSameDay(day, new Date()); return ( <div key={index} className={` border-r border-b border-gray-200 dark:border-gray-700 last:border-r-0 ${viewMode === 'month' ? 'min-h-[120px]' : 'min-h-[200px]'} ${!isCurrentMonth && viewMode === 'month' ? 'bg-gray-50 dark:bg-gray-900' : ''} ${isToday ? 'bg-blue-50 dark:bg-blue-900/20' : ''} `} > {/* Day Number */} <div className="p-2"> <div className={`text-sm font-medium ${ isToday ? 'text-blue-600 dark:text-blue-400' : isCurrentMonth ? 'text-gray-900 dark:text-gray-100' : 'text-gray-400 dark:text-gray-600' }`}> {format(day, 'd')} </div> </div> {/* Deal Events */} <div className="px-2 pb-2 space-y-1"> {dayDeals.slice(0, viewMode === 'month' ? 3 : 5).map((calendarDeal, i) => { const colors = getStageColor(calendarDeal.deal.stage); return ( <button key={`${calendarDeal.deal.id}-${calendarDeal.type}-${i}`} onClick={() => onDealClick(calendarDeal.deal)} className={` w-full text-left px-2 py-1 rounded text-xs font-medium ${colors.bg} ${colors.text} hover:opacity-80 transition-opacity border ${colors.border} `} title={`${calendarDeal.deal.name} - ${calendarDeal.type === 'created' ? 'Created' : 'Expected Close'}`} > <div className="flex items-center gap-1"> <span className={` w-1.5 h-1.5 rounded-full flex-shrink-0 ${calendarDeal.type === 'created' ? 'bg-gray-500' : colors.text.replace('text', 'bg')} `} /> <span className="truncate"> {calendarDeal.deal.name} </span> </div> </button> ); })} {dayDeals.length > (viewMode === 'month' ? 3 : 5) && ( <div className="text-xs text-gray-500 dark:text-gray-400 text-center"> +{dayDeals.length - (viewMode === 'month' ? 3 : 5)} more </div> )} </div> </div> ); })} </div> </div> </div> {/* Legend */} <div className="border-t border-gray-200 dark:border-gray-700 p-4"> <div className="flex items-center justify-between"> <div className="flex items-center gap-4 text-xs"> <div className="flex items-center gap-1"> <span className="w-2 h-2 rounded-full bg-gray-500" /> <span className="text-gray-600 dark:text-gray-400">Created</span> </div> <div className="flex items-center gap-1"> <span className="w-2 h-2 rounded-full bg-blue-500" /> <span className="text-gray-600 dark:text-gray-400">Expected Close</span> </div> </div> <div className="text-xs text-gray-500 dark:text-gray-400"> {deals.length} deals • {calendarDeals.length} events </div> </div> </div> </div> ); }; export default DealCalendarView;