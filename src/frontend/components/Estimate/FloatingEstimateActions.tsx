import React, { useState, useCallback, useEffect } from "react";

import { AllocationWithTotals } from "../../hooks/useEstimateStaffManagement";

// Same props as the existing EstimateActions component
interface FloatingEstimateActionsProps {
  isEstimateInitialized: boolean;
  isSaving: boolean;
  isPublishing?: boolean; // Separate publishing state
  isSavedSuccessfully: boolean;
  isPublishedSuccessfully?: boolean; // Separate published state
  saveError: string | null;
  publishError?: string | null; // Separate publish error
  draftUuid: string | null;
  canSaveEstimate: boolean;
  canPublishToHarvest?: boolean; // New prop for Harvest publishing permission
  isLoadingPermissions: boolean;
  hasStaffAllocations: boolean;
  onInitialize: () => void;
  onReset: () => void;
  onSaveDraft: () => Promise<boolean>;
  onSaveToHarvest: () => Promise<boolean>;
  isFormValidForInit: boolean;
  isLoadingClients: boolean;
  onExport: () => void; // New export handler
  allocationsWithTotals?: AllocationWithTotals[]; // Staff allocations for export button state
}

const FloatingEstimateActions: React.FC<FloatingEstimateActionsProps> = (
  props
) => {
  // Initialize state from localStorage or default to true
  const [isExpanded, setIsExpanded] = useState(() => {
    const savedState = localStorage.getItem("estimateActionsPanelExpanded");
    return savedState === null ? true : savedState === "true";
  });

  // State for help tooltip
  const [showHelp, setShowHelp] = useState(false);
  // State for copy feedback
  const [linkCopied, setLinkCopied] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [shareUrl, setShareUrl] = useState("");

  // Save expanded state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem("estimateActionsPanelExpanded", isExpanded.toString());
  }, [isExpanded]);

  // Simple share handler - will only be called when draftUuid exists (button is disabled otherwise)
  const handleShare = useCallback(() => {
    if (props.draftUuid) {
      // Simply generate and show the URL
      const url = `${window.location.origin}/estimates/${props.draftUuid}`;
      setShareUrl(url);
      setShowShareModal(true);
    }
  }, [props.draftUuid]);

  // Separate handler for clipboard operations
  const handleCopyToClipboard = useCallback(() => {
    if (shareUrl) {
      navigator.clipboard
        .writeText(shareUrl)
        .then(() => {
          setLinkCopied(true);
          setTimeout(() => setLinkCopied(false), 2000);
        })
        .catch((err) => {
          console.error("Failed to copy link: ", err);
          alert("Failed to copy link. Please select and copy manually.");
        });
    }
  }, [shareUrl]);

  const handleExport = () => {
    props.onExport();
  };

  return (
    <>
      <div
        className="fixed flex flex-col floating-actions-panel"
        role="region"
        aria-label="Estimate Actions Panel"
      >
        {/* Collapsed state shows only an icon button */}
        {!isExpanded ? (
          <button
            onClick={() => setIsExpanded(true)}
            className="self-end p-3 rounded-full bg-primary text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200 hover:scale-110"
            aria-label="Show Estimate Actions"
            aria-expanded="false"
            title="Show Estimate Actions"
          >
            <svg
              className="w-5 h-5"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
          </button>
        ) : (
          <div
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 w-full origin-top-right transition-all duration-300 ease-in-out scale-100 opacity-100"
            role="dialog"
            aria-modal="true"
            aria-labelledby="actions-panel-title"
          >
            {/* Header with title and close button */}
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Estimate Actions
                </h3>
                {/* Help icon with tooltip */}
                <div className="relative ml-1">
                  <button
                    className="w-4 h-4 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300 text-xs hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors focus:outline-none"
                    onClick={() => setShowHelp(!showHelp)}
                    aria-label="Help information"
                  >
                    ?
                  </button>
                  {showHelp && (
                    <div className="absolute z-50 bottom-6 -left-4 w-60 px-3 py-2 bg-white dark:bg-gray-700 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 text-xs text-gray-600 dark:text-gray-300">
                      <div className="flex justify-between items-start mb-1">
                        <strong className="text-gray-700 dark:text-gray-200">
                          Estimate Actions
                        </strong>
                        <button
                          onClick={() => setShowHelp(false)}
                          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                        >
                          ×
                        </button>
                      </div>
                      <ul className="list-disc pl-4 space-y-1 mb-1.5">
                        <li>
                          <strong>Save Draft</strong>: Store your estimate in
                          Upstream to continue later
                        </li>
                        <li>
                          <strong>Harvest</strong>: Create and publish this
                          estimate in Harvest
                        </li>
                        <li>
                          <strong>Clear</strong>: Reset all data and start over
                        </li>
                        <li>
                          <strong>Share</strong>: Share this estimate with
                          colleagues or clients
                        </li>
                        <li>
                          <strong>Export</strong>: Export estimate as CSV for
                          further modeling in spreadsheet software
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-all duration-200"
                aria-label="Collapse Estimate Actions"
                aria-expanded="true"
              >
                <svg
                  className="w-3.5 h-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Status indicators - only rendered when needed */}
            {(props.isSavedSuccessfully ||
              props.isPublishedSuccessfully ||
              props.saveError ||
              props.publishError) && (
              <div className="mb-2">
                {props.isSavedSuccessfully &&
                  !props.isPublishedSuccessfully && (
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      <svg
                        className="w-3 h-3 mr-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        ></path>
                      </svg>
                      Saved
                    </span>
                  )}
                {props.isPublishedSuccessfully && (
                  <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-[#f36c21]/10 text-[#f36c21] dark:bg-[#f36c21]/20 dark:text-[#f36c21]/90">
                    <svg
                      className="w-3 h-3 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M5 13l4 4L19 7"
                      ></path>
                    </svg>
                    Published to Harvest
                  </span>
                )}
                {(props.saveError || props.publishError) && (
                  <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                    <svg
                      className="w-3 h-3 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      ></path>
                    </svg>
                    Error
                  </span>
                )}
              </div>
            )}

            {/* Action buttons with consistent heights */}
            <div className="flex flex-col gap-2">
              {!props.isEstimateInitialized ? (
                <button
                  onClick={props.onInitialize}
                  className="btn-modern btn-modern--primary w-full px-3 py-2 rounded-lg font-medium text-sm disabled:opacity-50"
                  disabled={!props.isFormValidForInit || props.isLoadingClients}
                >
                  Initialise Estimate
                </button>
              ) : (
                <>
                  {/* Primary actions row */}
                  <button
                    onClick={props.onSaveDraft}
                    className="btn-modern btn-modern--primary w-full h-9 px-3 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed text-[12px] font-medium flex items-center justify-center"
                    disabled={
                      props.isSaving ||
                      props.isPublishing ||
                      !props.canSaveEstimate // This is for saving drafts - should always be true
                    }
                  >
                    {props.isSaving ? (
                      <div className="flex items-center justify-center">
                        <svg
                          className="animate-spin h-3 w-3 mr-1"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Saving...
                      </div>
                    ) : props.draftUuid ? (
                      "Update Draft"
                    ) : (
                      "Save Draft"
                    )}
                  </button>

                  {/* Two button rows */}
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={props.onSaveToHarvest}
                      className="h-9 px-2 bg-[#f36c21] text-white rounded-lg hover:bg-[#e05a10] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-[10px] font-medium"
                      disabled={
                        props.isSaving ||
                        props.isPublishing ||
                        props.isPublishedSuccessfully ||
                        !props.hasStaffAllocations ||
                        props.isLoadingPermissions ||
                        !(props.canPublishToHarvest ?? props.canSaveEstimate) // Use new prop if available, fallback to old
                      }
                      title={
                        !props.isLoadingPermissions &&
                        !(props.canPublishToHarvest ?? props.canSaveEstimate)
                          ? "You lack permission to publish to Harvest"
                          : undefined
                      }
                    >
                      {props.isPublishing ? (
                        <>
                          <svg
                            className="animate-spin h-3 w-3 mr-1"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Publishing...
                        </>
                      ) : props.isPublishedSuccessfully ? (
                        <>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-3 w-3 mr-1"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            strokeWidth={2}
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                          Published
                        </>
                      ) : (
                        <>Publish</>
                      )}
                    </button>

                    <button
                      onClick={props.onReset}
                      className="h-9 px-2 bg-white text-red-600 border border-red-600 rounded-lg hover:bg-red-50 text-[10px] font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                      disabled={props.isSaving || props.isPublishing}
                    >
                      Clear
                    </button>
                  </div>

                  {/* New action buttons */}
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={handleShare}
                      disabled={!props.draftUuid}
                      title={
                        !props.draftUuid
                          ? "Save the draft first to enable sharing"
                          : "Share this estimate with others"
                      }
                      className={`h-9 px-2 bg-white text-primary border border-primary rounded-lg flex items-center justify-center gap-1 text-[10px] font-medium
                      ${
                        props.draftUuid
                          ? "hover:bg-primary/5"
                          : "opacity-50 cursor-not-allowed"
                      }`}
                    >
                      {linkCopied ? (
                        <svg
                          className="w-3 h-3 text-green-500"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-3 h-3"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                          />
                        </svg>
                      )}
                      {linkCopied ? "Link Copied!" : "Share"}
                    </button>

                    <button
                      onClick={handleExport}
                      className="h-9 px-2 bg-white text-primary border border-primary rounded-lg hover:bg-primary/5 text-[10px] font-medium flex items-center justify-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                      disabled={
                        !props.isEstimateInitialized ||
                        props.allocationsWithTotals?.length === 0
                      }
                    >
                      <svg
                        className="w-3 h-3"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                      Export
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Share URL Modal */}
      {showShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-lg w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Share Estimate
              </h3>
              <button
                onClick={() => setShowShareModal(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                aria-label="Close modal"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Anyone with this link can access this estimate draft. Copy the
                link below:
              </p>
              <div className="flex">
                <input
                  type="text"
                  className="flex-grow p-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 focus:outline-none focus:ring-1 focus:ring-primary"
                  value={shareUrl}
                  readOnly
                  onClick={(e) => e.currentTarget.select()}
                />
                <button
                  onClick={handleCopyToClipboard}
                  className="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  {linkCopied ? "Copied!" : "Copy"}
                </button>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => setShowShareModal(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-500"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default FloatingEstimateActions;
