/**
 * Knowledge Graph Integration Test
 * 
 * Integration tests for the complete knowledge graph feature workflow,
 * covering API endpoints, database interactions, and data aggregation.
 */
import request from 'supertest';
import express from 'express';
import { jest } from '@jest/globals';
import { Database } from '@/database';
import { runMigrations } from '@/api/services/db-init';
import knowledgeGraphRoutes from '@/api/routes/knowledge-graph';
import { CompanyRepository } from '@/api/repositories/company-repository';
import { ContactRepository } from '@/api/repositories/contact-repository';
import { DealRepository } from '@/api/repositories/deal-repository';
import { ProjectRepository } from '@/api/repositories/project-repository';
import { EstimateDraftsRepository } from '@/api/repositories/estimate-drafts-repository';
import { ContactCompanyRepository } from '@/api/repositories/contact-company-repository';
import { ContactRoleRepository } from '@/api/repositories/relationships/contact-role-repository';
import { DealEstimateRepository } from '@/api/repositories/relationships/deal-estimate-repository';
import { v4 as uuidv4 } from 'uuid';

describe('Knowledge Graph Integration', () => {
  let app: express.Application;
  let db: Database;
  let companyRepo: CompanyRepository;
  let contactRepo: ContactRepository;
  let dealRepo: DealRepository;
  let projectRepo: ProjectRepository;
  let estimateRepo: EstimateDraftsRepository;
  let contactCompanyRepo: ContactCompanyRepository;
  let contactRoleRepo: ContactRoleRepository;
  let dealEstimateRepo: DealEstimateRepository;
  
  // Test data
  let testCompanyId: string;
  let testContactId: string;
  let testDealId: string;
  let testProjectId: string;
  let testEstimateId: string;
  
  beforeAll(async () => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Run migrations to set up schema
    await runMigrations(db);
    
    // Set up Express app
    app = express();
    app.use(express.json());
    app.use('/api/knowledge-graph', knowledgeGraphRoutes);
    
    // Initialize repositories
    companyRepo = new CompanyRepository();
    contactRepo = new ContactRepository();
    dealRepo = new DealRepository();
    projectRepo = new ProjectRepository();
    estimateRepo = new EstimateDraftsRepository();
    contactCompanyRepo = new ContactCompanyRepository();
    contactRoleRepo = new ContactRoleRepository();
    dealEstimateRepo = new DealEstimateRepository();
  });
  
  beforeEach(async () => {
    // Clear all data
    const tables = [
      'company', 'contact', 'deal', 'project', 'estimate',
      'contact_company', 'contact_relationships', 'contact_role',
      'deal_estimate', 'project_contact',
      'project_dependency', 'estimate_allocation', 'estimate_time_allocation'
    ];
    
    for (const table of tables) {
      try {
        db.prepare(`DELETE FROM ${table}`).run();
      } catch (error) {
        // Table might not exist
      }
    }
    
    // Create comprehensive test data
    await setupComprehensiveTestData();
  });
  
  afterAll(() => {
    db.close();
  });
  
  async function setupComprehensiveTestData() {
    // Create companies with relationships
    testCompanyId = uuidv4();
    const parentCompanyId = uuidv4();
    const subsidiaryId = uuidv4();
    
    companyRepo.createCompany({
      id: testCompanyId,
      name: 'Acme Corporation',
      industry: 'Technology',
      size: '50-200',
      website: 'https://acme.com'
    }, 'test-user');
    
    companyRepo.createCompany({
      id: parentCompanyId,
      name: 'Mega Holdings',
      industry: 'Investment'
    }, 'test-user');
    
    companyRepo.createCompany({
      id: subsidiaryId,
      name: 'Acme Subsidiary',
      industry: 'Software'
    }, 'test-user');
    
    // Create company relationships
    companyRelRepo.createRelationship({
      parent_company_id: parentCompanyId,
      child_company_id: testCompanyId,
      relationship_type: 'subsidiary'
    });
    
    companyRelRepo.createRelationship({
      parent_company_id: testCompanyId,
      child_company_id: subsidiaryId,
      relationship_type: 'subsidiary'
    });
    
    // Create contacts
    testContactId = uuidv4();
    const contact2Id = uuidv4();
    const contact3Id = uuidv4();
    
    contactRepo.createContact({
      id: testContactId,
      first_name: 'John',
      last_name: 'Smith',
      email: '<EMAIL>',
      job_title: 'CEO',
      phone: '******-0101'
    }, 'test-user');
    
    contactRepo.createContact({
      id: contact2Id,
      first_name: 'Jane',
      last_name: 'Doe',
      email: '<EMAIL>',
      job_title: 'CTO'
    }, 'test-user');
    
    contactRepo.createContact({
      id: contact3Id,
      first_name: 'Bob',
      last_name: 'Johnson',
      email: '<EMAIL>',
      job_title: 'Manager'
    }, 'test-user');
    
    // Create contact-company relationships
    contactCompanyRepo.createRelationship({
      contact_id: testContactId,
      company_id: testCompanyId,
      role: 'CEO',
      is_primary: true
    });
    
    contactCompanyRepo.createRelationship({
      contact_id: contact2Id,
      company_id: testCompanyId,
      role: 'CTO',
      is_primary: false
    });
    
    contactCompanyRepo.createRelationship({
      contact_id: contact3Id,
      company_id: subsidiaryId,
      role: 'Manager',
      is_primary: true
    });
    
    // Create contact relationships
    db.prepare(`
      INSERT INTO contact_relationships (
        id, source_contact_id, target_contact_id, relationship_type, 
        strength, context, created_at, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      uuidv4(), testContactId, contact2Id, 'colleague', 
      5, 'Work together at Acme', new Date().toISOString(), 'test-user'
    );
    
    // Create deals
    testDealId = uuidv4();
    const deal2Id = uuidv4();
    
    dealRepo.createDeal({
      id: testDealId,
      name: 'Enterprise Software Deal',
      company_id: testCompanyId,
      stage: 'negotiation',
      value: 250000,
      probability: 0.8,
      expected_close_date: '2024-06-30'
    }, 'test-user');
    
    dealRepo.createDeal({
      id: deal2Id,
      name: 'Consulting Services',
      company_id: subsidiaryId,
      stage: 'proposal',
      value: 75000,
      probability: 0.6
    }, 'test-user');
    
    // Create contact roles in deals
    contactRoleRepo.createContactRole({
      deal_id: testDealId,
      contact_id: testContactId,
      role: 'decision_maker'
    });
    
    contactRoleRepo.createContactRole({
      deal_id: testDealId,
      contact_id: contact2Id,
      role: 'technical_evaluator'
    });
    
    contactRoleRepo.createContactRole({
      deal_id: deal2Id,
      contact_id: contact3Id,
      role: 'champion'
    });
    
    // Create projects
    testProjectId = uuidv4();
    const project2Id = uuidv4();
    
    projectRepo.createProject({
      id: testProjectId,
      name: 'Digital Transformation',
      description: 'Complete digital overhaul',
      company_id: testCompanyId,
      deal_id: testDealId,
      status: 'active',
      start_date: '2024-01-01',
      end_date: '2024-12-31',
      budget: 200000,
      spent: 50000
    });
    
    projectRepo.createProject({
      id: project2Id,
      name: 'Website Redesign',
      company_id: subsidiaryId,
      deal_id: deal2Id,
      status: 'planning',
      start_date: '2024-03-01',
      end_date: '2024-08-31',
      budget: 60000
    });
    
    // Add project contacts
    projectRepo.addProjectContact({
      project_id: testProjectId,
      contact_id: testContactId,
      role: 'sponsor',
      allocation_percentage: 25,
      created_by: 'test-user',
      updated_by: 'test-user'
    });
    
    projectRepo.addProjectContact({
      project_id: testProjectId,
      contact_id: contact2Id,
      role: 'technical_lead',
      allocation_percentage: 75,
      created_by: 'test-user',
      updated_by: 'test-user'
    });
    
    // Create project dependency
    projectRepo.addProjectDependency({
      predecessor_project_id: project2Id,
      successor_project_id: testProjectId,
      dependency_type: 'finish_to_start',
      lag_days: 14,
      created_by: 'test-user'
    });
    
    // Create estimates
    testEstimateId = uuidv4();
    const estimate2Id = uuidv4();
    
    await estimateRepo.create({
      uuid: testEstimateId,
      clientId: 'harvest-123',
      clientName: 'Acme Corporation',
      projectName: 'Digital Transformation Estimate',
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      userId: 'test-user',
      notes: 'Primary estimate for enterprise deal',
      allocations: [{
        harvestUserId: 'harvest-user-1',
        firstName: 'Alice',
        lastName: 'Developer',
        onbordTargetRateDaily: 1000,
        onbordCostRateDaily: 600,
        rateProposedDaily: 1100,
        weeklyAllocation: {
          '2024-W01': 5,
          '2024-W02': 5,
          '2024-W03': 4
        }
      }]
    });
    
    await estimateRepo.create({
      uuid: estimate2Id,
      clientId: 'harvest-456',
      clientName: 'Acme Subsidiary',
      projectName: 'Website Redesign Estimate',
      startDate: '2024-03-01',
      endDate: '2024-08-31',
      userId: 'test-user',
      allocations: [{
        harvestUserId: 'harvest-user-2',
        firstName: 'Bob',
        lastName: 'Designer',
        onbordTargetRateDaily: 800,
        onbordCostRateDaily: 500,
        rateProposedDaily: 850,
        weeklyAllocation: {
          '2024-W09': 3,
          '2024-W10': 3
        }
      }]
    });
    
    // Link estimates to deals
    dealEstimateRepo.linkDealToEstimate({
      deal_id: testDealId,
      estimate_id: testEstimateId,
      is_primary: true
    });
    
    dealEstimateRepo.linkDealToEstimate({
      deal_id: deal2Id,
      estimate_id: estimate2Id,
      is_primary: true
    });
  }
  
  describe('GET /api/knowledge-graph', () => {
    it('returns complete knowledge graph with all relationships', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      
      const { nodes, links, stats } = response.body.data;
      
      // Verify all entity types are present
      const nodeTypes = [...new Set(nodes.map((n: any) => n.type))];
      expect(nodeTypes).toContain('company');
      expect(nodeTypes).toContain('contact');
      expect(nodeTypes).toContain('deal');
      expect(nodeTypes).toContain('project');
      expect(nodeTypes).toContain('estimate');
      
      // Verify specific entities
      const acmeCompany = nodes.find((n: any) => n.label === 'Acme Corporation');
      expect(acmeCompany).toBeDefined();
      expect(acmeCompany.type).toBe('company');
      expect(acmeCompany.metadata.industry).toBe('Technology');
      
      const johnContact = nodes.find((n: any) => n.label === 'John Smith');
      expect(johnContact).toBeDefined();
      expect(johnContact.type).toBe('contact');
      expect(johnContact.metadata.jobTitle).toBe('CEO');
      
      // Verify relationships
      const workRelationship = links.find((l: any) => 
        l.type === 'works_at' && l.source === johnContact.id && l.target === acmeCompany.id
      );
      expect(workRelationship).toBeDefined();
      
      // Verify statistics
      expect(stats.totalNodes).toBeGreaterThan(0);
      expect(stats.totalLinks).toBeGreaterThan(0);
      expect(stats.nodeTypes.company).toBeGreaterThanOrEqual(3);
      expect(stats.nodeTypes.contact).toBeGreaterThanOrEqual(3);
      expect(stats.nodeTypes.deal).toBeGreaterThanOrEqual(2);
      expect(stats.nodeTypes.project).toBeGreaterThanOrEqual(2);
      expect(stats.nodeTypes.estimate).toBeGreaterThanOrEqual(2);
    });
    
    it('filters by entity types', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph')
        .query({ entityTypes: 'company,contact' })
        .expect(200);
      
      const { nodes } = response.body.data;
      const nodeTypes = [...new Set(nodes.map((n: any) => n.type))];
      
      expect(nodeTypes).toEqual(expect.arrayContaining(['company', 'contact']));
      expect(nodeTypes).not.toContain('deal');
      expect(nodeTypes).not.toContain('project');
      expect(nodeTypes).not.toContain('estimate');
    });
    
    it('respects maxNodes limit', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph')
        .query({ maxNodes: '5' })
        .expect(200);
      
      const { nodes } = response.body.data;
      expect(nodes.length).toBeLessThanOrEqual(5);
    });
    
    it('includes deleted entities when requested', async () => {
      // Delete a company
      companyRepo.deleteCompany(testCompanyId, 'test-user');
      
      // Without includeDeleted
      const response1 = await request(app)
        .get('/api/knowledge-graph')
        .expect(200);
      
      const deletedCompany1 = response1.body.data.nodes.find((n: any) => n.id === testCompanyId);
      expect(deletedCompany1).toBeUndefined();
      
      // With includeDeleted
      const response2 = await request(app)
        .get('/api/knowledge-graph')
        .query({ includeDeleted: 'true' })
        .expect(200);
      
      const deletedCompany2 = response2.body.data.nodes.find((n: any) => n.id === testCompanyId);
      expect(deletedCompany2).toBeDefined();
    });
  });
  
  describe('GET /api/knowledge-graph/:entityId', () => {
    it('returns subgraph for a company', async () => {
      const response = await request(app)
        .get(`/api/knowledge-graph/${testCompanyId}`)
        .query({ entityType: 'company', depth: '2' })
        .expect(200);
      
      const { nodes, links } = response.body.data;
      
      // Should include the company itself
      const companyNode = nodes.find((n: any) => n.id === testCompanyId);
      expect(companyNode).toBeDefined();
      expect(companyNode.type).toBe('company');
      
      // Should include related entities
      const nodeTypes = [...new Set(nodes.map((n: any) => n.type))];
      expect(nodeTypes).toContain('contact'); // employees
      expect(nodeTypes).toContain('deal'); // company deals
      expect(nodeTypes).toContain('project'); // company projects
      
      // Verify specific relationships
      const johnContact = nodes.find((n: any) => n.label === 'John Smith');
      const workLink = links.find((l: any) => 
        l.source === johnContact?.id && l.target === testCompanyId && l.type === 'works_at'
      );
      expect(workLink).toBeDefined();
    });
    
    it('returns subgraph for a contact', async () => {
      const response = await request(app)
        .get(`/api/knowledge-graph/${testContactId}`)
        .query({ entityType: 'contact', depth: '2' })
        .expect(200);
      
      const { nodes, links } = response.body.data;
      
      // Should include the contact itself
      const contactNode = nodes.find((n: any) => n.id === testContactId);
      expect(contactNode).toBeDefined();
      expect(contactNode.type).toBe('contact');
      
      // Should include related entities
      const companyNode = nodes.find((n: any) => n.id === testCompanyId);
      const dealNode = nodes.find((n: any) => n.id === testDealId);
      expect(companyNode).toBeDefined();
      expect(dealNode).toBeDefined();
      
      // Verify deal role relationship
      const roleLink = links.find((l: any) => 
        l.source === testDealId && l.target === testContactId && l.type === 'decision_maker'
      );
      expect(roleLink).toBeDefined();
    });
    
    it('handles different depth levels', async () => {
      // Depth 1
      const depth1Response = await request(app)
        .get(`/api/knowledge-graph/${testDealId}`)
        .query({ entityType: 'deal', depth: '1' })
        .expect(200);
      
      // Depth 2
      const depth2Response = await request(app)
        .get(`/api/knowledge-graph/${testDealId}`)
        .query({ entityType: 'deal', depth: '2' })
        .expect(200);
      
      // Depth 2 should have same or more nodes than depth 1
      expect(depth2Response.body.data.nodes.length).toBeGreaterThanOrEqual(
        depth1Response.body.data.nodes.length
      );
    });
    
    it('returns empty result for non-existent entity', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph/non-existent-id')
        .query({ entityType: 'company' })
        .expect(200);
      
      const { nodes, links, stats } = response.body.data;
      expect(nodes).toHaveLength(0);
      expect(links).toHaveLength(0);
      expect(stats.totalNodes).toBe(0);
      expect(stats.totalLinks).toBe(0);
    });
  });
  
  describe('Complex relationship scenarios', () => {
    it('handles multi-hop relationships correctly', async () => {
      const response = await request(app)
        .get(`/api/knowledge-graph/${testContactId}`)
        .query({ entityType: 'contact', depth: '3' })
        .expect(200);
      
      const { nodes, links } = response.body.data;
      
      // Should traverse: Contact → Company → Deals → Estimates
      const estimateNode = nodes.find((n: any) => n.type === 'estimate');
      expect(estimateNode).toBeDefined();
      
      // Should include project relationships
      const projectNode = nodes.find((n: any) => n.type === 'project');
      expect(projectNode).toBeDefined();
    });
    
    it('includes all relationship types in complex scenarios', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph')
        .expect(200);
      
      const { links } = response.body.data;
      const linkTypes = [...new Set(links.map((l: any) => l.type))];
      
      // Verify all expected relationship types
      expect(linkTypes).toContain('works_at'); // contact-company
      expect(linkTypes).toContain('belongs_to'); // deal-company, project-company
      expect(linkTypes).toContain('decision_maker'); // contact role in deal
      expect(linkTypes).toContain('technical_evaluator'); // contact role in deal
      expect(linkTypes).toContain('has_estimate'); // deal-estimate
      expect(linkTypes).toContain('subsidiary'); // company-company
      expect(linkTypes).toContain('sponsor'); // project contact
      expect(linkTypes).toContain('finish_to_start'); // project dependency
      expect(linkTypes).toContain('colleague'); // contact-contact
    });
    
    it('maintains data integrity across all operations', async () => {
      // Get full graph
      const fullResponse = await request(app)
        .get('/api/knowledge-graph')
        .expect(200);
      
      const { nodes: allNodes, links: allLinks, stats } = fullResponse.body.data;
      
      // Verify node count matches stats
      expect(allNodes.length).toBe(stats.totalNodes);
      expect(allLinks.length).toBe(stats.totalLinks);
      
      // Verify all links reference existing nodes
      const nodeIds = new Set(allNodes.map((n: any) => n.id));
      for (const link of allLinks) {
        expect(nodeIds.has(link.source)).toBe(true);
        expect(nodeIds.has(link.target)).toBe(true);
      }
      
      // Verify node type counts
      const actualNodeTypes: Record<string, number> = {};
      allNodes.forEach((node: any) => {
        actualNodeTypes[node.type] = (actualNodeTypes[node.type] || 0) + 1;
      });
      expect(actualNodeTypes).toEqual(stats.nodeTypes);
      
      // Verify link type counts
      const actualLinkTypes: Record<string, number> = {};
      allLinks.forEach((link: any) => {
        actualLinkTypes[link.type] = (actualLinkTypes[link.type] || 0) + 1;
      });
      expect(actualLinkTypes).toEqual(stats.linkTypes);
    });
  });
  
  describe('Error handling', () => {
    it('handles invalid entity types gracefully', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph')
        .query({ entityTypes: 'invalid,company' })
        .expect(200);
      
      // Should filter out invalid types and process valid ones
      expect(response.body.success).toBe(true);
    });
    
    it('handles malformed query parameters', async () => {
      const response = await request(app)
        .get('/api/knowledge-graph')
        .query({ maxNodes: 'invalid', includeDeleted: 'maybe' })
        .expect(200);
      
      // Should use defaults for invalid parameters
      expect(response.body.success).toBe(true);
    });
  });
});