import { XeroService } from '../../../../src/services/xero/xero-service';
import { XeroApiClient } from '../../../../src/api/clients/xero-api-client';
import type { XeroInvoice, XeroBankAccount, XeroOrganisation } from '../../../../src/types/xero';

jest.mock('../../../../src/api/clients/xero-api-client');

describe('XeroService', () => {
  let service: XeroService;
  let mockApiClient: jest.Mocked<XeroApiClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockApiClient = new XeroApiClient('mock-token') as jest.Mocked<XeroApiClient>;
    service = new XeroService(mockApiClient);
  });

  describe('getInvoices', () => {
    const mockInvoices: XeroInvoice[] = [
      {
        InvoiceID: 'inv-1',
        Type: 'ACCREC',
        InvoiceNumber: 'INV-001',
        Reference: 'REF-001',
        AmountDue: 0,
        AmountPaid: 5000,
        Total: 5000,
        SubTotal: 4500,
        TotalTax: 500,
        Status: 'PAID',
        DueDate: '2024-03-01',
        Date: '2024-02-01',
        UpdatedDateUTC: '/Date(*************)/',
        CurrencyCode: 'USD',
        FullyPaidOnDate: '2024-02-15',
        Contact: {
          ContactID: 'contact-1',
          Name: 'Client A'
        },
        LineItems: []
      },
      {
        InvoiceID: 'inv-2',
        Type: 'ACCREC',
        InvoiceNumber: 'INV-002',
        Reference: '',
        AmountDue: 3000,
        AmountPaid: 0,
        Total: 3000,
        SubTotal: 2700,
        TotalTax: 300,
        Status: 'AUTHORISED',
        DueDate: '2024-03-31',
        Date: '2024-03-01',
        UpdatedDateUTC: '/Date(1709251200000)/',
        CurrencyCode: 'USD',
        Contact: {
          ContactID: 'contact-2',
          Name: 'Client B'
        },
        LineItems: []
      }
    ];

    it('should fetch all invoices', async () => {
      mockApiClient.get.mockResolvedValue({ Invoices: mockInvoices });

      const result = await service.getInvoices('tenant-123');

      expect(result).toEqual(mockInvoices);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        {}
      );
    });

    it('should fetch invoices with status filter', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Invoices: [mockInvoices[1]] 
      });

      const result = await service.getInvoices('tenant-123', { 
        status: 'AUTHORISED' 
      });

      expect(result).toHaveLength(1);
      expect(result[0].Status).toBe('AUTHORISED');
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { where: 'Status=="AUTHORISED"' }
      );
    });

    it('should fetch invoices with date filter', async () => {
      mockApiClient.get.mockResolvedValue({ Invoices: mockInvoices });

      await service.getInvoices('tenant-123', {
        modifiedAfter: new Date('2024-01-01')
      });

      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { 
          'If-Modified-Since': new Date('2024-01-01').toISOString() 
        }
      );
    });

    it('should handle pagination', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Invoices: mockInvoices,
        pagination: { page: 1, pageSize: 100 }
      });

      await service.getInvoices('tenant-123', { 
        page: 2 
      });

      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { page: 2 }
      );
    });

    it('should handle API errors', async () => {
      mockApiClient.get.mockRejectedValue(new Error('API error'));

      await expect(service.getInvoices('tenant-123'))
        .rejects.toThrow('API error');
    });
  });

  describe('getBankAccounts', () => {
    const mockBankAccounts: XeroBankAccount[] = [
      {
        AccountID: 'acc-1',
        Code: '090',
        Name: 'Business Transaction Account',
        Type: 'BANK',
        BankAccountNumber: '123456',
        Status: 'ACTIVE',
        BankAccountType: 'BANK',
        CurrencyCode: 'USD',
        TaxType: 'NONE',
        EnablePaymentsToAccount: true,
        ShowInExpenseClaims: true,
        Class: 'ASSET',
        UpdatedDateUTC: '/Date(*************)/',
        AddToWatchlist: false
      },
      {
        AccountID: 'acc-2',
        Code: '091',
        Name: 'Business Savings Account',
        Type: 'BANK',
        BankAccountNumber: '789012',
        Status: 'ACTIVE',
        BankAccountType: 'BANK',
        CurrencyCode: 'USD',
        TaxType: 'NONE',
        EnablePaymentsToAccount: false,
        ShowInExpenseClaims: false,
        Class: 'ASSET',
        UpdatedDateUTC: '/Date(*************)/',
        AddToWatchlist: false
      }
    ];

    it('should fetch all bank accounts', async () => {
      mockApiClient.get.mockResolvedValue({ 
        BankAccounts: mockBankAccounts 
      });

      const result = await service.getBankAccounts('tenant-123');

      expect(result).toEqual(mockBankAccounts);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/BankAccounts'
      );
    });

    it('should handle empty bank accounts', async () => {
      mockApiClient.get.mockResolvedValue({ BankAccounts: [] });

      const result = await service.getBankAccounts('tenant-123');

      expect(result).toEqual([]);
    });

    it('should handle API errors', async () => {
      mockApiClient.get.mockRejectedValue(
        new Error('Unauthorized')
      );

      await expect(service.getBankAccounts('tenant-123'))
        .rejects.toThrow('Unauthorized');
    });
  });

  describe('getOrganisation', () => {
    const mockOrganisation: XeroOrganisation = {
      OrganisationID: 'org-123',
      Name: 'Test Company Ltd',
      LegalName: 'Test Company Limited',
      PaysTax: true,
      Version: 'AU',
      OrganisationType: 'COMPANY',
      BaseCurrency: 'USD',
      CountryCode: 'US',
      IsDemoCompany: false,
      OrganisationStatus: 'ACTIVE',
      RegistrationNumber: '********',
      TaxNumber: 'TAX123',
      FinancialYearEndDay: 30,
      FinancialYearEndMonth: 6,
      SalesTaxBasis: 'ACCRUAL',
      SalesTaxPeriod: 'MONTHLY',
      DefaultSalesTax: 'OUTPUT',
      DefaultPurchasesTax: 'INPUT',
      CreatedDateUTC: '/Date(*************)/',
      Timezone: 'UTC',
      ShortCode: 'TEST',
      Edition: 'BUSINESS'
    };

    it('should fetch organisation details', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Organisations: [mockOrganisation] 
      });

      const result = await service.getOrganisation('tenant-123');

      expect(result).toEqual(mockOrganisation);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Organisation'
      );
    });

    it('should handle missing organisation', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Organisations: [] 
      });

      const result = await service.getOrganisation('tenant-123');

      expect(result).toBeUndefined();
    });
  });

  describe('getBalanceSheet', () => {
    const mockBalanceSheet = {
      Reports: [{
        ReportID: 'BalanceSheet',
        ReportName: 'Balance Sheet',
        ReportType: 'BalanceSheet',
        ReportTitles: ['Balance Sheet', 'Demo Company', 'As at 31 March 2024'],
        ReportDate: '31 March 2024',
        UpdatedDateUTC: '/Date(*************)/',
        Rows: [
          {
            RowType: 'Header',
            Cells: [
              { Value: 'Assets' }
            ]
          },
          {
            RowType: 'Section',
            Title: 'Bank',
            Rows: [
              {
                RowType: 'Row',
                Cells: [
                  { Value: 'Business Bank Account' },
                  { Value: '50000.00' }
                ]
              }
            ]
          }
        ]
      }]
    };

    it('should fetch balance sheet report', async () => {
      mockApiClient.get.mockResolvedValue(mockBalanceSheet);

      const result = await service.getBalanceSheet('tenant-123');

      expect(result).toEqual(mockBalanceSheet.Reports[0]);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Reports/BalanceSheet'
      );
    });

    it('should fetch balance sheet with date parameter', async () => {
      mockApiClient.get.mockResolvedValue(mockBalanceSheet);

      await service.getBalanceSheet('tenant-123', '2024-03-31');

      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Reports/BalanceSheet',
        { date: '2024-03-31' }
      );
    });
  });

  describe('getProfitAndLoss', () => {
    const mockProfitAndLoss = {
      Reports: [{
        ReportID: 'ProfitAndLoss',
        ReportName: 'Profit and Loss',
        ReportType: 'ProfitAndLoss',
        ReportTitles: ['Profit & Loss', 'Demo Company', '1 January 2024 to 31 March 2024'],
        ReportDate: '31 March 2024',
        UpdatedDateUTC: '/Date(*************)/',
        Rows: [
          {
            RowType: 'Header',
            Cells: [
              { Value: 'Income' }
            ]
          },
          {
            RowType: 'Section',
            Title: 'Income',
            Rows: [
              {
                RowType: 'Row',
                Cells: [
                  { Value: 'Sales' },
                  { Value: '100000.00' }
                ]
              }
            ]
          }
        ]
      }]
    };

    it('should fetch profit and loss report', async () => {
      mockApiClient.get.mockResolvedValue(mockProfitAndLoss);

      const result = await service.getProfitAndLoss('tenant-123');

      expect(result).toEqual(mockProfitAndLoss.Reports[0]);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Reports/ProfitAndLoss'
      );
    });

    it('should fetch P&L with date range', async () => {
      mockApiClient.get.mockResolvedValue(mockProfitAndLoss);

      await service.getProfitAndLoss(
        'tenant-123', 
        '2024-01-01', 
        '2024-03-31'
      );

      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Reports/ProfitAndLoss',
        { 
          fromDate: '2024-01-01',
          toDate: '2024-03-31'
        }
      );
    });
  });

  describe('getCashflowStatement', () => {
    it('should fetch cashflow statement', async () => {
      const mockCashflow = {
        Reports: [{
          ReportID: 'CashflowStatement',
          ReportName: 'Statement of Cash Flows',
          ReportType: 'CashflowStatement',
          Rows: []
        }]
      };

      mockApiClient.get.mockResolvedValue(mockCashflow);

      const result = await service.getCashflowStatement('tenant-123');

      expect(result).toEqual(mockCashflow.Reports[0]);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Reports/CashflowStatement'
      );
    });
  });

  describe('error handling', () => {
    it('should handle rate limiting errors', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).statusCode = 429;
      (rateLimitError as any).headers = {
        'retry-after': '60'
      };

      mockApiClient.get.mockRejectedValue(rateLimitError);

      await expect(service.getInvoices('tenant-123'))
        .rejects.toThrow('Rate limit exceeded');
    });

    it('should handle authentication errors', async () => {
      const authError = new Error('Unauthorized');
      (authError as any).statusCode = 401;

      mockApiClient.get.mockRejectedValue(authError);

      await expect(service.getInvoices('tenant-123'))
        .rejects.toThrow('Unauthorized');
    });

    it('should handle network errors', async () => {
      mockApiClient.get.mockRejectedValue(
        new Error('Network error')
      );

      await expect(service.getInvoices('tenant-123'))
        .rejects.toThrow('Network error');
    });
  });
});