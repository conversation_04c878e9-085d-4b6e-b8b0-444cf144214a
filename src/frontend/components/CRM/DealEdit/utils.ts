/** * Utility functions for the Deal Edit page */ import { Deal, DealEstimate } from "../../../types/crm-types"; /** * Check if a field is controlled by an estimate * * @param deal The deal object * @param fieldName The name of the field to check * @param fieldOwnership Optional field ownership data * @returns True if the field is controlled by an estimate */ export function isFieldControlledByEstimate( deal: Deal, fieldName: string, fieldOwnership?: Record<string, string> ): boolean { // If we have field ownership data, use it if (fieldOwnership) { return fieldOwnership[fieldName] === 'Estimate'; } // Fallback: If the deal has no estimates, no fields are controlled by estimates if (!deal.estimates || deal.estimates.length === 0) { return false; } // Fallback: Check if the field is one of the fields that can be controlled by estimates const estimateControlledFields = ['startDate', 'endDate', 'invoiceFrequency', 'paymentTerms', 'value']; if (!estimateControlledFields.includes(fieldName)) { return false; } // Fallback: If we don't have ownership data but the deal has estimates // and the field is in the list, assume it's controlled by an estimate return true; } /** * Get the first linked estimate from a deal * * @param deal The deal object * @returns The first linked estimate or undefined if none */ export function getFirstLinkedEstimate(deal: Deal): DealEstimate | undefined { if (!deal.estimates || deal.estimates.length === 0) { return undefined; } return deal.estimates[0]; } /** * Get the URL for an estimate * * @param estimate The estimate object * @returns The URL to view the estimate */ export function getEstimateUrl(estimate: DealEstimate): string { if (estimate.type === 'internal') { return `/estimates/${estimate.id}`; } // For Harvest estimates, construct the Harvest URL // Note: This assumes the Harvest account URL is available in the environment // In a real implementation, you'd get this from your configuration return `https://onbord.harvestapp.com/estimates/${estimate.id}`; } 