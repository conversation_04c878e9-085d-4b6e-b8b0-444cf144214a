/* Tax Calendar Visualization Styles */
.tax-calendar-visualization {
  overflow: hidden;
}

/* Header and controls */
.tax-calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.tax-calendar-year-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tax-calendar-year {
  font-weight: 600;
  font-size: 0.875rem;
  color: rgba(51, 65, 85, 1);
}

.dark .tax-calendar-year {
  color: rgba(226, 232, 240, 1);
}

.tax-calendar-year-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  background-color: rgba(241, 245, 249, 0.8);
  color: rgba(51, 65, 85, 1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.2s ease;
}

.tax-calendar-year-button:hover {
  background-color: rgba(226, 232, 240, 0.8);
}

.dark .tax-calendar-year-button {
  background-color: rgba(30, 41, 59, 0.8);
  color: rgba(226, 232, 240, 1);
  border-color: rgba(51, 65, 85, 0.8);
}

.dark .tax-calendar-year-button:hover {
  background-color: rgba(51, 65, 85, 0.8);
}

/* Legend */
.tax-calendar-legend {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.tax-calendar-legend-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: rgba(51, 65, 85, 1);
}

.dark .tax-calendar-legend-item {
  color: rgba(226, 232, 240, 1);
}

.tax-calendar-legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.tax-calendar-legend-color-group,
.tax-calendar-legend-color-pair {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.tax-calendar-legend-color-pair {
  position: relative;
  width: 1.75rem;
}

.tax-calendar-legend-color-pair .tax-calendar-legend-color:first-child {
  position: absolute;
  left: 0;
  top: 0;
}

.tax-calendar-legend-color-pair .tax-calendar-legend-color:last-child {
  position: absolute;
  right: 0;
  bottom: 0;
}

/* Calendar grid */
.tax-calendar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.tax-calendar-month {
  border-radius: 0.375rem;
  overflow: hidden;
  border: 1px solid rgba(226, 232, 240, 0.8);
  background-color: rgba(249, 250, 251, 0.5);
  display: flex;
  flex-direction: column;
}

.dark .tax-calendar-month {
  border-color: rgba(51, 65, 85, 0.8);
  background-color: rgba(30, 41, 59, 0.5);
}

.tax-calendar-month-header {
  padding: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(241, 245, 249, 0.8);
  color: rgba(51, 65, 85, 1);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  font-size: 0.875rem;
  font-weight: 600;
}

.dark .tax-calendar-month-header {
  background-color: rgba(30, 41, 59, 0.8);
  color: rgba(226, 232, 240, 1);
  border-bottom-color: rgba(51, 65, 85, 0.8);
}

.tax-calendar-quarter-label {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  background-color: rgba(226, 232, 240, 0.5);
  color: rgba(51, 65, 85, 0.9);
}

.dark .tax-calendar-quarter-label {
  background-color: rgba(51, 65, 85, 0.5);
  color: rgba(226, 232, 240, 0.9);
}

/* Days grid */
.tax-calendar-days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  padding: 0.5rem;
}

/* Day of week headers */
.tax-calendar-dow {
  text-align: center;
  font-size: 0.625rem;
  font-weight: 600;
  color: rgba(100, 116, 139, 0.8);
  padding: 0.25rem 0;
}

.dark .tax-calendar-dow {
  color: rgba(148, 163, 184, 0.8);
}

/* Day squares */
.tax-calendar-day {
  aspect-ratio: 1;
  position: relative;
  border-radius: 0.125rem;
  overflow: hidden;
  font-size: 0.625rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.tax-calendar-day-empty {
  aspect-ratio: 1;
}

.tax-calendar-day-number {
  position: absolute;
  top: 1px;
  left: 1px;
  font-weight: 500;
  font-size: 0.5rem;
  z-index: 20; /* Increased z-index to ensure visibility */
  color: rgba(15, 23, 42, 0.9);
  background-color: rgba(255, 252, 240, 0.7);
  border-radius: 3px;
  padding: 0 2px;
  line-height: 1;
  pointer-events: none; /* This prevents the number from interrupting hover */
}

.dark .tax-calendar-day-number {
  color: rgba(248, 250, 252, 0.9);
  background-color: rgba(30, 41, 59, 0.7);
}

/* Split day for showing both PAYGW and GST colors */
.tax-calendar-day-split {
  position: relative;
}

/* Wrapper for each half to handle hover */
.tax-calendar-day-half-wrapper {
  position: absolute;
  width: 100%;
  height: 50%;
  left: 0;
  cursor: pointer;
  z-index: 1;
}

.tax-calendar-day-paygw-wrapper {
  top: 0;
}

.tax-calendar-day-gst-wrapper {
  bottom: 0;
}

/* Actual colored halves */
.tax-calendar-day-paygw,
.tax-calendar-day-gst {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.tax-calendar-day-paygw {
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
}

.tax-calendar-day-gst {
  border-bottom-left-radius: 0.125rem;
  border-bottom-right-radius: 0.125rem;
}

/* Payment day styling - enhanced */
.tax-calendar-payment-day {
  font-weight: 600;
  border-width: 2px;
  z-index: 2; /* Increased z-index to ensure it's above regular days */
  transition: all 0.2s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
}

.tax-calendar-payment-day::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 0.25rem;
  border: 2px dashed white;
  pointer-events: none;
  animation: pulse 2s infinite; /* Subtle pulsing animation */
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.dark .tax-calendar-payment-day::after {
  border-color: rgba(30, 41, 59, 0.8);
}

/* Highlight effect for hover - color change approach */
.tax-calendar-day-highlight {
  /* No filter needed, we'll use class swapping for colors */
}

/* Highlight for payment days */
.tax-calendar-payment-day.tax-calendar-day-highlight {
  /* No background image needed, we'll use class swapping for colors */
  box-shadow: 0 0 0 2px rgba(255, 252, 240, 1), 0 0 8px rgba(0, 0, 0, 0.1); /* Keep subtle outer glow */
  z-index: 6; /* Ensure it's above other elements */
}

/* Current day styling - more prominent */
.tax-calendar-current-day {
  box-shadow: 0 0 0 2px rgba(52, 211, 153, 0.7); /* Thicker, more visible emerald shadow */
  z-index: 5; /* Ensure it's above regular days */
}

.dark .tax-calendar-current-day {
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.7); /* Thicker, more visible emerald shadow for dark mode */
}

/* Highlight for split day halves - using color change approach */
.tax-calendar-day-half-wrapper.tax-calendar-day-highlight::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

/* Add a border to the wrapper when highlighted */
.tax-calendar-day-paygw-wrapper.tax-calendar-day-highlight {
  box-shadow: 0 0 0 2px rgba(255, 252, 240, 1), 0 0 8px rgba(0, 0, 0, 0.1); /* Add subtle outer glow */
  border-top-left-radius: 0.125rem;
  border-top-right-radius: 0.125rem;
}

.tax-calendar-day-gst-wrapper.tax-calendar-day-highlight {
  box-shadow: 0 0 0 2px rgba(255, 252, 240, 1), 0 0 8px rgba(0, 0, 0, 0.1); /* Add subtle outer glow */
  border-bottom-left-radius: 0.125rem;
  border-bottom-right-radius: 0.125rem;
}

/* No color change for highlighted elements */
.tax-calendar-day-half-wrapper.tax-calendar-day-highlight .tax-calendar-day-paygw,
.tax-calendar-day-half-wrapper.tax-calendar-day-highlight .tax-calendar-day-gst {
  /* No filter change */
}

/* Dark mode support - using color change approach */
.dark .tax-calendar-payment-day.tax-calendar-day-highlight {
  /* No background image needed, we'll use class swapping for colors */
}

.dark .tax-calendar-payment-day.tax-calendar-day-highlight,
.dark .tax-calendar-day-paygw-wrapper.tax-calendar-day-highlight,
.dark .tax-calendar-day-gst-wrapper.tax-calendar-day-highlight {
  box-shadow: 0 0 0 2px rgba(148, 163, 184, 0.9), 0 0 8px rgba(0, 0, 0, 0.2); /* Add subtle outer glow for dark mode */
}

/* Tooltip styles */
#tax-calendar-tooltip {
  position: fixed; /* Fixed position relative to the viewport */
  background-color: rgba(255, 252, 240, 0.98);
  color: rgba(15, 23, 42, 0.9);
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(226, 232, 240, 0.8);
  z-index: 10000; /* Very high z-index */
  pointer-events: none;
  white-space: normal;
  max-width: 220px;
  text-align: left;
  transition: opacity 0.15s ease-in-out;
  opacity: 0;
}

.tax-calendar-tooltip-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.tax-calendar-tooltip-content {
  font-weight: 400;
  opacity: 0.9;
}

/* Tooltip visibility class */
#tax-calendar-tooltip.visible {
  opacity: 1;
}

.dark #tax-calendar-tooltip {
  background-color: rgba(30, 41, 59, 0.98);
  color: rgba(248, 250, 252, 0.9);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  border-color: rgba(51, 65, 85, 0.8);
}

/* Footer */
.tax-calendar-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.dark .tax-calendar-footer {
  border-top-color: rgba(51, 65, 85, 0.8);
}

/* Guide panel and examples */
.tax-calendar-guide-panel {
  transition: all 0.2s ease;
}

.tax-calendar-example {
  position: relative;
}

.bg-pattern-crosshatch {
  background-image: linear-gradient(45deg, rgba(255,252,240,0.7) 25%, transparent 25%, transparent 50%, rgba(255,252,240,0.7) 50%, rgba(255,252,240,0.7) 75%, transparent 75%, transparent);
  background-size: 8px 8px;
}

.dark .bg-pattern-crosshatch {
  background-image: linear-gradient(45deg, rgba(255,252,240,0.5) 25%, transparent 25%, transparent 50%, rgba(255,252,240,0.5) 50%, rgba(255,252,240,0.5) 75%, transparent 75%, transparent);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tax-calendar-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .tax-calendar-guide-panel .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .tax-calendar-example {
    padding-bottom: 0.5rem;
  }
}

@media (max-width: 640px) {
  .tax-calendar-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }

  .tax-calendar-month-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .tax-calendar-quarter-label {
    align-self: flex-start;
  }

  .tax-calendar-day-number {
    font-size: 0.5rem;
  }
}
