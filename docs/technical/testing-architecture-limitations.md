# Testing Architecture Limitations

This document outlines the challenges encountered when attempting to implement proper unit tests for the repository layer, and why the current architecture makes isolated testing difficult.

## The Problem

The current codebase uses a global database singleton pattern where all repositories import a shared database instance from `src/api/services/db-service.ts`. This creates several testing challenges:

### 1. Global State
```typescript
// In base-repository.ts
import db from '../services/db-service';

export class BaseRepository {
  public db: any;
  
  constructor(tableName: string) {
    this.db = db; // Always uses the global instance
  }
}
```

### 2. No Dependency Injection
Repositories don't accept database connections as constructor parameters, preventing us from:
- Using in-memory test databases
- Mocking database connections
- Running tests in isolation
- Running tests in parallel

### 3. Tight Coupling
The repositories are tightly coupled to:
- The specific database instance
- The SQLite driver implementation
- The file-based database location

## Failed Approaches

### 1. In-Memory Database Testing
We attempted to use in-memory SQLite databases for testing:
```typescript
const db = new Database(':memory:');
repository = new ContactRepository(db); // ❌ Constructor doesn't accept db parameter
```

### 2. Module Mocking
We tried mocking the db-service module:
```typescript
jest.mock('../../../../src/api/services/db-service', () => ({
  default: mockDb
}));
```
This approach failed because:
- The mock wasn't consistently applied
- Module caching interfered with test isolation
- The repository still used the global instance

### 3. Interface-Only Testing
We created tests that only verified method existence:
```typescript
expect(repository.createContact).toBeDefined();
expect(repository.createContact.length).toBeLessThanOrEqual(2);
```
These tests provide minimal value as they don't verify actual behavior.

## Recommended Solutions

### 1. Dependency Injection (Preferred)
Modify repositories to accept database connections:
```typescript
export class ContactRepository extends BaseRepository {
  constructor(db?: Database) {
    super('contact', db); // Pass db to parent
  }
}
```

### 2. Integration Testing
Accept that unit testing isn't feasible and focus on integration tests:
- Use a dedicated test database
- Run tests sequentially
- Clean data between tests
- Test actual database interactions

### 3. Repository Interface Pattern
Create interfaces for repositories and test against those:
```typescript
interface IContactRepository {
  createContact(data: ContactCreate): Promise<Contact>;
  getContactById(id: string): Promise<Contact | null>;
  // ... other methods
}
```

### 4. Test Database Factory
Create a factory for test databases:
```typescript
export function createTestDatabase(): Database {
  const db = new Database(':memory:');
  // Run migrations
  return db;
}
```

## Current State

Due to these limitations, we've removed the low-value repository tests that only checked method existence. Proper testing of the repository layer will require architectural changes to support dependency injection or a commitment to integration testing.

## Impact

Without proper repository testing:
1. Database queries aren't validated
2. Data transformations aren't tested
3. Constraint violations aren't caught
4. Performance issues aren't identified
5. Regressions can occur undetected

## Next Steps

1. **Short term**: Focus on integration tests that test the full stack
2. **Medium term**: Refactor repositories to support dependency injection
3. **Long term**: Implement proper unit tests with isolated test databases