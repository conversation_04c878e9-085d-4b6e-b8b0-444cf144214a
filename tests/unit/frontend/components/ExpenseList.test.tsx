import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ExpenseList } from '../../../../src/frontend/components/Expense';
import * as api from '../../../../src/frontend/api/expenses';

jest.mock('../../../../src/frontend/api/expenses');

describe('ExpenseList', () => {
  let queryClient: QueryClient;

  const mockExpenses = [
    {
      id: '1',
      description: 'Office Supplies',
      amount: 250.50,
      date: '2024-06-15',
      category: 'supplies',
      vendor: 'Staples',
      notes: 'Monthly supplies order'
    },
    {
      id: '2',
      description: 'Team Lunch',
      amount: 150.00,
      date: '2024-06-14',
      category: 'meals',
      vendor: 'Local Restaurant',
      notes: 'Team building lunch'
    },
    {
      id: '3',
      description: 'Software License',
      amount: 500.00,
      date: '2024-06-01',
      category: 'software',
      vendor: 'Adobe',
      notes: 'Creative Cloud subscription'
    }
  ];

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });
    (api.getExpenses as jest.Mock).mockResolvedValue(mockExpenses);
  });

  const renderExpenseList = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <ExpenseList />
      </QueryClientProvider>
    );
  };

  it('should render expense list', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Team Lunch')).toBeInTheDocument();
      expect(screen.getByText('Software License')).toBeInTheDocument();
    });
  });

  it('should display expense amounts', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('$250.50')).toBeInTheDocument();
      expect(screen.getByText('$150.00')).toBeInTheDocument();
      expect(screen.getByText('$500.00')).toBeInTheDocument();
    });
  });

  it('should show expense categories', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Supplies')).toBeInTheDocument();
      expect(screen.getByText('Meals')).toBeInTheDocument();
      expect(screen.getByText('Software')).toBeInTheDocument();
    });
  });

  it('should filter expenses by search', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search expenses...');
    fireEvent.change(searchInput, { target: { value: 'lunch' } });

    expect(screen.queryByText('Office Supplies')).not.toBeInTheDocument();
    expect(screen.getByText('Team Lunch')).toBeInTheDocument();
    expect(screen.queryByText('Software License')).not.toBeInTheDocument();
  });

  it('should filter by category', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const categoryFilter = screen.getByLabelText('Filter by category');
    fireEvent.change(categoryFilter, { target: { value: 'software' } });

    expect(screen.queryByText('Office Supplies')).not.toBeInTheDocument();
    expect(screen.queryByText('Team Lunch')).not.toBeInTheDocument();
    expect(screen.getByText('Software License')).toBeInTheDocument();
  });

  it('should sort expenses by date', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const sortButton = screen.getByText('Date');
    fireEvent.click(sortButton);

    const expenseItems = screen.getAllByTestId('expense-item');
    expect(expenseItems[0]).toHaveTextContent('Office Supplies'); // Most recent
    expect(expenseItems[1]).toHaveTextContent('Team Lunch');
    expect(expenseItems[2]).toHaveTextContent('Software License');
  });

  it('should sort expenses by amount', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const sortButton = screen.getByText('Amount');
    fireEvent.click(sortButton);

    const expenseItems = screen.getAllByTestId('expense-item');
    expect(expenseItems[0]).toHaveTextContent('Software License'); // Highest amount
    expect(expenseItems[1]).toHaveTextContent('Office Supplies');
    expect(expenseItems[2]).toHaveTextContent('Team Lunch');
  });

  it('should delete expense', async () => {
    (api.deleteExpense as jest.Mock).mockResolvedValue({ success: true });
    
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const deleteButtons = screen.getAllByLabelText('Delete expense');
    fireEvent.click(deleteButtons[0]);

    // Confirm deletion
    const confirmButton = screen.getByText('Confirm Delete');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(api.deleteExpense).toHaveBeenCalledWith('1');
    });
  });

  it('should edit expense', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const editButtons = screen.getAllByLabelText('Edit expense');
    fireEvent.click(editButtons[0]);

    expect(screen.getByDisplayValue('Office Supplies')).toBeInTheDocument();
    expect(screen.getByDisplayValue('250.5')).toBeInTheDocument();
  });

  it('should show expense details on click', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const expenseItem = screen.getByText('Office Supplies').closest('[data-testid="expense-item"]');
    fireEvent.click(expenseItem!);

    expect(screen.getByText('Monthly supplies order')).toBeInTheDocument();
    expect(screen.getByText('Vendor: Staples')).toBeInTheDocument();
  });

  it('should show total expenses', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Total: $900.50')).toBeInTheDocument();
    });
  });

  it('should filter by date range', async () => {
    renderExpenseList();

    const startDateInput = screen.getByLabelText('Start Date');
    const endDateInput = screen.getByLabelText('End Date');

    fireEvent.change(startDateInput, { target: { value: '2024-06-10' } });
    fireEvent.change(endDateInput, { target: { value: '2024-06-20' } });

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
      expect(screen.getByText('Team Lunch')).toBeInTheDocument();
      expect(screen.queryByText('Software License')).not.toBeInTheDocument();
    });
  });

  it('should handle empty expense list', async () => {
    (api.getExpenses as jest.Mock).mockResolvedValue([]);
    
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('No expenses found')).toBeInTheDocument();
      expect(screen.getByText('Add your first expense')).toBeInTheDocument();
    });
  });

  it('should add new expense', async () => {
    renderExpenseList();

    const addButton = screen.getByText('Add Expense');
    fireEvent.click(addButton);

    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(screen.getByLabelText('Amount')).toBeInTheDocument();
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
  });

  it('should group expenses by category', async () => {
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const groupToggle = screen.getByLabelText('Group by category');
    fireEvent.click(groupToggle);

    expect(screen.getByText('Supplies ($250.50)')).toBeInTheDocument();
    expect(screen.getByText('Meals ($150.00)')).toBeInTheDocument();
    expect(screen.getByText('Software ($500.00)')).toBeInTheDocument();
  });

  it('should export expenses to CSV', async () => {
    const mockCreateObjectURL = jest.fn();
    const mockClick = jest.fn();
    
    global.URL.createObjectURL = mockCreateObjectURL;
    global.document.createElement = jest.fn().mockImplementation((tag) => {
      if (tag === 'a') {
        return { click: mockClick, setAttribute: jest.fn() };
      }
      return {};
    });

    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Office Supplies')).toBeInTheDocument();
    });

    const exportButton = screen.getByText('Export CSV');
    fireEvent.click(exportButton);

    expect(mockCreateObjectURL).toHaveBeenCalled();
    expect(mockClick).toHaveBeenCalled();
  });

  it('should handle loading state', () => {
    (api.getExpenses as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    renderExpenseList();

    expect(screen.getByText('Loading expenses...')).toBeInTheDocument();
  });

  it('should handle error state', async () => {
    (api.getExpenses as jest.Mock).mockRejectedValue(
      new Error('Failed to load expenses')
    );

    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Error loading expenses')).toBeInTheDocument();
      expect(screen.getByText('Try again')).toBeInTheDocument();
    });
  });

  it('should paginate expenses', async () => {
    const manyExpenses = Array.from({ length: 25 }, (_, i) => ({
      id: `${i + 1}`,
      description: `Expense ${i + 1}`,
      amount: 100 * (i + 1),
      date: '2024-06-15',
      category: 'misc',
      vendor: 'Various',
      notes: ''
    }));

    (api.getExpenses as jest.Mock).mockResolvedValue(manyExpenses);
    
    renderExpenseList();

    await waitFor(() => {
      expect(screen.getByText('Expense 1')).toBeInTheDocument();
    });

    // Should show first 20 items
    expect(screen.getByText('Expense 20')).toBeInTheDocument();
    expect(screen.queryByText('Expense 21')).not.toBeInTheDocument();

    // Navigate to next page
    const nextPageButton = screen.getByLabelText('Next page');
    fireEvent.click(nextPageButton);

    expect(screen.queryByText('Expense 20')).not.toBeInTheDocument();
    expect(screen.getByText('Expense 21')).toBeInTheDocument();
  });
});