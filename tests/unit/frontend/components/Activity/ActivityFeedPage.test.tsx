import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ActivityFeedPage } from '../../../../../../src/frontend/components/Activity/ActivityFeedPage';
import * as activityApi from '../../../../../../src/frontend/api/activity';
import { EventContext } from '../../../../../../src/frontend/contexts/EventContext';
import type { Activity } from '../../../../../../src/types/activity-types';

// Mock API
jest.mock('../../../../../../src/frontend/api/activity');

// Mock child components
jest.mock('../../../../../../src/frontend/components/Activity/ActivityTimeline', () => ({
  ActivityTimeline: ({ activities, onLoadMore }: any) => (
    <div data-testid="activity-timeline">
      {activities.map((a: any) => (
        <div key={a.id} data-testid={`activity-${a.id}`}>
          {a.action} - {a.targetType}
        </div>
      ))}
      {onLoadMore && <button onClick={onLoadMore}>Load More</button>}
    </div>
  )
}));

jest.mock('../../../../../../src/frontend/components/Activity/ActivityFilters', () => ({
  ActivityFilters: ({ filters, onChange }: any) => (
    <div data-testid="activity-filters">
      <button onClick={() => onChange({ ...filters, type: 'company' })}>
        Filter Companies
      </button>
      <button onClick={() => onChange({ ...filters, actor: '<EMAIL>' })}>
        Filter by User
      </button>
    </div>
  )
}));

jest.mock('../../../../../../src/frontend/components/Activity/ActivityStatsCard', () => ({
  ActivityStatsCard: ({ stats }: any) => (
    <div data-testid="activity-stats">
      Total: {stats.total}, Today: {stats.today}
    </div>
  )
}));

describe('ActivityFeedPage', () => {
  const mockActivities: Activity[] = [
    {
      id: 'activity-1',
      action: 'created',
      targetType: 'deal',
      targetId: 'deal-1',
      targetName: 'Enterprise Deal',
      actor: '<EMAIL>',
      actorName: 'John Doe',
      timestamp: '2024-01-20T10:00:00Z',
      metadata: { amount: 50000 }
    },
    {
      id: 'activity-2',
      action: 'updated',
      targetType: 'company',
      targetId: 'company-1',
      targetName: 'Acme Corp',
      actor: '<EMAIL>',
      actorName: 'Jane Smith',
      timestamp: '2024-01-20T09:00:00Z',
      metadata: { field: 'status', oldValue: 'prospect', newValue: 'client' }
    },
    {
      id: 'activity-3',
      action: 'linked',
      targetType: 'estimate',
      targetId: 'estimate-1',
      targetName: 'Q1 Project',
      actor: '<EMAIL>',
      actorName: 'John Doe',
      timestamp: '2024-01-19T16:00:00Z',
      metadata: { linkedTo: 'deal-1', linkedType: 'deal' }
    }
  ];

  const mockStats = {
    total: 150,
    today: 12,
    thisWeek: 45,
    byType: {
      deal: 50,
      company: 40,
      contact: 30,
      estimate: 30
    },
    byAction: {
      created: 60,
      updated: 50,
      deleted: 10,
      linked: 30
    }
  };

  const mockEventContext = {
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (activityApi.getActivities as jest.Mock).mockResolvedValue({
      activities: mockActivities,
      hasMore: true,
      total: 150
    });
    (activityApi.getActivityStats as jest.Mock).mockResolvedValue(mockStats);
  });

  const renderComponent = () => {
    return render(
      <EventContext.Provider value={mockEventContext}>
        <ActivityFeedPage />
      </EventContext.Provider>
    );
  };

  describe('Initial Load', () => {
    it('should render loading state initially', () => {
      renderComponent();
      expect(screen.getByText(/loading activities/i)).toBeInTheDocument();
    });

    it('should load and display activities', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
        expect(screen.getByTestId('activity-1')).toHaveTextContent('created - deal');
        expect(screen.getByTestId('activity-2')).toHaveTextContent('updated - company');
        expect(screen.getByTestId('activity-3')).toHaveTextContent('linked - estimate');
      });

      expect(activityApi.getActivities).toHaveBeenCalledWith({
        page: 1,
        limit: 20
      });
    });

    it('should display activity statistics', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-stats')).toHaveTextContent('Total: 150, Today: 12');
      });

      expect(activityApi.getActivityStats).toHaveBeenCalled();
    });

    it('should handle loading errors', async () => {
      (activityApi.getActivities as jest.Mock).mockRejectedValue(new Error('Network error'));

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/error loading activities/i)).toBeInTheDocument();
      });
    });
  });

  describe('Filtering', () => {
    it('should filter activities by type', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-filters')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Filter Companies'));

      await waitFor(() => {
        expect(activityApi.getActivities).toHaveBeenLastCalledWith({
          page: 1,
          limit: 20,
          type: 'company'
        });
      });
    });

    it('should filter activities by actor', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-filters')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Filter by User'));

      await waitFor(() => {
        expect(activityApi.getActivities).toHaveBeenLastCalledWith({
          page: 1,
          limit: 20,
          actor: '<EMAIL>'
        });
      });
    });

    it('should reset to page 1 when filters change', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      // Load more first
      fireEvent.click(screen.getByText('Load More'));

      await waitFor(() => {
        expect(activityApi.getActivities).toHaveBeenCalledWith({
          page: 2,
          limit: 20
        });
      });

      // Apply filter - should reset to page 1
      fireEvent.click(screen.getByText('Filter Companies'));

      await waitFor(() => {
        expect(activityApi.getActivities).toHaveBeenLastCalledWith({
          page: 1,
          limit: 20,
          type: 'company'
        });
      });
    });
  });

  describe('Pagination', () => {
    it('should load more activities', async () => {
      const page1Activities = mockActivities.slice(0, 2);
      const page2Activities = [mockActivities[2]];

      (activityApi.getActivities as jest.Mock)
        .mockResolvedValueOnce({
          activities: page1Activities,
          hasMore: true,
          total: 150
        })
        .mockResolvedValueOnce({
          activities: page2Activities,
          hasMore: false,
          total: 150
        });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Load More'));

      await waitFor(() => {
        expect(activityApi.getActivities).toHaveBeenCalledWith({
          page: 2,
          limit: 20
        });
        expect(screen.getByTestId('activity-3')).toBeInTheDocument();
      });
    });

    it('should hide load more button when no more activities', async () => {
      (activityApi.getActivities as jest.Mock).mockResolvedValue({
        activities: mockActivities,
        hasMore: false,
        total: 3
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      expect(screen.queryByText('Load More')).not.toBeInTheDocument();
    });

    it('should show loading state while loading more', async () => {
      (activityApi.getActivities as jest.Mock)
        .mockResolvedValueOnce({
          activities: mockActivities,
          hasMore: true,
          total: 150
        })
        .mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Load More'));

      expect(screen.getByText(/loading more/i)).toBeInTheDocument();
    });
  });

  describe('Real-time Updates', () => {
    it('should listen for new activities', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      expect(mockEventContext.on).toHaveBeenCalledWith('activity:created', expect.any(Function));
    });

    it('should prepend new activities to the list', async () => {
      let eventHandler: Function;
      (mockEventContext.on as jest.Mock).mockImplementation((event, handler) => {
        if (event === 'activity:created') {
          eventHandler = handler;
        }
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      const newActivity: Activity = {
        id: 'activity-new',
        action: 'created',
        targetType: 'contact',
        targetId: 'contact-new',
        targetName: 'New Contact',
        actor: '<EMAIL>',
        actorName: 'Mike Johnson',
        timestamp: new Date().toISOString(),
        metadata: {}
      };

      // Trigger new activity event
      eventHandler!(newActivity);

      await waitFor(() => {
        expect(screen.getByTestId('activity-new')).toBeInTheDocument();
        expect(screen.getByTestId('activity-new')).toHaveTextContent('created - contact');
      });
    });

    it('should update stats when new activity is added', async () => {
      let eventHandler: Function;
      (mockEventContext.on as jest.Mock).mockImplementation((event, handler) => {
        if (event === 'activity:created') {
          eventHandler = handler;
        }
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-stats')).toHaveTextContent('Total: 150, Today: 12');
      });

      // Mock updated stats
      (activityApi.getActivityStats as jest.Mock).mockResolvedValue({
        ...mockStats,
        total: 151,
        today: 13
      });

      const newActivity: Activity = {
        id: 'activity-new',
        action: 'created',
        targetType: 'contact',
        targetId: 'contact-new',
        targetName: 'New Contact',
        actor: '<EMAIL>',
        actorName: 'Mike Johnson',
        timestamp: new Date().toISOString(),
        metadata: {}
      };

      eventHandler!(newActivity);

      await waitFor(() => {
        expect(screen.getByTestId('activity-stats')).toHaveTextContent('Total: 151, Today: 13');
      });
    });

    it('should clean up event listeners on unmount', () => {
      const { unmount } = renderComponent();

      unmount();

      expect(mockEventContext.off).toHaveBeenCalledWith('activity:created', expect.any(Function));
    });
  });

  describe('Refresh', () => {
    it('should have refresh button', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toBeInTheDocument();
    });

    it('should refresh activities and stats', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);

      await waitFor(() => {
        expect(activityApi.getActivities).toHaveBeenCalledTimes(2);
        expect(activityApi.getActivityStats).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Empty State', () => {
    it('should show empty state when no activities', async () => {
      (activityApi.getActivities as jest.Mock).mockResolvedValue({
        activities: [],
        hasMore: false,
        total: 0
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/no activities yet/i)).toBeInTheDocument();
      });
    });

    it('should show empty state for filtered results', async () => {
      (activityApi.getActivities as jest.Mock)
        .mockResolvedValueOnce({
          activities: mockActivities,
          hasMore: true,
          total: 150
        })
        .mockResolvedValueOnce({
          activities: [],
          hasMore: false,
          total: 0
        });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('Filter Companies'));

      await waitFor(() => {
        expect(screen.getByText(/no activities found/i)).toBeInTheDocument();
      });
    });
  });

  describe('Date Grouping', () => {
    it('should group activities by date', async () => {
      const activitiesWithDates = [
        { ...mockActivities[0], timestamp: '2024-01-20T10:00:00Z' },
        { ...mockActivities[1], timestamp: '2024-01-20T09:00:00Z' },
        { ...mockActivities[2], timestamp: '2024-01-19T16:00:00Z' }
      ];

      (activityApi.getActivities as jest.Mock).mockResolvedValue({
        activities: activitiesWithDates,
        hasMore: false,
        total: 3
      });

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText(/today/i)).toBeInTheDocument();
        expect(screen.getByText(/yesterday/i)).toBeInTheDocument();
      });
    });
  });

  describe('Activity Actions', () => {
    it('should allow clicking on activity items', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('activity-1')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('activity-1'));

      // Should emit navigation event
      expect(mockEventContext.emit).toHaveBeenCalledWith('navigate', {
        to: '/crm/deals/deal-1/edit'
      });
    });
  });
});