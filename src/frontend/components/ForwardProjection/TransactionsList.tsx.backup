import React from "react";
import { useProjection } from "./ProjectionContext";
import { useTransactions } from "./hooks/useTransactions";
import { useIsDesktop } from "../../hooks/useMediaQuery";
import TransactionsListItem from "./TransactionsListItem";
import FilterHeader from "./Transactions/FilterHeader";
import TransactionFilters from "./TransactionFilters";
import { Card } from "../shared/Card";
import { DataList } from "../shared/lists";

/**
 * Component for displaying the list of upcoming transactions.
 * No virtualization, no scrolling - simple list that shows all transactions.
 */
const TransactionsList = () => {
  const { highlightedDate, projectionData } = useProjection();
  const isDesktop = useIsDesktop();

  // Use our custom hook for transaction data and filtering logic
  const {
    filteredTransactions,
    filters,
    setFilters,
    filtersExpanded,
    setFiltersExpanded,
    totalTransactions,
  } = useTransactions();

  // Handle clearing all filters
  const clearFilters = () => {
    setFilters({
      types: [],
      sources: [],
      timeRange: [null, null],
      searchText: "",
      amountRange: [null, null],
    });
  };

  // Early return if transaction data isn't ready
  if (!filteredTransactions) return null;

  // Debug: Find GST transactions
  const gstTransactions = filteredTransactions.filter(
    (t) =>
      t.description &&
      (t.description.includes("GST Payment") || t.description.includes("BAS"))
  );
  console.log("GST Transactions:", gstTransactions);

  return (
    <div className="space-y-fluid-md onboarding-transactions">
      <h2 className="text-fluid-lg font-medium text-primary">
        Upcoming Transactions
      </h2>

      <Card className="p-0">
        {/* Filter header component */}
        <FilterHeader
          filtersExpanded={filtersExpanded}
          toggleFilters={() => setFiltersExpanded(!filtersExpanded)}
          filters={filters}
          clearFilters={clearFilters}
        />

        {/* Filter panel (only when expanded) */}
        {filtersExpanded && (
          <div
            id="transaction-filters-panel"
            className="p-fluid-md border-t border-gray-200 dark:border-gray-700"
          >
            <TransactionFilters
              transactions={projectionData?.projectedTransactions || []}
              filters={filters}
              onFilterChange={setFilters}
            />
          </div>
        )}

        {/* Responsive transaction display - uses JS media query for reliability */}
        <div>
          {!isDesktop ? (
            // Mobile view - uses DataList with cards
            <div className="p-4">
              <DataList
                variant="default"
                density="default"
                data={filteredTransactions}
                loading={false}
                empty={filteredTransactions.length === 0}
                emptyMessage="No transactions found"
                emptyIcon={
                  <svg
                    className="w-12 h-12"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                    />
                  </svg>
                }
                emptyAction={
                  totalTransactions > 0 ? (
                    <button
                      onClick={clearFilters}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/30 dark:hover:bg-primary-800/30 dark:text-primary-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Clear Filters
                    </button>
                  ) : undefined
                }
                keyExtractor={(transaction) => transaction.id}
                renderItem={(transaction, index) => {
                  const transactionDateString = new Date(transaction.date)
                    .toISOString()
                    .split("T")[0];
                  const isDateHighlighted =
                    highlightedDate === transactionDateString;

                  return (
                    <TransactionsListItem
                      transaction={transaction}
                      isHighlighted={isDateHighlighted}
                      index={index}
                      isMobile={true}
                    />
                  );
                }}
              />
            </div>
          ) : (
            // Desktop view - simple table without DataList
            <div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-center w-12">
                        Flow
                      </th>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">
                        Date
                      </th>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">
                        Description
                      </th>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">
                        Type
                      </th>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-left">
                        Source
                      </th>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-right">
                        Amount
                      </th>
                      <th className="px-4 py-2.5 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider text-right">
                        Balance
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredTransactions.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="px-4 py-12 text-center">
                          <div className="flex flex-col items-center">
                            <svg
                              className="w-12 h-12 text-gray-400 mb-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
                              />
                            </svg>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                              No transactions found
                            </p>
                            {totalTransactions > 0 && (
                              <button
                                onClick={clearFilters}
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900/30 dark:hover:bg-primary-800/30 dark:text-primary-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                              >
                                Clear Filters
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ) : (
                      filteredTransactions.map((transaction, index) => {
                        const transactionDateString = new Date(transaction.date)
                          .toISOString()
                          .split("T")[0];
                        const isDateHighlighted =
                          highlightedDate === transactionDateString;

                        return (
                          <TransactionsListItem
                            key={transaction.id}
                            transaction={transaction}
                            isHighlighted={isDateHighlighted}
                            index={index}
                            isMobile={false}
                          />
                        );
                      })
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default TransactionsList;
