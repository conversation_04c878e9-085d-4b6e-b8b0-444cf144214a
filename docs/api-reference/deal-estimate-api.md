# Deal-Estimate API Documentation

This document describes the API endpoints for managing deal-estimate relationships in the Onbord Financial Dashboard.

## Overview

The Deal-Estimate API provides endpoints for linking and unlinking estimates to deals. As of January 2025, **only internal estimates can be linked to deals**. Harvest estimates are read-only and cannot be linked or unlinked.

## Key Changes (January 2025)

- **Harvest estimates are now read-only**: They can be viewed but not linked to deals
- **Only internal (draft) estimates** can be linked to deals
- **Existing Harvest estimate links** are preserved but cannot be modified
- **Simplified synchronization**: Removed complex Harvest estimate sync logic

## Endpoints

### Get Deal Estimates

Retrieves all estimates linked to a specific deal.

```
GET /api/crm/deals/:id/estimates
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "estimate-uuid",
      "type": "draft",  // Only 'draft' type for new links
      "estimateType": "internal",
      "linkedAt": "2025-01-06T10:00:00Z",
      "linkedBy": "<EMAIL>"
    }
  ]
}
```

### Batch Get Deal Estimates (Added January 2025)

Retrieves estimates for multiple deals in a single request. This endpoint significantly improves performance when loading many deals.

```
POST /api/crm/deals/batch-estimates
```

**Request Body:**
```json
{
  "dealIds": ["deal-uuid-1", "deal-uuid-2", "deal-uuid-3", ...]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "deal-uuid-1": [
      {
        "id": "estimate-uuid",
        "type": "draft",
        "estimateType": "internal",
        "linkedAt": "2025-01-06T10:00:00Z",
        "linkedBy": "<EMAIL>"
      }
    ],
    "deal-uuid-2": [],
    "deal-uuid-3": [
      {
        "id": "estimate-uuid-2",
        "type": "draft",
        "estimateType": "internal",
        "linkedAt": "2025-01-05T14:00:00Z",
        "linkedBy": "<EMAIL>"
      }
    ]
  }
}
```

**Performance Notes:**
- Maximum 100 deals per request
- Frontend automatically chunks larger requests
- Rate limit: 120 requests/minute (300 in development)
- Reduces API calls from N (one per deal) to 1-2 batch requests

### Link Estimate to Deal

Links an internal estimate to a deal. **Only accepts 'draft' estimate type.**

```
POST /api/crm/deals/:id/estimates
```

**Request Body:**
```json
{
  "estimateId": "estimate-uuid",
  "estimateType": "draft",  // Required: Must be 'draft'
  "linkedBy": "<EMAIL>"  // Optional
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Estimate linked to deal successfully",
  "deal": {
    "id": "deal-uuid",
    "value": 25000,
    "startDate": "2025-01-15",
    "endDate": "2025-03-15",
    "invoiceFrequency": "monthly",
    "paymentTerms": 14,
    // ... other deal fields with updated values from estimate
  }
}
```

**Response (Error - Invalid Type):**
```json
{
  "success": false,
  "error": "Only draft estimates can be linked to deals",
  "validTypes": ["draft"]
}
```

### Get Deal Field Ownership

Retrieves field ownership information for a deal to determine which fields are controlled by estimates.

```
GET /api/crm/deals/:id/field-ownership
```

**Response:**
```json
{
  "success": true,
  "data": {
    "value": "Estimate",
    "startDate": "Estimate", 
    "endDate": "Estimate",
    "invoiceFrequency": "Estimate",
    "paymentTerms": "Estimate",
    "probability": "Estimate"
  }
}
```

### Unlink Estimate from Deal

Unlinks an estimate from a deal. **Only works for internal (draft) estimates.**

```
DELETE /api/crm/deals/:id/estimates/:estimateId?estimateType=draft
```

**Query Parameters:**
- `estimateType` (required): Must be 'draft'

**Response (Success):**
```json
{
  "success": true,
  "message": "Estimate unlinked from deal successfully"
}
```

**Response (Error - Harvest Estimate):**
```json
{
  "success": false,
  "error": "Only draft estimates can be unlinked from deals",
  "validTypes": ["draft"]
}
```

## Business Logic

### Automatic Deal Updates

When linking an internal estimate to a deal, the following fields are automatically updated and become read-only:

1. **Deal Value**: Updated from estimate's `totalFees` and marked as estimate-controlled
2. **Project Dates**: `startDate` and `endDate` from estimate with field ownership set
3. **Invoice Settings**: `invoiceFrequency` and `paymentTerms` with ownership tracking
4. **Probability**: Set based on deal stage if not already set

Note: Deal name is NOT controlled by estimates. Deal names remain editable and control the linked estimate's project name (one-way sync: deal → estimate).

### Field Ownership System

The system uses a comprehensive field ownership tracking mechanism:

- **Field Ownership**: Each field tracks which source controls it (`'Estimate'`, `'User'`, etc.)
- **Read-Only Enforcement**: Fields controlled by estimates become read-only in the UI
- **Visual Indicators**: Controlled fields show "Linked to estimate" hints and special styling
- **Ownership Clearing**: When estimates are unlinked, field ownership is cleared making fields editable again
- **API Response**: Successful linking returns the updated deal data for immediate UI updates

### Name Synchronization (Updated December 2025)

Deal and estimate names follow a one-way synchronization pattern:

- **Deal → Estimate**: When a deal name changes, the linked estimate's project name automatically updates
- **No Reverse Sync**: Estimate project names do NOT update deal names
- **Always Editable**: Deal names are never locked by estimates, allowing HubSpot sync and user edits
- **Field Ownership**: The estimate's `project_name` field is marked as "Deal-controlled"
- **HubSpot Compatible**: This design ensures HubSpot can always update deal names during sync

## Integration Notes

### Frontend Components

The following components have been enhanced for the improved field ownership system:

1. **DealEstimatesSection**: 
   - Shows info banner about estimate-controlled fields
   - Filters to only show linkable draft estimates
   - Enhanced query invalidation for real-time updates
   
2. **EstimateLinkModal**: 
   - Only displays internal estimates for linking
   - Shows estimate total calculations with logging
   - Enhanced error handling and loading states
   
3. **DealEditPage**: 
   - Fetches and passes field ownership data to child components
   - Automatic form data updates when deal changes
   - Real-time field ownership tracking
   
4. **DealInfoSection**: 
   - Accepts fieldOwnership prop for controlled field checks
   - Visual indicators for estimate-controlled fields
   - Disabled input styling for read-only fields

### Repository Changes

The `DealEstimateRepository` now validates estimate types:
```typescript
linkDealToEstimate(dealId, estimateId, estimateType) {
  if (estimateType !== 'internal') {
    throw new Error('Only internal estimates can be linked to deals');
  }
  // ... linking logic
}
```

## Migration Guide

If you have existing Harvest estimates linked to deals:

1. These links will remain visible in the UI
2. The estimates will display as read-only
3. You cannot unlink these estimates
4. You cannot link new Harvest estimates

To replace a Harvest estimate with an internal one:
1. Create a new internal estimate with the same details
2. Link the internal estimate to the deal
3. The Harvest estimate will remain linked but read-only

## Error Handling

Common error responses:

| Status Code | Error | Description |
|-------------|-------|-------------|
| 400 | Invalid estimate type | Attempting to link/unlink Harvest estimate |
| 404 | Deal not found | The specified deal ID doesn't exist |
| 404 | Estimate not found | The specified estimate ID doesn't exist |
| 500 | Failed to link estimate | Database or internal error |

## Best Practices

1. **Always check estimate type** before attempting to link
2. **Use internal estimates** for all new deal-estimate relationships
3. **Preserve existing data**: Don't attempt to modify historical Harvest links
4. **Clear user communication**: Inform users that Harvest estimates are read-only