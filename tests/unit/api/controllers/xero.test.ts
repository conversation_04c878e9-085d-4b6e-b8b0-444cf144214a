import { Request, Response, NextFunction } from 'express';
import * as xeroController from '../../../../src/api/controllers/xero';
import { XeroService } from '../../../../src/services/xero/xero-service';
import { XeroBillService } from '../../../../src/services/xero/bill-service';
import { ActivityService } from '../../../../src/api/services/activity-service';
import { createError } from '../../../../src/utils/error';

// Mock services
jest.mock('../../../../src/services/xero/xero-service');
jest.mock('../../../../src/services/xero/bill-service');
jest.mock('../../../../src/api/services/activity-service');
jest.mock('../../../../src/utils/error');

describe('Xero Controller', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;
  let mockXeroService: jest.Mocked<XeroService>;
  let mockBillService: jest.Mocked<XeroBillService>;
  let mockActivityService: jest.Mocked<ActivityService>;

  beforeEach(() => {
    jest.clearAllMocks();

    mockReq = {
      user: { tenantId: 'tenant-123' },
      query: {},
      params: {},
      body: {}
    };

    mockRes = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    mockXeroService = new XeroService(null as any) as jest.Mocked<XeroService>;
    mockBillService = new XeroBillService(null as any) as jest.Mocked<XeroBillService>;
    mockActivityService = new ActivityService(null as any) as jest.Mocked<ActivityService>;

    // Mock the service instances used in the controller
    (XeroService as any).mockImplementation(() => mockXeroService);
    (XeroBillService as any).mockImplementation(() => mockBillService);
    (ActivityService as any).mockImplementation(() => mockActivityService);
  });

  describe('getInvoices', () => {
    const mockInvoices = [
      {
        InvoiceID: 'inv-1',
        InvoiceNumber: 'INV-001',
        Type: 'ACCREC',
        Status: 'PAID',
        Total: 5000,
        AmountDue: 0,
        Contact: { ContactID: 'contact-1', Name: 'Client A' }
      },
      {
        InvoiceID: 'inv-2',
        InvoiceNumber: 'INV-002',
        Type: 'ACCREC',
        Status: 'AUTHORISED',
        Total: 3000,
        AmountDue: 3000,
        Contact: { ContactID: 'contact-2', Name: 'Client B' }
      }
    ];

    it('should return all invoices', async () => {
      mockXeroService.getInvoices.mockResolvedValue(mockInvoices);

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getInvoices).toHaveBeenCalledWith('tenant-123', {});
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockInvoices,
        count: 2
      });
    });

    it('should filter invoices by status', async () => {
      mockReq.query = { status: 'AUTHORISED' };
      mockXeroService.getInvoices.mockResolvedValue([mockInvoices[1]]);

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getInvoices).toHaveBeenCalledWith('tenant-123', {
        status: 'AUTHORISED'
      });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: [mockInvoices[1]],
        count: 1
      });
    });

    it('should handle date filters', async () => {
      mockReq.query = { modifiedAfter: '2024-01-01' };
      mockXeroService.getInvoices.mockResolvedValue(mockInvoices);

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getInvoices).toHaveBeenCalledWith('tenant-123', {
        modifiedAfter: new Date('2024-01-01')
      });
    });

    it('should handle service errors', async () => {
      const error = new Error('Xero API error');
      mockXeroService.getInvoices.mockRejectedValue(error);

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(error);
    });

    it('should return 401 if no tenant ID', async () => {
      mockReq.user = {};

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'No Xero tenant ID found'
      });
    });
  });

  describe('getBills', () => {
    const mockBills = [
      {
        InvoiceID: 'bill-1',
        InvoiceNumber: 'BILL-001',
        Type: 'ACCPAY',
        Status: 'PAID',
        Total: 2000,
        AmountDue: 0,
        Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
      },
      {
        InvoiceID: 'bill-2',
        InvoiceNumber: 'BILL-002',
        Type: 'ACCPAY',
        Status: 'AUTHORISED',
        Total: 5000,
        AmountDue: 5000,
        DueDate: '2024-03-15',
        Contact: { ContactID: 'supplier-2', Name: 'Supplier B' }
      }
    ];

    it('should return all bills', async () => {
      mockBillService.getBills.mockResolvedValue(mockBills);

      await xeroController.getBills(mockReq as Request, mockRes as Response, mockNext);

      expect(mockBillService.getBills).toHaveBeenCalledWith('tenant-123', {});
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockBills,
        count: 2
      });
    });

    it('should return overdue bills when requested', async () => {
      mockReq.query = { overdue: 'true' };
      const overdueBill = {
        ...mockBills[1],
        daysOverdue: 5
      };
      mockBillService.getOverdueBills.mockResolvedValue([overdueBill]);

      await xeroController.getBills(mockReq as Request, mockRes as Response, mockNext);

      expect(mockBillService.getOverdueBills).toHaveBeenCalledWith('tenant-123');
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: [overdueBill],
        count: 1
      });
    });

    it('should return upcoming bills when requested', async () => {
      mockReq.query = { upcoming: '30' };
      mockBillService.getUpcomingBills.mockResolvedValue([mockBills[1]]);

      await xeroController.getBills(mockReq as Request, mockRes as Response, mockNext);

      expect(mockBillService.getUpcomingBills).toHaveBeenCalledWith('tenant-123', 30);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: [mockBills[1]],
        count: 1
      });
    });
  });

  describe('getBankAccounts', () => {
    const mockBankAccounts = [
      {
        AccountID: 'acc-1',
        Code: '090',
        Name: 'Business Transaction Account',
        Type: 'BANK',
        Status: 'ACTIVE',
        BankAccountNumber: '123456'
      },
      {
        AccountID: 'acc-2',
        Code: '091',
        Name: 'Business Savings Account',
        Type: 'BANK',
        Status: 'ACTIVE',
        BankAccountNumber: '789012'
      }
    ];

    it('should return bank accounts', async () => {
      mockXeroService.getBankAccounts.mockResolvedValue(mockBankAccounts);

      await xeroController.getBankAccounts(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getBankAccounts).toHaveBeenCalledWith('tenant-123');
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockBankAccounts
      });
    });

    it('should handle empty bank accounts', async () => {
      mockXeroService.getBankAccounts.mockResolvedValue([]);

      await xeroController.getBankAccounts(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: []
      });
    });
  });

  describe('getOrganisation', () => {
    const mockOrganisation = {
      OrganisationID: 'org-123',
      Name: 'Test Company Ltd',
      LegalName: 'Test Company Limited',
      BaseCurrency: 'USD',
      CountryCode: 'US',
      TaxNumber: 'TAX123'
    };

    it('should return organisation details', async () => {
      mockXeroService.getOrganisation.mockResolvedValue(mockOrganisation);

      await xeroController.getOrganisation(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getOrganisation).toHaveBeenCalledWith('tenant-123');
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockOrganisation
      });
    });

    it('should handle missing organisation', async () => {
      mockXeroService.getOrganisation.mockResolvedValue(undefined);

      await xeroController.getOrganisation(mockReq as Request, mockRes as Response, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: 'Organisation not found'
      });
    });
  });

  describe('getBalanceSheet', () => {
    const mockBalanceSheet = {
      ReportID: 'BalanceSheet',
      ReportName: 'Balance Sheet',
      ReportDate: '2024-03-31',
      Rows: []
    };

    it('should return balance sheet report', async () => {
      mockXeroService.getBalanceSheet.mockResolvedValue(mockBalanceSheet);

      await xeroController.getBalanceSheet(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getBalanceSheet).toHaveBeenCalledWith('tenant-123', undefined);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockBalanceSheet
      });
    });

    it('should accept date parameter', async () => {
      mockReq.query = { date: '2024-03-31' };
      mockXeroService.getBalanceSheet.mockResolvedValue(mockBalanceSheet);

      await xeroController.getBalanceSheet(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getBalanceSheet).toHaveBeenCalledWith('tenant-123', '2024-03-31');
    });
  });

  describe('getProfitAndLoss', () => {
    const mockProfitAndLoss = {
      ReportID: 'ProfitAndLoss',
      ReportName: 'Profit and Loss',
      ReportDate: '2024-03-31',
      Rows: []
    };

    it('should return profit and loss report', async () => {
      mockXeroService.getProfitAndLoss.mockResolvedValue(mockProfitAndLoss);

      await xeroController.getProfitAndLoss(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getProfitAndLoss).toHaveBeenCalledWith(
        'tenant-123',
        undefined,
        undefined
      );
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: mockProfitAndLoss
      });
    });

    it('should accept date range parameters', async () => {
      mockReq.query = { fromDate: '2024-01-01', toDate: '2024-03-31' };
      mockXeroService.getProfitAndLoss.mockResolvedValue(mockProfitAndLoss);

      await xeroController.getProfitAndLoss(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.getProfitAndLoss).toHaveBeenCalledWith(
        'tenant-123',
        '2024-01-01',
        '2024-03-31'
      );
    });
  });

  describe('syncData', () => {
    it('should sync Xero data and log activity', async () => {
      const syncResult = {
        invoices: { synced: 10, errors: 0 },
        bills: { synced: 5, errors: 1 },
        bankAccounts: { synced: 2, errors: 0 }
      };

      mockXeroService.syncAllData = jest.fn().mockResolvedValue(syncResult);
      mockActivityService.create.mockResolvedValue(undefined);

      await xeroController.syncData(mockReq as Request, mockRes as Response, mockNext);

      expect(mockXeroService.syncAllData).toHaveBeenCalledWith('tenant-123');
      expect(mockActivityService.create).toHaveBeenCalledWith({
        action: 'xero_sync',
        targetType: 'system',
        targetId: null,
        metadata: syncResult
      });
      expect(mockRes.json).toHaveBeenCalledWith({
        success: true,
        data: syncResult,
        message: 'Xero data synced successfully'
      });
    });

    it('should handle sync errors', async () => {
      const error = new Error('Sync failed');
      mockXeroService.syncAllData = jest.fn().mockRejectedValue(error);

      await xeroController.syncData(mockReq as Request, mockRes as Response, mockNext);

      expect(mockActivityService.create).toHaveBeenCalledWith({
        action: 'xero_sync_failed',
        targetType: 'system',
        targetId: null,
        metadata: { error: 'Sync failed' }
      });
      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('Error Handling', () => {
    it('should handle rate limit errors', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).statusCode = 429;
      (rateLimitError as any).headers = { 'retry-after': '60' };
      
      mockXeroService.getInvoices.mockRejectedValue(rateLimitError);

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Rate limit exceeded',
        statusCode: 429
      }));
    });

    it('should handle authentication errors', async () => {
      const authError = new Error('Unauthorized');
      (authError as any).statusCode = 401;
      
      mockXeroService.getInvoices.mockRejectedValue(authError);

      await xeroController.getInvoices(mockReq as Request, mockRes as Response, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        message: 'Unauthorized',
        statusCode: 401
      }));
    });
  });
});