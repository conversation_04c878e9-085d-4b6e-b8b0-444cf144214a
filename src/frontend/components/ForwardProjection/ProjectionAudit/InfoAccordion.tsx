import React from 'react';

interface InfoAccordionProps {
  showInformation: boolean;
  setShowInformation: (show: boolean) => void;
}

const InfoAccordion: React.FC<InfoAccordionProps> = ({ 
  showInformation, 
  setShowInformation 
}) => {
  return (
    <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-blue-50/50 dark:bg-blue-900/10">
      <button
        className="w-full flex justify-between items-center focus:outline-none"
        onClick={() => setShowInformation(!showInformation)}
      >
        <div className="flex items-center">
          <svg className="w-5 h-5 text-blue-500 dark:text-blue-400 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="font-medium text-sm text-blue-700 dark:text-blue-300">Understanding Smart Forecast Decisions</span>
        </div>
        <svg
          className={`w-5 h-5 text-blue-500 dark:text-blue-400 transition-transform duration-200 ${showInformation ? 'transform rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {showInformation && (
        <div className="mt-3 text-sm text-gray-600 dark:text-gray-300 border-l-2 border-blue-300 dark:border-blue-700 pl-4 py-0.5 ml-1.5">
          <p className="mb-2">
            Smart Forecast Decisions shows exactly how the system decides which income projections to include or exclude from your forecast, providing transparency into the decision-making process.
          </p>
          <h4 className="font-medium text-blue-800 dark:text-blue-300 mb-1 mt-3">Main Filtering Rules:</h4>
          <ul className="list-disc pl-5 space-y-1">
            <li><strong>Uninvoiced Work Rule:</strong> Prevents double-counting by excluding uninvoiced work when projected income exists within ±3 days</li>
            <li><strong>Real Invoice Rule:</strong> Gives precedence to actual invoices by excluding projected income when real invoices exist within ±5 days</li>
            <li><strong>Payment Terms Rule:</strong> Excludes projected income if it falls within payment terms from today (income expected too soon should already be invoiced)</li>
          </ul>
          <div className="mt-3 flex justify-between items-center">
            <span className="text-xs text-blue-800/80 dark:text-blue-300/80 italic">
              Click on rows with the <span className="px-1 py-0.5 bg-blue-100/70 dark:bg-blue-900/30 rounded text-xs font-medium">Reconciled</span> badge to see detailed invoice relationships
            </span>
            <a href="#troubleshooting-section" className="text-xs text-blue-600 dark:text-blue-400 hover:underline">
              More details in Help docs →
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default InfoAccordion;