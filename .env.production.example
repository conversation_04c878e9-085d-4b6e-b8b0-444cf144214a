# Production environment for Render deployment
NODE_ENV=production
API_PORT=3000
SESSION_SECRET=generate_a_secure_random_string_here

# Use your Render app URL
FRONTEND_URL=https://your-render-app-name.onrender.com

# Xero credentials
XERO_CLIENT_ID=your_client_id_here
XERO_CLIENT_SECRET=your_client_secret_here
XERO_REDIRECT_URI=https://your-render-app-name.onrender.com/api/xero/callback
XERO_SCOPES=openid profile email accounting.transactions accounting.reports.read accounting.settings offline_access payroll.employees payroll.payruns

# Feedback email configuration
FEEDBACK_EMAIL=<EMAIL>
FEEDBACK_EMAIL_API_KEY=re_your_resend_api_key_here

# Harvest credentials
HARVEST_ACCESS_TOKEN=your_harvest_token_here
HARVEST_ACCOUNT_ID=your_harvest_account_id_here
