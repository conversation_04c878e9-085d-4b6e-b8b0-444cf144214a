/**
 * Company Repository Test
 * 
 * Tests for the company repository focusing on critical database operations.
 * These tests use an in-memory SQLite database for isolation.
 */
import { CompanyRepository } from '@/api/repositories/company-repository';
import { Database } from '@/database';
import { runMigrations } from '@/api/services/db-init';

describe('CompanyRepository', () => {
  let repository: CompanyRepository;
  let db: Database;
  
  beforeAll(async () => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Run migrations to set up schema
    await runMigrations(db);
  });
  
  beforeEach(() => {
    repository = new CompanyRepository();
    
    // Clear data before each test
    db.prepare('DELETE FROM company').run();
    db.prepare('DELETE FROM deal').run();
    db.prepare('DELETE FROM field_ownership').run();
    db.prepare('DELETE FROM change_log').run();
  });
  
  afterAll(() => {
    db.close();
  });
  
  describe('createCompany', () => {
    it('creates a company with all fields', () => {
      const companyData = {
        name: 'Test Company',
        industry: 'Technology',
        size: '10-50',
        website: 'https://test.com',
        address: '123 Test St',
        description: 'A test company',
        source: 'manual',
        radarState: 'active',
        priority: 'high',
        currentSpend: 5000,
        potentialSpend: 10000
      };
      
      const result = repository.createCompany(companyData, 'test-user');
      
      expect(result.id).toBeDefined();
      expect(result.name).toBe('Test Company');
      expect(result.industry).toBe('Technology');
      expect(result.priority).toBe('High'); // Should be normalized
      expect(result.createdBy).toBe('test-user');
      expect(result.updatedBy).toBe('test-user');
    });
    
    it('creates a company with minimal fields', () => {
      const companyData = {
        name: 'Minimal Company'
      };
      
      const result = repository.createCompany(companyData, 'test-user');
      
      expect(result.id).toBeDefined();
      expect(result.name).toBe('Minimal Company');
      expect(result.source).toBe('manual'); // Default value
    });
    
    it('normalizes priority values', () => {
      const priorities = ['high', 'medium', 'low', 'qualified out'];
      
      priorities.forEach(priority => {
        const company = repository.createCompany(
          { name: `Company ${priority}`, priority },
          'test-user'
        );
        
        const expected = priority.split(' ').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
        
        expect(company.priority).toBe(expected);
      });
    });
  });
  
  describe('updateCompany', () => {
    it('updates company fields', () => {
      // Create a company first
      const company = repository.createCompany(
        { name: 'Original Name' },
        'test-user'
      );
      
      // Update it
      const updates = {
        name: 'Updated Name',
        industry: 'Finance',
        website: 'https://updated.com'
      };
      
      const result = repository.updateCompany(company.id, updates, 'update-user');
      
      expect(result.name).toBe('Updated Name');
      expect(result.industry).toBe('Finance');
      expect(result.website).toBe('https://updated.com');
      expect(result.updatedBy).toBe('update-user');
    });
    
    it('creates change log entries for updates', () => {
      const company = repository.createCompany(
        { name: 'Test Company', industry: 'Tech' },
        'test-user'
      );
      
      repository.updateCompany(
        company.id,
        { industry: 'Finance' },
        'update-user'
      );
      
      // Check change log
      const changes = db.prepare(`
        SELECT * FROM change_log 
        WHERE entity_type = 'company' AND entity_id = ?
      `).all(company.id);
      
      expect(changes).toHaveLength(1);
      expect(changes[0].field_name).toBe('industry');
      expect(changes[0].old_value).toBe('Tech');
      expect(changes[0].new_value).toBe('Finance');
    });
  });
  
  describe('getAllCompanies', () => {
    it('returns companies with deal statistics', () => {
      // Create companies
      const company1 = repository.createCompany({ name: 'Company 1' }, 'test');
      const company2 = repository.createCompany({ name: 'Company 2' }, 'test');
      
      // Create deals for company1
      db.prepare(`
        INSERT INTO deal (id, name, stage, value, company_id, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'), 'test', 'test')
      `).run('deal-1', 'Deal 1', 'Proposal', 5000, company1.id);
      
      db.prepare(`
        INSERT INTO deal (id, name, stage, value, company_id, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'), 'test', 'test')
      `).run('deal-2', 'Deal 2', 'Negotiation', 3000, company1.id);
      
      const companies = repository.getAllCompanies();
      
      expect(companies).toHaveLength(2);
      
      const companyWithDeals = companies.find(c => c.id === company1.id);
      expect(companyWithDeals?.activeDealsCount).toBe(2);
      expect(companyWithDeals?.totalDealValue).toBe(8000);
      
      const companyWithoutDeals = companies.find(c => c.id === company2.id);
      expect(companyWithoutDeals?.activeDealsCount).toBe(0);
      expect(companyWithoutDeals?.totalDealValue).toBe(0);
    });
    
    it('excludes soft-deleted companies', () => {
      repository.createCompany({ name: 'Active Company' }, 'test');
      const deletedCompany = repository.createCompany({ name: 'Deleted Company' }, 'test');
      
      // Soft delete one company
      db.prepare(`
        UPDATE company SET deleted_at = datetime('now') WHERE id = ?
      `).run(deletedCompany.id);
      
      const companies = repository.getAllCompanies();
      
      expect(companies).toHaveLength(1);
      expect(companies[0].name).toBe('Active Company');
    });
  });
  
  describe('getCompanyForHubSpotUpdate', () => {
    it('returns necessary fields for HubSpot sync', () => {
      const company = repository.createCompany({
        name: 'Test Company',
        hubspotId: 'hs-123',
        website: 'https://test.com'
      }, 'test');
      
      const result = repository.getCompanyForHubSpotUpdate(company.id);
      
      expect(result).toBeDefined();
      expect(result?.id).toBe(company.id);
      expect(result?.hubspotId).toBe('hs-123');
      expect(result?.name).toBe('Test Company');
      expect(result?.website).toBe('https://test.com');
    });
  });
  
  describe('unlinkFromHubSpot', () => {
    it('removes HubSpot ID from company', () => {
      const company = repository.createCompany({
        name: 'Test Company',
        hubspotId: 'hs-123'
      }, 'test');
      
      repository.unlinkFromHubSpot(company.id);
      
      const updated = repository.getCompanyById(company.id);
      expect(updated?.hubspotId).toBeNull();
    });
  });
  
  describe('mergeFromExternalSystems', () => {
    it('merges data from HubSpot and Harvest', () => {
      const company = repository.createCompany({
        name: 'Original Name',
        website: 'https://original.com'
      }, 'test');
      
      const hubspotData = {
        name: 'HubSpot Name',
        website: 'https://hubspot.com',
        industry: 'Technology'
      };
      
      const harvestData = {
        name: 'Harvest Name',
        address: '456 Harvest St'
      };
      
      repository.mergeFromExternalSystems(
        company.id,
        hubspotData,
        harvestData,
        'merger-user'
      );
      
      const updated = repository.getCompanyById(company.id);
      
      // HubSpot data should win for fields it provides
      expect(updated?.name).toBe('HubSpot Name');
      expect(updated?.website).toBe('https://hubspot.com');
      expect(updated?.industry).toBe('Technology');
      
      // Harvest data should be used for fields HubSpot doesn't provide
      expect(updated?.address).toBe('456 Harvest St');
    });
    
    it('tracks field ownership during merge', () => {
      const company = repository.createCompany({ name: 'Test' }, 'test');
      
      repository.mergeFromExternalSystems(
        company.id,
        { name: 'HubSpot Name' },
        null,
        'merger'
      );
      
      const ownership = db.prepare(`
        SELECT * FROM field_ownership 
        WHERE entity_type = 'company' AND entity_id = ? AND field_name = 'name'
      `).get(company.id);
      
      expect(ownership).toBeDefined();
      expect(ownership.source).toBe('hubspot');
    });
  });
  
  describe('getLinkedCompanies', () => {
    beforeEach(() => {
      // Create test companies with different linking states
      repository.createCompany({ 
        name: 'Both Systems',
        hubspotId: 'hs-1',
        harvestId: 1
      }, 'test');
      
      repository.createCompany({ 
        name: 'HubSpot Only',
        hubspotId: 'hs-2'
      }, 'test');
      
      repository.createCompany({ 
        name: 'Harvest Only',
        harvestId: 2
      }, 'test');
      
      repository.createCompany({ 
        name: 'No Links'
      }, 'test');
    });
    
    it('returns companies with both links', () => {
      const result = repository.getLinkedCompanies('both');
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Both Systems');
    });
    
    it('returns companies with HubSpot only', () => {
      const result = repository.getLinkedCompanies('hubspot_only');
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('HubSpot Only');
    });
    
    it('returns companies with Harvest only', () => {
      const result = repository.getLinkedCompanies('harvest_only');
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Harvest Only');
    });
    
    it('returns companies with no links', () => {
      const result = repository.getLinkedCompanies('none');
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('No Links');
    });
  });
});