import React from 'react';
import { DayInfo, GSTQuarter, TooltipData } from './types';
import { useTheme } from './ThemeContext';
import { getCurrentQuarterInfo, getMonthName } from './calendarUtils';
import { getColorClass } from '../../utils/colors';

interface PaymentDayProps {
  day: DayInfo;
  gstQuarters?: GSTQuarter[];
  setTooltip: React.Dispatch<React.SetStateAction<TooltipData>>;
  setHighlightedElements: React.Dispatch<React.SetStateAction<Set<string>>>;
  highlightedElements: Set<string>;
}

const PaymentDay: React.FC<PaymentDayProps> = ({
  day,
  gstQuarters,
  setTooltip,
  setHighlightedElements,
  highlightedElements
}) => {
  const { isDarkMode } = useTheme();
  
  // Determine if this is a PAYGW or GST payment day
  const isPAYGW = day.isPAYGWDueDate && day.paygwPaymentForMonth !== null;
  const isGST = day.isGSTDueDate && day.gstPaymentForQuarter !== null;
  
  if (!isPAYGW && !isGST) return null;
  
  // Generate unique ID for this payment day
  const id = isPAYGW 
    ? `paygw-payment-${day.paygwPaymentForMonth}`
    : `gst-payment-${day.gstPaymentForQuarter}`;
  
  // Check if this payment day is highlighted
  const isHighlighted = highlightedElements.has(id);
  
  // Get color class
  const colorIndex = isPAYGW 
    ? day.paygwPaymentForMonth as number
    : day.gstPaymentForQuarter as number;
  
  const colorClass = getColorClass(
    isPAYGW ? 'paygw' : 'gst',
    colorIndex,
    true,
    isDarkMode,
    isHighlighted
  );
  
  // Handle hover
  const handleHover = (e: React.MouseEvent) => {
    // Create a new set with all elements to highlight
    const elementsToHighlight = new Set<string>();
    
    if (isPAYGW) {
      const monthIndex = day.paygwPaymentForMonth as number;
      
      // Add all days in the month this payment is for
      for (let i = 1; i <= 31; i++) {
        elementsToHighlight.add(`paygw-${monthIndex}-${i}`);
      }
      
      // Add this payment day
      elementsToHighlight.add(id);
      
      // Show tooltip
      setTooltip({
        visible: true,
        title: 'PAYGW Payment Due',
        content: `Payment for ${getMonthName(monthIndex)} PAYGW accrual`,
        x: e.clientX,
        y: e.clientY
      });
    } else if (isGST) {
      const quarterIndex = day.gstPaymentForQuarter as number;
      
      // Add all days in the quarter this payment is for
      for (let i = 1; i <= 31; i++) {
        elementsToHighlight.add(`gst-${quarterIndex}-${i}`);
      }
      
      // Add this payment day
      elementsToHighlight.add(id);
      
      // Determine if this is the current quarter's payment date
      const currentQuarterInfo = getCurrentQuarterInfo();
      const isCurrentQuarterPayment = (
        (quarterIndex === 0 && currentQuarterInfo.quarter === 3) || // Q3 payment
        (quarterIndex === 1 && currentQuarterInfo.quarter === 4) || // Q4 payment
        (quarterIndex === 2 && currentQuarterInfo.quarter === 1) || // Q1 payment
        (quarterIndex === 3 && currentQuarterInfo.quarter === 2)    // Q2 payment
      );
      
      // Get quarter info
      const quarterInfo = gstQuarters?.[quarterIndex];
      
      // Show tooltip
      setTooltip({
        visible: true,
        title: 'GST/BAS Payment Due',
        content: isCurrentQuarterPayment
          ? `Payment for ${quarterInfo?.label} GST accrual (BAS Prediction available)`
          : `Payment for ${quarterInfo?.label} GST accrual`,
        x: e.clientX,
        y: e.clientY
      });
    }
    
    // Update highlighted elements
    setHighlightedElements(elementsToHighlight);
  };
  
  // Handle mouse leave
  const handleMouseLeave = () => {
    setHighlightedElements(new Set());
    setTooltip(prev => ({ ...prev, visible: false }));
  };
  
  // Special case: if this is the current day, show a special tooltip
  const handleCurrentDayHover = (e: React.MouseEvent) => {
    setTooltip({
      visible: true,
      title: 'Today',
      content: `${getMonthName(new Date().getMonth())} ${day.day}, ${new Date().getFullYear()}`,
      x: e.clientX,
      y: e.clientY
    });
  };
  
  // Check if this is a current quarter payment (for GST)
  const isCurrentQuarterPayment = isGST && (() => {
    const currentQuarterInfo = getCurrentQuarterInfo();
    const quarterIndex = day.gstPaymentForQuarter as number;
    return (
      (quarterIndex === 0 && currentQuarterInfo.quarter === 3) || // Q3 payment
      (quarterIndex === 1 && currentQuarterInfo.quarter === 4) || // Q4 payment
      (quarterIndex === 2 && currentQuarterInfo.quarter === 1) || // Q1 payment
      (quarterIndex === 3 && currentQuarterInfo.quarter === 2)    // Q2 payment
    );
  })();
  
  return (
    <div
      key={`day-${day.day}`}
      id={id}
      className={`tax-calendar-day ${colorClass} tax-calendar-payment-day ${isHighlighted ? 'tax-calendar-day-highlight' : ''} ${day.isCurrentDay ? 'tax-calendar-current-day' : ''}`}
      onMouseEnter={handleHover}
      onMouseLeave={handleMouseLeave}
    >
      {/* Current day indicator */}
      {day.isCurrentDay && (
        <div 
          className="absolute inset-0 border-2 border-green-400 dark:border-green-500 rounded-sm z-10 pointer-events-none"
          onMouseEnter={handleCurrentDayHover}
        ></div>
      )}
      
      {/* Lightning bolt icon for current quarter's payment date */}
      {isCurrentQuarterPayment && (
        <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
          <div className="w-5 h-5 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-yellow-600 dark:text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      )}
      
      <span className="tax-calendar-day-number">{day.day}</span>
    </div>
  );
};

export default PaymentDay;
