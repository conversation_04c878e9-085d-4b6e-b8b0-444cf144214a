#!/bin/bash

# Run Critical Tests Script
# This script runs the most important tests that should always pass

echo "🧪 Running Critical Business Logic Tests..."
echo "========================================="

# Array of critical test files
CRITICAL_TESTS=(
    "deal-projection-service.test.ts"
    "transaction-service.test.ts"
    "projection-filter-service.test.ts"
    "company-repository.test.ts"
    "cashflow.test.ts"
)

FAILED_TESTS=()
PASSED_TESTS=()

# Run each test
for test in "${CRITICAL_TESTS[@]}"; do
    echo ""
    echo "Running: $test"
    echo "-----------------"
    
    if npm test -- --testPathPattern="$test" --no-coverage --silent; then
        echo "✅ PASSED: $test"
        PASSED_TESTS+=("$test")
    else
        echo "❌ FAILED: $test"
        FAILED_TESTS+=("$test")
    fi
done

echo ""
echo "========================================="
echo "Test Summary:"
echo "✅ Passed: ${#PASSED_TESTS[@]} tests"
echo "❌ Failed: ${#FAILED_TESTS[@]} tests"

if [ ${#FAILED_TESTS[@]} -gt 0 ]; then
    echo ""
    echo "Failed tests:"
    for test in "${FAILED_TESTS[@]}"; do
        echo "  - $test"
    done
    exit 1
else
    echo ""
    echo "🎉 All critical tests passed!"
    exit 0
fi