/* Modern Design System - 2025 UI/UX Enhancements with Flexoki Theme */

/* === FLEXOKI COLOR SYSTEM === */
/* 
 * All color values are generated from the single source of truth in flexoki-theme.ts
 * This ensures consistency across the entire application
 */

/* Light Theme Variables */
:root {
  /* Core UI Colors */
  --color-bg: #FFFCF0; /* flexoki paper */
  --color-surface: #F2F0E5; /* flexoki base-50 */
  --color-surface-alt: #E6E4D9; /* flexoki base-100 */
  --color-border: #CECDC3; /* flexoki base-200 */
  --color-border-strong: #B7B5AC; /* flexoki base-300 */
  
  /* Text Colors */
  --color-text: #100F0F; /* flexoki black */
  --color-text-muted: #6F6E69; /* flexoki base-600 */
  --color-text-subtle: #878580; /* flexoki base-500 */
  --color-text-disabled: #9F9D96; /* flexoki base-400 */
  
  /* Primary Colors (Blue) */
  --color-primary: #205EA6; /* flexoki blue-600 */
  --color-primary-hover: #1A4F8C; /* flexoki blue-700 */
  --color-primary-text: #205EA6;
  --color-primary-50: #E1ECEB;
  --color-primary-100: #C6DDE8;
  --color-primary-200: #ABCFE2;
  --color-primary-300: #92BFDB;
  --color-primary-400: #66A0C8;
  --color-primary-500: #3171B2;
  --color-primary-600: #205EA6;
  --color-primary-700: #1A4F8C;
  --color-primary-800: #163B66;
  --color-primary-900: #12253B;
  --color-primary-950: #101A24;
  
  /* Success Colors (Green) */
  --color-success: #66800B; /* flexoki green-600 */
  --color-success-hover: #536907; /* flexoki green-700 */
  --color-success-text: #536907; /* flexoki green-700 */
  --color-success-bg: #EDEECF; /* flexoki green-50 */
  --color-success-50: #EDEECF;
  --color-success-100: #DDE2B2;
  --color-success-200: #CDD597;
  --color-success-300: #BEC97E;
  --color-success-400: #A0AF54;
  --color-success-500: #879A39;
  --color-success-600: #66800B;
  --color-success-700: #536907;
  --color-success-800: #3D4C07;
  --color-success-900: #252D09;
  
  /* Warning Colors (Orange) */
  --color-warning: #BC5215; /* flexoki orange-600 */
  --color-warning-hover: #9D4310; /* flexoki orange-700 */
  --color-warning-text: #9D4310; /* flexoki orange-700 */
  --color-warning-bg: #FFE7CE; /* flexoki orange-50 */
  --color-warning-50: #FFE7CE;
  --color-warning-100: #FED3AF;
  --color-warning-200: #FCC192;
  --color-warning-300: #F9AE77;
  --color-warning-400: #EC8B49;
  --color-warning-500: #DA702C;
  --color-warning-600: #BC5215;
  --color-warning-700: #9D4310;
  --color-warning-800: #71320D;
  --color-warning-900: #40200D;
  
  /* Error Colors (Red) */
  --color-error: #AF3029; /* flexoki red-600 */
  --color-error-hover: #942822; /* flexoki red-700 */
  --color-error-text: #942822; /* flexoki red-700 */
  --color-error-bg: #FFE1D5; /* flexoki red-50 */
  --color-error-50: #FFE1D5;
  --color-error-100: #FFCABB;
  --color-error-200: #FDB2A2;
  --color-error-300: #F89A8A;
  --color-error-400: #E8705F;
  --color-error-500: #D14D41;
  --color-error-600: #AF3029;
  --color-error-700: #942822;
  --color-error-800: #6C201C;
  --color-error-900: #3E1715;
  
  /* Info Colors (Cyan) */
  --color-info: #24837B; /* flexoki cyan-600 */
  --color-info-hover: #1C6C66; /* flexoki cyan-700 */
  --color-info-text: #1C6C66; /* flexoki cyan-700 */
  --color-info-bg: #DDF1E4; /* flexoki cyan-50 */
  
  /* Neutral Colors (Base scale) */
  --color-gray-50: #F2F0E5;
  --color-gray-100: #E6E4D9;
  --color-gray-200: #CECDC3;
  --color-gray-300: #B7B5AC;
  --color-gray-400: #9F9D96;
  --color-gray-500: #878580;
  --color-gray-600: #6F6E69;
  --color-gray-700: #575653;
  --color-gray-800: #403E3C;
  --color-gray-900: #282726;
  --color-gray-950: #1C1B1A;
  
  /* Shadows - Modern Layered with Flexoki colors */
  --shadow-xs: 0 1px 2px rgba(var(--color-shadow-rgb), 0.05);
  --shadow-sm: 0 1px 3px rgba(var(--color-shadow-rgb), 0.1), 0 1px 2px rgba(var(--color-shadow-rgb), 0.06);
  --shadow-md: 0 4px 6px -1px rgba(var(--color-shadow-rgb), 0.1), 0 2px 4px -1px rgba(var(--color-shadow-rgb), 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(var(--color-shadow-rgb), 0.1), 0 4px 6px -2px rgba(var(--color-shadow-rgb), 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(var(--color-shadow-rgb), 0.1), 0 10px 10px -5px rgba(var(--color-shadow-rgb), 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(var(--color-shadow-rgb), 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(var(--color-shadow-rgb), 0.06);
  
  /* Transitions - Smooth Spring */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-spring: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* RGB component values for theme-aware rgba() usage */
  --color-shadow-rgb: 16, 15, 15; /* flexoki black #100F0F */
  --color-paper-rgb: 255, 252, 240; /* flexoki paper #FFFCF0 */
  --color-base-900-rgb: 30, 28, 24; /* flexoki base-950 #1E1C18 */
  --color-primary-rgb: 32, 94, 166; /* flexoki blue-600 #205EA6 */
  --color-purple-rgb: 163, 77, 171; /* flexoki purple-600 #A34DAB */
}

/* Dark Theme Variables */
@media (prefers-color-scheme: dark) {
  :root {
    /* Core UI Colors */
    --color-bg: #100F0F; /* flexoki black */
    --color-surface: #1C1B1A; /* flexoki base-950 */
    --color-surface-alt: #282726; /* flexoki base-900 */
    --color-border: #403E3C; /* flexoki base-800 */
    --color-border-strong: #575653; /* flexoki base-700 */
    
    /* RGB component values for dark theme */
    --color-shadow-rgb: 0, 0, 0; /* Pure black for cleaner dark shadows */
    --color-paper-rgb: 30, 28, 24; /* flexoki base-950 for dark theme */
    --color-base-900-rgb: 16, 15, 15; /* flexoki black */
    --color-primary-rgb: 67, 133, 190; /* flexoki blue-400 #4385BE */
    --color-purple-rgb: 184, 117, 188; /* flexoki purple-400 #B875BC */
    
    /* Text Colors */
    --color-text: #CECDC3; /* flexoki base-200 */
    --color-text-muted: #9F9D96; /* flexoki base-400 */
    --color-text-subtle: #878580; /* flexoki base-500 */
    --color-text-disabled: #6F6E69; /* flexoki base-600 */
    
    /* Primary Colors (Blue) */
    --color-primary: #4385BE; /* flexoki blue-400 */
    --color-primary-hover: #66A0C8; /* flexoki blue-300 */
    --color-primary-text: #4385BE;
    
    /* Success Colors (Green) */
    --color-success: #879A39; /* flexoki green-400 */
    --color-success-hover: #A0AF54; /* flexoki green-300 */
    --color-success-text: #879A39;
    --color-success-bg: rgba(135, 154, 57, 0.2); /* 20% opacity */
    
    /* Warning Colors (Orange) */
    --color-warning: #DA702C; /* flexoki orange-400 */
    --color-warning-hover: #EC8B49; /* flexoki orange-300 */
    --color-warning-text: #DA702C;
    --color-warning-bg: rgba(218, 112, 44, 0.2); /* 20% opacity */
    
    /* Error Colors (Red) */
    --color-error: #D14D41; /* flexoki red-400 */
    --color-error-hover: #E8705F; /* flexoki red-300 */
    --color-error-text: #D14D41;
    --color-error-bg: rgba(209, 77, 65, 0.2); /* 20% opacity */
    
    /* Info Colors (Cyan) */
    --color-info: #3AA99F; /* flexoki cyan-400 */
    --color-info-hover: #5ABDAC; /* flexoki cyan-300 */
    --color-info-text: #3AA99F;
    --color-info-bg: rgba(58, 169, 159, 0.2); /* 20% opacity */
    
    /* Shadows - Modern Layered for dark theme */
    --shadow-xs: 0 1px 2px rgba(var(--color-shadow-rgb), 0.1);
    --shadow-sm: 0 1px 3px rgba(var(--color-shadow-rgb), 0.2), 0 1px 2px rgba(var(--color-shadow-rgb), 0.12);
    --shadow-md: 0 4px 6px -1px rgba(var(--color-shadow-rgb), 0.2), 0 2px 4px -1px rgba(var(--color-shadow-rgb), 0.12);
    --shadow-lg: 0 10px 15px -3px rgba(var(--color-shadow-rgb), 0.2), 0 4px 6px -2px rgba(var(--color-shadow-rgb), 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(var(--color-shadow-rgb), 0.2), 0 10px 10px -5px rgba(var(--color-shadow-rgb), 0.08);
    --shadow-2xl: 0 25px 50px -12px rgba(var(--color-shadow-rgb), 0.5);
    --shadow-inner: inset 0 2px 4px 0 rgba(var(--color-shadow-rgb), 0.12);
  }
}

/* Explicit dark mode class support */
.dark {
  /* Core UI Colors */
  --color-bg: #100F0F;
  --color-surface: #1C1B1A;
  --color-surface-alt: #282726;
  --color-border: #403E3C;
  --color-border-strong: #575653;
  
  /* RGB component values for dark theme */
  --color-shadow-rgb: 0, 0, 0;
  --color-paper-rgb: 30, 28, 24;
  --color-base-900-rgb: 16, 15, 15;
  --color-primary-rgb: 67, 133, 190;
  --color-purple-rgb: 184, 117, 188;
  
  /* Text Colors */
  --color-text: #CECDC3;
  --color-text-muted: #9F9D96;
  --color-text-subtle: #878580;
  --color-text-disabled: #6F6E69;
  
  /* Primary Colors (Blue) */
  --color-primary: #4385BE;
  --color-primary-hover: #66A0C8;
  --color-primary-text: #4385BE;
  
  /* Success Colors (Green) */
  --color-success: #879A39;
  --color-success-hover: #A0AF54;
  --color-success-text: #879A39;
  --color-success-bg: rgba(135, 154, 57, 0.2);
  
  /* Warning Colors (Orange) */
  --color-warning: #DA702C;
  --color-warning-hover: #EC8B49;
  --color-warning-text: #DA702C;
  --color-warning-bg: rgba(218, 112, 44, 0.2);
  
  /* Error Colors (Red) */
  --color-error: #D14D41;
  --color-error-hover: #E8705F;
  --color-error-text: #D14D41;
  --color-error-bg: rgba(209, 77, 65, 0.2);
  
  /* Info Colors (Cyan) */
  --color-info: #3AA99F;
  --color-info-hover: #5ABDAC;
  --color-info-text: #3AA99F;
  --color-info-bg: rgba(58, 169, 159, 0.2);
  
  /* Shadows - Modern Layered for dark theme */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.12);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.08);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.12);
}

/* === MODERN BUTTON STYLES === */
@layer components {
  /* Base button with modern styling */
  .btn-modern {
    @apply relative inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg;
    @apply transition-all duration-200 ease-out;
    @apply transform-gpu active:scale-95;
    @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    background: linear-gradient(to bottom, rgba(var(--color-paper-rgb), 0.1), transparent);
    box-shadow: var(--shadow-sm);
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* Modern hover effect */
  .btn-modern::before {
    content: '';
    @apply absolute inset-0 rounded-lg opacity-0 transition-opacity duration-200;
    background: linear-gradient(to bottom, rgba(var(--color-paper-rgb), 0.2), transparent);
  }

  .btn-modern:hover::before {
    @apply opacity-100;
  }

  /* Primary button with gradient */
  .btn-modern--primary {
    background-color: var(--color-primary);
    color: var(--color-bg);
    box-shadow: 0 1px 3px rgba(16, 15, 15, 0.12), 0 1px 2px rgba(16, 15, 15, 0.24);
    @apply transition-all duration-200;
  }
  
  .btn-modern--primary:hover {
    background-color: var(--color-primary-hover);
  }
  
  .btn-modern--primary:active {
    background-color: var(--color-primary-hover);
    transform: scale(0.98);
  }
  
  .btn-modern--primary:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  .btn-modern--primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.24);
  }

  /* Secondary button with subtle style */
  .btn-modern--secondary {
    background-color: var(--color-surface);
    color: var(--color-text);
    @apply transition-all duration-200;
  }
  
  .btn-modern--secondary:hover {
    background-color: var(--color-surface-alt);
  }
  
  .btn-modern--secondary:focus-visible {
    outline: 2px solid var(--color-text-subtle);
    outline-offset: 2px;
  }

  /* Ghost button */
  .btn-modern--ghost {
    background-color: transparent;
    color: var(--color-text-muted);
    box-shadow: none;
    @apply transition-all duration-200;
  }
  
  .btn-modern--ghost:hover {
    background-color: var(--color-surface);
  }

  /* Loading state */
  .btn-modern--loading {
    @apply pointer-events-none opacity-70;
  }

  .btn-modern--loading::after {
    content: '';
    @apply absolute inset-0 flex items-center justify-center;
    background: inherit;
    border-radius: inherit;
  }

  /* Mobile-optimized sizes */
  .btn-modern--sm {
    @apply px-4 py-2 text-xs;
    min-height: 36px;
  }

  .btn-modern--md {
    @apply px-6 py-3 text-sm;
    min-height: 44px; /* Touch target */
  }

  .btn-modern--lg {
    @apply px-8 py-4 text-base;
    min-height: 52px;
  }
}

/* === MODERN FORM INPUTS === */
@layer components {
  /* Modern input field */
  .input-modern {
    @apply w-full px-4 py-3 rounded-lg transition-all duration-200;
    color: var(--color-text);
    background-color: var(--color-bg);
    border: 1px solid var(--color-border);
    min-height: 44px; /* Touch target */
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .input-modern:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(32, 94, 166, 0.1); /* Using primary-600 with opacity */
  }

  /* Floating label effect */
  .input-group {
    @apply relative;
  }

  .input-group .input-modern {
    @apply pt-6 pb-2;
  }

  .input-label {
    @apply absolute left-4 top-3 text-sm transition-all duration-200 pointer-events-none;
    color: var(--color-text-subtle);
    transform-origin: left center;
  }

  .input-modern:focus ~ .input-label,
  .input-modern:not(:placeholder-shown) ~ .input-label {
    transform: translateY(-1rem) scale(0.85);
    color: var(--color-primary);
  }

  /* Input states */
  .input-modern--error {
    border-color: var(--color-error);
  }
  
  .input-modern--error:focus {
    border-color: var(--color-error);
    box-shadow: 0 0 0 3px var(--color-error-bg);
  }

  .input-modern--success {
    border-color: var(--color-success);
  }
  
  .input-modern--success:focus {
    border-color: var(--color-success);
    box-shadow: 0 0 0 3px var(--color-success-bg);
  }
}

/* === MODERN CARD STYLES === */
@layer components {
  /* Enhanced card with modern depth */
  .card-modern {
    @apply rounded-xl overflow-hidden transition-all duration-300;
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    padding: theme("spacing.fluid-md");
  }

  /* Container query variants for card padding */
  @container (min-width: 300px) {
    .card-modern {
      padding: theme("spacing.fluid-lg");
    }
  }

  @container (min-width: 500px) {
    .card-modern {
      padding: theme("spacing.fluid-xl");
    }
  }

  /* Interactive card */
  .card-modern--interactive {
    @apply cursor-pointer;
  }

  .card-modern--interactive:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.14);
  }

  .card-modern--interactive:active {
    transform: translateY(0);
  }

  /* Glass morphism card */
  .card-glass {
    @apply backdrop-blur-md rounded-xl;
    background-color: rgba(255, 252, 240, 0.8); /* paper with opacity */
    border: 1px solid rgba(206, 205, 195, 0.5); /* base-200 with opacity */
    box-shadow: 0 8px 32px rgba(16, 15, 15, 0.08);
  }
  
  .dark .card-glass {
    background-color: rgba(16, 15, 15, 0.8); /* black with opacity */
    border-color: rgba(64, 62, 60, 0.5); /* base-800 with opacity */
  }
}

/* === MODERN ANIMATIONS === */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animation utilities */
.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

.animate-slide-down {
  animation: slide-down 0.3s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* === MICRO-INTERACTIONS === */
@layer utilities {
  /* Haptic feedback simulation */
  .haptic-light {
    @apply active:animate-pulse;
  }

  .haptic-medium {
    @apply active:scale-95 transition-transform duration-75;
  }

  .haptic-heavy {
    @apply active:scale-90 transition-transform duration-100;
  }

  /* Spring animations */
  .spring-bounce {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .spring-smooth {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  /* Touch optimizations */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* Momentum scrolling */
  .scroll-smooth {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Safe areas for modern devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* === FOCUS STATES === */
@layer components {
  /* Modern focus ring */
  .focus-ring {
    @apply focus:outline-none;
  }
  
  .focus-ring:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  /* Smooth focus transition */
  .focus-smooth {
    @apply transition-all duration-200 focus:outline-none;
  }
  
  .focus-smooth:focus {
    box-shadow: 0 0 0 3px rgba(32, 94, 166, 0.3); /* Using primary with opacity */
  }
}

/* === LOADING STATES === */
@layer components {
  /* Skeleton loader */
  .skeleton {
    background-color: var(--color-surface-alt);
    @apply rounded animate-pulse;
  }

  .skeleton-text {
    height: 1rem;
    background-color: var(--color-surface-alt);
    @apply rounded animate-pulse;
  }

  .skeleton-circle {
    background-color: var(--color-surface-alt);
    @apply rounded-full animate-pulse;
  }

  /* Modern spinner */
  .spinner {
    @apply inline-block w-5 h-5 border-2 rounded-full;
    border-color: var(--color-border);
    border-top-color: var(--color-primary);
    animation: spin 0.8s linear infinite;
  }
}

/* === TYPOGRAPHY ENHANCEMENTS === */
@layer base {
  /* Add Inter font */
  @font-face {
    font-family: 'Inter';
    font-style: normal;
    font-weight: 100 900;
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2');
  }

  /* Modern text styles */
  .text-display {
    @apply text-4xl md:text-5xl font-bold tracking-tight;
  }

  .text-headline {
    @apply text-2xl md:text-3xl font-semibold tracking-tight;
  }

  .text-title {
    @apply text-xl md:text-2xl font-semibold;
  }

  .text-body {
    @apply text-base leading-relaxed;
  }

  .text-caption {
    @apply text-sm;
    color: var(--color-text-muted);
  }
}

/* === 8. Mobile Floating Action Button (FAB) === */
@layer components {
  /* Container for FAB and sub-actions */
  .mobile-fab-container {
    @apply transition-all duration-300 ease-out;
  }

  .mobile-fab-container button {
    /* Apply haptic feedback on touch */
    @apply touch-manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  /* FAB shadow elevation */
  .fab-elevated {
    box-shadow: 
      0 3px 5px -1px rgba(0, 0, 0, 0.2),
      0 6px 10px 0 rgba(0, 0, 0, 0.14),
      0 1px 18px 0 rgba(0, 0, 0, 0.12);
  }

  .fab-elevated:hover {
    box-shadow: 
      0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
  }

  /* FAB rotation animation */
  @keyframes fab-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(45deg); }
  }

  /* Mobile navigation scroll indicator */
  .nav-scroll-indicator {
    @apply absolute top-0 bottom-0 w-6 pointer-events-none z-10;
    background: linear-gradient(to right, var(--color-bg), transparent);
  }

  .nav-scroll-indicator--right {
    @apply right-0;
    background: linear-gradient(to left, var(--color-bg), transparent);
  }
}