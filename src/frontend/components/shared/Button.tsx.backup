import React, { forwardRef } from 'react';
import { buttonVariants, type ButtonVariantProps } from '../../utils/component-variants';
import { cn } from '../../utils/component-variants';

interface ButtonProps extends ButtonVariantProps, Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'size'> {
  children: React.ReactNode;
  /**
   * Loading state - shows spinner and disables interaction
   */
  loading?: boolean;
  /**
   * Custom loading text (optional)
   */
  loadingText?: string;
  /**
   * Icon to display before text (optional)
   */
  icon?: React.ReactNode;
  /**
   * Icon to display after text (optional)
   */
  iconRight?: React.ReactNode;
  /**
   * Full width button
   */
  fullWidth?: boolean;
}

/**
 * A consistent Button component with built-in loading states, icons, and variants.
 * 
 * Features:
 * - Type-safe variant system
 * - Built-in loading states with spinner
 * - Icon support (left and right)
 * - Full accessibility support
 * - Dark mode support
 * - Consistent focus and hover states
 * 
 * @example
 * ```tsx
 * <Button variant="primary" size="lg" loading={isLoading}>
 *   Save Changes
 * </Button>
 * ```
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(({
  children,
  className,
  variant = 'primary',
  size = 'default',
  loading = false,
  loadingText,
  icon,
  iconRight,
  fullWidth = false,
  disabled,
  ...props
}, ref) => {
  const isLoading = loading;
  const isDisabled = disabled || isLoading;
  
  // Map old variants to modern design system classes
  const modernVariantMap = {
    primary: 'btn-modern btn-modern--primary',
    secondary: 'btn-modern btn-modern--secondary',
    outline: 'btn-modern btn-modern--secondary',
    ghost: 'btn-modern btn-modern--ghost',
    danger: 'btn-modern btn-modern--primary bg-error-600 hover:bg-error-700 focus-visible:ring-error-500',
    success: 'btn-modern btn-modern--primary bg-success-600 hover:bg-success-700 focus-visible:ring-success-500',
  };

  const modernSizeMap = {
    sm: 'btn-modern--sm',
    default: 'btn-modern--md',
    lg: 'btn-modern--lg',
  };

  const classes = cn(
    modernVariantMap[variant as keyof typeof modernVariantMap] || modernVariantMap.primary,
    modernSizeMap[size as keyof typeof modernSizeMap] || modernSizeMap.default,
    isLoading && 'btn-modern--loading',
    fullWidth && 'w-full',
    'haptic-medium touch-manipulation',
    className
  );

  return (
    <button
      ref={ref}
      className={classes}
      disabled={isDisabled}
      aria-disabled={isDisabled}
      {...props}
    >
      {/* Loading spinner */}
      {isLoading && (
        <span className="spinner mr-2" aria-hidden="true" />
      )}
      
      {/* Left icon */}
      {!isLoading && icon && (
        <span className="mr-2 -ml-1" aria-hidden="true">
          {icon}
        </span>
      )}
      
      {/* Button text */}
      <span>
        {isLoading && loadingText ? loadingText : children}
      </span>
      
      {/* Right icon */}
      {!isLoading && iconRight && (
        <span className="ml-2 -mr-1" aria-hidden="true">
          {iconRight}
        </span>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export { Button as default };