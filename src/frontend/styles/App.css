/* App.css - Main styling for the financial analysis application */

/* Flexoki Theme Variables - Imported from flexoki-theme.ts */
/* These are defined in modern-design-system.css and can be overridden here if needed */

/* Legacy compatibility mapping to new Flexoki variables */
:root {
  --primary-color: var(--color-primary);
  --secondary-color: var(--color-primary);
  --accent-color: var(--color-error);
  --text-color: var(--color-text);
  --background-color: var(--color-bg);
  --border-color: var(--color-border);
  --success-color: var(--color-success);
  --warning-color: var(--color-warning);
  --chart-actual: var(--color-primary);
  --chart-projected: var(--color-warning);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
}

.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.app-header h1 {
  color: var(--color-primary);
  font-size: 24px;
}

.header-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.sync-controls {
  display: flex;
  align-items: center;
}

.last-synced {
  font-size: 14px;
  color: #878580; /* Flexoki base-500 */
  margin-right: 15px;
}

button:not([class]),
.default-button {
  background-color: var(--color-primary);
  color: var(--color-bg);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

button:not([class]):hover,
.default-button:hover {
  background-color: var(--color-primary-hover);
}

button.syncing {
  background-color: var(--color-text-subtle);
  cursor: not-allowed;
}

/* Date range picker styles */
.date-control-container {
  margin-bottom: 30px;
}

.date-range-picker {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 5px;
  padding: 15px;
  box-shadow: var(--shadow-sm);
}

.date-inputs {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.input-group {
  flex: 1;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
}

.input-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 14px;
}

.quick-ranges {
  display: flex;
  gap: 10px;
}

.quick-ranges button {
  flex: 1;
  background-color: var(--color-surface);
  color: var(--color-text);
  font-size: 13px;
}

.quick-ranges button:hover {
  background-color: var(--color-surface-alt);
}

/* Summary container styles */
.summary-container {
  margin-bottom: 30px;
}

.cash-flow-summary {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 5px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.cash-flow-summary h2 {
  color: var(--color-primary);
  font-size: 18px;
  margin-bottom: 15px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.summary-item {
  text-align: center;
  padding: 15px;
  border-radius: 5px;
  background-color: var(--color-surface-alt);
}

.summary-item .label {
  font-size: 14px;
  color: var(--color-text-muted);
  margin-bottom: 8px;
}

.summary-item .value {
  font-size: 24px;
  font-weight: 600;
}

.value.positive {
  color: var(--color-success);
}

.value.negative {
  color: var(--color-error);
}

/* Chart container styles - transparent with no border */
.chart-container {
  margin-bottom: 30px;
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.cash-flow-chart h2 {
  color: var(--color-primary);
  font-size: 18px;
  margin-bottom: 15px;
}

/* Data grid styles */
.data-grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.work-in-progress,
.revenue-projections {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 5px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.work-in-progress h2,
.revenue-projections h2 {
  color: var(--color-primary);
  font-size: 18px;
  margin-bottom: 15px;
}

/* Legacy table styles - minimal defaults to avoid breaking existing tables */
table {
  border-collapse: collapse;
}

/* These styles are kept minimal to avoid overriding Tailwind utilities */
/* Tables that need specific styling should use Tailwind classes */

/* Metrics container styles */
.metrics-container {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 5px;
  padding: 20px;
  box-shadow: var(--shadow-sm);
}

.metrics-container h2 {
  color: var(--color-primary);
  font-size: 18px;
  margin-bottom: 15px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.metric-item {
  text-align: center;
  padding: 15px;
  border-radius: 5px;
  background-color: var(--color-surface-alt);
}

.metric-label {
  font-size: 14px;
  color: var(--color-text-muted);
  margin-bottom: 8px;
}

.metric-value {
  font-size: 22px;
  font-weight: 600;
}

/* Loading and error states */
.app-loading,
.app-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-indicator {
  background-color: var(--color-surface);
  padding: 20px 40px;
  border-radius: 5px;
  box-shadow: var(--shadow-lg);
  text-align: center;
  font-size: 16px;
  color: var(--color-primary);
}

.error-message {
  background-color: var(--color-error-bg);
  border: 1px solid var(--color-error);
  padding: 20px 40px;
  border-radius: 5px;
  box-shadow: var(--shadow-lg);
  text-align: center;
  max-width: 500px;
}

.error-message h2 {
  color: var(--color-error);
  margin-bottom: 10px;
}

.error-message button {
  margin-top: 15px;
  background-color: var(--color-error);
}

.error-message button:hover {
  background-color: var(--color-error-hover);
}

/* Budget Variance Styles - Merged from components/App.css */
.budget-variance-container {
  margin-top: 30px;
  padding: 20px;
  background-color: var(--color-surface);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

.budget-variance-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
}

.budget-variance-table th,
.budget-variance-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.budget-variance-table th {
  background-color: var(--color-surface-alt);
  font-weight: 500;
  color: var(--color-text);
}

.budget-variance-table tr:hover {
  background-color: var(--color-surface);
}

/* Legacy tables that don't use Tailwind - apply basic styles */
.legacy-table th,
.work-in-progress table th,
.revenue-projections table th {
  text-align: left;
  padding: 10px;
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  font-size: 14px;
}

.legacy-table td,
.work-in-progress table td,
.revenue-projections table td {
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
  font-size: 14px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.on-track {
  background-color: var(--color-success-bg);
  color: var(--color-success-text);
}

.status-badge.over {
  background-color: var(--color-error-bg);
  color: var(--color-error-text);
}

.status-badge.under {
  background-color: var(--color-info-bg);
  color: var(--color-info-text);
}

.root-causes {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
}

.root-causes li {
  margin-bottom: 4px;
}

.status-on-track {
  border-left: 4px solid var(--color-success);
}

.status-over {
  border-left: 4px solid var(--color-error);
}

.status-under {
  border-left: 4px solid var(--color-primary);
}

/* Section Styles - Merged from components/App.css */
section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--color-surface);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

section h2 {
  margin-top: 0;
  color: var(--color-text);
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .summary-grid,
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .data-grid-container {
    grid-template-columns: 1fr;
  }

  .date-inputs {
    flex-direction: column;
    gap: 15px;
  }

  .app-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .sync-controls {
    margin-top: 15px;
  }

  .app {
    padding: 10px;
  }

  .actual-projected-grid {
    grid-template-columns: 1fr;
  }

  .budget-variance-table {
    font-size: 14px;
  }

  .budget-variance-table th,
  .budget-variance-table td {
    padding: 8px;
  }

  .root-causes {
    font-size: 12px;
  }
}
