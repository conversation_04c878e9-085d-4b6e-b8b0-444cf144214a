/**
 * Setup file for Playwright E2E tests
 */

// Global configuration for tests
global.appConfig = {
  baseUrl: 'http://localhost:5173',
  serverUrl: 'http://localhost:3002',
  screenshotPath: './tests/e2e/screenshots',
  testTimeout: 30000,
};

// Global helper functions
global.utils = {
  // Sleep function for waiting
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
};

console.log('E2E test setup completed');