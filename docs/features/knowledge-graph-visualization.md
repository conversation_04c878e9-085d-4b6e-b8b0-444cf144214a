# Knowledge Graph Visualization

## Overview

The Knowledge Graph Visualization provides an interactive network view of business relationships and entities within the Onbord Financial Dashboard. Similar to <PERSON>bsidian's graph view, it allows users to visualize connections between companies, contacts, deals, projects, and estimates in a dynamic, force-directed graph.

## Features

### Interactive Network Visualization

- **Force-directed layout**: Entities are positioned based on their relationships using physics simulation
- **Real-time interactions**: Hover, click, drag, and zoom interactions
- **Entity types**: Companies, contacts, deals, projects, and estimates
- **Relationship types**: Various connection types including work relationships, deal associations, project dependencies

### Visual Design

- **Entity differentiation**: Different colors and icons for each entity type
  - Companies: Blue circles with building icon
  - Contacts: Green circles with person icon
  - Deals: Purple circles with currency icon
  - Projects: Orange circles with document icon
  - Estimates: Yellow circles with clipboard icon

- **Link styling**: Connections show relationship strength and type
- **Responsive design**: Adapts to different screen sizes
- **Smooth animations**: Fluid transitions and interactions

### Filtering and Search

- **Entity type filtering**: Show/hide specific types of entities
- **Search functionality**: Find specific entities by name
- **Relationship filtering**: Focus on specific types of connections
- **Node degree filtering**: Show only highly connected entities

### Performance Optimization

- **Node limiting**: Default maximum of 1000 nodes (configurable up to 2000)
- **Progressive loading**: Efficient data fetching and rendering
- **Memory management**: Proper cleanup of visualization resources

## Technical Implementation

### Architecture

The Knowledge Graph is implemented using:

- **Frontend**: React component using `react-force-graph-2d` library
- **Backend**: Specialized `KnowledgeGraphRepository` that aggregates relationship data
- **API**: RESTful endpoints for graph data retrieval

### Data Sources

The graph aggregates data from multiple repositories:

- `CompanyRepository` - Company entities and hierarchies
- `ContactRepository` - Individual contacts
- `DealRepository` - Business deals and opportunities
- `ProjectRepository` - Project data from Harvest integration
- `EstimateDraftsRepository` - Project estimates
- Various relationship repositories for connections

### Key Files

- **Frontend Component**: `src/frontend/components/Leads/KnowledgeGraph/KnowledgeGraph.tsx`
- **Repository**: `src/api/repositories/knowledge-graph-repository.ts`
- **API Routes**: `src/api/routes/knowledge-graph.ts`
- **Integration**: Added to Leads page as a new tab

### API Endpoints

#### Get Complete Knowledge Graph

```
GET /api/knowledge-graph
```

Query parameters:
- `includeDeleted`: Include deleted entities (default: false)
- `entityTypes`: Comma-separated list of entity types (default: company,contact,deal,project)
- `maxNodes`: Maximum number of nodes (default: 1000, max: 2000)

Response:
```json
{
  "success": true,
  "data": {
    "nodes": [...],
    "links": [...],
    "stats": {
      "totalNodes": 150,
      "totalLinks": 89,
      "nodeTypes": {"company": 25, "contact": 45, ...},
      "linkTypes": {"works_at": 30, "has_deal": 15, ...}
    }
  }
}
```

#### Get Entity Subgraph

```
GET /api/knowledge-graph/:entityId
```

Query parameters:
- `entityType`: Type of the entity (company, contact, deal, project, estimate)
- `depth`: Relationship depth to explore (default: 2)

## Usage

### Accessing the Knowledge Graph

1. Navigate to **Leads > Intelligence**
2. Select the **Knowledge Graph** tab
3. The graph will load automatically with default settings

### Interacting with the Graph

- **Hover**: Show entity details and connections
- **Click**: Select entity and show metadata
- **Drag**: Move entities to explore relationships
- **Scroll**: Zoom in/out of the graph
- **Double-click**: Center view on selected entity

### Filtering Data

1. Click the **Filters** button to open the filter panel
2. Select entity types to include/exclude
3. Use the search box to find specific entities
4. Adjust minimum node degree to focus on highly connected entities
5. Apply filters to update the visualization

### Performance Tips

- Use entity type filtering to reduce data volume for large datasets
- Increase node limit gradually if you need to see more connections
- Use subgraph view for focused exploration of specific entities

## Integration Points

### CRM System

The Knowledge Graph integrates with the CRM system to show:
- Company hierarchies and relationships
- Contact employment and role relationships
- Deal associations and ownership
- Project team assignments

### External Systems

Data is enriched from external integrations:
- **Harvest**: Project data and team assignments
- **HubSpot**: CRM relationships and deal information
- **Internal CRM**: Manual relationship definitions

## Future Enhancements

### Planned Features

- **Timeline view**: Show relationship evolution over time
- **Clustering**: Group related entities automatically
- **Export functionality**: Save graph views as images or data
- **Advanced filtering**: More sophisticated relationship queries
- **Collaborative features**: Share graph views with team members

### Technical Improvements

- **3D visualization**: Optional 3D graph view for complex datasets
- **Performance optimization**: Further improvements for larger datasets
- **Real-time updates**: Live graph updates when data changes
- **Custom layouts**: Alternative layout algorithms (hierarchical, circular, etc.)

## Configuration

### Environment Variables

No additional environment variables required. The Knowledge Graph uses existing database connections and repository patterns.

### Performance Settings

Default configuration in `KnowledgeGraph.tsx`:
```typescript
const DEFAULT_MAX_NODES = 1000;
const PERFORMANCE_MAX_NODES = 2000;
const DEFAULT_CACHE_TIME = 5 * 60 * 1000; // 5 minutes
```

## Troubleshooting

### Common Issues

1. **Graph not loading**: Check console for API errors
2. **Performance issues**: Reduce node limit or filter entity types
3. **Missing relationships**: Verify data integrity in source repositories
4. **Layout problems**: Restart the force simulation or refresh the page

### Debug Mode

Enable debug mode by setting `DEBUG_KNOWLEDGE_GRAPH=true` in local storage to see additional logging and performance metrics.

## Dependencies

- `react-force-graph-2d`: 3D force-directed graph visualization
- `@heroicons/react`: Icons for entity types
- `react-query`: Data fetching and caching
- Standard project dependencies (React, TypeScript, etc.)

## See Also

- [CRM Directory Enhancement](crm-directory-enhancements.md)
- [Opportunity Intelligence](opportunity-intelligence.md)
- [Technical Data Model Documentation](../technical/data-model/README.md)