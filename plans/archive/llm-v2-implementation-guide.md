# LLM Implementation Guide: V2 Structural Overhaul

## Context for LLM

You are implementing a major UX restructuring of the Upstream Financial Dashboard. The application has grown from a financial tool into a comprehensive business platform. This guide provides specific, actionable instructions for implementation.

## Implementation Rules

1. **All changes are frontend-only** - Do not modify backend APIs or database schema
2. **Use feature flags** - All new features must be behind flags for gradual rollout
3. **Preserve existing routes** - Add redirects from old to new URLs
4. **Reuse existing components** - Don't recreate functionality that already exists
5. **Follow existing patterns** - Match the codebase's TypeScript, React hooks, and Tailwind conventions

## Phase 1: Foundation (Priority: HIGH)

### Task 1.1: Create Feature Flag System

**Location**: `src/frontend/config/features.ts`

```typescript
export const features = {
  v2Navigation: process.env.REACT_APP_V2_NAVIGATION === 'true',
  v2Dashboard: process.env.REACT_APP_V2_DASHBOARD === 'true',
  v2OperationsHub: process.env.REACT_APP_V2_OPERATIONS_HUB === 'true',
  v2GrowthHub: process.env.REACT_APP_V2_GROWTH_HUB === 'true',
  v2IntelligenceHub: process.env.REACT_APP_V2_INTELLIGENCE_HUB === 'true',
};
```

### Task 1.2: Create V2 Navigation Component

**Location**: `src/frontend/components/Navigation/V2Navigation.tsx`

**Requirements**:
- Three main sections: Operations Hub, Growth Hub, Intelligence Hub
- Collapsible subsections
- Active state indicators
- Mobile responsive (bottom tabs on mobile, sidebar on desktop)
- Use existing icon components from current navigation

**Implementation Notes**:
- Copy structure from `UnifiedNavigation.tsx` but reorganize items
- Keep the same styling patterns (Tailwind classes)
- Add breadcrumb trail for deep navigation

### Task 1.3: Create Dashboard Component

**Location**: `src/frontend/components/Dashboard/`

**Required Files**:
- `V2Dashboard.tsx` - Main dashboard container
- `DashboardWidget.tsx` - Reusable widget component
- `DashboardLayout.tsx` - Grid layout manager
- `widgets/CashPositionWidget.tsx`
- `widgets/PipelineWidget.tsx`
- `widgets/UtilizationWidget.tsx`
- `widgets/ActionItemsWidget.tsx`

**Data Sources** (use existing):
- Cash position: `useXeroAccounts()` from `src/frontend/api/xero.ts`
- Pipeline: `useDeals()` from `src/frontend/api/crm.ts`
- Utilization: `useUtilizationReport()` from `src/frontend/api/reports.ts`

### Task 1.4: Update Routing

**Location**: `src/frontend/config/routes.tsx`

**Add these routes** (keep existing ones):
```typescript
// New V2 routes
{ path: '/', element: features.v2Dashboard ? <V2Dashboard /> : <Navigate to="/projection" /> },
{ path: '/operations/*', element: <OperationsHub /> },
{ path: '/growth/*', element: <GrowthHub /> },
{ path: '/intelligence/*', element: <IntelligenceHub /> },

// Redirects for backwards compatibility
{ path: '/projection', element: <Navigate to="/operations/cash-position" /> },
{ path: '/crm/deals', element: <Navigate to="/growth/pipeline" /> },
{ path: '/forecast', element: <Navigate to="/intelligence/smart-forecast" /> },
```

## Phase 2: Operations Hub

### Task 2.1: Create Operations Hub Container

**Location**: `src/frontend/components/OperationsHub/`

**Structure**:
```
OperationsHub/
├── index.tsx - Main hub container with sub-navigation
├── CashPosition/ - Unified cash view
│   ├── CashPositionPage.tsx
│   ├── CurrentBalanceCard.tsx (reuse from ForwardProjection)
│   └── ProjectionChart.tsx (reuse CashflowChart)
├── Payables/
│   ├── PayablesPage.tsx
│   ├── BillsCalendar.tsx (new)
│   └── ExpenseList.tsx (reuse from CustomExpensesTab)
└── Receivables/
    ├── ReceivablesPage.tsx
    ├── InvoiceAging.tsx (new)
    └── CollectionMetrics.tsx (new)
```

**Reuse these existing components**:
- `CashflowChart` from `ForwardProjection/CashflowChart.tsx`
- `TransactionsList` from `ForwardProjection/TransactionsList.tsx`
- `ExpenseList` from `Expense/ExpenseList.tsx`
- `CashflowSummaryCards` from `ForwardProjection/CashflowSummaryCards.tsx`

### Task 2.2: Enhance Cash Position View

**Combine these existing features**:
1. Current balance from Xero (`XeroAuthSection.tsx`)
2. Projection chart (`CashflowChart.tsx`)
3. Transaction timeline (`TransactionsList.tsx`)
4. Summary metrics (`CashflowSummaryCards.tsx`)

**New additions**:
- Quick filters for time periods (7, 30, 60, 90 days)
- Scenario toggle at page level (not just in chart)
- Export to CSV button

## Phase 3: Growth Hub

### Task 3.1: Create Growth Hub Container

**Location**: `src/frontend/components/GrowthHub/`

**Structure**:
```
GrowthHub/
├── index.tsx
├── Pipeline/
│   ├── PipelinePage.tsx (enhance existing DealBoard)
│   └── OpportunityCard.tsx (enhance DealCard with estimate indicator)
├── Clients/
│   ├── ClientsPage.tsx (unified view)
│   ├── ClientProfile.tsx (360° view)
│   └── ClientList.tsx (combine Companies + Contacts)
└── Estimates/
    └── (reuse existing Estimate components)
```

### Task 3.2: Create Unified Client Profile

**Location**: `src/frontend/components/GrowthHub/Clients/ClientProfile.tsx`

**Data to aggregate**:
- Company data from `useCompany()`
- Contacts from `useContacts({ companyId })`
- Deals from `useDeals({ companyId })`
- Projects from `useHarvestProjects({ clientId })`
- Notes from `useNotes({ companyId })`

**Layout**:
- Header with company info and quick stats
- Tabs: Overview, People, Opportunities, Projects, Financials, Notes
- Related actions in sidebar

### Task 3.3: Enhance Pipeline View

**Modifications to existing DealBoard**:
1. Add estimate indicator to DealCard
2. Add quick action buttons (Create Estimate, Update Stage)
3. Add pipeline value summary at top
4. Add filters for date range, owner, value

## Phase 4: Intelligence Hub

### Task 4.1: Create Intelligence Hub Container

**Location**: `src/frontend/components/IntelligenceHub/`

**Structure**:
```
IntelligenceHub/
├── index.tsx
├── Analytics/
│   ├── AnalyticsPage.tsx
│   ├── FinancialMetrics.tsx
│   └── OperationalMetrics.tsx
├── Forecasting/
│   ├── SmartForecastPage.tsx (move existing)
│   └── ScenarioPlanning.tsx (new)
└── Reports/
    └── (reuse existing Reports components)
```

### Task 4.2: Create Analytics Dashboard

**Combine these existing data sources**:
- Financial data from `useBalanceSheet()`
- Utilization from `useUtilizationReport()`
- Project budgets from `useProjectBudgets()`
- Pipeline analytics from deals data

**New visualizations**:
- Revenue trend chart (monthly)
- Client concentration pie chart
- Team utilization heatmap
- Project profitability matrix

## Phase 5: Polish & Migration

### Task 5.1: Add User Onboarding

**Location**: `src/frontend/components/Onboarding/`

**Components**:
- `OnboardingModal.tsx` - First-time user guide
- `FeatureTour.tsx` - Interactive tooltips
- `MigrationWizard.tsx` - Help users adapt to new structure

### Task 5.2: Update Help Documentation

**Location**: `src/frontend/components/HelpPage.tsx`

**Add sections for**:
- Three-pillar model explanation
- Navigation guide
- Feature mapping (old location → new location)
- Video tutorials (embed YouTube links)

## Testing Checklist

For each phase, verify:

1. **Feature flags work correctly**
   - New features hidden when flag is false
   - Gradual rollout possible

2. **Backwards compatibility**
   - All old URLs redirect properly
   - No broken bookmarks
   - API calls unchanged

3. **Mobile responsiveness**
   - Navigation works on all screen sizes
   - Touch interactions function
   - No horizontal scrolling

4. **Performance**
   - Dashboard loads in < 2 seconds
   - No new API calls added
   - Existing caching still works

5. **Accessibility**
   - Keyboard navigation works
   - Screen reader compatible
   - Color contrast passes WCAG AA

## Common Patterns to Follow

### Component Structure
```typescript
// Always use this pattern for new components
export const ComponentName: React.FC<Props> = ({ prop1, prop2 }) => {
  // Hooks first
  const { data, loading, error } = useData();
  
  // Early returns for loading/error
  if (loading) return <LoadingIndicator />;
  if (error) return <ErrorDisplay error={error} />;
  
  // Main render
  return (
    <div className="existing-tailwind-classes">
      {/* Content */}
    </div>
  );
};
```

### Data Fetching
```typescript
// Always use existing API hooks, don't create new endpoints
const data = useExistingHook({
  // Pass required parameters
});
```

### Styling
```typescript
// Use existing Tailwind classes from the codebase
className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6"

// Container queries for responsive design
className="@container"
className="@lg:grid-cols-2"
```

## Error Handling

When implementing, always:
1. Check if component/hook already exists before creating new
2. Use existing error boundaries and error components
3. Add proper TypeScript types (check existing types first)
4. Handle loading and error states consistently
5. Test with missing/invalid data

## Implementation Order

1. **Week 1-2**: Phase 1 (Foundation)
2. **Week 3-5**: Phase 2 (Operations Hub)
3. **Week 6-8**: Phase 3 (Growth Hub)
4. **Week 9-10**: Phase 4 (Intelligence Hub)
5. **Week 11-12**: Phase 5 (Polish & Migration)

## Success Criteria

After implementation:
- Users can navigate using three-pillar model
- Dashboard provides at-a-glance business health
- All existing features remain accessible
- No breaking changes to API or data model
- Performance is same or better than current