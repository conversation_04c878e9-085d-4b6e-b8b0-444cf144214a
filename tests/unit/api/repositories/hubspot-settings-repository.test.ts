/**
 * HubSpot Settings Repository Test
 * 
 * Tests for the HubSpot settings repository focusing on business behavior.
 */
import HubSpotSettingsRepository from '../../../../src/api/repositories/hubspot-settings-repository';

// Mock the database module before importing the repository
jest.mock('../../../../src/api/services/db-service', () => ({
  default: {
    prepare: jest.fn(),
    exec: jest.fn(),
    pragma: jest.fn(),
    transaction: jest.fn(),
    close: jest.fn(),
  }
}));

describe('HubSpotSettingsRepository - Business Behavior', () => {
  let repository: HubSpotSettingsRepository;
  let mockDb: any;

  beforeEach(() => {
    // Get the mocked db service
    mockDb = require('../../../../src/api/services/db-service').default;
    
    // Clear all mocks
    jest.clearAllMocks();
    
    repository = new HubSpotSettingsRepository();
  });

  describe('Business Rule: HubSpot Integration Configuration', () => {
    it('should provide default settings when none exist', () => {
      // Business requirement: System should work with default settings
      const mockStatement = {
        get: jest.fn().mockReturnValue(null)
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const settings = repository.getSettings();

      // Verify default settings are returned
      expect(settings).toMatchObject({
        sync_enabled: false,
        sync_interval: 3600, // Default 1 hour
        sync_entities: ['companies', 'contacts', 'deals'],
        field_mappings: {
          company: {},
          contact: {},
          deal: {}
        }
      });
    });

    it('should store and retrieve custom sync configuration', () => {
      // Business requirement: Admin can configure which entities to sync
      const customSettings = {
        sync_enabled: true,
        sync_interval: 7200, // 2 hours
        sync_entities: ['companies', 'contacts'], // Exclude deals
        field_mappings: {
          company: { name: 'company_name', website: 'domain' },
          contact: { email: 'email_address', firstName: 'first_name' }
        }
      };

      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.updateSettings(customSettings);

      // Verify settings are properly formatted for storage
      expect(mockDb.prepare).toHaveBeenCalledWith(expect.stringContaining('UPDATE hubspot_settings'));
      const updateCall = mockStatement.run.mock.calls[0];
      expect(updateCall[0]).toBe(1); // sync_enabled = 1
      expect(updateCall[1]).toBe(7200); // sync_interval
      expect(JSON.parse(updateCall[4])).toEqual(['companies', 'contacts']); // sync_entities
    });

    it('should handle field mapping configuration', () => {
      // Business requirement: Map HubSpot fields to internal fields
      const mappings = {
        company: {
          name: 'companyname',
          website: 'website',
          industry: 'industry'
        },
        contact: {
          email: 'email',
          firstName: 'firstname',
          lastName: 'lastname'
        },
        deal: {
          dealname: 'name',
          amount: 'value',
          closedate: 'expectedCloseDate'
        }
      };

      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.updateFieldMappings(mappings);

      // Verify mappings are stored as JSON
      const updateCall = mockStatement.run.mock.calls[0];
      expect(JSON.parse(updateCall[0])).toEqual(mappings);
    });
  });

  describe('Business Rule: Sync State Management', () => {
    it('should track last successful sync time', () => {
      // Business requirement: Know when data was last synchronized
      const syncTime = new Date().toISOString();
      
      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.updateLastSyncTime(syncTime);

      expect(mockDb.prepare).toHaveBeenCalledWith(expect.stringContaining('last_sync_at'));
      expect(mockStatement.run).toHaveBeenCalledWith(syncTime);
    });

    it('should enable and disable sync functionality', () => {
      // Business requirement: Admin can pause/resume sync
      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      // Enable sync
      repository.setSyncEnabled(true);
      expect(mockStatement.run).toHaveBeenCalledWith(1);

      // Disable sync
      repository.setSyncEnabled(false);
      expect(mockStatement.run).toHaveBeenCalledWith(0);
    });
  });

  describe('Business Rule: Security and API Configuration', () => {
    it('should handle API credentials securely', () => {
      // Business requirement: Store sensitive data securely
      const credentials = {
        api_key: 'pat-na1-abc123',
        portal_id: '12345678'
      };

      const mockStatement = {
        run: jest.fn().mockReturnValue({ changes: 1 })
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      repository.updateApiCredentials(credentials.api_key, credentials.portal_id);

      // Verify credentials are stored (encryption would happen at service layer)
      const updateCall = mockStatement.run.mock.calls[0];
      expect(updateCall[0]).toBe(credentials.api_key);
      expect(updateCall[1]).toBe(credentials.portal_id);
    });

    it('should validate portal ID format', () => {
      // Business requirement: Ensure valid HubSpot portal ID
      const invalidPortalId = 'invalid';
      
      expect(() => {
        repository.updateApiCredentials('valid-key', invalidPortalId);
      }).toThrow(/Invalid portal ID/);
    });
  });

  describe('Business Rule: Migration and Initialization', () => {
    it('should initialize settings table if not exists', () => {
      // Business requirement: Self-healing database initialization
      const mockStatement = {
        run: jest.fn()
      };
      mockDb.prepare.mockReturnValue(mockStatement);
      mockDb.exec.mockImplementation(() => {});

      repository.initializeSettingsTable();

      expect(mockDb.exec).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TABLE IF NOT EXISTS hubspot_settings')
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', () => {
      // Business expectation: Clear error messages for troubleshooting
      mockDb.prepare.mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      expect(() => repository.getSettings()).toThrow(/Database connection failed/);
    });

    it('should handle invalid JSON in stored settings', () => {
      // Business expectation: Recover from corrupted data
      const corruptedSettings = {
        id: 1,
        sync_enabled: 1,
        sync_interval: 3600,
        field_mappings: 'invalid-json',
        sync_entities: '["companies"]'
      };

      const mockStatement = {
        get: jest.fn().mockReturnValue(corruptedSettings)
      };
      mockDb.prepare.mockReturnValue(mockStatement);

      const settings = repository.getSettings();
      
      // Should return default mappings when JSON is invalid
      expect(settings.field_mappings).toEqual({
        company: {},
        contact: {},
        deal: {}
      });
    });
  });
});