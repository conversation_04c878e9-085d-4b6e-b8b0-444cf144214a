import React from 'react'; /** * Smart Forecast/Income section content for the help page */ export const IncomeSection: React.FC = () => { return ( <> <p> The Smart Forecast system automatically generates accurate income projections based on your project budgets, payment terms, and invoice frequency. </p> <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-4"> How It Works </h3> <div className="space-y-3 mt-2"> <div className="flex items-start space-x-3"> <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div> <p className="text-sm">Analyzes your Harvest project budgets and remaining work</p> </div> <div className="flex items-start space-x-3"> <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div> <p className="text-sm">Applies project-specific invoice frequency and payment terms</p> </div> <div className="flex items-start space-x-3"> <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div> <p className="text-sm">Generates projected invoices with realistic timing</p> </div> <div className="flex items-start space-x-3"> <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">4</div> <p className="text-sm">Filters out duplicate invoices that already exist in Harvest</p> </div> </div> <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-6"> Configuration </h3> <p className="text-sm mt-2"> Access the Projection Settings to configure invoice frequency, payment terms, and other project-specific settings that affect income projections. </p> </> ); };