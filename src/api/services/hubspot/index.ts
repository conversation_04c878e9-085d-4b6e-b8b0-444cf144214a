import { EventEmitter } from 'events';
import { HubSpotConfig } from './config';
import { HubSpotProgressTracker, ImportProgress, ImportResult } from './progress-tracker';
import { HubSpotUtils } from './utils';
import { HubSpotCompanies } from './companies';
import { HubSpotDeals } from './deals';
import { HubSpotContacts } from './contacts';
import { HubSpotNotes } from './notes';
import { HubSpotAssociations } from './associations';

/**
 * Main HubSpot service that coordinates all functionality
 */
export class HubSpotService extends EventEmitter {
  private config: HubSpotConfig;
  private progressTracker: HubSpotProgressTracker;
  private companies: HubSpotCompanies;
  private deals: HubSpotDeals;
  private contacts: HubSpotContacts;
  private notes: HubSpotNotes;
  private associations: HubSpotAssociations;

  constructor() {
    super();
    this.config = new HubSpotConfig();
    this.progressTracker = new HubSpotProgressTracker();
    this.companies = new HubSpotCompanies(this.progressTracker);
    this.deals = new HubSpotDeals(this.progressTracker);
    this.contacts = new HubSpotContacts(this.progressTracker);
    this.notes = new HubSpotNotes(this.progressTracker);
    this.associations = new HubSpotAssociations(this.progressTracker);

    // Forward progress events
    this.progressTracker.on('progress', (progress: ImportProgress) => {
      this.emit('progress', progress);
    });

    // Ensure imports table exists
    HubSpotUtils.ensureImportsTableExists();
  }

  /**
   * Set the Socket.IO instance for real-time progress updates
   */
  setSocketIO(io: any) {
    this.progressTracker.setSocketIO(io);
  }

  /**
   * Get the HubSpot access token from the database
   */
  getAccessToken(): string | null {
    return this.config.getAccessToken();
  }

  /**
   * Save the HubSpot access token to the database
   */
  saveAccessToken(token: string): boolean {
    return this.config.saveAccessToken(token);
  }

  /**
   * Check if HubSpot is properly configured
   */
  isConfigured(): boolean {
    return this.config.isConfigured();
  }

  /**
   * Check if HubSpot client is initialized
   */
  isInitialized(): boolean {
    return this.config.getClient() !== null;
  }

  /**
   * Initialize the HubSpot client with an access token
   */
  initialize(accessToken: string): boolean {
    return this.config.saveAccessToken(accessToken);
  }

  /**
   * Clear the HubSpot configuration
   */
  clearConfiguration(): boolean {
    return this.config.clearConfiguration();
  }

  /**
   * Delete the access token (alias for clearConfiguration)
   */
  deleteAccessToken(): boolean {
    return this.clearConfiguration();
  }

  /**
   * Import companies from HubSpot with progress tracking
   */
  async importCompanies(): Promise<ImportResult> {
    const client = this.config.getClient();
    if (!client) {
      return { 
        success: false, 
        count: 0, 
        errors: [], 
        updates: [], 
        created: [], 
        error: 'HubSpot client not initialized' 
      };
    }

    return this.companies.importCompanies(client);
  }

  /**
   * Import deals from HubSpot with progress tracking
   */
  async importDeals(): Promise<ImportResult> {
    const client = this.config.getClient();
    if (!client) {
      return { 
        success: false, 
        count: 0, 
        errors: [], 
        updates: [], 
        created: [], 
        error: 'HubSpot client not initialized' 
      };
    }

    return this.deals.importDeals(client);
  }

  /**
   * Import contacts from HubSpot with progress tracking
   */
  async importContacts(): Promise<ImportResult> {
    const client = this.config.getClient();
    if (!client) {
      return { 
        success: false, 
        count: 0, 
        errors: [], 
        updates: [], 
        created: [], 
        error: 'HubSpot client not initialized' 
      };
    }

    return this.contacts.importContacts(client);
  }

  /**
   * Import notes and activities from HubSpot
   */
  async importNotesAndActivities(): Promise<ImportResult> {
    const client = this.config.getClient();
    if (!client) {
      return { 
        success: false, 
        count: 0, 
        errors: [], 
        updates: [], 
        created: [], 
        error: 'HubSpot client not initialized' 
      };
    }

    return this.notes.importNotesAndActivities(client);
  }

  /**
   * Import associations (contact-company, deal-contact) from HubSpot
   */
  async importAssociations(): Promise<ImportResult> {
    const client = this.config.getClient();
    if (!client) {
      return { 
        success: false, 
        count: 0, 
        errors: [], 
        updates: [], 
        created: [], 
        error: 'HubSpot client not initialized' 
      };
    }

    return this.associations.importAssociations(client);
  }

  /**
   * Get companies from HubSpot for linking
   */
  async getCompaniesForLinking(): Promise<Array<{ 
    id: string; 
    name: string; 
    industry?: string; 
    website?: string 
  }>> {
    const client = this.config.getClient();
    if (!client) {
      throw new Error('HubSpot client not initialized');
    }

    return this.companies.getCompaniesForLinking(client);
  }

  /**
   * Import only core entities (companies, deals, contacts) from HubSpot with progress tracking
   * Skips notes and associations for faster sync
   */
  async importQuickWithProgress(): Promise<{
    success: boolean;
    companies: ImportResult;
    deals: ImportResult;
    contacts: ImportResult;
    error?: string;
  }> {
    const client = this.config.getClient();
    if (!client) {
      const error = 'HubSpot client not initialized';
      return {
        success: false,
        companies: { success: false, count: 0, errors: [], updates: [], created: [], error },
        deals: { success: false, count: 0, errors: [], updates: [], created: [], error },
        contacts: { success: false, count: 0, errors: [], updates: [], created: [], error },
        error
      };
    }

    const startTime = Date.now();

    try {
      // Import companies first (required for deals)
      console.log('Starting HubSpot companies import...');
      const companiesResult = await this.companies.importCompanies(client);

      // Import deals (requires companies to exist)
      console.log('Starting HubSpot deals import...');
      const dealsResult = await this.deals.importDeals(client);

      // Import contacts
      console.log('Starting HubSpot contacts import...');
      const contactsResult = await this.contacts.importContacts(client);

      const duration = Date.now() - startTime;

      // Record quick import with individual counts stored as metadata
      const allErrors = [
        ...companiesResult.errors,
        ...dealsResult.errors,
        ...contactsResult.errors
      ];

      const totalCount = companiesResult.count + dealsResult.count + contactsResult.count;

      // Consider import successful if we imported at least some records
      const hasImportedRecords = totalCount > 0;
      const success = hasImportedRecords || allErrors.length === 0;

      HubSpotUtils.recordImport({
        type: 'quick_import',
        success: success,
        count: totalCount,
        errors: allErrors,
        createdCount: (companiesResult.created?.length || 0) + (dealsResult.created?.length || 0) + 
          (contactsResult.created?.length || 0),
        updatedCount: (companiesResult.updates?.length || 0) + (dealsResult.updates?.length || 0) + 
          (contactsResult.updates?.length || 0),
        duration,
        // Store individual counts in metadata
        metadata: {
          companiesCount: companiesResult.count,
          dealsCount: dealsResult.count,
          contactsCount: contactsResult.count,
          importType: 'quick'
        }
      });

      console.log(`Quick import completed in ${duration}ms - ${totalCount} records imported`);

      return {
        success,
        companies: companiesResult,
        deals: dealsResult,
        contacts: contactsResult
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error during import';
      console.error('Error during HubSpot quick import:', error);

      return {
        success: false,
        companies: { success: false, count: 0, errors: [], updates: [], created: [], error: errorMessage },
        deals: { success: false, count: 0, errors: [], updates: [], created: [], error: errorMessage },
        contacts: { success: false, count: 0, errors: [], updates: [], created: [], error: errorMessage },
        error: errorMessage
      };
    }
  }

  /**
   * Import all data from HubSpot with progress tracking
   */
  async importAllWithProgress(): Promise<{
    success: boolean;
    companies: ImportResult;
    deals: ImportResult;
    contacts: ImportResult;
    notes?: ImportResult;
    associations?: ImportResult;
    error?: string;
  }> {
    const client = this.config.getClient();
    if (!client) {
      const error = 'HubSpot client not initialized';
      return {
        success: false,
        companies: { success: false, count: 0, errors: [], updates: [], created: [], error },
        deals: { success: false, count: 0, errors: [], updates: [], created: [], error },
        contacts: { success: false, count: 0, errors: [], updates: [], created: [], error },
        error
      };
    }

    const startTime = Date.now();

    try {
      // Import companies first (required for deals)
      console.log('Starting HubSpot companies import...');
      const companiesResult = await this.companies.importCompanies(client);

      // Import deals (requires companies to exist)
      console.log('Starting HubSpot deals import...');
      const dealsResult = await this.deals.importDeals(client);

      // Import contacts
      console.log('Starting HubSpot contacts import...');
      const contactsResult = await this.contacts.importContacts(client);

      // Import notes and activities (optional - only if entities were imported)
      let notesResult: ImportResult | undefined;
      if (companiesResult.count > 0 || dealsResult.count > 0 || contactsResult.count > 0) {
        console.log('Starting HubSpot notes and activities import...');
        notesResult = await this.notes.importNotesAndActivities(client);
      }

      // Import associations (optional - only if entities were imported)
      let associationsResult: ImportResult | undefined;
      if (contactsResult.count > 0 || dealsResult.count > 0) {
        console.log('Starting HubSpot associations import...');
        associationsResult = await this.associations.importAssociations(client);
      }

      const duration = Date.now() - startTime;

      // Record overall import with individual counts stored as metadata
      const allErrors = [
        ...companiesResult.errors,
        ...dealsResult.errors,
        ...contactsResult.errors,
        ...(notesResult?.errors || []),
        ...(associationsResult?.errors || [])
      ];

      const totalCount = companiesResult.count + dealsResult.count + contactsResult.count +
        (notesResult?.count || 0) + (associationsResult?.count || 0);

      // Consider import successful if we imported at least some records
      // Only mark as failed if nothing was imported or there was a critical error
      const hasImportedRecords = totalCount > 0;
      const hasPartialSuccess = hasImportedRecords && allErrors.length > 0;
      const success = hasImportedRecords || allErrors.length === 0;

      HubSpotUtils.recordImport({
        type: 'full_import', // Changed to indicate this is a full import
        success: success,
        count: totalCount,
        errors: allErrors,
        createdCount: (companiesResult.created?.length || 0) + (dealsResult.created?.length || 0) + 
          (contactsResult.created?.length || 0) + (notesResult?.created?.length || 0) + 
          (associationsResult?.created?.length || 0),
        updatedCount: (companiesResult.updates?.length || 0) + (dealsResult.updates?.length || 0) + 
          (contactsResult.updates?.length || 0),
        duration,
        // Store individual counts in metadata
        metadata: {
          companiesCount: companiesResult.count,
          dealsCount: dealsResult.count,
          contactsCount: contactsResult.count,
          notesCount: notesResult?.count || 0,
          associationsCount: associationsResult?.count || 0,
          hasPartialSuccess // Add flag to indicate partial success
        }
      });

      return {
        success,
        companies: companiesResult,
        deals: dealsResult,
        contacts: contactsResult,
        notes: notesResult,
        associations: associationsResult
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error during import';
      console.error('Error during HubSpot import:', error);

      return {
        success: false,
        companies: { success: false, count: 0, errors: [], updates: [], created: [], error: errorMessage },
        deals: { success: false, count: 0, errors: [], updates: [], created: [], error: errorMessage },
        contacts: { success: false, count: 0, errors: [], updates: [], created: [], error: errorMessage },
        error: errorMessage
      };
    }
  }

  /**
   * Get current import progress
   */
  getCurrentProgress(): ImportProgress | null {
    return this.progressTracker.getCurrentProgress();
  }

  /**
   * Reset progress tracker
   */
  resetProgress(): void {
    this.progressTracker.reset();
  }

  /**
   * Get import history
   */
  getImportHistory(): Array<{
    id: string;
    type: string;
    success: boolean;
    count: number;
    errors: Array<{ item: string; error: string }>;
    createdCount: number;
    updatedCount: number;
    duration: number;
    importedAt: string;
    metadata?: { companiesCount?: number; dealsCount?: number; contactsCount?: number } | null;
  }> {
    return HubSpotUtils.getImportHistory();
  }
}

// Create and export a singleton instance
export const hubspotService = new HubSpotService();

// Export types for external use
export { ImportProgress, ImportResult } from './progress-tracker';