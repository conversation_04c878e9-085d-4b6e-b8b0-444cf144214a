# Xero MCP Chat Demo - Claude Integration

## Prerequisites

1. **Set up Anthropic API Key**
   ```bash
   # Add to your .env file:
   ANTHROPIC_API_KEY=your_api_key_here
   ```
   Get your key from: https://console.anthropic.com/

2. **Start the application**
   ```bash
   npm run dev
   ```

3. **Open the application**
   - Navigate to http://localhost:5173
   - Authenticate with Xero if prompted

4. **Open the chat**
   - Click the blue chat button (bottom right corner)
   - Wait for "Connected to Xero" status

## Natural Conversations with <PERSON>

### Example 1: Basic Inquiry
```
You: Hi! Can you tell me about my business's current financial position?

<PERSON>: I'll help you understand your current financial position. Let me gather some key information for you.

[<PERSON> uses get_organisation_info and get_balance_sheet tools]

Based on your latest data, here's your financial position for [Your Company]:

**Cash Position**: $45,230 across 3 bank accounts
**Total Assets**: $125,450
**Total Liabilities**: $38,200
**Net Assets (Equity)**: $87,250

Your business appears to be in a healthy financial position with a strong equity base. Would you like me to show you a breakdown of your current assets or review your recent cash flow?
```

### Example 2: Specific Query
```
You: What invoices do I have that are overdue?

<PERSON>: Let me check your overdue invoices for you.

[Claude uses list_invoices tool with appropriate filters]

You have 2 overdue invoices totaling $8,750:

1. **INV-0089** - Tech Solutions Ltd
   - Amount: $5,500
   - Due: March 10, 2024 (15 days overdue)
   - Contact: John Smith (<EMAIL>)

2. **INV-0092** - Global Imports Co
   - Amount: $3,250  
   - Due: March 18, 2024 (7 days overdue)
   - Contact: Sarah Johnson (<EMAIL>)

Would you like me to show you the payment history for either of these customers, or would you prefer to see all unpaid invoices regardless of due date?
```

### Example 3: Financial Analysis
```
You: How did we do last month compared to the month before?

Claude: I'll analyze your performance for the last two months.

[Claude uses get_profit_loss tool with appropriate date ranges]

Here's your month-over-month comparison:

**February 2024**
- Revenue: $48,500
- Expenses: $32,100
- Net Profit: $16,400

**March 2024**
- Revenue: $52,300 (+7.8%)
- Expenses: $34,500 (+7.5%)
- Net Profit: $17,800 (+8.5%)

Great news! Your revenue grew by $3,800 (7.8%) while maintaining similar expense ratios. Your net profit increased by $1,400.

The main revenue drivers were:
- Consulting services: +$2,500
- Product sales: +$1,300

Would you like me to break down the expense categories to see where the additional spending occurred?
```

### Example 4: Complex Query
```
You: I need to understand our cash flow situation. Can you show me what money is coming in and what bills we need to pay?

Claude: I'll give you a comprehensive view of your cash flow situation.

[Claude uses multiple tools: list_invoices, list_bills, list_bank_transactions]

Here's your current cash flow overview:

**Current Bank Balance**: $23,450

**Money Coming In (Receivables)**:
- Paid invoices (last 30 days): $35,200
- Unpaid invoices due soon:
  - Within 7 days: $12,300 (3 invoices)
  - Within 30 days: $28,900 (7 invoices)
  
**Money Going Out (Payables)**:
- Bills due within 7 days: $8,500
  - Rent: $4,500
  - Utilities: $1,200
  - Suppliers: $2,800
  
- Bills due within 30 days: $15,300
  - Various suppliers and services

**Net Position**: 
- Next 7 days: +$3,800 (assuming collections)
- Next 30 days: +$13,600 (assuming all collections)

Your short-term cash flow looks healthy. Would you like me to show you which specific invoices are due for collection, or review any of the upcoming bills in detail?
```

## Claude's Capabilities

### What Claude Can Do
- **Understand context**: Remembers previous questions in the conversation
- **Analyze data**: Provides insights, not just raw data
- **Make suggestions**: Offers relevant follow-up actions
- **Format nicely**: Presents data in easy-to-read formats
- **Handle vague requests**: Interprets what you likely want to know

### Example Natural Queries
- "How's my business doing?"
- "Show me what I owe"
- "Who owes me money?"
- "What were my biggest expenses last month?"
- "Can you summarize my financial position?"
- "What's my profit margin?"
- "Show me all transactions from last week"

## Tips for Best Results

1. **Be conversational** - Talk to Claude like a financial assistant
2. **Ask follow-ups** - Claude remembers context
3. **Request comparisons** - "Compare this month to last month"
4. **Ask for insights** - "What do you notice about these numbers?"
5. **Request specific formats** - "Can you show that as a table?"

## Troubleshooting

### No response or errors
1. Check that `ANTHROPIC_API_KEY` is set in `.env`
2. Verify the API key is valid
3. Check browser console for specific errors
4. Ensure you're authenticated with Xero

### Slow responses
- First request may be slower as session initializes
- Complex queries with multiple tool calls take longer
- Check your internet connection

### Claude doesn't understand
- Try rephrasing your question
- Be more specific about what you want
- Ask for available options

## Understanding the Integration

### What's Happening Behind the Scenes
1. Your message goes to Claude (Anthropic's AI)
2. Claude understands your intent
3. Claude decides which Xero tools to use
4. The system fetches data from Xero
5. Claude formats a helpful response
6. You see a natural, conversational answer

### Why This Matters
- **No commands to memorize** - Just ask naturally
- **Intelligent responses** - Not just data dumps
- **Context awareness** - Follows conversation flow
- **Same as Claude Desktop** - Familiar experience if you use Claude Desktop with MCP

## Advanced Examples

### Multi-step Analysis
```
You: I'm preparing for a meeting with my accountant. Can you help me gather the key numbers?

Claude: I'll help you prepare a comprehensive financial summary for your accountant meeting.

[Uses multiple tools to gather data]

Here's your financial summary for the accountant meeting:

**Income Statement (Year-to-Date)**
- Total Revenue: $285,400
- Total Expenses: $198,200
- Net Income: $87,200
- Profit Margin: 30.6%

**Balance Sheet Highlights**
- Total Assets: $125,450
- Total Liabilities: $38,200
- Owner's Equity: $87,250
- Current Ratio: 2.3:1 (good liquidity)

**Accounts Receivable**
- Current: $28,500
- Overdue: $8,750
- Collection period: 28 days average

**Accounts Payable**
- Current: $15,300
- Overdue: $0 (all bills current)

**Cash Flow**
- Operating Cash Flow: $92,000
- Current Bank Balance: $23,450

**Key Observations**:
1. Strong profit margin at 30.6%
2. Healthy current ratio indicating good liquidity
3. Some attention needed on overdue receivables
4. All payables are current

Would you like me to prepare any specific reports or dive deeper into any of these areas?
```

This demonstrates Claude's ability to:
- Understand complex requests
- Gather comprehensive data
- Present it in a structured way
- Provide insights and analysis
- Offer relevant follow-ups
