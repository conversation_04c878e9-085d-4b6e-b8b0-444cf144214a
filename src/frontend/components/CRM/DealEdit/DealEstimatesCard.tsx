import React from "react"; import { DealEstimate } from "../../../types/crm-types"; import { Card } from "../../shared/Card"; import DealEstimatesSection from "./DealEstimatesSection"; import { DocumentTextIcon } from "@heroicons/react/24/outline"; interface DealEstimatesCardProps { dealId: string; estimates: DealEstimate[]; dealName?: string; dealCompanyName?: string; } /** * Card wrapper for the DealEstimatesSection component */ const DealEstimatesCard: React.FC<DealEstimatesCardProps> = ({ dealId, estimates, dealName, dealCompanyName, }) => { return ( <Card className="overflow-hidden !border-gray-200 dark:!border-gray-700"> <div className="px-4 py-5 sm:px-6 border-b !border-gray-200 dark:!border-gray-700 flex items-center"> <DocumentTextIcon className="h-6 w-6 text-green-500 mr-2" aria-hidden="true" /> <div> <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white"> Estimates </h3> <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400"> Estimates linked to this deal. </p> </div> </div> <div className="px-4 py-5 sm:p-6"> <DealEstimatesSection dealId={dealId} estimates={estimates} dealName={dealName} dealCompanyName={dealCompanyName} /> </div> </Card> ); }; export default DealEstimatesCard; 