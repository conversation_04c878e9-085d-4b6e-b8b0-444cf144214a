import { jest } from '@jest/globals';
import LateInvoiceService from '../../../src/services/harvest/late-invoice-service';
import { getInvoices } from '../../../src/api/integrations/harvest';

// Mock dependencies
jest.mock('../../../src/api/integrations/harvest');
jest.mock('../../../src/utils/backend-logger');

describe('LateInvoiceService', () => {
  let service: LateInvoiceService;

  beforeEach(() => {
    service = new LateInvoiceService();
    jest.clearAllMocks();
  });

  describe('getLateInvoices', () => {
    it('should identify and categorize late invoices', async () => {
      const mockInvoices = [
        {
          id: 1,
          number: 'INV-001',
          client: { name: 'Client A' },
          amount: 10000,
          due_amount: 10000,
          due_date: '2023-12-01',
          state: 'open',
          sent_at: '2023-11-01',
          payment_term: 'net 30',
        },
        {
          id: 2,
          number: 'INV-002',
          client: { name: 'Client B' },
          amount: 5000,
          due_amount: 2500,
          due_date: '2023-12-15',
          state: 'partial',
          sent_at: '2023-11-15',
          payment_term: 'net 30',
        },
        {
          id: 3,
          number: 'INV-003',
          client: { name: 'Client C' },
          amount: 15000,
          due_amount: 0,
          due_date: '2023-12-20',
          state: 'paid',
          paid_at: '2023-12-18',
          payment_term: 'net 30',
        },
      ];

      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      // Mock current date for consistent testing
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.getLateInvoices();

      expect(getInvoices).toHaveBeenCalledWith({
        state: 'open,partial',
        updated_since: expect.any(String),
      });

      expect(result).toEqual({
        summary: {
          totalLateInvoices: 2,
          totalAmountOutstanding: 12500,
          averageDaysOverdue: 24,
          oldestInvoiceDays: 31,
        },
        invoices: [
          {
            id: 1,
            number: 'INV-001',
            clientName: 'Client A',
            amount: 10000,
            dueAmount: 10000,
            dueDate: '2023-12-01',
            daysOverdue: 31,
            paymentTerm: 'net 30',
            state: 'open',
          },
          {
            id: 2,
            number: 'INV-002',
            clientName: 'Client B',
            amount: 5000,
            dueAmount: 2500,
            dueDate: '2023-12-15',
            daysOverdue: 17,
            paymentTerm: 'net 30',
            state: 'partial',
          },
        ],
        byClient: {
          'Client A': {
            count: 1,
            totalAmount: 10000,
            oldestDaysOverdue: 31,
          },
          'Client B': {
            count: 1,
            totalAmount: 2500,
            oldestDaysOverdue: 17,
          },
        },
      });

      jest.useRealTimers();
    });

    it('should handle empty invoice list', async () => {
      (getInvoices as jest.Mock).mockResolvedValue([]);

      const result = await service.getLateInvoices();

      expect(result).toEqual({
        summary: {
          totalLateInvoices: 0,
          totalAmountOutstanding: 0,
          averageDaysOverdue: 0,
          oldestInvoiceDays: 0,
        },
        invoices: [],
        byClient: {},
      });
    });

    it('should filter out future-dated invoices', async () => {
      const mockInvoices = [
        {
          id: 1,
          number: 'INV-001',
          client: { name: 'Client A' },
          amount: 10000,
          due_amount: 10000,
          due_date: '2024-02-01',
          state: 'open',
          payment_term: 'net 30',
        },
      ];

      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.getLateInvoices();

      expect(result.invoices).toHaveLength(0);
      expect(result.summary.totalLateInvoices).toBe(0);

      jest.useRealTimers();
    });
  });

  describe('getLateInvoicesByAgeRange', () => {
    it('should categorize invoices by age ranges', async () => {
      const mockInvoices = [
        {
          id: 1,
          number: 'INV-001',
          client: { name: 'Client A' },
          amount: 10000,
          due_amount: 10000,
          due_date: '2023-12-20',
          state: 'open',
        },
        {
          id: 2,
          number: 'INV-002',
          client: { name: 'Client B' },
          amount: 5000,
          due_amount: 5000,
          due_date: '2023-11-20',
          state: 'open',
        },
        {
          id: 3,
          number: 'INV-003',
          client: { name: 'Client C' },
          amount: 8000,
          due_amount: 8000,
          due_date: '2023-10-01',
          state: 'open',
        },
      ];

      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.getLateInvoicesByAgeRange();

      expect(result).toEqual({
        '0-30': {
          count: 1,
          totalAmount: 10000,
          invoices: expect.arrayContaining([
            expect.objectContaining({ number: 'INV-001' }),
          ]),
        },
        '31-60': {
          count: 1,
          totalAmount: 5000,
          invoices: expect.arrayContaining([
            expect.objectContaining({ number: 'INV-002' }),
          ]),
        },
        '61-90': {
          count: 1,
          totalAmount: 8000,
          invoices: expect.arrayContaining([
            expect.objectContaining({ number: 'INV-003' }),
          ]),
        },
        '90+': {
          count: 0,
          totalAmount: 0,
          invoices: [],
        },
      });

      jest.useRealTimers();
    });
  });

  describe('getClientPaymentHistory', () => {
    it('should analyze client payment patterns', async () => {
      const mockInvoices = [
        {
          id: 1,
          client: { id: 100, name: 'Client A' },
          amount: 10000,
          due_date: '2023-11-01',
          paid_at: '2023-11-05',
          state: 'paid',
        },
        {
          id: 2,
          client: { id: 100, name: 'Client A' },
          amount: 15000,
          due_date: '2023-12-01',
          paid_at: '2023-12-10',
          state: 'paid',
        },
        {
          id: 3,
          client: { id: 100, name: 'Client A' },
          amount: 20000,
          due_amount: 20000,
          due_date: '2023-12-15',
          state: 'open',
        },
      ];

      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.getClientPaymentHistory(100);

      expect(getInvoices).toHaveBeenCalledWith({
        client_id: 100,
        updated_since: expect.any(String),
      });

      expect(result).toEqual({
        clientId: 100,
        clientName: 'Client A',
        totalInvoices: 3,
        paidInvoices: 2,
        lateInvoices: 1,
        averagePaymentDays: 6.5,
        totalRevenue: 45000,
        outstandingAmount: 20000,
        paymentPerformance: 'fair',
        invoices: expect.arrayContaining([
          expect.objectContaining({
            daysToPayment: 4,
            wasLate: true,
          }),
          expect.objectContaining({
            daysToPayment: 9,
            wasLate: true,
          }),
          expect.objectContaining({
            daysOverdue: 17,
            wasLate: true,
          }),
        ]),
      });

      jest.useRealTimers();
    });

    it('should handle clients with no invoices', async () => {
      (getInvoices as jest.Mock).mockResolvedValue([]);

      const result = await service.getClientPaymentHistory(999);

      expect(result).toEqual({
        clientId: 999,
        clientName: 'Unknown',
        totalInvoices: 0,
        paidInvoices: 0,
        lateInvoices: 0,
        averagePaymentDays: 0,
        totalRevenue: 0,
        outstandingAmount: 0,
        paymentPerformance: 'unknown',
        invoices: [],
      });
    });
  });

  describe('predictCollectionDates', () => {
    it('should predict collection dates based on payment history', async () => {
      const mockHistoricalInvoices = [
        {
          id: 1,
          client: { id: 100, name: 'Client A' },
          due_date: '2023-10-01',
          paid_at: '2023-10-08',
          state: 'paid',
        },
        {
          id: 2,
          client: { id: 100, name: 'Client A' },
          due_date: '2023-11-01',
          paid_at: '2023-11-10',
          state: 'paid',
        },
      ];

      const mockOpenInvoices = [
        {
          id: 3,
          number: 'INV-003',
          client: { id: 100, name: 'Client A' },
          amount: 10000,
          due_amount: 10000,
          due_date: '2023-12-15',
          state: 'open',
        },
      ];

      (getInvoices as jest.Mock)
        .mockResolvedValueOnce(mockHistoricalInvoices)
        .mockResolvedValueOnce(mockOpenInvoices);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.predictCollectionDates();

      expect(result).toEqual([
        {
          invoiceId: 3,
          invoiceNumber: 'INV-003',
          clientName: 'Client A',
          amount: 10000,
          dueDate: '2023-12-15',
          predictedCollectionDate: '2023-12-23',
          confidenceLevel: 'high',
          basedOnAverageDays: 8,
        },
      ]);

      jest.useRealTimers();
    });
  });

  describe('generateCollectionPriorities', () => {
    it('should prioritize invoices for collection', async () => {
      const mockInvoices = [
        {
          id: 1,
          number: 'INV-001',
          client: { name: 'Client A' },
          amount: 50000,
          due_amount: 50000,
          due_date: '2023-10-01',
          state: 'open',
        },
        {
          id: 2,
          number: 'INV-002',
          client: { name: 'Client B' },
          amount: 10000,
          due_amount: 10000,
          due_date: '2023-11-15',
          state: 'open',
        },
        {
          id: 3,
          number: 'INV-003',
          client: { name: 'Client C' },
          amount: 25000,
          due_amount: 25000,
          due_date: '2023-12-01',
          state: 'open',
        },
      ];

      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.generateCollectionPriorities();

      expect(result).toEqual([
        {
          priority: 1,
          invoice: expect.objectContaining({
            number: 'INV-001',
            daysOverdue: 92,
          }),
          score: expect.any(Number),
          reasons: expect.arrayContaining([
            'High value invoice',
            'Very overdue (90+ days)',
          ]),
        },
        {
          priority: 2,
          invoice: expect.objectContaining({
            number: 'INV-003',
            daysOverdue: 31,
          }),
          score: expect.any(Number),
          reasons: expect.arrayContaining([
            'Significant amount',
            'Overdue 30+ days',
          ]),
        },
        {
          priority: 3,
          invoice: expect.objectContaining({
            number: 'INV-002',
            daysOverdue: 47,
          }),
          score: expect.any(Number),
          reasons: expect.arrayContaining([
            'Overdue 30+ days',
          ]),
        },
      ]);

      jest.useRealTimers();
    });
  });

  describe('error handling', () => {
    it('should handle API errors gracefully', async () => {
      (getInvoices as jest.Mock).mockRejectedValue(new Error('API Error'));

      await expect(service.getLateInvoices()).rejects.toThrow('API Error');
    });

    it('should handle malformed invoice data', async () => {
      const mockInvoices = [
        {
          id: 1,
          // Missing required fields
          client: null,
          amount: 'invalid',
        },
      ];

      (getInvoices as jest.Mock).mockResolvedValue(mockInvoices);

      const result = await service.getLateInvoices();

      expect(result.invoices).toHaveLength(0);
      expect(result.summary.totalLateInvoices).toBe(0);
    });
  });
});