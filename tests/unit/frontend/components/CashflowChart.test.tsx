/**
 * CashflowChart Component Test
 * 
 * This test verifies that the CashflowChart component renders correctly
 * using explicit test data (no mocks).
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import CashflowChart from '@/frontend/components/ForwardProjection/CashflowChart';
import { DailyCashflow } from '@/types/financial';

// Mock Recharts as it's not possible to fully render it in JSDOM
jest.mock('recharts', () => {
  const MockResponsiveContainer = ({ children }: { children: React.ReactNode }) => <div data-testid="chart-container">{children}</div>;
  const MockLineChart = ({ children }: { children: React.ReactNode }) => <div data-testid="line-chart">{children}</div>;
  const MockCartesianGrid = () => <div data-testid="cartesian-grid" />;
  const MockXAxis = () => <div data-testid="x-axis" />;
  const MockYAxis = () => <div data-testid="y-axis" />;
  const MockTooltip = () => <div data-testid="tooltip" />; 
  const MockLegend = () => <div data-testid="legend" />;
  const MockLine = () => <div data-testid="line" />;
  const MockReferenceLine = () => <div data-testid="reference-line" />;
  const MockArea = () => <div data-testid="area" />;

  return {
    ResponsiveContainer: MockResponsiveContainer,
    LineChart: MockLineChart,
    CartesianGrid: MockCartesianGrid,
    XAxis: MockXAxis,
    YAxis: MockYAxis,
    Tooltip: MockTooltip,
    Legend: MockLegend,
    Line: MockLine,
    ReferenceLine: MockReferenceLine,
    Area: MockArea,
    ComposedChart: ({ children }: { children: React.ReactNode }) => <div data-testid="composed-chart">{children}</div>,
  };
});

// Instead of mocking the useProjection hook, we'll explicitly create test data
describe('CashflowChart Component', () => {
  // Create explicit test data - no mocks
  const testDailyCashflow: DailyCashflow[] = [
    {
      date: new Date('2025-04-01'),
      balance: 1000,
      inflows: 0,
      outflows: 0,
      netFlow: 0,
      transactions: []
    },
    {
      date: new Date('2025-04-02'),
      balance: 1200,
      inflows: 200,
      outflows: 0,
      netFlow: 200,
      transactions: [{
        id: 'test-tx-1',
        date: new Date('2025-04-02'),
        amount: 200,
        description: 'Test Invoice',
        type: 'invoice',
        source: 'harvest'
      }]
    },
    {
      date: new Date('2025-04-03'),
      balance: 900,
      inflows: 0,
      outflows: 300,
      netFlow: -300,
      transactions: [{
        id: 'test-tx-2',
        date: new Date('2025-04-03'),
        amount: -300,
        description: 'Test Expense',
        type: 'expense',
        source: 'xero'
      }]
    }
  ];
  
  const testThreshold = 500;
  const mockSetHighlightedDate = jest.fn();

  // Test rendering the component with explicit data
  it('renders chart with dailyCashflow data', () => {
    // Use utility from your application to format data if needed
    // For now, we'll assume the component takes dailyCashflow directly
    render(
      <CashflowChart 
        dailyCashflow={testDailyCashflow} 
        threshold={testThreshold}
        onDateSelect={mockSetHighlightedDate}
      />
    );
    
    // Verify chart elements are rendered
    expect(screen.getByTestId('chart-container')).toBeInTheDocument();
    expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    expect(screen.getByTestId('x-axis')).toBeInTheDocument();
    expect(screen.getByTestId('y-axis')).toBeInTheDocument();
    expect(screen.getByTestId('reference-line')).toBeInTheDocument();
  });

  it('handles empty data gracefully', () => {
    render(
      <CashflowChart 
        dailyCashflow={[]} 
        threshold={testThreshold}
        onDateSelect={mockSetHighlightedDate}
      />
    );
    
    // Verify it still renders the chart container
    expect(screen.getByTestId('chart-container')).toBeInTheDocument();
  });
  
  it('handles null data gracefully', () => {
    render(
      <CashflowChart 
        dailyCashflow={null} 
        threshold={testThreshold}
        onDateSelect={mockSetHighlightedDate}
      />
    );
    
    // Verify it renders nothing or an appropriate message
    expect(screen.queryByTestId('chart-container')).not.toBeInTheDocument();
  });
});
