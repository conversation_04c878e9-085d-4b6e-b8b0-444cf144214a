# Deprecated Deployment Scripts

**Date**: June 2025  
**Status**: DEPRECATED - DO NOT USE

## Replaced Scripts

### `build.js.deprecated` (formerly `build.js`)
- **Purpose**: Complex custom build script with fallbacks
- **Lines**: 158 lines of custom build logic
- **Replaced By**: Standard `npm run build` using TypeScript + Vite

### `deploy.sh.deprecated` (formerly `deploy.sh`)  
- **Purpose**: Manual deployment to pre-built branches
- **Lines**: 102 lines of deployment orchestration
- **Replaced By**: Render auto-deploy on git push

## Migration Summary

The deployment strategy was simplified in June 2025 to use standard Render.com auto-deploy workflow.

### Old System (Deprecated)
```bash
# Complex manual deployment
npm run deploy:preview
npm run deploy:production

# Used these scripts:
- scripts/build.js (158 lines)
- scripts/deploy.sh (102 lines)
- Pre-built branches: pre-built-preview, pre-built-production
```

### New System (Current)
```bash
# Simple git workflow
git push origin preview     # Deploy to preview
git push origin main        # Deploy to production

# Uses standard build commands:
- npm ci --legacy-peer-deps --include=dev && npm run build
- npm start
```

### Benefits of New System
- ⚡ **Faster**: 2-3 minutes vs 5+ minutes
- 🛠️ **Standard**: Native Render features
- 🔧 **Simpler**: No custom scripts to maintain
- 👥 **Onboarding**: Standard git workflow

## Backup

The complete old system is preserved in the `backup-complex-deployment` branch for reference.

## Removal

These deprecated scripts can be safely removed after confirming the new deployment system works correctly for several deployments.