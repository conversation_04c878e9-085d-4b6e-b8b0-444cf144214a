#!/usr/bin/env node

/**
 * <PERSON>ript to verify that all columns referenced in repository files exist in the unified schema
 */

const fs = require('fs');
const path = require('path');

// Read the unified schema
const schemaPath = path.join(__dirname, '..', 'migrations', '000_unified_schema.sql');
const schemaContent = fs.readFileSync(schemaPath, 'utf8');

// Parse table definitions from schema
function parseSchema(content) {
  const tables = {};
  const tableRegex = /CREATE TABLE IF NOT EXISTS (\w+) \(/g;
  let match;
  
  while ((match = tableRegex.exec(content)) !== null) {
    const tableName = match[1];
    const startIndex = match.index + match[0].length;
    
    // Find the closing parenthesis for this CREATE TABLE
    let depth = 1;
    let endIndex = startIndex;
    while (depth > 0 && endIndex < content.length) {
      if (content[endIndex] === '(') depth++;
      if (content[endIndex] === ')') depth--;
      endIndex++;
    }
    
    const tableContent = content.substring(startIndex, endIndex - 1);
    const columns = [];
    
    // Parse column definitions
    const lines = tableContent.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('--') || trimmed.startsWith('FOREIGN KEY') || 
          trimmed.startsWith('CHECK') || trimmed.startsWith('UNIQUE')) continue;
      
      // Extract column name
      const columnMatch = trimmed.match(/^(\w+)\s+/);
      if (columnMatch) {
        columns.push(columnMatch[1]);
      }
    }
    
    tables[tableName] = columns;
  }
  
  return tables;
}

// Find all repository files
function findRepositoryFiles(dir) {
  const files = [];
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      files.push(...findRepositoryFiles(fullPath));
    } else if (stat.isFile() && item.endsWith('-repository.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Extract SQL queries from repository files
function extractQueries(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const queries = [];
  
  // Match SQL queries in template literals
  const queryRegex = /(?:prepare|db\.prepare)\s*\(\s*`([^`]+)`/g;
  let match;
  
  while ((match = queryRegex.exec(content)) !== null) {
    queries.push(match[1]);
  }
  
  // Also match SQL in regular strings
  const stringQueryRegex = /(?:prepare|db\.prepare)\s*\(\s*['"]([^'"]+)['"]/g;
  while ((match = stringQueryRegex.exec(content)) !== null) {
    queries.push(match[1]);
  }
  
  return queries;
}

// Extract column references from queries
function extractColumnReferences(query) {
  const references = [];
  
  // Remove comments
  query = query.replace(/--[^\n]*/g, '');
  
  // Common patterns for column references
  const patterns = [
    /INSERT INTO \w+ \(([^)]+)\)/gi,
    /SELECT\s+(.+?)\s+FROM/gi,
    /SET\s+(.+?)\s+WHERE/gi,
    /UPDATE\s+\w+\s+SET\s+(.+?)\s+WHERE/gi,
    /WHERE\s+(\w+)\s*=/gi,
    /ORDER BY\s+(\w+)/gi,
    /GROUP BY\s+(\w+)/gi,
  ];
  
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(query)) !== null) {
      const columnStr = match[1];
      // Split by comma and extract column names
      const parts = columnStr.split(',');
      for (const part of parts) {
        const cleaned = part.trim();
        // Extract column name (handle aliases, table prefixes, etc.)
        const columnMatch = cleaned.match(/(?:^|\s)(\w+)(?:\s+as\s+\w+)?(?:\s*$|,)/i);
        if (columnMatch) {
          references.push(columnMatch[1]);
        }
      }
    }
  }
  
  return references;
}

// Main verification
console.log('Parsing unified schema...');
const schemaTables = parseSchema(schemaContent);
console.log(`Found ${Object.keys(schemaTables).length} tables in schema`);

console.log('\nAnalyzing repository files...');
const repoDir = path.join(__dirname, '..', 'src', 'api', 'repositories');
const repoFiles = findRepositoryFiles(repoDir);
console.log(`Found ${repoFiles.length} repository files`);

const issues = [];

for (const file of repoFiles) {
  const fileName = path.basename(file);
  const queries = extractQueries(file);
  
  // Try to determine which table this repository is for
  let tableName = null;
  const tableMatch = fileName.match(/^(.+?)-repository\.ts$/);
  if (tableMatch) {
    const name = tableMatch[1].replace(/-/g, '_');
    // Handle special cases
    if (name === 'expenses') tableName = 'expense';
    else if (name === 'companies') tableName = 'company';
    else if (name === 'contacts') tableName = 'contact';
    else if (name === 'deals') tableName = 'deal';
    else if (name === 'contact_role') tableName = 'contact_role';
    else if (name === 'deal_estimate') tableName = 'deal_estimate';
    else tableName = name;
  }
  
  if (!tableName || !schemaTables[tableName]) {
    continue;
  }
  
  const schemaColumns = schemaTables[tableName];
  const referencedColumns = new Set();
  
  for (const query of queries) {
    const refs = extractColumnReferences(query);
    refs.forEach(ref => referencedColumns.add(ref));
  }
  
  // Check for missing columns
  for (const col of referencedColumns) {
    if (!schemaColumns.includes(col) && !['id', '*', '1', 'datetime', 'now'].includes(col)) {
      issues.push({
        file: fileName,
        table: tableName,
        column: col,
        type: 'missing'
      });
    }
  }
}

// Report results
if (issues.length === 0) {
  console.log('\n✅ All column references in repository files match the schema!');
} else {
  console.log('\n❌ Found column reference issues:');
  const byTable = {};
  for (const issue of issues) {
    if (!byTable[issue.table]) byTable[issue.table] = [];
    byTable[issue.table].push(issue);
  }
  
  for (const [table, tableIssues] of Object.entries(byTable)) {
    console.log(`\nTable: ${table}`);
    for (const issue of tableIssues) {
      console.log(`  - Column '${issue.column}' referenced in ${issue.file}`);
    }
  }
}