/**
 * Backend-specific logger that integrates with existing error middleware
 * and repository patterns
 */

import { Logger, LoggerConfig, LogContext, ApiContext, BusinessContext } from './logger';
import { Request, Response } from 'express';

/**
 * Extended context for backend operations
 */
export interface BackendContext extends LogContext {
  method?: string;
  url?: string;
  userAgent?: string;
  ip?: string;
  statusCode?: number;
  responseTime?: number;
}

/**
 * Database operation context
 */
export interface DatabaseContext extends LogContext {
  table?: string;
  query?: string;
  params?: any;
  duration?: number;
  rowsAffected?: number;
}

/**
 * Integration context for external APIs
 */
export interface IntegrationContext extends ApiContext {
  service: 'xero' | 'hubspot' | 'harvest';
  rateLimitRemaining?: number;
  retryAttempt?: number;
  circuitBreakerState?: 'open' | 'closed' | 'half-open';
}

/**
 * Backend-specific logger class
 */
export class BackendLogger extends Logger {
  constructor(config?: Partial<LoggerConfig>) {
    const backendConfig: Partial<LoggerConfig> = {
      environment: process.env.NODE_ENV as any || 'development',
      enableStructuredLogging: process.env.NODE_ENV === 'production',
      enableConsole: process.env.NODE_ENV !== 'production',
      enablePerformanceLogging: true,
      ...config
    };

    super(backendConfig);
  }

  /**
   * Extract context from Express request
   */
  private extractRequestContext(req: Request): BackendContext {
    return {
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      component: 'http_server',
      requestId: req.headers['x-request-id'] as string || undefined
    };
  }

  /**
   * Log HTTP requests
   */
  logRequest(req: Request, res: Response, responseTime?: number): void {
    const context: BackendContext = {
      ...this.extractRequestContext(req),
      statusCode: res.statusCode,
      responseTime
    };

    const message = `${req.method} ${req.originalUrl || req.url} - ${res.statusCode}`;

    if (res.statusCode >= 500) {
      this.error(message, undefined, context);
    } else if (res.statusCode >= 400) {
      this.warn(message, context);
    } else {
      this.info(message, context);
    }
  }

  /**
   * Log database operations
   */
  logDatabaseOperation(
    operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'TRANSACTION',
    context: DatabaseContext
  ): void {
    const message = `Database ${operation}: ${context.table || 'multiple tables'}`;
    
    const enrichedContext = {
      ...context,
      operation: 'database',
      component: 'repository'
    };

    if (context.duration && context.duration > 1000) { // Slow query threshold: 1 second
      this.warn(`Slow ${message} (${context.duration}ms)`, enrichedContext);
    } else {
      this.debug(message, enrichedContext);
    }
  }

  /**
   * Log external API integrations
   */
  logIntegration(operation: string, context: IntegrationContext): void {
    const message = `${context.service.toUpperCase()} API: ${operation}`;
    
    const enrichedContext = {
      ...context,
      operation: 'external_api',
      component: 'integration'
    };

    // Log based on status and context
    if (context.statusCode && context.statusCode >= 400) {
      this.error(message, undefined, enrichedContext);
    } else if (context.rateLimitRemaining && context.rateLimitRemaining < 10) {
      this.warn(`${message} - Rate limit warning (${context.rateLimitRemaining} remaining)`, enrichedContext);
    } else if (context.retryAttempt && context.retryAttempt > 0) {
      this.warn(`${message} - Retry attempt ${context.retryAttempt}`, enrichedContext);
    } else {
      this.info(message, enrichedContext);
    }
  }

  /**
   * Log repository operations
   */
  logRepositoryOperation(
    repository: string,
    method: string,
    entityId?: string,
    duration?: number,
    error?: Error
  ): void {
    const message = `${repository}.${method}${entityId ? `(${entityId})` : ''}`;
    
    const context: LogContext = {
      component: 'repository',
      operation: method,
      metadata: {
        repository,
        entityId,
        duration
      }
    };

    if (error) {
      this.error(`Repository error: ${message}`, error, context);
    } else if (duration && duration > 500) { // Slow operation threshold: 500ms
      this.warn(`Slow repository operation: ${message} (${duration}ms)`, context);
    } else {
      this.debug(`Repository operation: ${message}`, context);
    }
  }

  /**
   * Log authentication events
   */
  logAuthEvent(
    event: 'login' | 'logout' | 'token_refresh' | 'auth_failure' | 'session_expired',
    userId?: string,
    additionalContext?: LogContext
  ): void {
    const message = `Authentication event: ${event}`;
    
    const context: LogContext = {
      ...additionalContext,
      userId,
      component: 'authentication',
      operation: event
    };

    if (event === 'auth_failure') {
      this.warn(message, context);
    } else {
      this.info(message, context);
    }
  }

  /**
   * Log business events with enhanced context
   */
  logBusinessEvent(
    event: string,
    entityType: string,
    entityId: string,
    userId?: string,
    changes?: { field: string; oldValue: any; newValue: any }[]
  ): void {
    const message = `Business event: ${event} on ${entityType}`;
    
    const context: BusinessContext = {
      entityType,
      entityId,
      userId,
      component: 'business_logic',
      operation: event,
      metadata: {
        changes: changes?.map(change => ({
          field: change.field,
          oldValue: this.sanitizeValue(change.oldValue),
          newValue: this.sanitizeValue(change.newValue)
        }))
      }
    };

    this.info(message, context);
  }

  /**
   * Log system events
   */
  logSystemEvent(
    event: 'startup' | 'shutdown' | 'config_change' | 'migration' | 'cache_clear',
    details?: string,
    additionalContext?: LogContext
  ): void {
    const message = `System event: ${event}${details ? ` - ${details}` : ''}`;
    
    const context: LogContext = {
      ...additionalContext,
      component: 'system',
      operation: event
    };

    this.info(message, context);
  }

  /**
   * Sanitize sensitive values
   */
  private sanitizeValue(value: any): any {
    if (typeof value === 'string' && value.length > 100) {
      return `${value.substring(0, 100)}... (truncated)`;
    }
    return value;
  }
}

/**
 * Create and configure backend logger instance
 */
export function createBackendLogger(config?: Partial<LoggerConfig>): BackendLogger {
  return new BackendLogger(config);
}

/**
 * Default backend logger instance
 */
export const backendLogger = new BackendLogger();

/**
 * Express middleware for request logging
 */
export function requestLoggingMiddleware(logger: BackendLogger = backendLogger) {
  return (req: Request, res: Response, next: Function) => {
    const startTime = Date.now();

    // Log request start
    logger.debug(`Incoming request: ${req.method} ${req.originalUrl || req.url}`, {
      component: 'http_server',
      operation: 'request_start',
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    // Override res.end to log response
    const originalEnd = res.end;
    res.end = function(...args: any[]) {
      const responseTime = Date.now() - startTime;
      logger.logRequest(req, res, responseTime);
      
      // Call original end method
      originalEnd.apply(this, args);
    };

    next();
  };
}

/**
 * Utility function to create a logger with request context
 */
export function createRequestLogger(req: Request, baseLogger: BackendLogger = backendLogger): BackendLogger {
  const logger = new BackendLogger();
  
  // Override methods to include request context
  const originalError = logger.error.bind(logger);
  const originalWarn = logger.warn.bind(logger);
  const originalInfo = logger.info.bind(logger);
  const originalDebug = logger.debug.bind(logger);

  const requestContext = {
    requestId: req.headers['x-request-id'] as string,
    method: req.method,
    url: req.originalUrl || req.url,
    ip: req.ip
  };

  logger.error = (message: string, error?: Error, context?: LogContext) => {
    originalError(message, error, { ...requestContext, ...context });
  };

  logger.warn = (message: string, context?: LogContext) => {
    originalWarn(message, { ...requestContext, ...context });
  };

  logger.info = (message: string, context?: LogContext) => {
    originalInfo(message, { ...requestContext, ...context });
  };

  logger.debug = (message: string, context?: LogContext) => {
    originalDebug(message, { ...requestContext, ...context });
  };

  return logger;
}

export default backendLogger;