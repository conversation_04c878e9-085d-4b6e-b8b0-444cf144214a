import React from 'react'; import { FilterSummary } from '../types/projection-audit-types'; interface SummaryStatsProps { summary: FilterSummary; } const SummaryStats: React.FC<SummaryStatsProps> = ({ summary }) => { const inclusionRate = Math.round((summary.keptInvoices / (summary.totalInvoices || 1)) * 100); return ( <div className="bg-white dark:bg-gray-800 px-4 py-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-4"> <div className="flex flex-wrap items-center justify-between"> <div className="flex items-center mb-2 md:mb-0"> <svg className="w-4 h-4 text-blue-logo dark:text-blue-400 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" /> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" /> </svg> <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300"> Projection Summary </h3> </div> <div className="flex flex-wrap gap-x-3 gap-y-2"> <div className="flex items-center"> <div className="w-2 h-2 bg-gray-500 dark:bg-gray-400 rounded-full mr-1.5"></div> <span className="text-xs text-gray-500 dark:text-gray-400 mr-1">Total:</span> <span className="text-sm font-semibold text-gray-900 dark:text-white"> {summary.totalInvoices} </span> </div> <div className="flex items-center"> <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-1.5"></div> <span className="text-xs text-green-600 dark:text-green-400 mr-1">Included:</span> <span className="text-sm font-semibold text-green-600 dark:text-green-400"> {summary.keptInvoices} </span> </div> <div className="flex items-center"> <div className="w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full mr-1.5"></div> <span className="text-xs text-red-600 dark:text-red-400 mr-1">Excluded:</span> <span className="text-sm font-semibold text-red-600 dark:text-red-400"> {summary.excludedInvoices} </span> </div> <div className="flex items-center"> <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mr-1.5"></div> <span className="text-xs text-blue-600 dark:text-blue-400 mr-1">Rate:</span> <span className="text-sm font-semibold text-blue-600 dark:text-blue-400"> {inclusionRate}% </span> </div> </div> </div> </div> ); }; export default SummaryStats;