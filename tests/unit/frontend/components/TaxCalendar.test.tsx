import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { TaxCalendar } from '../../../../src/frontend/components/TaxCalendar';
import { ThemeProvider } from '../../../../src/frontend/components/TaxCalendar/ThemeContext';

describe('TaxCalendar', () => {
  const renderWithTheme = (component: React.ReactElement) => {
    return render(
      <ThemeProvider>
        {component}
      </ThemeProvider>
    );
  };

  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-06-15'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should render calendar grid', () => {
    renderWithTheme(<TaxCalendar />);

    expect(screen.getByText('June 2024')).toBeInTheDocument();
    expect(screen.getByText('Sun')).toBeInTheDocument();
    expect(screen.getByText('Mon')).toBeInTheDocument();
    expect(screen.getByText('Tue')).toBeInTheDocument();
  });

  it('should highlight current date', () => {
    renderWithTheme(<TaxCalendar />);

    const currentDate = screen.getByText('15');
    expect(currentDate.closest('.calendar-day')).toHaveClass('today');
  });

  it('should navigate to previous month', () => {
    renderWithTheme(<TaxCalendar />);

    const prevButton = screen.getByLabelText('Previous month');
    fireEvent.click(prevButton);

    expect(screen.getByText('May 2024')).toBeInTheDocument();
  });

  it('should navigate to next month', () => {
    renderWithTheme(<TaxCalendar />);

    const nextButton = screen.getByLabelText('Next month');
    fireEvent.click(nextButton);

    expect(screen.getByText('July 2024')).toBeInTheDocument();
  });

  it('should show tax deadlines', () => {
    renderWithTheme(<TaxCalendar />);

    // BAS deadline (28th of month after quarter)
    const basDeadline = screen.getByText('28');
    expect(basDeadline.closest('.calendar-day')).toHaveClass('bas-deadline');
  });

  it('should show PAYG deadlines', () => {
    renderWithTheme(<TaxCalendar />);

    // PAYG deadline (21st of each month)
    const paygDeadline = screen.getByText('21');
    expect(paygDeadline.closest('.calendar-day')).toHaveClass('payg-deadline');
  });

  it('should show super guarantee deadlines', () => {
    renderWithTheme(<TaxCalendar />);

    // Navigate to a quarter end month
    const nextButton = screen.getByLabelText('Next month');
    fireEvent.click(nextButton); // July

    // Super deadline (28th of month after quarter)
    const superDeadline = screen.getByText('28');
    expect(superDeadline.closest('.calendar-day')).toHaveClass('super-deadline');
  });

  it('should display tooltip on hover', () => {
    renderWithTheme(<TaxCalendar />);

    const deadline = screen.getByText('21');
    fireEvent.mouseEnter(deadline);

    expect(screen.getByRole('tooltip')).toBeInTheDocument();
    expect(screen.getByText(/PAYG Withholding/)).toBeInTheDocument();
  });

  it('should toggle dark mode', () => {
    renderWithTheme(<TaxCalendar />);

    const darkModeToggle = screen.getByLabelText('Toggle dark mode');
    fireEvent.click(darkModeToggle);

    expect(document.documentElement).toHaveClass('dark');
  });

  it('should show legend', () => {
    renderWithTheme(<TaxCalendar />);

    expect(screen.getByText('BAS Deadline')).toBeInTheDocument();
    expect(screen.getByText('PAYG Deadline')).toBeInTheDocument();
    expect(screen.getByText('Super Deadline')).toBeInTheDocument();
    expect(screen.getByText('Income Tax')).toBeInTheDocument();
  });

  it('should mark weekends differently', () => {
    renderWithTheme(<TaxCalendar />);

    const saturday = screen.getByText('1'); // June 1, 2024 is Saturday
    const sunday = screen.getByText('2'); // June 2, 2024 is Sunday

    expect(saturday.closest('.calendar-day')).toHaveClass('weekend');
    expect(sunday.closest('.calendar-day')).toHaveClass('weekend');
  });

  it('should show multiple deadlines on same day', () => {
    renderWithTheme(<TaxCalendar />);

    // Navigate to end of financial year
    const prevButton = screen.getByLabelText('Previous month');
    for (let i = 0; i < 6; i++) {
      fireEvent.click(prevButton);
    }

    expect(screen.getByText('December 2023')).toBeInTheDocument();
  });

  it('should highlight upcoming deadlines', () => {
    renderWithTheme(<TaxCalendar />);

    const upcomingDeadline = screen.getByText('21');
    expect(upcomingDeadline.closest('.calendar-day')).toHaveClass('upcoming');
  });

  it('should show overdue deadlines', () => {
    renderWithTheme(<TaxCalendar />);

    // Navigate to previous month to see overdue deadlines
    const prevButton = screen.getByLabelText('Previous month');
    fireEvent.click(prevButton);

    const overdueDeadline = screen.getByText('21');
    expect(overdueDeadline.closest('.calendar-day')).toHaveClass('overdue');
  });

  it('should return to current month', () => {
    renderWithTheme(<TaxCalendar />);

    // Navigate away
    const nextButton = screen.getByLabelText('Next month');
    fireEvent.click(nextButton);
    fireEvent.click(nextButton);

    expect(screen.getByText('August 2024')).toBeInTheDocument();

    // Click today button
    const todayButton = screen.getByText('Today');
    fireEvent.click(todayButton);

    expect(screen.getByText('June 2024')).toBeInTheDocument();
  });

  it('should handle year boundaries', () => {
    renderWithTheme(<TaxCalendar />);

    // Navigate to December
    const nextButton = screen.getByLabelText('Next month');
    for (let i = 0; i < 6; i++) {
      fireEvent.click(nextButton);
    }

    expect(screen.getByText('December 2024')).toBeInTheDocument();

    // Navigate to next year
    fireEvent.click(nextButton);
    expect(screen.getByText('January 2025')).toBeInTheDocument();
  });

  it('should be keyboard navigable', () => {
    renderWithTheme(<TaxCalendar />);

    const prevButton = screen.getByLabelText('Previous month');
    prevButton.focus();

    fireEvent.keyDown(prevButton, { key: 'Enter' });
    expect(screen.getByText('May 2024')).toBeInTheDocument();

    fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
    expect(document.activeElement).toBe(screen.getByLabelText('Next month'));
  });

  it('should show financial year indicator', () => {
    renderWithTheme(<TaxCalendar />);

    expect(screen.getByText('FY 2023-24')).toBeInTheDocument();

    // Navigate to July (new financial year)
    const nextButton = screen.getByLabelText('Next month');
    fireEvent.click(nextButton);

    expect(screen.getByText('FY 2024-25')).toBeInTheDocument();
  });
});