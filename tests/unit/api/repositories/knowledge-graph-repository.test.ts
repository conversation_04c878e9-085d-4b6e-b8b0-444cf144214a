/**
 * Knowledge Graph Repository Test
 * 
 * Tests for the knowledge graph repository focusing on aggregating relationships
 * across all entities (companies, contacts, deals, projects, estimates).
 * These tests use an in-memory SQLite database for isolation.
 */
import { KnowledgeGraphRepository } from '@/api/repositories/knowledge-graph-repository';
import { CompanyRepository } from '@/api/repositories/company-repository';
import { ContactRepository } from '@/api/repositories/contact-repository';
import { DealRepository } from '@/api/repositories/deal-repository';
import { ProjectRepository } from '@/api/repositories/project-repository';
import { EstimateDraftsRepository } from '@/api/repositories/estimate-drafts-repository';
import { ContactCompanyRepository } from '@/api/repositories/contact-company-repository';
import { ContactRoleRepository } from '@/api/repositories/relationships/contact-role-repository';
import { DealEstimateRepository } from '@/api/repositories/relationships/deal-estimate-repository';
import { Database } from '@/database';
import { runMigrations } from '@/api/services/db-init';
import { v4 as uuidv4 } from 'uuid';

describe('KnowledgeGraphRepository', () => {
  let repository: KnowledgeGraphRepository;
  let db: Database;
  let companyRepo: CompanyRepository;
  let contactRepo: ContactRepository;
  let dealRepo: DealRepository;
  let projectRepo: ProjectRepository;
  let estimateRepo: EstimateDraftsRepository;
  let contactCompanyRepo: ContactCompanyRepository;
  let contactRoleRepo: ContactRoleRepository;
  let dealEstimateRepo: DealEstimateRepository;
  
  // Test data IDs
  let testCompanyId: string;
  let testContactId: string;
  let testDealId: string;
  let testProjectId: string;
  let testEstimateId: string;
  
  beforeAll(async () => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Run migrations to set up schema
    await runMigrations(db);
  });
  
  beforeEach(async () => {
    // Initialize repositories
    repository = new KnowledgeGraphRepository();
    companyRepo = new CompanyRepository();
    contactRepo = new ContactRepository();
    dealRepo = new DealRepository();
    projectRepo = new ProjectRepository();
    estimateRepo = new EstimateDraftsRepository();
    contactCompanyRepo = new ContactCompanyRepository();
    contactRoleRepo = new ContactRoleRepository();
    dealEstimateRepo = new DealEstimateRepository();
    
    // Clear all data
    const tables = [
      'company', 'contact', 'deal', 'project', 'estimate',
      'contact_company', 'contact_relationships', 'contact_role',
      'deal_estimate', 'project_contact',
      'project_dependency', 'estimate_allocation', 'estimate_time_allocation'
    ];
    
    for (const table of tables) {
      try {
        db.prepare(`DELETE FROM ${table}`).run();
      } catch (error) {
        // Table might not exist in older schemas
      }
    }
    
    // Create test data
    await setupTestData();
  });
  
  afterAll(() => {
    db.close();
  });
  
  async function setupTestData() {
    // Create companies
    testCompanyId = uuidv4();
    const parentCompanyId = uuidv4();
    const childCompanyId = uuidv4();
    
    companyRepo.createCompany({
      id: testCompanyId,
      name: 'Test Company',
      industry: 'Technology',
      size: '10-50'
    }, 'test-user');
    
    companyRepo.createCompany({
      id: parentCompanyId,
      name: 'Parent Company',
      industry: 'Finance'
    }, 'test-user');
    
    companyRepo.createCompany({
      id: childCompanyId,
      name: 'Child Company',
      industry: 'Retail'
    }, 'test-user');
    
    // Note: company_relationship table has been removed from the schema
    
    // Create contacts
    testContactId = uuidv4();
    const contact2Id = uuidv4();
    
    contactRepo.createContact({
      id: testContactId,
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      job_title: 'CEO'
    }, 'test-user');
    
    contactRepo.createContact({
      id: contact2Id,
      first_name: 'Jane',
      last_name: 'Smith',
      email: '<EMAIL>',
      job_title: 'CTO'
    }, 'test-user');
    
    // Create contact-company relationships
    contactCompanyRepo.createRelationship({
      contact_id: testContactId,
      company_id: testCompanyId,
      role: 'CEO',
      is_primary: true
    });
    
    contactCompanyRepo.createRelationship({
      contact_id: contact2Id,
      company_id: testCompanyId,
      role: 'CTO',
      is_primary: false
    });
    
    // Create deals
    testDealId = uuidv4();
    dealRepo.createDeal({
      id: testDealId,
      name: 'Test Deal',
      company_id: testCompanyId,
      stage: 'proposal',
      value: 100000,
      probability: 0.75
    }, 'test-user');
    
    // Create contact roles in deals
    contactRoleRepo.createContactRole({
      deal_id: testDealId,
      contact_id: testContactId,
      role: 'decision_maker'
    });
    
    // Create projects
    testProjectId = uuidv4();
    projectRepo.createProject({
      id: testProjectId,
      name: 'Test Project',
      company_id: testCompanyId,
      deal_id: testDealId,
      status: 'active',
      start_date: '2024-01-01',
      end_date: '2024-12-31',
      budget: 50000
    });
    
    // Create estimates
    testEstimateId = uuidv4();
    await estimateRepo.create({
      uuid: testEstimateId,
      clientId: 'harvest-client-123',
      clientName: 'Test Company',
      projectName: 'Test Project Estimate',
      startDate: '2024-01-01',
      endDate: '2024-03-31',
      userId: 'test-user',
      allocations: [{
        harvestUserId: 'harvest-user-123',
        firstName: 'John',
        lastName: 'Developer',
        onbordTargetRateDaily: 800,
        onbordCostRateDaily: 500,
        rateProposedDaily: 850,
        weeklyAllocation: {
          '2024-W01': 5,
          '2024-W02': 5
        }
      }]
    });
    
    // Link estimate to deal
    dealEstimateRepo.linkDealToEstimate({
      deal_id: testDealId,
      estimate_id: testEstimateId,
      is_primary: true
    });
  }
  
  describe('getKnowledgeGraph', () => {
    it('returns complete knowledge graph with all entity types', async () => {
      const result = await repository.getKnowledgeGraph();
      
      expect(result.nodes).toBeDefined();
      expect(result.links).toBeDefined();
      expect(result.stats).toBeDefined();
      
      // Check that we have nodes of each type
      const nodeTypes = result.nodes.map(n => n.type);
      expect(nodeTypes).toContain('company');
      expect(nodeTypes).toContain('contact');
      expect(nodeTypes).toContain('deal');
      expect(nodeTypes).toContain('project');
      expect(nodeTypes).toContain('estimate');
      
      // Check stats
      expect(result.stats.totalNodes).toBeGreaterThan(0);
      expect(result.stats.totalLinks).toBeGreaterThan(0);
      expect(result.stats.nodeTypes).toHaveProperty('company');
      expect(result.stats.nodeTypes).toHaveProperty('contact');
    });
    
    it('filters by entity types', async () => {
      const result = await repository.getKnowledgeGraph({
        entityTypes: ['company', 'contact']
      });
      
      const nodeTypes = [...new Set(result.nodes.map(n => n.type))];
      expect(nodeTypes).toEqual(expect.arrayContaining(['company', 'contact']));
      expect(nodeTypes).not.toContain('deal');
      expect(nodeTypes).not.toContain('project');
      expect(nodeTypes).not.toContain('estimate');
    });
    
    it('excludes deleted entities by default', async () => {
      // Soft delete a company
      companyRepo.deleteCompany(testCompanyId, 'test-user');
      
      const result = await repository.getKnowledgeGraph();
      const companyNode = result.nodes.find(n => n.id === testCompanyId);
      
      expect(companyNode).toBeUndefined();
    });
    
    it('includes deleted entities when requested', async () => {
      // Soft delete a company
      companyRepo.deleteCompany(testCompanyId, 'test-user');
      
      const result = await repository.getKnowledgeGraph({
        includeDeleted: true
      });
      const companyNode = result.nodes.find(n => n.id === testCompanyId);
      
      expect(companyNode).toBeDefined();
    });
    
    it('respects maxNodes limit', async () => {
      const result = await repository.getKnowledgeGraph({
        maxNodes: 3
      });
      
      expect(result.nodes.length).toBeLessThanOrEqual(3);
    });
    
    it('includes all relationship types', async () => {
      const result = await repository.getKnowledgeGraph();
      
      const linkTypes = [...new Set(result.links.map(l => l.type))];
      expect(linkTypes).toContain('works_at'); // contact-company
      expect(linkTypes).toContain('belongs_to'); // deal-company
      expect(linkTypes).toContain('decision_maker'); // contact role in deal
      expect(linkTypes).toContain('has_estimate'); // deal-estimate
      expect(linkTypes).toContain('subsidiary'); // company-company
    });
    
    it('calculates correct stats', async () => {
      const result = await repository.getKnowledgeGraph();
      
      expect(result.stats.totalNodes).toBe(result.nodes.length);
      expect(result.stats.totalLinks).toBe(result.links.length);
      
      // Sum of node type counts should equal total nodes
      const nodeTypeSum = Object.values(result.stats.nodeTypes).reduce((a, b) => a + b, 0);
      expect(nodeTypeSum).toBe(result.stats.totalNodes);
      
      // Sum of link type counts should equal total links
      const linkTypeSum = Object.values(result.stats.linkTypes).reduce((a, b) => a + b, 0);
      expect(linkTypeSum).toBe(result.stats.totalLinks);
    });
  });
  
  describe('getEntitySubgraph', () => {
    it('returns subgraph centered on a company', async () => {
      const result = await repository.getEntitySubgraph(testCompanyId, 'company', 2);
      
      // Should include the company itself
      const companyNode = result.nodes.find(n => n.id === testCompanyId);
      expect(companyNode).toBeDefined();
      expect(companyNode?.type).toBe('company');
      
      // Should include related entities
      const nodeTypes = [...new Set(result.nodes.map(n => n.type))];
      expect(nodeTypes).toContain('contact'); // employees
      expect(nodeTypes).toContain('deal'); // company deals
      expect(nodeTypes).toContain('project'); // company projects
    });
    
    it('returns subgraph centered on a contact', async () => {
      const result = await repository.getEntitySubgraph(testContactId, 'contact', 2);
      
      // Should include the contact itself
      const contactNode = result.nodes.find(n => n.id === testContactId);
      expect(contactNode).toBeDefined();
      expect(contactNode?.type).toBe('contact');
      
      // Should include related company
      const companyNode = result.nodes.find(n => n.id === testCompanyId);
      expect(companyNode).toBeDefined();
      
      // Should include deals where contact has a role
      const dealNode = result.nodes.find(n => n.id === testDealId);
      expect(dealNode).toBeDefined();
    });
    
    it('respects depth parameter', async () => {
      // Depth 1: Only direct relationships
      const depth1 = await repository.getEntitySubgraph(testDealId, 'deal', 1);
      
      // Should include deal, its company, contacts, and estimates
      const nodeTypes1 = [...new Set(depth1.nodes.map(n => n.type))];
      expect(nodeTypes1).toContain('deal');
      expect(nodeTypes1).toContain('company');
      expect(nodeTypes1).toContain('contact');
      expect(nodeTypes1).toContain('estimate');
      
      // Depth 2: Should include more distant relationships
      const depth2 = await repository.getEntitySubgraph(testDealId, 'deal', 2);
      
      // Should have more nodes at depth 2
      expect(depth2.nodes.length).toBeGreaterThan(depth1.nodes.length);
    });
    
    it('handles non-existent entities gracefully', async () => {
      const result = await repository.getEntitySubgraph('non-existent-id', 'company', 2);
      
      expect(result.nodes).toHaveLength(0);
      expect(result.links).toHaveLength(0);
      expect(result.stats.totalNodes).toBe(0);
      expect(result.stats.totalLinks).toBe(0);
    });
    
    it('includes correct metadata for entities', async () => {
      const result = await repository.getEntitySubgraph(testCompanyId, 'company', 1);
      
      const companyNode = result.nodes.find(n => n.id === testCompanyId);
      expect(companyNode?.metadata).toBeDefined();
      expect(companyNode?.metadata?.industry).toBe('Technology');
      expect(companyNode?.metadata?.size).toBe('10-50');
      
      const contactNode = result.nodes.find(n => n.type === 'contact');
      expect(contactNode?.metadata).toBeDefined();
      expect(contactNode?.metadata?.jobTitle).toBeDefined();
    });
    
    it('avoids infinite loops with circular relationships', async () => {
      // This should complete without hanging
      const result = await repository.getEntitySubgraph(testCompanyId, 'company', 10);
      
      expect(result).toBeDefined();
      expect(result.nodes.length).toBeGreaterThan(0);
    });
  });
  
  describe('edge cases', () => {
    it('handles empty database gracefully', async () => {
      // Clear all data
      const tables = ['company', 'contact', 'deal', 'project', 'estimate'];
      for (const table of tables) {
        try {
          db.prepare(`DELETE FROM ${table}`).run();
        } catch (error) {
          // Ignore
        }
      }
      
      const result = await repository.getKnowledgeGraph();
      
      expect(result.nodes).toHaveLength(0);
      expect(result.links).toHaveLength(0);
      expect(result.stats.totalNodes).toBe(0);
      expect(result.stats.totalLinks).toBe(0);
    });
    
    it('handles entities with missing related data', async () => {
      // Create orphan deal without company
      const orphanDealId = uuidv4();
      db.prepare(`
        INSERT INTO deal (id, name, stage, value, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(orphanDealId, 'Orphan Deal', 'proposal', 50000, 
             new Date().toISOString(), new Date().toISOString(), 'test-user', 'test-user');
      
      const result = await repository.getKnowledgeGraph();
      const orphanNode = result.nodes.find(n => n.id === orphanDealId);
      
      expect(orphanNode).toBeDefined();
      expect(orphanNode?.type).toBe('deal');
    });
  });
});