import { Request, Response, NextFunction } from 'express';
import { getXeroService } from '../../services/xero';

/**
 * Middleware to handle OAuth token refresh and errors
 */
export const handleOAuthErrors = async (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Check if this is an OAuth-related error
  if (error.response?.status === 401 || error.message?.includes('token') || error.message?.includes('unauthorized')) {
    console.error('OAuth error detected:', {
      status: error.response?.status,
      message: error.message,
      endpoint: req.path
    });

    try {
      // Attempt to refresh the token if we have a refresh token
      if (req.session?.tokenSet?.refresh_token) {
        console.log('Attempting to refresh OAuth token...');
        
        const xeroService = getXeroService();
        // Note: The Xero SDK should handle token refresh internally
        // but we need to ensure it's properly configured
        
        // Clear the current session to force re-authentication
        delete req.session.tokenSet;
        delete req.session.decodedIdToken;
        delete req.session.decodedAccessToken;
        delete req.session.allTenants;
        delete req.session.activeTenant;
        delete req.session.userInfo;
        
        // Save session changes
        if (req.session.save) {
          await new Promise((resolve, reject) => {
            req.session.save((err) => {
              if (err) reject(err);
              else resolve(true);
            });
          });
        }
        
        return res.status(401).json({
          error: 'Authentication expired',
          type: 'OAUTH_TOKEN_EXPIRED',
          message: 'Your session has expired. Please re-authenticate.',
          redirectUrl: '/api/xero/auth',
          timestamp: new Date().toISOString()
        });
      }
    } catch (refreshError) {
      console.error('Failed to refresh OAuth token:', refreshError);
    }
    
    // If we can't refresh, return auth error
    return res.status(401).json({
      error: 'Authentication required',
      type: 'OAUTH_ERROR',
      message: 'Please authenticate with Xero to continue.',
      redirectUrl: '/api/xero/auth',
      timestamp: new Date().toISOString()
    });
  }
  
  // Pass non-OAuth errors to the next handler
  next(error);
};

/**
 * Middleware to validate OAuth session before API calls
 */
export const validateOAuthSession = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // Skip validation in mock mode
  if (process.env.USE_MOCK_AUTH === 'true' && process.env.NODE_ENV !== 'production') {
    console.log('[MOCK AUTH] Skipping OAuth validation for', req.path);
    return next();
  }

  // Skip validation for auth endpoints
  if (req.path.includes('/auth') || req.path.includes('/callback') || req.path === '/health') {
    return next();
  }
  
  try {
    // Check if we have a token set
    if (!req.session?.tokenSet) {
      return res.status(401).json({
        error: 'No authentication session',
        type: 'NO_OAUTH_SESSION',
        message: 'Please authenticate with Xero first.',
        redirectUrl: '/api/xero/auth',
        timestamp: new Date().toISOString()
      });
    }
    
    // Check if token is expired
    const tokenSet = req.session.tokenSet;
    if (tokenSet.expires_at) {
      const expiresAt = new Date(tokenSet.expires_at * 1000); // Convert from seconds to milliseconds
      const now = new Date();
      
      if (now >= expiresAt) {
        console.log('OAuth token expired:', {
          expiresAt: expiresAt.toISOString(),
          now: now.toISOString()
        });
        
        // Clear expired session
        delete req.session.tokenSet;
        delete req.session.decodedIdToken;
        delete req.session.decodedAccessToken;
        
        return res.status(401).json({
          error: 'Token expired',
          type: 'OAUTH_TOKEN_EXPIRED',
          message: 'Your authentication has expired. Please log in again.',
          redirectUrl: '/api/xero/auth',
          timestamp: new Date().toISOString()
        });
      }
      
      // Warn if token expires soon (within 5 minutes)
      const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
      if (expiresAt <= fiveMinutesFromNow) {
        console.warn('OAuth token expires soon:', {
          expiresAt: expiresAt.toISOString(),
          minutesRemaining: Math.floor((expiresAt.getTime() - now.getTime()) / 60000)
        });
        
        // Add warning header
        res.setHeader('X-Token-Expires-Soon', 'true');
        res.setHeader('X-Token-Expires-At', expiresAt.toISOString());
      }
    }
    
    next();
  } catch (error) {
    console.error('Error validating OAuth session:', error);
    next(error);
  }
};