import React from 'react';
import { useQuery } from 'react-query';
import { checkHubSpotStatus } from '../../../api/hubspot';

/**
 * Component for displaying HubSpot integration status
 * Configuration is now done via environment variable
 */
const HubSpotSettings: React.FC = () => {
  // Fetch HubSpot status
  const { data: hubspotStatus, isLoading: isStatusLoading } = useQuery(
    'hubspotStatus',
    checkHubSpotStatus
  );

  if (isStatusLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const isConfigured = hubspotStatus?.data?.isConfigured || false;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
        HubSpot Integration
      </h3>
      
      <div className="space-y-4">
        {/* Status Display */}
        <div className="flex items-center space-x-2">
          <div className={`h-3 w-3 rounded-full ${isConfigured ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {isConfigured ? 'Connected' : 'Not Connected'}
          </span>
        </div>

        {/* Configuration Instructions */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Configuration
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            HubSpot integration is now configured via environment variable.
          </p>
          
          {!isConfigured && (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                To enable HubSpot integration:
              </p>
              <ol className="list-decimal list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1 ml-2">
                <li>Create a private app in your HubSpot account</li>
                <li>Copy the access token</li>
                <li>Set the <code className="bg-gray-200 dark:bg-gray-600 px-1 py-0.5 rounded text-xs">HUBSPOT_ACCESS_TOKEN</code> environment variable</li>
                <li>Restart the application</li>
              </ol>
            </div>
          )}
        </div>

        {/* HubSpot Link */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <a
            href="https://app.hubspot.com/private-apps"
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Manage HubSpot Private Apps →
          </a>
        </div>
      </div>
    </div>
  );
};

export default HubSpotSettings;