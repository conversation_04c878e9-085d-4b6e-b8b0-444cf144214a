import { Request, Response } from 'express';
import { jest } from '@jest/globals';

// Mock dependencies
jest.mock('../../../../src/api/services/activity-service');
jest.mock('../../../../src/api/repositories/activity-repository');
jest.mock('../../../../src/utils/backend-logger');

describe('Activity API Routes', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      query: {},
      params: {},
      body: {},
      user: { id: 'user-123', email: '<EMAIL>' },
    };
    mockResponse = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    mockNext = jest.fn();
    jest.clearAllMocks();
  });

  describe('GET /api/activity', () => {
    it('should fetch activities with pagination', async () => {
      mockRequest.query = {
        page: '1',
        limit: '20',
        type: 'deal_updated',
      };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      const mockActivities = {
        activities: [
          {
            id: 1,
            actor_type: 'user',
            actor_id: 'user-123',
            action: 'deal_updated',
            target_type: 'deal',
            target_id: 'deal-456',
            metadata: { changes: ['amount', 'stage'] },
            created_at: '2024-01-01T00:00:00Z',
          },
          {
            id: 2,
            actor_type: 'user',
            actor_id: 'user-123',
            action: 'deal_updated',
            target_type: 'deal',
            target_id: 'deal-789',
            metadata: { changes: ['probability'] },
            created_at: '2024-01-01T01:00:00Z',
          },
        ],
        total: 50,
        page: 1,
        limit: 20,
      };
      ActivityService.prototype.getActivities = jest.fn().mockResolvedValue(mockActivities);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getActivities).toHaveBeenCalledWith({
        page: 1,
        limit: 20,
        type: 'deal_updated',
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockActivities);
    });

    it('should fetch activities with date range filter', async () => {
      mockRequest.query = {
        from: '2024-01-01',
        to: '2024-01-31',
        actor_id: 'user-123',
      };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      const mockActivities = {
        activities: [],
        total: 0,
        page: 1,
        limit: 50,
      };
      ActivityService.prototype.getActivities = jest.fn().mockResolvedValue(mockActivities);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getActivities).toHaveBeenCalledWith({
        from: '2024-01-01',
        to: '2024-01-31',
        actor_id: 'user-123',
        page: 1,
        limit: 50,
      });
    });
  });

  describe('POST /api/activity', () => {
    it('should create a new activity', async () => {
      mockRequest.body = {
        action: 'estimate_published',
        target_type: 'estimate',
        target_id: 'estimate-123',
        metadata: {
          estimate_name: 'Q1 2024 Project',
          amount: 50000,
        },
      };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      const mockCreatedActivity = {
        id: 3,
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_published',
        target_type: 'estimate',
        target_id: 'estimate-123',
        metadata: {
          estimate_name: 'Q1 2024 Project',
          amount: 50000,
        },
        created_at: '2024-01-01T02:00:00Z',
      };
      ActivityService.prototype.createActivity = jest.fn().mockResolvedValue(mockCreatedActivity);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.createActivity).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user-123',
        action: 'estimate_published',
        target_type: 'estimate',
        target_id: 'estimate-123',
        metadata: {
          estimate_name: 'Q1 2024 Project',
          amount: 50000,
        },
      });
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith(mockCreatedActivity);
    });

    it('should handle validation errors', async () => {
      mockRequest.body = {
        action: 'invalid_action',
      };
      
      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/' && layer.route?.methods?.post
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Missing required fields: target_type, target_id',
      });
    });
  });

  describe('GET /api/activity/stats', () => {
    it('should fetch activity statistics', async () => {
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      const mockStats = {
        total_activities: 1000,
        activities_today: 50,
        activities_this_week: 250,
        activities_this_month: 800,
        top_actions: [
          { action: 'deal_updated', count: 300 },
          { action: 'estimate_created', count: 200 },
          { action: 'company_created', count: 150 },
        ],
        top_actors: [
          { actor_id: 'user-123', actor_type: 'user', count: 400 },
          { actor_id: 'user-456', actor_type: 'user', count: 300 },
        ],
      };
      ActivityService.prototype.getStats = jest.fn().mockResolvedValue(mockStats);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/stats'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getStats).toHaveBeenCalled();
      expect(mockResponse.json).toHaveBeenCalledWith(mockStats);
    });
  });

  describe('GET /api/activity/timeline/:targetType/:targetId', () => {
    it('should fetch activity timeline for a specific target', async () => {
      mockRequest.params = {
        targetType: 'deal',
        targetId: 'deal-123',
      };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      const mockTimeline = [
        {
          id: 1,
          action: 'deal_created',
          actor_type: 'user',
          actor_id: 'user-123',
          created_at: '2024-01-01T00:00:00Z',
          metadata: {},
        },
        {
          id: 2,
          action: 'deal_updated',
          actor_type: 'user',
          actor_id: 'user-456',
          created_at: '2024-01-02T00:00:00Z',
          metadata: { changes: ['amount'] },
        },
      ];
      ActivityService.prototype.getTargetTimeline = jest.fn().mockResolvedValue(mockTimeline);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/timeline/:targetType/:targetId'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getTargetTimeline).toHaveBeenCalledWith('deal', 'deal-123');
      expect(mockResponse.json).toHaveBeenCalledWith(mockTimeline);
    });
  });

  describe('DELETE /api/activity/:id', () => {
    it('should delete an activity', async () => {
      mockRequest.params = { id: '123' };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      ActivityService.prototype.deleteActivity = jest.fn().mockResolvedValue(true);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.deleteActivity).toHaveBeenCalledWith(123);
      expect(mockResponse.status).toHaveBeenCalledWith(204);
      expect(mockResponse.json).toHaveBeenCalledWith({ success: true });
    });

    it('should handle not found errors', async () => {
      mockRequest.params = { id: '999' };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      ActivityService.prototype.deleteActivity = jest.fn().mockResolvedValue(false);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/:id' && layer.route?.methods?.delete
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Activity not found' });
    });
  });

  describe('GET /api/activity/feed', () => {
    it('should fetch activity feed with real-time updates', async () => {
      mockRequest.query = {
        since: '2024-01-01T00:00:00Z',
        limit: '10',
      };
      const ActivityService = (await import('../../../../src/api/services/activity-service')).default;
      const mockFeed = {
        activities: [
          {
            id: 10,
            action: 'xero_sync',
            actor_type: 'system',
            actor_id: 'system',
            target_type: 'integration',
            target_id: 'xero',
            metadata: { records_synced: 150 },
            created_at: '2024-01-01T00:30:00Z',
          },
        ],
        has_more: false,
        last_timestamp: '2024-01-01T00:30:00Z',
      };
      ActivityService.prototype.getFeed = jest.fn().mockResolvedValue(mockFeed);

      const activityRoutes = await import('../../../../src/api/routes/activity');
      const handler = activityRoutes.default.stack.find(
        (layer: any) => layer.route?.path === '/feed'
      )?.route?.stack[0]?.handle;

      await handler(mockRequest as Request, mockResponse as Response, mockNext);

      expect(ActivityService.prototype.getFeed).toHaveBeenCalledWith({
        since: '2024-01-01T00:00:00Z',
        limit: 10,
      });
      expect(mockResponse.json).toHaveBeenCalledWith(mockFeed);
    });
  });
});