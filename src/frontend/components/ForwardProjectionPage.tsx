import React, { useState, useEffect } from "react"; import { CustomExpense } from "../../types"; import { ProjectionProvider, ErrorDisplay, ContentDisplay, ProjectionSettingsPanel, LoadingIndicator, } from "./ForwardProjection"; import { getExpenses } from "../api/expenses"; interface ForwardProjectionPageProps { customExpenses?: CustomExpense[]; } /** * Forward projection page component * Shows a simplified cashflow projection from today forward * Automatically loads projection data when authenticated * Now fetches expenses directly from API to ensure they're available */ export const ForwardProjectionPage: React.FC<ForwardProjectionPageProps> = ({ customExpenses: propExpenses = [], }) => { const [customExpenses, setCustomExpenses] = useState<CustomExpense[]>(propExpenses); const [loading, setLoading] = useState<boolean>(propExpenses.length === 0); // Fetch expenses from API when component mounts if none were provided as props useEffect(() => { async function loadExpenses() { try { setLoading(true); const expenses = await getExpenses(); setCustomExpenses(expenses); } catch (error) { console.error("Failed to load expenses for projection:", error); } finally { setLoading(false); } } // Only fetch if we don't already have expenses (from props) if (propExpenses.length === 0) { loadExpenses(); } }, [propExpenses.length]); if (loading) { return ( <div className="flex justify-center items-center p-8"> <LoadingIndicator /> </div> ); } return ( <ProjectionProvider customExpenses={customExpenses}> <ForwardProjectionContent /> </ProjectionProvider> ); }; // ForecastModeIndicator component has been removed as Smart Forecast is now the only system const ForwardProjectionContent: React.FC = () => { // Removed the useEffect hook that called loadProjectionData(true) // The initial load is now handled by the ProjectionProvider return ( <div className="px-3 sm:px-6 md:px-8 max-w-7xl mx-auto"> <div className="space-y-2 sm:space-y-4"> <ProjectionSettingsPanel /> <ErrorDisplay /> <ContentDisplay /> </div> </div> ); }; 