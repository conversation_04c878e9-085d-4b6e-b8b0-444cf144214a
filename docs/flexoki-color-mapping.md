# Theming and Styling System Documentation

This document provides a comprehensive guide to the theming and styling architecture in the Onbord Financial Dashboard, including how to work with the Flexoki theme and implement new themes.

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Core Theme System](#core-theme-system)
3. [CSS Variables System](#css-variables-system)
4. [Tailwind Integration](#tailwind-integration)
5. [Component Styling](#component-styling)
6. [Theme Switching](#theme-switching)
7. [Color Mapping Reference](#color-mapping-reference)
8. [Best Practices](#best-practices)
9. [Troubleshooting](#troubleshooting)

## Architecture Overview

The theming system is built on three pillars:

```
┌─────────────────────────────────────────────────────────────┐
│                    flexoki-theme.ts                         │
│               (Single Source of Truth)                      │
└──────────────────────┬──────────────────────────────────────┘
                       │
        ┌──────────────┴──────────────┬────────────────────┐
        ▼                             ▼                    ▼
┌───────────────┐          ┌──────────────────┐  ┌─────────────────┐
│ Tailwind CSS  │          │  CSS Variables   │  │   Component     │
│   Config      │          │    System        │  │    Styles       │
└───────────────┘          └──────────────────┘  └─────────────────┘
```

### Key Files
- **`src/styles/flexoki-theme.ts`** - Central theme definition
- **`src/frontend/styles/modern-design-system.css`** - CSS variables and utilities
- **`tailwind.config.js`** - Tailwind configuration with theme integration
- **`src/frontend/styles/foundation.css`** - Base styles and resets

## Core Theme System

### 1. Theme Definition Structure

```typescript
// src/styles/flexoki-theme.ts

// Base palette with raw color values
export const flexokiPalette = {
  black: '#100F0F',
  paper: '#FFFCF0',
  base: {
    50: '#F2F0E5',
    100: '#E6E4D9',
    // ... full scale 50-950
  },
  red: { /* full scale */ },
  orange: { /* full scale */ },
  yellow: { /* full scale */ },
  green: { /* full scale */ },
  cyan: { /* full scale */ },
  blue: { /* full scale */ },
  purple: { /* full scale */ },
  magenta: { /* full scale */ }
};

// Semantic color mappings
export const semanticColors = {
  primary: flexokiPalette.blue,
  success: flexokiPalette.green,
  warning: flexokiPalette.orange,
  error: flexokiPalette.red,
  info: flexokiPalette.cyan
};
```

### 2. Color Generation Functions

```typescript
// Generate Tailwind-compatible colors
export const generateTailwindColors = () => {
  return {
    ...flexokiPalette,
    ...semanticColors,
    // Legacy mappings for backward compatibility
    gray: flexokiPalette.base,
    slate: flexokiPalette.base,
    // ... other mappings
  };
};

// Generate CSS variables
export const generateCSSVariables = () => {
  return {
    '--color-primary': semanticColors.primary[600],
    '--color-success': semanticColors.success[600],
    // ... etc
  };
};
```

## CSS Variables System

### 1. Variable Structure

```css
/* modern-design-system.css */

:root {
  /* Core UI Colors */
  --color-bg: #FFFCF0; /* flexoki paper */
  --color-surface: #F2F0E5; /* flexoki base-50 */
  --color-border: #CECDC3; /* flexoki base-200 */
  
  /* Text Colors */
  --color-text: #100F0F; /* flexoki black */
  --color-text-muted: #6F6E69; /* flexoki base-600 */
  
  /* Semantic Colors */
  --color-primary: #205EA6;
  --color-success: #66800B;
  --color-warning: #BC5215;
  --color-error: #AF3029;
  
  /* RGB Component Values (for rgba usage) */
  --color-shadow-rgb: 16, 15, 15; /* flexoki black */
  --color-paper-rgb: 255, 252, 240; /* flexoki paper */
  --color-primary-rgb: 32, 94, 166; /* flexoki blue-600 */
}

/* Dark theme overrides */
.dark {
  --color-bg: #100F0F;
  --color-surface: #1C1B1A;
  --color-shadow-rgb: 0, 0, 0; /* pure black for dark shadows */
  /* ... etc */
}
```

### 2. RGB Variables for Transparency

For theme-aware transparent colors:

```css
/* Shadows using RGB variables */
--shadow-sm: 0 1px 3px rgba(var(--color-shadow-rgb), 0.1);

/* Gradients using RGB variables */
background: linear-gradient(to bottom, rgba(var(--color-paper-rgb), 0.1), transparent);
```

## Tailwind Integration

### 1. Configuration

```javascript
// tailwind.config.js
const { generateTailwindColors } = require('./src/styles/flexoki-theme');

module.exports = {
  theme: {
    extend: {
      colors: generateTailwindColors(),
      // This provides all Flexoki colors plus legacy mappings
    }
  }
}
```

### 2. Color Mappings

| Original Tailwind | Mapped To | Notes |
|-------------------|-----------|-------|
| `gray-*` | `base-*` | All grayscale variants |
| `slate-*` | `base-*` | Consolidated to base |
| `emerald-*` | `green-*` | Simplified palette |
| `indigo-*` | `blue-*` | Merged with blue |
| `amber-*` | `yellow-*` | Consolidated |
| `rose-*` | `red-*` | Simplified |

### 3. Usage Examples

```jsx
// These all work due to mappings:
<div className="bg-gray-100" />     // → bg-base-100
<div className="text-emerald-600" /> // → text-green-600
<div className="border-slate-300" /> // → border-base-300

// Preferred semantic usage:
<div className="bg-surface" />       // Uses CSS variable
<div className="text-primary" />     // Uses CSS variable
```

## Component Styling

### 1. CSS Variable Usage

```css
/* Component styles should use CSS variables */
.card {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

/* Dark mode automatically handled */
.dark .card {
  /* Variables are already overridden in :root */
}
```

### 2. Tailwind in Components

```jsx
// React component using Tailwind
function Card({ children }) {
  return (
    <div className="bg-surface border border-border rounded-lg p-4">
      {children}
    </div>
  );
}
```

### 3. Dynamic Styling

```jsx
// For dynamic colors, use the theme object
import { flexokiPalette } from '@/styles/flexoki-theme';

function StatusIndicator({ status }) {
  const color = status === 'success' 
    ? flexokiPalette.green[600]
    : flexokiPalette.red[600];
    
  return <div style={{ color }} />;
}
```

## Theme Switching

### 1. Implementing a New Theme

To add a new theme (e.g., Solarized):

```typescript
// src/styles/solarized-theme.ts
export const solarizedPalette = {
  black: '#002b36',
  paper: '#fdf6e3',
  base: {
    50: '#fdf6e3',
    100: '#eee8d5',
    // ... full scale
  },
  // ... other colors
};

export const generateTailwindColors = () => {
  // Same structure as flexoki-theme.ts
};
```

### 2. Dynamic Theme Switching

```typescript
// src/contexts/ThemeContext.tsx
export function ThemeProvider({ children }) {
  const [theme, setTheme] = useState('flexoki');
  
  useEffect(() => {
    // Load theme CSS variables
    const root = document.documentElement;
    const themeModule = themes[theme];
    const cssVars = themeModule.generateCSSVariables();
    
    Object.entries(cssVars).forEach(([key, value]) => {
      root.style.setProperty(key, value);
    });
  }, [theme]);
  
  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}
```

### 3. Theme Toggle Component

```jsx
function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  
  return (
    <select value={theme} onChange={(e) => setTheme(e.target.value)}>
      <option value="flexoki">Flexoki</option>
      <option value="solarized">Solarized</option>
      <option value="dracula">Dracula</option>
    </select>
  );
}
```

## Color Mapping Reference

### Flexoki Palette

| Color | Light Mode | Dark Mode | Usage |
|-------|------------|-----------|-------|
| Background | `#FFFCF0` (paper) | `#100F0F` (black) | Main background |
| Surface | `#F2F0E5` (base-50) | `#1C1B1A` (base-950) | Cards, panels |
| Text | `#100F0F` (black) | `#CECDC3` (base-200) | Primary text |
| Border | `#CECDC3` (base-200) | `#403E3C` (base-800) | Borders, dividers |

### Semantic Colors

| Semantic | Color | Scale | Usage |
|----------|-------|-------|-------|
| Primary | Blue | 50-950 | Actions, links |
| Success | Green | 50-950 | Positive states, income |
| Warning | Orange | 50-950 | Warnings, attention |
| Error | Red | 50-950 | Errors, expenses |
| Info | Cyan | 50-950 | Information |

### Financial Color Standards

- **Green**: ONLY for income/positive transactions
- **Red**: ONLY for expenses/negative values
- **Blue**: Primary actions, links
- **Base/Gray**: Neutral values, UI chrome

## Best Practices

### 1. Color Usage

```jsx
// ✅ DO: Use semantic colors
<Button className="bg-primary text-white" />
<Alert className="bg-error-50 text-error-700" />

// ❌ DON'T: Use specific color names for semantic purposes
<Button className="bg-blue-600" />
<Alert className="bg-red-100" />
```

### 2. Dark Mode

```css
/* ✅ DO: Let CSS variables handle dark mode */
.component {
  background: var(--color-surface);
  color: var(--color-text);
}

/* ❌ DON'T: Manually specify dark mode colors */
.component {
  background: white;
}
.dark .component {
  background: #1a1a1a;
}
```

### 3. Custom Colors

```typescript
// ✅ DO: Reference theme colors
import { flexokiPalette } from '@/styles/flexoki-theme';
const customColor = flexokiPalette.blue[400];

// ❌ DON'T: Hardcode hex values
const customColor = '#3B82F6';
```

### 4. Opacity and Shadows

```css
/* ✅ DO: Use RGB variables for transparency */
box-shadow: 0 4px 6px rgba(var(--color-shadow-rgb), 0.1);

/* ❌ DON'T: Hardcode rgba values */
box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
```

## Troubleshooting

### Common Issues

1. **Colors not updating after theme change**
   - Clear Tailwind cache: `rm -rf .next` or `rm -rf dist`
   - Rebuild: `npm run build`

2. **Hardcoded colors appearing**
   - Search for hex values: `grep -r "#[0-9A-Fa-f]\{6\}" src/`
   - Check for rgb/rgba: `grep -r "rgb\|rgba" src/`

3. **Dark mode not working**
   - Verify `.dark` class on `<html>` element
   - Check CSS variable overrides in `.dark` selector

4. **Tailwind classes not working**
   - Check color exists in `generateTailwindColors()`
   - Verify Tailwind config imports theme file

5. **Card readability issues**
   - Ensure cards use `var(--color-surface)` instead of `theme('colors.white')`
   - Use `text-gray-900` for primary text instead of `text-gray-700`
   - Check that card classes use CSS variables instead of hardcoded Tailwind colors

6. **Component styling inconsistencies**
   - Verify components use CSS variables for backgrounds and text
   - Check that hover states use Flexoki color variables
   - Ensure proper contrast ratios are maintained

### Debug Commands

```bash
# Find non-theme colors
grep -r "rgb\|#[0-9A-Fa-f]\{3,6\}" src/ --include="*.css" --include="*.tsx"

# Check for hardcoded white/black
grep -r "white\|black" src/ --include="*.tsx" | grep -v "flexoki"

# Verify theme imports
grep -r "flexoki-theme" src/
```

### Migration Checklist

When updating the theme:
- [ ] Update color values in `flexoki-theme.ts`
- [ ] Regenerate CSS variables if needed
- [ ] Run build to update Tailwind
- [ ] Test in both light and dark modes
- [ ] Check financial colors (green=income, red=expense)
- [ ] Verify contrast ratios for accessibility
- [ ] Update card classes to use CSS variables instead of Tailwind colors
- [ ] Ensure text colors provide adequate contrast (use `text-gray-900` for primary text)
- [ ] Test component readability in both light and dark modes
- [ ] Verify Transaction Filters and other interactive components use proper theming

## Recent Fixes (December 2024)

### Card System Improvements

Fixed major readability issues with the card system:

**Problem**: Cards were using white backgrounds with light gray text, causing poor readability.

**Solution**: Updated all card classes to use Flexoki CSS variables:

```css
/* Before */
.card-info {
  @apply bg-blue-50 dark:bg-blue-900 bg-opacity-50 dark:bg-opacity-20 border border-blue-100 dark:border-blue-800;
}

/* After */
.card-info {
  background-color: var(--color-info-bg);
  border: 1px solid var(--color-border);
  color: var(--color-text);
}
```

### Transaction Filters Styling

Fixed the "black box" issue in Transaction Filters component:

**Problem**: Component was using generic Tailwind gray classes that appeared as dark boxes.

**Solution**: Replaced with Flexoki CSS variables and proper hover states:

```tsx
// Before
className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"

// After
style={{
  backgroundColor: 'var(--color-surface-alt)',
  color: 'var(--color-text)',
  transition: 'background-color 0.2s ease'
}}
```

### Text Contrast Improvements

Updated text colors for better readability:

- Changed primary text from `text-gray-700` to `text-gray-900`
- Updated subtitle text from `text-gray-500` to `text-gray-600`
- Ensured all labels use `var(--color-text)` for consistency

### Files Updated

- `src/tailwind.css` - Card class definitions
- `src/frontend/styles/components/card.css` - Adaptive card styling
- `src/frontend/styles/foundation.css` - Base card component
- `src/frontend/components/ForwardProjection/CashflowSummaryCards.tsx` - Text contrast
- `src/frontend/components/ForwardProjection/Transactions/FilterHeader.tsx` - Filter styling
- `src/frontend/components/ForwardProjection/TransactionFilters.tsx` - Input styling

## Future Enhancements

1. **Theme Builder UI**: Visual tool to create custom themes
2. **Contrast Checker**: Automated accessibility validation
3. **Theme Presets**: Additional built-in themes
4. **Component Variants**: Theme-aware component variations
5. **Animation Themes**: Consistent motion design per theme