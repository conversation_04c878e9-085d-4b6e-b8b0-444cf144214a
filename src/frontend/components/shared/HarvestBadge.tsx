import React from 'react'; import { IconBadge } from './IconBadge'; import type { IconBadgeVariantProps } from '../../utils/component-variants'; interface HarvestBadgeProps extends Partial<IconBadgeVariantProps> { className?: string; /** * Click handler for interactive Harvest badge */ onClick?: () => void; } /** * Harvest logo badge component with consistent styling and accessibility. * * Features: * - Uses official Harvest logo SVG * - Consistent sizing with design system * - Proper accessibility attributes * - Optional interactive behavior * - Performance optimized loading * * @example * ```tsx * <HarvestBadge size="lg" title="Time tracking data from Harvest" /> * ``` */ const HarvestBadge: React.FC<HarvestBadgeProps> = ({ className, size = 'default', rounded = 'default', onClick, ...props }) => { return ( <IconBadge src="/HarvestLogo.svg" alt="Harvest" title="From Harvest" size={size} rounded={rounded} className={className} onClick={onClick} loading="eager" // Harvest badges are frequently used, load immediately {...props} /> ); }; export default HarvestBadge;