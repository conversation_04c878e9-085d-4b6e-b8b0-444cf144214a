# Testing Quick Wins Guide

## Current State Analysis

After analyzing the test suite, here are the key findings:

### Issues Identified
1. **Import/Export Mismatches**: ~30% of tests fail due to incorrect imports
2. **Outdated Test Expectations**: Tests expect APIs that have changed
3. **Mock Setup Problems**: Database mocking isn't working correctly
4. **Missing Test Coverage**: New features (data sync, event system) lack tests

### Quick Wins (Immediate Impact)

#### 1. Fix Import Statements (30 minutes)
```typescript
// Wrong:
import DealEstimateRepository from '...';

// Correct:
import { DealEstimateRepository } from '...';
```

#### 2. Skip Outdated Tests Temporarily (15 minutes)
```typescript
// Instead of fixing complex tests immediately:
describe.skip('outdated feature', () => {
  // tests that need updating
});
```

#### 3. Focus on Critical Path Tests
Priority order:
1. **Authentication flows** - Business critical
2. **Cashflow calculations** - Core feature
3. **Deal/Estimate linking** - Recent changes
4. **Data synchronization** - New feature

#### 4. Create Test Utilities (1 hour)
```typescript
// tests/utils/test-helpers.ts
export const createMockDeal = (overrides = {}) => ({
  id: 'deal-1',
  name: 'Test Deal',
  stage: 'Identified',
  ...overrides
});

export const mockQueryClient = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  return {
    queryClient,
    wrapper: ({ children }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    )
  };
};
```

### This Week's Action Plan

#### Day 1: Get Tests Running
1. Run the fix script: `node scripts/fix-critical-tests.js`
2. Skip non-critical failing tests
3. Ensure CI passes with reduced test set

#### Day 2: Fix Critical Tests
1. Authentication test suite
2. Cashflow calculation tests
3. Repository pattern tests

#### Day 3: Add New Test Coverage
1. Data synchronization (EventContext)
2. Query invalidation utilities
3. Recent UI changes

#### Day 4-5: Documentation & Cleanup
1. Document which tests are maintained
2. Create test writing guide
3. Set up GitHub Actions for critical tests only

### Test Organization Strategy

```
tests/
├── critical/           # Must pass for deployment
│   ├── auth.test.ts
│   ├── cashflow.test.ts
│   └── data-sync.test.ts
├── features/           # Feature-specific tests
│   ├── deals/
│   ├── estimates/
│   └── crm/
└── legacy/            # Tests needing updates
    └── (moved old tests here)
```

### Package.json Scripts

```json
{
  "scripts": {
    "test:critical": "jest tests/critical --bail",
    "test:quick": "jest --changedSince=main",
    "test:fix": "jest -u --testPathPattern",
    "test:coverage": "jest --coverage --coveragePathIgnorePatterns=legacy"
  }
}
```

### Mock Patterns That Work

```typescript
// For database repositories:
jest.mock('../services/db-service', () => ({
  default: {
    getDb: () => mockDb,
    prepare: jest.fn(),
    exec: jest.fn(),
  }
}));

// For React Query:
const wrapper = ({ children }) => (
  <QueryClientProvider client={createTestQueryClient()}>
    <EventProvider>
      {children}
    </EventProvider>
  </QueryClientProvider>
);
```

### Measurement & Success Criteria

Week 1 Goals:
- ✅ 10 critical tests passing
- ✅ CI/CD pipeline working
- ✅ Test execution time < 30 seconds

Month 1 Goals:
- ✅ 50% of tests updated
- ✅ New features have tests
- ✅ Team follows test patterns

### Common Pitfalls to Avoid

1. **Don't try to fix everything at once** - Focus on critical paths
2. **Don't delete old tests** - Move to legacy folder
3. **Don't mock everything** - Use real implementations where simple
4. **Don't skip documentation** - Future you will thank you

### Next Steps

1. Run `npm test:critical` to see baseline
2. Apply fixes from `scripts/fix-critical-tests.js`
3. Focus on one test file at a time
4. Celebrate small wins!

Remember: The goal is sustainable testing, not perfect coverage immediately.