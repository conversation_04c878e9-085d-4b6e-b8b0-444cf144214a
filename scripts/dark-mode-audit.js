#!/usr/bin/env node

/**
 * Dark Mode Audit Script
 * 
 * This script scans TypeScript/React files for missing dark mode support.
 * It identifies common patterns that should have dark mode variants but don't.
 * 
 * Usage: node scripts/dark-mode-audit.js [path]
 * Example: node scripts/dark-mode-audit.js src/frontend/components
 */

const fs = require('fs');
const path = require('path');

// Color mappings that should have dark mode variants
const DARK_MODE_MAPPINGS = {
  // Background colors
  'bg-white': 'dark:bg-gray-800',
  'bg-gray-50': 'dark:bg-gray-900',
  'bg-gray-100': 'dark:bg-gray-700',
  'bg-gray-200': 'dark:bg-gray-600',
  'bg-gray-300': 'dark:bg-gray-600',
  
  // Text colors
  'text-gray-900': 'dark:text-white',
  'text-gray-800': 'dark:text-gray-200',
  'text-gray-700': 'dark:text-gray-300',
  'text-gray-600': 'dark:text-gray-400',
  'text-gray-500': 'dark:text-gray-400',
  
  // Border colors
  'border-gray-200': 'dark:border-gray-700',
  'border-gray-300': 'dark:border-gray-600',
  'border-gray-400': 'dark:border-gray-500',
  
  // Hover states
  'hover:bg-gray-50': 'dark:hover:bg-gray-800',
  'hover:bg-gray-100': 'dark:hover:bg-gray-700',
  'hover:bg-gray-200': 'dark:hover:bg-gray-600',
  
  // Focus states
  'focus:ring-blue-500': 'dark:focus:ring-blue-400',
  'focus:ring-indigo-500': 'dark:focus:ring-indigo-400',
  
  // Special backgrounds
  'bg-red-50': 'dark:bg-red-900/20',
  'bg-green-50': 'dark:bg-green-900/20',
  'bg-yellow-50': 'dark:bg-yellow-900/20',
  'bg-blue-50': 'dark:bg-blue-900/20',
  'bg-indigo-50': 'dark:bg-indigo-900/20',
  'bg-purple-50': 'dark:bg-purple-900/20',
  
  // Special text colors
  'text-red-700': 'dark:text-red-400',
  'text-green-700': 'dark:text-green-400',
  'text-yellow-700': 'dark:text-yellow-400',
  'text-blue-700': 'dark:text-blue-400',
  'text-indigo-700': 'dark:text-indigo-400',
  'text-purple-700': 'dark:text-purple-400',
  
  // Special borders
  'border-red-200': 'dark:border-red-800',
  'border-green-200': 'dark:border-green-800',
  'border-yellow-200': 'dark:border-yellow-800',
  'border-blue-200': 'dark:border-blue-800',
  'border-indigo-200': 'dark:border-indigo-800',
  'border-purple-200': 'dark:border-purple-800',
  
  // Shadows (often need adjustment in dark mode)
  'shadow-sm': 'dark:shadow-sm',
  'shadow': 'dark:shadow',
  'shadow-md': 'dark:shadow-md',
  'shadow-lg': 'dark:shadow-lg',
  'shadow-xl': 'dark:shadow-xl',
  
  // Ring offsets
  'ring-offset-2': 'dark:ring-offset-gray-800',
  'ring-offset-white': 'dark:ring-offset-gray-800',
  
  // Disabled states
  'disabled:bg-gray-300': 'dark:disabled:bg-gray-600',
  'disabled:text-gray-400': 'dark:disabled:text-gray-500',
};

// Patterns to check for hardcoded colors
const HARDCODED_COLOR_PATTERNS = [
  /#[0-9A-Fa-f]{6}/g,  // Hex colors like #FFFFFF
  /#[0-9A-Fa-f]{3}/g,   // Short hex colors like #FFF
  /rgb\([^)]+\)/g,      // RGB colors
  /rgba\([^)]+\)/g,     // RGBA colors
];

// File extensions to check
const FILE_EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to skip
const SKIP_DIRS = ['node_modules', '.git', 'dist', 'build', 'coverage', '__tests__', '.next'];

// Results storage
const results = {
  missingDarkMode: {},
  hardcodedColors: {},
  suspiciousPatterns: {},
  stats: {
    filesScanned: 0,
    issuesFound: 0,
    missingDarkModeCount: 0,
    hardcodedColorCount: 0,
    suspiciousPatternCount: 0
  }
};

/**
 * Check if a file should be scanned
 */
function shouldScanFile(filePath) {
  return FILE_EXTENSIONS.some(ext => filePath.endsWith(ext));
}

/**
 * Check if a directory should be skipped
 */
function shouldSkipDir(dirName) {
  return SKIP_DIRS.includes(dirName) || dirName.startsWith('.');
}

/**
 * Extract className strings from content
 */
function extractClassNames(content) {
  const classNameRegex = /className\s*=\s*["{`]([^"}` ]+)["}` ]/g;
  const classNameMultilineRegex = /className\s*=\s*["{`]([^"}` ]+(?:\s+[^"}` ]+)*)["}` ]/g;
  const classNameTemplateRegex = /className\s*=\s*{`([^`]+)`}/g;
  
  const classNames = [];
  let match;
  
  while ((match = classNameRegex.exec(content)) !== null) {
    classNames.push({ className: match[1], index: match.index });
  }
  
  while ((match = classNameMultilineRegex.exec(content)) !== null) {
    classNames.push({ className: match[1], index: match.index });
  }
  
  while ((match = classNameTemplateRegex.exec(content)) !== null) {
    classNames.push({ className: match[1], index: match.index });
  }
  
  return classNames;
}

/**
 * Check for missing dark mode classes
 */
function checkMissingDarkMode(filePath, content) {
  const issues = [];
  const classNames = extractClassNames(content);
  
  classNames.forEach(({ className, index }) => {
    Object.entries(DARK_MODE_MAPPINGS).forEach(([lightClass, darkClass]) => {
      if (className.includes(lightClass) && !className.includes(darkClass.replace('dark:', ''))) {
        // Check if the dark variant exists anywhere nearby (within 200 chars)
        const nearbyContent = content.substring(Math.max(0, index - 100), index + 200);
        if (!nearbyContent.includes(darkClass)) {
          const lineNumber = content.substring(0, index).split('\n').length;
          issues.push({
            line: lineNumber,
            class: lightClass,
            suggestedDark: darkClass,
            context: className
          });
        }
      }
    });
  });
  
  return issues;
}

/**
 * Check for hardcoded colors
 */
function checkHardcodedColors(filePath, content) {
  const issues = [];
  const lines = content.split('\n');
  
  lines.forEach((line, index) => {
    // Skip import statements and comments
    if (line.trim().startsWith('import') || line.trim().startsWith('//') || line.trim().startsWith('*')) {
      return;
    }
    
    HARDCODED_COLOR_PATTERNS.forEach(pattern => {
      const matches = line.match(pattern);
      if (matches) {
        matches.forEach(match => {
          // Skip if it's in a comment
          const beforeMatch = line.substring(0, line.indexOf(match));
          if (!beforeMatch.includes('//')) {
            issues.push({
              line: index + 1,
              color: match,
              context: line.trim()
            });
          }
        });
      }
    });
  });
  
  return issues;
}

/**
 * Check for suspicious patterns
 */
function checkSuspiciousPatterns(filePath, content) {
  const issues = [];
  const lines = content.split('\n');
  
  const suspiciousPatterns = [
    // Style objects that might need dark mode consideration
    /style\s*=\s*\{\{[^}]+backgroundColor[^}]+\}\}/g,
    /style\s*=\s*\{\{[^}]+color[^}]+\}\}/g,
    /style\s*=\s*\{\{[^}]+borderColor[^}]+\}\}/g,
    
    // Inline styles
    /style="[^"]*background[^"]*"/g,
    /style="[^"]*color[^"]*"/g,
    
    // Canvas/SVG fills and strokes
    /fillStyle\s*=\s*['"][^'"]+['"]/g,
    /strokeStyle\s*=\s*['"][^'"]+['"]/g,
    /fill\s*=\s*['"][^'"]+['"]/g,
    /stroke\s*=\s*['"][^'"]+['"]/g,
  ];
  
  lines.forEach((line, index) => {
    suspiciousPatterns.forEach(pattern => {
      if (pattern.test(line)) {
        issues.push({
          line: index + 1,
          pattern: pattern.source,
          context: line.trim()
        });
      }
    });
  });
  
  return issues;
}

/**
 * Scan a single file
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    results.stats.filesScanned++;
    
    // Check for missing dark mode classes
    const darkModeIssues = checkMissingDarkMode(filePath, content);
    if (darkModeIssues.length > 0) {
      results.missingDarkMode[filePath] = darkModeIssues;
      results.stats.missingDarkModeCount += darkModeIssues.length;
      results.stats.issuesFound += darkModeIssues.length;
    }
    
    // Check for hardcoded colors
    const colorIssues = checkHardcodedColors(filePath, content);
    if (colorIssues.length > 0) {
      results.hardcodedColors[filePath] = colorIssues;
      results.stats.hardcodedColorCount += colorIssues.length;
      results.stats.issuesFound += colorIssues.length;
    }
    
    // Check for suspicious patterns
    const patternIssues = checkSuspiciousPatterns(filePath, content);
    if (patternIssues.length > 0) {
      results.suspiciousPatterns[filePath] = patternIssues;
      results.stats.suspiciousPatternCount += patternIssues.length;
      results.stats.issuesFound += patternIssues.length;
    }
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
  }
}

/**
 * Recursively scan directory
 */
function scanDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !shouldSkipDir(item)) {
        scanDirectory(fullPath);
      } else if (stat.isFile() && shouldScanFile(fullPath)) {
        scanFile(fullPath);
      }
    });
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

/**
 * Generate report
 */
function generateReport() {
  console.log('\n🌓 Dark Mode Audit Report');
  console.log('========================\n');
  
  console.log(`📊 Summary:`);
  console.log(`   Files scanned: ${results.stats.filesScanned}`);
  console.log(`   Total issues found: ${results.stats.issuesFound}`);
  console.log(`   Missing dark mode variants: ${results.stats.missingDarkModeCount}`);
  console.log(`   Hardcoded colors: ${results.stats.hardcodedColorCount}`);
  console.log(`   Suspicious patterns: ${results.stats.suspiciousPatternCount}\n`);
  
  // Report missing dark mode classes
  if (Object.keys(results.missingDarkMode).length > 0) {
    console.log('🔍 Missing Dark Mode Classes:');
    console.log('----------------------------');
    Object.entries(results.missingDarkMode).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}`);
      issues.forEach(issue => {
        console.log(`   Line ${issue.line}: ${issue.class} → ${issue.suggestedDark}`);
        console.log(`   Context: className="${issue.context}"`);
      });
    });
  }
  
  // Report hardcoded colors
  if (Object.keys(results.hardcodedColors).length > 0) {
    console.log('\n\n🎨 Hardcoded Colors:');
    console.log('-------------------');
    Object.entries(results.hardcodedColors).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}`);
      issues.forEach(issue => {
        console.log(`   Line ${issue.line}: ${issue.color}`);
        console.log(`   Context: ${issue.context}`);
      });
    });
  }
  
  // Report suspicious patterns
  if (Object.keys(results.suspiciousPatterns).length > 0) {
    console.log('\n\n⚠️  Suspicious Patterns:');
    console.log('----------------------');
    Object.entries(results.suspiciousPatterns).forEach(([file, issues]) => {
      console.log(`\n📄 ${file}`);
      issues.forEach(issue => {
        console.log(`   Line ${issue.line}: Inline styles detected`);
        console.log(`   Context: ${issue.context}`);
      });
    });
  }
  
  // Generate fix suggestions
  if (results.stats.issuesFound > 0) {
    console.log('\n\n💡 Fix Suggestions:');
    console.log('------------------');
    console.log('1. Add dark mode classes to all color utilities');
    console.log('2. Replace hardcoded colors with CSS variables or Tailwind classes');
    console.log('3. Use theme-aware colors for Canvas/SVG rendering');
    console.log('4. Consider using the @container queries for component-level theming');
    console.log('5. Test all components in both light and dark modes');
    
    // Generate a summary CSV for easier tracking
    const csvPath = path.join(process.cwd(), 'dark-mode-audit-report.csv');
    const csvContent = generateCSV();
    fs.writeFileSync(csvPath, csvContent);
    console.log(`\n📊 Detailed CSV report saved to: ${csvPath}`);
  } else {
    console.log('\n✅ No dark mode issues found! Great job!');
  }
}

/**
 * Generate CSV report
 */
function generateCSV() {
  let csv = 'File,Line,Issue Type,Current,Suggested Fix,Context\n';
  
  // Add missing dark mode issues
  Object.entries(results.missingDarkMode).forEach(([file, issues]) => {
    issues.forEach(issue => {
      csv += `"${file}",${issue.line},"Missing Dark Mode","${issue.class}","${issue.suggestedDark}","${issue.context}"\n`;
    });
  });
  
  // Add hardcoded color issues
  Object.entries(results.hardcodedColors).forEach(([file, issues]) => {
    issues.forEach(issue => {
      csv += `"${file}",${issue.line},"Hardcoded Color","${issue.color}","Use Tailwind class or CSS variable","${issue.context.replace(/"/g, '""')}"\n`;
    });
  });
  
  // Add suspicious pattern issues
  Object.entries(results.suspiciousPatterns).forEach(([file, issues]) => {
    issues.forEach(issue => {
      csv += `"${file}",${issue.line},"Suspicious Pattern","Inline style","Use className with dark mode support","${issue.context.replace(/"/g, '""')}"\n`;
    });
  });
  
  return csv;
}

// Main execution
const targetPath = process.argv[2] || 'src/frontend/components';
const resolvedPath = path.resolve(process.cwd(), targetPath);

console.log(`🔍 Scanning for dark mode issues in: ${resolvedPath}`);
console.log(`   This may take a moment...\n`);

if (!fs.existsSync(resolvedPath)) {
  console.error(`❌ Path does not exist: ${resolvedPath}`);
  process.exit(1);
}

const stat = fs.statSync(resolvedPath);
if (stat.isFile()) {
  scanFile(resolvedPath);
} else {
  scanDirectory(resolvedPath);
}

generateReport();