import { Request, Response } from 'express';
import { LeadsRepository } from '../repositories/leads-repository';
import { CompanyRepository } from '../repositories/company-repository';
import { HarvestClient } from '../integrations/harvest';
import { RadarCompanyUpdate } from '../../types/company-types';
import { RadarState, CompanyPriority } from '../../types/shared-types';

/**
 * Controller for Leads-related endpoints
 */
export class LeadsController {
  private leadsRepository: LeadsRepository;
  private companyRepository: CompanyRepository;
  private harvestClient: HarvestClient;

  constructor(leadsRepository: LeadsRepository, harvestClient: HarvestClient) {
    this.leadsRepository = leadsRepository;
    this.companyRepository = new CompanyRepository();
    this.harvestClient = harvestClient;

    // Bind methods to ensure 'this' context
    this.getRadarCompanies = this.getRadarCompanies.bind(this);
    this.updateRadarCompany = this.updateRadarCompany.bind(this);
    this.getHarvestCompanies = this.getHarvestCompanies.bind(this);
    this.addCompanyToRadar = this.addCompanyToRadar.bind(this);
    this.removeCompanyFromRadar = this.removeCompanyFromRadar.bind(this);
  }

  /**
   * Get all companies with radar information
   */
  async getRadarCompanies(req: Request, res: Response) {
    try {
      const companies = this.leadsRepository.getRadarCompanies();
      res.json({ success: true, data: companies });
    } catch (error) {
      console.error('Error getting radar companies:', error);
      res.status(500).json({ success: false, message: 'Failed to get radar companies' });
    }
  }

  /**
   * Update a company's radar information
   */
  async updateRadarCompany(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updateData: RadarCompanyUpdate = req.body;

      // Use the shared validation utility for consistency
      const dataValidation = await import('../../utils/data-validation');
      const validation = dataValidation.validateCompanyRadarFields(updateData);

      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid company data',
          errors: validation.errors
        });
      }

      // Record the user who made the update
      const updater = req.session?.user?.email || 'system';

      // Update the company using the unified model
      const company = this.leadsRepository.updateRadarCompany(id, {
        ...updateData,
        updatedBy: updater
      });

      if (!company) {
        return res.status(404).json({ success: false, message: 'Company not found' });
      }

      // Log the radar update activity
      try {
        const activityLogger = await import('../../utils/activity-logger');
        await activityLogger.default.logCompanyRadarChanged(
          id,
          company.name || 'Unknown Company',
          updateData,
          updater
        );
      } catch (logError) {
        console.warn('Failed to log radar update activity:', logError);
        // Don't fail the request if logging fails
      }

      res.json({ success: true, data: company });
    } catch (error) {
      console.error('Error updating radar company:', error);
      res.status(500).json({ success: false, message: 'Failed to update radar company' });
    }
  }

  /**
   * Get companies from Harvest API
   */
  async getHarvestCompanies(_req: Request, res: Response) {
    try {
      const companies = await this.harvestClient.getClients();
      res.json({ success: true, data: companies });
    } catch (error) {
      console.error('Error getting Harvest companies:', error);
      res.status(500).json({ success: false, message: 'Failed to get Harvest companies' });
    }
  }

  /**
   * Add a company to the radar
   */
  async addCompanyToRadar(req: Request, res: Response) {
    try {
      console.log('Adding company to radar, request body:', req.body);
      const { companyId, radarState, priority } = req.body;

      // Validate required parameters
      if (!companyId) {
        return res.status(400).json({ success: false, message: 'Missing required field: companyId' });
      }

      if (!radarState) {
        return res.status(400).json({ success: false, message: 'Missing required field: radarState' });
      }

      // Validate radar state
      if (!['Strategy', 'Transformation', 'BAU', 'Transition out'].includes(radarState)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid radar state. Must be one of: Strategy, Transformation, BAU, Transition out'
        });
      }

      // Validate priority if provided
      if (priority && !['High', 'Medium', 'Low', 'Qualified out'].includes(priority)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid priority. Must be one of: High, Medium, Low, Qualified out'
        });
      }

      console.log(`Looking for existing company with ID: ${companyId}`);

      // Get existing company from our database
      const existingCompany = this.companyRepository.getCompanyById(companyId);

      if (!existingCompany) {
        console.log(`Company with ID ${companyId} not found in database`);
        return res.status(404).json({ success: false, message: 'Company not found' });
      }

      console.log(`Found existing company: ${existingCompany.name} (ID: ${existingCompany.id})`);

      // Update the existing company with radar information
      const updatedCompany = this.companyRepository.updateCompany(companyId, {
        radarState: radarState as RadarState,
        priority: priority as CompanyPriority,
        updatedBy: req.session?.user?.email || 'system'
      });

      if (!updatedCompany) {
        return res.status(500).json({ success: false, message: 'Failed to update company radar information' });
      }

      // Log the radar addition activity
      try {
        const activityLogger = await import('../../utils/activity-logger');
        await activityLogger.default.logCompanyRadarChanged(
          companyId,
          updatedCompany.name || 'Unknown Company',
          { radarState, priority, action: 'added_to_radar' },
          req.session?.user?.email || 'system'
        );
      } catch (logError) {
        console.warn('Failed to log radar addition activity:', logError);
        // Don't fail the request if logging fails
      }

      console.log(`Successfully added company to radar: ${updatedCompany.name}`);
      res.json({ success: true, data: updatedCompany });
    } catch (error) {
      console.error('Error adding company to radar:', error);
      res.status(500).json({ success: false, message: 'Failed to add company to radar' });
    }
  }

  /**
   * Remove a company from the radar
   */
  async removeCompanyFromRadar(req: Request, res: Response) {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({ success: false, message: 'Missing required parameter: id' });
      }

      // Get the company first to check if it exists (use company repository to find any company)
      const company = this.companyRepository.getCompanyById(id);
      if (!company) {
        return res.status(404).json({ success: false, message: `Company with ID ${id} not found` });
      }

      // Check if company is actually on the radar
      if (!company.radarState) {
        return res.status(400).json({ success: false, message: 'Company is not currently on the radar' });
      }

      // Update the company to remove radar information using company repository
      const updatedCompany = this.companyRepository.updateCompany(id, {
        radarState: null,
        priority: null,
        updatedBy: req.session?.user?.email || 'system'
      });

      if (!updatedCompany) {
        return res.status(500).json({ success: false, message: 'Failed to remove company from radar' });
      }

      // Log the radar removal activity
      try {
        const activityLogger = await import('../../utils/activity-logger');
        await activityLogger.default.logCompanyRadarChanged(
          id,
          company.name || 'Unknown Company',
          { action: 'removed_from_radar', previousState: company.radarState, previousPriority: company.priority },
          req.session?.user?.email || 'system'
        );
      } catch (logError) {
        console.warn('Failed to log radar removal activity:', logError);
        // Don't fail the request if logging fails
      }

      res.json({ success: true, message: 'Company removed from radar' });
    } catch (error) {
      console.error('Error removing company from radar:', error);
      res.status(500).json({ success: false, message: 'Failed to remove company from radar' });
    }
  }
}
