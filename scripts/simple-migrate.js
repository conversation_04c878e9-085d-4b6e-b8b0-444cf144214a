#!/usr/bin/env node

/**
 * Simple SQL-based Migration Runner
 * 
 * This replaces the complex TypeScript migration system with a simple
 * SQL file-based approach that's reliable in production.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get database path
function getDatabasePath() {
  if (process.env.NODE_ENV === 'production') {
    return '/data/upstream.db';
  } else {
    return path.join(__dirname, '..', 'data', 'upstream.db');
  }
}

// Ensure data directory exists
function ensureDataDirectory() {
  const dbPath = getDatabasePath();
  const dataDir = path.dirname(dbPath);
  
  if (!fs.existsSync(dataDir)) {
    console.log(`Creating data directory: ${dataDir}`);
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Get all migration files
function getMigrationFiles() {
  // Try different possible paths for migrations directory
  const possiblePaths = [
    path.join(__dirname, '..', 'migrations'),        // Development: /scripts/../migrations
    path.join(__dirname, '..', '..', 'migrations'),  // Production: /src/scripts/../../migrations  
    path.join(process.cwd(), 'migrations')           // Fallback: project root
  ];
  
  let migrationsDir = null;
  for (const migrationPath of possiblePaths) {
    if (fs.existsSync(migrationPath)) {
      migrationsDir = migrationPath;
      break;
    }
  }
  
  if (!migrationsDir) {
    console.log('No migrations directory found. Checked paths:', possiblePaths);
    return [];
  }
  
  console.log(`Using migrations directory: ${migrationsDir}`);
  
  return fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort(); // Ensure they run in order
}

// Initialize database and run migrations
function runMigrations() {
  ensureDataDirectory();
  const dbPath = getDatabasePath();
  
  console.log(`Connecting to database: ${dbPath}`);
  const db = new BetterSqlite3(dbPath);
  
  try {
    // Enable foreign keys and set up WAL mode
    db.pragma('foreign_keys = ON');
    db.pragma('journal_mode = WAL');
    
    // Ensure migrations table exists
    db.exec(`
      CREATE TABLE IF NOT EXISTS migrations (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        executed_at TEXT NOT NULL DEFAULT (datetime('now')),
        batch INTEGER NOT NULL DEFAULT 1
      )
    `);
    
    // Get executed migrations
    const executedMigrations = new Set(
      db.prepare('SELECT id FROM migrations').all().map(row => row.id)
    );
    
    // Get migration files
    const migrationFiles = getMigrationFiles();
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found');
      return;
    }
    
    let executedCount = 0;
    
    // Run each migration
    for (const filename of migrationFiles) {
      // Extract migration ID from filename (e.g., "001_initial_schema.sql" -> "001")
      const migrationId = filename.split('_')[0];
      
      if (executedMigrations.has(migrationId)) {
        console.log(`⏭️  Skipping migration ${migrationId} (already executed)`);
        continue;
      }
      
      console.log(`🔄 Running migration ${migrationId}: ${filename}`);
      
      // Read and execute migration file - find the correct migrations directory first
      const possiblePaths = [
        path.join(__dirname, '..', 'migrations'),        // Development: /scripts/../migrations
        path.join(__dirname, '..', '..', 'migrations'),  // Production: /src/scripts/../../migrations  
        path.join(process.cwd(), 'migrations')           // Fallback: project root
      ];
      
      let migrationsDir = null;
      for (const migrationPath of possiblePaths) {
        if (fs.existsSync(migrationPath)) {
          migrationsDir = migrationPath;
          break;
        }
      }
      
      const migrationPath = path.join(migrationsDir, filename);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      
      try {
        // Execute the migration in a transaction
        db.transaction(() => {
          db.exec(migrationSQL);
          
          // Record migration execution (if not already recorded by the SQL file)
          const migrationName = filename.replace('.sql', '');
          db.prepare(`
            INSERT OR IGNORE INTO migrations (id, name) 
            VALUES (?, ?)
          `).run(migrationId, migrationName);
        })();
        
        console.log(`✅ Migration ${migrationId} completed successfully`);
        executedCount++;
        
      } catch (error) {
        console.error(`❌ Migration ${migrationId} failed:`, error.message);
        throw error;
      }
    }
    
    if (executedCount === 0) {
      console.log('✅ All migrations are up to date');
    } else {
      console.log(`✅ Successfully executed ${executedCount} migrations`);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    db.close();
  }
}

// CLI interface
function main() {
  const command = process.argv[2] || 'migrate';
  
  try {
    switch (command) {
      case 'migrate':
      case 'up':
        runMigrations();
        break;
        
      case 'status':
        showMigrationStatus();
        break;
        
      default:
        console.log('Usage:');
        console.log('  node scripts/simple-migrate.js [command]');
        console.log('');
        console.log('Commands:');
        console.log('  migrate, up     Run pending migrations');
        console.log('  status          Show migration status');
        process.exit(1);
    }
  } catch (error) {
    console.error('Migration command failed:', error.message);
    process.exit(1);
  }
}

// Show migration status
function showMigrationStatus() {
  ensureDataDirectory();
  const dbPath = getDatabasePath();
  const db = new BetterSqlite3(dbPath);
  
  try {
    // Check if migrations table exists
    const tableExists = db.prepare(`
      SELECT 1 FROM sqlite_master 
      WHERE type='table' AND name='migrations'
    `).get();
    
    if (!tableExists) {
      console.log('❌ Migrations table does not exist. Run migrations first.');
      return;
    }
    
    const executedMigrations = db.prepare('SELECT * FROM migrations ORDER BY id').all();
    const migrationFiles = getMigrationFiles();
    
    console.log('Migration Status:');
    console.log('================');
    
    if (executedMigrations.length > 0) {
      console.log('\n✅ Executed migrations:');
      executedMigrations.forEach(migration => {
        console.log(`  - ${migration.id}: ${migration.name} (${migration.executed_at})`);
      });
    }
    
    const executedIds = new Set(executedMigrations.map(m => m.id));
    const pendingFiles = migrationFiles.filter(file => {
      const migrationId = file.split('_')[0];
      return !executedIds.has(migrationId);
    });
    
    if (pendingFiles.length > 0) {
      console.log('\n⏳ Pending migrations:');
      pendingFiles.forEach(file => {
        const migrationId = file.split('_')[0];
        console.log(`  - ${migrationId}: ${file}`);
      });
    }
    
    if (executedMigrations.length === 0 && migrationFiles.length === 0) {
      console.log('No migrations found');
    }
    
  } finally {
    db.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { runMigrations, showMigrationStatus };