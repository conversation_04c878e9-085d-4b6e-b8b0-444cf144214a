import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { useFloatingPanels } from "../../contexts/FloatingPanelsContext";
import FeedbackButton from "../common/FeedbackButton";
import UpdateNotification from "./UpdateNotification";
import { AIChat } from "../AIChat";
import { XeroChat } from "../XeroChat";

/**
 * Mobile Floating Action Button (FAB) Component
 *
 * Consolidates multiple floating buttons into a single expandable FAB
 * for better mobile experience. Implements material design patterns.
 */
export const MobileFAB: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const { feedbackExpanded, expandFeedback, expandAiChat, expandUpdate } =
    useFloatingPanels();

  // Hide FAB on scroll down, show on scroll up
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Scrolling down
        setIsVisible(false);
        setIsExpanded(false);
      } else {
        // Scrolling up
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  // Close expanded state when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest(".mobile-fab-container")) {
        setIsExpanded(false);
      }
    };

    if (isExpanded) {
      document.addEventListener("click", handleClickOutside);
      return () => document.removeEventListener("click", handleClickOutside);
    }
  }, [isExpanded]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleActionClick = (action: "feedback" | "update" | "ai") => {
    setIsExpanded(false);
    switch (action) {
      case "feedback":
        expandFeedback();
        break;
      case "update":
        expandUpdate();
        break;
      case "ai":
        expandAiChat();
        break;
    }
  };

  return createPortal(
    <div
      className={`mobile-fab-container fixed bottom-20 right-4 z-40 transition-all duration-300 ${
        isVisible ? "translate-y-0" : "translate-y-32"
      } md:hidden`}
    >
      {/* Sub-action buttons */}
      <div
        className={`flex flex-col items-end gap-3 mb-3 transition-all duration-300 ${
          isExpanded
            ? "opacity-100 scale-100"
            : "opacity-0 scale-95 pointer-events-none"
        }`}
      >
        {/* Feedback Button */}
        <button
          onClick={() => handleActionClick("feedback")}
          className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-full shadow-lg 
                     border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-200"
          aria-label="Report Bug or Request Feature"
        >
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Feedback
          </span>
          <div className="w-8 h-8 rounded-full bg-red-500 flex items-center justify-center">
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </button>

        {/* Updates Button */}
        <button
          onClick={() => handleActionClick("update")}
          className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-full shadow-lg 
                     border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-200"
          aria-label="View Updates"
        >
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Updates
          </span>
          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </button>

        {/* AI Assistant Button */}
        <button
          onClick={() => handleActionClick("ai")}
          className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-full shadow-lg 
                     border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-200"
          aria-label="AI Assistant"
        >
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            AI Assistant
          </span>
          <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
              />
            </svg>
          </div>
        </button>
      </div>

      {/* Main FAB button */}
      <button
        onClick={toggleExpanded}
        className={`w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center ${
          isExpanded ? "rotate-45" : ""
        }`}
        style={{
          backgroundColor: "var(--color-primary)",
          color: "var(--color-bg)",
        }}
        onMouseEnter={(e) =>
          (e.currentTarget.style.backgroundColor = "var(--color-primary-hover)")
        }
        onMouseLeave={(e) =>
          (e.currentTarget.style.backgroundColor = "var(--color-primary)")
        }
        aria-label="Toggle Actions Menu"
        aria-expanded={isExpanded}
      >
        <svg
          className="w-6 h-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
      </button>

      {/* Backdrop */}
      {isExpanded && (
        <div className="fixed inset-0 bg-black bg-opacity-20 -z-10" />
      )}
    </div>,
    document.body
  );
};

export default MobileFAB;
