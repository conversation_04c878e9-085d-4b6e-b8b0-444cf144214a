import { fetchFrom<PERSON>pi } from './utils';
import { HubSpotImport } from '../types/crm-types';

/**
 * Check if HubSpot integration is configured
 */
export const checkHubSpotStatus = async (): Promise<{ isConfigured: boolean }> => {
  const response = await fetchFromApi('/api/hubspot/status');
  return response.data;
};

// Configuration methods have been removed.
// HubSpot integration is now configured via HUBSPOT_ACCESS_TOKEN environment variable.

/**
 * Import deals from HubSpot
 */
export const importDealsFromHubSpot = async (): Promise<{ count: number }> => {
  const response = await fetchFromApi('/api/hubspot/import/deals', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Import contacts from HubSpot
 */
export const importContactsFromHubSpot = async (): Promise<{ count: number }> => {
  const response = await fetchFrom<PERSON><PERSON>('/api/hubspot/import/contacts', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Import companies from HubSpot
 */
export const importCompaniesFromHubSpot = async (): Promise<{ count: number }> => {
  const response = await fetchFromApi('/api/hubspot/import/companies', {
    method: 'POST'
  });
  return response.data;
};

/**
 * Import all data from HubSpot (companies, deals, contacts)
 */
export const importAllFromHubSpot = async (): Promise<{
  totalCount: number;
  results: {
    companies: { success: boolean; count: number; error?: string };
    deals: { success: boolean; count: number; error?: string };
    contacts: { success: boolean; count: number; error?: string };
    notes?: { success: boolean; count: number; error?: string };
    associations?: { success: boolean; count: number; error?: string };
  };
}> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: {
      totalCount: number;
      results: {
        companies: { success: boolean; count: number; error?: string };
        deals: { success: boolean; count: number; error?: string };
        contacts: { success: boolean; count: number; error?: string };
        notes?: { success: boolean; count: number; error?: string };
        associations?: { success: boolean; count: number; error?: string };
      };
    };
  }>('/api/hubspot/import/all', {
    method: 'POST',
    timeout: 900000 // 15 minutes timeout for import
  });
  return response.data;
};

/**
 * Quick import from HubSpot (companies, deals, contacts only - no notes/associations)
 */
export const importQuickFromHubSpot = async (): Promise<{
  totalCount: number;
  results: {
    companies: { success: boolean; count: number; error?: string };
    deals: { success: boolean; count: number; error?: string };
    contacts: { success: boolean; count: number; error?: string };
  };
}> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: {
      totalCount: number;
      results: {
        companies: { success: boolean; count: number; error?: string };
        deals: { success: boolean; count: number; error?: string };
        contacts: { success: boolean; count: number; error?: string };
      };
    };
  }>('/api/hubspot/import/quick', {
    method: 'POST',
    timeout: 300000 // 5 minutes timeout for quick import
  });
  return response.data;
};

/**
 * Get import history
 */
export const getHubSpotImportHistory = async (): Promise<HubSpotImport[]> => {
  const response = await fetchFromApi('/api/hubspot/import/history');
  return response.data;
};

/**
 * Get all companies from HubSpot for linking purposes
 */
export const getHubSpotCompaniesForLinking = async (): Promise<Array<{
  id: string;
  name: string;
  industry?: string;
  website?: string;
}>> => {
  const response = await fetchFromApi<{
    success: boolean;
    data: Array<{
      id: string;
      name: string;
      industry?: string;
      website?: string;
    }>;
  }>('/api/hubspot/companies');
  return response.data;
};

