import { jest } from '@jest/globals';
import RepeatingBillService from '../../../src/services/xero/repeating-bill-service';
import XeroService from '../../../src/services/xero/xero-service';

// Mock dependencies
jest.mock('../../../src/services/xero/xero-service');
jest.mock('../../../src/utils/backend-logger');

describe('RepeatingBillService', () => {
  let service: RepeatingBillService;
  let mockXeroService: jest.Mocked<XeroService>;

  beforeEach(() => {
    mockXeroService = {
      makeAuthenticatedRequest: jest.fn(),
    } as any;
    
    (XeroService as jest.Mock).mockImplementation(() => mockXeroService);
    service = new RepeatingBillService();
    jest.clearAllMocks();
  });

  describe('getRepeatingBills', () => {
    it('should fetch and transform repeating bills', async () => {
      const mockXeroResponse = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'bill-123',
            Type: 'ACCPAY',
            Contact: {
              ContactID: 'contact-123',
              Name: 'Supplier A',
            },
            Schedule: {
              Period: 1,
              Unit: 'MONTHLY',
              DueDate: 15,
              NextScheduledDate: '2024-02-15',
            },
            LineAmountTypes: 'Exclusive',
            LineItems: [
              {
                Description: 'Monthly service',
                Quantity: 1,
                UnitAmount: 1000,
                TaxType: 'GST',
                LineAmount: 1000,
              },
            ],
            SubTotal: 1000,
            TotalTax: 100,
            Total: 1100,
            Status: 'AUTHORISED',
            Reference: 'MONTHLY-SERVICE',
          },
          {
            RepeatingInvoiceID: 'bill-456',
            Type: 'ACCPAY',
            Contact: {
              ContactID: 'contact-456',
              Name: 'Supplier B',
            },
            Schedule: {
              Period: 3,
              Unit: 'MONTHLY',
              DueDate: 1,
              NextScheduledDate: '2024-03-01',
            },
            LineAmountTypes: 'Inclusive',
            LineItems: [
              {
                Description: 'Quarterly license',
                Quantity: 1,
                UnitAmount: 3000,
                LineAmount: 3000,
              },
            ],
            SubTotal: 2727.27,
            TotalTax: 272.73,
            Total: 3000,
            Status: 'AUTHORISED',
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockXeroResponse);

      const result = await service.getRepeatingBills();

      expect(mockXeroService.makeAuthenticatedRequest).toHaveBeenCalledWith(
        'GET',
        '/repeatinginvoices',
        { where: 'Type=="ACCPAY"' }
      );

      expect(result).toEqual([
        {
          id: 'bill-123',
          supplier: 'Supplier A',
          supplierId: 'contact-123',
          amount: 1100,
          taxAmount: 100,
          frequency: 'monthly',
          nextDueDate: '2024-02-15',
          dayOfMonth: 15,
          status: 'active',
          reference: 'MONTHLY-SERVICE',
          description: 'Monthly service',
        },
        {
          id: 'bill-456',
          supplier: 'Supplier B',
          supplierId: 'contact-456',
          amount: 3000,
          taxAmount: 272.73,
          frequency: 'quarterly',
          nextDueDate: '2024-03-01',
          dayOfMonth: 1,
          status: 'active',
          reference: '',
          description: 'Quarterly license',
        },
      ]);
    });

    it('should handle empty response', async () => {
      mockXeroService.makeAuthenticatedRequest.mockResolvedValue({
        RepeatingInvoices: [],
      });

      const result = await service.getRepeatingBills();

      expect(result).toEqual([]);
    });

    it('should filter out non-ACCPAY types', async () => {
      const mockXeroResponse = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'inv-123',
            Type: 'ACCREC',
            Contact: { Name: 'Customer A' },
            Total: 1000,
          },
          {
            RepeatingInvoiceID: 'bill-456',
            Type: 'ACCPAY',
            Contact: { Name: 'Supplier B' },
            Total: 2000,
            Schedule: { NextScheduledDate: '2024-02-01' },
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockXeroResponse);

      const result = await service.getRepeatingBills();

      expect(result).toHaveLength(1);
      expect(result[0].supplier).toBe('Supplier B');
    });
  });

  describe('getUpcomingBills', () => {
    it('should project upcoming bills for specified period', async () => {
      const mockRepeatingBills = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'bill-123',
            Type: 'ACCPAY',
            Contact: { Name: 'Monthly Supplier' },
            Schedule: {
              Period: 1,
              Unit: 'MONTHLY',
              DueDate: 15,
              NextScheduledDate: '2024-01-15',
            },
            Total: 1000,
            Status: 'AUTHORISED',
          },
          {
            RepeatingInvoiceID: 'bill-456',
            Type: 'ACCPAY',
            Contact: { Name: 'Weekly Supplier' },
            Schedule: {
              Period: 1,
              Unit: 'WEEKLY',
              DueDate: 5,
              NextScheduledDate: '2024-01-05',
            },
            Total: 250,
            Status: 'AUTHORISED',
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockRepeatingBills);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.getUpcomingBills(30);

      expect(result).toEqual([
        {
          date: '2024-01-05',
          bills: [
            {
              id: 'bill-456',
              supplier: 'Weekly Supplier',
              amount: 250,
              frequency: 'weekly',
            },
          ],
          totalAmount: 250,
        },
        {
          date: '2024-01-12',
          bills: [
            {
              id: 'bill-456',
              supplier: 'Weekly Supplier',
              amount: 250,
              frequency: 'weekly',
            },
          ],
          totalAmount: 250,
        },
        {
          date: '2024-01-15',
          bills: [
            {
              id: 'bill-123',
              supplier: 'Monthly Supplier',
              amount: 1000,
              frequency: 'monthly',
            },
          ],
          totalAmount: 1000,
        },
        {
          date: '2024-01-19',
          bills: [
            {
              id: 'bill-456',
              supplier: 'Weekly Supplier',
              amount: 250,
              frequency: 'weekly',
            },
          ],
          totalAmount: 250,
        },
        {
          date: '2024-01-26',
          bills: [
            {
              id: 'bill-456',
              supplier: 'Weekly Supplier',
              amount: 250,
              frequency: 'weekly',
            },
          ],
          totalAmount: 250,
        },
      ]);

      jest.useRealTimers();
    });

    it('should handle annual bills correctly', async () => {
      const mockRepeatingBills = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'bill-789',
            Type: 'ACCPAY',
            Contact: { Name: 'Annual Supplier' },
            Schedule: {
              Period: 1,
              Unit: 'YEARLY',
              NextScheduledDate: '2024-06-01',
            },
            Total: 12000,
            Status: 'AUTHORISED',
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockRepeatingBills);

      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-01'));

      const result = await service.getUpcomingBills(365);

      const annualBill = result.find(r => r.date === '2024-06-01');
      expect(annualBill).toBeDefined();
      expect(annualBill?.bills[0].amount).toBe(12000);

      jest.useRealTimers();
    });
  });

  describe('analyzeSpendBySupplier', () => {
    it('should analyze spend patterns by supplier', async () => {
      const mockRepeatingBills = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'bill-1',
            Type: 'ACCPAY',
            Contact: { 
              ContactID: 'supplier-1',
              Name: 'Supplier A',
            },
            Schedule: { Period: 1, Unit: 'MONTHLY' },
            Total: 1000,
            Status: 'AUTHORISED',
          },
          {
            RepeatingInvoiceID: 'bill-2',
            Type: 'ACCPAY',
            Contact: { 
              ContactID: 'supplier-1',
              Name: 'Supplier A',
            },
            Schedule: { Period: 1, Unit: 'WEEKLY' },
            Total: 250,
            Status: 'AUTHORISED',
          },
          {
            RepeatingInvoiceID: 'bill-3',
            Type: 'ACCPAY',
            Contact: { 
              ContactID: 'supplier-2',
              Name: 'Supplier B',
            },
            Schedule: { Period: 3, Unit: 'MONTHLY' },
            Total: 3000,
            Status: 'AUTHORISED',
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockRepeatingBills);

      const result = await service.analyzeSpendBySupplier();

      expect(result).toEqual({
        suppliers: [
          {
            supplierId: 'supplier-1',
            supplierName: 'Supplier A',
            monthlySpend: 2000,
            annualSpend: 24000,
            billCount: 2,
            bills: expect.any(Array),
          },
          {
            supplierId: 'supplier-2',
            supplierName: 'Supplier B',
            monthlySpend: 1000,
            annualSpend: 12000,
            billCount: 1,
            bills: expect.any(Array),
          },
        ],
        summary: {
          totalSuppliers: 2,
          totalMonthlySpend: 3000,
          totalAnnualSpend: 36000,
          averageMonthlyPerSupplier: 1500,
        },
      });
    });
  });

  describe('getRepeatingBillById', () => {
    it('should fetch a specific repeating bill', async () => {
      const mockResponse = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'bill-123',
            Type: 'ACCPAY',
            Contact: { Name: 'Supplier A' },
            Total: 1000,
            Schedule: {
              Period: 1,
              Unit: 'MONTHLY',
              NextScheduledDate: '2024-02-01',
            },
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockResponse);

      const result = await service.getRepeatingBillById('bill-123');

      expect(mockXeroService.makeAuthenticatedRequest).toHaveBeenCalledWith(
        'GET',
        '/repeatinginvoices/bill-123'
      );
      expect(result).toEqual({
        id: 'bill-123',
        supplier: 'Supplier A',
        amount: 1000,
        frequency: 'monthly',
        nextDueDate: '2024-02-01',
        dayOfMonth: undefined,
        status: 'active',
        reference: '',
        description: '',
        supplierId: undefined,
        taxAmount: 0,
      });
    });

    it('should return null for non-existent bill', async () => {
      mockXeroService.makeAuthenticatedRequest.mockResolvedValue({
        RepeatingInvoices: [],
      });

      const result = await service.getRepeatingBillById('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('createRepeatingBill', () => {
    it('should create a new repeating bill', async () => {
      const billData = {
        supplierId: 'supplier-123',
        description: 'Monthly Software License',
        amount: 99.99,
        taxType: 'GST',
        frequency: 'monthly' as const,
        startDate: '2024-02-01',
        dayOfMonth: 1,
      };

      const mockResponse = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'new-bill-123',
            Type: 'ACCPAY',
            Contact: { ContactID: 'supplier-123', Name: 'Software Co' },
            Total: 109.99,
            Schedule: {
              Period: 1,
              Unit: 'MONTHLY',
              StartDate: '2024-02-01',
              DueDate: 1,
            },
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockResponse);

      const result = await service.createRepeatingBill(billData);

      expect(mockXeroService.makeAuthenticatedRequest).toHaveBeenCalledWith(
        'POST',
        '/repeatinginvoices',
        undefined,
        expect.objectContaining({
          Type: 'ACCPAY',
          Contact: { ContactID: 'supplier-123' },
          Schedule: expect.objectContaining({
            Period: 1,
            Unit: 'MONTHLY',
            StartDate: '2024-02-01',
            DueDate: 1,
          }),
        })
      );
      expect(result.id).toBe('new-bill-123');
    });
  });

  describe('updateRepeatingBill', () => {
    it('should update an existing repeating bill', async () => {
      const updates = {
        amount: 150,
        status: 'paused' as const,
      };

      const mockResponse = {
        RepeatingInvoices: [
          {
            RepeatingInvoiceID: 'bill-123',
            Type: 'ACCPAY',
            Contact: { Name: 'Supplier A' },
            Total: 150,
            Status: 'DRAFT',
          },
        ],
      };

      mockXeroService.makeAuthenticatedRequest.mockResolvedValue(mockResponse);

      const result = await service.updateRepeatingBill('bill-123', updates);

      expect(mockXeroService.makeAuthenticatedRequest).toHaveBeenCalledWith(
        'POST',
        '/repeatinginvoices/bill-123',
        undefined,
        expect.objectContaining({
          Status: 'DRAFT',
          LineItems: expect.any(Array),
        })
      );
      expect(result.amount).toBe(150);
      expect(result.status).toBe('paused');
    });
  });

  describe('error handling', () => {
    it('should handle API errors gracefully', async () => {
      mockXeroService.makeAuthenticatedRequest.mockRejectedValue(
        new Error('Xero API Error')
      );

      await expect(service.getRepeatingBills()).rejects.toThrow('Xero API Error');
    });

    it('should handle malformed responses', async () => {
      mockXeroService.makeAuthenticatedRequest.mockResolvedValue({
        // Missing RepeatingInvoices property
      });

      const result = await service.getRepeatingBills();

      expect(result).toEqual([]);
    });
  });
});