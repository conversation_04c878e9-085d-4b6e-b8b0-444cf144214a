# Code Samples

## Programming Language Examples

Sample code for accessing the v2 API is available in multiple languages:

- [Java](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/HarvestAPISample.java)
- [C#](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.cs)
- [Go](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.go)
- [JavaScript](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.js)
- [PHP](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.php)
- [PowerShell](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.ps1)
- [Python](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.py)
- [Ruby](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.rb)
- [Visual Basic](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.vbs)
- [Google Apps Script](https://github.com/harvesthq/harvest_api_samples/blob/master/v2/harvest_api_sample.gs)

## OAuth Sample

A live demo of the OAuth implicit grant flow from a client-side application is available at:
[https://harvesthq.github.io/harvest_api_samples/](https://harvesthq.github.io/harvest_api_samples/)

The [source code](https://github.com/harvesthq/harvest_api_samples/tree/master/v2/oauth) for the OAuth demo is also available.