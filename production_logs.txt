2025-06-10T01:42:47.452769296Z ==> Cloning from https://github.com/adrian-dotco/onbord-financial-dashboard
2025-06-10T01:42:49.333638315Z ==> Checking out commit 3105260e6f7afb1e6b6444a791e09830948d21a9 in branch main
2025-06-10T01:43:37.056763834Z ==> Using Node.js version 18.17.1 via /opt/render/project/src/.node-version
2025-06-10T01:43:37.082855431Z ==> Node.js version 18.17.1 has reached end-of-life.
2025-06-10T01:43:37.082874002Z ==> Upgrade to a maintained version to receive important security updates.
2025-06-10T01:43:37.082884092Z ==> Information on maintained Node.js versions: https://nodejs.org/en/about/previous-releases
2025-06-10T01:43:37.082888952Z ==> Docs on specifying a Node.js version: https://render.com/docs/node-version
2025-06-10T01:43:38.861278904Z ==> Using Bun version 1.1.0 (default)
2025-06-10T01:43:38.861297644Z ==> Docs on specifying a bun version: https://render.com/docs/bun-version
2025-06-10T01:43:38.915741298Z ==> Running build command 'npm ci --legacy-peer-deps --include=dev && npm run build'...
2025-06-10T01:44:30.824040753Z 
2025-06-10T01:44:30.824069273Z > onbord-financial-dashboard-preview@0.1.0 preinstall
2025-06-10T01:44:30.824088504Z > npm config set legacy-peer-deps true
2025-06-10T01:44:30.824092614Z 
2025-06-10T01:44:31.149023771Z 
2025-06-10T01:44:31.149046922Z added 1062 packages, and audited 1063 packages in 52s
2025-06-10T01:44:31.149221367Z 
2025-06-10T01:44:31.149245917Z 199 packages are looking for funding
2025-06-10T01:44:31.149254448Z   run `npm fund` for details
2025-06-10T01:44:31.150705956Z 
2025-06-10T01:44:31.150722227Z found 0 vulnerabilities
2025-06-10T01:44:31.455001823Z 
2025-06-10T01:44:31.455037774Z > onbord-financial-dashboard-preview@0.1.0 build
2025-06-10T01:44:31.455043914Z > npm run build:frontend && npm run build:backend
2025-06-10T01:44:31.455048134Z 
2025-06-10T01:44:31.754404268Z 
2025-06-10T01:44:31.754424709Z > onbord-financial-dashboard-preview@0.1.0 build:frontend
2025-06-10T01:44:31.754428109Z > vite build
2025-06-10T01:44:31.754430949Z 
2025-06-10T01:44:31.942760038Z The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
2025-06-10T01:44:31.951977605Z Building with API_URL: https://upstream.onbord.au/api in production mode
2025-06-10T01:44:31.974239509Z vite v6.3.5 building for production...
2025-06-10T01:44:32.297000348Z transforming...
2025-06-10T01:44:45.970946592Z ✓ 4686 modules transformed.
2025-06-10T01:44:46.61646851Z rendering chunks...
2025-06-10T01:44:46.791771032Z [esbuild css minify]
2025-06-10T01:44:46.791790203Z ▲ [WARNING] Expected identifier but found "84rem\\\\" [css-syntax-error]
2025-06-10T01:44:46.791806603Z 
2025-06-10T01:44:46.791809753Z     <stdin>:9412:36:
2025-06-10T01:44:46.791813153Z       9412 │   .max-w-6xl, .max-w-7xl, .max-w-\\[84rem\\] {
2025-06-10T01:44:46.791816183Z            ╵                                     ~~~~~~~
2025-06-10T01:44:46.791818853Z 
2025-06-10T01:44:46.791821563Z 
2025-06-10T01:44:47.329479211Z computing gzip size...
2025-06-10T01:44:47.382948339Z dist/index.html                                      3.09 kB │ gzip:   1.30 kB
2025-06-10T01:44:47.38298004Z dist/assets/index-ChHdbfbe.css                     246.79 kB │ gzip:  36.45 kB
2025-06-10T01:44:47.383234567Z dist/assets/PlusIcon-DcyYfeCY.js                     0.48 kB │ gzip:   0.35 kB
2025-06-10T01:44:47.383308969Z dist/assets/ClockIcon-zMV3b4D9.js                    0.50 kB │ gzip:   0.37 kB
2025-06-10T01:44:47.383358441Z dist/assets/CheckCircleIcon-CP3Uuf6-.js              0.52 kB │ gzip:   0.37 kB
2025-06-10T01:44:47.383380071Z dist/assets/ArrowTrendingUpIcon-CmUg09qQ.js          0.56 kB │ gzip:   0.40 kB
2025-06-10T01:44:47.383406092Z dist/assets/UserIcon-6jgvbQsa.js                     0.61 kB │ gzip:   0.42 kB
2025-06-10T01:44:47.383489384Z dist/assets/LinkIcon-BsU8lF03.js                     0.63 kB │ gzip:   0.40 kB
2025-06-10T01:44:47.383528065Z dist/assets/ExclamationTriangleIcon-C5lIC1Yc.js      0.63 kB │ gzip:   0.45 kB
2025-06-10T01:44:47.383540035Z dist/assets/CurrencyDollarIcon-BABfCB7W.js           0.69 kB │ gzip:   0.47 kB
2025-06-10T01:44:47.383596427Z dist/assets/LightBulbIcon-BxLgafpZ.js                0.70 kB │ gzip:   0.47 kB
2025-06-10T01:44:47.383629638Z dist/assets/ArchiveBoxIcon-Db9dPSN5.js               0.71 kB │ gzip:   0.45 kB
2025-06-10T01:44:47.383647288Z dist/assets/DocumentTextIcon-CGIQLQN7.js             0.74 kB │ gzip:   0.47 kB
2025-06-10T01:44:47.383676839Z dist/assets/BuildingOffice2Icon-CeevT43c.js          0.75 kB │ gzip:   0.46 kB
2025-06-10T01:44:47.3837208Z dist/assets/ChartBarIcon-C4dM7KOb.js                 0.90 kB │ gzip:   0.50 kB
2025-06-10T01:44:47.38372683Z dist/assets/ChevronRightIcon-CSmXWsYL.js             0.92 kB │ gzip:   0.39 kB
2025-06-10T01:44:47.38372998Z dist/assets/UserGroupIcon-C2zI9wFx.js                0.97 kB │ gzip:   0.57 kB
2025-06-10T01:44:47.38373706Z dist/assets/hubspot-B8jswuYT.js                      1.00 kB │ gzip:   0.56 kB
2025-06-10T01:44:47.383739431Z dist/assets/CalendarDaysIcon-BlOatql3.js             1.01 kB │ gzip:   0.51 kB
2025-06-10T01:44:47.383772141Z dist/assets/BriefcaseIcon-Yk32uPVS.js                1.04 kB │ gzip:   0.63 kB
2025-06-10T01:44:47.383775301Z dist/assets/SparklesIcon-CqLOHgB0.js                 1.07 kB │ gzip:   0.56 kB
2025-06-10T01:44:47.383783752Z dist/assets/leads-AS3U8yBF.js                        1.12 kB │ gzip:   0.54 kB
2025-06-10T01:44:47.383791582Z dist/assets/QuestionMarkCircleIcon-BorfLwIQ.js       1.17 kB │ gzip:   0.53 kB
2025-06-10T01:44:47.384065529Z dist/assets/CalendarIcon-jp7FeKHJ.js                 1.27 kB │ gzip:   0.53 kB
2025-06-10T01:44:47.384070229Z dist/assets/UserCircleIcon-DRG9Qq0T.js               2.08 kB │ gzip:   0.74 kB
2025-06-10T01:44:47.384121771Z dist/assets/CommandPalette-Coe-my1E.js               3.78 kB │ gzip:   1.50 kB
2025-06-10T01:44:47.384143291Z dist/assets/ContactsList-BCsBqGQD.js                 5.51 kB │ gzip:   2.17 kB
2025-06-10T01:44:47.384180022Z dist/assets/CompaniesList-Bn2f9mdo.js                6.48 kB │ gzip:   2.37 kB
2025-06-10T01:44:47.384189633Z dist/assets/EstimateLinkModal-D1MiO3S_.js            9.01 kB │ gzip:   2.86 kB
2025-06-10T01:44:47.384234274Z dist/assets/ContactCompanyLinker-CIFCQaXV.js        14.24 kB │ gzip:   4.32 kB
2025-06-10T01:44:47.384238104Z dist/assets/DataManagementPage-EYNEpaAl.js          17.38 kB │ gzip:   4.60 kB
2025-06-10T01:44:47.384270625Z dist/assets/TenderPipeline-CMruNouz.js              20.12 kB │ gzip:   4.95 kB
2025-06-10T01:44:47.384274455Z dist/assets/CRMLayout-ciOu32TQ.js                   21.72 kB │ gzip:   5.76 kB
2025-06-10T01:44:47.384299955Z dist/assets/ContactDetail-CRKzQ_q0.js               22.29 kB │ gzip:   4.65 kB
2025-06-10T01:44:47.384303726Z dist/assets/EnhancedDealBoard-CdLS8WsT.js           35.65 kB │ gzip:   9.20 kB
2025-06-10T01:44:47.384335327Z dist/assets/CompanyDetail-DwmgkEwV.js               41.02 kB │ gzip:   7.75 kB
2025-06-10T01:44:47.384338837Z dist/assets/index-uM676NJi.js                       48.69 kB │ gzip:  13.11 kB
2025-06-10T01:44:47.384348257Z dist/assets/DirectoryPage-CUGu6l9B.js               51.48 kB │ gzip:  13.07 kB
2025-06-10T01:44:47.384353387Z dist/assets/HubSpotIntegration-BmOPJ58x.js          74.84 kB │ gzip:  19.38 kB
2025-06-10T01:44:47.384367017Z dist/assets/IntelligencePage-Bw_bNKKG.js            75.43 kB │ gzip:  17.44 kB
2025-06-10T01:44:47.384402078Z dist/assets/DealEditPage-7YFITrax.js                91.19 kB │ gzip:  18.55 kB
2025-06-10T01:44:47.384417439Z dist/assets/react-force-graph-2d-Cmttbs3G.js       176.78 kB │ gzip:  56.94 kB
2025-06-10T01:44:47.38446329Z dist/assets/index-DQ1wPe8g.js                    1,480.54 kB │ gzip: 374.16 kB
2025-06-10T01:44:47.384574953Z 
2025-06-10T01:44:47.384582513Z (!) Some chunks are larger than 500 kB after minification. Consider:
2025-06-10T01:44:47.384586263Z - Using dynamic import() to code-split the application
2025-06-10T01:44:47.384590113Z - Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
2025-06-10T01:44:47.384593433Z - Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
2025-06-10T01:44:47.384907472Z ✓ built in 15.38s
2025-06-10T01:44:47.803185792Z 
2025-06-10T01:44:47.803206792Z > onbord-financial-dashboard-preview@0.1.0 build:backend
2025-06-10T01:44:47.803211302Z > tsc --project tsconfig.backend.json --outDir dist --noEmitOnError false || echo 'TypeScript compilation failed, will use ts-node fallback'
2025-06-10T01:44:47.803214973Z 
2025-06-10T01:44:56.012183354Z src/api/clients/xero-api-client.ts(35,51): error TS2339: Property 'getAccessToken' does not exist on type 'XeroService'.
2025-06-10T01:44:56.01244159Z src/api/clients/xero-api-client.ts(54,33): error TS2339: Property 'refreshToken' does not exist on type 'XeroService'.
2025-06-10T01:44:56.012677177Z src/api/controllers/leads.ts(81,11): error TS2345: Argument of type 'RadarCompanyUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:44:56.012685957Z   Index signature for type 'string' is missing in type 'RadarCompanyUpdate'.
2025-06-10T01:44:56.012689827Z src/api/controllers/xero/auth.ts(430,9): error TS2322: Type 'Organisation' is not assignable to type 'XeroOrganization'.
2025-06-10T01:44:56.012692227Z   Index signature for type 'string' is missing in type 'Organisation'.
2025-06-10T01:44:56.012732328Z src/api/controllers/xero/reports.ts(211,58): error TS2345: Argument of type 'ReportWithRows' is not assignable to parameter of type 'XeroProfitAndLossReport'.
2025-06-10T01:44:56.012741149Z   Types of property 'reports' are incompatible.
2025-06-10T01:44:56.012745499Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:44:56.012749269Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:44:56.012772269Z src/api/controllers/xero/reports.ts(230,9): error TS2345: Argument of type 'ProjectSetting[]' is not assignable to parameter of type 'import("/opt/render/project/src/src/services/harvest/project-budget-service").ProjectSetting[]'.
2025-06-10T01:44:56.01278121Z   Type 'ProjectSetting' is missing the following properties from type 'ProjectSetting': projectId, invoiceFrequency, invoiceIntervalDays, paymentTerms
2025-06-10T01:44:56.01279374Z src/api/controllers/xero/reports.ts(487,42): error TS2551: Property 'rows' does not exist on type 'XeroReport'. Did you mean 'Rows'?
2025-06-10T01:44:56.01280308Z src/api/controllers/xero/reports.ts(517,50): error TS2551: Property 'value' does not exist on type 'XeroReportCell'. Did you mean 'Value'?
2025-06-10T01:44:56.012871152Z src/api/controllers/xero/reports.ts(550,59): error TS2551: Property 'value' does not exist on type 'XeroReportCell'. Did you mean 'Value'?
2025-06-10T01:44:56.013074527Z src/api/integrations/harvest.ts(399,26): error TS2339: Property 'cost_rate' does not exist on type 'HarvestUser'.
2025-06-10T01:44:56.013080018Z src/api/integrations/harvest.ts(402,26): error TS2339: Property 'cost_rate' does not exist on type 'HarvestUser'.
2025-06-10T01:44:56.013125809Z src/api/integrations/xero.ts(283,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to type 'XeroApiResponse<XeroBalanceSheetResponse>'.
2025-06-10T01:44:56.013144009Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:44:56.013148909Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:44:56.01315224Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:44:56.013204311Z src/api/integrations/xero.ts(366,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to type 'XeroApiResponse<XeroBankSummaryResponse>'.
2025-06-10T01:44:56.013209381Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:44:56.013211551Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:44:56.013213701Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:44:56.013283743Z src/api/integrations/xero.ts(421,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: RepeatingInvoices; }' is not assignable to type 'XeroApiResponse<XeroRepeatingInvoicesResponse>'.
2025-06-10T01:44:56.013290033Z   Types of property 'response' are incompatible.
2025-06-10T01:44:56.013293873Z     Property 'statusCode' is missing in type 'AxiosResponse<any, any>' but required in type '{ statusCode: number; headers: Record<string, string>; }'.
2025-06-10T01:44:56.013429647Z src/api/integrations/xero.ts(471,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: Invoices; }' is not assignable to type 'XeroApiResponse<XeroBillsResponse>'.
2025-06-10T01:44:56.013437517Z   Types of property 'response' are incompatible.
2025-06-10T01:44:56.013440487Z     Property 'statusCode' is missing in type 'AxiosResponse<any, any>' but required in type '{ statusCode: number; headers: Record<string, string>; }'.
2025-06-10T01:44:56.013442667Z src/api/integrations/xero.ts(775,9): error TS2322: Type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to type 'XeroApiResponse<XeroReportsResponse>'.
2025-06-10T01:44:56.013444857Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:44:56.013447087Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:44:56.013449188Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:44:56.013462718Z src/api/integrations/xero.ts(809,48): error TS2345: Argument of type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to parameter of type 'XeroApiResponse<XeroReportsResponse>'.
2025-06-10T01:44:56.013467698Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:44:56.013470988Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:44:56.013474438Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:44:56.013478368Z src/api/integrations/xero.ts(887,55): error TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.013483748Z   Type 'number' is not assignable to type 'string'.
2025-06-10T01:44:56.013491998Z src/api/integrations/xero.ts(888,41): error TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.013497069Z   Type 'number' is not assignable to type 'string'.
2025-06-10T01:44:56.013587851Z src/api/middleware/mock-auth.ts(12,11): error TS2430: Interface 'MockRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.013606352Z   Property 'session' is optional in type 'MockRequest' but required in type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.013610532Z src/api/repositories/activity-repository.ts(301,7): error TS2322: Type 'string' is not assignable to type 'ActivityEntityType'.
2025-06-10T01:44:56.013624742Z src/api/repositories/activity-repository.ts(400,7): error TS2322: Type 'string' is not assignable to type 'ActivityType'.
2025-06-10T01:44:56.013631662Z src/api/repositories/activity-repository.ts(403,7): error TS2322: Type 'string' is not assignable to type 'ActivityStatus'.
2025-06-10T01:44:56.013667563Z src/api/repositories/activity-repository.ts(404,7): error TS2322: Type 'string' is not assignable to type 'ActivityEntityType'.
2025-06-10T01:44:56.013783716Z src/api/repositories/activity-repository.ts(413,7): error TS2322: Type 'string' is not assignable to type 'ActivityImportance'.
2025-06-10T01:44:56.013786937Z src/api/repositories/activity-repository.ts(415,7): error TS2322: Type 'string' is not assignable to type 'ActivitySource'.
2025-06-10T01:44:56.013811597Z src/api/repositories/company-repository.ts(212,7): error TS2322: Type 'Contact[]' is not assignable to type 'number | Partial<BaseContact>[]'.
2025-06-10T01:44:56.013818967Z   Type 'Contact[]' is not assignable to type 'Partial<BaseContact>[]'.
2025-06-10T01:44:56.013822698Z     Type 'Contact' is not assignable to type 'Partial<BaseContact>'.
2025-06-10T01:44:56.013826158Z       Types of property 'company' are incompatible.
2025-06-10T01:44:56.013829598Z         Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:44:56.013832768Z src/api/repositories/company-repository.ts(291,7): error TS2322: Type 'Contact[]' is not assignable to type 'number | Partial<BaseContact>[]'.
2025-06-10T01:44:56.013836198Z   Type 'Contact[]' is not assignable to type 'Partial<BaseContact>[]'.
2025-06-10T01:44:56.013839668Z     Type 'Contact' is not assignable to type 'Partial<BaseContact>'.
2025-06-10T01:44:56.013843218Z       Types of property 'company' are incompatible.
2025-06-10T01:44:56.013847068Z         Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:44:56.013864628Z src/api/repositories/deal-repository.ts(164,22): error TS2352: Conversion of type '{ id: string; name: string; stage: string; value: number | null; currency: string; probability: number | null; expected_close_date: string | null; description: string | null; company_id: string; ... 10 more ...; updated_by: string | null; }' to type 'Deal & { companyName?: string; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.013874169Z   Type '{ id: string; name: string; stage: string; value: number; currency: string; probability: number; expected_close_date: string; description: string; company_id: string; company_name: string; company_industry: string; ... 8 more ...; updated_by: string; }' is missing the following properties from type 'Deal': createdAt, updatedAt
2025-06-10T01:44:56.013879289Z src/api/repositories/deal-repository.ts(180,17): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.013882959Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:44:56.013886769Z src/api/repositories/deal-repository.ts(181,17): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.013902269Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:44:56.01391791Z src/api/repositories/deal-repository.ts(182,17): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.01392188Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:44:56.01392422Z src/api/repositories/deal-repository.ts(243,20): error TS2352: Conversion of type '{ id: string; name: string; stage: string; value: number | null; currency: string; probability: number | null; expected_close_date: string | null; description: string | null; company_id: string; ... 10 more ...; updated_by: string | null; }' to type 'Deal & { companyName?: string; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.01392743Z   Type '{ id: string; name: string; stage: string; value: number; currency: string; probability: number; expected_close_date: string; description: string; company_id: string; company_name: string; company_industry: string; ... 8 more ...; updated_by: string; }' is missing the following properties from type 'Deal': createdAt, updatedAt
2025-06-10T01:44:56.01393633Z src/api/repositories/deal-repository.ts(259,15): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.013938621Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:44:56.013941201Z src/api/repositories/deal-repository.ts(260,15): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.013943541Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:44:56.013945641Z src/api/repositories/deal-repository.ts(261,15): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:44:56.013947881Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:44:56.013968581Z src/api/repositories/deal-repository.ts(505,22): error TS2551: Property 'customFields' does not exist on type 'DealUpdateData'. Did you mean 'custom_fields'?
2025-06-10T01:44:56.013973551Z src/api/repositories/deal-repository.ts(506,39): error TS2551: Property 'customFields' does not exist on type 'DealUpdateData'. Did you mean 'custom_fields'?
2025-06-10T01:44:56.013987612Z src/api/repositories/deal-repository.ts(506,80): error TS2551: Property 'customFields' does not exist on type 'DealUpdateData'. Did you mean 'custom_fields'?
2025-06-10T01:44:56.013999232Z src/api/repositories/deal-repository.ts(542,20): error TS2551: Property 'expectedCloseDate' does not exist on type 'DealUpdateData'. Did you mean 'expected_close_date'?
2025-06-10T01:44:56.014003402Z src/api/repositories/deal-repository.ts(542,65): error TS2551: Property 'expectedCloseDate' does not exist on type 'DealUpdateData'. Did you mean 'expected_close_date'?
2025-06-10T01:44:56.014010803Z src/api/repositories/deal-repository.ts(557,20): error TS2551: Property 'updatedBy' does not exist on type 'DealUpdateData'. Did you mean 'updated_by'?
2025-06-10T01:44:56.014023913Z src/api/repositories/enhanced-repository.ts(627,7): error TS2322: Type 'Contact[]' is not assignable to type 'number | Partial<BaseContact>[]'.
2025-06-10T01:44:56.014027953Z   Type 'Contact[]' is not assignable to type 'Partial<BaseContact>[]'.
2025-06-10T01:44:56.014031283Z     Type 'Contact' is not assignable to type 'Partial<BaseContact>'.
2025-06-10T01:44:56.014035223Z       Types of property 'company' are incompatible.
2025-06-10T01:44:56.014038213Z         Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:44:56.014041543Z src/api/repositories/enhanced-repository.ts(637,7): error TS2322: Type 'Deal[]' is not assignable to type 'Partial<BaseDeal>[]'.
2025-06-10T01:44:56.014044863Z   Type 'Deal' is not assignable to type 'Partial<BaseDeal>'.
2025-06-10T01:44:56.014048373Z     Types of property 'company' are incompatible.
2025-06-10T01:44:56.014051904Z       Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:44:56.014059224Z src/api/repositories/expenses-repository.ts(17,3): error TS2416: Property 'getAll' in type 'ExpensesRepository' is not assignable to the same property in base type 'BaseRepositoryEnhanced'.
2025-06-10T01:44:56.014062984Z   Type '(options?: EnhancedQueryOptions) => CustomExpense[]' is not assignable to type '<T>(conditions?: Record<string, any>) => T[]'.
2025-06-10T01:44:56.014066624Z     Type 'CustomExpense[]' is not assignable to type 'T[]'.
2025-06-10T01:44:56.014069784Z       Type 'CustomExpense' is not assignable to type 'T'.
2025-06-10T01:44:56.014074874Z         'T' could be instantiated with an arbitrary type which could be unrelated to 'CustomExpense'.
2025-06-10T01:44:56.014078624Z src/api/repositories/expenses-repository.ts(60,3): error TS2416: Property 'getById' in type 'ExpensesRepository' is not assignable to the same property in base type 'BaseRepositoryEnhanced'.
2025-06-10T01:44:56.014082304Z   Type '(id: string, options?: EnhancedQueryOptions) => CustomExpense' is not assignable to type '<T>(id: string) => T'.
2025-06-10T01:44:56.014086125Z     Type 'CustomExpense' is not assignable to type 'T'.
2025-06-10T01:44:56.014089585Z       'T' could be instantiated with an arbitrary type which could be unrelated to 'CustomExpense'.
2025-06-10T01:44:56.014140836Z src/api/repositories/knowledge-graph-repository.ts(870,44): error TS2339: Property 'getDealsByCompany' does not exist on type 'DealRepository'.
2025-06-10T01:44:56.014145666Z src/api/repositories/knowledge-graph-repository.ts(911,27): error TS2339: Property 'relatedContactId' does not exist on type 'ContactRelationshipWithDetails'.
2025-06-10T01:44:56.014153366Z src/api/repositories/knowledge-graph-repository.ts(937,28): error TS2551: Property 'company_id' does not exist on type 'Deal'. Did you mean 'companyId'?
2025-06-10T01:44:56.014157146Z src/api/repositories/project-repository.ts(3,10): error TS2614: Module '"../services/db-service"' has no exported member 'db'. Did you mean to use 'import db from "../services/db-service"' instead?
2025-06-10T01:44:56.014163916Z src/api/repositories/project-repository.ts(4,10): error TS2305: Module '"../services/activity-service"' has no exported member 'logActivity'.
2025-06-10T01:44:56.014177207Z src/api/repositories/project-repository.ts(57,7): error TS2322: Type 'string' is not assignable to type 'string[]'.
2025-06-10T01:44:56.014185347Z src/api/repositories/project-repository.ts(58,7): error TS2322: Type 'string' is not assignable to type 'Record<string, any>'.
2025-06-10T01:44:56.014189967Z src/api/repositories/project-repository.ts(185,7): error TS2322: Type 'string' is not assignable to type 'string[]'.
2025-06-10T01:44:56.014202837Z src/api/repositories/project-repository.ts(188,7): error TS2322: Type 'string' is not assignable to type 'Record<string, any>'.
2025-06-10T01:44:56.014234209Z src/api/repositories/relationships/deal-estimate-repository.ts(134,9): error TS2367: This comparison appears to be unintentional because the types 'EstimateType.INTERNAL' and '"harvest"' have no overlap.
2025-06-10T01:44:56.014237438Z src/api/routes/crm/companies.ts(273,11): error TS2345: Argument of type 'CompanyUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:44:56.014239609Z   Index signature for type 'string' is missing in type 'CompanyUpdate'.
2025-06-10T01:44:56.014244969Z src/api/routes/crm/companies.ts(281,11): error TS2345: Argument of type 'CompanyUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:44:56.014247419Z   Index signature for type 'string' is missing in type 'CompanyUpdate'.
2025-06-10T01:44:56.01427203Z src/api/routes/crm/conversations.ts(112,35): error TS2339: Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.01429316Z src/api/routes/crm/deals.ts(216,9): error TS2345: Argument of type 'DealUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:44:56.01429528Z   Index signature for type 'string' is missing in type 'DealUpdate'.
2025-06-10T01:44:56.01429696Z src/api/routes/crm/network.ts(208,22): error TS2339: Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.01430364Z src/api/routes/crm/network.ts(239,22): error TS2339: Property 'user' does not exist on type 'Request<{ id: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.01430906Z src/api/routes/crm/network.ts(470,7): error TS2322: Type 'string' is not assignable to type 'number'.
2025-06-10T01:44:56.014345591Z src/api/routes/crm/network.ts(559,33): error TS2445: Property 'getById' is protected and only accessible within class 'BaseRepository' and its subclasses.
2025-06-10T01:44:56.014348332Z src/api/routes/crm/network.ts(580,39): error TS2339: Property 'coveredRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:44:56.014367922Z src/api/routes/crm/network.ts(580,71): error TS2339: Property 'totalRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:44:56.014387082Z src/api/routes/crm/network.ts(583,49): error TS2339: Property 'coveredRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:44:56.014411183Z src/api/routes/crm/network.ts(583,73): error TS2339: Property 'totalRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:44:56.014415443Z src/api/routes/crm/network.ts(584,36): error TS2339: Property 'totalRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:44:56.014447384Z src/api/routes/crm/network.ts(584,58): error TS2339: Property 'coveredRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:44:56.014451074Z src/api/routes/crm/network.ts(591,17): error TS2339: Property 'harvestId' does not exist on type 'unknown'.
2025-06-10T01:44:56.014462835Z src/api/routes/crm/network.ts(594,53): error TS2339: Property 'getClientProjects' does not exist on type 'HarvestService'.
2025-06-10T01:44:56.014501225Z src/api/routes/crm/network.ts(594,79): error TS2339: Property 'harvestId' does not exist on type 'unknown'.
2025-06-10T01:44:56.014518716Z src/api/routes/deal-contacts.ts(6,33): error TS2305: Module '"../repositories/relationships/contact-role-repository"' has no exported member 'ContactRole'.
2025-06-10T01:44:56.014525366Z src/api/routes/estimates.ts(247,59): error TS2345: Argument of type '"harvest"' is not assignable to parameter of type '"internal"'.
2025-06-10T01:44:56.014556507Z src/api/routes/feature-flags.ts(78,27): error TS2339: Property 'user' does not exist on type 'Request<{ flag: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.014565447Z src/api/routes/feature-flags.ts(132,27): error TS2339: Property 'user' does not exist on type 'Request<{ flag: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.014584758Z src/api/routes/feature-flags.ts(173,27): error TS2339: Property 'user' does not exist on type 'Request<{ flag: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.014587138Z src/api/routes/feature-flags.ts(217,27): error TS2339: Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.014590898Z src/api/routes/feature-flags.ts(288,27): error TS2339: Property 'user' does not exist on type 'Request<{ action: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.014615739Z src/api/routes/harvest.ts(397,82): error TS2339: Property 'name' does not exist on type 'HarvestEstimate'.
2025-06-10T01:44:56.014618769Z src/api/routes/harvest.ts(407,60): error TS2345: Argument of type '"harvest"' is not assignable to parameter of type '"internal"'.
2025-06-10T01:44:56.014625049Z src/api/routes/harvest.ts(433,20): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.014628319Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.014640019Z src/api/routes/harvest.ts(434,15): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.014643239Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.01464938Z src/api/routes/harvest.ts(435,13): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.014652269Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.01467019Z src/api/routes/harvest.ts(436,16): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.01467257Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:44:56.01467651Z src/api/routes/hubspot-mcp/index.ts(50,33): error TS2551: Property 'userId' does not exist on type 'Session & Partial<SessionData>'. Did you mean 'user'?
2025-06-10T01:44:56.014702591Z src/api/routes/hubspot-mcp/index.ts(98,35): error TS2551: Property 'userId' does not exist on type 'Session & Partial<SessionData>'. Did you mean 'user'?
2025-06-10T01:44:56.014747172Z src/api/routes/hubspot-mcp/index.ts(199,9): error TS2353: Object literal may only specify known properties, and 'toolsUsed' does not exist in type 'LogContext'.
2025-06-10T01:44:56.014757782Z src/api/routes/hubspot-mcp/mcp-session-manager.ts(88,9): error TS2353: Object literal may only specify known properties, and 'messageCount' does not exist in type 'LogContext'.
2025-06-10T01:44:56.014768113Z src/api/routes/hubspot-mcp/mcp-session-manager.ts(110,65): error TS2353: Object literal may only specify known properties, and 'count' does not exist in type 'LogContext'.
2025-06-10T01:44:56.014818744Z src/api/routes/hubspot-mcp/tool-executor.ts(8,10): error TS2724: '"../../services/hubspot"' has no exported member named 'getHubSpotService'. Did you mean 'HubSpotService'?
2025-06-10T01:44:56.014830684Z src/api/routes/hubspot-mcp/tool-executor.ts(26,43): error TS2353: Object literal may only specify known properties, and 'toolName' does not exist in type 'LogContext'.
2025-06-10T01:44:56.014838345Z src/api/routes/hubspot-mcp/tool-executor.ts(103,52): error TS2353: Object literal may only specify known properties, and 'toolName' does not exist in type 'Error'.
2025-06-10T01:44:56.014845275Z src/api/routes/hubspot-mcp/tool-executor.ts(121,32): error TS2339: Property 'searchContacts' does not exist on type 'ContactRepository'.
2025-06-10T01:44:56.014888746Z src/api/routes/hubspot-mcp/tool-executor.ts(145,45): error TS2345: Argument of type '{ firstName: any; lastName: any; email: any; phone: any; jobTitle: any; notes: any; source: string; }' is not assignable to parameter of type 'ContactCreate'.
2025-06-10T01:44:56.014893586Z   Types of property 'source' are incompatible.
2025-06-10T01:44:56.014897006Z     Type 'string' is not assignable to type 'ContactSource'.
2025-06-10T01:44:56.014900036Z src/api/routes/hubspot-mcp/tool-executor.ts(152,59): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.014941557Z src/api/routes/hubspot-mcp/tool-executor.ts(161,68): error TS2554: Expected 2 arguments, but got 3.
2025-06-10T01:44:56.014947077Z src/api/routes/hubspot-mcp/tool-executor.ts(172,66): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.014950477Z src/api/routes/hubspot-mcp/tool-executor.ts(192,33): error TS2339: Property 'searchCompanies' does not exist on type 'CompanyRepository'.
2025-06-10T01:44:56.014956208Z src/api/routes/hubspot-mcp/tool-executor.ts(222,45): error TS2345: Argument of type '{ name: any; domain: any; industry: any; phone: any; address: any; city: any; state: any; country: any; numberOfEmployees: any; annualRevenue: any; description: any; website: any; source: string; }' is not assignable to parameter of type 'CompanyCreate'.
2025-06-10T01:44:56.014958598Z   Types of property 'source' are incompatible.
2025-06-10T01:44:56.014960458Z     Type 'string' is not assignable to type 'CompanySource'.
2025-06-10T01:44:56.014962218Z src/api/routes/hubspot-mcp/tool-executor.ts(229,59): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.014990619Z src/api/routes/hubspot-mcp/tool-executor.ts(238,68): error TS2554: Expected 2 arguments, but got 3.
2025-06-10T01:44:56.014995599Z src/api/routes/hubspot-mcp/tool-executor.ts(249,66): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.015002259Z src/api/routes/hubspot-mcp/tool-executor.ts(316,56): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.01502954Z src/api/routes/hubspot-mcp/tool-executor.ts(336,63): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.015033Z src/api/routes/hubspot-mcp/tool-executor.ts(346,39): error TS2339: Property 'getContactCompanies' does not exist on type 'ContactRelationshipsRepository'.
2025-06-10T01:44:56.01503477Z src/api/routes/hubspot-mcp/tool-executor.ts(352,38): error TS2339: Property 'getCompanyContacts' does not exist on type 'ContactRelationshipsRepository'.
2025-06-10T01:44:56.01503697Z src/api/routes/hubspot-mcp/tool-executor.ts(358,37): error TS2339: Property 'associateContactWithCompany' does not exist on type 'ContactRelationshipsRepository'.
2025-06-10T01:44:56.01504106Z src/api/routes/hubspot-mcp/tool-executor.ts(399,49): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:44:56.01505371Z src/api/routes/hubspot.ts(437,79): error TS2339: Property 'notesCount' does not exist on type '{ companiesCount?: number; dealsCount?: number; contactsCount?: number; }'.
2025-06-10T01:44:56.01505583Z src/api/routes/hubspot.ts(440,86): error TS2339: Property 'associationsCount' does not exist on type '{ companiesCount?: number; dealsCount?: number; contactsCount?: number; }'.
2025-06-10T01:44:56.015088401Z src/api/routes/mcp/index.ts(11,5): error TS2717: Subsequent property declarations must have the same type.  Property 'tokenSet' must be of type 'XeroTokenSet', but here has type 'any'.
2025-06-10T01:44:56.015090861Z src/api/routes/mcp/tool-executor.ts(21,103): error TS2345: Argument of type '{ page: any; status: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.015092851Z src/api/routes/mcp/tool-executor.ts(29,103): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.015095221Z src/api/routes/mcp/tool-executor.ts(41,92): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.015101741Z src/api/routes/mcp/tool-executor.ts(48,108): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.015141203Z src/api/routes/mcp/tool-executor.ts(60,98): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.015173703Z src/api/routes/mcp/tool-executor.ts(67,88): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:44:56.015179074Z src/api/routes/mcp/tool-executor.ts(74,104): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type '"AUTHORISED" | "DRAFT" | "SUBMITTED" | "DELETED" | "BILLED"'.
2025-06-10T01:44:56.015186894Z src/api/routes/mcp/tool-executor.ts(161,55): error TS2551: Property 'getEmployeeLeave' does not exist on type 'PayrollAuApi'. Did you mean 'getEmployee'?
2025-06-10T01:44:56.015189804Z src/api/routes/mcp/tool-executor.ts(169,63): error TS2339: Property 'getEmployeeLeaveBalances' does not exist on type 'PayrollAuApi'.
2025-06-10T01:44:56.015226265Z src/api/routes/mcp/tool-executor.ts(177,60): error TS2339: Property 'getEmployeeLeaveTypes' does not exist on type 'PayrollAuApi'.
2025-06-10T01:44:56.015230485Z src/api/routes/mcp/tool-executor.ts(185,62): error TS2339: Property 'getEmployeeLeavePeriods' does not exist on type 'PayrollAuApi'.
2025-06-10T01:44:56.015235375Z src/api/routes/mcp/tool-executor.ts(193,63): error TS2339: Property 'getLeaveTypes' does not exist on type 'PayrollAuApi'.
2025-06-10T01:44:56.015279536Z src/api/routes/mcp/tool-executor.ts(202,39): error TS2551: Property 'timesheets' does not exist on type 'TimesheetObject'. Did you mean 'timesheet'?
2025-06-10T01:44:56.015284057Z src/api/routes/mcp/tool-executor.ts(216,22): error TS2322: Type '{ name: any; emailAddress: any; phones: { phoneType: string; phoneNumber: any; }[]; isCustomer: any; isSupplier: any; }' is not assignable to type 'Contact'.
2025-06-10T01:44:56.015285877Z   Types of property ''phones'' are incompatible.
2025-06-10T01:44:56.015289046Z     Type '{ phoneType: string; phoneNumber: any; }[]' is not assignable to type 'Phone[]'.
2025-06-10T01:44:56.015290977Z       Type '{ phoneType: string; phoneNumber: any; }' is not assignable to type 'Phone'.
2025-06-10T01:44:56.015292907Z         Types of property ''phoneType'' are incompatible.
2025-06-10T01:44:56.015294777Z           Type 'string' is not assignable to type 'PhoneTypeEnum'.
2025-06-10T01:44:56.015299987Z src/api/routes/mcp/tool-executor.ts(299,22): error TS2322: Type '{ contactID: any; name: any; emailAddress: any; phones: { phoneType: string; phoneNumber: any; }[]; }' is not assignable to type 'Contact'.
2025-06-10T01:44:56.015311997Z   Types of property ''phones'' are incompatible.
2025-06-10T01:44:56.015313907Z     Type '{ phoneType: string; phoneNumber: any; }[]' is not assignable to type 'Phone[]'.
2025-06-10T01:44:56.015315617Z       Type '{ phoneType: string; phoneNumber: any; }' is not assignable to type 'Phone'.
2025-06-10T01:44:56.015317267Z         Types of property ''phoneType'' are incompatible.
2025-06-10T01:44:56.015319027Z           Type 'string' is not assignable to type 'PhoneTypeEnum'.
2025-06-10T01:44:56.015322767Z src/api/routes/radar-actions.ts(26,7): error TS2322: Type 'string | string[]' is not assignable to type 'RadarActionStatus | RadarActionStatus[]'.
2025-06-10T01:44:56.015324807Z   Type 'string' is not assignable to type 'RadarActionStatus | RadarActionStatus[]'.
2025-06-10T01:44:56.015328378Z src/api/routes/radar-actions.ts(31,7): error TS2322: Type 'string | string[]' is not assignable to type 'RadarActionPriority | RadarActionPriority[]'.
2025-06-10T01:44:56.015330138Z   Type 'string' is not assignable to type 'RadarActionPriority | RadarActionPriority[]'.
2025-06-10T01:44:56.015332018Z src/api/routes/radar-actions.ts(36,7): error TS2322: Type 'string | string[]' is not assignable to type 'RadarActionType | RadarActionType[]'.
2025-06-10T01:44:56.015333948Z   Type 'string' is not assignable to type 'RadarActionType | RadarActionType[]'.
2025-06-10T01:44:56.015357399Z src/api/routes/radar-actions.ts(136,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.015359848Z src/api/routes/radar-actions.ts(165,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.015361619Z src/api/routes/radar-actions.ts(190,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.015394579Z src/api/routes/radar-actions.ts(212,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.01539738Z src/api/routes/radar-actions.ts(234,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:44:56.01540136Z src/api/services/activity-service.ts(214,7): error TS2322: Type '"ui"' is not assignable to type 'ActivitySource'.
2025-06-10T01:44:56.01543541Z src/api/services/hubspot/deals.ts(37,31): error TS2339: Property 'associationsApi' does not exist on type 'DealsDiscovery'.
2025-06-10T01:44:56.015437741Z src/api/services/hubspot/deals.ts(50,51): error TS2339: Property 'associationsApi' does not exist on type 'DealsDiscovery'.
2025-06-10T01:44:56.015441781Z src/api/services/hubspot/deals.ts(469,46): error TS2345: Argument of type '{ dealsCount: number; companiesCount: number; contactsCount: number; }' is not assignable to parameter of type 'number'.
2025-06-10T01:44:56.015472311Z src/api/services/hubspot/index.ts(299,11): error TS2353: Object literal may only specify known properties, and 'notesCount' does not exist in type '{ companiesCount?: number; dealsCount?: number; contactsCount?: number; }'.
2025-06-10T01:44:56.015474751Z src/api/services/hubspot/notes.ts(224,53): error TS2339: Property 'getNotesByEntity' does not exist on type 'NoteRepository'.
2025-06-10T01:44:56.015499902Z src/api/services/hubspot/notes.ts(240,64): error TS2345: Argument of type '{ entityType: "company" | "contact" | "deal"; entityId: string; content: string; noteType: string; isPrivate: boolean; createdBy: string; }' is not assignable to parameter of type 'NoteCreate'.
2025-06-10T01:44:56.015505212Z   Property 'dealId' is missing in type '{ entityType: "company" | "contact" | "deal"; entityId: string; content: string; noteType: string; isPrivate: boolean; createdBy: string; }' but required in type 'NoteCreate'.
2025-06-10T01:44:56.015528773Z src/api/services/tender-ingestion-service.ts(147,48): error TS2339: Property 'searchCompanies' does not exist on type 'CompanyRepository'.
2025-06-10T01:44:56.015539283Z src/api/utils/db-safe-query.ts(17,7): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:44:56.015542263Z src/api/utils/db-safe-query.ts(51,33): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:44:56.015577444Z src/api/utils/db-safe-query.ts(73,7): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:44:56.015583205Z src/api/utils/db-timeout.ts(8,36): error TS2709: Cannot use namespace 'Statement' as a type.
2025-06-10T01:44:56.015650916Z src/api/utils/db-timeout.ts(11,69): error TS2709: Cannot use namespace 'RunResult' as a type.
2025-06-10T01:44:56.015655976Z src/api/utils/db-timeout.ts(14,35): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:44:56.015659167Z src/api/utils/db-timeout.ts(42,14): error TS2709: Cannot use namespace 'Statement' as a type.
2025-06-10T01:44:56.015662287Z src/api/utils/db-timeout.ts(95,37): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:44:56.015665147Z src/api/utils/db-timeout.ts(116,77): error TS2709: Cannot use namespace 'RunResult' as a type.
2025-06-10T01:44:56.015675007Z src/database/index.ts(14,31): error TS2694: Namespace '"better-sqlite3"' has no exported member 'Database'.
2025-06-10T01:44:56.015686177Z src/database/index.ts(51,46): error TS2694: Namespace '"better-sqlite3"' has no exported member 'Database'.
2025-06-10T01:44:56.015691097Z src/database/index.ts(144,53): error TS2694: Namespace '"better-sqlite3"' has no exported member 'Database'.
2025-06-10T01:44:56.015859622Z src/frontend/types/crm-types.ts(53,18): error TS2430: Interface 'Deal' incorrectly extends interface 'BaseDeal'.
2025-06-10T01:44:56.015864902Z   Types of property 'company' are incompatible.
2025-06-10T01:44:56.015868342Z     Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:44:56.015871312Z src/frontend/types/crm-types.ts(109,18): error TS2430: Interface 'Contact' incorrectly extends interface 'BaseContact'.
2025-06-10T01:44:56.015873622Z   Types of property 'company' are incompatible.
2025-06-10T01:44:56.015876052Z     Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:44:56.015879002Z src/services/cashflow/index.ts(231,11): error TS2345: Argument of type 'Transaction[]' is not assignable to parameter of type 'ProjectionInvoice[]'.
2025-06-10T01:44:56.015883702Z   Property 'what' is missing in type 'Transaction' but required in type 'ProjectionInvoice'.
2025-06-10T01:44:56.015897593Z src/services/cashflow/projection-filter-service.ts(234,34): error TS2339: Property 'clientName' does not exist on type 'ProjectionInvoice'.
2025-06-10T01:44:56.015903613Z src/services/cashflow/projection-filter-service.ts(258,38): error TS2339: Property 'clientName' does not exist on type 'OpenInvoice'.
2025-06-10T01:44:56.015906423Z src/services/cashflow/projection-filter-service.ts(263,85): error TS2339: Property 'number' does not exist on type 'OpenInvoice'.
2025-06-10T01:44:56.015937724Z src/services/cashflow/projection-filter-service.ts(265,75): error TS2339: Property 'due_date' does not exist on type 'OpenInvoice'.
2025-06-10T01:44:56.015941774Z src/services/harvest/time-report-service.ts(115,64): error TS2339: Property 'project_id' does not exist on type 'unknown'.
2025-06-10T01:44:56.015945844Z src/services/harvest/time-report-service.ts(117,59): error TS2339: Property 'billable_amount' does not exist on type 'unknown'.
2025-06-10T01:44:56.015950164Z src/services/harvest/time-report-service.ts(119,35): error TS2339: Property 'project_id' does not exist on type 'unknown'.
2025-06-10T01:44:56.015964875Z src/services/harvest/time-report-service.ts(120,37): error TS2339: Property 'project_name' does not exist on type 'unknown'.
2025-06-10T01:44:56.015967995Z src/services/harvest/time-report-service.ts(121,34): error TS2339: Property 'client_id' does not exist on type 'unknown'.
2025-06-10T01:44:56.015973755Z src/services/harvest/time-report-service.ts(122,36): error TS2339: Property 'client_name' does not exist on type 'unknown'.
2025-06-10T01:44:56.015979085Z src/services/harvest/time-report-service.ts(123,40): error TS2339: Property 'billable_amount' does not exist on type 'unknown'.
2025-06-10T01:44:56.016002736Z src/services/harvest/time-report-service.ts(124,34): error TS2339: Property 'currency' does not exist on type 'unknown'.
2025-06-10T01:44:56.016004956Z src/services/harvest/time-report-service.ts(183,71): error TS2339: Property 'user_id' does not exist on type 'unknown'.
2025-06-10T01:44:56.016006696Z src/services/harvest/time-report-service.ts(188,45): error TS2339: Property 'weekly_capacity' does not exist on type 'HarvestUser'.
2025-06-10T01:44:56.016010526Z src/services/harvest/time-report-service.ts(191,46): error TS2339: Property 'billable_hours' does not exist on type 'unknown'.
2025-06-10T01:44:56.016024466Z src/services/harvest/time-report-service.ts(192,43): error TS2339: Property 'total_hours' does not exist on type 'unknown'.
2025-06-10T01:44:56.016027536Z src/services/harvest/time-report-service.ts(195,47): error TS2339: Property 'billable_amount' does not exist on type 'unknown'.
2025-06-10T01:44:56.016033826Z src/services/harvest/time-report-service.ts(196,41): error TS2339: Property 'currency' does not exist on type 'unknown'.
2025-06-10T01:44:56.016044307Z src/services/harvest/time-report-service.ts(330,28): error TS2339: Property 'cost_rate' does not exist on type 'HarvestUser'.
2025-06-10T01:44:56.016050377Z src/services/harvest/time-report-service.ts(332,32): error TS2339: Property 'is_contractor' does not exist on type 'HarvestUser'.
2025-06-10T01:44:56.016092928Z src/services/xero/bank-account-service.ts(193,54): error TS2345: Argument of type 'XeroReport' is not assignable to parameter of type 'XeroBankSummaryReport'.
2025-06-10T01:44:56.016095628Z   Types of property 'Rows' are incompatible.
2025-06-10T01:44:56.016097688Z     Type 'XeroReportRow[]' is not assignable to type 'XeroReportRow[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:44:56.016099438Z       Type 'XeroReportRow' is not assignable to type 'XeroReportRow'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:44:56.016101228Z         Types of property 'Cells' are incompatible.
2025-06-10T01:44:56.016123609Z           Type 'XeroReportCell[]' is not assignable to type 'XeroReportCell[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:44:56.016134969Z             Type 'XeroReportCell' has no properties in common with type 'XeroReportCell'.
2025-06-10T01:44:56.016136909Z src/services/xero/bank-account-service.ts(196,54): error TS2345: Argument of type 'XeroReport' is not assignable to parameter of type 'XeroBankSummaryReport'.
2025-06-10T01:44:56.016139589Z   Types of property 'Rows' are incompatible.
2025-06-10T01:44:56.016142399Z     Type 'XeroReportRow[]' is not assignable to type 'XeroReportRow[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:44:56.01614498Z       Type 'XeroReportRow' is not assignable to type 'XeroReportRow'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:44:56.01614762Z         Types of property 'Cells' are incompatible.
2025-06-10T01:44:56.01615014Z           Type 'XeroReportCell[]' is not assignable to type 'XeroReportCell[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:44:56.01615885Z             Type 'XeroReportCell' has no properties in common with type 'XeroReportCell'.
2025-06-10T01:44:56.01616201Z src/services/xero/bank-account-service.ts(202,46): error TS2551: Property 'ReportID' does not exist on type 'XeroBankSummaryResponse'. Did you mean 'Reports'?
2025-06-10T01:44:56.01617487Z src/services/xero/bank-account-service.ts(204,56): error TS2559: Type 'XeroBankSummaryResponse' has no properties in common with type 'XeroBankSummaryReport'.
2025-06-10T01:44:56.016185461Z src/services/xero/bank-account-service.ts(314,28): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:44:56.016187981Z src/services/xero/bank-account-service.ts(326,28): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:44:56.016216501Z src/services/xero/bank-account-service.ts(347,18): error TS2339: Property 'address' does not exist on type 'XeroReportCell'.
2025-06-10T01:44:56.016221851Z src/services/xero/bank-account-service.ts(347,51): error TS2339: Property 'address' does not exist on type 'XeroReportCell'.
2025-06-10T01:44:56.016235282Z src/services/xero/bank-account-service.ts(347,82): error TS2339: Property 'address' does not exist on type 'XeroReportCell'.
2025-06-10T01:44:56.016266113Z src/services/xero/bank-account-service.ts(351,32): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:44:56.016270353Z src/services/xero/bank-account-service.ts(354,32): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:44:56.016277473Z src/services/xero/bank-account-service.ts(474,40): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:44:56.016306844Z src/services/xero/bank-account-service.ts(481,39): error TS2551: Property 'Attributes' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:44:56.016314354Z src/services/xero/bank-account-service.ts(484,32): error TS2551: Property 'id' does not exist on type 'XeroReportAttribute'. Did you mean 'Id'?
2025-06-10T01:44:56.016343695Z src/services/xero/bank-account-service.ts(494,43): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:44:56.016347635Z src/services/xero/bank-account-service.ts(495,43): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:44:56.016388236Z src/services/xero/bank-account-service.ts(535,45): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:44:56.016392496Z src/services/xero/bank-account-service.ts(536,45): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:44:56.016399536Z src/services/xero/bank-account-service.ts(583,33): error TS2339: Property 'AccountID' does not exist on type 'unknown'.
2025-06-10T01:44:56.016412287Z src/services/xero/bank-account-service.ts(583,54): error TS2339: Property 'accountID' does not exist on type 'unknown'.
2025-06-10T01:44:56.016449848Z src/services/xero/bank-account-service.ts(584,35): error TS2339: Property 'Name' does not exist on type 'unknown'.
2025-06-10T01:44:56.016453358Z src/services/xero/bank-account-service.ts(584,51): error TS2339: Property 'name' does not exist on type 'unknown'.
2025-06-10T01:44:56.016456168Z src/services/xero/bank-account-service.ts(590,26): error TS2339: Property 'CurrentBalance' does not exist on type 'unknown'.
2025-06-10T01:44:56.016461948Z src/services/xero/bank-account-service.ts(591,34): error TS2339: Property 'CurrentBalance' does not exist on type 'unknown'.
2025-06-10T01:44:56.016470068Z src/services/xero/bank-account-service.ts(593,33): error TS2339: Property 'Balance' does not exist on type 'unknown'.
2025-06-10T01:44:56.016473898Z src/services/xero/bank-account-service.ts(594,34): error TS2339: Property 'Balance' does not exist on type 'unknown'.
2025-06-10T01:44:56.01652003Z src/services/xero/bank-account-service.ts(596,33): error TS2339: Property 'currentBalance' does not exist on type 'unknown'.
2025-06-10T01:44:56.01652556Z src/services/xero/bank-account-service.ts(597,34): error TS2339: Property 'currentBalance' does not exist on type 'unknown'.
2025-06-10T01:44:56.01653287Z src/services/xero/bank-account-service.ts(599,33): error TS2339: Property 'balance' does not exist on type 'unknown'.
2025-06-10T01:44:56.016559731Z src/services/xero/bank-account-service.ts(600,34): error TS2339: Property 'balance' does not exist on type 'unknown'.
2025-06-10T01:44:56.016563751Z src/services/xero/repeating-bill-service.ts(64,30): error TS2698: Spread types may only be created from object types.
2025-06-10T01:44:56.016607732Z src/services/xero/xero-service.ts(281,7): error TS2322: Type 'TokenSet' is not assignable to type 'XeroTokenSet'.
2025-06-10T01:44:56.016612562Z   Property 'access_token' is optional in type 'TokenSet' but required in type 'XeroTokenSet'.
2025-06-10T01:44:56.016619132Z src/services/xero/xero-service.ts(319,9): error TS2353: Object literal may only specify known properties, and 'tenantConnections' does not exist in type 'XeroTenant'.
2025-06-10T01:44:56.016649083Z src/services/xero/xero-service.ts(442,9): error TS2322: Type 'import("/opt/render/project/src/src/services/xero/bank-account-service").BankBalances' is not assignable to type 'BankBalances'.
2025-06-10T01:44:56.016652533Z   Property 'totalOpeningBalance' is optional in type 'BankBalances' but required in type 'BankBalances'.
2025-06-10T01:44:56.016654563Z src/services/xero/xero-service.ts(462,9): error TS2322: Type 'RepeatingBill[]' is not assignable to type 'RepeatingBillData[]'.
2025-06-10T01:44:56.016656303Z   Type 'RepeatingBill' is missing the following properties from type 'RepeatingBillData': contact, lineAmount, totalAmount, period, and 2 more.
2025-06-10T01:44:56.016774406Z src/utils/backend-logger.ts(277,7): error TS2353: Object literal may only specify known properties, and 'method' does not exist in type 'LogContext'.
2025-06-10T01:44:56.016778196Z src/utils/backend-logger.ts(285,5): error TS2322: Type '(...args: any[]) => void' is not assignable to type '{ (cb?: () => void): Response<any, Record<string, any>>; (chunk: any, cb?: () => void): Response<any, Record<string, any>>; (chunk: any, encoding: BufferEncoding, cb?: () => void): Response<...>; }'.
2025-06-10T01:44:56.016780126Z   Type 'void' is not assignable to type 'Response<any, Record<string, any>>'.
2025-06-10T01:44:56.016784006Z src/utils/deal-tracking.ts(302,12): error TS2678: Type '"Harvest"' is not comparable to type 'DataSource'.
2025-06-10T01:44:56.016824748Z src/utils/deal-tracking.ts(303,12): error TS2678: Type '"Xero"' is not comparable to type 'DataSource'.
2025-06-10T01:44:56.016830228Z src/utils/deal-tracking.ts(307,12): error TS2678: Type '"User"' is not comparable to type 'DataSource'.
2025-06-10T01:44:56.016846578Z src/utils/deal-tracking.ts(313,12): error TS2678: Type '"Import"' is not comparable to type 'DataSource'.
2025-06-10T01:44:56.016867149Z src/utils/deal-tracking.ts(316,12): error TS2678: Type '"API"' is not comparable to type 'DataSource'.
2025-06-10T01:44:56.016872469Z src/utils/deal-tracking.ts(508,49): error TS2367: This comparison appears to be unintentional because the types '"HubSpot" | "Manual" | "Estimate"' and '"System"' have no overlap.
2025-06-10T01:44:56.01690996Z src/utils/deal-tracking.ts(513,48): error TS2367: This comparison appears to be unintentional because the types '"HubSpot" | "Manual" | "Estimate"' and '"System"' have no overlap.
2025-06-10T01:44:56.01691697Z src/utils/email.ts(74,20): error TS2339: Property 'message' does not exist on type 'unknown'.
2025-06-10T01:44:56.01692112Z src/utils/estimate-deal-sync.ts(138,34): error TS2339: Property 'updateDraftEstimate' does not exist on type 'EstimateDraftsRepository'.
2025-06-10T01:44:56.016952371Z src/utils/frontend-logger.ts(467,5): error TS2686: 'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.
2025-06-10T01:44:56.016954841Z src/utils/frontend-logger.ts(476,14): error TS2686: 'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.
2025-06-10T01:44:56.016979792Z src/utils/logger.ts(213,9): error TS2698: Spread types may only be created from object types.
2025-06-10T01:44:56.016982932Z src/utils/logger.ts(243,7): error TS2322: Type 'unknown' is not assignable to type 'string'.
2025-06-10T01:44:56.016984772Z src/utils/logger.ts(253,9): error TS2322: Type 'unknown' is not assignable to type 'string'.
2025-06-10T01:44:56.016989072Z src/utils/logging-migration.ts(64,52): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.016991132Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.016994782Z src/utils/logging-migration.ts(66,51): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.016996642Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017027153Z src/utils/logging-migration.ts(83,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017030873Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017033733Z src/utils/logging-migration.ts(85,43): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017035423Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017037243Z src/utils/logging-migration.ts(101,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017039203Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017041213Z src/utils/logging-migration.ts(103,43): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017042913Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017046924Z src/utils/logging-migration.ts(119,45): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017048724Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017050334Z src/utils/logging-migration.ts(121,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017052124Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017058834Z src/utils/logging-migration.ts(137,45): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017061974Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017063754Z src/utils/logging-migration.ts(139,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:44:56.017065434Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:44:56.017096475Z src/utils/logging-migration.ts(173,9): error TS2322: Type 'ServiceName' is not assignable to type '"xero" | "harvest" | "hubspot"'.
2025-06-10T01:44:56.017100555Z   Type '"unknown"' is not assignable to type '"xero" | "harvest" | "hubspot"'.
2025-06-10T01:44:56.017117955Z src/utils/logging-migration.ts(217,104): error TS2345: Argument of type 'LogMetadata' is not assignable to parameter of type '{ field: string; oldValue: any; newValue: any; }[]'.
2025-06-10T01:44:56.017126286Z   Type 'LogMetadata' is missing the following properties from type '{ field: string; oldValue: any; newValue: any; }[]': length, pop, push, concat, and 29 more.
2025-06-10T01:44:56.017137406Z src/utils/logging-setup.ts(221,11): error TS2345: Argument of type '(req: Request, res: Response, next: Function) => void' is not assignable to parameter of type 'ExpressMiddleware | ExpressErrorHandler'.
2025-06-10T01:44:56.017140816Z   Type '(req: Request, res: Response, next: Function) => void' is not assignable to type 'ExpressMiddleware'.
2025-06-10T01:44:56.017143636Z     Types of parameters 'req' and 'req' are incompatible.
2025-06-10T01:44:56.017146276Z       Type 'ExpressRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': cookies, signedCookies, header, accepts, and 95 more.
2025-06-10T01:44:56.068800846Z TypeScript compilation failed, will use ts-node fallback
2025-06-10T01:44:57.431075845Z ==> Uploading build...
2025-06-10T01:45:14.34370376Z ==> Uploaded in 8.4s. Compression took 8.5s
2025-06-10T01:45:14.386120132Z ==> Build successful 🎉
2025-06-10T01:45:17.722242027Z ==> Deploying...
2025-06-10T01:46:49.433633964Z Found server file: /opt/render/project/src/dist/api/server.js
2025-06-10T01:46:51.397681727Z Production environment detected, using environment variable for callback URL
2025-06-10T01:46:51.397708178Z Xero callback URL configured as: https://upstream.onbord.au/api/xero/callback
2025-06-10T01:46:51.399752096Z Initializing XeroClient with config: {
2025-06-10T01:46:51.399763086Z   clientId: '72609...',
2025-06-10T01:46:51.399765897Z   redirectUri: 'https://upstream.onbord.au/api/xero/callback',
2025-06-10T01:46:51.399767657Z   scopes: [
2025-06-10T01:46:51.399769397Z     'openid',
2025-06-10T01:46:51.399771167Z     'profile',
2025-06-10T01:46:51.399772917Z     'email',
2025-06-10T01:46:51.399775497Z     'accounting.transactions',
2025-06-10T01:46:51.399777227Z     'accounting.reports.read',
2025-06-10T01:46:51.399778947Z     'accounting.settings',
2025-06-10T01:46:51.399781127Z     'offline_access',
2025-06-10T01:46:51.399782837Z     'payroll.employees',
2025-06-10T01:46:51.399784497Z     'payroll.payruns'
2025-06-10T01:46:51.399786197Z   ]
2025-06-10T01:46:51.399787947Z }
2025-06-10T01:46:51.400192558Z Creating Xero client with config: {
2025-06-10T01:46:51.400200769Z   clientId: '72609...',
2025-06-10T01:46:51.400203239Z   clientSecret: 'present',
2025-06-10T01:46:51.400205559Z   redirectUri: 'https://upstream.onbord.au/api/xero/callback',
2025-06-10T01:46:51.400207319Z   scopes: [
2025-06-10T01:46:51.400209109Z     'openid',
2025-06-10T01:46:51.400210899Z     'profile',
2025-06-10T01:46:51.400212599Z     'email',
2025-06-10T01:46:51.400214339Z     'accounting.transactions',
2025-06-10T01:46:51.400216009Z     'accounting.reports.read',
2025-06-10T01:46:51.400217639Z     'accounting.settings',
2025-06-10T01:46:51.400219319Z     'offline_access',
2025-06-10T01:46:51.400221039Z     'payroll.employees',
2025-06-10T01:46:51.40022305Z     'payroll.payruns'
2025-06-10T01:46:51.40022529Z   ]
2025-06-10T01:46:51.400227059Z }
2025-06-10T01:46:51.401380782Z ✓ Xero API method "getReportBalanceSheet" available
2025-06-10T01:46:51.401563697Z ✓ Xero API method "getReportBankSummary" available
2025-06-10T01:46:51.401573728Z ✓ Xero API method "getRepeatingInvoices" available
2025-06-10T01:46:51.401604018Z Found Australian Payroll API
2025-06-10T01:46:51.401781463Z Xero SDK initialized with: {
2025-06-10T01:46:51.401784923Z   clientId: '72609...',
2025-06-10T01:46:51.401786934Z   redirectUri: 'https://upstream.onbord.au/api/xero/callback',
2025-06-10T01:46:51.401788674Z   scopes: 9,
2025-06-10T01:46:51.401790924Z   hasPayrollApi: true
2025-06-10T01:46:51.401792664Z }
2025-06-10T01:46:51.402063742Z Initializing HarvestClient...
2025-06-10T01:46:51.402217636Z Harvest credentials: Token=Present, Account ID=Present
2025-06-10T01:46:51.408038431Z Harvest settings: Running in production mode, using data directory: /data
2025-06-10T01:46:51.409293276Z Harvest settings: Successfully verified write access to /data
2025-06-10T01:46:51.501836494Z Running in production mode, using data directory: /data
2025-06-10T01:46:51.501858035Z Using database path: /data/upstream.db
2025-06-10T01:46:51.502076341Z Successfully verified write access to /data
2025-06-10T01:46:51.522388096Z Successfully connected to SQLite database with timeouts configured
2025-06-10T01:46:51.524870086Z LeadsRepository: Using unified company table
2025-06-10T01:46:51.606263979Z KnowledgeGraphRepository: Database instance: available
2025-06-10T01:46:51.606494916Z KnowledgeGraphRepository: All repositories initialized successfully
2025-06-10T01:46:51.629938639Z Proxy trust enabled for production (first proxy only)
2025-06-10T01:46:51.631801712Z Cookie domain configured as: default (current domain)
2025-06-10T01:46:51.631815072Z Session config: NODE_ENV=production, API_PORT=3000, isLocalDevelopment=false, isPreviewDeployment=false
2025-06-10T01:46:51.631944236Z Using session configuration with cookie settings: {
2025-06-10T01:46:51.631950846Z   secure: true,
2025-06-10T01:46:51.631954096Z   maxAge: *********,
2025-06-10T01:46:51.631955986Z   httpOnly: true,
2025-06-10T01:46:51.631957856Z   sameSite: 'none',
2025-06-10T01:46:51.631959526Z   domain: '(default)',
2025-06-10T01:46:51.631961306Z   path: '/'
2025-06-10T01:46:51.631962996Z }
2025-06-10T01:46:51.631972977Z Production environment detected for cookies
2025-06-10T01:46:51.631996937Z Using Render hostname for cookies: upstream-hkfd.onrender.com
2025-06-10T01:46:51.640864888Z Session middleware configured with RedisStore: {
2025-06-10T01:46:51.640896449Z   resave: false,
2025-06-10T01:46:51.640901679Z   saveUninitialized: false,
2025-06-10T01:46:51.640905489Z   cookieSecure: true,
2025-06-10T01:46:51.640907269Z   cookieMaxAge: '7 days',
2025-06-10T01:46:51.640908989Z   sameSite: 'none',
2025-06-10T01:46:51.64091081Z   domain: 'default (browser determined)'
2025-06-10T01:46:51.64091276Z }
2025-06-10T01:46:51.64129784Z Initializing database...
2025-06-10T01:46:51.69392278Z Current database schema version: none
2025-06-10T01:46:51.69394656Z Database schema not found. Running simple migration...
2025-06-10T01:46:51.694028503Z Found migration file: /opt/render/project/src/migrations/000_unified_schema.sql
2025-06-10T01:46:51.694215318Z Executing migration...
2025-06-10T01:46:51.695117343Z Error initializing database schema: SqliteError: index idx_migrations_executed_at already exists
2025-06-10T01:46:51.695132364Z     at Database.exec (/opt/render/project/src/node_modules/better-sqlite3/lib/methods/wrappers.js:9:14)
2025-06-10T01:46:51.695135154Z     at initializeDatabase (/opt/render/project/src/dist/database/index.js:156:30)
2025-06-10T01:46:51.695138314Z     at Object.<anonymous> (/opt/render/project/src/dist/api/server.js:265:44)
2025-06-10T01:46:51.695140734Z     at Module._compile (node:internal/modules/cjs/loader:1256:14)
2025-06-10T01:46:51.695142864Z     at Module._extensions..js (node:internal/modules/cjs/loader:1310:10)
2025-06-10T01:46:51.695145694Z     at Module.load (node:internal/modules/cjs/loader:1119:32)
2025-06-10T01:46:51.695147814Z     at Module._load (node:internal/modules/cjs/loader:960:12)
2025-06-10T01:46:51.695159624Z     at Module.require (node:internal/modules/cjs/loader:1143:19)
2025-06-10T01:46:51.695162255Z     at require (node:internal/modules/cjs/helpers:121:18)
2025-06-10T01:46:51.695164585Z     at Object.<anonymous> (/opt/render/project/src/server.js:55:3) {
2025-06-10T01:46:51.695167735Z   code: 'SQLITE_ERROR'
2025-06-10T01:46:51.695170095Z }
2025-06-10T01:46:51.695249057Z Error initializing database: SqliteError: index idx_migrations_executed_at already exists
2025-06-10T01:46:51.695256357Z     at Database.exec (/opt/render/project/src/node_modules/better-sqlite3/lib/methods/wrappers.js:9:14)
2025-06-10T01:46:51.695259137Z     at initializeDatabase (/opt/render/project/src/dist/database/index.js:156:30)
2025-06-10T01:46:51.695276848Z     at Object.<anonymous> (/opt/render/project/src/dist/api/server.js:265:44)
2025-06-10T01:46:51.695279358Z     at Module._compile (node:internal/modules/cjs/loader:1256:14)
2025-06-10T01:46:51.695282208Z     at Module._extensions..js (node:internal/modules/cjs/loader:1310:10)
2025-06-10T01:46:51.695284878Z     at Module.load (node:internal/modules/cjs/loader:1119:32)
2025-06-10T01:46:51.695287128Z     at Module._load (node:internal/modules/cjs/loader:960:12)
2025-06-10T01:46:51.695289368Z     at Module.require (node:internal/modules/cjs/loader:1143:19)
2025-06-10T01:46:51.695291838Z     at require (node:internal/modules/cjs/helpers:121:18)
2025-06-10T01:46:51.695294678Z     at Object.<anonymous> (/opt/render/project/src/server.js:55:3) {
2025-06-10T01:46:51.695297659Z   code: 'SQLITE_ERROR'
2025-06-10T01:46:51.695300679Z }
2025-06-10T01:46:51.69537427Z Initializing cashflow snapshot job...
2025-06-10T01:46:51.695385611Z Initializing cashflow snapshot job...
2025-06-10T01:46:51.715261693Z Cashflow snapshot job scheduled to run at 1:00 AM daily
2025-06-10T01:46:51.715283244Z Cashflow snapshot job initialized and started
2025-06-10T01:46:51.715287014Z Environment configuration:
2025-06-10T01:46:51.715289844Z - Xero integration: Configured
2025-06-10T01:46:51.715299334Z - Harvest integration: Configured
2025-06-10T01:46:51.715767668Z Serving frontend static files from: /opt/render/project/src/dist (Render deployment)
2025-06-10T01:46:51.717345802Z API server running on port 3000
2025-06-10T01:46:51.717391424Z Socket.IO server running on port 3000
2025-06-10T01:46:51.717402784Z Xero callback URL configured as: https://upstream.onbord.au/api/xero/callback
2025-06-10T01:46:51.720512502Z Connected to Redis successfully.
2025-06-10T01:46:52.600162791Z {"level":2,"message":"HubSpot MCP Session Manager initialized","timestamp":"2025-06-10T01:46:51.602Z","context":{"sessionId":"***MASKED***","requestId":"fc8b35d4-5474-43f6-85d6-cda1e2cdeeb8"},"environment":"production","source":"backend"}
2025-06-10T01:46:52.67469519Z [DEBUG PRE-SESSION] Request to /. Cookies: None
2025-06-10T01:46:52.676282825Z [DEBUG POST-SESSION] Request to /. SessionID: 4uW95VeI-N7BpSdg01QrADDnwzI1RKIw. Has tokenSet: false
2025-06-10T01:46:53.017504956Z ==> No open ports detected, continuing to scan...
2025-06-10T01:46:53.528680857Z ==> Docs on specifying a port: https://render.com/docs/web-services#port-binding
2025-06-10T01:46:59.454274204Z ==> Your service is live 🎉