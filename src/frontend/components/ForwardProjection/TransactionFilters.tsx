import * as React from "react"; import { Transaction } from "../../../types/financial"; const { useMemo } = React; export interface TransactionFilters { types: string[]; sources: string[]; timeRange: [Date | null, Date | null]; searchText: string; amountRange: [number | null, number | null]; } interface TransactionFiltersProps { transactions: Transaction[]; filters: TransactionFilters; onFilterChange: (filters: TransactionFilters) => void; } const TransactionFilters: React.FC<TransactionFiltersProps> = ({ transactions, filters, onFilterChange, }) => { // Extract unique transaction types and sources for filter options const transactionTypes = useMemo(() => { const typesSet = new Set<string>(); transactions.forEach((transaction) => { typesSet.add(transaction.type); }); return Array.from(typesSet); }, [transactions]); // Extract income vs expense transactions for income filter const hasIncomeTransactions = useMemo(() => { return transactions.some((transaction) => transaction.amount > 0); }, [transactions]); // Check if we have real invoices and/or projected income const hasRealInvoices = useMemo(() => { return transactions.some( (transaction) => transaction.source === "Harvest" && transaction.type === "invoice" && !transaction.id.startsWith("future-work-") && !transaction.id.startsWith("uninvoiced-") ); }, [transactions]); const hasProjectedIncome = useMemo(() => { return transactions.some( (transaction) => transaction.source === "Harvest" && transaction.type === "invoice" && (transaction.id.startsWith("future-work-") || transaction.id.startsWith("uninvoiced-")) ); }, [transactions]); const transactionSources = useMemo(() => { const sourcesSet = new Set<string>(); transactions.forEach((transaction) => { if (transaction.source) { sourcesSet.add(transaction.source); } }); return Array.from(sourcesSet); }, [transactions]); // Handle filter changes const handleTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => { const type = e.target.value; const newTypes = e.target.checked ? [...filters.types, type] : filters.types.filter((t) => t !== type); onFilterChange({ ...filters, types: newTypes, }); }; const handleSourceChange = (e: React.ChangeEvent<HTMLInputElement>) => { const source = e.target.value; const newSources = e.target.checked ? [...filters.sources, source] : filters.sources.filter((s) => s !== source); onFilterChange({ ...filters, sources: newSources, }); }; const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => { onFilterChange({ ...filters, searchText: e.target.value, }); }; const handleAmountMinChange = (e: React.ChangeEvent<HTMLInputElement>) => { const min = e.target.value === "" ? null : parseFloat(e.target.value); onFilterChange({ ...filters, amountRange: [min, filters.amountRange[1]], }); }; const handleAmountMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => { const max = e.target.value === "" ? null : parseFloat(e.target.value); onFilterChange({ ...filters, amountRange: [filters.amountRange[0], max], }); }; const handleClearFilters = () => { onFilterChange({ types: [], sources: [], // Keep this to maintain interface compatibility timeRange: [null, null], searchText: "", amountRange: [null, null], }); }; return ( <div className="p-4"> <div className="grid grid-cols-1 lg:grid-cols-4 gap-4"> {/* Search filter */} <div className="lg:col-span-2"> <label htmlFor="search-filter" className="block text-xs font-medium mb-1" style={{ color: "var(--color-text)" }} > Search </label> <input type="text" id="search-filter" value={filters.searchText} onChange={handleSearchChange} placeholder="Search description..." className="w-full p-2 text-sm border rounded-md" style={{ backgroundColor: "var(--color-bg)", borderColor: "var(--color-border)", color: "var(--color-text)", }} /> </div> {/* Amount range */} <div className="lg:col-span-2"> <label className="block text-xs font-medium mb-1" style={{ color: "var(--color-text)" }} > Amount Range </label> <div className="flex space-x-2"> <input type="number" value={ filters.amountRange[0] === null ? "" : filters.amountRange[0] } onChange={handleAmountMinChange} placeholder="Min" className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" /> <input type="number" value={ filters.amountRange[1] === null ? "" : filters.amountRange[1] } onChange={handleAmountMaxChange} placeholder="Max" className="w-full p-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" /> </div> </div> </div> <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"> {/* Transaction direction filter (Income/Expense) */} <div> <label className="block text-xs font-medium mb-2" style={{ color: "var(--color-text)" }} > Transaction Direction </label> <div className="flex flex-wrap gap-2"> {/* Income filter */} {hasIncomeTransactions && ( <div className="flex items-center"> <input id="filter-income" name="filter-income" type="checkbox" value="income" checked={filters.types.includes("income")} onChange={(e) => { const newTypes = e.target.checked ? [...filters.types, "income"] : filters.types.filter((t) => t !== "income"); onFilterChange({ ...filters, types: newTypes, }); }} className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" /> <label htmlFor="filter-income" className="ml-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs px-2 py-1 rounded border border-green-200 dark:border-green-800/30" > Income </label> </div> )} {/* Expense filter */} <div className="flex items-center ml-2"> <input id="filter-expense" name="filter-expense" type="checkbox" value="expense" checked={filters.types.includes("expense")} onChange={(e) => { // Special handling for expense filter - will filter by amount < 0 const newTypes = e.target.checked ? [...filters.types, "expense"] : filters.types.filter((t) => t !== "expense"); onFilterChange({ ...filters, types: newTypes, }); }} className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded" /> <label htmlFor="filter-expense" className="ml-2 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 text-xs px-2 py-1 rounded border border-red-200 dark:border-red-800/30" > Expense </label> </div> </div> </div> {/* Transaction types filter */} <div> <label className="block text-xs font-medium mb-2" style={{ color: "var(--color-text)" }} > Transaction Types </label> <div className="flex flex-wrap gap-2"> {/* Invoice Filter */} {hasRealInvoices && ( <div className="flex items-center"> <input id="filter-real" name="filter-real" type="checkbox" value="real_invoice" checked={filters.types.includes("real_invoice")} onChange={(e) => { const newTypes = e.target.checked ? [...filters.types, "real_invoice"] : filters.types.filter((t) => t !== "real_invoice"); onFilterChange({ ...filters, types: newTypes, }); }} className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" /> <label htmlFor="filter-real" className="ml-2 bg-green-200 text-green-800 dark:bg-green-900/50 dark:text-green-200 text-xs px-2 py-1 rounded border border-green-300 dark:border-green-800/50" > Invoice </label> </div> )} {/* Projected Income Filter */} {hasProjectedIncome && ( <div className="flex items-center ml-2"> <input id="filter-projected" name="filter-projected" type="checkbox" value="projected_income" checked={filters.types.includes("projected_income")} onChange={(e) => { const newTypes = e.target.checked ? [...filters.types, "projected_income"] : filters.types.filter((t) => t !== "projected_income"); onFilterChange({ ...filters, types: newTypes, }); }} className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded" /> <label htmlFor="filter-projected" className="ml-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs px-2 py-1 rounded border border-green-200 dark:border-green-800/30" > <svg className="w-3 h-3 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" /> </svg> Projected Income </label> </div> )} {/* Custom Expense types */} {transactionTypes.map((type) => { // Skip non-custom expense types as they are handled separately if ( !type.startsWith("custom_expense_") || type === "income" || type === "expense" || type === "invoice" ) { return null; } // Determine visual appearance based on transaction type let typeColor = "bg-gray-100 text-gray-800"; if (type.includes("Monthly Payroll")) { typeColor = "bg-blue-100 text-blue-800"; } else if (type.includes("Software")) { typeColor = "bg-purple-100 text-purple-800"; } else if (type.includes("Superannuation")) { typeColor = "bg-yellow-100 text-yellow-800"; } else if (type.includes("Insurances")) { typeColor = "bg-magenta-100 text-magenta-800"; } else if (type.includes("Taxes")) { typeColor = "bg-blue-100 text-blue-800"; } else if (type.includes("Fees")) { typeColor = "bg-red-100 text-red-800"; } // Format display name const displayName = type.replace("custom_expense_", ""); return ( <div key={type} className="flex items-center mt-2"> <input id={`type-${type}`} name={`type-${type}`} type="checkbox" value={type} checked={filters.types.includes(type)} onChange={handleTypeChange} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" /> <label htmlFor={`type-${type}`} className={`ml-2 ${typeColor} text-xs px-2 py-1 rounded border border-current border-opacity-20 dark:border-opacity-30`} > {displayName} </label> </div> ); })} {/* Other transaction types if needed */} {transactionTypes.map((type) => { // Only include specialized types that aren't already handled if ( type === "income" || type === "expense" || type === "invoice" || type.startsWith("custom_expense_") ) { return null; } // Determine visual appearance based on transaction type let typeColor = "bg-gray-100 text-gray-800"; if (type === "repeating_bill") { typeColor = "bg-orange-100 text-orange-800"; } else if (type === "fixed_expense") { typeColor = "bg-purple-100 text-purple-800"; } return ( <div key={type} className="flex items-center mt-2"> <input id={`type-${type}`} name={`type-${type}`} type="checkbox" value={type} checked={filters.types.includes(type)} onChange={handleTypeChange} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" /> <label htmlFor={`type-${type}`} className={`ml-2 ${typeColor} text-xs px-2 py-1 rounded border border-current border-opacity-20 dark:border-opacity-30`} > {type} </label> </div> ); })} </div> </div> </div> </div> ); }; export default TransactionFilters; 