import { Transaction } from '../../../../types/financial'; /** * Interface for transaction filters */ export interface TransactionFilters { types: string[]; sources: string[]; timeRange: [Date | null, Date | null]; searchText: string; amountRange: [number | null, number | null]; } /** * Extended transaction with processed display properties */ export interface ProcessedTransaction extends Transaction { formattedDescription: string; isRealInvoice: boolean; isProjectedInvoice: boolean; isExpense: boolean; typeColor: string; percentChange: number; runningBalance: number; } /** * Return type for the useTransactions hook */ export interface TransactionsHookResult { filteredTransactions: ProcessedTransaction[]; filters: TransactionFilters; setFilters: (filters: TransactionFilters) => void; filtersExpanded: boolean; setFiltersExpanded: (expanded: boolean) => void; totalTransactions: number; } /** * Props for the PercentageChange component */ export interface PercentageChangeProps { percentChange: number; } /** * Props for the TransactionCard component (mobile view) */ export interface TransactionCardProps { transaction: ProcessedTransaction; isHighlighted: boolean; index: number; } /** * Props for the TransactionRow component (desktop view) */ export interface TransactionRowProps { transaction: ProcessedTransaction; isHighlighted: boolean; index: number; ref?: React.Ref<HTMLTableRowElement>; } /** * Props for the FilterHeader component */ export interface FilterHeaderProps { filtersExpanded: boolean; toggleFilters: () => void; filters: TransactionFilters; clearFilters: () => void; }