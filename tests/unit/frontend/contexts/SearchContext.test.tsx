import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { SearchContext, SearchProvider } from '../../../../src/frontend/contexts/SearchContext';
import * as api from '../../../../src/frontend/api/crm';

jest.mock('../../../../src/frontend/api/crm');

describe('SearchContext', () => {
  const mockSearchResults = {
    companies: [
      { id: '1', name: 'Acme Corp', type: 'company' }
    ],
    contacts: [
      { id: '2', name: '<PERSON>', email: '<EMAIL>', type: 'contact' }
    ],
    deals: [
      { id: '3', name: 'Big Deal', amount: 50000, type: 'deal' }
    ]
  };

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    (api.searchAll as jest.Mock).mockResolvedValue(mockSearchResults);
  });

  it('should provide search context to children', () => {
    const TestComponent = () => {
      const context = React.useContext(SearchContext);
      return (
        <div>
          <span>Has Context: {context ? 'Yes' : 'No'}</span>
          <span>Loading: {context.isSearching.toString()}</span>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    expect(screen.getByText('Has Context: Yes')).toBeInTheDocument();
    expect(screen.getByText('Loading: false')).toBeInTheDocument();
  });

  it('should perform search', async () => {
    const TestComponent = () => {
      const { search, results, isSearching } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => search('test query')}>Search</button>
          <span>Searching: {isSearching.toString()}</span>
          {results && (
            <div>
              <span>Companies: {results.companies.length}</span>
              <span>Contacts: {results.contacts.length}</span>
              <span>Deals: {results.deals.length}</span>
            </div>
          )}
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Search').click();
    });

    expect(screen.getByText('Searching: true')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Searching: false')).toBeInTheDocument();
      expect(screen.getByText('Companies: 1')).toBeInTheDocument();
      expect(screen.getByText('Contacts: 1')).toBeInTheDocument();
      expect(screen.getByText('Deals: 1')).toBeInTheDocument();
    });

    expect(api.searchAll).toHaveBeenCalledWith('test query');
  });

  it('should debounce search requests', async () => {
    jest.useFakeTimers();

    const TestComponent = () => {
      const { search } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => {
            search('query1');
            search('query2');
            search('query3');
          }}>
            Multiple Searches
          </button>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Multiple Searches').click();
    });

    expect(api.searchAll).not.toHaveBeenCalled();

    act(() => {
      jest.advanceTimersByTime(300);
    });

    await waitFor(() => {
      expect(api.searchAll).toHaveBeenCalledTimes(1);
      expect(api.searchAll).toHaveBeenCalledWith('query3');
    });

    jest.useRealTimers();
  });

  it('should handle search errors', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    (api.searchAll as jest.Mock).mockRejectedValue(new Error('Search failed'));

    const TestComponent = () => {
      const { search, error } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => search('error query')}>Search</button>
          {error && <span>Error: {error}</span>}
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Search').click();
    });

    await waitFor(() => {
      expect(screen.getByText('Error: Search failed')).toBeInTheDocument();
    });

    consoleSpy.mockRestore();
  });

  it('should clear search results', async () => {
    const TestComponent = () => {
      const { search, clearResults, results } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => search('test')}>Search</button>
          <button onClick={clearResults}>Clear</button>
          <span>Has Results: {results ? 'Yes' : 'No'}</span>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Search').click();
    });

    await waitFor(() => {
      expect(screen.getByText('Has Results: Yes')).toBeInTheDocument();
    });

    act(() => {
      screen.getByText('Clear').click();
    });

    expect(screen.getByText('Has Results: No')).toBeInTheDocument();
  });

  it('should manage recent searches', () => {
    const TestComponent = () => {
      const { addRecentSearch, recentSearches, clearRecentSearches } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => addRecentSearch('search1')}>Add Search 1</button>
          <button onClick={() => addRecentSearch('search2')}>Add Search 2</button>
          <button onClick={clearRecentSearches}>Clear Recent</button>
          <div>
            {recentSearches.map((search, index) => (
              <span key={index}>Recent: {search}</span>
            ))}
          </div>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Add Search 1').click();
    });

    expect(screen.getByText('Recent: search1')).toBeInTheDocument();

    act(() => {
      screen.getByText('Add Search 2').click();
    });

    expect(screen.getByText('Recent: search2')).toBeInTheDocument();

    act(() => {
      screen.getByText('Clear Recent').click();
    });

    expect(screen.queryByText(/Recent:/)).not.toBeInTheDocument();
  });

  it('should persist recent searches to localStorage', () => {
    const TestComponent = () => {
      const { addRecentSearch } = React.useContext(SearchContext);
      
      return (
        <button onClick={() => addRecentSearch('persistent search')}>
          Add Search
        </button>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Add Search').click();
    });

    const stored = localStorage.getItem('recentSearches');
    expect(stored).toBeTruthy();
    expect(JSON.parse(stored!)).toContain('persistent search');
  });

  it('should load recent searches from localStorage', () => {
    localStorage.setItem('recentSearches', JSON.stringify(['stored search']));

    const TestComponent = () => {
      const { recentSearches } = React.useContext(SearchContext);
      
      return (
        <div>
          {recentSearches.map((search, index) => (
            <span key={index}>Loaded: {search}</span>
          ))}
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    expect(screen.getByText('Loaded: stored search')).toBeInTheDocument();
  });

  it('should limit recent searches to 10', () => {
    const TestComponent = () => {
      const { addRecentSearch, recentSearches } = React.useContext(SearchContext);
      
      React.useEffect(() => {
        for (let i = 1; i <= 12; i++) {
          addRecentSearch(`search${i}`);
        }
      }, []);

      return (
        <div>
          <span>Count: {recentSearches.length}</span>
          <span>First: {recentSearches[0]}</span>
          <span>Last: {recentSearches[recentSearches.length - 1]}</span>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    expect(screen.getByText('Count: 10')).toBeInTheDocument();
    expect(screen.getByText('First: search12')).toBeInTheDocument(); // Most recent
    expect(screen.getByText('Last: search3')).toBeInTheDocument(); // Oldest kept
  });

  it('should not add duplicate recent searches', () => {
    const TestComponent = () => {
      const { addRecentSearch, recentSearches } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => {
            addRecentSearch('duplicate');
            addRecentSearch('duplicate');
          }}>
            Add Duplicate
          </button>
          <span>Count: {recentSearches.length}</span>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Add Duplicate').click();
    });

    expect(screen.getByText('Count: 1')).toBeInTheDocument();
  });

  it('should handle empty search query', async () => {
    const TestComponent = () => {
      const { search, results } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => search('')}>Empty Search</button>
          <span>Results: {results ? 'Yes' : 'No'}</span>
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('Empty Search').click();
    });

    await waitFor(() => {
      expect(screen.getByText('Results: No')).toBeInTheDocument();
    });

    expect(api.searchAll).not.toHaveBeenCalled();
  });

  it('should cancel previous search when new search starts', async () => {
    let resolveFirst: any;
    const firstPromise = new Promise(resolve => { resolveFirst = resolve; });
    
    (api.searchAll as jest.Mock)
      .mockReturnValueOnce(firstPromise)
      .mockResolvedValueOnce(mockSearchResults);

    const TestComponent = () => {
      const { search, results } = React.useContext(SearchContext);
      
      return (
        <div>
          <button onClick={() => search('first')}>First Search</button>
          <button onClick={() => search('second')}>Second Search</button>
          {results && <span>Results from: {results.query}</span>}
        </div>
      );
    };

    render(
      <SearchProvider>
        <TestComponent />
      </SearchProvider>
    );

    act(() => {
      screen.getByText('First Search').click();
    });

    act(() => {
      screen.getByText('Second Search').click();
    });

    // Resolve first search
    act(() => {
      resolveFirst({ ...mockSearchResults, query: 'first' });
    });

    await waitFor(() => {
      expect(screen.getByText('Results from: second')).toBeInTheDocument();
    });
  });
});