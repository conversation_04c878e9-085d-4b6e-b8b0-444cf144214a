/**
 * Enrichment API Client Tests
 * 
 * Tests for the frontend enrichment API client including:
 * - API endpoint calls
 * - Request/response handling
 * - Error handling
 * - Parameter validation
 * - Response parsing
 */

import * as enrichmentApi from '../../../../src/frontend/api/enrichment';
import * as utils from '../../../../src/frontend/api/utils';

// Mock the utils module
jest.mock('../../../../src/frontend/api/utils');
const mockUtils = utils as jest.Mocked<typeof utils>;

describe('Enrichment API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('enrichCompany', () => {
    it('should call the correct endpoint with company ID', async () => {
      const mockResponse = {
        company: { id: 'comp-123', name: 'Test Company' },
        enrichment: [],
        results: [
          {
            source: 'abn_lookup',
            success: true,
            confidence: 0.95,
            data: { abn: '12 ***********' }
          }
        ]
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.enrichCompany('comp-123');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123', {
        method: 'POST',
        body: JSON.stringify({ sources: undefined })
      });

      expect(result).toEqual(mockResponse);
    });

    it('should include sources in request body when provided', async () => {
      const mockResponse = {
        company: { id: 'comp-123' },
        enrichment: [],
        results: []
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.enrichCompany('comp-123', ['abn_lookup', 'clearbit']);

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123', {
        method: 'POST',
        body: JSON.stringify({ sources: ['abn_lookup', 'clearbit'] })
      });
    });

    it('should handle API errors', async () => {
      const apiError = new Error('API Error: Company not found');
      mockUtils.fetchFromApi.mockRejectedValue(apiError);

      await expect(enrichmentApi.enrichCompany('non-existent'))
        .rejects.toThrow('API Error: Company not found');
    });

    it('should handle empty sources array', async () => {
      const mockResponse = {
        company: { id: 'comp-123' },
        enrichment: [],
        results: []
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.enrichCompany('comp-123', []);

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123', {
        method: 'POST',
        body: JSON.stringify({ sources: [] })
      });
    });

    it('should return enrichment results with correct types', async () => {
      const mockResponse = {
        company: {
          id: 'comp-123',
          name: 'Test Company',
          lastEnrichedAt: '2024-01-01T12:00:00Z'
        },
        enrichment: [
          {
            id: 'enrich-1',
            source: 'abn_lookup',
            data: {
              abn: '12 ***********',
              abnStatus: 'Active',
              entityType: 'Australian Private Company'
            },
            confidence: 0.95,
            enrichedAt: '2024-01-01T12:00:00Z'
          }
        ],
        results: [
          {
            source: 'abn_lookup',
            success: true,
            confidence: 0.95,
            data: {
              abn: '12 ***********',
              abnStatus: 'Active'
            }
          }
        ]
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.enrichCompany('comp-123');

      expect(result.company).toBeDefined();
      expect(result.enrichment).toBeInstanceOf(Array);
      expect(result.results).toBeInstanceOf(Array);
      expect(result.results[0]).toHaveProperty('source');
      expect(result.results[0]).toHaveProperty('success');
      expect(result.results[0]).toHaveProperty('confidence');
    });
  });

  describe('getCompanyEnrichment', () => {
    it('should call the correct endpoint without source filter', async () => {
      const mockResponse = {
        enrichment: [
          {
            id: 'enrich-1',
            source: 'abn_lookup',
            data: { abn: '12 ***********' },
            confidence: 0.95
          }
        ]
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.getCompanyEnrichment('comp-123');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123');
      expect(result).toEqual(mockResponse);
    });

    it('should include source parameter when provided', async () => {
      const mockResponse = {
        enrichment: [
          {
            id: 'enrich-1',
            source: 'abn_lookup',
            data: { abn: '12 ***********' }
          }
        ]
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.getCompanyEnrichment('comp-123', 'abn_lookup');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123?source=abn_lookup');
    });

    it('should handle multiple source filters correctly', async () => {
      const mockResponse = { enrichment: [] };
      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.getCompanyEnrichment('comp-123', 'clearbit');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123?source=clearbit');
    });

    it('should handle empty enrichment response', async () => {
      const mockResponse = { enrichment: [] };
      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.getCompanyEnrichment('comp-123');

      expect(result.enrichment).toEqual([]);
    });

    it('should handle API errors gracefully', async () => {
      const apiError = new Error('Network error');
      mockUtils.fetchFromApi.mockRejectedValue(apiError);

      await expect(enrichmentApi.getCompanyEnrichment('comp-123'))
        .rejects.toThrow('Network error');
    });

    it('should handle special characters in company ID', async () => {
      const mockResponse = { enrichment: [] };
      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.getCompanyEnrichment('comp-123-test!@#');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123-test!@#');
    });
  });

  describe('checkCompanyEnrichmentStatus', () => {
    it('should call the correct endpoint', async () => {
      const mockResponse = {
        needsEnrichment: true,
        lastEnrichedAt: null,
        enrichmentStatus: null
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.checkCompanyEnrichmentStatus('comp-123');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123/check');
      expect(result).toEqual(mockResponse);
    });

    it('should handle companies that need enrichment', async () => {
      const mockResponse = {
        needsEnrichment: true,
        lastEnrichedAt: null,
        enrichmentStatus: null
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.checkCompanyEnrichmentStatus('comp-123');

      expect(result.needsEnrichment).toBe(true);
      expect(result.lastEnrichedAt).toBeNull();
      expect(result.enrichmentStatus).toBeNull();
    });

    it('should handle companies that do not need enrichment', async () => {
      const mockResponse = {
        needsEnrichment: false,
        lastEnrichedAt: '2024-01-01T12:00:00Z',
        enrichmentStatus: {
          lastEnriched: '2024-01-01T12:00:00Z',
          sources: {
            abn_lookup: {
              success: true,
              confidence: 0.95
            }
          }
        }
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.checkCompanyEnrichmentStatus('comp-123');

      expect(result.needsEnrichment).toBe(false);
      expect(result.lastEnrichedAt).toBe('2024-01-01T12:00:00Z');
      expect(result.enrichmentStatus).toBeDefined();
      expect(result.enrichmentStatus?.sources).toBeDefined();
    });

    it('should handle API errors for status check', async () => {
      const apiError = new Error('Company not found');
      mockUtils.fetchFromApi.mockRejectedValue(apiError);

      await expect(enrichmentApi.checkCompanyEnrichmentStatus('non-existent'))
        .rejects.toThrow('Company not found');
    });

    it('should return proper type information', async () => {
      const mockResponse = {
        needsEnrichment: false,
        lastEnrichedAt: '2024-01-01T12:00:00Z',
        enrichmentStatus: {
          lastEnriched: '2024-01-01T12:00:00Z',
          sources: {
            abn_lookup: { success: true, confidence: 0.95 }
          }
        }
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.checkCompanyEnrichmentStatus('comp-123');

      expect(typeof result.needsEnrichment).toBe('boolean');
      expect(typeof result.lastEnrichedAt).toBe('string');
      expect(typeof result.enrichmentStatus).toBe('object');
    });
  });

  describe('getEnrichmentStats', () => {
    it('should call the correct endpoint', async () => {
      const mockResponse = {
        overview: {
          enrichedCompanies: 150,
          totalEnrichments: 200,
          avgConfidence: 0.85,
          lastEnrichmentAt: '2024-01-01T12:00:00Z'
        },
        bySource: [
          {
            source: 'abn_lookup',
            count: 120,
            avgConfidence: 0.9
          },
          {
            source: 'clearbit',
            count: 80,
            avgConfidence: 0.8
          }
        ]
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.getEnrichmentStats();

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/stats');
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty stats response', async () => {
      const mockResponse = {
        overview: {
          enrichedCompanies: 0,
          totalEnrichments: 0,
          avgConfidence: 0,
          lastEnrichmentAt: null
        },
        bySource: []
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.getEnrichmentStats();

      expect(result.overview.enrichedCompanies).toBe(0);
      expect(result.bySource).toEqual([]);
    });

    it('should handle API errors for stats', async () => {
      const apiError = new Error('Internal server error');
      mockUtils.fetchFromApi.mockRejectedValue(apiError);

      await expect(enrichmentApi.getEnrichmentStats())
        .rejects.toThrow('Internal server error');
    });

    it('should return stats with correct structure', async () => {
      const mockResponse = {
        overview: {
          enrichedCompanies: 100,
          totalEnrichments: 150,
          avgConfidence: 0.85,
          lastEnrichmentAt: '2024-01-01T12:00:00Z'
        },
        bySource: [
          {
            source: 'abn_lookup',
            count: 100,
            avgConfidence: 0.9
          }
        ]
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const result = await enrichmentApi.getEnrichmentStats();

      expect(result).toHaveProperty('overview');
      expect(result).toHaveProperty('bySource');
      expect(result.overview).toHaveProperty('enrichedCompanies');
      expect(result.overview).toHaveProperty('totalEnrichments');
      expect(result.overview).toHaveProperty('avgConfidence');
      expect(result.bySource).toBeInstanceOf(Array);
    });
  });

  describe('Error handling', () => {
    it('should propagate network errors', async () => {
      const networkError = new Error('fetch failed');
      mockUtils.fetchFromApi.mockRejectedValue(networkError);

      await expect(enrichmentApi.enrichCompany('comp-123'))
        .rejects.toThrow('fetch failed');
    });

    it('should handle timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      mockUtils.fetchFromApi.mockRejectedValue(timeoutError);

      await expect(enrichmentApi.getCompanyEnrichment('comp-123'))
        .rejects.toThrow('Request timeout');
    });

    it('should handle malformed responses', async () => {
      // Mock a response that doesn\'t match expected structure
      mockUtils.fetchFromApi.mockResolvedValue(null);

      const result = await enrichmentApi.getCompanyEnrichment('comp-123');

      expect(result).toBeNull();
    });

    it('should handle HTTP error codes', async () => {
      const httpError = new Error('HTTP 404: Not Found');
      mockUtils.fetchFromApi.mockRejectedValue(httpError);

      await expect(enrichmentApi.checkCompanyEnrichmentStatus('comp-123'))
        .rejects.toThrow('HTTP 404: Not Found');
    });
  });

  describe('Parameter validation', () => {
    it('should handle empty company ID', async () => {
      const mockResponse = { enrichment: [] };
      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.getCompanyEnrichment('');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/');
    });

    it('should handle undefined sources', async () => {
      const mockResponse = {
        company: { id: 'comp-123' },
        enrichment: [],
        results: []
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.enrichCompany('comp-123', undefined);

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123', {
        method: 'POST',
        body: JSON.stringify({ sources: undefined })
      });
    });

    it('should handle null source parameter', async () => {
      const mockResponse = { enrichment: [] };
      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.getCompanyEnrichment('comp-123', null as any);

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123');
    });
  });

  describe('Request serialization', () => {
    it('should properly serialize complex source arrays', async () => {
      const mockResponse = {
        company: { id: 'comp-123' },
        enrichment: [],
        results: []
      };

      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      const complexSources = ['abn_lookup', 'clearbit', 'apollo', 'manual'];
      await enrichmentApi.enrichCompany('comp-123', complexSources);

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123', {
        method: 'POST',
        body: JSON.stringify({ sources: complexSources })
      });
    });

    it('should handle special characters in source names', async () => {
      const mockResponse = { enrichment: [] };
      mockUtils.fetchFromApi.mockResolvedValue(mockResponse);

      await enrichmentApi.getCompanyEnrichment('comp-123', 'custom-source_v2');

      expect(mockUtils.fetchFromApi).toHaveBeenCalledWith('/api/enrichment/company/comp-123?source=custom-source_v2');
    });
  });
});