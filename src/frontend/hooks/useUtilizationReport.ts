/**
 * Custom hook for utilization report data fetching and management
 */

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getStaffUtilization } from '../api/harvest';
import { StaffUtilization } from '../../services/harvest/time-report-service';
import { format } from 'date-fns';
import { useDateRange, TIME_PERIODS, TimePeriod } from './useDateRange';

/**
 * Hook for fetching utilization data
 */
export function useUtilizationData(startDate: Date, endDate: Date, enabled: boolean = true) {
  return useQuery<StaffUtilization[]>({
    queryKey: ['staff-utilization', format(startDate, 'yyyy-MM-dd'), format(endDate, 'yyyy-MM-dd')],
    queryFn: async () => {
      const from = format(startDate, 'yyyy-MM-dd');
      const to = format(endDate, 'yyyy-MM-dd');
      console.log(`Fetching utilization data for period: ${from} to ${to}`);
      return getStaffUtilization(from, to);
    },
    enabled: enabled && startDate && endDate,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  });
}

/**
 * Hook for managing utilization report state and data
 */
export function useUtilizationReport() {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>(TIME_PERIODS.WEEK);
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [excludeLeave, setExcludeLeave] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Calculate current date range based on time period
  const dateRange = useDateRange(timePeriod, customStartDate, customEndDate);

  // Fetch utilization data
  const isCustomDateValid = timePeriod !== TIME_PERIODS.CUSTOM || (customStartDate && customEndDate);
  const {
    data: utilizationData = [],
    isLoading,
    error,
    refetch
  } = useUtilizationData(dateRange.startDate, dateRange.endDate, isCustomDateValid);

  return {
    // State
    timePeriod,
    customStartDate,
    customEndDate,
    excludeLeave,
    isDropdownOpen,
    
    // Date range
    ...dateRange,
    
    // Data
    utilizationData,
    isLoading,
    error,
    
    // Actions
    setTimePeriod,
    setCustomStartDate,
    setCustomEndDate,
    setExcludeLeave,
    setIsDropdownOpen,
    refetch
  };
}