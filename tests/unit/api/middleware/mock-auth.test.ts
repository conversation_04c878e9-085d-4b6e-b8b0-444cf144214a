import { Request, Response, NextFunction } from 'express';
import { mockAuthMiddleware } from '../../../../src/api/middleware/mock-auth';

describe('Mock Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let originalEnv: string | undefined;

  beforeEach(() => {
    mockRequest = {
      session: {}
    };
    mockResponse = {};
    mockNext = jest.fn();
    originalEnv = process.env.MOCK_AUTH;
  });

  afterEach(() => {
    process.env.MOCK_AUTH = originalEnv;
  });

  it('should add mock user when MOCK_AUTH is enabled', () => {
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.user).toEqual({
      id: 'mock-user-id',
      email: '<EMAIL>',
      name: 'Mock User',
      role: 'admin',
      permissions: ['all'],
      isMock: true
    });
    expect(mockRequest.session?.xeroTokenSet).toBeDefined();
    expect(mockNext).toHaveBeenCalled();
  });

  it('should not add mock user when MOCK_AUTH is disabled', () => {
    process.env.MOCK_AUTH = 'false';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.user).toBeUndefined();
    expect(mockRequest.session?.xeroTokenSet).toBeUndefined();
    expect(mockNext).toHaveBeenCalled();
  });

  it('should not add mock user when MOCK_AUTH is not set', () => {
    delete process.env.MOCK_AUTH;

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.user).toBeUndefined();
    expect(mockNext).toHaveBeenCalled();
  });

  it('should add mock Xero token set', () => {
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    const tokenSet = mockRequest.session?.xeroTokenSet;
    expect(tokenSet).toEqual({
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_at: expect.any(Number),
      token_type: 'Bearer',
      scope: 'accounting.transactions accounting.contacts accounting.settings offline_access'
    });
  });

  it('should set token expiry to future date', () => {
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    const tokenSet = mockRequest.session?.xeroTokenSet;
    const expiresAt = tokenSet?.expires_at || 0;
    const now = Math.floor(Date.now() / 1000);

    expect(expiresAt).toBeGreaterThan(now);
    expect(expiresAt).toBeLessThanOrEqual(now + 1800); // 30 minutes
  });

  it('should add mock HubSpot credentials', () => {
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.hubspotTokens).toEqual({
      access_token: 'mock-hubspot-access-token',
      refresh_token: 'mock-hubspot-refresh-token',
      expires_at: expect.any(Number)
    });
  });

  it('should add mock active tenants', () => {
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.activeTenants).toEqual([{
      tenantId: 'mock-tenant-id',
      tenantName: 'Mock Company',
      tenantType: 'ORGANISATION'
    }]);
  });

  it('should not override existing session data', () => {
    process.env.MOCK_AUTH = 'true';
    mockRequest.session = {
      existingData: 'should-remain',
      user: { id: 'real-user', name: 'Real User' }
    };

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session.existingData).toBe('should-remain');
    expect(mockRequest.session.user.isMock).toBe(true);
  });

  it('should handle missing session object', () => {
    process.env.MOCK_AUTH = 'true';
    mockRequest.session = undefined;

    expect(() => {
      mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);
    }).not.toThrow();

    expect(mockNext).toHaveBeenCalled();
  });

  it('should log when mock auth is activated', () => {
    process.env.MOCK_AUTH = 'true';
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Mock auth enabled'));
    consoleSpy.mockRestore();
  });

  it('should work in development environment', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.user?.isMock).toBe(true);
    process.env.NODE_ENV = originalNodeEnv;
  });

  it('should be disabled in production even if MOCK_AUTH is set', () => {
    const originalNodeEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';
    process.env.MOCK_AUTH = 'true';

    mockAuthMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.session?.user).toBeUndefined();
    process.env.NODE_ENV = originalNodeEnv;
  });
});