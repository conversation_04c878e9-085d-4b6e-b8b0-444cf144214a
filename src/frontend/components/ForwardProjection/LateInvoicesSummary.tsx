import { useState, useEffect } from "react"; import { getLateInvoices, LateInvoicesResponse, LateInvoice, } from "../../api/harvest"; import { formatCurrency } from "../../utils/format"; import { formatDate } from "./utils"; interface LateInvoicesSummaryProps { className?: string; } const LateInvoicesSummary = ({ className = "" }: LateInvoicesSummaryProps) => { const [lateInvoicesData, setLateInvoicesData] = useState<LateInvoicesResponse | null>(null); const [loading, setLoading] = useState<boolean>(true); const [error, setError] = useState<string | null>(null); const [expanded, setExpanded] = useState<boolean>(false); useEffect(() => { const fetchLateInvoices = async () => { setLoading(true); setError(null); try { const data = await getLateInvoices(); setLateInvoicesData(data); } catch (err: any) { console.error("Error fetching late invoices:", err); setError(err.message || "Failed to load late invoices"); } finally { setLoading(false); } }; fetchLateInvoices(); }, []); // Always render the component, even with no late invoices const hasNoLateInvoices = !loading && (!lateInvoicesData || lateInvoicesData.count === 0) && !error; // Sort invoices by days past due (most overdue first) const sortedInvoices = lateInvoicesData?.invoices ? [...lateInvoicesData.invoices].sort( (a, b) => b.daysPastDue - a.daysPastDue ) : []; return ( <div className={`relative group ${className}`}> {/* Beautiful Rounded Rectangle Summary */} <button onClick={() => setExpanded(!expanded)} className={` flex items-center justify-between h-full min-h-[40px] px-2 py-1 rounded-lg w-full transition-all duration-200 ${ hasNoLateInvoices ? "bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200 dark:/40 hover:shadow-md border-green-200 dark:border-green-800/50" : expanded ? "bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-200 shadow-md border-blue-200 dark:border-blue-800/50" : "bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 dark:/40 hover:shadow-md border-blue-200 dark:border-blue-800/50" } border focus:outline-none focus:ring-2 ${ hasNoLateInvoices ? "focus:ring-green-500/40 dark:focus:ring-green-600/40" : "focus:ring-blue-500/40 dark:focus:ring-blue-600/40" } `} > <div className="flex items-center"> {loading ? ( <span className="text-xs font-medium animate-pulse"> Checking... </span> ) : error ? ( <span className="text-xs font-medium text-red-600 dark:text-red-400"> Error loading invoices </span> ) : hasNoLateInvoices ? ( <> <svg className="w-4 h-4 mr-1 flex-shrink-0 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /> </svg> <span className="text-xs font-medium whitespace-nowrap mr-2" style={{ fontSize: "12px" }} > No late invoices </span> <span className="font-bold whitespace-nowrap text-green-700 dark:text-green-300" style={{ fontSize: "12px" }} > $0 </span> </> ) : ( <> <span className="text-xs font-medium whitespace-nowrap mr-2" style={{ fontSize: "12px" }} > {lateInvoicesData?.count}{" "} {lateInvoicesData?.count === 1 ? "late invoice" : "late invoices"} </span> <span className={`font-bold whitespace-nowrap ${ expanded ? "text-blue-700 dark:text-blue-300" : "text-blue-700 dark:text-blue-300" }`} style={{ fontSize: "12px" }} > {formatCurrency(lateInvoicesData?.totalAmount || 0)} </span> </> )} </div> {/* Expand/collapse icon */} <svg className={`h-5 w-5 transform transition-transform duration-200 ${ expanded ? "rotate-180" : "" }`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" > <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /> </svg> </button> {/* Dropdown panel with beautiful styling - without fixed height */} {expanded && ( <div className="absolute top-full left-0 lg:left-auto lg:right-0 mt-1 z-10 w-full min-w-[300px] sm:min-w-[400px] md:min-w-[460px] max-w-md transform transition-all duration-200 ease-out scale-100 opacity-100 origin-top-right"> <div className={`overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5 dark:ring-white/10 bg-white dark:bg-gray-800 dark:shadow-xl dark:shadow-gray-900/30 ${ hasNoLateInvoices ? "border border-green-200 dark:border-green-800/50" : "" }`} > {/* Panel Header */} <div className={`${ hasNoLateInvoices ? "bg-green-50 dark:bg-green-900/20" : "bg-blue-50 dark:bg-blue-900/30" } px-4 py-2 border-b ${ hasNoLateInvoices ? "border-green-100 dark:border-green-800/30" : "border-blue-100 dark:border-blue-800/30" }`} > <h3 className={`text-sm font-semibold ${ hasNoLateInvoices ? "text-green-800 dark:text-green-200" : "text-blue-800 dark:text-blue-200" }`} > Overdue Invoices </h3> <p className={`text-xs ${ hasNoLateInvoices ? "text-green-600 dark:text-green-400" : "text-blue-600 dark:text-blue-400" }`} > {hasNoLateInvoices ? "All invoices are paid on time" : "Not yet reflected in your bank balance"} </p> </div> {/* Content area */} {hasNoLateInvoices ? ( <div className="bg-white dark:bg-gray-800 p-8 text-center"> <svg className="w-12 h-12 mx-auto mb-3 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /> </svg> <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"> No overdue invoices </p> <p className="text-xs text-gray-500 dark:text-gray-400"> All your clients are paying on time </p> </div> ) : ( <> {/* Invoices List - removed max height constraint */} <div className="overflow-y-auto w-full"> <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700"> <thead className="btn-modern--secondary"dark:bg-gray-800 sticky top-0"> <tr> <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-5/12" > Client </th> <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-4/12" > Invoice # </th> <th scope="col" className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-3/12" > Amount </th> </tr> </thead> <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700"> {sortedInvoices.map((invoice) => ( <tr key={invoice.id} className="hover:btn-modern--secondary"dark:hover:bg-gray-700" > <td className="px-4 py-2"> <div className="flex flex-col"> <span className="text-xs font-medium text-gray-900 dark:text-gray-200 truncate max-w-[180px]" title={invoice.client} > {invoice.client} </span> <span className="text-xs font-medium text-red-600 dark:text-red-300"> {invoice.daysPastDue}{" "} {invoice.daysPastDue === 1 ? "day" : "days"}{" "} late </span> </div> </td> <td className="px-3 py-2"> <div className="flex flex-col"> <span className="text-xs text-gray-900 dark:text-gray-200"> #{invoice.invoiceNumber} </span> <span className="text-xs text-gray-500 dark:text-gray-400"> Due {formatDate(invoice.dueDate.toString())} </span> </div> </td> <td className="px-4 py-2 text-xs font-medium text-gray-900 dark:text-gray-200 text-right"> {formatCurrency(invoice.amount)} </td> </tr> ))} </tbody> </table> </div> {/* Panel Footer */} <div className="btn-modern--secondary"dark:bg-gray-800 px-4 py-2 border-t border-gray-100 dark:border-gray-700"> <div className="flex justify-between items-center"> <span className="text-xs text-gray-500 dark:text-gray-400"> Total Outstanding </span> <span className="text-sm font-bold text-green-600 dark:text-green-300"> {formatCurrency(lateInvoicesData?.totalAmount || 0)} </span> </div> </div> </> )} </div> </div> )} </div> ); }; export default LateInvoicesSummary; 