import React from "react"; export const RecentUpdatesSection: React.FC = () => { return ( <> <div className="space-y-6"> {/* December 2024 Theme Fixes */} <div className="btn-modern--primary"dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"> <div className="flex items-start space-x-3"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center"> <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" /> </svg> </div> </div> <div className="flex-1"> <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2"> Visual Improvements - December 2024 </h3> <div className="text-green-700 dark:text-green-300 space-y-2"> <p className="font-medium"> Fixed major visual issues with the Flexoki theme: </p> <ul className="list-disc list-inside space-y-1 ml-4"> <li> <strong>Card Readability:</strong> Fixed light text on white backgrounds in summary cards </li> <li> <strong>Transaction Filters:</strong> Resolved black box display issue </li> <li> <strong>Text Contrast:</strong> Improved readability throughout the application </li> <li> <strong>Consistent Theming:</strong> All components now use proper Flexoki colors </li> </ul> <p className="text-sm mt-3"> <strong>Impact:</strong> Better readability, consistent visual design, and improved user experience across all pages. </p> </div> </div> </div> </div> {/* General Updates Section */} <div className="btn-modern--primary"dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"> <div className="flex items-start space-x-3"> <div className="flex-shrink-0"> <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center"> <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> </div> </div> <div className="flex-1"> <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2"> Ongoing Improvements </h3> <div className="text-blue-700 dark:text-blue-300 space-y-2"> <p>We're continuously improving Upstream with:</p> <ul className="list-disc list-inside space-y-1 ml-4"> <li> Enhanced integration reliability with Xero, Harvest, and HubSpot </li> <li>Improved performance and loading times</li> <li>Better mobile responsiveness</li> <li>More accurate financial forecasting</li> </ul> </div> </div> </div> </div> {/* Feedback Section */} <div className="btn-modern--secondary"dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4"> <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2"> Have Feedback? </h3> <p className="text-gray-600 dark:text-gray-400"> Notice any issues or have suggestions for improvements? We'd love to hear from you! Your feedback helps us make Upstream better for everyone. </p> </div> </div> </> ); }; 