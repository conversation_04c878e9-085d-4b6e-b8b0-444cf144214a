import express from 'express';
import { CompanyRepository } from '../../repositories/company-repository';
import {
  CompanyCreate,
  CompanyUpdate,
  Company
} from '../../../types/company-types';
import activityLogger from '../../../utils/activity-logger';

// TypeScript interfaces for this file
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

const router = express.Router();
const companyRepository = new CompanyRepository();

/**
 * @route GET /api/crm/companies
 * @desc Get all companies
 * @access Private
 */
router.get('/', async (req, res) => {
  try {
    // Get all companies with deal statistics
    const companies = companyRepository.getAllCompanies();

    res.json({
      success: true,
      data: companies
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting companies:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to get companies',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route GET /api/crm/companies/linking-status
 * @desc Get all companies with their linking status to external systems
 * @access Private
 */
router.get('/linking-status', async (req, res) => {
  try {
    const companiesWithStatus = companyRepository.getAllCompaniesWithLinkingStatus();
    res.json({
      success: true,
      data: companiesWithStatus
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting companies with linking status:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to get companies with linking status',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route GET /api/crm/companies/unlinked
 * @desc Get companies that need linking between systems (legacy endpoint)
 * @access Private
 */
router.get('/unlinked', async (req, res) => {
  try {
    const unlinkedCompanies = companyRepository.getUnlinkedCompanies();
    res.json({
      success: true,
      data: unlinkedCompanies
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting unlinked companies:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to get unlinked companies',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route GET /api/crm/companies/:id
 * @desc Get a company by ID
 * @access Private
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const company = companyRepository.getCompanyById(id);

    if (!company) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    // Include source information in response for clarity
    const source = company.source || 'Unknown';
    const hasRadarData = company.radarState !== undefined && company.radarState !== null;

    res.json({
      success: true,
      data: company,
      meta: {
        source,
        hasRadarData
      }
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to get company',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route POST /api/crm/companies
 * @desc Create a new company
 * @access Private
 */
router.post('/', async (req, res) => {
  try {
    const companyData: CompanyCreate = req.body;

    // Validate required fields
    if (!companyData.name) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields',
        requiredFields: ['name']
      });
    }

    // Validate radar state if provided
    if (companyData.radarState &&
        !['Strategy', 'Transformation', 'BAU', 'Transition out'].includes(companyData.radarState)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid radar state',
        message: 'Radar state must be one of: Strategy, Transformation, BAU, Transition out'
      });
    }

    // Validate priority if provided
    if (companyData.priority &&
        !['High', 'Medium', 'Low', 'Qualified out'].includes(companyData.priority)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid priority',
        message: 'Priority must be one of: High, Medium, Low, Qualified out'
      });
    }

    // If no source is specified, default to 'Manual'
    if (!companyData.source) {
      companyData.source = 'Manual';
    }

    // Check if source is valid
    if (!['HubSpot', 'Harvest', 'Manual'].includes(companyData.source)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid source',
        message: 'Source must be one of: HubSpot, Harvest, Manual'
      });
    }

    const company = companyRepository.createCompany(companyData, req.body.createdBy || 'api');

    if (!company) {
      return res.status(500).json({
        success: false,
        error: 'Failed to create company'
      });
    }

    // Log company creation activity
    try {
      await activityLogger.logCompanyCreated(
        company.id,
        company.name,
        req.session?.userInfo?.sub || 'unknown-user'
      );
    } catch (activityError) {
      console.error('Error logging company creation activity:', activityError);
      // Don't fail the request if activity logging fails
    }

    res.status(201).json({
      success: true,
      data: company
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error creating company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to create company',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route PUT /api/crm/companies/:id
 * @desc Update a company (includes both CRM and Radar fields)
 * @access Private
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const companyData: CompanyUpdate = req.body;

    // Validate the company data including Radar-specific fields
    const { validateCompanyData } = require('../../../utils/data-validation');
    const validation = validateCompanyData(companyData);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid company data',
        validationErrors: validation.errors
      });
    }

    // Track updater
    companyData.updatedBy = req.session?.user?.email || 'system';

    const company = companyRepository.updateCompany(id, companyData);

    if (!company) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    // If this company has Radar fields, invalidate the Radar companies cache
    if (company.radarState || companyData.radarState) {
      // This helps ensure Radar view stays in sync
      res.locals.invalidateCache = res.locals.invalidateCache || [];
      res.locals.invalidateCache.push('radarCompanies');
    }

    // Log the company update activity
    try {
      const activityLogger = await import('../../../utils/activity-logger');
      
      // Check if radar fields were updated
      const hasRadarChanges = companyData.radarState !== undefined || 
                             companyData.priority !== undefined || 
                             companyData.notes !== undefined;
      
      if (hasRadarChanges) {
        // Log as radar change
        await activityLogger.default.logCompanyRadarChanged(
          id,
          company.name || 'Unknown Company',
          companyData,
          companyData.updatedBy || 'system'
        );
      } else {
        // Log as general company update
        await activityLogger.default.logCompanyUpdated(
          id,
          company.name || 'Unknown Company',
          companyData,
          companyData.updatedBy || 'system'
        );
      }
    } catch (logError) {
      console.warn('Failed to log company update activity:', logError);
      // Don't fail the request if logging fails
    }

    res.json({
      success: true,
      data: company
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error updating company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to update company',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route DELETE /api/crm/companies/:id
 * @desc Delete a company
 * @access Private
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const success = companyRepository.deleteCompany(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      message: 'Company deleted successfully'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error deleting company:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to delete company',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route POST /api/crm/companies/:sourceId/merge/:targetId
 * @desc Merge two companies (useful when same company exists in both systems)
 * @access Private
 */
router.post('/:sourceId/merge/:targetId', async (req, res) => {
  try {
    const { sourceId, targetId } = req.params;

    if (sourceId === targetId) {
      return res.status(400).json({
        success: false,
        error: 'Cannot merge a company with itself'
      });
    }

    const userId = req.session?.user?.email || 'system';
    const mergedCompany = companyRepository.mergeCompanies(sourceId, targetId, userId);

    if (!mergedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Failed to merge companies'
      });
    }

    res.json({
      success: true,
      data: mergedCompany,
      message: 'Companies successfully merged'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error merging companies:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to merge companies',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route POST /api/crm/companies/:id/link-hubspot
 * @desc Link a company to HubSpot
 * @access Private
 */
router.post('/:id/link-hubspot', async (req, res) => {
  try {
    const { id } = req.params;
    const { hubspotId } = req.body;

    if (!hubspotId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required field: hubspotId'
      });
    }

    const userId = req.session?.user?.email || 'system';
    const updatedCompany = companyRepository.linkCompanyToHubSpot(id, hubspotId, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully linked to HubSpot'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error linking company to HubSpot:', apiError);

    if (apiError.message && apiError.message.includes('already linked')) {
      return res.status(409).json({
        success: false,
        error: 'Conflict',
        message: apiError.message
      });
    }

    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to link company to HubSpot',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route POST /api/crm/companies/:id/link-harvest
 * @desc Link a company to Harvest
 * @access Private
 */
router.post('/:id/link-harvest', async (req, res) => {
  try {
    const { id } = req.params;
    const { harvestId } = req.body;

    if (!harvestId || typeof harvestId !== 'number') {
      return res.status(400).json({
        success: false,
        error: 'Missing or invalid required field: harvestId (must be number)'
      });
    }

    const userId = req.session?.user?.email || 'system';
    const updatedCompany = companyRepository.linkCompanyToHarvest(id, harvestId, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully linked to Harvest'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error linking company to Harvest:', apiError);

    if (apiError.message && apiError.message.includes('already linked')) {
      return res.status(409).json({
        success: false,
        error: 'Conflict',
        message: apiError.message
      });
    }

    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to link company to Harvest',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route DELETE /api/crm/companies/:id/link-hubspot
 * @desc Unlink a company from HubSpot
 * @access Private
 */
router.delete('/:id/link-hubspot', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.session?.user?.email || 'system';

    const updatedCompany = companyRepository.unlinkCompanyFromHubSpot(id, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully unlinked from HubSpot'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error unlinking company from HubSpot:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to unlink company from HubSpot',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route DELETE /api/crm/companies/:id/link-harvest
 * @desc Unlink a company from Harvest
 * @access Private
 */
router.delete('/:id/link-harvest', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.session?.user?.email || 'system';

    const updatedCompany = companyRepository.unlinkCompanyFromHarvest(id, userId);

    if (!updatedCompany) {
      return res.status(404).json({
        success: false,
        error: 'Company not found'
      });
    }

    res.json({
      success: true,
      data: updatedCompany,
      message: 'Company successfully unlinked from Harvest'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error unlinking company from Harvest:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to unlink company from Harvest',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

export default router;