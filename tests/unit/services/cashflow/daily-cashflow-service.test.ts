import { DailyCashflowService } from '../../../../src/services/cashflow/daily-cashflow-service';
import { CacheService } from '../../../../src/services/cashflow/cache-service';
import { TransactionService } from '../../../../src/services/cashflow/transaction-service';
import { ProjectionFilterService } from '../../../../src/services/cashflow/projection-filter-service';
import { XeroBillService } from '../../../../src/services/xero/bill-service';
import { XeroService } from '../../../../src/services/xero/xero-service';
import { HarvestInvoiceService } from '../../../../src/services/harvest/invoice-service';
import { HarvestProjectBudgetService } from '../../../../src/services/harvest/project-budget-service';
import { ExpenseService } from '../../../../src/services/cashflow/expense-service';
import { DealProjectionService } from '../../../../src/services/cashflow/deal-projection-service';
import type { DailyCashflow, Transaction } from '../../../../src/types/financial';

// Mock all dependencies
jest.mock('../../../../src/services/cashflow/cache-service');
jest.mock('../../../../src/services/cashflow/transaction-service');
jest.mock('../../../../src/services/cashflow/projection-filter-service');
jest.mock('../../../../src/services/xero/bill-service');
jest.mock('../../../../src/services/xero/xero-service');
jest.mock('../../../../src/services/harvest/invoice-service');
jest.mock('../../../../src/services/harvest/project-budget-service');
jest.mock('../../../../src/services/cashflow/expense-service');
jest.mock('../../../../src/services/cashflow/deal-projection-service');

describe('DailyCashflowService', () => {
  let service: DailyCashflowService;
  let mockCacheService: jest.Mocked<CacheService>;
  let mockTransactionService: jest.Mocked<TransactionService>;
  let mockProjectionFilterService: jest.Mocked<ProjectionFilterService>;
  let mockXeroBillService: jest.Mocked<XeroBillService>;
  let mockXeroService: jest.Mocked<XeroService>;
  let mockHarvestInvoiceService: jest.Mocked<HarvestInvoiceService>;
  let mockHarvestProjectBudgetService: jest.Mocked<HarvestProjectBudgetService>;
  let mockExpenseService: jest.Mocked<ExpenseService>;
  let mockDealProjectionService: jest.Mocked<DealProjectionService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock instances
    mockCacheService = new CacheService() as jest.Mocked<CacheService>;
    mockTransactionService = new TransactionService() as jest.Mocked<TransactionService>;
    mockProjectionFilterService = new ProjectionFilterService() as jest.Mocked<ProjectionFilterService>;
    mockXeroBillService = new XeroBillService(null as any) as jest.Mocked<XeroBillService>;
    mockXeroService = new XeroService(null as any) as jest.Mocked<XeroService>;
    mockHarvestInvoiceService = new HarvestInvoiceService(null as any) as jest.Mocked<HarvestInvoiceService>;
    mockHarvestProjectBudgetService = new HarvestProjectBudgetService(null as any) as jest.Mocked<HarvestProjectBudgetService>;
    mockExpenseService = new ExpenseService(null as any) as jest.Mocked<ExpenseService>;
    mockDealProjectionService = new DealProjectionService(null as any) as jest.Mocked<DealProjectionService>;

    service = new DailyCashflowService(
      mockCacheService,
      mockTransactionService,
      mockProjectionFilterService,
      mockXeroBillService,
      mockXeroService,
      mockHarvestInvoiceService,
      mockHarvestProjectBudgetService,
      mockExpenseService,
      mockDealProjectionService
    );
  });

  describe('getDailyCashflow', () => {
    const mockStartDate = '2024-01-01';
    const mockEndDate = '2024-01-31';
    const mockTransactions: Transaction[] = [
      {
        id: '1',
        date: '2024-01-15',
        amount: 1000,
        type: 'income',
        category: 'invoice',
        description: 'Invoice #123',
        source: 'harvest',
        metadata: {},
        tenantId: 'test-tenant'
      },
      {
        id: '2',
        date: '2024-01-20',
        amount: -500,
        type: 'expense',
        category: 'bill',
        description: 'Office supplies',
        source: 'xero',
        metadata: {},
        tenantId: 'test-tenant'
      }
    ];

    it('should return cached data if available', async () => {
      const cachedData: DailyCashflow = {
        startDate: mockStartDate,
        endDate: mockEndDate,
        dailyBalances: [],
        transactions: mockTransactions,
        totalIncome: 1000,
        totalExpenses: 500,
        projectedInvoices: [],
        projectedBudgets: [],
        scenarios: {
          worstCase: { dailyBalances: [] },
          expected: { dailyBalances: [] },
          bestCase: { dailyBalances: [] }
        }
      };

      mockCacheService.get.mockResolvedValue(cachedData);

      const result = await service.getDailyCashflow(mockStartDate, mockEndDate, 'test-tenant');

      expect(result).toEqual(cachedData);
      expect(mockCacheService.get).toHaveBeenCalledWith(
        expect.stringContaining('daily-cashflow:'),
        expect.any(Function),
        900000 // 15 minutes
      );
    });

    it('should fetch and calculate cashflow data when not cached', async () => {
      mockCacheService.get.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      mockTransactionService.fetchAllTransactions.mockResolvedValue(mockTransactions);
      mockProjectionFilterService.applyAllFilters.mockResolvedValue({
        transactions: mockTransactions,
        projectedInvoices: [],
        projectedBudgets: []
      });

      const result = await service.getDailyCashflow(mockStartDate, mockEndDate, 'test-tenant');

      expect(result).toBeDefined();
      expect(result.transactions).toEqual(mockTransactions);
      expect(result.totalIncome).toBe(1000);
      expect(result.totalExpenses).toBe(500);
      expect(mockTransactionService.fetchAllTransactions).toHaveBeenCalledWith(
        mockStartDate,
        mockEndDate,
        'test-tenant',
        expect.any(Object)
      );
    });

    it('should handle errors gracefully', async () => {
      mockCacheService.get.mockRejectedValue(new Error('Cache error'));

      await expect(service.getDailyCashflow(mockStartDate, mockEndDate, 'test-tenant'))
        .rejects.toThrow('Cache error');
    });

    it('should include project settings when provided', async () => {
      const projectSettings = {
        enableProjectedInvoices: true,
        defaultPaymentTerms: 30,
        includeExpectedScenarios: true
      };

      mockCacheService.get.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      mockTransactionService.fetchAllTransactions.mockResolvedValue(mockTransactions);
      mockProjectionFilterService.applyAllFilters.mockResolvedValue({
        transactions: mockTransactions,
        projectedInvoices: [],
        projectedBudgets: []
      });

      await service.getDailyCashflow(mockStartDate, mockEndDate, 'test-tenant', projectSettings);

      expect(mockTransactionService.fetchAllTransactions).toHaveBeenCalledWith(
        mockStartDate,
        mockEndDate,
        'test-tenant',
        projectSettings
      );
    });

    it('should calculate daily balances correctly', async () => {
      mockCacheService.get.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      mockTransactionService.fetchAllTransactions.mockResolvedValue(mockTransactions);
      mockProjectionFilterService.applyAllFilters.mockResolvedValue({
        transactions: mockTransactions,
        projectedInvoices: [],
        projectedBudgets: []
      });

      const result = await service.getDailyCashflow(mockStartDate, mockEndDate, 'test-tenant');

      // Verify daily balances are calculated
      expect(result.dailyBalances).toBeDefined();
      expect(result.dailyBalances.length).toBeGreaterThan(0);
      
      // Check that balances accumulate correctly
      let runningBalance = 0;
      for (const balance of result.dailyBalances) {
        const dayTransactions = mockTransactions.filter(t => t.date === balance.date);
        const dayTotal = dayTransactions.reduce((sum, t) => sum + t.amount, 0);
        runningBalance += dayTotal;
        
        if (dayTransactions.length > 0) {
          expect(balance.balance).toBe(runningBalance);
        }
      }
    });

    it('should generate scenario projections', async () => {
      mockCacheService.get.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      mockTransactionService.fetchAllTransactions.mockResolvedValue(mockTransactions);
      mockProjectionFilterService.applyAllFilters.mockResolvedValue({
        transactions: mockTransactions,
        projectedInvoices: [
          {
            id: 'proj-1',
            date: '2024-01-25',
            amount: 2000,
            type: 'income' as const,
            category: 'projected_invoice',
            description: 'Projected invoice',
            source: 'harvest',
            metadata: { isProjected: true },
            tenantId: 'test-tenant'
          }
        ],
        projectedBudgets: []
      });

      const result = await service.getDailyCashflow(mockStartDate, mockEndDate, 'test-tenant');

      expect(result.scenarios).toBeDefined();
      expect(result.scenarios.worstCase).toBeDefined();
      expect(result.scenarios.expected).toBeDefined();
      expect(result.scenarios.bestCase).toBeDefined();

      // Best case should include projected income
      expect(result.scenarios.bestCase.dailyBalances.length).toBeGreaterThan(0);
    });
  });

  describe('invalidateCache', () => {
    it('should call cache service invalidate', async () => {
      mockCacheService.invalidate.mockResolvedValue(undefined);

      await service.invalidateCache();

      expect(mockCacheService.invalidate).toHaveBeenCalledWith('daily-cashflow:*');
    });
  });
});