#!/bin/bash

# Comprehensive script to find all potential issues related to repository refactoring

echo "=== REPOSITORY REFACTORING ISSUE FINDER ==="
echo "This script will search for potential issues caused by the repository refactoring."
echo ""

# Create a directory for the results
mkdir -p refactoring-issues
RESULTS_FILE="refactoring-issues/results.txt"
echo "Results will be saved to $RESULTS_FILE"
echo "" > $RESULTS_FILE

# Function to search and log results
search_and_log() {
  local search_term="$1"
  local description="$2"
  local file_pattern="$3"
  
  echo "Searching for: $description" | tee -a $RESULTS_FILE
  echo "Pattern: $search_term" | tee -a $RESULTS_FILE
  
  # Perform the search
  grep -r "$search_term" --include="$file_pattern" src/ | tee -a $RESULTS_FILE
  
  echo "" | tee -a $RESULTS_FILE
  echo "-------------------------------------------" | tee -a $RESULTS_FILE
  echo "" | tee -a $RESULTS_FILE
}

# 1. Search for references to the old CRM repository
echo "=== CATEGORY 1: References to old CRM repository ===" | tee -a $RESULTS_FILE
search_and_log "crmRepository" "References to crmRepository variable" "*.ts"
search_and_log "CRMRepository" "References to CRMRepository class" "*.ts"
search_and_log "crm-repository" "Import statements referencing crm-repository" "*.ts"

# 2. Search for SQL queries with potential column reference issues
echo "=== CATEGORY 2: SQL queries with potential column reference issues ===" | tee -a $RESULTS_FILE
search_and_log "c\.company_id" "References to c.company_id in SQL queries" "*.ts"
search_and_log "JOIN contact c" "SQL joins with contact table aliased as c" "*.ts"
search_and_log "JOIN company c" "SQL joins with company table aliased as c" "*.ts"
search_and_log "JOIN deal d" "SQL joins with deal table aliased as d" "*.ts"

# 3. Search for old table names
echo "=== CATEGORY 3: References to old table names ===" | tee -a $RESULTS_FILE
search_and_log "deal_contact" "References to old deal_contact table" "*.ts"
search_and_log "FROM deal_contact" "SQL queries using deal_contact table" "*.ts"
search_and_log "JOIN deal_contact" "SQL joins with deal_contact table" "*.ts"

# 4. Search for repository method access patterns
echo "=== CATEGORY 4: Repository method access patterns ===" | tee -a $RESULTS_FILE
search_and_log "this\.crmRepository\." "Method calls on crmRepository" "*.ts"
search_and_log "crmRepository\." "Method calls on crmRepository" "*.ts"
search_and_log "getContactByHubSpotId" "References to getContactByHubSpotId method" "*.ts"
search_and_log "getCompanyByHubSpotId" "References to getCompanyByHubSpotId method" "*.ts"
search_and_log "getDealByHubSpotId" "References to getDealByHubSpotId method" "*.ts"

# 5. Search for constructor initialization
echo "=== CATEGORY 5: Constructor initialization issues ===" | tee -a $RESULTS_FILE
search_and_log "new CRMRepository" "Initialization of CRMRepository" "*.ts"
search_and_log "private crmRepository" "Private crmRepository field declarations" "*.ts"

# 6. Search for database schema references
echo "=== CATEGORY 6: Database schema references ===" | tee -a $RESULTS_FILE
search_and_log "CREATE TABLE.*deal_contact" "Creation of deal_contact table" "*.ts"
search_and_log "ALTER TABLE.*deal_contact" "Alteration of deal_contact table" "*.ts"

echo "=== SEARCH COMPLETE ==="
echo "Results saved to $RESULTS_FILE"
echo "Review the results and update the code accordingly."
