#!/usr/bin/env node

/**
 * Automated Button Standardization Script
 * 
 * This script automatically fixes button inconsistencies by:
 * 1. Replacing custom button styling with standardized Flexoki classes
 * 2. Converting inline styles to proper CSS classes
 * 3. Ensuring consistent button variants across the app
 */

const fs = require('fs');
const path = require('path');

// Button transformation rules
const BUTTON_TRANSFORMATIONS = [
  // Primary button patterns
  {
    pattern: /className\s*=\s*["{`]([^"}` ]*)(bg-primary(?:-\d+)?|bg-blue-\d+)([^"}` ]*)["}` ]/g,
    replacement: 'className="$1btn-modern btn-modern--primary$3"',
    description: 'Convert primary background colors to btn-modern--primary'
  },
  
  // Secondary button patterns  
  {
    pattern: /className\s*=\s*["{`]([^"}` ]*)(bg-secondary(?:-\d+)?|bg-gray-\d+)([^"}` ]*)["}` ]/g,
    replacement: 'className="$1btn-modern btn-modern--secondary$3"',
    description: 'Convert secondary background colors to btn-modern--secondary'
  },
  
  // Success/Green button patterns
  {
    pattern: /className\s*=\s*["{`]([^"}` ]*)(bg-green-\d+|btn-success)([^"}` ]*)["}` ]/g,
    replacement: 'className="$1btn-modern btn-modern--primary$3"',
    description: 'Convert green buttons to btn-modern--primary with success styling'
  },
  
  // Danger/Red button patterns
  {
    pattern: /className\s*=\s*["{`]([^"}` ]*)(bg-red-\d+|btn-danger)([^"}` ]*)["}` ]/g,
    replacement: 'className="$1btn-modern btn-modern--primary$3"',
    description: 'Convert red buttons to btn-modern--primary with error styling'
  },
  
  // Remove hover color overrides that conflict with modern system
  {
    pattern: /hover:bg-primary-dark|hover:bg-primary-\d+|hover:bg-blue-\d+|hover:bg-green-\d+|hover:bg-red-\d+/g,
    replacement: '',
    description: 'Remove custom hover colors (handled by btn-modern system)'
  },
  
  // Remove text color overrides on buttons (handled by variants)
  {
    pattern: /text-white(?=.*btn-modern)/g,
    replacement: '',
    description: 'Remove text-white on modern buttons (handled by variant)'
  },
  
  // Clean up legacy button classes
  {
    pattern: /btn-primary(?!-)/g,
    replacement: 'btn-modern btn-modern--primary',
    description: 'Convert legacy btn-primary to modern system'
  },
  
  {
    pattern: /btn-secondary(?!-)/g,
    replacement: 'btn-modern btn-modern--secondary',
    description: 'Convert legacy btn-secondary to modern system'
  },
  
  // Clean up multiple spaces and duplicate classes
  {
    pattern: /\s+/g,
    replacement: ' ',
    description: 'Clean up multiple spaces'
  },
  
  {
    pattern: /(btn-modern\s+)+btn-modern/g,
    replacement: 'btn-modern',
    description: 'Remove duplicate btn-modern classes'
  }
];

// Special cases that need manual review
const MANUAL_REVIEW_PATTERNS = [
  /style\s*=\s*\{\{[^}]*backgroundColor[^}]*\}\}/g,
  /style\s*=\s*\{\{[^}]*background[^}]*\}\}/g,
  /className\s*=\s*["{`][^"}` ]*add-company-button[^"}` ]*["}` ]/g,
  /className\s*=\s*["{`][^"}` ]*xero-action-button[^"}` ]*["}` ]/g
];

/**
 * Apply transformations to file content
 */
function transformButtonStyling(content, filePath) {
  let transformedContent = content;
  const appliedTransformations = [];
  
  // Apply each transformation rule
  BUTTON_TRANSFORMATIONS.forEach(rule => {
    const beforeCount = (transformedContent.match(rule.pattern) || []).length;
    transformedContent = transformedContent.replace(rule.pattern, rule.replacement);
    const afterCount = (transformedContent.match(rule.pattern) || []).length;
    
    if (beforeCount > afterCount) {
      appliedTransformations.push({
        description: rule.description,
        changes: beforeCount - afterCount
      });
    }
  });
  
  // Check for manual review cases
  const manualReviewNeeded = MANUAL_REVIEW_PATTERNS.some(pattern => 
    pattern.test(transformedContent)
  );
  
  return {
    content: transformedContent,
    changed: transformedContent !== content,
    appliedTransformations,
    manualReviewNeeded
  };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const result = transformButtonStyling(originalContent, filePath);
    
    if (result.changed) {
      // Create backup
      const backupPath = filePath + '.backup';
      fs.writeFileSync(backupPath, originalContent);
      
      // Write transformed content
      fs.writeFileSync(filePath, result.content);
      
      return {
        filePath,
        success: true,
        appliedTransformations: result.appliedTransformations,
        manualReviewNeeded: result.manualReviewNeeded,
        backupCreated: true
      };
    }
    
    return {
      filePath,
      success: true,
      appliedTransformations: [],
      manualReviewNeeded: result.manualReviewNeeded,
      backupCreated: false
    };
    
  } catch (error) {
    return {
      filePath,
      success: false,
      error: error.message
    };
  }
}

/**
 * Get all files to process
 */
function getFilesToProcess() {
  const files = [];
  const extensions = ['.tsx', '.ts', '.jsx', '.js'];
  const directories = ['src/frontend/components', 'src/frontend/pages'];
  
  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  directories.forEach(scanDirectory);
  return files;
}

/**
 * Main execution function
 */
function fixButtons() {
  console.log('🔧 Starting Automated Button Standardization...\n');
  
  const files = getFilesToProcess();
  console.log(`📁 Processing ${files.length} files...\n`);
  
  const results = {
    processed: 0,
    changed: 0,
    errors: 0,
    manualReviewNeeded: [],
    transformationsSummary: {},
    backupsCreated: 0
  };
  
  // Process each file
  files.forEach(filePath => {
    const result = processFile(filePath);
    results.processed++;
    
    if (result.success) {
      if (result.appliedTransformations.length > 0) {
        results.changed++;
        console.log(`✅ ${filePath}`);
        
        result.appliedTransformations.forEach(transform => {
          console.log(`   - ${transform.description} (${transform.changes} changes)`);
          
          if (!results.transformationsSummary[transform.description]) {
            results.transformationsSummary[transform.description] = 0;
          }
          results.transformationsSummary[transform.description] += transform.changes;
        });
        
        if (result.backupCreated) {
          results.backupsCreated++;
        }
      }
      
      if (result.manualReviewNeeded) {
        results.manualReviewNeeded.push(filePath);
      }
    } else {
      results.errors++;
      console.log(`❌ ${filePath}: ${result.error}`);
    }
  });
  
  // Display summary
  console.log('\n📊 STANDARDIZATION RESULTS');
  console.log('===========================\n');
  
  console.log(`📁 Files Processed: ${results.processed}`);
  console.log(`✅ Files Changed: ${results.changed}`);
  console.log(`💾 Backups Created: ${results.backupsCreated}`);
  console.log(`❌ Errors: ${results.errors}\n`);
  
  if (Object.keys(results.transformationsSummary).length > 0) {
    console.log('🔄 TRANSFORMATIONS APPLIED:');
    console.log('============================');
    Object.entries(results.transformationsSummary).forEach(([description, count]) => {
      console.log(`• ${description}: ${count} changes`);
    });
    console.log('');
  }
  
  if (results.manualReviewNeeded.length > 0) {
    console.log('⚠️  FILES NEEDING MANUAL REVIEW:');
    console.log('================================');
    results.manualReviewNeeded.forEach(filePath => {
      console.log(`• ${filePath}`);
    });
    console.log('\nThese files contain inline styles or custom classes that need manual attention.\n');
  }
  
  console.log('💡 NEXT STEPS:');
  console.log('===============');
  console.log('1. Review the changes in your editor');
  console.log('2. Test the application to ensure buttons work correctly');
  console.log('3. Address any files marked for manual review');
  console.log('4. Run the button audit again to verify improvements');
  console.log('5. Remove backup files once satisfied with changes');
  
  return results;
}

// Run the fix if called directly
if (require.main === module) {
  fixButtons();
}

module.exports = { fixButtons };
