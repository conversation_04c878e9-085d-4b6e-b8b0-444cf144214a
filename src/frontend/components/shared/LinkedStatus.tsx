import React from "react"; import { CheckIcon } from "@heroicons/react/20/solid"; import { Badge } from "./Badge"; import { But<PERSON> } from "./Button"; import HubSpotBadge from "./HubSpotBadge"; import HarvestBadge from "./HarvestBadge"; interface LinkedStatusProps { /** * The system type (hubspot or harvest) */ system: "hubspot" | "harvest"; /** * Whether the item is linked */ isLinked: boolean; /** * The external ID (optional, for display) */ externalId?: string | number; /** * Handler for linking action */ onLink?: () => void; /** * Handler for unlinking action */ onUnlink?: () => void; /** * Whether actions are disabled (loading state) */ disabled?: boolean; /** * Size variant */ size?: "sm" | "default"; } /** * A consistent linked status component that shows linking state with proper branding. * * Features: * - System-specific branding (HubSpot/Harvest logos) * - Clear linked/unlinked states * - Consistent styling with app design system * - Accessible interaction patterns * - Loading states * * @example * ```tsx * <LinkedStatus * system="hubspot" * isLinked={true} * externalId="12345" * onUnlink={() => handleUnlink()} * /> * ``` */ const LinkedStatus = ({ system, isLinked, externalId, onLink, onUnlink, disabled = false, size = "default", }: LinkedStatusProps) => { const systemConfig = { hubspot: { name: "HubSpot", badge: <HubSpotBadge size={size === "sm" ? "sm" : "default"} />, linkText: "Link HubSpot", colors: { linked: "bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800", unlinked: "border-orange-300 text-orange-600 hover:bg-orange-50 dark:border-orange-700 dark:text-orange-400 dark:hover:bg-orange-900/20", }, }, harvest: { name: "Harvest", badge: <HarvestBadge size={size === "sm" ? "sm" : "default"} />, linkText: "Link Harvest", colors: { linked: "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800", unlinked: "border-green-300 text-green-600 dark:border-green-700 dark:text-green-400 dark:/20", }, }, }; const config = systemConfig[system]; if (isLinked) { return ( <div className="space-y-1"> {/* First line: System badge and Linked status */} <div className="flex items-center gap-2"> {/* System badge */} {config.badge} {/* Linked badge with checkmark */} <Badge variant="success" size={size === "sm" ? "sm" : "default"} icon={<CheckIcon className="w-3 h-3" />} className={`${config.colors.linked} font-medium`} > Linked </Badge> </div> {/* Second line: External ID and Unlink button (if provided) */} {(externalId || onUnlink) && ( <div className="flex items-center gap-2"> {/* External ID (if provided) */} {externalId && ( <span className="text-xs text-gray-500 dark:text-gray-400 font-mono"> {externalId} </span> )} {/* Unlink button */} {onUnlink && ( <Button variant="ghost" size={size === "sm" ? "sm" : "default"} onClick={onUnlink} disabled={disabled} className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 dark:/20 px-3 whitespace-nowrap" > Unlink </Button> )} </div> )} </div> ); } // Unlinked state return ( <div className="flex items-center gap-2"> {/* System badge (grayed out) */} <div className="opacity-40">{config.badge}</div> {/* Link button */} {onLink && ( <Button variant="outline" size={size === "sm" ? "sm" : "default"} onClick={onLink} disabled={disabled} className={`${config.colors.unlinked} border-dashed`} > {config.linkText} </Button> )} </div> ); }; export default LinkedStatus; 