import React from 'react';

/**
 * CRM Deals section content for the help page
 */
export const DealsSection: React.FC = () => {
  return (
    <>
      <p>
        Manage your sales pipeline with comprehensive deal tracking, contact management,
        and relationship mapping. Follow deals from initial contact through to project delivery.
      </p>
      
      <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-4">
        Deal Management
      </h3>
      <ul className="list-disc pl-6 space-y-2 mt-2 text-sm">
        <li>Visual Kanban board for pipeline management</li>
        <li>Deal stage tracking with probability settings</li>
        <li>Contact and company relationship mapping</li>
        <li>Estimate linking for project proposals</li>
        <li>Activity timeline and note-taking</li>
      </ul>

      <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-6">
        Integration Features
      </h3>
      <div className="space-y-3 mt-2">
        <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm">HubSpot Sync</h4>
          <p className="text-xs mt-1">Import contacts, companies, and deals from HubSpot</p>
        </div>
        <div className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <h4 className="font-medium text-gray-800 dark:text-gray-200 text-sm">Harvest Projects</h4>
          <p className="text-xs mt-1">Link deals to Harvest projects for seamless workflow</p>
        </div>
      </div>
    </>
  );
};