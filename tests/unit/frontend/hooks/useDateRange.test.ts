import { renderHook, act } from '@testing-library/react';
import { useDateRange } from '../../../../src/frontend/hooks/useDateRange';

describe('useDateRange', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-06-15'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should initialize with default date range', () => {
    const { result } = renderHook(() => useDateRange());

    expect(result.current.startDate).toEqual(new Date('2024-06-01'));
    expect(result.current.endDate).toEqual(new Date('2024-06-30'));
    expect(result.current.period).toBe('month');
  });

  it('should initialize with custom date range', () => {
    const { result } = renderHook(() => 
      useDateRange({
        defaultStartDate: new Date('2024-01-01'),
        defaultEndDate: new Date('2024-12-31'),
        defaultPeriod: 'year'
      })
    );

    expect(result.current.startDate).toEqual(new Date('2024-01-01'));
    expect(result.current.endDate).toEqual(new Date('2024-12-31'));
    expect(result.current.period).toBe('year');
  });

  it('should update date range', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setDateRange(new Date('2024-03-01'), new Date('2024-03-31'));
    });

    expect(result.current.startDate).toEqual(new Date('2024-03-01'));
    expect(result.current.endDate).toEqual(new Date('2024-03-31'));
    expect(result.current.period).toBe('custom');
  });

  it('should set period to current month', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('month');
    });

    expect(result.current.startDate).toEqual(new Date('2024-06-01'));
    expect(result.current.endDate).toEqual(new Date('2024-06-30'));
    expect(result.current.period).toBe('month');
  });

  it('should set period to current quarter', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('quarter');
    });

    expect(result.current.startDate).toEqual(new Date('2024-04-01'));
    expect(result.current.endDate).toEqual(new Date('2024-06-30'));
    expect(result.current.period).toBe('quarter');
  });

  it('should set period to current year', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('year');
    });

    expect(result.current.startDate).toEqual(new Date('2024-01-01'));
    expect(result.current.endDate).toEqual(new Date('2024-12-31'));
    expect(result.current.period).toBe('year');
  });

  it('should set period to last 30 days', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('last30');
    });

    expect(result.current.startDate).toEqual(new Date('2024-05-16'));
    expect(result.current.endDate).toEqual(new Date('2024-06-15'));
    expect(result.current.period).toBe('last30');
  });

  it('should navigate to previous period', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('month');
      result.current.goToPrevious();
    });

    expect(result.current.startDate).toEqual(new Date('2024-05-01'));
    expect(result.current.endDate).toEqual(new Date('2024-05-31'));
  });

  it('should navigate to next period', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('month');
      result.current.goToNext();
    });

    expect(result.current.startDate).toEqual(new Date('2024-07-01'));
    expect(result.current.endDate).toEqual(new Date('2024-07-31'));
  });

  it('should format date range as string', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setPeriod('month');
    });

    expect(result.current.formatDateRange()).toBe('Jun 1 - Jun 30, 2024');
  });

  it('should check if date is in range', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setDateRange(new Date('2024-06-01'), new Date('2024-06-30'));
    });

    expect(result.current.isDateInRange(new Date('2024-06-15'))).toBe(true);
    expect(result.current.isDateInRange(new Date('2024-05-31'))).toBe(false);
    expect(result.current.isDateInRange(new Date('2024-07-01'))).toBe(false);
  });

  it('should calculate days in range', () => {
    const { result } = renderHook(() => useDateRange());

    act(() => {
      result.current.setDateRange(new Date('2024-06-01'), new Date('2024-06-30'));
    });

    expect(result.current.getDaysInRange()).toBe(30);
  });

  it('should reset to default range', () => {
    const { result } = renderHook(() => useDateRange({ defaultPeriod: 'year' }));

    act(() => {
      result.current.setPeriod('month');
      result.current.reset();
    });

    expect(result.current.period).toBe('year');
    expect(result.current.startDate).toEqual(new Date('2024-01-01'));
    expect(result.current.endDate).toEqual(new Date('2024-12-31'));
  });
});