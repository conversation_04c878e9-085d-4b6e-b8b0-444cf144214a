/**
 * Basic Navigation E2E Test
 * 
 * This test checks that the application loads correctly and basic navigation works.
 * It doesn't require authentication, making it suitable for CI environments.
 */
import { test, expect } from '@playwright/test';
import { waitForStableState } from './utils';

test.describe('Basic Navigation', () => {
  test('application loads with authentication screen', async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to stabilize
    await waitForStableState(page);
    
    // Check that we're on the auth screen (the app always starts with auth)
    const authButton = page.locator('button:has-text("Connect to Xero")');
    await expect(authButton).toBeVisible();
    
    // Verify page title
    const title = await page.title();
    expect(title).toContain('Onbord'); // Adjust based on actual title
    
    // Take a screenshot for reference
    await page.screenshot({ path: './tests/e2e/screenshots/auth-screen.png' });
  });
  
  // We can add more basic tests here that don't require authentication
  // For example, testing the UI components on the auth screen
  test('auth screen has expected elements', async ({ page }) => {
    await page.goto('/');
    await waitForStableState(page);
    
    // Check for logo
    const logo = page.locator('img[alt*="logo" i], [data-testid="logo"]');
    await expect(logo).toBeVisible();
    
    // Check for welcome message or heading
    const heading = page.locator('h1, h2, [role="heading"]').first();
    await expect(heading).toBeVisible();
    
    // Verify connect button exists and is enabled
    const connectButton = page.locator('button:has-text("Connect to Xero")');
    await expect(connectButton).toBeVisible();
    await expect(connectButton).toBeEnabled();
  });
});

// This suite tests authenticated flows but is marked as skipped by default
// It can be run manually when needed
test.describe.skip('Authenticated Flows', () => {
  test('dashboard loads after authentication', async ({ page }) => {
    // This test would use the auth.json state from manual authentication
    // We skip it by default but it can be run manually
    await page.goto('/');
    
    // The rest of the test would verify the dashboard loads correctly
    // after authentication, but we're skipping that part for now
  });
});
