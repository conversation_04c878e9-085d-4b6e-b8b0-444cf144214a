import React, { useState, useEffect, useMemo } from "react";
import { useQuery } from "react-query";
import { getDeals } from "../../../api/crm";
import { Deal, DealStage } from "../../../types/crm-types";
import { format, parseISO } from "date-fns";
import { MagnifyingGlassIcon, XMarkIcon } from "@heroicons/react/24/outline";

// Define deal stages in pipeline order
const DEAL_STAGES: DealStage[] = [
  "Identified",
  "Qualified",
  "Solution proposal",
  "Solution presentation",
  "Objection handling",
  "Finalising terms",
  "Closed won",
  "Closed lost",
  "Abandoned",
];

// Closed deal stages to filter out
const CLOSED_STAGES: DealStage[] = ["Closed won", "Closed lost", "Abandoned"];

/**
 * Calculate similarity score between two strings
 * Returns a score between 0 and 1, where 1 is an exact match
 */
const calculateSimilarity = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0;
  
  const s1 = str1.toLowerCase().trim();
  const s2 = str2.toLowerCase().trim();
  
  // Exact match
  if (s1 === s2) return 1;
  
  // Check if one contains the other
  if (s1.includes(s2) || s2.includes(s1)) return 0.8;
  
  // Check for common words
  const words1 = s1.split(/\s+/);
  const words2 = s2.split(/\s+/);
  const commonWords = words1.filter(word => words2.includes(word));
  const commonScore = commonWords.length / Math.max(words1.length, words2.length);
  
  // Simple character-based similarity
  const maxLen = Math.max(s1.length, s2.length);
  let matches = 0;
  for (let i = 0; i < Math.min(s1.length, s2.length); i++) {
    if (s1[i] === s2[i]) matches++;
  }
  const charScore = matches / maxLen;
  
  // Return weighted average
  return commonScore * 0.7 + charScore * 0.3;
};

interface DealSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectDeal: (deal: Deal) => void;
  estimateClientName?: string;
  estimateProjectName?: string;
}

/**
 * Modal for selecting a deal to populate estimate form fields
 */
const DealSelectionModal: React.FC<DealSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectDeal,
  estimateClientName,
  estimateProjectName,
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedDealId, setSelectedDealId] = useState<string>("");

  // Fetch all deals and filter out closed ones
  const { data: allDeals = [], isLoading: isDealsLoading } = useQuery(
    "deals",
    getDeals,
    {
      enabled: isOpen, // Only fetch when modal is open
      staleTime: 30000, // 30 seconds
    }
  );

  // Filter out closed deals
  const activeDeals = useMemo(() => {
    return allDeals.filter(
      (deal) => !CLOSED_STAGES.includes(deal.stage as DealStage)
    );
  }, [allDeals]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm("");
      setSelectedDealId("");
    }
  }, [isOpen]);

  // Calculate suggested matches based on similarity
  const suggestedMatches = useMemo(() => {
    if (!estimateClientName && !estimateProjectName) return [];
    if (activeDeals.length === 0) return [];
    
    const dealsWithScores = activeDeals.map(deal => {
      let maxScore = 0;
      
      // Calculate similarity with client name
      if (estimateClientName) {
        const companyScore = deal.company?.name 
          ? calculateSimilarity(estimateClientName, deal.company.name) 
          : 0;
        maxScore = Math.max(maxScore, companyScore);
        
        // Also check if deal name contains client name
        const dealNameScore = calculateSimilarity(estimateClientName, deal.name);
        maxScore = Math.max(maxScore, dealNameScore);
      }
      
      // Calculate similarity with project name
      if (estimateProjectName) {
        const projectScore = calculateSimilarity(estimateProjectName, deal.name);
        maxScore = Math.max(maxScore, projectScore);
      }
      
      return { deal, score: maxScore };
    });
    
    // Sort by score and take top 3 matches with score > 0.3
    return dealsWithScores
      .filter(item => item.score > 0.3)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(item => item.deal);
  }, [estimateClientName, estimateProjectName, activeDeals]);

  // Filter and sort deals based on search term and pipeline order
  const filteredDeals = useMemo(() => {
    // Start with active deals
    let deals = activeDeals;
    
    // If searching, filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      deals = deals.filter((deal) => {
        return (
          deal.name.toLowerCase().includes(searchLower) ||
          (deal.company?.name &&
            deal.company.name.toLowerCase().includes(searchLower)) ||
          deal.stage.toLowerCase().includes(searchLower)
        );
      });
    } else {
      // If not searching, exclude suggested matches from the main list
      const suggestedIds = new Set(suggestedMatches.map(d => d.id));
      deals = deals.filter(deal => !suggestedIds.has(deal.id));
    }
    
    // Sort by pipeline stage order first
    return deals.sort((a, b) => {
      const aStageIndex = DEAL_STAGES.indexOf(a.stage);
      const bStageIndex = DEAL_STAGES.indexOf(b.stage);

      if (aStageIndex !== bStageIndex) {
        return aStageIndex - bStageIndex;
      }

      // If same stage, sort by deal name alphabetically
      return a.name.localeCompare(b.name);
    });
  }, [activeDeals, searchTerm, suggestedMatches]);

  // Handle deal selection
  const handleSelectDeal = () => {
    const selectedDeal = activeDeals.find((deal) => deal.id === selectedDealId);
    if (selectedDeal) {
      onSelectDeal(selectedDeal);
      onClose();
    }
  };

  // Format currency
  const formatCurrency = (amount?: number) => {
    if (!amount) return "No value";
    return new Intl.NumberFormat("en-AU", {
      style: "currency",
      currency: "AUD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "No date";
    try {
      return format(parseISO(dateString), "dd/MM/yyyy");
    } catch {
      return "Invalid date";
    }
  };

  // Get stage color
  const getStageColor = (stage: string) => {
    switch (stage) {
      case "Closed won":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "Closed lost":
      case "Abandoned":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      case "Finalising terms":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Select Deal
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Search */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search deals by name, company, or stage..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isDealsLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
            </div>
          ) : activeDeals.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              No active deals available.
            </div>
          ) : (
            <div className="space-y-4">
              {/* Suggested Matches Section */}
              {!searchTerm && suggestedMatches.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                    <svg className="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    Suggested Matches
                  </h3>
                  <div className="space-y-3">
                    {suggestedMatches.map((deal: Deal) => (
                      <div
                        key={deal.id}
                        onClick={() => setSelectedDealId(deal.id)}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          selectedDealId === deal.id
                            ? "border-green-500 bg-green-50 dark:bg-green-900/20 dark:border-green-400"
                            : "border-green-200 dark:border-green-800 hover:border-green-300 dark:hover:border-green-700 bg-green-50/50 dark:bg-green-900/10"
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {deal.name}
                          </h3>
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full ${getStageColor(
                              deal.stage
                            )}`}
                          >
                            {deal.stage}
                          </span>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div>
                            <span className="font-medium">Company:</span>{" "}
                            {deal.company?.name || "No company"}
                          </div>
                          <div>
                            <span className="font-medium">Value:</span>{" "}
                            {formatCurrency(deal.value)}
                          </div>
                          <div>
                            <span className="font-medium">Expected Close:</span>{" "}
                            {formatDate(deal.expectedCloseDate)}
                          </div>
                          <div>
                            <span className="font-medium">Probability:</span>{" "}
                            {deal.probability || 0}%
                          </div>
                        </div>

                        {deal.description && (
                          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                            {deal.description}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Divider between suggested and all deals */}
              {!searchTerm && suggestedMatches.length > 0 && filteredDeals.length > 0 && (
                <div className="relative">
                  <div className="absolute inset-0 flex items-center" aria-hidden="true">
                    <div className="w-full border-t border-gray-300 dark:border-gray-600" />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="px-3 bg-white dark:bg-gray-800 text-sm text-gray-500 dark:text-gray-400">
                      All Deals
                    </span>
                  </div>
                </div>
              )}

              {/* All Deals or Search Results */}
              {filteredDeals.length === 0 && searchTerm ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No deals found matching your search.
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredDeals.map((deal: Deal) => (
                    <div
                      key={deal.id}
                      onClick={() => setSelectedDealId(deal.id)}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedDealId === deal.id
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20 dark:border-green-400"
                          : "border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {deal.name}
                        </h3>
                        <span
                          className={`px-2 py-1 text-xs font-medium rounded-full ${getStageColor(
                            deal.stage
                          )}`}
                        >
                          {deal.stage}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <div>
                          <span className="font-medium">Company:</span>{" "}
                          {deal.company?.name || "No company"}
                        </div>
                        <div>
                          <span className="font-medium">Value:</span>{" "}
                          {formatCurrency(deal.value)}
                        </div>
                        <div>
                          <span className="font-medium">Expected Close:</span>{" "}
                          {formatDate(deal.expectedCloseDate)}
                        </div>
                        <div>
                          <span className="font-medium">Probability:</span>{" "}
                          {deal.probability || 0}%
                        </div>
                      </div>

                      {deal.description && (
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                          {deal.description}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleSelectDeal}
            disabled={!selectedDealId}
            className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Select Deal
          </button>
        </div>
      </div>
    </div>
  );
};

export default DealSelectionModal;
