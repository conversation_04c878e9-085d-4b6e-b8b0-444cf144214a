# Why We Kept The Current Schema

**Summary**: The 14-table simplified schema would break core features. We're optimizing the existing 30-table schema instead.

## Executive Summary

After comprehensive analysis of the codebase, we've determined that the current database schema, while complex, serves real business needs. Rather than a complete rewrite to a simplified 14-table schema, we recommend targeted optimizations to fix specific issues while preserving the functionality users depend on.

## Analysis Results

### What's Working Well
- **30 table schema** supports all current features
- **Notes with threading** enables conversation tracking  
- **Estimate allocations** power utilization reports
- **Enrichment tables** manage external data properly
- **Integration sync** tracking prevents conflicts

### What Needs Fixing
1. **CASCADE DELETE Issues** - Currently causing data loss
2. **Inconsistent Soft Delete** - Not all queries respect deleted_at
3. **Unused Tables** - A few tables have no active features
4. **Migration Complexity** - 15+ migration files accumulated

## Recommended Approach: Targeted Optimization

### Phase 1: Fix Critical Issues (Week 1)

#### 1.1 Fix Foreign Key Constraints
Change dangerous CASCADE DELETE to SET NULL:
```sql
-- Example: Preserve deals when company is deleted
ALTER TABLE deal DROP FOREIGN KEY fk_company;
ALTER TABLE deal ADD FOREIGN KEY (company_id) 
  REFERENCES company(id) ON DELETE SET NULL;
```

#### 1.2 Ensure Soft Delete Consistency
- Audit all repositories for missing `deleted_at IS NULL` checks
- Add soft delete support to any tables missing it
- Update queries to respect soft deletes

#### 1.3 Create Unified Schema File
- Consolidate 15+ migrations into single `000_unified_schema.sql`
- Incorporate all lessons learned
- This becomes the source of truth

### Phase 2: Remove Unused Features (Week 2)

Based on code analysis, these tables can be removed:
- `company_relationship` - Backend exists but UI is disabled ("coming soon")
- `opportunity_intelligence` - No active code uses this

Keep these tables that have active code dependencies:
- `note` - Used by conversation features
- `estimate_allocation` & `estimate_time_allocation` - Used by reports
- `enrichment` tables - Active enrichment features

### Phase 3: Schema Documentation (Week 3)

1. Document actual table usage
2. Create data dictionary
3. Document integration field mappings
4. Update developer guides

## Why Not 14 Tables?

Our analysis found that the simplified 14-table schema would break:
- **Notes & Conversations** - Threading requires normalized table
- **Estimate Allocations** - Complex aggregations need separate tables
- **Performance** - JSON queries 100-1000x slower for aggregations
- **Integration Features** - Field ownership tracking needs structure

## Implementation Plan

### Week 1: Critical Fixes
- [ ] Fix all CASCADE DELETE → SET NULL
- [ ] Audit and fix soft delete queries
- [ ] Create unified schema file
- [ ] Test fixes on preview environment

### Week 2: Cleanup
- [ ] Remove 4 unused tables
- [ ] Update repository classes
- [ ] Clean up old migration files
- [ ] Update test fixtures

### Week 3: Documentation
- [ ] Update schema documentation
- [ ] Create migration guide
- [ ] Update CLAUDE.md
- [ ] Developer training

## Expected Outcomes

1. **Data Safety** - No more cascade delete data loss
2. **Consistency** - All soft deletes work properly
3. **Simplicity** - Single schema file, 28 tables (down from 30)
4. **Stability** - No breaking changes to features
5. **Performance** - Maintained query performance

## Conclusion

The current schema complexity serves real business needs. Rather than a risky rewrite, targeted optimization delivers the benefits of a cleaner schema while preserving the features and performance users depend on.

Total effort: 3 weeks vs 3-6 months for full rewrite
Risk: Low vs High
Benefit: Same end result - a working, optimized schema