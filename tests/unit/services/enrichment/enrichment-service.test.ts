/**
 * Enrichment Service Unit Tests
 * 
 * Comprehensive tests for the Enrichment Service including:
 * - Company enrichment orchestration
 * - Multiple source integration
 * - Confidence scoring
 * - Expiry calculation
 * - Error handling
 * - Business logic validation
 */

import { EnrichmentService } from '../../../../src/api/services/enrichment/enrichment-service';
import { ABNLookupService } from '../../../../src/api/services/enrichment/abn-lookup-service';
import { EnrichmentRepository } from '../../../../src/api/repositories/enrichment-repository';
import { Company } from '../../../../src/types/company-types';

// Mock dependencies
jest.mock('../../../../src/api/services/enrichment/abn-lookup-service');
jest.mock('../../../../src/api/repositories/enrichment-repository');

describe('EnrichmentService', () => {
  let enrichmentService: EnrichmentService;
  let mockABNLookupService: jest.Mocked<ABNLookupService>;
  let mockEnrichmentRepository: jest.Mocked<EnrichmentRepository>;

  // Test data factories
  const createMockCompany = (overrides: Partial<Company> = {}): Company => ({
    id: 'comp-123',
    name: 'Test Company Pty Ltd',
    industry: 'Technology',
    size: '10-50',
    website: 'https://test.com.au',
    address: '123 Collins St, Melbourne VIC 3000',
    description: 'A test company',
    source: 'manual',
    radarState: 'active',
    priority: 'High',
    currentSpend: 5000,
    potentialSpend: 10000,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'test-user',
    updatedBy: 'test-user',
    lastEnrichedAt: null,
    enrichmentStatus: null,
    activeDealsCount: 0,
    totalDealValue: 0,
    linkingStatus: 'none',
    hubspotId: null,
    harvestId: null,
    ...overrides
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mocked instances
    mockEnrichmentRepository = new EnrichmentRepository() as jest.Mocked<EnrichmentRepository>;
    mockABNLookupService = new ABNLookupService() as jest.Mocked<ABNLookupService>;
    
    // Initialize service and inject mocks
    enrichmentService = new EnrichmentService();
    (enrichmentService as any).enrichmentRepository = mockEnrichmentRepository;
    (enrichmentService as any).abnLookupService = mockABNLookupService;

    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('enrichCompany', () => {
    it('should successfully enrich company with ABN data', async () => {
      const company = createMockCompany();
      
      // Mock ABN service responses
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([
        {
          abn: '12 ***********',
          name: 'Test Company Pty Ltd',
          status: 'Active',
          score: 95.5
        }
      ]);
      mockABNLookupService.getByABN.mockResolvedValue({
        abn: '12 ***********',
        abnStatus: 'Active',
        entityTypeCode: 'PRV',
        entityTypeName: 'Australian Private Company',
        gstStatus: 'Registered',
        entityName: 'Test Company Pty Ltd',
        tradingNames: ['Test Trading Name'],
        businessLocation: {
          stateCode: 'VIC',
          postcode: '3000'
        },
        asicNumber: '*********'
      });

      // Mock repository methods
      mockEnrichmentRepository.saveCompanyEnrichment.mockResolvedValue('enrichment-id');
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        source: 'abn_lookup',
        success: true,
        confidence: expect.any(Number)
      });

      expect(results[0].data).toMatchObject({
        abn: '12 ***********',
        abnStatus: 'Active',
        entityType: 'Australian Private Company',
        gstStatus: 'Registered',
        tradingNames: ['Test Trading Name'],
        asicNumber: '*********',
        address: 'VIC 3000',
        dataQuality: 'high'
      });

      // Verify repository calls
      expect(mockEnrichmentRepository.saveCompanyEnrichment).toHaveBeenCalledWith({
        companyId: company.id,
        source: 'abn_lookup',
        data: expect.any(Object),
        confidence: expect.any(Number),
        expiresAt: expect.any(Date)
      });

      expect(mockEnrichmentRepository.updateCompanyEnrichmentStatus).toHaveBeenCalledWith(
        company.id,
        expect.objectContaining({
          lastEnriched: expect.any(String),
          sources: {
            abn_lookup: {
              success: true,
              confidence: expect.any(Number),
              error: undefined
            }
          }
        })
      );
    });

    it('should handle ABN lookup service not configured', async () => {
      const company = createMockCompany();
      
      mockABNLookupService.isConfigured.mockReturnValue(false);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        source: 'abn_lookup',
        success: false,
        error: 'ABN Lookup service not configured',
        confidence: 0
      });

      // Should still update status
      expect(mockEnrichmentRepository.updateCompanyEnrichmentStatus).toHaveBeenCalled();
    });

    it('should handle no ABN matches found', async () => {
      const company = createMockCompany();
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([]);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        source: 'abn_lookup',
        success: false,
        error: 'No matching ABN found',
        confidence: 0
      });
    });

    it('should handle ABN detail retrieval failure', async () => {
      const company = createMockCompany();
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([
        { abn: '12 ***********', name: 'Test Company', status: 'Active', score: 95.5 }
      ]);
      mockABNLookupService.getByABN.mockResolvedValue(null);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        source: 'abn_lookup',
        success: false,
        error: 'Failed to get ABN details',
        confidence: 0
      });
    });

    it('should handle service errors gracefully', async () => {
      const company = createMockCompany();
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockRejectedValue(new Error('Network error'));
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        source: 'abn_lookup',
        success: false,
        error: 'Network error',
        confidence: 0
      });

      expect(console.error).toHaveBeenCalledWith(
        'Error enriching company from abn_lookup:',
        expect.any(Error)
      );
    });

    it('should use specified sources when provided', async () => {
      const company = createMockCompany();
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([]);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      await enrichmentService.enrichCompany(company, ['abn_lookup']);

      expect(mockABNLookupService.searchByName).toHaveBeenCalled();
    });

    it('should skip non-Australian companies for ABN lookup', async () => {
      const nonAusCompany = createMockCompany({
        website: 'https://test.com', // No .au domain
        address: '123 Main St, New York, USA'
      });
      
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(nonAusCompany);

      expect(results).toHaveLength(0);
      expect(mockABNLookupService.searchByName).not.toHaveBeenCalled();
    });
  });

  describe('Confidence calculation', () => {
    it('should calculate high confidence for exact name matches', async () => {
      const company = createMockCompany({ name: 'Perfect Match Company' });
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([
        {
          abn: '12 ***********',
          name: 'Perfect Match Company',
          status: 'Active',
          score: 100
        }
      ]);
      mockABNLookupService.getByABN.mockResolvedValue({
        abn: '12 ***********',
        abnStatus: 'Active',
        entityTypeCode: 'PRV',
        entityTypeName: 'Australian Private Company',
        gstStatus: 'Registered',
        entityName: 'Perfect Match Company'
      });
      
      mockEnrichmentRepository.saveCompanyEnrichment.mockResolvedValue('enrichment-id');
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results[0].confidence).toBeGreaterThan(0.8);
      expect(results[0].data.dataQuality).toBe('high');
    });

    it('should calculate medium confidence for similar names', async () => {
      const company = createMockCompany({ name: 'Test Company Pty Ltd' });
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([
        {
          abn: '12 ***********',
          name: 'Test Company Limited',
          status: 'Active',
          score: 75
        }
      ]);
      mockABNLookupService.getByABN.mockResolvedValue({
        abn: '12 ***********',
        abnStatus: 'Active',
        entityTypeCode: 'PRV',
        entityTypeName: 'Australian Private Company',
        gstStatus: 'Registered',
        entityName: 'Test Company Limited'
      });
      
      mockEnrichmentRepository.saveCompanyEnrichment.mockResolvedValue('enrichment-id');
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results[0].confidence).toBeGreaterThan(0.6);
      expect(results[0].confidence).toBeLessThanOrEqual(0.8);
      expect(results[0].data.dataQuality).toBe('medium');
    });

    it('should calculate low confidence for poor matches', async () => {
      const company = createMockCompany({ name: 'Original Company Name' });
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([
        {
          abn: '12 ***********',
          name: 'Completely Different Name',
          status: 'Active',
          score: 40
        }
      ]);
      mockABNLookupService.getByABN.mockResolvedValue({
        abn: '12 ***********',
        abnStatus: 'Active',
        entityTypeCode: 'PRV',
        entityTypeName: 'Australian Private Company',
        gstStatus: 'Registered',
        entityName: 'Completely Different Name'
      });
      
      mockEnrichmentRepository.saveCompanyEnrichment.mockResolvedValue('enrichment-id');
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company);

      expect(results[0].confidence).toBeLessThanOrEqual(0.6);
      expect(results[0].data.dataQuality).toBe('low');
    });
  });

  describe('Australian company detection', () => {
    it('should identify companies with .au domains as Australian', async () => {
      const company = createMockCompany({ 
        website: 'https://company.com.au',
        address: null 
      });
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([]);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      await enrichmentService.enrichCompany(company);

      expect(mockABNLookupService.searchByName).toHaveBeenCalled();
    });

    it('should identify companies with Australian addresses', async () => {
      const company = createMockCompany({ 
        website: null,
        address: 'Sydney, NSW, Australia' 
      });
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([]);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      await enrichmentService.enrichCompany(company);

      expect(mockABNLookupService.searchByName).toHaveBeenCalled();
    });

    it('should identify companies with Australian state abbreviations', async () => {
      const ausStates = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'ACT', 'NT'];
      
      for (const state of ausStates) {
        const company = createMockCompany({ 
          website: null,
          address: `123 Test St, ${state} 2000` 
        });
        
        mockABNLookupService.isConfigured.mockReturnValue(true);
        mockABNLookupService.searchByName.mockResolvedValue([]);
        mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

        await enrichmentService.enrichCompany(company);

        expect(mockABNLookupService.searchByName).toHaveBeenCalled();
        
        jest.clearAllMocks();
      }
    });
  });

  describe('Expiry calculation', () => {
    it('should set appropriate expiry dates for different sources', () => {
      const service = new EnrichmentService();
      const now = new Date();
      
      // Test ABN lookup expiry (90 days)
      const abnExpiry = (service as any).calculateExpiryDate('abn_lookup');
      const abnDays = Math.round((abnExpiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      expect(abnDays).toBe(90);
      
      // Test other sources expiry (30 days)
      const clearbitExpiry = (service as any).calculateExpiryDate('clearbit');
      const clearbitDays = Math.round((clearbitExpiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      expect(clearbitDays).toBe(30);
      
      const apolloExpiry = (service as any).calculateExpiryDate('apollo');
      const apolloDays = Math.round((apolloExpiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      expect(apolloDays).toBe(30);
    });
  });

  describe('getCompanyEnrichment', () => {
    it('should retrieve enrichment data from repository', async () => {
      const companyId = 'comp-123';
      const source = 'abn_lookup';
      const mockData = [{ id: 'enrich-1', data: { abn: '12 ***********' } }];
      
      mockEnrichmentRepository.getCompanyEnrichment.mockResolvedValue(mockData);

      const result = await enrichmentService.getCompanyEnrichment(companyId, source);

      expect(result).toBe(mockData);
      expect(mockEnrichmentRepository.getCompanyEnrichment).toHaveBeenCalledWith(companyId, source);
    });

    it('should retrieve all enrichment data when no source specified', async () => {
      const companyId = 'comp-123';
      const mockData = [
        { id: 'enrich-1', source: 'abn_lookup', data: { abn: '12 ***********' } },
        { id: 'enrich-2', source: 'clearbit', data: { website: 'test.com' } }
      ];
      
      mockEnrichmentRepository.getCompanyEnrichment.mockResolvedValue(mockData);

      const result = await enrichmentService.getCompanyEnrichment(companyId);

      expect(result).toBe(mockData);
      expect(mockEnrichmentRepository.getCompanyEnrichment).toHaveBeenCalledWith(companyId, undefined);
    });
  });

  describe('companyNeedsEnrichment', () => {
    it('should return true for companies with no enrichment data', async () => {
      const company = createMockCompany();
      
      mockEnrichmentRepository.getCompanyEnrichment.mockResolvedValue([]);

      const result = await enrichmentService.companyNeedsEnrichment(company);

      expect(result).toBe(true);
    });

    it('should return true for companies with expired enrichment', async () => {
      const company = createMockCompany();
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1); // Yesterday
      
      mockEnrichmentRepository.getCompanyEnrichment.mockResolvedValue([
        {
          id: 'enrich-1',
          source: 'abn_lookup',
          expiresAt: pastDate.toISOString(),
          data: { abn: '12 ***********' }
        }
      ]);

      const result = await enrichmentService.companyNeedsEnrichment(company);

      expect(result).toBe(true);
    });

    it('should return false for companies with valid enrichment', async () => {
      const company = createMockCompany();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30); // 30 days from now
      
      mockEnrichmentRepository.getCompanyEnrichment.mockResolvedValue([
        {
          id: 'enrich-1',
          source: 'abn_lookup',
          expiresAt: futureDate.toISOString(),
          data: { abn: '12 ***********' }
        }
      ]);

      const result = await enrichmentService.companyNeedsEnrichment(company);

      expect(result).toBe(false);
    });

    it('should return false for enrichment without expiry date', async () => {
      const company = createMockCompany();
      
      mockEnrichmentRepository.getCompanyEnrichment.mockResolvedValue([
        {
          id: 'enrich-1',
          source: 'abn_lookup',
          expiresAt: null,
          data: { abn: '12 ***********' }
        }
      ]);

      const result = await enrichmentService.companyNeedsEnrichment(company);

      expect(result).toBe(false);
    });
  });

  describe('String similarity calculation', () => {
    it('should calculate correct similarity scores', () => {
      const service = new EnrichmentService();
      
      // Test exact match
      const exactMatch = (service as any).calculateSimilarity('test', 'test');
      expect(exactMatch).toBe(1.0);
      
      // Test no match
      const noMatch = (service as any).calculateSimilarity('abc', 'xyz');
      expect(noMatch).toBeLessThan(0.5);
      
      // Test partial match
      const partialMatch = (service as any).calculateSimilarity('testcompany', 'testcorp');
      expect(partialMatch).toBeGreaterThan(0.5);
      expect(partialMatch).toBeLessThan(1.0);
      
      // Test empty strings
      const emptyMatch = (service as any).calculateSimilarity('', '');
      expect(emptyMatch).toBe(1.0);
    });
  });

  describe('Levenshtein distance calculation', () => {
    it('should calculate correct edit distances', () => {
      const service = new EnrichmentService();
      
      // Test identical strings
      expect((service as any).levenshteinDistance('test', 'test')).toBe(0);
      
      // Test single character difference
      expect((service as any).levenshteinDistance('test', 'best')).toBe(1);
      
      // Test insertion
      expect((service as any).levenshteinDistance('test', 'tests')).toBe(1);
      
      // Test deletion
      expect((service as any).levenshteinDistance('tests', 'test')).toBe(1);
      
      // Test completely different strings
      expect((service as any).levenshteinDistance('abc', 'xyz')).toBe(3);
    });
  });

  describe('Error handling in enrichment status update', () => {
    it('should handle repository errors during status update', async () => {
      const company = createMockCompany();
      
      mockABNLookupService.isConfigured.mockReturnValue(true);
      mockABNLookupService.searchByName.mockResolvedValue([]);
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockRejectedValue(
        new Error('Database error')
      );

      // Should not throw even if status update fails
      await expect(enrichmentService.enrichCompany(company)).resolves.not.toThrow();
      
      expect(console.error).toHaveBeenCalledWith(
        'Error enriching company from abn_lookup:',
        expect.any(Error)
      );
    });
  });

  describe('Unknown enrichment sources', () => {
    it('should handle unknown sources gracefully', async () => {
      const company = createMockCompany();
      
      mockEnrichmentRepository.updateCompanyEnrichmentStatus.mockResolvedValue();

      const results = await enrichmentService.enrichCompany(company, ['unknown_source' as any]);

      expect(results).toHaveLength(1);
      expect(results[0]).toMatchObject({
        source: 'unknown_source',
        success: false,
        error: 'Unknown enrichment source: unknown_source',
        confidence: 0
      });
    });
  });
});