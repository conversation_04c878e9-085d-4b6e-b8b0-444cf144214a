import React, { useState, useEffect } from "react";
import { ProjectTotals } from "../../hooks/useEstimateStaffManagement";
import { formatCurrency } from "./utils";

interface FloatingFinancialSummaryProps {
  projectTotals: ProjectTotals;
}

const FloatingFinancialSummary: React.FC<FloatingFinancialSummaryProps> = ({
  projectTotals,
}) => {
  // Initialize state from localStorage or default to true
  const [isExpanded, setIsExpanded] = useState(() => {
    const savedState = localStorage.getItem("financialSummaryPanelExpanded");
    return savedState === null ? true : savedState === "true";
  });

  // Save expanded state to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(
      "financialSummaryPanelExpanded",
      isExpanded.toString()
    );
  }, [isExpanded]);

  // Helper function to determine margin health indicator
  const getMarginHealthClass = (percentage: number) => {
    if (percentage >= 30)
      return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
    if (percentage >= 20)
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
    return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
  };

  return (
    <div className="fixed flex flex-col floating-finance-panel">
      {/* Collapsed state shows only an icon button */}
      {!isExpanded ? (
        <button
          onClick={() => setIsExpanded(true)}
          className="self-end p-3 rounded-full bg-primary text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200 hover:scale-110"
          aria-label="Show Financial Summary"
          aria-expanded="false"
          title="Show Financial Summary"
        >
          <svg
            className="w-5 h-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </button>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 w-full origin-top-right transition-all duration-300 ease-in-out scale-100 opacity-100">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Financial Summary
            </h3>
            <button
              onClick={() => setIsExpanded(false)}
              className="p-1.5 rounded-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-all duration-200"
              aria-label="Collapse Financial Summary"
              aria-expanded="true"
            >
              <svg
                className="w-3.5 h-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Redesigned metrics display with Fees as primary */}
          <div className="space-y-4">
            {/* Fees (ex. GST) - Primary metric with more prominence */}
            <div className="bg-primary/5 -mx-3 px-3 py-2 border-b border-primary/20">
              <div className="flex items-center justify-between">
                <span className="text-xs font-medium text-primary-dark dark:text-primary-light">
                  Fees (ex. GST)
                </span>
                <span className="text-[9px] text-gray-500 dark:text-gray-400">
                  {projectTotals.totalDays.toFixed(1)} days
                </span>
              </div>
              <div className="mt-0.5">
                <span className="text-base font-bold text-gray-900 dark:text-white">
                  {formatCurrency(
                    isNaN(projectTotals.discountedRevenue)
                      ? isNaN(projectTotals.totalRevenue)
                        ? 0
                        : projectTotals.totalRevenue
                      : projectTotals.discountedRevenue
                  )}
                </span>
                {projectTotals.discountAmount > 0 && (
                  <span className="ml-2 text-xs text-gray-500 dark:text-gray-400 line-through">
                    {formatCurrency(projectTotals.totalRevenue)}
                  </span>
                )}
              </div>
            </div>

            {/* GM and Total Cost on same row */}
            <div className="grid grid-cols-2 gap-2">
              {/* Gross Margin */}
              <div>
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    GM
                  </span>
                  <div
                    className={`px-1 py-0.5 rounded text-[9px] font-medium ${getMarginHealthClass(
                      projectTotals.marginPercentage
                    )}`}
                  >
                    {projectTotals.marginPercentage >= 30
                      ? "Good"
                      : projectTotals.marginPercentage >= 20
                      ? "OK"
                      : "Low"}
                  </div>
                </div>
                <div className="mt-1">
                  <div className="flex flex-col">
                    <span
                      className={`text-sm font-bold ${
                        projectTotals.marginPercentage >= 30
                          ? "text-green-600 dark:text-green-400"
                          : projectTotals.marginPercentage >= 20
                          ? "text-yellow-500 dark:text-yellow-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {projectTotals.marginPercentage.toFixed(1)}%
                    </span>
                    <span className="text-[10px] text-gray-600 dark:text-gray-400">
                      {formatCurrency(projectTotals.marginAmount)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Total Cost */}
              <div>
                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                    Cost
                  </span>
                  <span className="text-[9px] text-gray-500 dark:text-gray-400">
                    Pre-OH
                  </span>
                </div>
                <div className="mt-1">
                  <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                    {formatCurrency(
                      isNaN(projectTotals.totalCost)
                        ? 0
                        : projectTotals.totalCost
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloatingFinancialSummary;
