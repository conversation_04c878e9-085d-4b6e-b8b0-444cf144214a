# Test Suite Overview

This directory contains the comprehensive test suite for the Onbord Financial Dashboard, with 60+ test files covering all major functionality.

## Quick Start

```bash
# Run all tests
npm test

# Run with mock authentication (recommended)
npm run test:mock

# Run specific test suites
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:e2e          # E2E tests with Playwright

# Development mode
npm test -- --watch       # Watch mode
npm test -- --coverage    # Coverage report
```

## Documentation

- **[Comprehensive Testing Guide](./testing-guide.md)** - Complete testing documentation
- **[Test Coverage Summary](./TEST-COVERAGE-SUMMARY.md)** - Current coverage metrics

## Test Organization

```
tests/
├── unit/               # Fast, isolated tests (~75% of tests)
├── integration/        # API and service tests (~20% of tests)
├── e2e/               # Browser automation (~5% of tests)
├── real-data/         # Optional real API tests
├── fixtures/          # Test data factories
└── setup/            # Test configuration
```

## Mock Authentication

Tests use backend mock authentication (`USE_MOCK_AUTH=true`) to bypass external OAuth:

```bash
npm run dev:mock   # Development server with mock auth
npm run test:mock  # Tests with mock auth
```

This provides immediate access using your local test data with production safety checks.

## Quick Reference

### Running Specific Tests
```bash
npm test -- path/to/test.ts              # Single file
npm test -- --testNamePattern="pattern"  # By name
npm test -- --watch                      # Watch mode
```

### Debugging
```bash
node --inspect-brk node_modules/.bin/jest --runInBand  # Debug mode
npm test -- --verbose                                   # Verbose output
npm test -- --detectOpenHandles                        # Find leaks
```

### Coverage
```bash
npm test -- --coverage                   # Generate report
open coverage/lcov-report/index.html     # View in browser
```

For detailed information, see the [Comprehensive Testing Guide](./testing-guide.md).