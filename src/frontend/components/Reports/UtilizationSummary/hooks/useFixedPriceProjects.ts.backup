import { useState, useEffect } from "react";
import { FixedPriceProject, getFixedPriceProjectIncome } from "../../../../api/harvest";

/**
 * Hook to fetch fixed price project income
 */
export const useFixedPriceProjects = (fromDate?: string, toDate?: string) => {
  const [fixedPriceProjects, setFixedPriceProjects] = useState<FixedPriceProject[]>([]);
  const [isLoadingFixedPrice, setIsLoadingFixedPrice] = useState<boolean>(false);

  // Fetch fixed price project income when date range changes
  useEffect(() => {
    const fetchFixedPriceIncome = async () => {
      if (fromDate && toDate) {
        setIsLoadingFixedPrice(true);
        try {
          const projects = await getFixedPriceProjectIncome(fromDate, toDate);
          setFixedPriceProjects(projects);
        } catch (error) {
          console.error("Error fetching fixed price project income:", error);
        } finally {
          setIsLoadingFixedPrice(false);
        }
      }
    };

    fetchFixedPriceIncome();
  }, [fromDate, toDate]);

  return { fixedPriceProjects, isLoadingFixedPrice };
};
