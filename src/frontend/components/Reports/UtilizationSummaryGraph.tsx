import { StaffUtilization } from "../../../services/harvest/time-report-service"; import { formatPercentage } from "../../utils/format"; interface UtilizationSummaryGraphProps { utilizationData: StaffUtilization[]; excludeLeave?: boolean; // Whether to exclude leave from capacity calculations overallUtilization?: number; // Overall utilization percentage } /** * A compact graph showing staff utilization percentages */ const UtilizationSummaryGraph = ({ utilizationData, excludeLeave = false, overallUtilization, }: UtilizationSummaryGraphProps) => { // Format percentage for display const formatPercentageLocal = (value: number) => { return formatPercentage(value, { decimals: 0 }); }; // Get utilization color based on percentage const getUtilizationColor = (utilization: number) => { if (utilization >= 70) return "bg-green-500"; if (utilization >= 50) return "bg-yellow-500"; if (utilization > 0) return "bg-red-500"; return "bg-red-300"; // Light red for 0% utilization }; return ( <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden p-3 h-full flex flex-col"> <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2"> Staff Utilisation Summary{" "} {excludeLeave && ( <span className="text-xs text-gray-500">(leave excluded)</span> )} </h3> <div className="space-y-1 flex-grow"> {/* Single column of staff utilization */} {utilizationData .sort((a, b) => excludeLeave ? b.adjustedUtilization - a.adjustedUtilization : b.utilization - a.utilization ) // Sort by utilization (highest to lowest) .map((staff) => ( <div key={staff.userId} className="flex items-center mb-1"> {/* Staff photo and name */} <div className="flex items-center w-32 mr-2"> {staff.avatarUrl ? ( <img src={staff.avatarUrl} alt="" className="h-5 w-5 rounded-full mr-1.5" /> ) : ( <div className="h-5 w-5 rounded-full bg-gray-200 dark:bg-gray-700 mr-1.5 flex items-center justify-center"> <span className="text-xs text-gray-500 dark:text-gray-400"> {staff.userName.charAt(0)} </span> </div> )} <span className="text-xs font-medium text-gray-900 dark:text-white truncate"> {staff.userName} </span> </div> {/* Utilization bar */} <div className="flex-1 flex items-center"> <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 mr-2"> {/* Use adjusted utilization if excludeLeave is true */} {(excludeLeave ? staff.adjustedUtilization : staff.utilization) > 0 ? ( // Normal utilization bar for values > 0% <div className={`h-2 rounded-full ${getUtilizationColor( excludeLeave ? staff.adjustedUtilization : staff.utilization )}`} style={{ width: `${Math.min( 100, excludeLeave ? staff.adjustedUtilization : staff.utilization )}%`, }} ></div> ) : ( // Full width light red bar for 0% utilization <div className="h-2 rounded-full bg-red-300 w-full"></div> )} </div> <span className="text-xs text-gray-900 dark:text-white min-w-[30px] text-right"> {formatPercentageLocal( excludeLeave ? staff.adjustedUtilization : staff.utilization )} </span> </div> </div> ))} </div> {/* Overall utilization indicator at the bottom */} <div className="mt-auto pt-3"> <div className="flex justify-between items-center mb-1"> <span className="text-sm font-medium text-gray-600 dark:text-gray-300"> Overall utilisation{" "} {excludeLeave && ( <span className="text-xs text-gray-400">(leave excluded)</span> )} </span> <span className="text-xl font-bold text-gray-900 dark:text-white"> {typeof overallUtilization === "number" ? formatPercentageLocal(overallUtilization) : "0%"} </span> </div> <div className="w-full bg-gray-200 rounded-full h-3 dark:bg-gray-700"> <div className={`h-3 rounded-full ${getUtilizationColor( typeof overallUtilization === "number" ? overallUtilization : 0 )}`} style={{ width: `${Math.min( 100, typeof overallUtilization === "number" ? overallUtilization : 0 )}%`, }} ></div> </div> </div> </div> ); }; export default UtilizationSummaryGraph; 