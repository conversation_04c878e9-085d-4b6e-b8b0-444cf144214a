import { jest } from '@jest/globals';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { initializeDatabase, getDatabase, closeDatabase } from '../../../src/api/services/db-service';

// Mock dependencies
jest.mock('better-sqlite3');
jest.mock('fs');
jest.mock('../../../src/utils/backend-logger');

describe('Database Service', () => {
  let mockDb: jest.Mocked<Database.Database>;

  beforeEach(() => {
    mockDb = {
      prepare: jest.fn(),
      exec: jest.fn(),
      close: jest.fn(),
      pragma: jest.fn(),
      inTransaction: false,
    } as any;
    
    (Database as unknown as jest.Mock).mockReturnValue(mockDb);
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Clean up any open connections
    closeDatabase();
  });

  describe('initializeDatabase', () => {
    it('should initialize database with migrations in development', async () => {
      process.env.NODE_ENV = 'development';
      const mockMigrationFiles = ['001_initial.sql', '002_update.sql'];
      const mockMigrationContent = {
        '001_initial.sql': 'CREATE TABLE test (id INTEGER);',
        '002_update.sql': 'ALTER TABLE test ADD COLUMN name TEXT;',
      };

      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue(mockMigrationFiles);
      (fs.readFileSync as jest.Mock).mockImplementation((filepath) => {
        const filename = path.basename(filepath as string);
        return mockMigrationContent[filename] || '';
      });

      const mockMigrationStmt = {
        all: jest.fn().mockReturnValue([]),
      };
      const mockInsertStmt = {
        run: jest.fn(),
      };
      
      mockDb.prepare
        .mockReturnValueOnce({ run: jest.fn() } as any) // CREATE TABLE migrations
        .mockReturnValueOnce(mockMigrationStmt as any) // SELECT from migrations
        .mockReturnValueOnce(mockInsertStmt as any) // INSERT migration 1
        .mockReturnValueOnce(mockMigrationStmt as any) // SELECT from migrations
        .mockReturnValueOnce(mockInsertStmt as any); // INSERT migration 2

      await initializeDatabase();

      expect(mockDb.pragma).toHaveBeenCalledWith('journal_mode = WAL');
      expect(mockDb.pragma).toHaveBeenCalledWith('foreign_keys = ON');
      expect(mockDb.exec).toHaveBeenCalledWith('CREATE TABLE test (id INTEGER);');
      expect(mockDb.exec).toHaveBeenCalledWith('ALTER TABLE test ADD COLUMN name TEXT;');
      expect(mockInsertStmt.run).toHaveBeenCalledTimes(2);
    });

    it('should initialize database with test data in test environment', async () => {
      process.env.NODE_ENV = 'test';
      process.env.USE_TEST_DATA = 'true';
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue([]);

      await initializeDatabase();

      expect(mockDb.pragma).toHaveBeenCalledWith('journal_mode = MEMORY');
      expect(mockDb.exec).toHaveBeenCalledWith(expect.stringContaining('INSERT INTO company'));
    });

    it('should skip migrations if already applied', async () => {
      process.env.NODE_ENV = 'development';
      const mockMigrationFiles = ['001_initial.sql'];
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue(mockMigrationFiles);
      
      const mockMigrationStmt = {
        all: jest.fn().mockReturnValue([{ name: '001_initial.sql' }]),
      };
      
      mockDb.prepare
        .mockReturnValueOnce({ run: jest.fn() } as any) // CREATE TABLE migrations
        .mockReturnValueOnce(mockMigrationStmt as any); // SELECT from migrations

      await initializeDatabase();

      expect(mockDb.exec).not.toHaveBeenCalledWith(expect.stringContaining('CREATE TABLE'));
    });

    it('should handle migration errors gracefully', async () => {
      process.env.NODE_ENV = 'development';
      const mockMigrationFiles = ['001_bad.sql'];
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue(mockMigrationFiles);
      (fs.readFileSync as jest.Mock).mockReturnValue('INVALID SQL;');
      
      const mockMigrationStmt = {
        all: jest.fn().mockReturnValue([]),
      };
      
      mockDb.prepare
        .mockReturnValueOnce({ run: jest.fn() } as any)
        .mockReturnValueOnce(mockMigrationStmt as any);
      mockDb.exec.mockImplementation(() => {
        throw new Error('SQL Error');
      });

      await expect(initializeDatabase()).rejects.toThrow('SQL Error');
    });

    it('should use production settings in production environment', async () => {
      process.env.NODE_ENV = 'production';
      process.env.DATABASE_PATH = '/data/prod.db';
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue([]);

      await initializeDatabase();

      expect(Database).toHaveBeenCalledWith('/data/prod.db');
      expect(mockDb.pragma).toHaveBeenCalledWith('journal_mode = WAL');
      expect(mockDb.pragma).toHaveBeenCalledWith('busy_timeout = 5000');
      expect(mockDb.pragma).toHaveBeenCalledWith('cache_size = -2000');
    });
  });

  describe('getDatabase', () => {
    it('should return existing database instance', async () => {
      await initializeDatabase();
      const db1 = getDatabase();
      const db2 = getDatabase();

      expect(db1).toBe(db2);
      expect(db1).toBe(mockDb);
    });

    it('should throw error if database not initialized', () => {
      expect(() => getDatabase()).toThrow('Database not initialized');
    });
  });

  describe('closeDatabase', () => {
    it('should close database connection', async () => {
      await initializeDatabase();
      closeDatabase();

      expect(mockDb.close).toHaveBeenCalled();
      expect(() => getDatabase()).toThrow('Database not initialized');
    });

    it('should handle multiple close calls gracefully', async () => {
      await initializeDatabase();
      closeDatabase();
      closeDatabase();

      expect(mockDb.close).toHaveBeenCalledTimes(1);
    });

    it('should do nothing if database not initialized', () => {
      closeDatabase();
      expect(mockDb.close).not.toHaveBeenCalled();
    });
  });

  describe('database health checks', () => {
    it('should verify foreign key constraints are enabled', async () => {
      const mockPragmaResult = { foreign_keys: 1 };
      mockDb.pragma.mockReturnValue(mockPragmaResult);

      await initializeDatabase();

      expect(mockDb.pragma).toHaveBeenCalledWith('foreign_keys = ON');
    });

    it('should handle database file permissions error', async () => {
      (Database as unknown as jest.Mock).mockImplementation(() => {
        throw new Error('SQLITE_CANTOPEN');
      });

      await expect(initializeDatabase()).rejects.toThrow('SQLITE_CANTOPEN');
    });
  });

  describe('test data initialization', () => {
    it('should insert comprehensive test data', async () => {
      process.env.NODE_ENV = 'test';
      process.env.USE_TEST_DATA = 'true';
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue([]);

      await initializeDatabase();

      // Check that test data includes all entity types
      const execCalls = mockDb.exec.mock.calls.map(call => call[0]);
      const testDataSql = execCalls.find(sql => sql.includes('INSERT INTO'));
      
      expect(testDataSql).toContain('INSERT INTO company');
      expect(testDataSql).toContain('INSERT INTO contact');
      expect(testDataSql).toContain('INSERT INTO deal');
      expect(testDataSql).toContain('INSERT INTO estimate');
      expect(testDataSql).toContain('INSERT INTO activity');
    });
  });

  describe('environment configuration', () => {
    it('should use memory database for tests by default', async () => {
      process.env.NODE_ENV = 'test';
      delete process.env.DATABASE_PATH;
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue([]);

      await initializeDatabase();

      expect(Database).toHaveBeenCalledWith(':memory:');
    });

    it('should respect custom database path', async () => {
      process.env.DATABASE_PATH = '/custom/path/db.sqlite';
      
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      (fs.readdirSync as jest.Mock).mockReturnValue([]);

      await initializeDatabase();

      expect(Database).toHaveBeenCalledWith('/custom/path/db.sqlite');
    });
  });
});