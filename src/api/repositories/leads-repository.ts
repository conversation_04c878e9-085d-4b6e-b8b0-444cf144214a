import { v4 as uuidv4 } from 'uuid';
import {
  Company,
  RadarCompanyUpdate
} from '../../types/company-types';
import {
  CompanyPriority,
  RadarState
} from '../../types/shared-types';

// Use any type for Database to avoid TypeScript errors
// TODO: Fix this type definition properly in a follow-up task
type Database = any;

/**
 * Repository for Leads-related data operations
 */
export class LeadsRepository {
  private db: Database;

  constructor(db: Database) {
    this.db = db;
    this.initializeDatabase();
  }

  /**
   * Initialize the database with required tables
   */
  private initializeDatabase() {
    // We no longer need to create the company_radar table
    // as we're using the unified company table
    console.log('LeadsRepository: Using unified company table');
  }

  /**
   * Get all companies with radar information including deal statistics and calculated spend
   * @returns Array of companies with radar state information and deal data
   */
  getRadarCompanies(): Company[] {
    const companies = this.db.prepare(`
      SELECT
        c.id,
        c.name,
        c.industry,
        c.size,
        c.website,
        c.current_spend AS currentSpend,
        c.potential_spend AS potentialSpend,
        c.priority,
        c.radar_state AS radarState,
        c.last_interaction_date AS lastInteractionDate,
        c.notes,
        c.harvest_id AS harvestId,
        c.hubspot_id AS hubspotId,
        c.source,
        c.address,
        c.description,
        c.created_at AS createdAt,
        c.updated_at AS updatedAt,
        c.created_by AS createdBy,
        c.updated_by AS updatedBy,
        -- Contact count
        (SELECT COUNT(*) FROM contact_company cc WHERE cc.company_id = c.id AND cc.deleted_at IS NULL) AS contacts,
        -- Deal statistics
        COALESCE(deal_stats.active_deals_count, 0) AS activeDealsCount,
        COALESCE(deal_stats.total_deal_value, 0) AS totalDealValue,
        -- Calculate total spend: manual + Harvest invoices (avoiding double-counting)
        (COALESCE(c.current_spend, 0) + COALESCE(harvest_cache.total_invoiced, 0)) AS totalSpend,
        -- Keep individual components for transparency
        c.current_spend AS manualSpend,
        COALESCE(harvest_cache.total_invoiced, 0) AS harvestSpend
      FROM company c
      LEFT JOIN (
        SELECT
          company_id,
          COUNT(*) AS active_deals_count,
          SUM(COALESCE(value, 0)) AS total_deal_value
        FROM deal
        WHERE deleted_at IS NULL
          AND stage NOT IN ('Closed won', 'Closed lost', 'Abandoned')
        GROUP BY company_id
      ) deal_stats ON c.id = deal_stats.company_id
      LEFT JOIN harvest_invoice_cache harvest_cache ON c.harvest_id = harvest_cache.harvest_client_id
      WHERE c.radar_state IS NOT NULL
      ORDER BY c.name
    `).all();

    return companies;
  }

  /**
   * Update a company's radar information
   * @param id Company ID
   * @param updateData Radar-specific update data
   * @returns Updated company
   */
  updateRadarCompany(id: string, updateData: RadarCompanyUpdate): Company | null {
    const now = new Date().toISOString();

    // Build the SET clause dynamically based on the provided fields
    const updateFields: string[] = [];
    const params: any = { id, updated_at: now, updated_by: 'system' };

    if (updateData.priority !== undefined) {
      updateFields.push('priority = :priority');
      params.priority = updateData.priority;
    }

    if (updateData.radarState !== undefined) {
      updateFields.push('radar_state = :radar_state');
      params.radar_state = updateData.radarState;
    }

    if (updateData.potentialSpend !== undefined) {
      updateFields.push('potential_spend = :potential_spend');
      params.potential_spend = updateData.potentialSpend;
    }

    if (updateData.notes !== undefined) {
      updateFields.push('notes = :notes');
      params.notes = updateData.notes;
    }

    if (updateData.lastInteractionDate !== undefined) {
      updateFields.push('last_interaction_date = :last_interaction_date');
      params.last_interaction_date = updateData.lastInteractionDate;
    }

    // Always update the updated_at and updated_by timestamps
    updateFields.push('updated_at = :updated_at');
    updateFields.push('updated_by = :updated_by');

    // Execute the update
    this.db.prepare(`
      UPDATE company
      SET ${updateFields.join(', ')}
      WHERE id = :id
    `).run(params);

    // Get the updated company
    return this.getRadarCompanyById(id);
  }

  /**
   * Get a company by ID
   * @param id Company ID
   * @returns Company with radar information
   */
  getRadarCompanyById(id: string): Company | null {
    const company = this.db.prepare(`
      SELECT
        id,
        name,
        industry,
        size,
        website,
        address,
        description,
        hubspot_id AS hubspotId,
        harvest_id AS harvestId,
        source,
        radar_state AS radarState,
        priority,
        current_spend AS currentSpend,
        potential_spend AS potentialSpend,
        last_interaction_date AS lastInteractionDate,
        notes,
        created_at AS createdAt,
        updated_at AS updatedAt,
        created_by AS createdBy,
        updated_by AS updatedBy,
        deleted_at AS deletedAt
      FROM company
      WHERE id = ? AND radar_state IS NOT NULL
    `).get(id);

    return company || null;
  }

  /**
   * Add a company to the radar
   * @param companyData Basic company data required for Radar
   * @returns Newly created company
   */
  addCompanyToRadar(companyData: {
    name: string;
    harvestId: number;
    radarState: RadarState;
    priority?: CompanyPriority;
    industry?: string;
    size?: string;
    website?: string;
  }): Company | null {
    const now = new Date().toISOString();
    const id = uuidv4();

    // Check if the company already exists in the table by harvest_id
    const existingCompany = this.db.prepare(`
      SELECT id FROM company WHERE harvest_id = ?
    `).get(companyData.harvestId);

    if (existingCompany) {
      // If the company exists but doesn't have radar_state, update it
      const companyWithRadar = this.db.prepare(`
        SELECT id FROM company WHERE harvest_id = ? AND radar_state IS NOT NULL
      `).get(companyData.harvestId);

      if (companyWithRadar) {
        throw new Error('Company already exists in the radar');
      }

      // Update the existing company with radar information
      this.db.prepare(`
        UPDATE company
        SET
          radar_state = ?,
          priority = ?,
          source = COALESCE(source, 'Harvest'),
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(
        companyData.radarState,
        companyData.priority || null,
        now,
        'system',
        existingCompany.id
      );

      return this.getRadarCompanyById(existingCompany.id);
    }

    // Insert a new company with radar information
    this.db.prepare(`
      INSERT INTO company (
        id,
        name,
        industry,
        size,
        website,
        harvest_id,
        source,
        radar_state,
        priority,
        created_at,
        updated_at,
        created_by,
        updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id,
      companyData.name,
      companyData.industry || null,
      companyData.size || null,
      companyData.website || null,
      companyData.harvestId,
      'Harvest',
      companyData.radarState,
      companyData.priority || null,
      now,
      now,
      'system',
      'system'
    );

    // Get the newly created company
    return this.getRadarCompanyById(id);
  }

  /**
   * Remove a company from the radar (clear radar-specific fields)
   * @param id Company ID
   * @returns Success status
   */
  removeCompanyFromRadar(id: string): boolean {
    try {
      // Instead of deleting the company, we just clear the radar-specific fields
      this.db.prepare(`
        UPDATE company
        SET
          radar_state = NULL,
          priority = NULL,
          current_spend = NULL,
          potential_spend = NULL,
          updated_at = ?,
          updated_by = ?
        WHERE id = ?
      `).run(new Date().toISOString(), 'system', id);

      return true;
    } catch (error) {
      console.error('Failed to remove company from radar:', error);
      return false;
    }
  }

  /**
   * Get companies by radar state
   * @param radarState Radar state to filter by
   * @returns Companies with the specified radar state
   */
  getCompaniesByRadarState(radarState: RadarState): Company[] {
    return this.db.prepare(`
      SELECT
        id,
        name,
        industry,
        size,
        website,
        current_spend AS currentSpend,
        potential_spend AS potentialSpend,
        priority,
        radar_state AS radarState,
        last_interaction_date AS lastInteractionDate,
        notes,
        harvest_id AS harvestId,
        hubspot_id AS hubspotId,
        source,
        address,
        description,
        created_at AS createdAt,
        updated_at AS updatedAt,
        created_by AS createdBy,
        updated_by AS updatedBy
      FROM company
      WHERE radar_state = ?
      ORDER BY name
    `).all(radarState);
  }

  /**
   * Get companies by priority
   * @param priority Priority to filter by
   * @returns Companies with the specified priority
   */
  getCompaniesByPriority(priority: CompanyPriority): Company[] {
    return this.db.prepare(`
      SELECT
        id,
        name,
        industry,
        size,
        website,
        current_spend AS currentSpend,
        potential_spend AS potentialSpend,
        priority,
        radar_state AS radarState,
        last_interaction_date AS lastInteractionDate,
        notes,
        harvest_id AS harvestId,
        hubspot_id AS hubspotId,
        source,
        address,
        description,
        created_at AS createdAt,
        updated_at AS updatedAt,
        created_by AS createdBy,
        updated_by AS updatedBy
      FROM company
      WHERE priority = ?
      ORDER BY name
    `).all(priority);
  }
}
