import { Client } from '@hubspot/api-client';
import { ContactCompanyRepository } from '../../repositories/contact-company-repository';
import { ContactRepository } from '../../repositories/contact-repository';
import { CompanyRepository } from '../../repositories/company-repository';
import { DealRepository } from '../../repositories/deal-repository';
import { ContactRoleRepository } from '../../repositories/relationships/contact-role-repository';
import { HubSpotProgressTracker, ImportResult } from './progress-tracker';
import { HubSpotUtils } from './utils';
import db from '../../services/db-service';

/**
 * HubSpot associations import functionality
 */
export class HubSpotAssociations {
  private contactCompanyRepository: ContactCompanyRepository;
  private contactRepository: ContactRepository;
  private companyRepository: CompanyRepository;
  private dealRepository: DealRepository;
  private contactRoleRepository: ContactRoleRepository;
  private progressTracker: HubSpotProgressTracker;

  constructor(progressTracker: HubSpotProgressTracker) {
    this.contactCompanyRepository = new ContactCompanyRepository(db);
    this.contactRepository = new ContactRepository();
    this.companyRepository = new CompanyRepository();
    this.dealRepository = new DealRepository();
    this.contactRoleRepository = new ContactRoleRepository();
    this.progressTracker = progressTracker;
  }

  /**
   * Import all associations from HubSpot
   */
  async importAssociations(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const created: Array<{ item: string }> = [];
    let totalCount = 0;

    try {
      console.log('Starting HubSpot associations import...');

      // Get all entities that have HubSpot IDs to calculate total
      const contacts = this.contactRepository.getAllContacts().filter(c => c.hubspotId);
      const deals = this.dealRepository.getAllDeals().filter(d => d.hubspotId);
      const totalEntities = contacts.length + deals.length;
      
      this.progressTracker.startStep('associations', totalEntities);

      // Import contact-company associations
      const contactCompanyResult = await this.importContactCompanyAssociations(client);
      totalCount += contactCompanyResult.count;
      created.push(...contactCompanyResult.created);
      errors.push(...contactCompanyResult.errors);

      // Import deal-contact associations (using contact_role table)
      const dealContactResult = await this.importDealContactAssociations(client);
      totalCount += dealContactResult.count;
      created.push(...dealContactResult.created);
      errors.push(...dealContactResult.errors);

      console.log(`Successfully imported ${totalCount} associations`);

      this.progressTracker.completeStep();

      return {
        success: errors.length === 0,
        count: totalCount,
        errors,
        updates: [],
        created
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error during associations import';
      console.error('Error during HubSpot associations import:', error);
      
      this.progressTracker.completeStep();
      
      return {
        success: false,
        count: totalCount,
        errors: [...errors, { item: 'General', error: errorMessage }],
        updates: [],
        created
      };
    }
  }

  /**
   * Import contact-company associations
   */
  private async importContactCompanyAssociations(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const created: Array<{ item: string }> = [];
    let count = 0;

    try {
      // Get all contacts with HubSpot IDs
      const contacts = this.contactRepository.getAllContacts()
        .filter(c => c.hubspotId);

      console.log(`[Associations] Found ${contacts.length} contacts with HubSpot IDs to process`);

      let processedCount = 0;
      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];
        const contactName = `${contact.firstName} ${contact.lastName}`;
        
        processedCount++;
        this.progressTracker.updateProgress(processedCount, `Processing associations for contact: ${contactName}`);

        try {
          // Get company associations for this contact
          const associations = await HubSpotUtils.withRetry(async () => {
            const response = await client.crm.associations.batchApi.read('contacts', 'companies', {
              inputs: [{ id: contact.hubspotId! }]
            });
            
            console.log(`[Associations] HubSpot API response for contact ${contactName}:`, JSON.stringify(response.results?.[0], null, 2));
            return response.results?.[0]?.to || [];
          });

          console.log(`[Associations] Found ${associations.length} company associations for contact ${contactName}`);

          // Process each association
          for (const association of associations) {
            console.log(`[Associations] Processing association to company HubSpot ID: ${association.id}`);
            const company = this.companyRepository.getCompanyByHubspotId(association.id);
            
            if (company) {
              console.log(`[Associations] Found company in database: ${company.name} (ID: ${company.id})`);
              
              // Check if association already exists using the correct method
              const alreadyLinked = this.contactCompanyRepository.isContactAssociatedWithCompany(contact.id, company.id);

              if (!alreadyLinked) {
                // Create the association
                // Cast to any to handle different API response types
                const associationWithTypes = association as any;
                const linkData = {
                  contactId: contact.id,
                  companyId: company.id,
                  role: associationWithTypes.associationTypes?.find((t: any) => t.label)?.label || 'Employee',
                  isPrimary: associationWithTypes.associationTypes?.some((t: any) => t.isPrimary) || false
                };

                console.log(`[Associations] Creating new association:`, linkData);
                
                try {
                  // Use the correct method from ContactCompanyRepository
                  this.contactCompanyRepository.associateContactWithCompany(
                    linkData.contactId,
                    linkData.companyId,
                    linkData.isPrimary,
                    linkData.role,
                    'HubSpot'
                  );
                  
                  count++;
                  created.push({ item: `${contactName} → ${company.name}` });
                  console.log(`[Associations] Successfully created association: ${contactName} → ${company.name}`);
                } catch (linkError) {
                  console.log(`[Associations] Failed to create association for ${contactName} → ${company.name}:`, linkError);
                }
              } else {
                console.log(`[Associations] Association already exists: ${contactName} → ${company.name}`);
              }
            } else {
              console.log(`[Associations] Company not found in database for HubSpot ID: ${association.id}`);
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[Associations] Error processing contact ${contactName}:`, error);
          errors.push({ item: `Contact: ${contactName}`, error: errorMessage });
        }
      }

      console.log(`[Associations] Deal-contact import complete: ${count} associations created`);
      if (errors.length > 0) {
        console.log(`[Associations] ${errors.length} errors occurred during deal-contact import`);
      }

      return {
        success: errors.length === 0,
        count,
        errors,
        updates: [],
        created
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('[Associations] Fatal error during import:', error);
      return {
        success: false,
        count: 0,
        errors: [{ item: 'Contact-Company Associations', error: errorMessage }],
        updates: [],
        created: []
      };
    }
  }

  /**
   * Import deal-contact associations
   */
  private async importDealContactAssociations(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const created: Array<{ item: string }> = [];
    let count = 0;

    try {
      // Get all deals with HubSpot IDs
      const deals = this.dealRepository.getAllDeals()
        .filter(d => d.hubspotId);

      console.log(`[Associations] Found ${deals.length} deals with HubSpot IDs to process`);

      // Continue from where contact-company left off
      let processedCount = this.contactRepository.getAllContacts().filter(c => c.hubspotId).length;
      
      for (let i = 0; i < deals.length; i++) {
        const deal = deals[i];
        
        processedCount++;
        this.progressTracker.updateProgress(processedCount, `Processing associations for deal: ${deal.name}`);

        try {
          // Get contact associations for this deal
          const associations = await HubSpotUtils.withRetry(async () => {
            const response = await client.crm.associations.batchApi.read('deals', 'contacts', {
              inputs: [{ id: deal.hubspotId! }]
            });
            
            console.log(`[Associations] HubSpot API response for deal ${deal.name}:`, JSON.stringify(response.results?.[0], null, 2));
            return response.results?.[0]?.to || [];
          });

          console.log(`[Associations] Found ${associations.length} contact associations for deal ${deal.name}`);

          // Process each association
          for (const association of associations) {
            console.log(`[Associations] Processing association to contact HubSpot ID: ${association.id}`);
            const contact = this.contactRepository.getContactByHubspotId(association.id);
            
            if (contact) {
              console.log(`[Associations] Found contact in database: ${contact.firstName} ${contact.lastName} (ID: ${contact.id})`);
              // Check if association already exists
              const existingRoles = this.contactRoleRepository.getDealContacts(deal.id);
              const isDuplicate = existingRoles.some((cr: any) =>
                cr.id === contact.id
              );

              if (!isDuplicate) {
                // Create the association using contact_role table
                // Cast to any to handle different API response types
                const associationWithTypes = association as any;
                const role = associationWithTypes.associationTypes?.find((t: any) => t.label)?.label || 'Contact';
                
                try {
                  this.contactRoleRepository.addContactToDeal(
                    deal.id,
                    contact.id,
                    role,
                    'HubSpot'
                  );
                  
                  count++;
                  created.push({ item: `${contact.firstName} ${contact.lastName} → ${deal.name}` });
                  console.log(`[Associations] Successfully created deal-contact association: ${contact.firstName} ${contact.lastName} → ${deal.name}`);
                } catch (linkError) {
                  console.log(`[Associations] Failed to create deal-contact association for ${contact.firstName} ${contact.lastName} → ${deal.name}:`, linkError);
                }
              } else {
                console.log(`[Associations] Deal-contact association already exists: ${contact.firstName} ${contact.lastName} → ${deal.name}`);
              }
            } else {
              console.log(`[Associations] Contact not found in database for HubSpot ID: ${association.id}`);
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push({ item: `Deal: ${deal.name}`, error: errorMessage });
        }
      }

      console.log(`[Associations] Deal-contact import complete: ${count} associations created`);
      if (errors.length > 0) {
        console.log(`[Associations] ${errors.length} errors occurred during deal-contact import`);
      }

      return {
        success: errors.length === 0,
        count,
        errors,
        updates: [],
        created
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        count: 0,
        errors: [{ item: 'Deal-Contact Associations', error: errorMessage }],
        updates: [],
        created: []
      };
    }
  }
}