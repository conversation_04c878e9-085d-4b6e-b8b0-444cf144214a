import React from 'react';
import { DayInfo, QuarterInfo, TooltipData } from './types';
import { useTheme } from './ThemeContext';
import { getMonthName } from './calendarUtils';
import { getColorClass } from '../../utils/colors';

interface CalendarDayProps {
  day: DayInfo;
  monthIndex: number;
  quarterInfo: QuarterInfo;
  setTooltip: React.Dispatch<React.SetStateAction<TooltipData>>;
  setHighlightedElements: React.Dispatch<React.SetStateAction<Set<string>>>;
  highlightedElements: Set<string>;
}

const CalendarDay: React.FC<CalendarDayProps> = ({
  day,
  monthIndex,
  quarterInfo,
  setTooltip,
  setHighlightedElements,
  highlightedElements
}) => {
  const { isDarkMode } = useTheme();

  // Generate unique IDs for this day's elements
  const paygwId = `paygw-${monthIndex}-${day.day}`;
  const gstId = `gst-${quarterInfo.colorIndex}-${day.day}`;

  // Check if elements are highlighted
  const isPaygwHighlighted = highlightedElements.has(paygwId);
  const isGstHighlighted = highlightedElements.has(gstId);

  // Get color classes
  const paygwClass = getColorClass('paygw', monthIndex, false, isDarkMode, isPaygwHighlighted);
  const gstClass = getColorClass('gst', quarterInfo.colorIndex, false, isDarkMode, isGstHighlighted);

  // Handle hover for PAYGW half
  const handlePaygwHover = (e: React.MouseEvent) => {
    // Create a new set with all elements to highlight
    const elementsToHighlight = new Set<string>();

    // Add all days in this month
    for (let i = 1; i <= 31; i++) {
      elementsToHighlight.add(`paygw-${monthIndex}-${i}`);
    }

    // Add the payment day
    elementsToHighlight.add(`paygw-payment-${monthIndex}`);

    // Update highlighted elements
    setHighlightedElements(elementsToHighlight);

    // Show tooltip
    setTooltip({
      visible: true,
      title: 'PAYGW Accrual Period',
      content: `${getMonthName(monthIndex)} PAYGW accrual (paid on the 21st of the following month)`,
      x: e.clientX,
      y: e.clientY
    });
  };

  // Handle hover for GST half
  const handleGstHover = (e: React.MouseEvent) => {
    // Create a new set with all elements to highlight
    const elementsToHighlight = new Set<string>();

    // Add all days in this quarter
    for (let i = 1; i <= 31; i++) {
      elementsToHighlight.add(`gst-${quarterInfo.colorIndex}-${i}`);
    }

    // Add the payment day
    elementsToHighlight.add(`gst-payment-${quarterInfo.colorIndex}`);

    // Update highlighted elements
    setHighlightedElements(elementsToHighlight);

    // Show tooltip
    setTooltip({
      visible: true,
      title: 'GST/BAS Accrual Period',
      content: `${quarterInfo.label} GST accrual (paid on the 26th of the month following the month after quarter end)`,
      x: e.clientX,
      y: e.clientY
    });
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    setHighlightedElements(new Set());
    setTooltip(prev => ({ ...prev, visible: false }));
  };

  // Special case: if this is the current day, show a special tooltip
  const handleCurrentDayHover = (e: React.MouseEvent) => {
    setTooltip({
      visible: true,
      title: 'Today',
      content: `${getMonthName(monthIndex)} ${day.day}, ${new Date().getFullYear()}`,
      x: e.clientX,
      y: e.clientY
    });
  };

  return (
    <div
      key={`day-${day.day}`}
      className={`tax-calendar-day tax-calendar-day-split ${day.isCurrentDay ? 'tax-calendar-current-day' : ''}`}
    >
      {/* Current day indicator */}
      {day.isCurrentDay && (
        <div
          className="absolute inset-0 border-2 border-green-400 dark:border-green-500 rounded-sm z-10 pointer-events-none"
          onMouseEnter={handleCurrentDayHover}
        ></div>
      )}

      {/* PAYGW half */}
      <div
        className={`tax-calendar-day-half-wrapper tax-calendar-day-paygw-wrapper ${isPaygwHighlighted ? 'tax-calendar-day-highlight' : ''}`}
        id={paygwId}
        onMouseEnter={handlePaygwHover}
        onMouseLeave={handleMouseLeave}
      >
        <div className={`tax-calendar-day-paygw ${paygwClass}`}></div>
      </div>

      {/* GST half */}
      <div
        className={`tax-calendar-day-half-wrapper tax-calendar-day-gst-wrapper ${isGstHighlighted ? 'tax-calendar-day-highlight' : ''}`}
        id={gstId}
        onMouseEnter={handleGstHover}
        onMouseLeave={handleMouseLeave}
      >
        <div className={`tax-calendar-day-gst ${gstClass}`}></div>
      </div>

      <span className="tax-calendar-day-number">{day.day}</span>
    </div>
  );
};

export default CalendarDay;
