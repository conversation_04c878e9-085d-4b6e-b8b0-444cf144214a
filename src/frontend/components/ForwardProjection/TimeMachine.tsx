import React, { useState, useEffect } from "react"; import { useProjection } from "./ProjectionContext"; import { format } from "date-fns"; /** * TimeMachine component for the cashflow projection page * Allows users to view historical snapshots of the cashflow projection * and create new snapshots manually */ export const TimeMachine: React.FC = () => { const { isHistoricalView, historicalDate, availableSnapshotDates, loadHistoricalData, exitHistoricalView, createSnapshot, isCreatingSnapshot, } = useProjection(); // State to track if we're on a small screen const [isSmallScreen, setIsSmallScreen] = useState(false); // Effect to check screen size on mount and resize useEffect(() => { const checkScreenSize = () => { setIsSmallScreen(window.innerWidth <= 480); }; // Initial check checkScreenSize(); // Add resize listener window.addEventListener("resize", checkScreenSize); // Cleanup return () => window.removeEventListener("resize", checkScreenSize); }, []); // Format date for display const formatDate = (dateString: string): string => { try { return format(new Date(dateString), "MMMM d, yyyy"); } catch (error) { return dateString; } }; // Handle date selection const handleDateChange = (event: React.ChangeEvent<HTMLSelectElement>) => { const date = event.target.value; if (date === "current") { exitHistoricalView(); } else { loadHistoricalData(date); } }; // Handle create snapshot button click const handleCreateSnapshot = async () => { try { await createSnapshot(); } catch (error) { console.error("Error creating snapshot:", error); } }; // Only render the historical view banner when in historical mode const HistoricalBanner = () => { if (!isHistoricalView) return null; return ( <div className="mb-4 p-2 bg-yellow-100 border border-yellow-300 rounded"> <div className="flex items-center justify-between"> <span> <span className="mr-2">🕒</span> Viewing snapshot from {formatDate(historicalDate || "")} </span> <button onClick={exitHistoricalView} className="px-2 py-1 bg-yellow-200 hover:bg-yellow-300 text-yellow-800 rounded text-sm" > Return to Current View </button> </div> </div> ); }; // The dropdown component - styled to match the timeframe dropdown const SnapshotSelector = () => ( <div className="flex items-center"> {/* Desktop version */} <select id="time-machine-select" value={isHistoricalView ? historicalDate || "" : "current"} onChange={handleDateChange} className="form-select text-xs sm:text-sm py-2 px-2 sm:px-3 rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-secondary dark:focus:border-secondary focus:ring focus:ring-secondary/20 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium min-h-[40px] hidden sm:block" style={{ minWidth: "140px", width: "auto", maxWidth: "180px", }} disabled={availableSnapshotDates.length === 0} title="View historical snapshots" > <option value="current">Current View</option> {availableSnapshotDates.length === 0 && ( <option disabled>No snapshots available</option> )} {availableSnapshotDates.map((date) => ( <option key={date} value={date}> {format(new Date(date), "d MMM yyyy")} </option> ))} </select> {/* Mobile version */} <select id="time-machine-select-mobile" value={isHistoricalView ? historicalDate || "" : "current"} onChange={handleDateChange} className="form-select text-xs py-2 px-2 rounded-md border border-gray-300 dark:border-gray-600 shadow-sm focus:border-secondary dark:focus:border-secondary focus:ring focus:ring-secondary/20 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium min-h-[40px] block sm:hidden" style={{ minWidth: "80px", width: "auto", maxWidth: "100px", }} disabled={availableSnapshotDates.length === 0} title="View historical snapshots" > <option value="current">Current</option> {availableSnapshotDates.length === 0 && ( <option disabled>No snapshots</option> )} {availableSnapshotDates.map((date) => ( <option key={`mobile-${date}`} value={date}> {format(new Date(date), "d MMM")} </option> ))} </select> </div> ); // The create snapshot button - styled to match the labels toggle const CreateSnapshotButton = () => { if (isHistoricalView) return null; return ( <button onClick={handleCreateSnapshot} disabled={isCreatingSnapshot} className="flex items-center px-3 py-2 text-xs sm:text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-70 disabled:cursor-not-allowed min-h-[40px]" title="Create a snapshot of the current cashflow projection" > {isCreatingSnapshot ? ( <> <span className="inline-block animate-spin mr-1.5">⟳</span> <span className="font-medium">Saving...</span> </> ) : ( <span className="font-medium">Save View</span> )} </button> ); }; return ( <div className="time-machine-controls flex items-center space-x-2 sm:space-x-4"> <SnapshotSelector /> <CreateSnapshotButton /> </div> ); }; export default TimeMachine; 