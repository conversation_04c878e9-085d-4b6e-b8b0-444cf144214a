# Color Audit Results - Non-Flexoki Color References

## Summary
After a comprehensive search, I found several remaining non-Flexoki color references that need to be addressed:

## 1. Non-Flexoki Colors in TypeScript/JavaScript

### WeeklyEffortHistogram.tsx
- **File**: `src/frontend/components/Estimate/WeeklyEffortHistogram.tsx`
- **Lines**: 164-165
- **Issue**: Uses `rgba(255,255,255,0.25)` and `rgba(255,255,255,0.4)` for pattern overlays
- **Action**: Replace with Flexoki white/transparency values

### visualization-utils.ts
- **File**: `src/frontend/components/Estimate/utils/visualization-utils.ts`
- **Lines**: Multiple
- **Issue**: Uses HSL colors and non-Flexoki hex colors (#2C3E50, #f1f5f9) for dynamic staff color generation
- **Action**: This appears intentional for generating varied colors for staff visualization. May need to keep but document.

## 2. Non-Flexoki Colors in CSS

### App.css
- **File**: `src/frontend/styles/App.css`
- **Line**: 68
- **Issue**: `.last-synced` uses `color: #777`
- **Action**: Replace with appropriate Flexoki gray

### modern-design-system.css
- **File**: `src/frontend/styles/modern-design-system.css`
- **Multiple lines**
- **Issue**: Shadow definitions use `rgba(0, 0, 0, ...)` for various opacity levels
- **Action**: These are for shadows and may be acceptable, but consider using Flexoki shadow system

### tax-calendar.css
- **File**: `src/frontend/styles/components/tax-calendar.css`
- **Multiple lines**
- **Issues**:
  - `rgba(255, 255, 255, 0.7)` and `rgba(255, 255, 255, 0.98)` for backgrounds
  - `rgba(148, 163, 184, 0.9)` for dark mode glow
  - Various shadow definitions with `rgba(0, 0, 0, ...)`
- **Action**: Replace with Flexoki equivalents

### Other CSS Files
- Various files use `transparent` keyword - this is acceptable
- Shadow definitions using `rgba(0, 0, 0, ...)` appear throughout - consider standardizing

## 3. Test Files
Test files contain color references but these appear to be for testing purposes and don't affect the UI.

## 4. Documentation
Documentation files contain color references but these are descriptive and don't affect the application.

## Recommendations

1. **Critical Fixes** (affecting UI):
   - Fix `#777` in App.css
   - Update WeeklyEffortHistogram.tsx rgba values
   - Review tax-calendar.css rgba values

2. **Consider Keeping**:
   - visualization-utils.ts HSL generation (needed for dynamic colors)
   - Shadow definitions (standard practice, minimal visual impact)
   - `transparent` keyword usage (CSS standard)

3. **Low Priority**:
   - Test file color references
   - Documentation color mentions

## Files to Update
1. `src/frontend/styles/App.css` - Line 68
2. `src/frontend/components/Estimate/WeeklyEffortHistogram.tsx` - Lines 164-165
3. `src/frontend/styles/components/tax-calendar.css` - Multiple rgba values
4. Consider creating a standardized shadow system using Flexoki base colors