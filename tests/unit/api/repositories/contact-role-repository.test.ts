/**
 * Contact Role Repository Test
 * 
 * Tests for the contact role repository focusing on business behavior of contact-deal relationships.
 * Uses an in-memory SQLite database for realistic testing without mocks.
 */
import ContactRoleRepository from '../../../../src/api/repositories/relationships/contact-role-repository';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

describe('ContactRoleRepository - Business Behavior', () => {
  let repository: ContactRoleRepository;
  let db: Database.Database;
  
  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Initialize schema using the unified migration
    const migrationPath = path.join(__dirname, '../../../../migrations/000_unified_schema.sql');
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    } else {
      throw new Error(`Migration file not found at ${migrationPath}`);
    }
  });
  
  beforeEach(() => {
    repository = new ContactRoleRepository();
    
    // Clear data before each test
    db.prepare('DELETE FROM contact_role').run();
    db.prepare('DELETE FROM deal').run();
    db.prepare('DELETE FROM contact').run();
    db.prepare('DELETE FROM company').run();
    
    // Insert test data
    db.prepare(`
      INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
      VALUES ('company-1', 'Enterprise Corp', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO contact (id, first_name, last_name, email, created_at, updated_at, created_by, updated_by)
      VALUES ('contact-1', 'John', 'Doe', '<EMAIL>', datetime('now'), datetime('now'), 'test-user', 'test-user'),
             ('contact-2', 'Jane', 'Smith', '<EMAIL>', datetime('now'), datetime('now'), 'test-user', 'test-user'),
             ('contact-3', 'Bob', 'Wilson', '<EMAIL>', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO deal (id, deal_name, company_id, stage, amount, created_at, updated_at, created_by, updated_by)
      VALUES ('deal-1', 'Enterprise Deal', 'company-1', 'negotiation', 500000, datetime('now'), datetime('now'), 'test-user', 'test-user'),
             ('deal-2', 'Expansion Project', 'company-1', 'proposal', 250000, datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
  });
  
  afterAll(() => {
    db.close();
  });

  describe('Business Rule: Deal Team Management', () => {
    it('should assign contacts to deal teams with specific roles', () => {
      const roleData = {
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Decision Maker',
        permissions: ['view', 'edit', 'approve'],
      };

      const result = repository.addContactToDeal(roleData);

      // Business expectation: Track who's involved in each deal
      expect(result.deal_id).toBe('deal-1');
      expect(result.contact_id).toBe('contact-1');
      expect(result.role).toBe('Decision Maker');
      expect(result.permissions).toEqual(['view', 'edit', 'approve']);
      expect(result.created_at).toBeDefined();
    });

    it('should define different permission levels for different roles', () => {
      // Decision maker - full permissions
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Decision Maker',
        permissions: ['view', 'edit', 'approve', 'sign'],
      });
      
      // Technical evaluator - limited permissions
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-2',
        role: 'Technical Evaluator',
        permissions: ['view', 'comment'],
      });
      
      // Procurement - specific permissions
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-3',
        role: 'Procurement',
        permissions: ['view', 'negotiate_price'],
      });

      const dealTeam = repository.getDealContacts('deal-1');

      // Business requirement: Support complex buying committees
      expect(dealTeam).toHaveLength(3);
      const decisionMaker = dealTeam.find(c => c.role === 'Decision Maker');
      const techEval = dealTeam.find(c => c.role === 'Technical Evaluator');
      const procurement = dealTeam.find(c => c.role === 'Procurement');
      
      expect(decisionMaker?.permissions).toContain('approve');
      expect(techEval?.permissions).not.toContain('approve');
      expect(procurement?.permissions).toContain('negotiate_price');
    });

    it('should prevent duplicate contact assignments to the same deal', () => {
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Technical Advisor',
        permissions: ['view', 'comment'],
      });

      // Business rule: One role per contact per deal
      expect(() => repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Different Role',
        permissions: ['view'],
      })).toThrow(/constraint/i);
    });
  });

  describe('Business Rule: Deal Team Visibility', () => {
    beforeEach(() => {
      // Set up a deal team
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Executive Sponsor',
        permissions: ['view', 'edit', 'approve', 'sign'],
      });
      
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-2',
        role: 'Technical Lead',
        permissions: ['view', 'comment', 'technical_approval'],
      });
    });

    it('should show complete buying committee for a deal', () => {
      const dealTeam = repository.getDealContacts('deal-1');

      // Business requirement: Sales team needs visibility into all stakeholders
      expect(dealTeam).toHaveLength(2);
      
      const sponsor = dealTeam.find(c => c.contact_id === 'contact-1');
      expect(sponsor).toBeDefined();
      expect(sponsor?.first_name).toBe('John');
      expect(sponsor?.last_name).toBe('Doe');
      expect(sponsor?.email).toBe('<EMAIL>');
      expect(sponsor?.role).toBe('Executive Sponsor');
      expect(sponsor?.permissions).toContain('sign');
      
      const techLead = dealTeam.find(c => c.contact_id === 'contact-2');
      expect(techLead).toBeDefined();
      expect(techLead?.role).toBe('Technical Lead');
      expect(techLead?.permissions).toContain('technical_approval');
    });

    it('should handle deals with no assigned contacts', () => {
      const emptyTeam = repository.getDealContacts('deal-2');

      // Business expectation: New deals start with empty teams
      expect(emptyTeam).toEqual([]);
    });
  });

  describe('Business Rule: Contact Deal Involvement', () => {
    beforeEach(() => {
      // Set up contact involvement in multiple deals
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Decision Maker',
        permissions: ['view', 'edit', 'approve'],
      });
      
      repository.addContactToDeal({
        deal_id: 'deal-2',
        contact_id: 'contact-1',
        role: 'Executive Champion',
        permissions: ['view', 'approve', 'sign'],
      });
    });

    it('should track all deals a contact is involved in', () => {
      const contactDeals = repository.getContactDeals('contact-1');

      // Business requirement: Understand contact's full engagement
      expect(contactDeals).toHaveLength(2);
      
      const enterpriseDeal = contactDeals.find(d => d.deal_id === 'deal-1');
      expect(enterpriseDeal).toBeDefined();
      expect(enterpriseDeal?.name).toBe('Enterprise Deal');
      expect(enterpriseDeal?.amount).toBe(500000);
      expect(enterpriseDeal?.stage).toBe('negotiation');
      expect(enterpriseDeal?.role).toBe('Decision Maker');
      
      const expansionDeal = contactDeals.find(d => d.deal_id === 'deal-2');
      expect(expansionDeal).toBeDefined();
      expect(expansionDeal?.name).toBe('Expansion Project');
      expect(expansionDeal?.role).toBe('Executive Champion');
    });

    it('should show different roles across different deals', () => {
      const deals = repository.getContactDeals('contact-1');
      
      // Business insight: Same person, different roles
      const roles = deals.map(d => d.role);
      expect(roles).toContain('Decision Maker');
      expect(roles).toContain('Executive Champion');
    });
  });

  describe('Business Rule: Role Evolution', () => {
    beforeEach(() => {
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Evaluator',
        permissions: ['view', 'comment'],
      });
    });

    it('should promote contact as their involvement deepens', () => {
      // Business scenario: Contact gains more authority during sales process
      const promoted = repository.updateContactRole('deal-1', 'contact-1', {
        role: 'Executive Sponsor',
        permissions: ['view', 'edit', 'approve', 'sign'],
      });

      expect(promoted).toBe(true);
      
      // Verify the promotion
      const updated = repository.getContactPermissions('deal-1', 'contact-1');
      expect(updated?.role).toBe('Executive Sponsor');
      expect(updated?.permissions).toContain('sign');
    });

    it('should handle role changes reflecting organizational changes', () => {
      // Business scenario: Contact changes departments but stays involved
      const changed = repository.updateContactRole('deal-1', 'contact-1', {
        role: 'Finance Approver',
        permissions: ['view', 'approve_budget'],
      });

      expect(changed).toBe(true);
      
      const newRole = repository.getContactPermissions('deal-1', 'contact-1');
      expect(newRole?.permissions).toContain('approve_budget');
      expect(newRole?.permissions).not.toContain('comment');
    });

    it('should fail when updating non-existent relationship', () => {
      const result = repository.updateContactRole('deal-99', 'contact-99', {
        role: 'New Role'
      });

      expect(result).toBe(false);
    });
  });

  describe('Business Rule: Team Member Removal', () => {
    beforeEach(() => {
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Technical Evaluator',
        permissions: ['view', 'comment'],
      });
    });

    it('should remove contact when they leave the buying process', () => {
      // Business scenario: Contact changes jobs or is no longer involved
      const removed = repository.removeContactFromDeal('deal-1', 'contact-1');

      expect(removed).toBe(true);
      
      // Verify removal
      const dealTeam = repository.getDealContacts('deal-1');
      expect(dealTeam.find(c => c.contact_id === 'contact-1')).toBeUndefined();
    });

    it('should handle removal of already removed contacts gracefully', () => {
      repository.removeContactFromDeal('deal-1', 'contact-1');
      
      // Try to remove again
      const secondRemoval = repository.removeContactFromDeal('deal-1', 'contact-1');
      
      // Business expectation: No error for redundant removals
      expect(secondRemoval).toBe(false);
    });
  });

  describe('Business Rule: Permission Checking', () => {
    beforeEach(() => {
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Budget Holder',
        permissions: ['view', 'approve_budget', 'negotiate_terms'],
      });
    });

    it('should retrieve specific permissions for access control', () => {
      const permissions = repository.getContactPermissions('deal-1', 'contact-1');

      // Business requirement: Fine-grained access control
      expect(permissions).toBeDefined();
      expect(permissions?.role).toBe('Budget Holder');
      expect(permissions?.permissions).toContain('approve_budget');
      expect(permissions?.permissions).toContain('negotiate_terms');
      expect(permissions?.permissions).not.toContain('sign'); // Can approve but not sign
    });

    it('should return null for contacts not on the deal', () => {
      const permissions = repository.getContactPermissions('deal-1', 'contact-3');

      // Business expectation: No access for uninvolved contacts
      expect(permissions).toBeNull();
    });
  });

  describe('Business Rule: Authorization Checks', () => {
    beforeEach(() => {
      // Set up different permission levels
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Full Access',
        permissions: ['view', 'edit', 'approve', 'sign'],
      });
      
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-2',
        role: 'Read Only',
        permissions: ['view'],
      });
    });

    it('should verify permission before allowing actions', () => {
      // Business requirement: Enforce permission boundaries
      expect(repository.hasPermission('deal-1', 'contact-1', 'edit')).toBe(true);
      expect(repository.hasPermission('deal-1', 'contact-1', 'approve')).toBe(true);
      expect(repository.hasPermission('deal-1', 'contact-1', 'sign')).toBe(true);
    });

    it('should deny permissions not explicitly granted', () => {
      // Business security: Principle of least privilege
      expect(repository.hasPermission('deal-1', 'contact-2', 'edit')).toBe(false);
      expect(repository.hasPermission('deal-1', 'contact-2', 'approve')).toBe(false);
      expect(repository.hasPermission('deal-1', 'contact-2', 'delete')).toBe(false);
    });

    it('should deny all permissions for non-team members', () => {
      // Business security: No implicit access
      expect(repository.hasPermission('deal-1', 'contact-3', 'view')).toBe(false);
      expect(repository.hasPermission('deal-2', 'contact-1', 'view')).toBe(false);
    });
  });

  describe('Business Rule: Role-Based Deal Discovery', () => {
    beforeEach(() => {
      // Contact 1 is decision maker in multiple deals
      repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Decision Maker',
        permissions: ['view', 'edit', 'approve'],
      });
      
      repository.addContactToDeal({
        deal_id: 'deal-2',
        contact_id: 'contact-1',
        role: 'Decision Maker',
        permissions: ['view', 'edit', 'approve', 'sign'],
      });
      
      // Same contact, different role in another deal
      repository.addContactToDeal({
        deal_id: 'deal-2',
        contact_id: 'contact-2',
        role: 'Technical Advisor',
        permissions: ['view', 'comment'],
      });
    });

    it('should find all deals where contact has decision-making authority', () => {
      const decisionMakerDeals = repository.getDealsByRole('contact-1', 'Decision Maker');

      // Business insight: Track decision-making influence
      expect(decisionMakerDeals).toHaveLength(2);
      
      const deal1 = decisionMakerDeals.find(d => d.deal_id === 'deal-1');
      expect(deal1?.deal_name).toBe('Enterprise Deal');
      expect(deal1?.permissions).toContain('approve');
      
      const deal2 = decisionMakerDeals.find(d => d.deal_id === 'deal-2');
      expect(deal2?.deal_name).toBe('Expansion Project');
      expect(deal2?.permissions).toContain('sign');
    });

    it('should distinguish between different roles', () => {
      const advisorDeals = repository.getDealsByRole('contact-2', 'Technical Advisor');
      
      // Business requirement: Role-specific views
      expect(advisorDeals).toHaveLength(1);
      expect(advisorDeals[0].permissions).not.toContain('approve');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid deal references', () => {
      // Business expectation: Clear errors for invalid data
      expect(() => repository.addContactToDeal({
        deal_id: 'non-existent-deal',
        contact_id: 'contact-1',
        role: 'Test Role',
        permissions: ['view'],
      })).toThrow(/constraint/i);
    });

    it('should handle invalid contact references', () => {
      // Business expectation: Maintain data integrity
      expect(() => repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'non-existent-contact',
        role: 'Test Role',
        permissions: ['view'],
      })).toThrow(/constraint/i);
    });

    it('should handle empty permission arrays', () => {
      // Business flexibility: Allow roles without specific permissions
      const result = repository.addContactToDeal({
        deal_id: 'deal-1',
        contact_id: 'contact-1',
        role: 'Observer',
        permissions: [],
      });
      
      expect(result.permissions).toEqual([]);
    });
  });
});