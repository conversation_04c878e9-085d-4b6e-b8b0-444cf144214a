/**
 * Common types and interfaces for API integrations
 */

export interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, string | number | boolean>;
  headers?: Record<string, string>;
  data?: unknown;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface ApiError extends Error {
  status?: number;
  code?: string;
  data?: unknown;
}

export interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  retryAfterMs: number;
}

export interface ApiClientConfig {
  baseUrl: string;
  rateLimit?: RateLimitConfig;
  timeout?: number;
  retryAttempts?: number;
}

export interface ApiResponse<T> {
  data: T;
  meta?: {
    page?: number;
    perPage?: number;
    total?: number;
  };
}
