import React, { useState, useEffect } from "react";
import ProjectInvoiceSettings from "./ProjectInvoiceSettings";
import ProjectedInvoices from "./ProjectedInvoices";
import LoadingIndicator from "./LoadingIndicator";
import { useLoading } from "../../contexts/LoadingContext";
import { formatCurrency } from "./utils";
import { useProjection } from "./ProjectionContext";
import {
  getProjects,
  getProjectSettings,
  saveProjectSettings,
  getProjectedInvoices,
  Project,
  ProjectSetting,
  ProjectedInvoice,
} from "../../api/harvest";

// Main component
const ProjectionSettings: React.FC = () => {
  const { refreshProjection } = useProjection();
  const { setLoading: setGlobalLoading } = useLoading();

  // State for data
  const [loading, setLocalLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectSettings, setProjectSettings] = useState<ProjectSetting[]>([]);
  const [projectedInvoices, setProjectedInvoices] = useState<
    ProjectedInvoice[]
  >([]);
  const [showAboutInfo, setShowAboutInfo] = useState<boolean>(false);

  // Load projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      setLocalLoading(true);
      setGlobalLoading(true, "harvest");
      setError(null);

      try {
        const projectsData = await getProjects();
        setProjects(projectsData);

        // Initialize settings if none exist
        const settingsData = await getProjectSettings();

        if (settingsData.length > 0) {
          setProjectSettings(settingsData);
        } else if (projectsData.length > 0 && settingsData.length === 0) {
          // Create default settings for new projects
          const defaultSettings = projectsData.map((project) => ({
            projectId: project.id,
            invoiceFrequency: "biweekly",
            invoiceIntervalDays: 14,
            paymentTerms: 30,
          }));

          setProjectSettings(defaultSettings);
          await saveProjectSettings(defaultSettings);
        }
      } catch (err: any) {
        setError(err.message || "Failed to load projects");
        console.error("Error loading projects:", err);
      } finally {
        setLocalLoading(false);
        setGlobalLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // Load projected invoices when settings change
  useEffect(() => {
    if (projects.length === 0 || projectSettings.length === 0) return;

    const fetchProjectedInvoices = async () => {
      setLocalLoading(true);
      setGlobalLoading(true, "harvest");
      setError(null);

      try {
        // Use a longer timeframe for Smart Forecast to capture all future projects
        const timeframe = 365; // Show up to 1 year of projected invoices
        const invoicesData = await getProjectedInvoices(timeframe);
        setProjectedInvoices(invoicesData);
      } catch (err: any) {
        setError(err.message || "Failed to load projected invoices");
        console.error("Error loading projected invoices:", err);
      } finally {
        setLocalLoading(false);
        setGlobalLoading(false);
      }
    };

    fetchProjectedInvoices();
  }, [projects, projectSettings]);

  // Handle settings changes
  const handleSettingsChange = (newSettings: ProjectSetting[]) => {
    setProjectSettings(newSettings);
  };

  // Apply settings to update projections

  // Apply projection settings to cashflow
  const handleApplySettings = async () => {
    setLocalLoading(true);
    setGlobalLoading(true, "harvest");
    setError(null);

    try {
      // Save settings to API
      await saveProjectSettings(projectSettings);

      // Refresh projected invoices with longer timeframe
      const timeframe = 365; // Show up to 1 year of projected invoices
      const invoicesData = await getProjectedInvoices(timeframe);
      setProjectedInvoices(invoicesData);

      // Refresh projection in the main cashflow view
      refreshProjection();

      alert("Projection settings applied successfully!");
    } catch (err: any) {
      setError(err.message || "Failed to apply settings");
      console.error("Error applying settings:", err);
      alert("Failed to apply settings. Please try again.");
    } finally {
      setLocalLoading(false);
      setGlobalLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="w-full px-2 md:px-4">
        <div className="bg-white dark:bg-gray-800 px-0 py-2 md:p-3 md:rounded-lg mb-3">
          <div className="px-4 py-3 mb-4">
            {/* Info accordion - same style as Audit Log */}
            <div className="border border-blue-200 dark:border-blue-800/30 bg-blue-50/50 dark:bg-blue-900/10 rounded-lg overflow-hidden">
              <button
                className="w-full flex justify-between items-center p-4 focus:outline-none"
                onClick={() => setShowAboutInfo(!showAboutInfo)}
              >
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 text-blue-500 dark:text-blue-400 mr-1.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <span className="font-medium text-sm text-blue-700 dark:text-blue-300">
                    About Smart Forecast
                  </span>
                </div>
                <svg
                  className={`w-5 h-5 text-blue-500 dark:text-blue-400 transition-transform duration-200 ${
                    showAboutInfo ? "transform rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>

              {showAboutInfo && (
                <div className="px-4 pb-4 pt-1 border-t border-blue-100 dark:border-blue-800/30">
                  <div className="border-l-2 border-blue-300 dark:border-blue-700 pl-4 py-1 text-sm text-gray-600 dark:text-gray-300">
                    <p className="mb-2">
                      Smart Forecast uses project budget data to generate
                      accurate income projections by:
                    </p>
                    <ul className="list-disc pl-5 mt-1 space-y-1">
                      <li>
                        Calculating invoice dates based on invoice frequency and
                        project timeline
                      </li>
                      <li>
                        Distributing remaining project budgets across invoice
                        periods
                      </li>
                      <li>
                        Applying payment terms to determine when money will
                        arrive
                      </li>
                    </ul>
                    <p className="mt-2">
                      Configure each project's invoice frequency and payment
                      terms below to improve projection accuracy.
                    </p>
                    <div className="mt-3 text-xs text-right">
                      <a
                        href="#income-section"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        More details in Help docs →
                      </a>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Harvest warning box */}
            <div className="mt-3 border border-[rgba(250,93,0,0.3)] bg-[rgba(250,93,0,0.05)] dark:bg-[rgba(250,93,0,0.1)] rounded-lg overflow-hidden shadow-sm">
              <div className="px-4 py-2.5">
                <div className="sm:flex sm:items-center sm:justify-between">
                  <div className="flex items-start">
                    <svg
                      className="w-4 h-4 text-[rgba(250,93,0,0.9)] flex-shrink-0 mr-1.5 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                    <div className="flex-1 pr-2">
                      <h4 className="text-xs font-medium text-[rgba(250,93,0,0.9)] mb-0.5">
                        Important: Check Harvest Project Settings
                      </h4>
                      <p className="text-xs text-gray-700 dark:text-gray-300 mb-0">
                        This system relies on{" "}
                        <strong>correct project settings in Harvest</strong>.
                        <br />
                        <span className="opacity-80">
                          Ensure start dates, end dates, and budgets are
                          accurate.
                        </span>
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 sm:mt-0 sm:ml-2 sm:flex-shrink-0">
                    <a
                      href="https://onbord.harvestapp.com/projects?filter=with-budget&show_forecast_data=true"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-3 py-2 bg-[rgba(250,93,0,0.9)] text-white text-xs font-medium rounded-md shadow-sm hover:bg-[rgba(250,93,0,1)] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[rgba(250,93,0,0.7)]"
                    >
                      Go to Harvest
                      <svg
                        className="ml-1.5 w-3.5 h-3.5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="px-4 py-8">
              <div className="flex flex-col items-center justify-center">
                <svg
                  className="animate-spin text-blue-500 w-8 h-8 mb-3"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Loading project settings...
                </p>
              </div>
            </div>
          ) : (
            <ProjectInvoiceSettings
              projects={projects}
              settings={projectSettings}
              onSettingsChange={handleSettingsChange}
            />
          )}

          <div className="px-4 py-3">
            <button
              onClick={handleApplySettings}
              disabled={loading}
              className="btn-modern btn-modern--primary w-full sm:w-auto px-4 py-3 sm:py-2 h-12 sm:h-10 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 tap-highlight-transparent touch-manipulation font-medium disabled:opacity-70 disabled:cursor-not-allowed onboarding-apply-settings"
              aria-label="Apply settings to cashflow projection"
            >
              {loading ? "Processing..." : "Apply Settings to Cashflow"}
            </button>
          </div>
        </div>
      </div>

      <div className="w-full px-2 md:px-4">
        {loading ? (
          <div>
            <h2 className="text-lg font-semibold text-primary mb-3 flex items-center">
              <svg
                className="w-4 h-4 mr-1.5 text-secondary"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              Projected Income
            </h2>
            <div className="bg-white dark:bg-gray-800 px-0 py-2 md:p-3 md:rounded-lg mb-3">
              <div className="flex flex-col items-center justify-center py-8">
                <svg
                  className="animate-spin text-blue-500 w-8 h-8 mb-3"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Loading projected income data...
                </p>
              </div>
            </div>
          </div>
        ) : (
          projectedInvoices.length > 0 && (
            <ProjectedInvoices projectedInvoices={projectedInvoices} />
          )
        )}
      </div>
    </div>
  );
};

export default ProjectionSettings;
