import React, { useState, useLayoutEffect, useMemo, useCallback } from "react"; // Added useMemo and useCallback
import { format } from "date-fns";
import { useIsMobile } from "../../hooks"; // Import mobile detection hook
import {
  // LineChart, // Removed LineChart
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  ComposedChart,
  ReferenceLine,
  Customized,
  // Removed Brush import
} from "recharts";

// TypeScript interfaces for Recharts components
interface ChartClickData {
  activePayload?: Array<{
    payload?: {
      date?: string;
      [key: string]: unknown;
    };
  }>;
}

interface DotProps {
  cx?: number;
  cy?: number;
  payload?: {
    date?: string;
    hasSignificantInflow?: boolean;
    hasSignificantOutflow?: boolean;
    significantInflowName?: string;
    significantInflowAmount?: number;
    significantOutflowName?: string;
    significantOutflowAmount?: number;
    inflowLabelPosition?: number;
    outflowLabelPosition?: number;
    [key: string]: unknown;
  };
}

// Create properly typed component aliases
const RechartLineSeries = Line;
const RechartXAxis = XAxis;
const RechartYAxis = YAxis;
const RechartGrid = CartesianGrid;
const RechartTooltip = Tooltip;
const RechartLegend = Legend;
const RechartContainer = ResponsiveContainer;
const RechartArea = Area;
const RechartComposed = ComposedChart;
const RechartReferenceLine = ReferenceLine;
const RechartCustomized = Customized;
// Removed Brush alias
import {
  useProjection,
  formatChartData,
  formatScenarioChartData,
  ChartTooltip,
  ScenarioChartDataPoint,
} from ".";
import ScenarioToggle from "./ScenarioToggle";

// Removed useResponsiveSize hook - using container queries and Tailwind responsive utilities instead

interface CashflowChartProps {
  labelsVisible?: boolean;
  height?: string;
  minHeight?: string;
  maxHeight?: string;
}

/**
 * Component for cashflow chart
 * Uses container queries for component-level adaptivity
 */
const CashflowChart = ({
  labelsVisible = true,
  height = "40vh",
  minHeight = "220px",
  maxHeight = "350px",
}: CashflowChartProps) => {
  // Get projection data and the setter for highlightedDate from context
  const {
    projectionData,
    scenarioProjectionData,
    showScenarios,
    setShowScenarios,
    setHighlightedDate,
  } = useProjection();
  
  // Check if mobile
  const isMobile = useIsMobile();

  if (!projectionData) return null;

  // Format data including scenarios if available
  // Use useMemo to prevent unnecessary recalculations
  const chartData = useMemo(() => {
    console.log("Recalculating chart data...");
    return showScenarios && scenarioProjectionData
      ? formatScenarioChartData(scenarioProjectionData)
      : formatChartData(projectionData);
  }, [showScenarios, scenarioProjectionData, projectionData]);

  // Define payload for the recharts legend - memoize to prevent unnecessary re-renders
  const legendPayload = useMemo(
    () => [
      { value: "Balance", type: "square", id: "balance", color: "#4385BE" }, // Flexoki blue-400
      { value: "Money In", type: "square", id: "inflows", color: "#66800B" }, // Flexoki green-600
      { value: "Money Out", type: "square", id: "outflows", color: "#AF3029" }, // Flexoki red-600
      // Add these items when showing scenarios
      ...(showScenarios
        ? [
            {
              value: "Expected Case",
              type: "square",
              id: "expected",
              color: "rgba(67, 133, 190, 0.5)", // Flexoki blue-400 with 50% opacity
            },
            {
              value: "Best Case",
              type: "square",
              id: "best",
              color: "rgba(67, 133, 190, 0.3)", // Flexoki blue-400 with 30% opacity
            },
          ]
        : []),
    ],
    [showScenarios]
  );

  // Handler for chart clicks - now attached to the ComposedChart
  // Memoize to prevent unnecessary re-renders
  const handleChartClick = useCallback(
    (data: ChartClickData) => {
      // Check if the click event provides the active payload
      if (data && data.activePayload && data.activePayload.length > 0) {
        const clickedDate = data.activePayload[0]?.payload?.date;
        // console.log('Setting highlightedDate from chart click:', clickedDate); // Keep commented
        setHighlightedDate(clickedDate || null);
      } else {
        // Clear highlight if clicking outside a data point or data is unexpected
        // console.log('Clearing highlightedDate (click data invalid or outside)'); // Keep commented
        setHighlightedDate(null);
      }
    },
    [setHighlightedDate]
  );

  return (
    <div
      className="chart-container mb-0 overflow-visible relative w-full flex justify-center"
      style={{
        height: isMobile ? "300px" : "46vh", // Fixed height on mobile
        minHeight: isMobile ? "300px" : "290px",
        maxHeight: isMobile ? "300px" : "460px",
        position: "relative", // Ensure proper stacking context
      }}
    >
      <RechartContainer width="100%" height="100%" style={{ maxWidth: "100%" }}>
        <RechartComposed
          data={chartData}
          margin={{
            // Mobile-optimized margins
            top: isMobile ? 15 : 20,
            right: isMobile ? 5 : 10,
            left: isMobile ? 5 : 10,
            bottom: 5,
          }}
          onClick={handleChartClick} // Attach onClick to the chart itself
        >
          {/* Add the built-in Legend */}
          <RechartLegend
            payload={legendPayload}
            verticalAlign="top"
            align="right"
            wrapperStyle={{
              position: "absolute", // Position absolutely relative to parent
              top: "0px", // Align to the top
              right: "10px", // Mobile-first margin
              paddingTop: "3px", // Add some padding from the top edge
              fontSize: "10px", // Smaller text size for mobile
              lineHeight: "14px", // Adjust line height for spacing
            }}
            iconSize={8} // Smaller icon size for mobile
          />

          {/* Grid with mobile-first styling */}
          <RechartGrid
            strokeDasharray="3 3"
            stroke="rgba(206, 205, 195, 0.3)" // Flexoki base-200 with 30% opacity
            horizontal={true}
            vertical={false} // Hide vertical grid on mobile for cleaner look
            horizontalPoints={[0, 25, 50, 75, 100]} // Simplified grid for mobile
          />
          <RechartXAxis
            dataKey="date"
            tick={{
              fontSize: 10, // Mobile-first size
              fill: "currentColor",
              opacity: 0.8,
            }}
            interval="preserveEnd" // Mobile-first setting - fewer ticks
            tickFormatter={(value) => {
              // Mobile-first date formatting - shorter format
              return format(new Date(value), "d MMM");
            }}
            axisLine={{ stroke: "rgba(206, 205, 195, 0.5)" }} // Flexoki base-200
            tickLine={{ stroke: "rgba(206, 205, 195, 0.5)" }} // Flexoki base-200
            height={20} // Reduced from 30 to 20
            dy={3} // Reduced from 5 to 3
          />
          <RechartYAxis
            tickFormatter={(value) => {
              // Mobile-first currency format - more compact
              return new Intl.NumberFormat("en-AU", {
                style: "currency",
                currency: "AUD",
                notation: "compact",
                maximumFractionDigits: 0, // No decimals on mobile
              }).format(value);
            }}
            width={45} // Minimal width to save horizontal space
            tick={{
              fontSize: 10, // Mobile-first font size
              fill: "currentColor",
              opacity: 0.8,
            }}
            axisLine={{ stroke: "rgba(206, 205, 195, 0.5)" }} // Flexoki base-200
            tickLine={{ stroke: "rgba(206, 205, 195, 0.5)" }} // Flexoki base-200
            tickCount={4} // Mobile-first tick count - fewer ticks
          />
          {/* Only show tooltip on desktop - it's broken on mobile */}
          {!isMobile && (
            <RechartTooltip
              content={<ChartTooltip />}
              cursor={{ strokeWidth: 1 }}
              wrapperStyle={{
                outline: "none",
                pointerEvents: "all",
                zIndex: 999999,
                overflow: "visible",
                minWidth: "450px",
                maxWidth: "95%",
                maxHeight: "none",
                display: "block",
                filter: "drop-shadow(0 4px 6px rgba(16, 15, 15, 0.1))", // Flexoki black with 10% opacity
              }}
              allowEscapeViewBox={{ x: false, y: false }}
              position="right"
              isAnimationActive={false}
              viewBox={{ x: 0, y: 0, width: "100%", height: "100%" }}
            />
          )}

          {/* Reference line for zero balance */}
          <RechartReferenceLine
            y={0}
            stroke="rgba(111, 110, 105, 0.5)" // Flexoki base-600 with 50% opacity
            strokeDasharray="3 3"
            strokeOpacity={0.5}
          />

          {/* Area and Line for Balance */}
          <defs>
            <linearGradient id="balanceGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#4385BE" stopOpacity={0.2} /> {/* Flexoki blue-400 */}
              <stop offset="95%" stopColor="#4385BE" stopOpacity={0} /> {/* Flexoki blue-400 */}
            </linearGradient>
          </defs>
          <RechartArea
            type="monotone"
            dataKey="balance"
            name="Balance"
            fill="url(#balanceGradient)"
            stroke="#4385BE" // Flexoki blue-400
            activeDot={{
              r: 6, // Mobile-first size
              strokeWidth: 0,
              fill: "#4385BE", // Flexoki blue-400
            }}
            strokeWidth={2} // Mobile-first width
            dot={false}
            isAnimationActive={false} // Disable animations on mobile for better performance
            // Removed onClick handler from Area
          />

          {/* Add scenario areas when showScenarios is true */}
          {showScenarios && (
            <>
              <defs>
                <linearGradient
                  id="expectedCaseGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#4385BE" stopOpacity={0.4} /> {/* Flexoki blue-400 */}
                  <stop offset="95%" stopColor="#4385BE" stopOpacity={0.1} /> {/* Flexoki blue-400 */}
                </linearGradient>
                <linearGradient
                  id="bestCaseGradient"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#4385BE" stopOpacity={0.2} /> {/* Flexoki blue-400 */}
                  <stop offset="95%" stopColor="#4385BE" stopOpacity={0.05} /> {/* Flexoki blue-400 */}
                </linearGradient>
              </defs>

              {/* Expected case area */}
              <RechartArea
                type="monotone"
                dataKey="expectedCaseBalance"
                name="Expected Case"
                fill="url(#expectedCaseGradient)"
                stroke="rgba(67, 133, 190, 0.5)" // Flexoki blue-400
                strokeWidth={1}
                activeDot={false}
                dot={false}
                isAnimationActive={false} // Disable animations on mobile for better performance
                fillOpacity={0.6}
              />

              {/* Best case area */}
              <RechartArea
                type="monotone"
                dataKey="bestCaseBalance"
                name="Best Case"
                fill="url(#bestCaseGradient)"
                stroke="rgba(67, 133, 190, 0.3)" // Flexoki blue-400
                strokeWidth={1}
                activeDot={false}
                dot={false}
                isAnimationActive={false} // Disable animations on mobile for better performance
                fillOpacity={0.3}
              />
            </>
          )}

          {/* Lines for Money In and Money Out */}
          <RechartLineSeries
            type="monotone"
            dataKey="inflows"
            name="Money In"
            stroke="#66800B" // Flexoki green-600
            strokeWidth={1.5} // Mobile-first width
            strokeDasharray="3 3" // Mobile-first dash pattern
            dot={(props: DotProps) => {
              // Only render dots for significant inflows
              const { cx, cy, payload } = props;
              if (payload?.hasSignificantInflow) {
                return (
                  <g key={`inflow-dot-${payload.date}`}>
                    <circle
                      cx={cx}
                      cy={cy}
                      r={3} // Mobile-first radius
                      fill="#66800B" // Flexoki green-600
                    />
                    {/* Minimal indicator for significant inflow */}
                    {labelsVisible && (
                      <g key={`inflow-label-${payload.date}`}>
                        {/* Small marker line */}
                        <line
                          key={`inflow-line-${payload.date}`}
                          x1={cx}
                          y1={cy}
                          x2={cx}
                          y2={
                            payload?.inflowLabelPosition === 0
                              ? cy - 8
                              : payload?.inflowLabelPosition === 1
                              ? cy - 20
                              : cy - 32
                          }
                          stroke="#66800B" // Flexoki green-600
                          strokeWidth={1}
                          strokeDasharray="1 1"
                        />
                        {/* Transaction name and amount */}
                        <text
                          key={`inflow-text-${payload.date}`}
                          x={cx}
                          y={
                            payload?.inflowLabelPosition === 0
                              ? cy - 10
                              : payload?.inflowLabelPosition === 1
                              ? cy - 22
                              : cy - 34
                          }
                          textAnchor="middle"
                          fill="#66800B" // Flexoki green-600
                          fontSize={8}
                          fontWeight="500"
                        >
                          {`${payload?.significantInflowName?.substring(0, 10)}${
                            (payload?.significantInflowName?.length || 0) > 10
                              ? ".."
                              : ""
                          } $${(
                            Math.abs(payload?.significantInflowAmount || 0) /
                            1000
                          ).toFixed(0)}k`}
                        </text>
                      </g>
                    )}
                  </g>
                );
              }
              return null;
            }}
            activeDot={{
              r: 4, // Mobile-first radius
              strokeWidth: 0,
              fill: "#66800B", // Flexoki green-600
            }}
            isAnimationActive={false} // Disable animations on mobile for better performance
          />
          <RechartLineSeries
            type="monotone"
            dataKey="outflows"
            name="Money Out"
            stroke="#AF3029" // Flexoki red-600
            strokeWidth={1.5} // Mobile-first width
            strokeDasharray="3 3" // Mobile-first dash pattern
            dot={(props: DotProps) => {
              // Only render dots for significant outflows
              const { cx, cy, payload } = props;
              if (payload?.hasSignificantOutflow) {
                return (
                  <g key={`outflow-dot-${payload.date}`}>
                    <circle
                      cx={cx}
                      cy={cy}
                      r={3} // Mobile-first radius
                      fill="#AF3029" // Flexoki red-600
                    />
                    {/* Minimal indicator for significant outflow */}
                    {labelsVisible && (
                      <g key={`outflow-label-${payload.date}`}>
                        {/* Small marker line */}
                        <line
                          key={`outflow-line-${payload.date}`}
                          x1={cx}
                          y1={cy}
                          x2={cx}
                          y2={
                            payload?.outflowLabelPosition === 0
                              ? cy + 8
                              : payload?.outflowLabelPosition === 1
                              ? cy + 20
                              : cy + 32
                          }
                          stroke="#AF3029" // Flexoki red-600
                          strokeWidth={1}
                          strokeDasharray="1 1"
                        />
                        {/* Transaction name and amount */}
                        <text
                          key={`outflow-text-${payload.date}`}
                          x={cx}
                          y={
                            payload?.outflowLabelPosition === 0
                              ? cy + 16
                              : payload?.outflowLabelPosition === 1
                              ? cy + 28
                              : cy + 40
                          }
                          textAnchor="middle"
                          fill="#AF3029" // Flexoki red-600
                          fontSize={8}
                          fontWeight="500"
                        >
                          {`${payload?.significantOutflowName?.substring(0, 8)}${
                            (payload?.significantOutflowName?.length || 0) > 8
                              ? ".."
                              : ""
                          } $${(
                            Math.abs(payload?.significantOutflowAmount || 0) /
                            1000
                          ).toFixed(0)}k`}
                        </text>
                      </g>
                    )}
                  </g>
                );
              }
              return null;
            }}
            activeDot={{
              r: 4, // Mobile-first radius
              strokeWidth: 0,
              fill: "#AF3029", // Flexoki red-600
            }}
            isAnimationActive={false} // Disable animations on mobile for better performance
          />
          {/* Removed Brush component */}
        </RechartComposed>
      </RechartContainer>
    </div>
  );
};

// Create a custom hook to manage label visibility
export const useLabelVisibility = () => {
  const [labelsVisible, setLabelsVisible] = useState(true);

  const toggleLabels = () => {
    setLabelsVisible(!labelsVisible);
  };

  return { labelsVisible, toggleLabels };
};

export default CashflowChart;
