import { Router } from 'express';
import { XeroController } from '../controllers/xero';
import { validateOAuthSession, handleOAuthErrors } from '../middleware/oauth-error-handler';

const router = Router();
const xeroController = new XeroController();

// Apply OAuth session validation to all non-auth routes
router.use(validateOAuthSession);

// Xero authentication routes
router.get('/auth', xeroController.initiateAuth);
router.get('/callback', xeroController.handleCallback);
router.get('/auth-status', xeroController.checkAuthStatus);
router.get('/userinfo', xeroController.getUserInfo);
router.get('/account', xeroController.getAccountInfo);
router.get('/logout', xeroController.logout);

// Xero data routes
router.get('/organization', xeroController.getOrganization);
router.get('/cash-flow-forecast', xeroController.getCashFlowForecast);

// Bills routes
router.get('/bills', xeroController.getBills);
router.post('/bills/convert', xeroController.convertBillToExpense);

// Payroll routes
router.get('/payroll', xeroController.getPayrollExpenses);
router.post('/payroll/convert', xeroController.convertPayrollToExpense);

// Expense breakdown routes
router.get('/expense-breakdown', xeroController.getPayrollExpenseBreakdown);
router.post('/sync-expense', xeroController.syncExpense);

// Superannuation routes
router.get('/superannuation', xeroController.getSuperannuationExpenses);
router.post('/superannuation/convert', xeroController.convertSuperannuationToExpense);

// Activity statements routes
router.get('/activity-statements', xeroController.getActivityStatements);
router.post('/activity-statements/convert', xeroController.convertActivityStatementToExpense);

// GST routes
router.get('/gst', xeroController.getGSTData);
router.post('/gst/convert', xeroController.convertGSTToExpense);

// Reports routes
router.get('/reports/balance-sheet', xeroController.getBalanceSheet);

// Late bills routes
router.get('/late-bills', xeroController.getLateBills);

// Apply OAuth error handler to catch authentication errors
router.use(handleOAuthErrors);

export default router;
