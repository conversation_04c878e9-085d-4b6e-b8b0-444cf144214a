import { XeroBillService } from '../../../../src/services/xero/bill-service';
import { XeroApiClient } from '../../../../src/api/clients/xero-api-client';
import type { XeroBill, XeroContact } from '../../../../src/types/xero';

jest.mock('../../../../src/api/clients/xero-api-client');

describe('XeroBillService', () => {
  let service: XeroBillService;
  let mockApiClient: jest.Mocked<XeroApiClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockApiClient = new XeroApiClient('mock-token') as jest.Mocked<XeroApiClient>;
    service = new XeroBillService(mockApiClient);
  });

  describe('getBills', () => {
    const mockBills: XeroBill[] = [
      {
        InvoiceID: 'bill-1',
        Type: 'ACCPAY',
        InvoiceNumber: 'BILL-001',
        Reference: 'Supplier Ref 123',
        AmountDue: 0,
        AmountPaid: 2000,
        Total: 2000,
        SubTotal: 1800,
        TotalTax: 200,
        Status: 'PAID',
        DueDate: '2024-02-28',
        Date: '2024-02-01',
        UpdatedDateUTC: '/Date(1706745600000)/',
        CurrencyCode: 'USD',
        FullyPaidOnDate: '2024-02-20',
        Contact: {
          ContactID: 'supplier-1',
          Name: 'Supplier A',
          ContactStatus: 'ACTIVE'
        },
        LineAmountTypes: 'Exclusive',
        LineItems: [
          {
            LineItemID: 'line-1',
            Description: 'Office supplies',
            Quantity: 1,
            UnitAmount: 1800,
            TaxType: 'OUTPUT',
            TaxAmount: 200,
            LineAmount: 1800,
            AccountCode: '400'
          }
        ]
      },
      {
        InvoiceID: 'bill-2',
        Type: 'ACCPAY',
        InvoiceNumber: 'BILL-002',
        Reference: '',
        AmountDue: 5000,
        AmountPaid: 0,
        Total: 5000,
        SubTotal: 4500,
        TotalTax: 500,
        Status: 'AUTHORISED',
        DueDate: '2024-03-15',
        Date: '2024-03-01',
        UpdatedDateUTC: '/Date(*************)/',
        CurrencyCode: 'USD',
        Contact: {
          ContactID: 'supplier-2',
          Name: 'Supplier B',
          ContactStatus: 'ACTIVE'
        },
        LineAmountTypes: 'Exclusive',
        LineItems: []
      }
    ];

    it('should fetch all bills', async () => {
      mockApiClient.get.mockResolvedValue({ Invoices: mockBills });

      const result = await service.getBills('tenant-123');

      expect(result).toEqual(mockBills);
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { where: 'Type="ACCPAY"' }
      );
    });

    it('should fetch bills with status filter', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Invoices: [mockBills[1]] 
      });

      const result = await service.getBills('tenant-123', { 
        status: 'AUTHORISED' 
      });

      expect(result).toHaveLength(1);
      expect(result[0].Status).toBe('AUTHORISED');
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { where: 'Type="ACCPAY" AND Status="AUTHORISED"' }
      );
    });

    it('should fetch bills for specific supplier', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Invoices: [mockBills[0]] 
      });

      const result = await service.getBills('tenant-123', { 
        contactId: 'supplier-1' 
      });

      expect(result).toHaveLength(1);
      expect(result[0].Contact.ContactID).toBe('supplier-1');
      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { where: 'Type="ACCPAY" AND Contact.ContactID=Guid("supplier-1")' }
      );
    });

    it('should fetch bills with date filter', async () => {
      mockApiClient.get.mockResolvedValue({ Invoices: mockBills });

      await service.getBills('tenant-123', {
        modifiedAfter: new Date('2024-01-01')
      });

      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { 
          where: 'Type="ACCPAY"',
          'If-Modified-Since': new Date('2024-01-01').toISOString() 
        }
      );
    });

    it('should handle pagination', async () => {
      mockApiClient.get.mockResolvedValue({ 
        Invoices: mockBills,
        pagination: { page: 1, pageSize: 100 }
      });

      await service.getBills('tenant-123', { page: 2 });

      expect(mockApiClient.get).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { 
          where: 'Type="ACCPAY"',
          page: 2 
        }
      );
    });
  });

  describe('getOverdueBills', () => {
    it('should return only overdue bills', async () => {
      const today = new Date();
      const pastDue = new Date(today);
      pastDue.setDate(today.getDate() - 10);
      const futureDue = new Date(today);
      futureDue.setDate(today.getDate() + 10);

      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-001',
          AmountDue: 1000,
          Total: 1000,
          Status: 'AUTHORISED',
          DueDate: pastDue.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: {
            ContactID: 'supplier-1',
            Name: 'Supplier A'
          }
        } as XeroBill,
        {
          InvoiceID: 'bill-2',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-002',
          AmountDue: 2000,
          Total: 2000,
          Status: 'AUTHORISED',
          DueDate: futureDue.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: {
            ContactID: 'supplier-2',
            Name: 'Supplier B'
          }
        } as XeroBill,
        {
          InvoiceID: 'bill-3',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-003',
          AmountDue: 0,
          Total: 3000,
          Status: 'PAID',
          DueDate: pastDue.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: {
            ContactID: 'supplier-3',
            Name: 'Supplier C'
          }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      const result = await service.getOverdueBills('tenant-123');

      expect(result).toHaveLength(1);
      expect(result[0].InvoiceID).toBe('bill-1');
      expect(result[0]).toHaveProperty('daysOverdue');
      expect(result[0].daysOverdue).toBeGreaterThanOrEqual(9);
      expect(result[0].daysOverdue).toBeLessThanOrEqual(11);
    });

    it('should handle no overdue bills', async () => {
      const futureDue = new Date();
      futureDue.setDate(futureDue.getDate() + 10);

      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-001',
          AmountDue: 1000,
          Total: 1000,
          Status: 'AUTHORISED',
          DueDate: futureDue.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: {
            ContactID: 'supplier-1',
            Name: 'Supplier A'
          }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      const result = await service.getOverdueBills('tenant-123');

      expect(result).toEqual([]);
    });
  });

  describe('getBillStatistics', () => {
    it('should calculate bill statistics', async () => {
      const overdueBill = new Date();
      overdueBill.setDate(overdueBill.getDate() - 5);

      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          Total: 5000,
          AmountDue: 0,
          AmountPaid: 5000,
          Status: 'PAID',
          DueDate: '2024-01-01',
          Date: '2024-01-01',
          FullyPaidOnDate: '2024-01-15',
          Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
        } as XeroBill,
        {
          InvoiceID: 'bill-2',
          Type: 'ACCPAY',
          Total: 3000,
          AmountDue: 3000,
          AmountPaid: 0,
          Status: 'AUTHORISED',
          DueDate: '2024-04-01',
          Date: '2024-03-01',
          Contact: { ContactID: 'supplier-2', Name: 'Supplier B' }
        } as XeroBill,
        {
          InvoiceID: 'bill-3',
          Type: 'ACCPAY',
          Total: 2000,
          AmountDue: 2000,
          AmountPaid: 0,
          Status: 'AUTHORISED',
          DueDate: overdueBill.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: { ContactID: 'supplier-3', Name: 'Supplier C' }
        } as XeroBill,
        {
          InvoiceID: 'bill-4',
          Type: 'ACCPAY',
          Total: 1000,
          AmountDue: 500,
          AmountPaid: 500,
          Status: 'AUTHORISED',
          DueDate: '2024-03-15',
          Date: '2024-02-01',
          Contact: { ContactID: 'supplier-4', Name: 'Supplier D' }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      const result = await service.getBillStatistics('tenant-123');

      expect(result).toEqual({
        totalBills: 4,
        totalAmount: 11000,
        totalPaid: 5500,
        totalOutstanding: 5500,
        overdueAmount: 2000,
        overdueCount: 1,
        averagePaymentTime: expect.any(Number)
      });
    });

    it('should handle empty bill list', async () => {
      mockApiClient.get.mockResolvedValue({ Invoices: [] });

      const result = await service.getBillStatistics('tenant-123');

      expect(result).toEqual({
        totalBills: 0,
        totalAmount: 0,
        totalPaid: 0,
        totalOutstanding: 0,
        overdueAmount: 0,
        overdueCount: 0,
        averagePaymentTime: 0
      });
    });
  });

  describe('getUpcomingBills', () => {
    it('should return bills due within specified days', async () => {
      const today = new Date();
      const in5Days = new Date(today);
      in5Days.setDate(today.getDate() + 5);
      const in15Days = new Date(today);
      in15Days.setDate(today.getDate() + 15);
      const in35Days = new Date(today);
      in35Days.setDate(today.getDate() + 35);

      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-001',
          AmountDue: 1000,
          Total: 1000,
          Status: 'AUTHORISED',
          DueDate: in5Days.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
        } as XeroBill,
        {
          InvoiceID: 'bill-2',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-002',
          AmountDue: 2000,
          Total: 2000,
          Status: 'AUTHORISED',
          DueDate: in15Days.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: { ContactID: 'supplier-2', Name: 'Supplier B' }
        } as XeroBill,
        {
          InvoiceID: 'bill-3',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-003',
          AmountDue: 3000,
          Total: 3000,
          Status: 'AUTHORISED',
          DueDate: in35Days.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: { ContactID: 'supplier-3', Name: 'Supplier C' }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      // Default 30 days
      const result = await service.getUpcomingBills('tenant-123');

      expect(result).toHaveLength(2);
      expect(result.map(b => b.InvoiceID)).toEqual(['bill-1', 'bill-2']);

      // Custom 10 days
      const result10Days = await service.getUpcomingBills('tenant-123', 10);

      expect(result10Days).toHaveLength(1);
      expect(result10Days[0].InvoiceID).toBe('bill-1');
    });

    it('should exclude paid bills', async () => {
      const in5Days = new Date();
      in5Days.setDate(in5Days.getDate() + 5);

      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          InvoiceNumber: 'BILL-001',
          AmountDue: 0,
          AmountPaid: 1000,
          Total: 1000,
          Status: 'PAID',
          DueDate: in5Days.toISOString().split('T')[0],
          Date: '2024-01-01',
          Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      const result = await service.getUpcomingBills('tenant-123');

      expect(result).toEqual([]);
    });
  });

  describe('getBillsBySupplier', () => {
    it('should group bills by supplier', async () => {
      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          Total: 1000,
          Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
        } as XeroBill,
        {
          InvoiceID: 'bill-2',
          Type: 'ACCPAY',
          Total: 2000,
          Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
        } as XeroBill,
        {
          InvoiceID: 'bill-3',
          Type: 'ACCPAY',
          Total: 3000,
          Contact: { ContactID: 'supplier-2', Name: 'Supplier B' }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      const result = await service.getBillsBySupplier('tenant-123');

      expect(result).toEqual({
        'supplier-1': {
          supplierName: 'Supplier A',
          bills: [bills[0], bills[1]],
          totalAmount: 3000,
          totalOutstanding: 0
        },
        'supplier-2': {
          supplierName: 'Supplier B',
          bills: [bills[2]],
          totalAmount: 3000,
          totalOutstanding: 0
        }
      });
    });

    it('should calculate outstanding amounts', async () => {
      const bills: XeroBill[] = [
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          Total: 1000,
          AmountDue: 500,
          AmountPaid: 500,
          Contact: { ContactID: 'supplier-1', Name: 'Supplier A' }
        } as XeroBill
      ];

      mockApiClient.get.mockResolvedValue({ Invoices: bills });

      const result = await service.getBillsBySupplier('tenant-123');

      expect(result['supplier-1'].totalOutstanding).toBe(500);
    });
  });

  describe('createBill', () => {
    it('should create a new bill', async () => {
      const newBill = {
        Type: 'ACCPAY' as const,
        Contact: { ContactID: 'supplier-1' },
        Date: '2024-03-01',
        DueDate: '2024-03-31',
        LineAmountTypes: 'Exclusive' as const,
        LineItems: [
          {
            Description: 'Consulting services',
            Quantity: 1,
            UnitAmount: 5000,
            AccountCode: '400',
            TaxType: 'OUTPUT'
          }
        ]
      };

      const createdBill = {
        ...newBill,
        InvoiceID: 'bill-new',
        InvoiceNumber: 'BILL-003',
        Total: 5500,
        SubTotal: 5000,
        TotalTax: 500,
        Status: 'DRAFT',
        AmountDue: 5500,
        AmountPaid: 0
      };

      mockApiClient.put.mockResolvedValue({ Invoices: [createdBill] });

      const result = await service.createBill('tenant-123', newBill);

      expect(result).toEqual(createdBill);
      expect(mockApiClient.put).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices',
        { Invoices: [newBill] }
      );
    });
  });

  describe('updateBill', () => {
    it('should update an existing bill', async () => {
      const updates = {
        DueDate: '2024-04-15',
        Reference: 'Updated reference'
      };

      const updatedBill = {
        InvoiceID: 'bill-1',
        Type: 'ACCPAY',
        InvoiceNumber: 'BILL-001',
        DueDate: '2024-04-15',
        Reference: 'Updated reference',
        Total: 1000
      };

      mockApiClient.post.mockResolvedValue({ Invoices: [updatedBill] });

      const result = await service.updateBill('tenant-123', 'bill-1', updates);

      expect(result).toEqual(updatedBill);
      expect(mockApiClient.post).toHaveBeenCalledWith(
        'tenant-123',
        '/api.xro/2.0/Invoices/bill-1',
        { ...updates, InvoiceID: 'bill-1' }
      );
    });
  });

  describe('error handling', () => {
    it('should handle API errors gracefully', async () => {
      mockApiClient.get.mockRejectedValue(new Error('API error'));

      await expect(service.getBills('tenant-123'))
        .rejects.toThrow('API error');
    });

    it('should handle rate limiting', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).statusCode = 429;

      mockApiClient.get.mockRejectedValue(rateLimitError);

      await expect(service.getBills('tenant-123'))
        .rejects.toThrow('Rate limit exceeded');
    });
  });
});