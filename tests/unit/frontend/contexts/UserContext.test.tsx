import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { UserContext, UserProvider } from '../../../../src/frontend/contexts/UserContext';
import * as api from '../../../../src/frontend/api/utils';

jest.mock('../../../../src/frontend/api/utils');

describe('UserContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should provide user context to children', () => {
    const TestComponent = () => {
      const context = React.useContext(UserContext);
      return (
        <div>
          <span>Loading: {context.loading.toString()}</span>
          <span>User: {context.user?.name || 'None'}</span>
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    expect(screen.getByText('Loading: true')).toBeInTheDocument();
    expect(screen.getByText('User: None')).toBeInTheDocument();
  });

  it('should fetch user on mount', async () => {
    const mockUser = { id: '1', name: 'Test User', email: '<EMAIL>' };
    (api.fetchCurrentUser as jest.Mock).mockResolvedValue(mockUser);

    const TestComponent = () => {
      const { user, loading } = React.useContext(UserContext);
      return (
        <div>
          {loading && <span>Loading...</span>}
          {user && <span>Welcome {user.name}</span>}
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Welcome Test User')).toBeInTheDocument();
    });

    expect(api.fetchCurrentUser).toHaveBeenCalledTimes(1);
  });

  it('should handle fetch user error', async () => {
    (api.fetchCurrentUser as jest.Mock).mockRejectedValue(new Error('Auth failed'));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    const TestComponent = () => {
      const { user, loading } = React.useContext(UserContext);
      return (
        <div>
          <span>Loading: {loading.toString()}</span>
          <span>User: {user ? 'Loaded' : 'Not loaded'}</span>
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Loading: false')).toBeInTheDocument();
      expect(screen.getByText('User: Not loaded')).toBeInTheDocument();
    });

    expect(consoleSpy).toHaveBeenCalledWith('Failed to fetch user:', expect.any(Error));
    consoleSpy.mockRestore();
  });

  it('should update user with setUser', async () => {
    const TestComponent = () => {
      const { user, setUser } = React.useContext(UserContext);
      
      const updateUser = () => {
        setUser({ id: '2', name: 'Updated User', email: '<EMAIL>' });
      };

      return (
        <div>
          <span>User: {user?.name || 'None'}</span>
          <button onClick={updateUser}>Update User</button>
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('User: None')).toBeInTheDocument();
    });

    act(() => {
      screen.getByText('Update User').click();
    });

    expect(screen.getByText('User: Updated User')).toBeInTheDocument();
  });

  it('should provide user permissions', async () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      permissions: ['view_estimates', 'edit_estimates']
    };
    (api.fetchCurrentUser as jest.Mock).mockResolvedValue(mockUser);

    const TestComponent = () => {
      const { user } = React.useContext(UserContext);
      const hasPermission = user?.permissions?.includes('edit_estimates');
      
      return (
        <div>
          <span>Can edit: {hasPermission ? 'Yes' : 'No'}</span>
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Can edit: Yes')).toBeInTheDocument();
    });
  });

  it('should handle null user gracefully', () => {
    (api.fetchCurrentUser as jest.Mock).mockResolvedValue(null);

    const TestComponent = () => {
      const { user, loading } = React.useContext(UserContext);
      
      return (
        <div>
          <span>Authenticated: {user ? 'Yes' : 'No'}</span>
          <span>Loading: {loading.toString()}</span>
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    waitFor(() => {
      expect(screen.getByText('Authenticated: No')).toBeInTheDocument();
      expect(screen.getByText('Loading: false')).toBeInTheDocument();
    });
  });

  it('should only fetch user once on mount', async () => {
    const mockUser = { id: '1', name: 'Test User' };
    (api.fetchCurrentUser as jest.Mock).mockResolvedValue(mockUser);

    const TestComponent = () => {
      const { user } = React.useContext(UserContext);
      const [count, setCount] = React.useState(0);
      
      return (
        <div>
          <span>User: {user?.name || 'None'}</span>
          <span>Count: {count}</span>
          <button onClick={() => setCount(count + 1)}>Increment</button>
        </div>
      );
    };

    const { rerender } = render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('User: Test User')).toBeInTheDocument();
    });

    // Trigger re-render
    act(() => {
      screen.getByText('Increment').click();
    });

    expect(screen.getByText('Count: 1')).toBeInTheDocument();
    expect(api.fetchCurrentUser).toHaveBeenCalledTimes(1);
  });

  it('should clear user on logout', async () => {
    const mockUser = { id: '1', name: 'Test User' };
    (api.fetchCurrentUser as jest.Mock).mockResolvedValue(mockUser);

    const TestComponent = () => {
      const { user, setUser } = React.useContext(UserContext);
      
      const logout = () => {
        setUser(null);
      };

      return (
        <div>
          <span>User: {user?.name || 'None'}</span>
          <button onClick={logout}>Logout</button>
        </div>
      );
    };

    render(
      <UserProvider>
        <TestComponent />
      </UserProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('User: Test User')).toBeInTheDocument();
    });

    act(() => {
      screen.getByText('Logout').click();
    });

    expect(screen.getByText('User: None')).toBeInTheDocument();
  });

  it('should handle context outside provider', () => {
    const TestComponent = () => {
      const context = React.useContext(UserContext);
      return <div>Context exists: {context ? 'Yes' : 'No'}</div>;
    };

    // Render without provider
    render(<TestComponent />);

    expect(screen.getByText('Context exists: No')).toBeInTheDocument();
  });
});