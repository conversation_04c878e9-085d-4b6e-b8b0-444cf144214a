import React from 'react';

interface SectionWrapperProps {
  id: string;
  title: string;
  isExpanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  sectionRef?: (el: HTMLDivElement | null) => void;
}

/**
 * Reusable wrapper component for help page sections
 */
export const SectionWrapper: React.FC<SectionWrapperProps> = ({
  id,
  title,
  isExpanded,
  onToggle,
  children,
  sectionRef
}) => {
  return (
    <div
      className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden"
      ref={sectionRef}
      id={`${id}-section`}
    >
      <button
        className="w-full flex justify-between items-center px-4 py-3 sm:px-6 text-left"
        onClick={onToggle}
      >
        <h2 className="text-lg font-medium text-primary">
          {title}
        </h2>
        <span className="ml-6 flex-shrink-0">
          <svg
            className={`h-5 w-5 transform transition-transform duration-200 ${
              isExpanded ? "rotate-180" : "rotate-0"
            }`}
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </span>
      </button>
      {isExpanded && (
        <div className="px-4 py-5 sm:px-6 border-t border-gray-200 dark:border-gray-700">
          <div className="prose dark:pred-invert max-w-none">
            {children}
          </div>
        </div>
      )}
    </div>
  );
};