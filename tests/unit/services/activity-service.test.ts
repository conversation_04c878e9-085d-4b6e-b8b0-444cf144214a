import { ActivityService } from '../../../src/api/services/activity-service';
import { ActivityRepository } from '../../../src/api/repositories/activity-repository';
import { EventEmitter } from 'events';

// Mock the repository
jest.mock('../../../src/api/repositories/activity-repository');

// Mock EventEmitter
jest.mock('events');

describe('ActivityService', () => {
  let service: ActivityService;
  let mockRepository: jest.Mocked<ActivityRepository>;
  let mockEventEmitter: jest.Mocked<EventEmitter>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockRepository = new ActivityRepository() as jest.Mocked<ActivityRepository>;
    mockEventEmitter = new EventEmitter() as jest.Mocked<EventEmitter>;
    service = new ActivityService();
    (service as any).repository = mockRepository;
    (service as any).eventEmitter = mockEventEmitter;
  });

  describe('createActivity', () => {
    it('should create an activity and emit event', async () => {
      const activityData = {
        actor_type: 'user' as const,
        actor_id: 'user123',
        actor_name: 'John Doe',
        action: 'created',
        target_type: 'deal' as const,
        target_id: 'deal456',
        target_name: 'Big Deal',
        metadata: { value: 50000 }
      };

      const createdActivity = {
        id: 1,
        ...activityData,
        created_at: '2024-01-01T00:00:00Z'
      };

      mockRepository.create.mockResolvedValue(createdActivity);

      const result = await service.createActivity(activityData);

      expect(mockRepository.create).toHaveBeenCalledWith(activityData);
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('activity:created', createdActivity);
      expect(result).toEqual(createdActivity);
    });

    it('should handle repository errors', async () => {
      const activityData = {
        actor_type: 'user' as const,
        actor_id: 'user123',
        actor_name: 'John Doe',
        action: 'created',
        target_type: 'deal' as const,
        target_id: 'deal456',
        target_name: 'Big Deal'
      };

      mockRepository.create.mockRejectedValue(new Error('Database error'));

      await expect(service.createActivity(activityData)).rejects.toThrow('Database error');
      expect(mockEventEmitter.emit).not.toHaveBeenCalled();
    });
  });

  describe('getActivities', () => {
    it('should fetch activities with filters', async () => {
      const filters = {
        actor_type: 'user' as const,
        target_type: 'deal' as const,
        limit: 20,
        offset: 0
      };

      const mockActivities = [
        {
          id: 1,
          actor_type: 'user',
          actor_id: 'user123',
          actor_name: 'John Doe',
          action: 'created',
          target_type: 'deal',
          target_id: 'deal456',
          target_name: 'Big Deal',
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      mockRepository.findAll.mockResolvedValue(mockActivities);

      const result = await service.getActivities(filters);

      expect(mockRepository.findAll).toHaveBeenCalledWith(filters);
      expect(result).toEqual(mockActivities);
    });

    it('should handle date range filters', async () => {
      const filters = {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
        limit: 50
      };

      mockRepository.findAll.mockResolvedValue([]);

      await service.getActivities(filters);

      expect(mockRepository.findAll).toHaveBeenCalledWith(filters);
    });
  });

  describe('getActivityStats', () => {
    it('should calculate activity statistics', async () => {
      const mockActivities = [
        {
          id: 1,
          actor_type: 'user',
          actor_id: 'user123',
          action: 'created',
          target_type: 'deal',
          created_at: '2024-01-01T10:00:00Z'
        },
        {
          id: 2,
          actor_type: 'user',
          actor_id: 'user123',
          action: 'updated',
          target_type: 'company',
          created_at: '2024-01-01T11:00:00Z'
        },
        {
          id: 3,
          actor_type: 'system',
          actor_id: 'system',
          action: 'synced',
          target_type: 'invoice',
          created_at: '2024-01-01T12:00:00Z'
        }
      ];

      mockRepository.findAll.mockResolvedValue(mockActivities);
      mockRepository.getStats.mockResolvedValue({
        total_activities: 100,
        activities_today: 10,
        activities_this_week: 50,
        most_active_user: 'user123',
        most_common_action: 'updated'
      });

      const result = await service.getActivityStats('2024-01-01', '2024-01-31');

      expect(mockRepository.getStats).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
      expect(result).toHaveProperty('total_activities', 100);
    });
  });

  describe('trackDealActivity', () => {
    it('should track deal creation', async () => {
      const dealData = {
        id: 'deal123',
        name: 'New Deal',
        value: 10000,
        stage: 'qualification'
      };

      mockRepository.create.mockResolvedValue({
        id: 1,
        actor_type: 'user',
        actor_id: 'current-user',
        actor_name: 'Current User',
        action: 'created',
        target_type: 'deal',
        target_id: 'deal123',
        target_name: 'New Deal',
        metadata: { value: 10000, stage: 'qualification' },
        created_at: '2024-01-01T00:00:00Z'
      });

      await service.trackDealActivity('created', dealData, 'current-user', 'Current User');

      expect(mockRepository.create).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'current-user',
        actor_name: 'Current User',
        action: 'created',
        target_type: 'deal',
        target_id: 'deal123',
        target_name: 'New Deal',
        metadata: { value: 10000, stage: 'qualification' }
      });
    });

    it('should track deal update with changes', async () => {
      const dealData = {
        id: 'deal123',
        name: 'Updated Deal',
        value: 15000,
        stage: 'proposal'
      };

      const changes = {
        value: { from: 10000, to: 15000 },
        stage: { from: 'qualification', to: 'proposal' }
      };

      mockRepository.create.mockResolvedValue({
        id: 1,
        actor_type: 'user',
        actor_id: 'current-user',
        actor_name: 'Current User',
        action: 'updated',
        target_type: 'deal',
        target_id: 'deal123',
        target_name: 'Updated Deal',
        metadata: { 
          value: 15000, 
          stage: 'proposal',
          changes 
        },
        created_at: '2024-01-01T00:00:00Z'
      });

      await service.trackDealActivity('updated', dealData, 'current-user', 'Current User', changes);

      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'updated',
          metadata: expect.objectContaining({ changes })
        })
      );
    });
  });

  describe('trackEstimateActivity', () => {
    it('should track estimate publication', async () => {
      const estimateData = {
        estimate_number: 'EST-001',
        project_name: 'Test Project',
        total_amount: 25000
      };

      mockRepository.create.mockResolvedValue({
        id: 1,
        actor_type: 'user',
        actor_id: 'user123',
        actor_name: 'John Doe',
        action: 'published',
        target_type: 'estimate',
        target_id: 'EST-001',
        target_name: 'Test Project',
        metadata: { total_amount: 25000 },
        created_at: '2024-01-01T00:00:00Z'
      });

      await service.trackEstimateActivity('published', estimateData, 'user123', 'John Doe');

      expect(mockRepository.create).toHaveBeenCalledWith({
        actor_type: 'user',
        actor_id: 'user123',
        actor_name: 'John Doe',
        action: 'published',
        target_type: 'estimate',
        target_id: 'EST-001',
        target_name: 'Test Project',
        metadata: { total_amount: 25000 }
      });
    });
  });

  describe('trackSystemActivity', () => {
    it('should track system synchronization', async () => {
      const syncData = {
        service: 'xero',
        records_synced: 45,
        duration_ms: 3500
      };

      mockRepository.create.mockResolvedValue({
        id: 1,
        actor_type: 'system',
        actor_id: 'xero-sync',
        actor_name: 'Xero Sync',
        action: 'synced',
        target_type: 'integration',
        target_id: 'xero',
        target_name: 'Xero Integration',
        metadata: syncData,
        created_at: '2024-01-01T00:00:00Z'
      });

      await service.trackSystemActivity('synced', 'xero', syncData);

      expect(mockRepository.create).toHaveBeenCalledWith({
        actor_type: 'system',
        actor_id: 'xero-sync',
        actor_name: 'Xero Sync',
        action: 'synced',
        target_type: 'integration',
        target_id: 'xero',
        target_name: 'Xero Integration',
        metadata: syncData
      });
    });
  });

  describe('getRecentActivitiesByActor', () => {
    it('should fetch recent activities for a specific actor', async () => {
      const mockActivities = [
        {
          id: 1,
          actor_type: 'user',
          actor_id: 'user123',
          actor_name: 'John Doe',
          action: 'created',
          target_type: 'deal',
          target_id: 'deal456',
          target_name: 'Big Deal',
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      mockRepository.findByActor.mockResolvedValue(mockActivities);

      const result = await service.getRecentActivitiesByActor('user', 'user123', 10);

      expect(mockRepository.findByActor).toHaveBeenCalledWith('user', 'user123', 10);
      expect(result).toEqual(mockActivities);
    });
  });

  describe('getActivitiesByTarget', () => {
    it('should fetch activities for a specific target', async () => {
      const mockActivities = [
        {
          id: 1,
          actor_type: 'user',
          actor_id: 'user123',
          actor_name: 'John Doe',
          action: 'updated',
          target_type: 'deal',
          target_id: 'deal456',
          target_name: 'Big Deal',
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          actor_type: 'user',
          actor_id: 'user456',
          actor_name: 'Jane Smith',
          action: 'commented',
          target_type: 'deal',
          target_id: 'deal456',
          target_name: 'Big Deal',
          created_at: '2024-01-02T00:00:00Z'
        }
      ];

      mockRepository.findByTarget.mockResolvedValue(mockActivities);

      const result = await service.getActivitiesByTarget('deal', 'deal456');

      expect(mockRepository.findByTarget).toHaveBeenCalledWith('deal', 'deal456');
      expect(result).toHaveLength(2);
    });
  });

  describe('cleanupOldActivities', () => {
    it('should delete old activities', async () => {
      mockRepository.deleteOlderThan.mockResolvedValue(50);

      const result = await service.cleanupOldActivities(90);

      expect(mockRepository.deleteOlderThan).toHaveBeenCalledWith(90);
      expect(result).toBe(50);
    });
  });

  describe('subscribeToActivities', () => {
    it('should subscribe to activity events', () => {
      const callback = jest.fn();

      service.subscribeToActivities(callback);

      expect(mockEventEmitter.on).toHaveBeenCalledWith('activity:created', callback);
    });
  });

  describe('unsubscribeFromActivities', () => {
    it('should unsubscribe from activity events', () => {
      const callback = jest.fn();

      service.unsubscribeFromActivities(callback);

      expect(mockEventEmitter.off).toHaveBeenCalledWith('activity:created', callback);
    });
  });
});