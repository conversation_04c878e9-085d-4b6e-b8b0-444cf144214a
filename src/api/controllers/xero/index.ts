import { XeroAuthController } from './auth';
import { XeroReportsController } from './reports';
import { XeroBillsController } from './bills';
import { XeroPayrollController } from './payroll';
import { XeroActivityController } from './activity';

/**
 * Unified Xero Controller that combines all specialized controllers
 * Maintains backward compatibility with the original monolithic XeroController
 */
export class XeroController {
  // Controller instances
  private authController = new XeroAuthController();
  private reportsController = new XeroReportsController();
  private billsController = new XeroBillsController();
  private payrollController = new XeroPayrollController();
  private activityController = new XeroActivityController();

  // ========== AUTH METHODS ==========
  public initiateAuth = this.authController.initiateAuth;
  public handleCallback = this.authController.handleCallback;
  public checkAuthStatus = this.authController.checkAuthStatus;
  public getUserInfo = this.authController.getUserInfo;
  public logout = this.authController.logout;
  public getAccountInfo = this.authController.getAccountInfo;
  public getOrganization = this.authController.getOrganization;

  // ========== REPORTS METHODS ==========
  public getCashFlowForecast = this.reportsController.getCashFlowForecast;
  public getGSTData = this.reportsController.getGSTData;
  public getBalanceSheet = this.reportsController.getBalanceSheet;

  // ========== BILLS METHODS ==========
  public getBills = this.billsController.getBills;
  public convertBillToExpense = this.billsController.convertBillToExpense;
  public getLateBills = this.billsController.getLateBills;

  // ========== PAYROLL METHODS ==========
  public getPayrollExpenses = this.payrollController.getPayrollExpenses;
  public getPayrollExpenseBreakdown = this.payrollController.getPayrollExpenseBreakdown;
  public convertPayrollToExpense = this.payrollController.convertPayrollToExpense;
  public syncExpense = this.payrollController.syncExpense;
  public getSuperannuationExpenses = this.payrollController.getSuperannuationExpenses;
  public convertSuperannuationToExpense = this.payrollController.convertSuperannuationToExpense;

  // ========== ACTIVITY/TAX METHODS ==========
  public getActivityStatements = this.activityController.getActivityStatements;
  public convertActivityStatementToExpense = this.activityController.convertActivityStatementToExpense;
  public convertGSTToExpense = this.activityController.convertGSTToExpense;
}

// Export individual controllers for direct use if needed
export { XeroAuthController } from './auth';
export { XeroReportsController } from './reports';
export { XeroBillsController } from './bills';
export { XeroPayrollController } from './payroll';
export { XeroActivityController } from './activity';

// Export the unified controller as default for backward compatibility
export default XeroController;