/**
 * Estimate Drafts Repository Test
 * 
 * Tests for the estimate drafts repository focusing on business behavior of draft management.
 * Uses an in-memory SQLite database for realistic testing without mocks.
 */
import { EstimateDraftsRepository } from '../../../../src/api/repositories/estimate-drafts-repository';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

describe('EstimateDraftsRepository - Business Behavior', () => {
  let repository: EstimateDraftsRepository;
  let db: Database.Database;
  
  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Initialize schema using the unified migration
    const migrationPath = path.join(__dirname, '../../../../migrations/000_unified_schema.sql');
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    } else {
      throw new Error(`Migration file not found at ${migrationPath}`);
    }
    
    // Ensure estimate_drafts table exists
    db.exec(`
      CREATE TABLE IF NOT EXISTS estimate_drafts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        data TEXT NOT NULL,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);
  });
  
  beforeEach(() => {
    repository = new EstimateDraftsRepository();
    
    // Clear data before each test
    db.prepare('DELETE FROM estimate_drafts').run();
  });
  
  afterAll(() => {
    db.close();
  });

  describe('Business Rule: Draft Management', () => {
    it('should save work-in-progress estimates as drafts', async () => {
      // Create test drafts
      const draft1 = await repository.create({
        name: 'Website Redesign - Draft',
        data: { 
          estimate_number: 'EST001',
          project_name: 'Website Redesign',
          client_name: 'Acme Corp',
          total_amount: 25000,
          status: 'draft'
        }
      });
      
      const draft2 = await repository.create({
        name: 'Mobile App Development - Draft',
        data: { 
          estimate_number: 'EST002',
          project_name: 'Mobile App',
          client_name: 'TechCo',
          total_amount: 75000,
          status: 'draft'
        }
      });

      const allDrafts = await repository.findAll();

      // Business expectation: All unsent estimates are accessible
      expect(allDrafts).toHaveLength(2);
      // Most recently updated first
      expect(allDrafts[0].name).toBe('Mobile App Development - Draft');
      expect(allDrafts[1].name).toBe('Website Redesign - Draft');
    });

    it('should handle empty draft list gracefully', async () => {
      const result = await repository.findAll();

      // Business expectation: New users see empty state
      expect(result).toEqual([]);
    });
  });

  describe('Business Rule: Draft Retrieval', () => {
    it('should retrieve specific draft for editing', async () => {
      const created = await repository.create({
        name: 'Consulting Services - Draft',
        data: {
          estimate_number: 'EST001',
          project_name: 'Consulting Services',
          scope: 'Full project management and technical consulting',
          duration_weeks: 12,
          resources: [
            { name: 'Senior Consultant', rate: 1500, hours: 480 },
            { name: 'Technical Lead', rate: 1200, hours: 320 }
          ]
        }
      });

      const retrieved = await repository.findById(created.id);

      // Business expectation: Can resume work on saved drafts
      expect(retrieved).toBeDefined();
      expect(retrieved?.name).toBe('Consulting Services - Draft');
      expect(retrieved?.data.resources).toHaveLength(2);
      expect(retrieved?.data.duration_weeks).toBe(12);
    });

    it('should handle non-existent draft gracefully', async () => {
      const result = await repository.findById(99999);

      // Business expectation: Clear indication when draft doesn't exist
      expect(result).toBeNull();
    });
  });

  describe('Business Rule: Draft Creation', () => {
    it('should save complex estimate configurations as draft', async () => {
      const complexDraft = {
        name: 'Enterprise Solution - Work in Progress',
        data: {
          estimate_number: 'EST-2024-001',
          client: {
            name: 'Fortune 500 Co',
            contact: 'John Smith',
            email: '<EMAIL>'
          },
          project: {
            name: 'Digital Transformation',
            phases: [
              { name: 'Discovery', weeks: 4, cost: 50000 },
              { name: 'Implementation', weeks: 16, cost: 200000 },
              { name: 'Training & Support', weeks: 4, cost: 30000 }
            ],
            total_cost: 280000
          },
          team: [
            { role: 'Project Manager', rate: 1500, allocation: 0.5 },
            { role: 'Tech Lead', rate: 1800, allocation: 1.0 },
            { role: 'Developer', rate: 1200, allocation: 2.0 }
          ],
          terms: {
            payment_schedule: 'monthly',
            discount: 5,
            valid_until: '2024-02-01'
          }
        }
      };

      const result = await repository.create(complexDraft);

      // Business expectation: Complex estimates are saved without data loss
      expect(result.id).toBeDefined();
      expect(result.name).toBe('Enterprise Solution - Work in Progress');
      expect(result.data.project.phases).toHaveLength(3);
      expect(result.data.project.total_cost).toBe(280000);
      expect(result.data.team).toHaveLength(3);
      expect(result.created_at).toBeDefined();
    });

    it('should prevent creation of drafts with circular references', async () => {
      const circularDraft = {
        name: 'Circular Reference Draft',
        data: { parent: {} } as any
      };
      // Create circular reference
      circularDraft.data.parent.child = circularDraft.data;

      // Business expectation: Prevent data corruption
      await expect(repository.create(circularDraft)).rejects.toThrow();
    });
  });

  describe('Business Rule: Draft Updates', () => {
    it('should update draft as user refines estimate', async () => {
      // Create initial draft
      const initial = await repository.create({
        name: 'Initial Estimate',
        data: {
          estimate_number: 'EST001',
          total_amount: 50000,
          status: 'draft',
          items: [
            { description: 'Phase 1', amount: 25000 }
          ]
        }
      });

      // User adds more details
      const updated = await repository.update(initial.id, {
        name: 'Detailed Estimate - Ready for Review',
        data: {
          estimate_number: 'EST001',
          total_amount: 75000,
          status: 'draft',
          items: [
            { description: 'Phase 1 - Discovery', amount: 25000 },
            { description: 'Phase 2 - Implementation', amount: 40000 },
            { description: 'Phase 3 - Support', amount: 10000 }
          ],
          notes: 'Added implementation phase after client meeting'
        }
      });

      // Business expectation: Incremental improvements are saved
      expect(updated.name).toBe('Detailed Estimate - Ready for Review');
      expect(updated.data.total_amount).toBe(75000);
      expect(updated.data.items).toHaveLength(3);
      expect(updated.data.notes).toBeDefined();
    });

    it('should allow renaming draft without changing content', async () => {
      const draft = await repository.create({
        name: 'Unclear Name',
        data: { estimate_number: 'EST002', complex_data: 'preserved' }
      });

      const renamed = await repository.update(draft.id, {
        name: 'ACME Corp - Website Redesign v2'
      });

      // Business expectation: Quick renames don't lose work
      expect(renamed.name).toBe('ACME Corp - Website Redesign v2');
      expect(renamed.data.complex_data).toBe('preserved');
    });

    it('should fail gracefully when updating non-existent draft', async () => {
      // Business expectation: Clear error for invalid operations
      await expect(repository.update(99999, { name: 'Test' }))
        .rejects.toThrow('not found');
    });
  });

  describe('Business Rule: Draft Deletion', () => {
    it('should delete draft when user discards work', async () => {
      const draft = await repository.create({
        name: 'Abandoned Estimate',
        data: { estimate_number: 'EST-DEL-001' }
      });

      const result = await repository.delete(draft.id);

      // Business expectation: Clean up unwanted drafts
      expect(result).toBe(true);
      
      // Verify it's actually gone
      const retrieved = await repository.findById(draft.id);
      expect(retrieved).toBeNull();
    });

    it('should handle deletion of non-existent draft', async () => {
      const result = await repository.delete(99999);

      // Business expectation: No error for already deleted drafts
      expect(result).toBe(false);
    });
  });

  describe('Business Rule: Draft Search', () => {
    it('should find drafts by client or project name', async () => {
      // Create drafts for different clients
      await repository.create({
        name: 'Acme Corp - Infrastructure Project',
        data: { client: 'Acme Corp', project: 'Infrastructure' }
      });
      
      await repository.create({
        name: 'TechCo - Mobile App',
        data: { client: 'TechCo', project: 'Mobile App' }
      });
      
      await repository.create({
        name: 'Acme Corp - Security Audit',
        data: { client: 'Acme Corp', project: 'Security Audit' }
      });

      const acmeDrafts = await repository.findByName('Acme');

      // Business expectation: Find all drafts for a specific client
      expect(acmeDrafts).toHaveLength(2);
      expect(acmeDrafts.every(d => d.name.includes('Acme Corp'))).toBe(true);
    });
  });

  describe('Business Rule: Recent Draft Access', () => {
    it('should show most recently worked on drafts first', async () => {
      // Create drafts with different update times
      const draft1 = await repository.create({
        name: 'Old Draft',
        data: { created: 'yesterday' }
      });
      
      // Small delay to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const draft2 = await repository.create({
        name: 'Recent Draft',
        data: { created: 'today' }
      });
      
      // Update the first draft to make it most recent
      await new Promise(resolve => setTimeout(resolve, 10));
      await repository.update(draft1.id, {
        name: 'Old Draft - Updated'
      });

      const recent = await repository.getRecentDrafts(2);

      // Business expectation: See what you worked on last
      expect(recent).toHaveLength(2);
      expect(recent[0].name).toBe('Old Draft - Updated');
      expect(recent[1].name).toBe('Recent Draft');
    });
  });

  describe('Business Rule: Draft Cleanup', () => {
    it('should clean up abandoned drafts after retention period', async () => {
      // Create old draft (simulate by direct SQL)
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 40);
      
      db.prepare(`
        INSERT INTO estimate_drafts (name, data, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `).run(
        'Very Old Draft',
        JSON.stringify({ estimate_number: 'OLD-001' }),
        oldDate.toISOString(),
        oldDate.toISOString()
      );
      
      // Create recent draft
      await repository.create({
        name: 'Recent Draft',
        data: { estimate_number: 'NEW-001' }
      });
      
      // Business requirement: Auto-cleanup old drafts to save space
      const deletedCount = await repository.deleteOldDrafts(30);
      
      expect(deletedCount).toBe(1);
      
      // Verify only recent draft remains
      const remaining = await repository.findAll();
      expect(remaining).toHaveLength(1);
      expect(remaining[0].name).toBe('Recent Draft');
    });
  });

  describe('Business Rule: Draft Templates', () => {
    it('should duplicate draft to create estimate variations', async () => {
      // Create a comprehensive template draft
      const template = await repository.create({
        name: 'Standard Web Development Package',
        data: {
          estimate_number: 'TEMPLATE-001',
          package_items: [
            { item: 'UI/UX Design', hours: 80, rate: 150 },
            { item: 'Frontend Development', hours: 160, rate: 140 },
            { item: 'Backend Development', hours: 120, rate: 150 },
            { item: 'Testing & QA', hours: 40, rate: 100 },
            { item: 'Project Management', hours: 40, rate: 120 }
          ],
          standard_terms: {
            payment: '50% upfront, 50% on completion',
            timeline: '12 weeks',
            warranty: '90 days'
          }
        }
      });

      const copy = await repository.duplicateDraft(template.id);

      // Business expectation: Quickly create variations of standard packages
      expect(copy.name).toBe('Standard Web Development Package (Copy)');
      expect(copy.id).not.toBe(template.id);
      expect(copy.data.package_items).toHaveLength(5);
      expect(copy.data.standard_terms.payment).toBe('50% upfront, 50% on completion');
    });

    it('should fail when duplicating non-existent draft', async () => {
      // Business expectation: Clear error for invalid operations
      await expect(repository.duplicateDraft(99999))
        .rejects.toThrow('not found');
    });
  });

  describe('Error Handling', () => {
    it('should handle large draft data gracefully', async () => {
      const largeDraft = {
        name: 'Large Enterprise Estimate',
        data: {
          estimate_number: 'LARGE-001',
          line_items: Array(1000).fill(null).map((_, i) => ({
            id: i,
            description: `Line item ${i}`,
            quantity: Math.random() * 100,
            rate: Math.random() * 1000,
            total: Math.random() * 10000
          }))
        }
      };

      // Business expectation: Handle complex enterprise estimates
      const created = await repository.create(largeDraft);
      expect(created.data.line_items).toHaveLength(1000);
    });
  });
});