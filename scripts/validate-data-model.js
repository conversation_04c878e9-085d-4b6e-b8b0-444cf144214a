#!/usr/bin/env node

/**
 * Data Model Validation Script
 * 
 * This script validates that all database operations in the codebase
 * match the actual database schema to prevent runtime errors.
 */

const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

console.log(`${colors.blue}Data Model Validation Script${colors.reset}\n`);

// Open database
const dbPath = path.join(__dirname, '..', 'data', 'upstream.db');
const db = new Database(dbPath);

// Get all table schemas
const tables = {};
const tableList = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();

tableList.forEach(table => {
  const columns = db.prepare(`PRAGMA table_info(${table.name})`).all();
  tables[table.name] = columns.map(col => col.name);
  console.log(`${colors.green}✓${colors.reset} Found table: ${table.name} with ${columns.length} columns`);
});

console.log(`\nTotal tables found: ${Object.keys(tables).length}\n`);

// Patterns to search for in repository files
const patterns = [
  {
    name: 'INSERT statements',
    regex: /INSERT\s+INTO\s+(\w+)\s*\([^)]+\)/gi,
    extract: (match) => {
      const tableName = match[1];
      const columnsMatch = match[0].match(/\(([^)]+)\)/);
      if (columnsMatch) {
        const columns = columnsMatch[1].split(',').map(c => c.trim().replace(/`/g, ''));
        return { tableName, columns };
      }
      return null;
    }
  },
  {
    name: 'UPDATE statements',
    regex: /UPDATE\s+(\w+)\s+SET\s+([^W]+)/gi,
    extract: (match) => {
      const tableName = match[1];
      const setClause = match[2];
      const columns = setClause.split(',').map(part => {
        const col = part.trim().split(/\s*=\s*/)[0].replace(/`/g, '');
        return col;
      }).filter(col => col && !col.includes('?'));
      return { tableName, columns };
    }
  },
  {
    name: 'SELECT statements',
    regex: /SELECT\s+(.+?)\s+FROM\s+(\w+)/gi,
    extract: (match) => {
      const tableName = match[2];
      const selectClause = match[1];
      
      // Skip if it's SELECT *
      if (selectClause.trim() === '*') return null;
      
      // Extract column names from SELECT clause
      const columns = selectClause.split(',').map(part => {
        // Handle "column as alias" pattern
        const colMatch = part.trim().match(/(\w+)(?:\s+as\s+\w+)?/i);
        return colMatch ? colMatch[1] : null;
      }).filter(col => col && col !== 'COUNT' && col !== 'SUM' && col !== 'MAX' && col !== 'MIN');
      
      return { tableName, columns };
    }
  }
];

// Search for SQL operations in repository files
const repoDir = path.join(__dirname, '..', 'src', 'api', 'repositories');
const issues = [];

function searchFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath);
  
  patterns.forEach(pattern => {
    let match;
    const regex = new RegExp(pattern.regex);
    
    while ((match = regex.exec(content)) !== null) {
      const result = pattern.extract(match);
      if (!result) continue;
      
      const { tableName, columns } = result;
      
      // Check if table exists
      if (!tables[tableName]) {
        issues.push({
          file: fileName,
          type: pattern.name,
          issue: `Table '${tableName}' does not exist`,
          severity: 'ERROR'
        });
        continue;
      }
      
      // Check if columns exist
      columns.forEach(column => {
        if (!tables[tableName].includes(column)) {
          // Check for common aliases
          const aliasMap = {
            'client_id': 'company_id',
            'company_id': 'company_id',
            'deleted_at': 'deleted_at'
          };
          
          if (!aliasMap[column] || !tables[tableName].includes(aliasMap[column])) {
            issues.push({
              file: fileName,
              type: pattern.name,
              issue: `Column '${column}' does not exist in table '${tableName}'`,
              severity: 'ERROR',
              availableColumns: tables[tableName].join(', ')
            });
          }
        }
      });
    }
  });
}

// Search all repository files
function searchDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      searchDirectory(filePath);
    } else if (file.endsWith('.ts') || file.endsWith('.js')) {
      searchFile(filePath);
    }
  });
}

console.log(`${colors.blue}Scanning repository files...${colors.reset}\n`);
searchDirectory(repoDir);

// Also check the routes directory
const routesDir = path.join(__dirname, '..', 'src', 'api', 'routes');
searchDirectory(routesDir);

// Display results
console.log(`\n${colors.blue}Validation Results:${colors.reset}\n`);

if (issues.length === 0) {
  console.log(`${colors.green}✓ No data model issues found!${colors.reset}`);
} else {
  console.log(`${colors.red}✗ Found ${issues.length} issues:${colors.reset}\n`);
  
  issues.forEach((issue, index) => {
    console.log(`${index + 1}. ${colors.yellow}${issue.file}${colors.reset}`);
    console.log(`   Type: ${issue.type}`);
    console.log(`   Issue: ${colors.red}${issue.issue}${colors.reset}`);
    if (issue.availableColumns) {
      console.log(`   Available columns: ${issue.availableColumns}`);
    }
    console.log();
  });
}

// Additional checks
console.log(`\n${colors.blue}Additional Checks:${colors.reset}\n`);

// Check for tables with missing foreign keys
const foreignKeyChecks = [
  { table: 'deal', column: 'company_id', references: 'company' },
  { table: 'estimate', column: 'company_id', references: 'company' },
  { table: 'contact_company', column: 'company_id', references: 'company' },
  { table: 'contact_company', column: 'contact_id', references: 'contact' },
  { table: 'deal_estimate', column: 'deal_id', references: 'deal' },
  { table: 'deal_estimate', column: 'estimate_id', references: 'estimate' }
];

foreignKeyChecks.forEach(check => {
  if (tables[check.table] && tables[check.table].includes(check.column)) {
    console.log(`${colors.green}✓${colors.reset} ${check.table}.${check.column} → ${check.references}`);
  } else {
    console.log(`${colors.red}✗${colors.reset} Missing: ${check.table}.${check.column} → ${check.references}`);
  }
});

db.close();

console.log(`\n${colors.blue}Validation complete!${colors.reset}`);

// Exit with error code if issues found
if (issues.length > 0) {
  process.exit(1);
}