/* Deal Edit Page - Modern Professional Design */

/* Main Container */
.deal-edit-page {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900;
  position: relative;
}

/* Header Wrapper with Gradient */
.deal-header-wrapper {
  @apply relative overflow-hidden;
  background: linear-gradient(135deg, rgb(242 240 229), rgb(230 228 217)); /* Flexoki base-50 to base-100 */
  border-bottom: 1px solid rgb(230 228 217); /* Flexoki base-100 */
}

.dark .deal-header-wrapper {
  background: linear-gradient(135deg, rgb(64 62 60), rgb(40 39 38)); /* Flexoki base-800 to base-900 */
  border-bottom-color: rgb(87 86 83); /* Flexoki base-700 */
}

.deal-header-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6;
}

.deal-header-gradient {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    ellipse at top right,
    rgb(154 122 160 / 0.1), /* Flexoki purple-400 with 10% opacity */
    transparent 50%
  );
  pointer-events: none;
}

.dark .deal-header-gradient {
  background: radial-gradient(
    ellipse at top right,
    rgb(154 122 160 / 0.2), /* Flexoki purple-400 with 20% opacity */
    transparent 50%
  );
}

/* Content Wrapper */
.deal-content-wrapper {
  @apply flex gap-6 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6;
}

/* Sidebar Navigation */
.deal-sidebar {
  @apply hidden lg:block w-64 flex-shrink-0;
}

.sidebar-sticky {
  @apply sticky top-6 space-y-6;
}

.sidebar-nav {
  @apply space-y-1;
}

.sidebar-nav-item {
  @apply w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-all duration-200;
  @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white;
  @apply hover:bg-white dark:hover:bg-gray-800 hover:shadow-sm;
  position: relative;
  overflow: hidden;
}

.sidebar-nav-item.active {
  @apply bg-white dark:bg-gray-800 text-purple-600 dark:text-purple-400 shadow-sm;
  @apply border border-gray-200 dark:border-gray-700;
}

.sidebar-nav-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  width: 3px;
  height: 0;
  background: linear-gradient(to bottom, #9A7AA0, #8B7EC8); /* Flexoki purple-400 to purple-500 */
  transform: translateY(-50%);
  transition: height 0.3s ease;
}

.sidebar-nav-item.active::before {
  height: 70%;
}

.nav-icon {
  @apply text-gray-500 dark:text-gray-400;
}

.sidebar-nav-item:hover .nav-icon {
  @apply text-gray-700 dark:text-gray-300;
}

.sidebar-nav-item.active .nav-icon {
  @apply text-purple-600 dark:text-purple-400;
}

.nav-label {
  @apply text-sm font-medium;
}

.nav-indicator {
  @apply ml-auto w-2 h-2 rounded-full bg-purple-600 dark:bg-purple-400;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Sidebar Stats */
.sidebar-stats {
  @apply mt-8 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
}

.sidebar-stats .stat-item {
  @apply py-3 border-b border-gray-100 dark:border-gray-700 last:border-0;
}

.sidebar-stats .stat-label {
  @apply block text-xs text-gray-500 dark:text-gray-400 mb-1;
}

.sidebar-stats .stat-value {
  @apply block text-lg font-semibold text-gray-900 dark:text-white;
}

/* Main Content Area */
.deal-main-content {
  @apply flex-1 min-w-0;
}

/* Mobile Section Tabs */
.mobile-section-tabs {
  @apply lg:hidden mb-6 -mx-4 px-4 border-b border-gray-200 dark:border-gray-700;
  @apply bg-white dark:bg-gray-800 sticky top-0 z-10;
}

.tabs-scroll-container {
  @apply flex gap-2 overflow-x-auto pb-3 pt-3;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tabs-scroll-container::-webkit-scrollbar {
  display: none;
}

.section-tab {
  @apply flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap;
  @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200;
}

.section-tab.active {
  @apply bg-purple-100 dark:bg-purple-900 dark:bg-opacity-30 text-purple-700 dark:text-purple-300;
}

.tab-icon {
  @apply text-gray-500 dark:text-gray-400;
}

.section-tab:hover .tab-icon {
  @apply text-gray-700 dark:text-gray-300;
}

.section-tab.active .tab-icon {
  @apply text-purple-600 dark:text-purple-400;
}

/* Content Sections */
.content-sections {
  @apply space-y-8;
}

.content-section {
  @apply scroll-mt-24;
  animation: fadeIn 0.5s ease-out;
}

/* Section Cards - Modern Design */
.content-section > div {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-300 hover:shadow-md;
  position: relative;
  overflow: hidden;
}

.content-section > div::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #9A7AA0, #8B7EC8, #8B7EC8); /* Flexoki purple gradient */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.content-section > div:hover::before {
  transform: scaleX(1);
}

/* Enhanced Card Headers */
.deal-section-header {
  @apply flex items-center justify-between p-6 border-b border-gray-100 dark:border-gray-700;
}

.deal-section-title {
  @apply flex items-center gap-3;
}

.deal-section-title h3 {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.deal-section-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center;
  @apply bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30;
}

.deal-section-icon svg {
  @apply w-5 h-5 text-purple-600 dark:text-purple-400;
}

/* Form Styling Enhancements */
.deal-form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6 p-6;
}

.deal-form-field {
  @apply space-y-2;
}

.deal-form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.deal-form-input {
  @apply w-full px-4 py-2.5 rounded-lg border border-gray-300 dark:border-gray-600;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply focus:ring-2 focus:ring-purple-500 focus:border-transparent;
  @apply transition-all duration-200;
}

.deal-form-input:hover:not(:focus) {
  @apply border-gray-400 dark:border-gray-500;
}

/* Financial Metrics Card */
.financial-metrics-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4 p-6;
}

.metric-card {
  @apply p-4 rounded-lg bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50 border border-gray-200 dark:border-gray-600;
  @apply transition-all duration-200 hover:border-purple-300 dark:hover:border-purple-600;
}

.metric-label {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-1;
}

.metric-value {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.metric-trend {
  @apply flex items-center gap-1 mt-2 text-xs;
}

.trend-up {
  @apply text-green-600 dark:text-green-400;
}

.trend-down {
  @apply text-red-600 dark:text-red-400;
}

/* Timeline Visualization */
.timeline-container {
  @apply relative p-6;
}

.timeline-line {
  @apply absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700;
}

.timeline-item {
  @apply relative flex items-start gap-4 mb-6 last:mb-0;
}

.timeline-marker {
  @apply w-4 h-4 rounded-full bg-purple-600 dark:bg-purple-400 z-10;
  @apply ring-4 ring-white dark:ring-gray-800;
}

.timeline-content {
  @apply flex-1 bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50 rounded-lg p-4;
}

/* Contact & Estimate Cards */
.entity-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6;
}

.entity-card {
  @apply p-4 rounded-lg border border-gray-200 dark:border-gray-700;
  @apply hover:border-purple-300 dark:hover:border-purple-600;
  @apply transition-all duration-200 hover:shadow-sm;
}

/* Notes Timeline */
.notes-timeline {
  @apply p-6 space-y-4;
}

.note-item {
  @apply flex gap-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
}

.note-avatar {
  @apply w-10 h-10 rounded-full bg-gradient-to-br from-purple-400 to-purple-600;
  @apply flex items-center justify-center text-white font-semibold;
}

/* Loading States */
.deal-loading {
  @apply flex items-center justify-center h-64;
}

.deal-loading-spinner {
  @apply animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-t-transparent;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 1280px) {
  .deal-header-card .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (max-width: 1024px) {
  .deal-content-wrapper {
    @apply block;
  }

  .deal-sidebar {
    @apply hidden;
  }

  .deal-header-card .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .deal-form-grid {
    @apply grid-cols-1;
  }

  .financial-metrics-grid {
    @apply grid-cols-2;
  }

  .entity-grid {
    @apply grid-cols-1;
  }
}

/* Deal Header Card Styles */
.deal-header-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden;
  @apply border border-gray-200 dark:border-gray-700;
}

/* Metric Highlight Cards */
.metric-highlight-card {
  @apply flex items-center gap-3 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50;
  @apply border border-gray-200 dark:border-gray-600;
  @apply hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200;
}

.metric-highlight-card.cursor-pointer {
  @apply hover:bg-gray-100 dark:hover:bg-gray-700;
}

.metric-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;
}

.metric-content {
  @apply flex-1 min-w-0;
}

.metric-content .metric-label {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-0.5;
}

.metric-content .metric-value {
  @apply text-lg font-semibold text-gray-900 dark:text-white truncate;
}

/* Content Section Card */
.content-section-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-300 hover:shadow-md;
  position: relative;
  overflow: hidden;
}

.content-section-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #9A7AA0, #8B7EC8, #8B7EC8); /* Flexoki purple gradient */
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.content-section-card:hover::before {
  transform: scaleX(1);
}

/* Info Cards for View Mode */
.info-card {
  @apply flex items-start gap-3 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50;
  @apply border border-gray-200 dark:border-gray-600;
  @apply hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-200;
}

.info-card-icon {
  @apply w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0;
}

.info-label {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-0.5;
}

.info-value {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

/* Description Card */
.description-card {
  @apply p-4 rounded-lg bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50;
  @apply border border-gray-200 dark:border-gray-600;
}

/* Financial Summary Section */
.financial-summary-section {
  @apply mb-6;
}

.financial-metrics-container {
  @apply bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700;
  @apply rounded-xl p-6 border border-gray-200 dark:border-gray-700;
}

.financial-metrics-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.financial-metric-card {
  @apply bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm;
  @apply border border-gray-200 dark:border-gray-700;
  @apply hover:shadow-md transition-all duration-200;
  @apply flex items-start gap-3;
  min-width: 0; /* Allow content to shrink */
  overflow: hidden; /* Prevent content overflow */
}

.metric-icon-wrapper {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;
}

.metric-details {
  @apply flex-1 min-w-0;
  overflow: hidden; /* Prevent overflow */
}

.metric-details .metric-label {
  @apply text-xs text-gray-500 dark:text-gray-400 mb-1;
  white-space: nowrap; /* Prevent label wrapping */
}

.metric-details .metric-value {
  @apply font-bold text-gray-900 dark:text-white;
  word-break: break-word; /* Allow long values to wrap */
  line-height: 1.2; /* Tighter line height for wrapped text */
}

/* Section Divider */
.section-divider {
  @apply relative;
}

.section-divider::before {
  content: "";
  @apply absolute inset-0 flex items-center;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgb(230 228 217), /* Flexoki base-100 */
    transparent
  );
}

.dark .section-divider::before {
  background: linear-gradient(
    to right,
    transparent,
    rgb(111 110 105), /* Flexoki base-600 */
    transparent
  );
}

.section-divider-content {
  @apply relative flex justify-center;
}

.section-divider-content > * {
  @apply bg-white dark:bg-gray-800 px-3;
}

/* Empty State Card */
.empty-state-card {
  @apply text-center py-8 px-6 rounded-lg bg-gray-50 dark:bg-gray-700 dark:bg-opacity-50;
  @apply border border-dashed border-gray-300 dark:border-gray-600;
}

.empty-state-icon {
  @apply mb-3 flex justify-center;
}

/* Dark Mode Enhancements */
.dark .content-section > div {
  background: linear-gradient(
    135deg,
    rgb(64 62 60 / 0.95), /* Flexoki base-800 */
    rgb(40 39 38 / 0.95) /* Flexoki base-900 */
  );
  border-color: rgb(87 86 83) !important; /* Flexoki base-700 - Ensure dark borders */
}

/* Container Queries for Responsive Components */
@container (min-width: 640px) {
  .deal-form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container (min-width: 768px) {
  .financial-metrics-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
