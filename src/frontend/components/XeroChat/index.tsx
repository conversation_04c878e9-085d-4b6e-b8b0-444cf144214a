import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send, Loader2, Bo<PERSON>, User } from 'lucide-react';
import { useFloatingPanels } from '../../contexts/FloatingPanelsContext';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  toolCalls?: ToolCall[];
}

interface ToolCall {
  tool: string;
  arguments: any;
  result?: any;
}

interface Tool {
  name: string;
  description?: string;
  input_schema?: any;
}

export const XeroChat: React.FC = () => {
  const { xeroExpanded: isOpen, setXeroExpanded: setIsOpen } = useFloatingPanels();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [tools, setTools] = useState<Tool[]>([]);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  // Initialize MCP session when chat opens
  useEffect(() => {
    if (isOpen && !sessionId) {
      initializeSession();
    }
  }, [isOpen, sessionId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const initializeSession = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/mcp/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initialize MCP session');
      }

      const data = await response.json();
      setSessionId(data.sessionId);
      setTools(data.tools || []);

      // Add welcome message
      setMessages([{
        id: Date.now().toString(),
        role: 'assistant',
        content: 'Hello! I\'m connected to your Xero account. I can help you with:\n\n• Viewing invoices, bills, and payments\n• Checking your accounts and bank transactions\n• Reviewing financial reports\n• Finding customer and supplier information\n\nWhat would you like to know?',
        timestamp: new Date()
      }]);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect';
      setError(errorMessage);
      console.error('Error initializing MCP session:', err);
      
      // Check if it's an API key issue
      if (errorMessage.includes('Anthropic API not configured')) {
        setError('The Anthropic API is not configured. Please set the ANTHROPIC_API_KEY environment variable.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSend = async () => {
    if (!input.trim() || !sessionId) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);

    try {
      // Send message to Claude
      const response = await fetch('/api/mcp/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          sessionId,
          message: input
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send message');
      }

      const data = await response.json();

      // Add Claude's response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.message,
        timestamp: new Date(),
        toolCalls: data.toolCalls
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      console.error('Error processing message:', err);
      
      // Add error message to chat
      const errorMsg: Message = {
        id: (Date.now() + 1).toString(),
        role: 'system',
        content: `Error: ${errorMessage}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMsg]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const closeChat = async () => {
    if (sessionId) {
      try {
        await fetch('/api/mcp/close', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ sessionId })
        });
      } catch (err) {
        console.error('Error closing session:', err);
      }
    }
    setIsOpen(false);
    setSessionId(null);
    setMessages([]);
    setTools([]);
    setError(null);
  };

  const suggestedQueries = [
    "Show me unpaid invoices",
    "What's my current bank balance?",
    "List recent payments",
    "Show this month's profit and loss"
  ];

  return (
    <>
      {/* Floating button */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-4 left-4 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-all duration-200 hover:scale-110 z-50"
          aria-label="Open UpstreamAI"
        >
          <MessageCircle size={20} />
        </button>
      )}

      {/* Chat window */}
      {isOpen && (
        <div className="fixed bottom-6 left-4 w-96 h-[600px] bg-white dark:bg-gray-800 rounded-lg shadow-2xl flex flex-col z-50 border border-gray-200 dark:border-gray-700">
          {/* Header */}
          <div className="bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot size={20} />
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">UpstreamAI</h3>
                <span className="text-xs bg-blue-500/50 px-2 py-0.5 rounded text-blue-100">
                  Powered by Claude
                </span>
              </div>
            </div>
            <button
              onClick={closeChat}
              className="text-white hover:bg-blue-700 p-1 rounded transition-colors"
              aria-label="Close chat"
            >
              <X size={20} />
            </button>
          </div>

          {/* Messages area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 p-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-2 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role !== 'user' && (
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.role === 'system' ? 'bg-gray-300 dark:bg-gray-600' : 'bg-blue-100 dark:bg-blue-900/30'
                  }`}>
                    <Bot size={16} className={message.role === 'system' ? 'text-gray-600' : 'text-blue-600'} />
                  </div>
                )}
                
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : message.role === 'system'
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  
                  {message.toolCalls && message.toolCalls.length > 0 && (
                    <div className="mt-2 pt-2 border-t border-gray-300 dark:border-gray-600">
                      <p className="text-xs opacity-70">
                        Used: {message.toolCalls.map(tc => tc.tool.replace(/_/g, ' ')).join(', ')}
                      </p>
                    </div>
                  )}
                </div>

                {message.role === 'user' && (
                  <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <User size={16} className="text-gray-600" />
                  </div>
                )}
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-2 justify-start">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <Bot size={16} className="text-blue-600" />
                </div>
                <div className="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg p-3 flex items-center gap-2">
                  <Loader2 className="animate-spin" size={16} />
                  <span className="text-sm">Thinking...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Suggested queries for empty chat */}
          {messages.length === 1 && !isLoading && (
            <div className="px-4 pb-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Try asking:</p>
              <div className="flex flex-wrap gap-1">
                {suggestedQueries.map((query, index) => (
                  <button
                    key={index}
                    onClick={() => setInput(query)}
                    className="text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 px-2 py-1 rounded transition-colors"
                  >
                    {query}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input area */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex gap-2">
              <input
                type="text"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me about your Xero data..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
                disabled={isLoading || !sessionId}
              />
              <button
                onClick={handleSend}
                disabled={isLoading || !input.trim() || !sessionId}
                className="bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed"
                aria-label="Send message"
              >
                <Send size={20} />
              </button>
            </div>
            
            {/* Status indicator */}
            <div className="mt-2 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${sessionId ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'}`} />
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {sessionId ? 'Connected to Xero' : 'Connecting...'}
                </span>
              </div>
              {tools.length > 0 && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {tools.length} tools available
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};
