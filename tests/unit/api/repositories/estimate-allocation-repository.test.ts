/**
 * Estimate Allocation Repository Test
 * 
 * Tests for the estimate allocation repository focusing on business behavior.
 * Uses an in-memory SQLite database for realistic testing without mocks.
 */
import { EstimateAllocationRepository } from '../../../../src/api/repositories/estimate-allocation-repository';
import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

describe('EstimateAllocationRepository - Business Behavior', () => {
  let repository: EstimateAllocationRepository;
  let db: Database.Database;
  
  beforeAll(() => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Initialize schema using the unified migration
    const migrationPath = path.join(__dirname, '../../../../migrations/000_unified_schema.sql');
    if (fs.existsSync(migrationPath)) {
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    } else {
      throw new Error(`Migration file not found at ${migrationPath}`);
    }
  });
  
  beforeEach(() => {
    repository = new EstimateAllocationRepository(db);
    
    // Clear data before each test
    db.prepare('DELETE FROM estimate_allocation').run();
    db.prepare('DELETE FROM estimate_time_allocation').run();
    db.prepare('DELETE FROM estimate').run();
    db.prepare('DELETE FROM company').run();
    
    // Insert test data
    db.prepare(`
      INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
      VALUES ('company-1', 'Test Company', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
    
    db.prepare(`
      INSERT INTO estimate (id, company_id, project_name, estimate_type, status, created_at, updated_at, created_by, updated_by)
      VALUES ('EST001', 'company-1', 'Test Project', 'internal', 'draft', datetime('now'), datetime('now'), 'test-user', 'test-user'),
             ('EST002', 'company-1', 'Another Project', 'internal', 'draft', datetime('now'), datetime('now'), 'test-user', 'test-user')
    `).run();
  });
  
  afterAll(() => {
    db.close();
  });

  describe('Business Rule: Staff Allocation Management', () => {
    it('should create staff allocation for an estimate', () => {
      const allocationData = {
        estimate_id: 'EST001',
        harvest_user_id: 123,
        first_name: 'John',
        last_name: 'Doe',
        role: 'Senior Developer',
        rate_onbord_cost_daily: 800,
        rate_proposed_daily: 1200,
        rate_client_final_daily: 1200
      };
      
      const result = repository.create(allocationData);
      
      // Business expectation: Staff member is allocated to estimate
      expect(result.id).toBeDefined();
      expect(result.estimate_id).toBe('EST001');
      expect(result.first_name).toBe('John');
      expect(result.role).toBe('Senior Developer');
      expect(result.rate_proposed_daily).toBe(1200);
    });

    it('should allow multiple staff on the same estimate', () => {
      // Business scenario: Team of different roles on a project
      const allocations = [
        {
          estimate_id: 'EST001',
          first_name: 'John',
          last_name: 'Doe',
          role: 'Lead Developer',
          rate_onbord_cost_daily: 800,
          rate_proposed_daily: 1200
        },
        {
          estimate_id: 'EST001',
          first_name: 'Jane',
          last_name: 'Smith',
          role: 'UX Designer',
          rate_onbord_cost_daily: 700,
          rate_proposed_daily: 1000
        },
        {
          estimate_id: 'EST001',
          first_name: 'Bob',
          last_name: 'Johnson',
          role: 'Project Manager',
          rate_onbord_cost_daily: 900,
          rate_proposed_daily: 1300
        }
      ];
      
      allocations.forEach(alloc => repository.create(alloc));
      
      const result = repository.findByEstimateId('EST001');
      
      // Business requirement: Support multi-disciplinary teams
      expect(result).toHaveLength(3);
      expect(result.map(a => a.role)).toEqual(
        expect.arrayContaining(['Lead Developer', 'UX Designer', 'Project Manager'])
      );
    });
  });

  describe('Business Rule: Rate Management', () => {
    it('should track cost vs proposed rates for margin calculation', () => {
      const allocation = repository.create({
        estimate_id: 'EST001',
        first_name: 'Sarah',
        last_name: 'Wilson',
        role: 'Consultant',
        rate_onbord_cost_daily: 600,  // What we pay
        rate_proposed_daily: 1000,     // What we charge
        rate_client_final_daily: 950   // Negotiated rate
      });
      
      // Business requirement: Track profitability per resource
      const margin = ((allocation.rate_proposed_daily - allocation.rate_onbord_cost_daily) / allocation.rate_proposed_daily) * 100;
      expect(margin).toBeCloseTo(40); // 40% margin
      
      // Track discount given
      const discount = ((allocation.rate_proposed_daily - allocation.rate_client_final_daily) / allocation.rate_proposed_daily) * 100;
      expect(discount).toBeCloseTo(5); // 5% discount
    });

    it('should support different rates for different skill levels', () => {
      const juniorDev = repository.create({
        estimate_id: 'EST001',
        first_name: 'Junior',
        last_name: 'Dev',
        role: 'Junior Developer',
        rate_onbord_cost_daily: 400,
        rate_proposed_daily: 600
      });
      
      const seniorDev = repository.create({
        estimate_id: 'EST001',
        first_name: 'Senior',
        last_name: 'Dev',
        role: 'Senior Developer',
        rate_onbord_cost_daily: 800,
        rate_proposed_daily: 1200
      });
      
      // Business requirement: Rate differentiation by experience
      expect(seniorDev.rate_proposed_daily).toBe(2 * juniorDev.rate_proposed_daily);
    });
  });

  describe('Business Rule: Estimate Staff Retrieval', () => {
    beforeEach(() => {
      // Set up test allocations
      const testAllocations = [
        {
          estimate_id: 'EST001',
          first_name: 'Alice',
          last_name: 'Anderson',
          role: 'Tech Lead',
          rate_onbord_cost_daily: 900,
          rate_proposed_daily: 1300
        },
        {
          estimate_id: 'EST001',
          first_name: 'Bob',
          last_name: 'Brown',
          role: 'Backend Developer',
          rate_onbord_cost_daily: 700,
          rate_proposed_daily: 1000
        },
        {
          estimate_id: 'EST002',
          first_name: 'Charlie',
          last_name: 'Chen',
          role: 'Frontend Developer',
          rate_onbord_cost_daily: 700,
          rate_proposed_daily: 1000
        }
      ];
      
      testAllocations.forEach(alloc => repository.create(alloc));
    });

    it('should retrieve all staff for a specific estimate', () => {
      const est001Staff = repository.findByEstimateId('EST001');
      
      // Business requirement: View complete project team
      expect(est001Staff).toHaveLength(2);
      expect(est001Staff.map(s => s.role)).toEqual(
        expect.arrayContaining(['Tech Lead', 'Backend Developer'])
      );
    });

    it('should calculate team cost for an estimate', () => {
      const staff = repository.findByEstimateId('EST001');
      
      // Business requirement: Understand total team cost
      const totalDailyCost = staff.reduce((sum, s) => sum + s.rate_onbord_cost_daily, 0);
      const totalDailyRevenue = staff.reduce((sum, s) => sum + s.rate_proposed_daily, 0);
      
      expect(totalDailyCost).toBe(1600); // 900 + 700
      expect(totalDailyRevenue).toBe(2300); // 1300 + 1000
    });

    it('should identify staff working on multiple estimates', () => {
      // Add same person to another estimate
      repository.create({
        estimate_id: 'EST002',
        first_name: 'Alice',
        last_name: 'Anderson',
        role: 'Tech Lead',
        rate_onbord_cost_daily: 900,
        rate_proposed_daily: 1400 // Different rate for different project
      });
      
      // Business requirement: Track resource utilization across projects
      const allAllocations = db.prepare(`
        SELECT DISTINCT first_name, last_name, COUNT(DISTINCT estimate_id) as project_count
        FROM estimate_allocation
        WHERE first_name = ? AND last_name = ?
        GROUP BY first_name, last_name
      `).get('Alice', 'Anderson') as any;
      
      expect(allAllocations.project_count).toBe(2);
    });
  });

  describe('Business Rule: Staff Updates and Changes', () => {
    let testAllocation: any;
    
    beforeEach(() => {
      testAllocation = repository.create({
        estimate_id: 'EST001',
        first_name: 'Update',
        last_name: 'Test',
        role: 'Developer',
        rate_onbord_cost_daily: 700,
        rate_proposed_daily: 1000
      });
    });

    it('should update staff rates when negotiations change', () => {
      const updated = repository.update(testAllocation.id, {
        rate_client_final_daily: 950 // Client negotiated down
      });
      
      // Business requirement: Track rate negotiations
      expect(updated.rate_client_final_daily).toBe(950);
      expect(updated.rate_proposed_daily).toBe(1000); // Original proposal preserved
    });

    it('should update staff role changes', () => {
      const updated = repository.update(testAllocation.id, {
        role: 'Senior Developer',
        rate_onbord_cost_daily: 800,
        rate_proposed_daily: 1200
      });
      
      // Business requirement: Handle promotions/role changes
      expect(updated.role).toBe('Senior Developer');
      expect(updated.rate_proposed_daily).toBe(1200);
    });

    it('should remove staff from estimate when needed', () => {
      repository.delete(testAllocation.id);
      
      // Business requirement: Adjust team composition
      const remaining = repository.findByEstimateId('EST001');
      expect(remaining.find(s => s.id === testAllocation.id)).toBeUndefined();
    });
  });

  describe('Business Rule: Time Allocation Integration', () => {
    it('should support weekly time allocations for staff', () => {
      const allocation = repository.create({
        estimate_id: 'EST001',
        first_name: 'Weekly',
        last_name: 'Worker',
        role: 'Developer',
        rate_onbord_cost_daily: 700,
        rate_proposed_daily: 1000
      });
      
      // Create weekly allocations (would be in estimate_time_allocation table)
      const weeklyData = [
        { week_of: '2024-01-01', days_allocated: 5 },
        { week_of: '2024-01-08', days_allocated: 5 },
        { week_of: '2024-01-15', days_allocated: 3 },
        { week_of: '2024-01-22', days_allocated: 5 }
      ];
      
      weeklyData.forEach(week => {
        db.prepare(`
          INSERT INTO estimate_time_allocation 
          (estimate_allocation_id, week_of, days_allocated, created_at, updated_at)
          VALUES (?, ?, ?, datetime('now'), datetime('now'))
        `).run(allocation.id, week.week_of, week.days_allocated);
      });
      
      // Business requirement: Calculate total effort and cost
      const timeAllocations = db.prepare(`
        SELECT SUM(days_allocated) as total_days
        FROM estimate_time_allocation
        WHERE estimate_allocation_id = ?
      `).get(allocation.id) as any;
      
      expect(timeAllocations.total_days).toBe(18);
      const totalCost = timeAllocations.total_days * allocation.rate_onbord_cost_daily;
      expect(totalCost).toBe(12600); // 18 days * $700/day
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid estimate references', () => {
      // Business expectation: Cannot allocate to non-existent estimate
      expect(() => repository.create({
        estimate_id: 'INVALID',
        first_name: 'Test',
        last_name: 'User',
        role: 'Developer',
        rate_onbord_cost_daily: 700,
        rate_proposed_daily: 1000
      })).toThrow(/constraint/i);
    });

    it('should validate required fields', () => {
      // Business expectation: Must have minimum information
      expect(() => repository.create({
        estimate_id: 'EST001',
        // Missing required fields
      } as any)).toThrow();
    });
  });
});