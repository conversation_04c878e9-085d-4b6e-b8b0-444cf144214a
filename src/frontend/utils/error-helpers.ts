/**
 * Frontend-specific error handling utilities
 * 
 * Provides user-friendly error messages with retry guidance for frontend components
 */

/**
 * Generate user-friendly error message with retry guidance
 * @param error The error that occurred
 * @param operationName Name of the operation (e.g., "save", "update", "create")
 * @returns User-friendly error message
 */
export function getRetryableErrorMessage(error: unknown, operationName: string = 'operation'): string {
  // Check if it's a network/connection error
  if (error && typeof error === 'object' && 'isNetworkError' in error) {
    return `Connection issue detected. Please check your internet and try again - it usually works on the second attempt.`;
  }
  
  // Check for API errors with status codes
  if (error && typeof error === 'object' && 'status' in error) {
    const status = (error as { status: number }).status;
    
    // Rate limit errors
    if (status === 429) {
      return `Too many requests. Please wait a moment and try again.`;
    }
    
    // Server errors (5xx)
    if (status >= 500) {
      return `Server temporarily unavailable. Don't worry - please try again in a moment as it typically works on retry.`;
    }
    
    // Authentication errors
    if (status === 401) {
      return `Authentication required. Please refresh the page and sign in again.`;
    }
    
    // Permission errors
    if (status === 403) {
      return `Access denied. Please check your permissions.`;
    }
    
    // Not found errors
    if (status === 404) {
      return `Resource not found. Please refresh the page and try again.`;
    }
  }
  
  // Handle timeout and network errors by message content
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    if (message.includes('timeout') || message.includes('etimedout')) {
      return `Request timed out. Please try again - timeout errors often resolve on retry.`;
    }
    
    if (message.includes('fetch') || message.includes('network') || message.includes('connection')) {
      return `Connection issue detected. Please check your internet and try again - it usually works on the second attempt.`;
    }
    
    if (message.includes('econnreset') || message.includes('econnrefused')) {
      return `Connection was reset. Please try again in a moment - this is usually temporary.`;
    }
  }
  
  // Check if this is marked as a retryable error
  if (error && typeof error === 'object' && 'isRetryable' in error && (error as { isRetryable: boolean }).isRetryable) {
    return `Something went wrong with the ${operationName}. Don't worry - please try again as it often works on the second try.`;
  }
  
  // Default message for unknown errors
  return `Failed to ${operationName}. Please try again - it often works on the second attempt.`;
}

/**
 * Check if an error is likely retryable
 * @param error The error to check
 * @returns True if the error is likely to succeed on retry
 */
export function isRetryableError(error: unknown): boolean {
  // Check explicit retryable flag
  if (error && typeof error === 'object' && 'isRetryable' in error) {
    return !!(error as { isRetryable: boolean }).isRetryable;
  }
  
  // Check for retryable status codes
  if (error && typeof error === 'object' && 'status' in error) {
    const status = (error as { status: number }).status;
    return status >= 500 || status === 429; // Server errors and rate limits
  }
  
  // Check for network/timeout errors
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    return (
      message.includes('timeout') ||
      message.includes('etimedout') ||
      message.includes('econnreset') ||
      message.includes('econnrefused') ||
      message.includes('network') ||
      message.includes('connection') ||
      message.includes('fetch')
    );
  }
  
  return false;
}