/**
 * Reset Database Script
 *
 * This script provides a simple way to reset the database to a clean state
 * by running the initialize-fresh-database.js script.
 * 
 * IMPORTANT: This will delete all existing data. Only use when you want to start fresh.
 */

const { execSync } = require('child_process');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Ask for confirmation before proceeding
rl.question('WARNING: This will delete ALL data in the database. Are you sure you want to continue? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes') {
    console.log('Proceeding with database reset...');
    
    try {
      // Run the initialize-fresh-database.js script
      const scriptPath = path.join(__dirname, 'initialize-fresh-database.js');
      console.log(`Executing: node ${scriptPath}`);
      
      execSync(`node ${scriptPath}`, { stdio: 'inherit' });
      
      console.log('\nDatabase reset completed successfully.');
      console.log('The database now has a clean, fresh unified data model.');
    } catch (error) {
      console.error('Error resetting database:', error);
      process.exit(1);
    }
  } else {
    console.log('Database reset cancelled.');
  }
  
  rl.close();
});
