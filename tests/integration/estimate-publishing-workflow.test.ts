import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeDatabase, closeDatabase } from '../../src/api/services/db-service';
import EstimateAllocationRepository from '../../src/api/repositories/estimate-allocation-repository';
import EstimateTimeAllocationRepository from '../../src/api/repositories/estimate-time-allocation-repository';
import EstimateDraftsRepository from '../../src/api/repositories/estimate-drafts-repository';
import DealRepository from '../../src/api/repositories/deal-repository';
import ActivityService from '../../src/api/services/activity-service';
import { getProjects, getUsers, createEstimate } from '../../src/api/integrations/harvest';

// Mock dependencies
jest.mock('../../src/api/integrations/harvest');
jest.mock('../../src/utils/backend-logger');
jest.mock('../../src/utils/email');

describe('Estimate Publishing Workflow Integration', () => {
  let app: any;
  let estimateRepo: EstimateAllocationRepository;
  let timeAllocationRepo: EstimateTimeAllocationRepository;
  let draftsRepo: EstimateDraftsRepository;
  let dealRepo: DealRepository;
  let activityService: ActivityService;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    await initializeDatabase();
    
    const { app: testApp } = await import('../../src/api/server');
    app = testApp;
    
    // Initialize repositories
    estimateRepo = new EstimateAllocationRepository();
    timeAllocationRepo = new EstimateTimeAllocationRepository();
    draftsRepo = new EstimateDraftsRepository();
    dealRepo = new DealRepository();
    activityService = new ActivityService();
  });

  afterAll(async () => {
    await closeDatabase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Estimate Creation and Publishing Workflow', () => {
    const mockHarvestData = {
      projects: [
        {
          id: 100,
          name: 'Website Redesign',
          client: { id: 10, name: 'Acme Corp' },
          budget: 50000,
          budget_by: 'project',
        },
      ],
      users: [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Developer',
          email: '<EMAIL>',
          cost_rate: 75,
          default_hourly_rate: 150,
          is_active: true,
          weekly_capacity: 40,
        },
        {
          id: 2,
          first_name: 'Jane',
          last_name: 'Designer',
          email: '<EMAIL>',
          cost_rate: 65,
          default_hourly_rate: 130,
          is_active: true,
          weekly_capacity: 40,
        },
      ],
    };

    it('should create estimate draft, save, and publish', async () => {
      // Mock Harvest API responses
      (getProjects as jest.Mock).mockResolvedValue(mockHarvestData.projects);
      (getUsers as jest.Mock).mockResolvedValue(mockHarvestData.users);

      // Step 1: Create estimate draft
      const draftResponse = await request(app)
        .post('/api/estimates/drafts')
        .send({
          name: 'Q1 2024 Project Estimate',
          data: {
            estimate_name: 'Website Redesign Phase 2',
            client_name: 'Acme Corp',
            project_type: 'Fixed Price',
            date_sent: '2024-01-15',
            valid_until: '2024-02-15',
            start_date: '2024-02-01',
            end_date: '2024-04-30',
          },
        })
        .expect(201);

      const draftId = draftResponse.body.id;
      expect(draftId).toBeDefined();

      // Step 2: Add team allocations to draft
      const updatedDraftResponse = await request(app)
        .put(`/api/estimates/drafts/${draftId}`)
        .send({
          data: {
            ...draftResponse.body.data,
            allocations: [
              {
                harvest_user_id: 1,
                first_name: 'John',
                last_name: 'Developer',
                role: 'Senior Developer',
                hourly_rate: 150,
                cost_rate: 75,
                hours_per_week: 32,
              },
              {
                harvest_user_id: 2,
                first_name: 'Jane',
                last_name: 'Designer',
                role: 'UI/UX Designer',
                hourly_rate: 130,
                cost_rate: 65,
                hours_per_week: 20,
              },
            ],
          },
        })
        .expect(200);

      // Step 3: Add time allocations
      const timeAllocations = [
        { week_identifier: '2024-W05', days: 8 }, // Feb week 1
        { week_identifier: '2024-W06', days: 10 }, // Feb week 2
        { week_identifier: '2024-W07', days: 10 }, // Feb week 3
        { week_identifier: '2024-W08', days: 8 }, // Feb week 4
        { week_identifier: '2024-W09', days: 10 }, // Mar week 1
        { week_identifier: '2024-W10', days: 10 }, // Mar week 2
      ];

      await request(app)
        .put(`/api/estimates/drafts/${draftId}`)
        .send({
          data: {
            ...updatedDraftResponse.body.data,
            timeAllocations,
          },
        })
        .expect(200);

      // Step 4: Calculate totals
      const totalDays = timeAllocations.reduce((sum, week) => sum + week.days, 0);
      const johnHours = (totalDays * 32) / 5; // 32 hours/week
      const janeHours = (totalDays * 20) / 5; // 20 hours/week
      const totalHours = johnHours + janeHours;
      const totalCost = (johnHours * 150) + (janeHours * 130);

      // Step 5: Create estimate from draft
      const estimateResponse = await request(app)
        .post('/api/estimates')
        .send({
          estimate_name: 'Website Redesign Phase 2',
          client_name: 'Acme Corp',
          project_type: 'Fixed Price',
          date_sent: '2024-01-15',
          valid_until: '2024-02-15',
          start_date: '2024-02-01',
          end_date: '2024-04-30',
          total_hours: totalHours,
          total_cost: totalCost,
          allocations: updatedDraftResponse.body.data.allocations,
          timeAllocations,
          draft_id: draftId,
        })
        .expect(201);

      const estimateId = estimateResponse.body.id;
      expect(estimateId).toBeDefined();

      // Verify estimate was created correctly
      const estimate = await estimateRepo.getEstimateById(estimateId);
      expect(estimate).toMatchObject({
        estimate_name: 'Website Redesign Phase 2',
        total_hours: totalHours,
        total_cost: totalCost,
        status: 'draft',
      });

      // Verify allocations were created
      const allocations = await estimateRepo.getAllocationsByEstimateId(estimateId);
      expect(allocations).toHaveLength(2);
      expect(allocations[0]).toMatchObject({
        first_name: 'John',
        last_name: 'Developer',
        role: 'Senior Developer',
        hourly_rate: 150,
      });

      // Verify time allocations were created
      const timeAllocs = await timeAllocationRepo.getTimeAllocationsByEstimateId(estimateId);
      expect(timeAllocs).toHaveLength(6);

      // Step 6: Link to a deal
      const deal = await dealRepo.createDeal({
        name: 'Website Redesign Phase 2 Deal',
        amount: totalCost,
        stage: 'proposal',
        probability: 0.5,
        close_date: '2024-02-15',
        company_id: 1,
        owner_id: 'user-123',
      });

      await request(app)
        .post(`/api/crm/deals/${deal.id}/estimates`)
        .send({
          estimate_id: estimateId,
          is_primary: true,
        })
        .expect(201);

      // Step 7: Publish estimate
      const publishResponse = await request(app)
        .post(`/api/estimates/${estimateId}/publish`)
        .send({
          send_to_client: true,
          client_email: '<EMAIL>',
          message: 'Please find attached our estimate for Phase 2 of the website redesign.',
        })
        .expect(200);

      expect(publishResponse.body).toMatchObject({
        status: 'published',
        published_at: expect.any(String),
      });

      // Step 8: Export to Harvest
      (createEstimate as jest.Mock).mockResolvedValue({
        id: 'harvest-est-123',
        number: 'EST-2024-001',
        amount: totalCost,
        state: 'sent',
      });

      const harvestExportResponse = await request(app)
        .post(`/api/estimates/${estimateId}/export-harvest`)
        .send({
          harvest_client_id: 10,
          harvest_project_id: 100,
        })
        .expect(200);

      expect(harvestExportResponse.body).toMatchObject({
        harvest_estimate_id: 'harvest-est-123',
        harvest_estimate_number: 'EST-2024-001',
      });

      // Verify Harvest API was called correctly
      expect(createEstimate).toHaveBeenCalledWith({
        client_id: 10,
        subject: 'Website Redesign Phase 2',
        amount: totalCost,
        line_items: expect.arrayContaining([
          expect.objectContaining({
            kind: 'Service',
            description: expect.stringContaining('Senior Developer'),
            quantity: johnHours,
            unit_price: 150,
          }),
          expect.objectContaining({
            kind: 'Service',
            description: expect.stringContaining('UI/UX Designer'),
            quantity: janeHours,
            unit_price: 130,
          }),
        ]),
      });

      // Verify activity logs
      const activities = await activityService.getActivities({
        target_type: 'estimate',
        target_id: estimateId.toString(),
      });

      const activityTypes = activities.activities.map(a => a.action);
      expect(activityTypes).toContain('estimate_created');
      expect(activityTypes).toContain('estimate_published');
      expect(activityTypes).toContain('estimate_exported_to_harvest');

      // Verify draft was marked as used
      const draft = await draftsRepo.getDraftById(draftId);
      expect(draft?.is_published).toBe(true);
    });

    it('should handle estimate revision workflow', async () => {
      // Create initial estimate
      const originalEstimate = await estimateRepo.createEstimate({
        estimate_name: 'Original Estimate',
        client_name: 'Test Client',
        date_sent: '2024-01-01',
        valid_until: '2024-01-31',
        start_date: '2024-02-01',
        end_date: '2024-02-28',
        total_hours: 100,
        total_cost: 10000,
        status: 'published',
        version: 1,
      });

      // Create revision
      const revisionResponse = await request(app)
        .post(`/api/estimates/${originalEstimate.id}/revise`)
        .send({
          reason: 'Client requested additional features',
          changes: {
            total_hours: 150,
            total_cost: 15000,
            end_date: '2024-03-15',
          },
        })
        .expect(201);

      const revisionId = revisionResponse.body.id;

      // Verify revision was created
      expect(revisionResponse.body).toMatchObject({
        estimate_name: 'Original Estimate (Revision 2)',
        version: 2,
        previous_version_id: originalEstimate.id,
        status: 'draft',
      });

      // Add new team member to revision
      await request(app)
        .post(`/api/estimates/${revisionId}/allocations`)
        .send({
          harvest_user_id: 3,
          first_name: 'Bob',
          last_name: 'Tester',
          role: 'QA Engineer',
          hourly_rate: 100,
          hours_per_week: 20,
        })
        .expect(201);

      // Publish revision
      await request(app)
        .post(`/api/estimates/${revisionId}/publish`)
        .send({
          supersede_previous: true,
        })
        .expect(200);

      // Verify original was superseded
      const originalAfterRevision = await estimateRepo.getEstimateById(originalEstimate.id);
      expect(originalAfterRevision?.status).toBe('superseded');
      expect(originalAfterRevision?.superseded_by_id).toBe(revisionId);

      // Verify activity log
      const activities = await activityService.getActivities({
        target_type: 'estimate',
        target_id: revisionId.toString(),
      });

      expect(activities.activities.some(a => 
        a.action === 'estimate_revised' && 
        a.metadata.reason === 'Client requested additional features'
      )).toBe(true);
    });

    it('should validate estimate constraints', async () => {
      // Test invalid date range
      const invalidDateResponse = await request(app)
        .post('/api/estimates')
        .send({
          estimate_name: 'Invalid Date Estimate',
          client_name: 'Test Client',
          date_sent: '2024-01-15',
          valid_until: '2024-01-14', // Before date_sent
          start_date: '2024-02-01',
          end_date: '2024-01-31', // Before start_date
          total_hours: 100,
          total_cost: 10000,
        })
        .expect(400);

      expect(invalidDateResponse.body.error).toContain('Invalid date range');

      // Test allocation hours exceed weekly capacity
      const overCapacityResponse = await request(app)
        .post('/api/estimates')
        .send({
          estimate_name: 'Over Capacity Estimate',
          client_name: 'Test Client',
          date_sent: '2024-01-15',
          valid_until: '2024-02-15',
          start_date: '2024-02-01',
          end_date: '2024-02-07', // 1 week
          total_hours: 200,
          total_cost: 20000,
          allocations: [
            {
              harvest_user_id: 1,
              first_name: 'John',
              last_name: 'Developer',
              role: 'Developer',
              hourly_rate: 100,
              hours_per_week: 50, // Exceeds typical 40 hour capacity
            },
          ],
        })
        .expect(400);

      expect(overCapacityResponse.body.error).toContain('exceeds capacity');
    });
  });

  describe('Estimate Templates', () => {
    it('should create and use estimate templates', async () => {
      // Create template
      const templateResponse = await request(app)
        .post('/api/estimates/templates')
        .send({
          name: 'Standard Web Project Template',
          description: 'Template for typical web development projects',
          default_allocations: [
            {
              role: 'Project Manager',
              hourly_rate: 120,
              hours_per_week: 8,
            },
            {
              role: 'Senior Developer',
              hourly_rate: 150,
              hours_per_week: 32,
            },
            {
              role: 'Designer',
              hourly_rate: 130,
              hours_per_week: 16,
            },
          ],
          default_duration_weeks: 12,
        })
        .expect(201);

      const templateId = templateResponse.body.id;

      // Use template to create estimate
      const estimateFromTemplateResponse = await request(app)
        .post('/api/estimates/from-template')
        .send({
          template_id: templateId,
          estimate_name: 'New Web Project',
          client_name: 'New Client',
          start_date: '2024-03-01',
          duration_weeks: 8, // Override template default
        })
        .expect(201);

      // Verify estimate was created with template values
      const estimate = await estimateRepo.getEstimateById(estimateFromTemplateResponse.body.id);
      const allocations = await estimateRepo.getAllocationsByEstimateId(estimate!.id);

      expect(allocations).toHaveLength(3);
      expect(allocations.find(a => a.role === 'Project Manager')).toBeDefined();
      expect(estimate!.end_date).toBe('2024-04-26'); // 8 weeks from start
    });
  });

  describe('Estimate Approval Workflow', () => {
    it('should handle multi-stage approval process', async () => {
      // Create estimate requiring approval
      const estimate = await estimateRepo.createEstimate({
        estimate_name: 'High Value Estimate',
        client_name: 'Enterprise Client',
        date_sent: '2024-01-15',
        valid_until: '2024-02-15',
        start_date: '2024-03-01',
        end_date: '2024-06-30',
        total_hours: 1000,
        total_cost: 150000, // High value requiring approval
        status: 'pending_approval',
        approval_required: true,
      });

      // Request approval
      const approvalRequestResponse = await request(app)
        .post(`/api/estimates/${estimate.id}/request-approval`)
        .send({
          approvers: ['<EMAIL>', '<EMAIL>'],
          message: 'Please review this high-value estimate',
        })
        .expect(200);

      expect(approvalRequestResponse.body.approval_status).toBe('pending');

      // First approval
      await request(app)
        .post(`/api/estimates/${estimate.id}/approve`)
        .send({
          approver_email: '<EMAIL>',
          comments: 'Looks good from my side',
        })
        .expect(200);

      // Second approval
      const finalApprovalResponse = await request(app)
        .post(`/api/estimates/${estimate.id}/approve`)
        .send({
          approver_email: '<EMAIL>',
          comments: 'Approved',
        })
        .expect(200);

      expect(finalApprovalResponse.body.approval_status).toBe('approved');
      expect(finalApprovalResponse.body.status).toBe('approved');

      // Now can publish
      await request(app)
        .post(`/api/estimates/${estimate.id}/publish`)
        .expect(200);

      // Verify approval history
      const activities = await activityService.getActivities({
        target_type: 'estimate',
        target_id: estimate.id.toString(),
      });

      const approvalActivities = activities.activities.filter(a => 
        a.action.includes('approval')
      );
      expect(approvalActivities).toHaveLength(3); // Request + 2 approvals
    });

    it('should handle approval rejection', async () => {
      const estimate = await estimateRepo.createEstimate({
        estimate_name: 'Rejected Estimate',
        client_name: 'Test Client',
        total_hours: 100,
        total_cost: 10000,
        status: 'pending_approval',
      });

      const rejectionResponse = await request(app)
        .post(`/api/estimates/${estimate.id}/reject`)
        .send({
          rejector_email: '<EMAIL>',
          reason: 'Pricing seems too low for the scope',
          suggestions: 'Please review the hourly rates and add QA resources',
        })
        .expect(200);

      expect(rejectionResponse.body.status).toBe('rejected');
      expect(rejectionResponse.body.rejection_reason).toBe('Pricing seems too low for the scope');

      // Cannot publish rejected estimate
      await request(app)
        .post(`/api/estimates/${estimate.id}/publish`)
        .expect(400);
    });
  });
});