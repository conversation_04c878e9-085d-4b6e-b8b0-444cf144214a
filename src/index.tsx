import React from "react";
import ReactDOM from "react-dom/client";
import axios from "axios";
import { QueryClient, QueryClientProvider } from "react-query";
import App from "./frontend/components/App";
import { LoadingProvider } from "./frontend/contexts/LoadingContext";
import "./tailwind.css";
import "./frontend/styles/short-term-cashflow.css";
import "./frontend/styles/components/command-menu.css";

// Configure axios globally
axios.defaults.withCredentials = true;
console.log("Axios configured with withCredentials=true globally");


// Create a client for React Query with enhanced configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      // Enable request deduplication to prevent simultaneous identical requests
      refetchOnMount: true,
      refetchOnReconnect: false,
    },
    mutations: {
      // Add global mutation error handling
      onError: (error) => {
        console.error('Mutation error:', error);
      },
    },
  },
});

// Create root element and render the app
const rootElement = document.getElementById("root");

if (!rootElement) {
  throw new Error("Failed to find the root element");
}

const root = ReactDOM.createRoot(rootElement);

// Conditionally use StrictMode only in development
const AppWrapper = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  const app = (
    <QueryClientProvider client={queryClient}>
      <LoadingProvider>
        <App />
      </LoadingProvider>
    </QueryClientProvider>
  );

  return isDevelopment ? <React.StrictMode>{app}</React.StrictMode> : app;
};

// Render the app
root.render(<AppWrapper />);
