import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Header } from "./Header";
import { Footer } from "./Footer";
import { UnifiedNavigation } from "../Navigation"; // Import the new unified navigation
import FeedbackButton from "../common/FeedbackButton";
import { useMediaQuery } from "../../hooks/useMediaQuery";

interface MainLayoutProps {
  children: React.ReactNode;
  handleLogout: () => void;
}

export const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  handleLogout,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Handler for version history navigation
  const handleViewVersionHistory = () => {
    navigate("/version-history");
  };

  // Check if current route is CRM to apply full-height layout
  const isCRMRoute = location.pathname.startsWith('/crm');

  if (isCRMRoute) {
    // Full-height layout for CRM with proper scrolling
    return (
      <div className="min-h-screen flex flex-col">
        <Header handleLogout={handleLogout} />
        <div className="flex-1">
          {children}
        </div>
        <UnifiedNavigation variant="mobile" />
        {!isMobile && <FeedbackButton />}
      </div>
    );
  }

  // Standard layout for other pages
  return (
    <>
      {/* Header with desktop navigation */}
      <Header handleLogout={handleLogout} />

      {/* Main Content Container */}
      <div className="max-w-7xl mx-auto bg-white dark:bg-gray-800 shadow-sm rounded-lg mb-6 overflow-visible">
        <div className="p-fluid-md sm:p-fluid-lg">{children}</div>
      </div>

      {/* Mobile Navigation - only visible on small screens */}
      <UnifiedNavigation variant="mobile" />

      {/* Feedback Button for bug reports and feature requests - desktop only */}
      {!isMobile && <FeedbackButton />}

      <Footer onViewVersionHistory={handleViewVersionHistory} />
    </>
  );
};
