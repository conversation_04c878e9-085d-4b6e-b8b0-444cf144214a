#!/bin/bash

# Script to kill processes using specific ports and start the application

API_PORT=3002
FRONTEND_PORT=5173

echo "Checking for processes using API port $API_PORT..."
if pids=$(lsof -ti:$API_PORT); then
  echo "Killing processes on port $API_PORT: $pids"
  kill -9 $pids
else
  echo "No processes found on port $API_PORT."
fi

echo "Checking for processes using frontend port $FRONTEND_PORT..."
if pids=$(lsof -ti:$FRONTEND_PORT); then
  echo "Killing processes on port $FRONTEND_PORT: $pids"
  kill -9 $pids
else
  echo "No processes found on port $FRONTEND_PORT."
fi

echo "Starting application..."
npm run dev
