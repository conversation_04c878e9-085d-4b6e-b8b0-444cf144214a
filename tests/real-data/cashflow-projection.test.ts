/**
 * Cashflow Projection Test
 * 
 * This file tests the core cashflow projection functionality using real data.
 * It ensures the cashflow calculation logic works correctly with actual API responses.
 */
import { isTestEnvironmentConfigured, getTestDateRange } from './utils';

// Import actual service implementations
import { getXeroService } from '../../src/services/xero';
import { getHarvestService } from '../../src/services/harvest';
import { DailyCashflowService } from '../../src/services/cashflow/daily-cashflow-service';
import { TransactionService } from '../../src/services/cashflow/transaction-service';

// Skip all tests if environment is not configured
const runTests = isTestEnvironmentConfigured();

describe('Cashflow Projection with Real Data', () => {
  // Skip the entire suite if credentials are missing
  if (!runTests) {
    it.skip('Cashflow tests skipped due to missing credentials', () => {
      // This test will be skipped
    });
    return;
  }

  // Service instances
  let xeroService: any;
  let harvestService: any;
  let transactionService: TransactionService;
  let dailyCashflowService: DailyCashflowService;
  
  // Test date range
  const { startDate, endDate } = getTestDateRange();

  beforeAll(async () => {
    // Initialize services
    xeroService = getXeroService();
    harvestService = getHarvestService();
    transactionService = new TransactionService();
    dailyCashflowService = new DailyCashflowService();
    
    // Authenticate if needed
    if (xeroService.authenticate) {
      await xeroService.authenticate();
    }
  });

  it('can generate daily cashflow with real transaction data', async () => {
    // Get actual bank accounts from Xero
    const bankAccounts = await xeroService.getBankAccounts();
    expect(bankAccounts).toBeDefined();
    
    // Determine starting balance from bank accounts
    const startingBalance = bankAccounts.reduce((total: number, account: any) => 
      total + (account.balance || 0), 0);
    
    // Get real transactions from various sources
    // 1. Bank transactions from Xero
    const bankTransactions = await xeroService.getBankTransactions(startDate, endDate);
    
    // 2. Outstanding invoices from Harvest
    const invoices = await harvestService.getOutstandingInvoices();
    
    // 3. Transform them to our internal transaction format
    const transformedBankTransactions = transactionService.transformBankTransactionsToTransactions(bankTransactions);
    const transformedInvoices = transactionService.transformInvoicesToTransactions(invoices);
    
    // Combine all transactions
    const allTransactions = [
      ...transformedBankTransactions,
      ...transformedInvoices
    ];
    
    // Generate daily cashflow
    const dailyCashflow = dailyCashflowService.generateDailyCashflow(
      startDate,
      endDate,
      startingBalance,
      allTransactions
    );
    
    // Validate the output structure
    expect(dailyCashflow).toBeDefined();
    expect(Array.isArray(dailyCashflow)).toBe(true);
    expect(dailyCashflow.length).toBeGreaterThan(0);
    
    // Check the first day's data
    const firstDay = dailyCashflow[0];
    expect(firstDay).toHaveProperty('date');
    expect(firstDay).toHaveProperty('balance');
    expect(firstDay).toHaveProperty('inflows');
    expect(firstDay).toHaveProperty('outflows');
    expect(firstDay).toHaveProperty('netFlow');
    expect(firstDay).toHaveProperty('transactions');
    
    // Verify calculation integrity
    let runningBalance = startingBalance;
    for (const day of dailyCashflow) {
      // Each day's net flow should be inflows minus outflows
      expect(day.netFlow).toBeCloseTo(day.inflows - day.outflows);
      
      // Update running balance
      runningBalance += day.netFlow;
      
      // Each day's balance should match the running calculation
      expect(day.balance).toBeCloseTo(runningBalance);
    }
    
    // Log summary for visibility
    console.log(`Generated daily cashflow for ${dailyCashflow.length} days with ${allTransactions.length} transactions`);
    console.log(`Starting balance: ${startingBalance}, Ending balance: ${dailyCashflow[dailyCashflow.length - 1].balance}`);
  });
});
