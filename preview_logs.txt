2025-06-10T01:42:23.936008735Z ==> Cloning from https://github.com/adrian-dotco/onbord-financial-dashboard
2025-06-10T01:42:26.673986269Z ==> Checking out commit f7be78b6eadfd8a4166552f7e2fcd1a4cdb3c227 in branch preview
2025-06-10T01:42:28.881022231Z ==> Using Node.js version 18.17.1 via /opt/render/project/src/.node-version
2025-06-10T01:42:28.912731665Z ==> Node.js version 18.17.1 has reached end-of-life.
2025-06-10T01:42:28.912814557Z ==> Upgrade to a maintained version to receive important security updates.
2025-06-10T01:42:28.912823787Z ==> Information on maintained Node.js versions: https://nodejs.org/en/about/previous-releases
2025-06-10T01:42:28.912869098Z ==> Docs on specifying a Node.js version: https://render.com/docs/node-version
2025-06-10T01:42:30.60603215Z ==> Using Bun version 1.1.0 (default)
2025-06-10T01:42:30.606054991Z ==> Docs on specifying a bun version: https://render.com/docs/bun-version
2025-06-10T01:42:30.663589827Z ==> Running build command 'npm ci --legacy-peer-deps --include=dev && npm run build'...
2025-06-10T01:43:14.5176756Z 
2025-06-10T01:43:14.517719781Z > onbord-financial-dashboard-preview@0.1.0 preinstall
2025-06-10T01:43:14.517728692Z > npm config set legacy-peer-deps true
2025-06-10T01:43:14.517733342Z 
2025-06-10T01:43:14.868723028Z 
2025-06-10T01:43:14.868770309Z added 1062 packages, and audited 1063 packages in 44s
2025-06-10T01:43:14.868988694Z 
2025-06-10T01:43:14.869004695Z 199 packages are looking for funding
2025-06-10T01:43:14.869025475Z   run `npm fund` for details
2025-06-10T01:43:14.871116156Z 
2025-06-10T01:43:14.871129496Z found 0 vulnerabilities
2025-06-10T01:43:15.138556528Z 
2025-06-10T01:43:15.138591359Z > onbord-financial-dashboard-preview@0.1.0 build
2025-06-10T01:43:15.138598099Z > npm run build:frontend && npm run build:backend
2025-06-10T01:43:15.138602529Z 
2025-06-10T01:43:15.364904491Z 
2025-06-10T01:43:15.364931292Z > onbord-financial-dashboard-preview@0.1.0 build:frontend
2025-06-10T01:43:15.364936022Z > vite build
2025-06-10T01:43:15.364939832Z 
2025-06-10T01:43:15.572572074Z The CJS build of Vite's Node API is deprecated. See https://vite.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.
2025-06-10T01:43:15.585555527Z Using preview deployment API URL: upstream-preview.onbord.au
2025-06-10T01:43:15.585583718Z Building with API_URL: https://upstream-preview.onbord.au/api in production mode
2025-06-10T01:43:15.61803662Z vite v6.3.5 building for production...
2025-06-10T01:43:15.960180733Z transforming...
2025-06-10T01:43:27.993571823Z ✓ 4686 modules transformed.
2025-06-10T01:43:28.664121927Z rendering chunks...
2025-06-10T01:43:28.840115937Z [esbuild css minify]
2025-06-10T01:43:28.840143858Z ▲ [WARNING] Expected identifier but found "84rem\\\\" [css-syntax-error]
2025-06-10T01:43:28.840148248Z 
2025-06-10T01:43:28.840152148Z     <stdin>:9412:36:
2025-06-10T01:43:28.840156758Z       9412 │   .max-w-6xl, .max-w-7xl, .max-w-\\[84rem\\] {
2025-06-10T01:43:28.840161178Z            ╵                                     ~~~~~~~
2025-06-10T01:43:28.840164878Z 
2025-06-10T01:43:28.840168548Z 
2025-06-10T01:43:29.375472525Z computing gzip size...
2025-06-10T01:43:29.423047231Z dist/index.html                                      3.09 kB │ gzip:   1.30 kB
2025-06-10T01:43:29.423074681Z dist/assets/index-ChHdbfbe.css                     246.79 kB │ gzip:  36.45 kB
2025-06-10T01:43:29.423348498Z dist/assets/PlusIcon-DcyYfeCY.js                     0.48 kB │ gzip:   0.35 kB
2025-06-10T01:43:29.423401129Z dist/assets/ClockIcon-zMV3b4D9.js                    0.50 kB │ gzip:   0.37 kB
2025-06-10T01:43:29.4234376Z dist/assets/CheckCircleIcon-CP3Uuf6-.js              0.52 kB │ gzip:   0.37 kB
2025-06-10T01:43:29.423483891Z dist/assets/ArrowTrendingUpIcon-CmUg09qQ.js          0.56 kB │ gzip:   0.40 kB
2025-06-10T01:43:29.423540063Z dist/assets/UserIcon-6jgvbQsa.js                     0.61 kB │ gzip:   0.42 kB
2025-06-10T01:43:29.423566233Z dist/assets/LinkIcon-BsU8lF03.js                     0.63 kB │ gzip:   0.40 kB
2025-06-10T01:43:29.423598664Z dist/assets/ExclamationTriangleIcon-C5lIC1Yc.js      0.63 kB │ gzip:   0.45 kB
2025-06-10T01:43:29.423660765Z dist/assets/CurrencyDollarIcon-BABfCB7W.js           0.69 kB │ gzip:   0.47 kB
2025-06-10T01:43:29.423675916Z dist/assets/LightBulbIcon-BxLgafpZ.js                0.70 kB │ gzip:   0.47 kB
2025-06-10T01:43:29.423730307Z dist/assets/ArchiveBoxIcon-Db9dPSN5.js               0.71 kB │ gzip:   0.45 kB
2025-06-10T01:43:29.423799849Z dist/assets/DocumentTextIcon-CGIQLQN7.js             0.74 kB │ gzip:   0.47 kB
2025-06-10T01:43:29.423822509Z dist/assets/BuildingOffice2Icon-CeevT43c.js          0.75 kB │ gzip:   0.46 kB
2025-06-10T01:43:29.42385721Z dist/assets/ChartBarIcon-C4dM7KOb.js                 0.90 kB │ gzip:   0.50 kB
2025-06-10T01:43:29.423874431Z dist/assets/ChevronRightIcon-CSmXWsYL.js             0.92 kB │ gzip:   0.39 kB
2025-06-10T01:43:29.423931612Z dist/assets/UserGroupIcon-C2zI9wFx.js                0.97 kB │ gzip:   0.57 kB
2025-06-10T01:43:29.423944002Z dist/assets/hubspot-B8jswuYT.js                      1.00 kB │ gzip:   0.56 kB
2025-06-10T01:43:29.423988393Z dist/assets/CalendarDaysIcon-BlOatql3.js             1.01 kB │ gzip:   0.51 kB
2025-06-10T01:43:29.424002054Z dist/assets/BriefcaseIcon-Yk32uPVS.js                1.04 kB │ gzip:   0.63 kB
2025-06-10T01:43:29.424051545Z dist/assets/SparklesIcon-CqLOHgB0.js                 1.07 kB │ gzip:   0.56 kB
2025-06-10T01:43:29.424056445Z dist/assets/leads-AS3U8yBF.js                        1.12 kB │ gzip:   0.54 kB
2025-06-10T01:43:29.42425581Z dist/assets/QuestionMarkCircleIcon-BorfLwIQ.js       1.17 kB │ gzip:   0.53 kB
2025-06-10T01:43:29.424447544Z dist/assets/CalendarIcon-jp7FeKHJ.js                 1.27 kB │ gzip:   0.53 kB
2025-06-10T01:43:29.424453325Z dist/assets/UserCircleIcon-DRG9Qq0T.js               2.08 kB │ gzip:   0.74 kB
2025-06-10T01:43:29.424461665Z dist/assets/CommandPalette-Coe-my1E.js               3.78 kB │ gzip:   1.50 kB
2025-06-10T01:43:29.424521286Z dist/assets/ContactsList-BCsBqGQD.js                 5.51 kB │ gzip:   2.17 kB
2025-06-10T01:43:29.424526866Z dist/assets/CompaniesList-Bn2f9mdo.js                6.48 kB │ gzip:   2.37 kB
2025-06-10T01:43:29.424543137Z dist/assets/EstimateLinkModal-D1MiO3S_.js            9.01 kB │ gzip:   2.86 kB
2025-06-10T01:43:29.424580148Z dist/assets/ContactCompanyLinker-CIFCQaXV.js        14.24 kB │ gzip:   4.32 kB
2025-06-10T01:43:29.424625669Z dist/assets/DataManagementPage-EYNEpaAl.js          17.38 kB │ gzip:   4.60 kB
2025-06-10T01:43:29.424640269Z dist/assets/TenderPipeline-CMruNouz.js              20.12 kB │ gzip:   4.95 kB
2025-06-10T01:43:29.42467824Z dist/assets/CRMLayout-ciOu32TQ.js                   21.72 kB │ gzip:   5.76 kB
2025-06-10T01:43:29.42470378Z dist/assets/ContactDetail-CRKzQ_q0.js               22.29 kB │ gzip:   4.65 kB
2025-06-10T01:43:29.424807633Z dist/assets/EnhancedDealBoard-CdLS8WsT.js           35.65 kB │ gzip:   9.20 kB
2025-06-10T01:43:29.424821723Z dist/assets/CompanyDetail-DwmgkEwV.js               41.02 kB │ gzip:   7.75 kB
2025-06-10T01:43:29.424838594Z dist/assets/index-uM676NJi.js                       48.69 kB │ gzip:  13.11 kB
2025-06-10T01:43:29.424845374Z dist/assets/DirectoryPage-CUGu6l9B.js               51.48 kB │ gzip:  13.07 kB
2025-06-10T01:43:29.424889825Z dist/assets/HubSpotIntegration-BmOPJ58x.js          74.84 kB │ gzip:  19.38 kB
2025-06-10T01:43:29.424900205Z dist/assets/IntelligencePage-Bw_bNKKG.js            75.43 kB │ gzip:  17.44 kB
2025-06-10T01:43:29.424931706Z dist/assets/DealEditPage-7YFITrax.js                91.19 kB │ gzip:  18.55 kB
2025-06-10T01:43:29.424967787Z dist/assets/react-force-graph-2d-Cmttbs3G.js       176.78 kB │ gzip:  56.94 kB
2025-06-10T01:43:29.425008598Z dist/assets/index-DQ1wPe8g.js                    1,480.54 kB │ gzip: 374.16 kB
2025-06-10T01:43:29.425124251Z 
2025-06-10T01:43:29.425127421Z (!) Some chunks are larger than 500 kB after minification. Consider:
2025-06-10T01:43:29.425129941Z - Using dynamic import() to code-split the application
2025-06-10T01:43:29.425132881Z - Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
2025-06-10T01:43:29.425134771Z - Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.
2025-06-10T01:43:29.425475779Z ✓ built in 13.77s
2025-06-10T01:43:29.78747663Z 
2025-06-10T01:43:29.787510361Z > onbord-financial-dashboard-preview@0.1.0 build:backend
2025-06-10T01:43:29.787515661Z > tsc --project tsconfig.backend.json --outDir dist --noEmitOnError false || echo 'TypeScript compilation failed, will use ts-node fallback'
2025-06-10T01:43:29.787522851Z 
2025-06-10T01:43:37.011178607Z src/api/clients/xero-api-client.ts(35,51): error TS2339: Property 'getAccessToken' does not exist on type 'XeroService'.
2025-06-10T01:43:37.011454274Z src/api/clients/xero-api-client.ts(54,33): error TS2339: Property 'refreshToken' does not exist on type 'XeroService'.
2025-06-10T01:43:37.011630168Z src/api/controllers/leads.ts(81,11): error TS2345: Argument of type 'RadarCompanyUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:43:37.011637158Z   Index signature for type 'string' is missing in type 'RadarCompanyUpdate'.
2025-06-10T01:43:37.011659999Z src/api/controllers/xero/auth.ts(430,9): error TS2322: Type 'Organisation' is not assignable to type 'XeroOrganization'.
2025-06-10T01:43:37.011676079Z   Index signature for type 'string' is missing in type 'Organisation'.
2025-06-10T01:43:37.01168563Z src/api/controllers/xero/reports.ts(211,58): error TS2345: Argument of type 'ReportWithRows' is not assignable to parameter of type 'XeroProfitAndLossReport'.
2025-06-10T01:43:37.01169077Z   Types of property 'reports' are incompatible.
2025-06-10T01:43:37.0116946Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:43:37.01169863Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:43:37.0117097Z src/api/controllers/xero/reports.ts(230,9): error TS2345: Argument of type 'ProjectSetting[]' is not assignable to parameter of type 'import("/opt/render/project/src/src/services/harvest/project-budget-service").ProjectSetting[]'.
2025-06-10T01:43:37.01171688Z   Type 'ProjectSetting' is missing the following properties from type 'ProjectSetting': projectId, invoiceFrequency, invoiceIntervalDays, paymentTerms
2025-06-10T01:43:37.01172156Z src/api/controllers/xero/reports.ts(487,42): error TS2551: Property 'rows' does not exist on type 'XeroReport'. Did you mean 'Rows'?
2025-06-10T01:43:37.011732991Z src/api/controllers/xero/reports.ts(517,50): error TS2551: Property 'value' does not exist on type 'XeroReportCell'. Did you mean 'Value'?
2025-06-10T01:43:37.011814493Z src/api/controllers/xero/reports.ts(550,59): error TS2551: Property 'value' does not exist on type 'XeroReportCell'. Did you mean 'Value'?
2025-06-10T01:43:37.011960176Z src/api/integrations/harvest.ts(399,26): error TS2339: Property 'cost_rate' does not exist on type 'HarvestUser'.
2025-06-10T01:43:37.011978857Z src/api/integrations/harvest.ts(402,26): error TS2339: Property 'cost_rate' does not exist on type 'HarvestUser'.
2025-06-10T01:43:37.012015098Z src/api/integrations/xero.ts(283,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to type 'XeroApiResponse<XeroBalanceSheetResponse>'.
2025-06-10T01:43:37.012020548Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:43:37.012022638Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:43:37.012024378Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:43:37.012060749Z src/api/integrations/xero.ts(366,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to type 'XeroApiResponse<XeroBankSummaryResponse>'.
2025-06-10T01:43:37.012063089Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:43:37.012064939Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:43:37.012066639Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:43:37.012068849Z src/api/integrations/xero.ts(421,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: RepeatingInvoices; }' is not assignable to type 'XeroApiResponse<XeroRepeatingInvoicesResponse>'.
2025-06-10T01:43:37.012070999Z   Types of property 'response' are incompatible.
2025-06-10T01:43:37.012072939Z     Property 'statusCode' is missing in type 'AxiosResponse<any, any>' but required in type '{ statusCode: number; headers: Record<string, string>; }'.
2025-06-10T01:43:37.012200432Z src/api/integrations/xero.ts(471,7): error TS2322: Type '{ response: AxiosResponse<any, any>; body: Invoices; }' is not assignable to type 'XeroApiResponse<XeroBillsResponse>'.
2025-06-10T01:43:37.012203702Z   Types of property 'response' are incompatible.
2025-06-10T01:43:37.012206132Z     Property 'statusCode' is missing in type 'AxiosResponse<any, any>' but required in type '{ statusCode: number; headers: Record<string, string>; }'.
2025-06-10T01:43:37.012208382Z src/api/integrations/xero.ts(775,9): error TS2322: Type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to type 'XeroApiResponse<XeroReportsResponse>'.
2025-06-10T01:43:37.012210662Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:43:37.012212302Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:43:37.012213982Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:43:37.012218803Z src/api/integrations/xero.ts(809,48): error TS2345: Argument of type '{ response: AxiosResponse<any, any>; body: ReportWithRows; }' is not assignable to parameter of type 'XeroApiResponse<XeroReportsResponse>'.
2025-06-10T01:43:37.012220612Z   The types of 'body.reports' are incompatible between these types.
2025-06-10T01:43:37.012222332Z     Type 'ReportWithRow[]' is not assignable to type 'XeroReport[]'.
2025-06-10T01:43:37.012224023Z       Type 'ReportWithRow' has no properties in common with type 'XeroReport'.
2025-06-10T01:43:37.012245203Z src/api/integrations/xero.ts(887,55): error TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.012248103Z   Type 'number' is not assignable to type 'string'.
2025-06-10T01:43:37.012249763Z src/api/integrations/xero.ts(888,41): error TS2345: Argument of type 'string | number' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.012251463Z   Type 'number' is not assignable to type 'string'.
2025-06-10T01:43:37.012315265Z src/api/middleware/mock-auth.ts(12,11): error TS2430: Interface 'MockRequest' incorrectly extends interface 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.012328065Z   Property 'session' is optional in type 'MockRequest' but required in type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.012333835Z src/api/repositories/activity-repository.ts(301,7): error TS2322: Type 'string' is not assignable to type 'ActivityEntityType'.
2025-06-10T01:43:37.012356526Z src/api/repositories/activity-repository.ts(400,7): error TS2322: Type 'string' is not assignable to type 'ActivityType'.
2025-06-10T01:43:37.012359436Z src/api/repositories/activity-repository.ts(403,7): error TS2322: Type 'string' is not assignable to type 'ActivityStatus'.
2025-06-10T01:43:37.012395277Z src/api/repositories/activity-repository.ts(404,7): error TS2322: Type 'string' is not assignable to type 'ActivityEntityType'.
2025-06-10T01:43:37.01252497Z src/api/repositories/activity-repository.ts(413,7): error TS2322: Type 'string' is not assignable to type 'ActivityImportance'.
2025-06-10T01:43:37.01252843Z src/api/repositories/activity-repository.ts(415,7): error TS2322: Type 'string' is not assignable to type 'ActivitySource'.
2025-06-10T01:43:37.012555051Z src/api/repositories/company-repository.ts(212,7): error TS2322: Type 'Contact[]' is not assignable to type 'number | Partial<BaseContact>[]'.
2025-06-10T01:43:37.01255776Z   Type 'Contact[]' is not assignable to type 'Partial<BaseContact>[]'.
2025-06-10T01:43:37.012559751Z     Type 'Contact' is not assignable to type 'Partial<BaseContact>'.
2025-06-10T01:43:37.012562201Z       Types of property 'company' are incompatible.
2025-06-10T01:43:37.012563991Z         Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:43:37.012573951Z src/api/repositories/company-repository.ts(291,7): error TS2322: Type 'Contact[]' is not assignable to type 'number | Partial<BaseContact>[]'.
2025-06-10T01:43:37.012578871Z   Type 'Contact[]' is not assignable to type 'Partial<BaseContact>[]'.
2025-06-10T01:43:37.012581401Z     Type 'Contact' is not assignable to type 'Partial<BaseContact>'.
2025-06-10T01:43:37.012584531Z       Types of property 'company' are incompatible.
2025-06-10T01:43:37.012587081Z         Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:43:37.012595321Z src/api/repositories/deal-repository.ts(164,22): error TS2352: Conversion of type '{ id: string; name: string; stage: string; value: number | null; currency: string; probability: number | null; expected_close_date: string | null; description: string | null; company_id: string; ... 10 more ...; updated_by: string | null; }' to type 'Deal & { companyName?: string; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012602982Z   Type '{ id: string; name: string; stage: string; value: number; currency: string; probability: number; expected_close_date: string; description: string; company_id: string; company_name: string; company_industry: string; ... 8 more ...; updated_by: string; }' is missing the following properties from type 'Deal': createdAt, updatedAt
2025-06-10T01:43:37.012606042Z src/api/repositories/deal-repository.ts(180,17): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012608722Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:43:37.012610762Z src/api/repositories/deal-repository.ts(181,17): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012622772Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:43:37.012667473Z src/api/repositories/deal-repository.ts(182,17): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012674363Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:43:37.012677663Z src/api/repositories/deal-repository.ts(243,20): error TS2352: Conversion of type '{ id: string; name: string; stage: string; value: number | null; currency: string; probability: number | null; expected_close_date: string | null; description: string | null; company_id: string; ... 10 more ...; updated_by: string | null; }' to type 'Deal & { companyName?: string; }' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012680483Z   Type '{ id: string; name: string; stage: string; value: number; currency: string; probability: number; expected_close_date: string; description: string; company_id: string; company_name: string; company_industry: string; ... 8 more ...; updated_by: string; }' is missing the following properties from type 'Deal': createdAt, updatedAt
2025-06-10T01:43:37.012683174Z src/api/repositories/deal-repository.ts(259,15): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012685794Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:43:37.012688304Z src/api/repositories/deal-repository.ts(260,15): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012690814Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:43:37.012693454Z src/api/repositories/deal-repository.ts(261,15): error TS2352: Conversion of type 'Deal & { companyName?: string; }' to type 'DealRowWithCompany' may be a mistake because neither type sufficiently overlaps with the other. If this was intentional, convert the expression to 'unknown' first.
2025-06-10T01:43:37.012695974Z   Type 'Deal & { companyName?: string; }' is missing the following properties from type 'DealRowWithCompany': expected_close_date, company_id, company_name, company_industry, and 7 more.
2025-06-10T01:43:37.012710234Z src/api/repositories/deal-repository.ts(505,22): error TS2551: Property 'customFields' does not exist on type 'DealUpdateData'. Did you mean 'custom_fields'?
2025-06-10T01:43:37.012713574Z src/api/repositories/deal-repository.ts(506,39): error TS2551: Property 'customFields' does not exist on type 'DealUpdateData'. Did you mean 'custom_fields'?
2025-06-10T01:43:37.012718915Z src/api/repositories/deal-repository.ts(506,80): error TS2551: Property 'customFields' does not exist on type 'DealUpdateData'. Did you mean 'custom_fields'?
2025-06-10T01:43:37.012731635Z src/api/repositories/deal-repository.ts(542,20): error TS2551: Property 'expectedCloseDate' does not exist on type 'DealUpdateData'. Did you mean 'expected_close_date'?
2025-06-10T01:43:37.012762485Z src/api/repositories/deal-repository.ts(542,65): error TS2551: Property 'expectedCloseDate' does not exist on type 'DealUpdateData'. Did you mean 'expected_close_date'?
2025-06-10T01:43:37.012781056Z src/api/repositories/deal-repository.ts(557,20): error TS2551: Property 'updatedBy' does not exist on type 'DealUpdateData'. Did you mean 'updated_by'?
2025-06-10T01:43:37.012787386Z src/api/repositories/enhanced-repository.ts(627,7): error TS2322: Type 'Contact[]' is not assignable to type 'number | Partial<BaseContact>[]'.
2025-06-10T01:43:37.012790536Z   Type 'Contact[]' is not assignable to type 'Partial<BaseContact>[]'.
2025-06-10T01:43:37.012793086Z     Type 'Contact' is not assignable to type 'Partial<BaseContact>'.
2025-06-10T01:43:37.012796066Z       Types of property 'company' are incompatible.
2025-06-10T01:43:37.012798606Z         Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:43:37.012822707Z src/api/repositories/enhanced-repository.ts(637,7): error TS2322: Type 'Deal[]' is not assignable to type 'Partial<BaseDeal>[]'.
2025-06-10T01:43:37.012827637Z   Type 'Deal' is not assignable to type 'Partial<BaseDeal>'.
2025-06-10T01:43:37.012829807Z     Types of property 'company' are incompatible.
2025-06-10T01:43:37.012831927Z       Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:43:37.012834037Z src/api/repositories/expenses-repository.ts(17,3): error TS2416: Property 'getAll' in type 'ExpensesRepository' is not assignable to the same property in base type 'BaseRepositoryEnhanced'.
2025-06-10T01:43:37.012839247Z   Type '(options?: EnhancedQueryOptions) => CustomExpense[]' is not assignable to type '<T>(conditions?: Record<string, any>) => T[]'.
2025-06-10T01:43:37.012841607Z     Type 'CustomExpense[]' is not assignable to type 'T[]'.
2025-06-10T01:43:37.012843647Z       Type 'CustomExpense' is not assignable to type 'T'.
2025-06-10T01:43:37.012846618Z         'T' could be instantiated with an arbitrary type which could be unrelated to 'CustomExpense'.
2025-06-10T01:43:37.012851978Z src/api/repositories/expenses-repository.ts(60,3): error TS2416: Property 'getById' in type 'ExpensesRepository' is not assignable to the same property in base type 'BaseRepositoryEnhanced'.
2025-06-10T01:43:37.012854318Z   Type '(id: string, options?: EnhancedQueryOptions) => CustomExpense' is not assignable to type '<T>(id: string) => T'.
2025-06-10T01:43:37.012856378Z     Type 'CustomExpense' is not assignable to type 'T'.
2025-06-10T01:43:37.012858468Z       'T' could be instantiated with an arbitrary type which could be unrelated to 'CustomExpense'.
2025-06-10T01:43:37.012860698Z src/api/repositories/knowledge-graph-repository.ts(870,44): error TS2339: Property 'getDealsByCompany' does not exist on type 'DealRepository'.
2025-06-10T01:43:37.012862788Z src/api/repositories/knowledge-graph-repository.ts(911,27): error TS2339: Property 'relatedContactId' does not exist on type 'ContactRelationshipWithDetails'.
2025-06-10T01:43:37.012867618Z src/api/repositories/knowledge-graph-repository.ts(937,28): error TS2551: Property 'company_id' does not exist on type 'Deal'. Did you mean 'companyId'?
2025-06-10T01:43:37.012895519Z src/api/repositories/project-repository.ts(3,10): error TS2614: Module '"../services/db-service"' has no exported member 'db'. Did you mean to use 'import db from "../services/db-service"' instead?
2025-06-10T01:43:37.012898099Z src/api/repositories/project-repository.ts(4,10): error TS2305: Module '"../services/activity-service"' has no exported member 'logActivity'.
2025-06-10T01:43:37.012905789Z src/api/repositories/project-repository.ts(57,7): error TS2322: Type 'string' is not assignable to type 'string[]'.
2025-06-10T01:43:37.012917179Z src/api/repositories/project-repository.ts(58,7): error TS2322: Type 'string' is not assignable to type 'Record<string, any>'.
2025-06-10T01:43:37.012921649Z src/api/repositories/project-repository.ts(185,7): error TS2322: Type 'string' is not assignable to type 'string[]'.
2025-06-10T01:43:37.012927469Z src/api/repositories/project-repository.ts(188,7): error TS2322: Type 'string' is not assignable to type 'Record<string, any>'.
2025-06-10T01:43:37.0129334Z src/api/repositories/relationships/deal-estimate-repository.ts(134,9): error TS2367: This comparison appears to be unintentional because the types 'EstimateType.INTERNAL' and '"harvest"' have no overlap.
2025-06-10T01:43:37.01296953Z src/api/routes/crm/companies.ts(273,11): error TS2345: Argument of type 'CompanyUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:43:37.012973201Z   Index signature for type 'string' is missing in type 'CompanyUpdate'.
2025-06-10T01:43:37.012975301Z src/api/routes/crm/companies.ts(281,11): error TS2345: Argument of type 'CompanyUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:43:37.012977421Z   Index signature for type 'string' is missing in type 'CompanyUpdate'.
2025-06-10T01:43:37.013003221Z src/api/routes/crm/conversations.ts(112,35): error TS2339: Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013008691Z src/api/routes/crm/deals.ts(216,9): error TS2345: Argument of type 'DealUpdate' is not assignable to parameter of type 'EntityChanges'.
2025-06-10T01:43:37.013010722Z   Index signature for type 'string' is missing in type 'DealUpdate'.
2025-06-10T01:43:37.013015191Z src/api/routes/crm/network.ts(208,22): error TS2339: Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013017012Z src/api/routes/crm/network.ts(239,22): error TS2339: Property 'user' does not exist on type 'Request<{ id: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013021202Z src/api/routes/crm/network.ts(470,7): error TS2322: Type 'string' is not assignable to type 'number'.
2025-06-10T01:43:37.013024842Z src/api/routes/crm/network.ts(559,33): error TS2445: Property 'getById' is protected and only accessible within class 'BaseRepository' and its subclasses.
2025-06-10T01:43:37.013057553Z src/api/routes/crm/network.ts(580,39): error TS2339: Property 'coveredRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:43:37.013059743Z src/api/routes/crm/network.ts(580,71): error TS2339: Property 'totalRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:43:37.013090683Z src/api/routes/crm/network.ts(583,49): error TS2339: Property 'coveredRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:43:37.013092993Z src/api/routes/crm/network.ts(583,73): error TS2339: Property 'totalRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:43:37.013094913Z src/api/routes/crm/network.ts(584,36): error TS2339: Property 'totalRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:43:37.013098713Z src/api/routes/crm/network.ts(584,58): error TS2339: Property 'coveredRoles' does not exist on type 'CompanyCoverageSummary'.
2025-06-10T01:43:37.013103814Z src/api/routes/crm/network.ts(591,17): error TS2339: Property 'harvestId' does not exist on type 'unknown'.
2025-06-10T01:43:37.013134414Z src/api/routes/crm/network.ts(594,53): error TS2339: Property 'getClientProjects' does not exist on type 'HarvestService'.
2025-06-10T01:43:37.013146415Z src/api/routes/crm/network.ts(594,79): error TS2339: Property 'harvestId' does not exist on type 'unknown'.
2025-06-10T01:43:37.013158605Z src/api/routes/deal-contacts.ts(6,33): error TS2305: Module '"../repositories/relationships/contact-role-repository"' has no exported member 'ContactRole'.
2025-06-10T01:43:37.013163275Z src/api/routes/estimates.ts(247,59): error TS2345: Argument of type '"harvest"' is not assignable to parameter of type '"internal"'.
2025-06-10T01:43:37.013184596Z src/api/routes/feature-flags.ts(78,27): error TS2339: Property 'user' does not exist on type 'Request<{ flag: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013186946Z src/api/routes/feature-flags.ts(132,27): error TS2339: Property 'user' does not exist on type 'Request<{ flag: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013188656Z src/api/routes/feature-flags.ts(173,27): error TS2339: Property 'user' does not exist on type 'Request<{ flag: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013194246Z src/api/routes/feature-flags.ts(217,27): error TS2339: Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013225437Z src/api/routes/feature-flags.ts(288,27): error TS2339: Property 'user' does not exist on type 'Request<{ action: string; }, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013227707Z src/api/routes/harvest.ts(397,82): error TS2339: Property 'name' does not exist on type 'HarvestEstimate'.
2025-06-10T01:43:37.013252987Z src/api/routes/harvest.ts(407,60): error TS2345: Argument of type '"harvest"' is not assignable to parameter of type '"internal"'.
2025-06-10T01:43:37.013255117Z src/api/routes/harvest.ts(433,20): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013257757Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013279428Z src/api/routes/harvest.ts(434,15): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013281998Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013283838Z src/api/routes/harvest.ts(435,13): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013285558Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013287308Z src/api/routes/harvest.ts(436,16): error TS2322: Type 'string | ParsedQs | (string | ParsedQs)[]' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013289058Z   Type 'ParsedQs' is not assignable to type 'QueryParamValue'.
2025-06-10T01:43:37.013299088Z src/api/routes/hubspot-mcp/index.ts(50,33): error TS2551: Property 'userId' does not exist on type 'Session & Partial<SessionData>'. Did you mean 'user'?
2025-06-10T01:43:37.013303738Z src/api/routes/hubspot-mcp/index.ts(98,35): error TS2551: Property 'userId' does not exist on type 'Session & Partial<SessionData>'. Did you mean 'user'?
2025-06-10T01:43:37.013330969Z src/api/routes/hubspot-mcp/index.ts(199,9): error TS2353: Object literal may only specify known properties, and 'toolsUsed' does not exist in type 'LogContext'.
2025-06-10T01:43:37.013348419Z src/api/routes/hubspot-mcp/mcp-session-manager.ts(88,9): error TS2353: Object literal may only specify known properties, and 'messageCount' does not exist in type 'LogContext'.
2025-06-10T01:43:37.01335061Z src/api/routes/hubspot-mcp/mcp-session-manager.ts(110,65): error TS2353: Object literal may only specify known properties, and 'count' does not exist in type 'LogContext'.
2025-06-10T01:43:37.01336767Z src/api/routes/hubspot-mcp/tool-executor.ts(8,10): error TS2724: '"../../services/hubspot"' has no exported member named 'getHubSpotService'. Did you mean 'HubSpotService'?
2025-06-10T01:43:37.013381Z src/api/routes/hubspot-mcp/tool-executor.ts(26,43): error TS2353: Object literal may only specify known properties, and 'toolName' does not exist in type 'LogContext'.
2025-06-10T01:43:37.01338506Z src/api/routes/hubspot-mcp/tool-executor.ts(103,52): error TS2353: Object literal may only specify known properties, and 'toolName' does not exist in type 'Error'.
2025-06-10T01:43:37.013386931Z src/api/routes/hubspot-mcp/tool-executor.ts(121,32): error TS2339: Property 'searchContacts' does not exist on type 'ContactRepository'.
2025-06-10T01:43:37.013431872Z src/api/routes/hubspot-mcp/tool-executor.ts(145,45): error TS2345: Argument of type '{ firstName: any; lastName: any; email: any; phone: any; jobTitle: any; notes: any; source: string; }' is not assignable to parameter of type 'ContactCreate'.
2025-06-10T01:43:37.013436702Z   Types of property 'source' are incompatible.
2025-06-10T01:43:37.013439132Z     Type 'string' is not assignable to type 'ContactSource'.
2025-06-10T01:43:37.013441402Z src/api/routes/hubspot-mcp/tool-executor.ts(152,59): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013447682Z src/api/routes/hubspot-mcp/tool-executor.ts(161,68): error TS2554: Expected 2 arguments, but got 3.
2025-06-10T01:43:37.013452482Z src/api/routes/hubspot-mcp/tool-executor.ts(172,66): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013471953Z src/api/routes/hubspot-mcp/tool-executor.ts(192,33): error TS2339: Property 'searchCompanies' does not exist on type 'CompanyRepository'.
2025-06-10T01:43:37.013475683Z src/api/routes/hubspot-mcp/tool-executor.ts(222,45): error TS2345: Argument of type '{ name: any; domain: any; industry: any; phone: any; address: any; city: any; state: any; country: any; numberOfEmployees: any; annualRevenue: any; description: any; website: any; source: string; }' is not assignable to parameter of type 'CompanyCreate'.
2025-06-10T01:43:37.013477953Z   Types of property 'source' are incompatible.
2025-06-10T01:43:37.013479823Z     Type 'string' is not assignable to type 'CompanySource'.
2025-06-10T01:43:37.013483643Z src/api/routes/hubspot-mcp/tool-executor.ts(229,59): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013485653Z src/api/routes/hubspot-mcp/tool-executor.ts(238,68): error TS2554: Expected 2 arguments, but got 3.
2025-06-10T01:43:37.013500093Z src/api/routes/hubspot-mcp/tool-executor.ts(249,66): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013502033Z src/api/routes/hubspot-mcp/tool-executor.ts(316,56): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013529334Z src/api/routes/hubspot-mcp/tool-executor.ts(336,63): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013532594Z src/api/routes/hubspot-mcp/tool-executor.ts(346,39): error TS2339: Property 'getContactCompanies' does not exist on type 'ContactRelationshipsRepository'.
2025-06-10T01:43:37.013562505Z src/api/routes/hubspot-mcp/tool-executor.ts(352,38): error TS2339: Property 'getCompanyContacts' does not exist on type 'ContactRelationshipsRepository'.
2025-06-10T01:43:37.013565105Z src/api/routes/hubspot-mcp/tool-executor.ts(358,37): error TS2339: Property 'associateContactWithCompany' does not exist on type 'ContactRelationshipsRepository'.
2025-06-10T01:43:37.013569255Z src/api/routes/hubspot-mcp/tool-executor.ts(399,49): error TS2353: Object literal may only specify known properties, and 'error' does not exist in type 'Error'.
2025-06-10T01:43:37.013584625Z src/api/routes/hubspot.ts(437,79): error TS2339: Property 'notesCount' does not exist on type '{ companiesCount?: number; dealsCount?: number; contactsCount?: number; }'.
2025-06-10T01:43:37.013590545Z src/api/routes/hubspot.ts(440,86): error TS2339: Property 'associationsCount' does not exist on type '{ companiesCount?: number; dealsCount?: number; contactsCount?: number; }'.
2025-06-10T01:43:37.013611746Z src/api/routes/mcp/index.ts(11,5): error TS2717: Subsequent property declarations must have the same type.  Property 'tokenSet' must be of type 'XeroTokenSet', but here has type 'any'.
2025-06-10T01:43:37.013614186Z src/api/routes/mcp/tool-executor.ts(21,103): error TS2345: Argument of type '{ page: any; status: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.013623166Z src/api/routes/mcp/tool-executor.ts(29,103): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.013654767Z src/api/routes/mcp/tool-executor.ts(41,92): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.013659747Z src/api/routes/mcp/tool-executor.ts(48,108): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.013665987Z src/api/routes/mcp/tool-executor.ts(60,98): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.013668387Z src/api/routes/mcp/tool-executor.ts(67,88): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type 'string'.
2025-06-10T01:43:37.013673847Z src/api/routes/mcp/tool-executor.ts(74,104): error TS2345: Argument of type '{ page: any; }' is not assignable to parameter of type '"AUTHORISED" | "DRAFT" | "SUBMITTED" | "DELETED" | "BILLED"'.
2025-06-10T01:43:37.013676467Z src/api/routes/mcp/tool-executor.ts(161,55): error TS2551: Property 'getEmployeeLeave' does not exist on type 'PayrollAuApi'. Did you mean 'getEmployee'?
2025-06-10T01:43:37.013685638Z src/api/routes/mcp/tool-executor.ts(169,63): error TS2339: Property 'getEmployeeLeaveBalances' does not exist on type 'PayrollAuApi'.
2025-06-10T01:43:37.013691108Z src/api/routes/mcp/tool-executor.ts(177,60): error TS2339: Property 'getEmployeeLeaveTypes' does not exist on type 'PayrollAuApi'.
2025-06-10T01:43:37.013720088Z src/api/routes/mcp/tool-executor.ts(185,62): error TS2339: Property 'getEmployeeLeavePeriods' does not exist on type 'PayrollAuApi'.
2025-06-10T01:43:37.013723839Z src/api/routes/mcp/tool-executor.ts(193,63): error TS2339: Property 'getLeaveTypes' does not exist on type 'PayrollAuApi'.
2025-06-10T01:43:37.01377344Z src/api/routes/mcp/tool-executor.ts(202,39): error TS2551: Property 'timesheets' does not exist on type 'TimesheetObject'. Did you mean 'timesheet'?
2025-06-10T01:43:37.01378263Z src/api/routes/mcp/tool-executor.ts(216,22): error TS2322: Type '{ name: any; emailAddress: any; phones: { phoneType: string; phoneNumber: any; }[]; isCustomer: any; isSupplier: any; }' is not assignable to type 'Contact'.
2025-06-10T01:43:37.01378596Z   Types of property ''phones'' are incompatible.
2025-06-10T01:43:37.01378874Z     Type '{ phoneType: string; phoneNumber: any; }[]' is not assignable to type 'Phone[]'.
2025-06-10T01:43:37.01379091Z       Type '{ phoneType: string; phoneNumber: any; }' is not assignable to type 'Phone'.
2025-06-10T01:43:37.0137932Z         Types of property ''phoneType'' are incompatible.
2025-06-10T01:43:37.01379531Z           Type 'string' is not assignable to type 'PhoneTypeEnum'.
2025-06-10T01:43:37.013800601Z src/api/routes/mcp/tool-executor.ts(299,22): error TS2322: Type '{ contactID: any; name: any; emailAddress: any; phones: { phoneType: string; phoneNumber: any; }[]; }' is not assignable to type 'Contact'.
2025-06-10T01:43:37.013809511Z   Types of property ''phones'' are incompatible.
2025-06-10T01:43:37.013811881Z     Type '{ phoneType: string; phoneNumber: any; }[]' is not assignable to type 'Phone[]'.
2025-06-10T01:43:37.013813941Z       Type '{ phoneType: string; phoneNumber: any; }' is not assignable to type 'Phone'.
2025-06-10T01:43:37.013815961Z         Types of property ''phoneType'' are incompatible.
2025-06-10T01:43:37.013818061Z           Type 'string' is not assignable to type 'PhoneTypeEnum'.
2025-06-10T01:43:37.013822851Z src/api/routes/radar-actions.ts(26,7): error TS2322: Type 'string | string[]' is not assignable to type 'RadarActionStatus | RadarActionStatus[]'.
2025-06-10T01:43:37.013825351Z   Type 'string' is not assignable to type 'RadarActionStatus | RadarActionStatus[]'.
2025-06-10T01:43:37.013827521Z src/api/routes/radar-actions.ts(31,7): error TS2322: Type 'string | string[]' is not assignable to type 'RadarActionPriority | RadarActionPriority[]'.
2025-06-10T01:43:37.013829611Z   Type 'string' is not assignable to type 'RadarActionPriority | RadarActionPriority[]'.
2025-06-10T01:43:37.013831581Z src/api/routes/radar-actions.ts(36,7): error TS2322: Type 'string | string[]' is not assignable to type 'RadarActionType | RadarActionType[]'.
2025-06-10T01:43:37.013833661Z   Type 'string' is not assignable to type 'RadarActionType | RadarActionType[]'.
2025-06-10T01:43:37.013835951Z src/api/routes/radar-actions.ts(136,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013840551Z src/api/routes/radar-actions.ts(165,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013843102Z src/api/routes/radar-actions.ts(190,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013859272Z src/api/routes/radar-actions.ts(212,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013874122Z src/api/routes/radar-actions.ts(234,24): error TS2339: Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.
2025-06-10T01:43:37.013908473Z src/api/services/activity-service.ts(214,7): error TS2322: Type '"ui"' is not assignable to type 'ActivitySource'.
2025-06-10T01:43:37.013911453Z src/api/services/hubspot/deals.ts(37,31): error TS2339: Property 'associationsApi' does not exist on type 'DealsDiscovery'.
2025-06-10T01:43:37.013916393Z src/api/services/hubspot/deals.ts(50,51): error TS2339: Property 'associationsApi' does not exist on type 'DealsDiscovery'.
2025-06-10T01:43:37.013918973Z src/api/services/hubspot/deals.ts(469,46): error TS2345: Argument of type '{ dealsCount: number; companiesCount: number; contactsCount: number; }' is not assignable to parameter of type 'number'.
2025-06-10T01:43:37.013948444Z src/api/services/hubspot/index.ts(299,11): error TS2353: Object literal may only specify known properties, and 'notesCount' does not exist in type '{ companiesCount?: number; dealsCount?: number; contactsCount?: number; }'.
2025-06-10T01:43:37.013951564Z src/api/services/hubspot/notes.ts(224,53): error TS2339: Property 'getNotesByEntity' does not exist on type 'NoteRepository'.
2025-06-10T01:43:37.013957184Z src/api/services/hubspot/notes.ts(240,64): error TS2345: Argument of type '{ entityType: "company" | "contact" | "deal"; entityId: string; content: string; noteType: string; isPrivate: boolean; createdBy: string; }' is not assignable to parameter of type 'NoteCreate'.
2025-06-10T01:43:37.013964514Z   Property 'dealId' is missing in type '{ entityType: "company" | "contact" | "deal"; entityId: string; content: string; noteType: string; isPrivate: boolean; createdBy: string; }' but required in type 'NoteCreate'.
2025-06-10T01:43:37.013973995Z src/api/services/tender-ingestion-service.ts(147,48): error TS2339: Property 'searchCompanies' does not exist on type 'CompanyRepository'.
2025-06-10T01:43:37.013997655Z src/api/utils/db-safe-query.ts(17,7): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:43:37.014000775Z src/api/utils/db-safe-query.ts(51,33): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:43:37.014003125Z src/api/utils/db-safe-query.ts(73,7): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:43:37.014028596Z src/api/utils/db-timeout.ts(8,36): error TS2709: Cannot use namespace 'Statement' as a type.
2025-06-10T01:43:37.014031366Z src/api/utils/db-timeout.ts(11,69): error TS2709: Cannot use namespace 'RunResult' as a type.
2025-06-10T01:43:37.014033716Z src/api/utils/db-timeout.ts(14,35): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:43:37.014069837Z src/api/utils/db-timeout.ts(42,14): error TS2709: Cannot use namespace 'Statement' as a type.
2025-06-10T01:43:37.014075337Z src/api/utils/db-timeout.ts(95,37): error TS2709: Cannot use namespace 'Database' as a type.
2025-06-10T01:43:37.014107038Z src/api/utils/db-timeout.ts(116,77): error TS2709: Cannot use namespace 'RunResult' as a type.
2025-06-10T01:43:37.014109968Z src/database/index.ts(14,31): error TS2694: Namespace '"better-sqlite3"' has no exported member 'Database'.
2025-06-10T01:43:37.014114668Z src/database/index.ts(51,46): error TS2694: Namespace '"better-sqlite3"' has no exported member 'Database'.
2025-06-10T01:43:37.014116968Z src/database/index.ts(144,53): error TS2694: Namespace '"better-sqlite3"' has no exported member 'Database'.
2025-06-10T01:43:37.014280612Z src/frontend/types/crm-types.ts(53,18): error TS2430: Interface 'Deal' incorrectly extends interface 'BaseDeal'.
2025-06-10T01:43:37.014284852Z   Types of property 'company' are incompatible.
2025-06-10T01:43:37.014287422Z     Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:43:37.014289892Z src/frontend/types/crm-types.ts(109,18): error TS2430: Interface 'Contact' incorrectly extends interface 'BaseContact'.
2025-06-10T01:43:37.014292362Z   Types of property 'company' are incompatible.
2025-06-10T01:43:37.014294882Z     Type 'Partial<BaseCompany>' is not assignable to type 'string'.
2025-06-10T01:43:37.014297153Z src/services/cashflow/index.ts(231,11): error TS2345: Argument of type 'Transaction[]' is not assignable to parameter of type 'ProjectionInvoice[]'.
2025-06-10T01:43:37.014300213Z   Property 'what' is missing in type 'Transaction' but required in type 'ProjectionInvoice'.
2025-06-10T01:43:37.014310613Z src/services/cashflow/projection-filter-service.ts(234,34): error TS2339: Property 'clientName' does not exist on type 'ProjectionInvoice'.
2025-06-10T01:43:37.014313063Z src/services/cashflow/projection-filter-service.ts(258,38): error TS2339: Property 'clientName' does not exist on type 'OpenInvoice'.
2025-06-10T01:43:37.014322303Z src/services/cashflow/projection-filter-service.ts(263,85): error TS2339: Property 'number' does not exist on type 'OpenInvoice'.
2025-06-10T01:43:37.014331583Z src/services/cashflow/projection-filter-service.ts(265,75): error TS2339: Property 'due_date' does not exist on type 'OpenInvoice'.
2025-06-10T01:43:37.014341643Z src/services/harvest/time-report-service.ts(115,64): error TS2339: Property 'project_id' does not exist on type 'unknown'.
2025-06-10T01:43:37.014349824Z src/services/harvest/time-report-service.ts(117,59): error TS2339: Property 'billable_amount' does not exist on type 'unknown'.
2025-06-10T01:43:37.014358664Z src/services/harvest/time-report-service.ts(119,35): error TS2339: Property 'project_id' does not exist on type 'unknown'.
2025-06-10T01:43:37.014373294Z src/services/harvest/time-report-service.ts(120,37): error TS2339: Property 'project_name' does not exist on type 'unknown'.
2025-06-10T01:43:37.014376894Z src/services/harvest/time-report-service.ts(121,34): error TS2339: Property 'client_id' does not exist on type 'unknown'.
2025-06-10T01:43:37.014378904Z src/services/harvest/time-report-service.ts(122,36): error TS2339: Property 'client_name' does not exist on type 'unknown'.
2025-06-10T01:43:37.014388085Z src/services/harvest/time-report-service.ts(123,40): error TS2339: Property 'billable_amount' does not exist on type 'unknown'.
2025-06-10T01:43:37.014419616Z src/services/harvest/time-report-service.ts(124,34): error TS2339: Property 'currency' does not exist on type 'unknown'.
2025-06-10T01:43:37.014422596Z src/services/harvest/time-report-service.ts(183,71): error TS2339: Property 'user_id' does not exist on type 'unknown'.
2025-06-10T01:43:37.014425065Z src/services/harvest/time-report-service.ts(188,45): error TS2339: Property 'weekly_capacity' does not exist on type 'HarvestUser'.
2025-06-10T01:43:37.014440196Z src/services/harvest/time-report-service.ts(191,46): error TS2339: Property 'billable_hours' does not exist on type 'unknown'.
2025-06-10T01:43:37.014445226Z src/services/harvest/time-report-service.ts(192,43): error TS2339: Property 'total_hours' does not exist on type 'unknown'.
2025-06-10T01:43:37.014451386Z src/services/harvest/time-report-service.ts(195,47): error TS2339: Property 'billable_amount' does not exist on type 'unknown'.
2025-06-10T01:43:37.014454146Z src/services/harvest/time-report-service.ts(196,41): error TS2339: Property 'currency' does not exist on type 'unknown'.
2025-06-10T01:43:37.014456776Z src/services/harvest/time-report-service.ts(330,28): error TS2339: Property 'cost_rate' does not exist on type 'HarvestUser'.
2025-06-10T01:43:37.014482397Z src/services/harvest/time-report-service.ts(332,32): error TS2339: Property 'is_contractor' does not exist on type 'HarvestUser'.
2025-06-10T01:43:37.014511338Z src/services/xero/bank-account-service.ts(193,54): error TS2345: Argument of type 'XeroReport' is not assignable to parameter of type 'XeroBankSummaryReport'.
2025-06-10T01:43:37.014514338Z   Types of property 'Rows' are incompatible.
2025-06-10T01:43:37.014516478Z     Type 'XeroReportRow[]' is not assignable to type 'XeroReportRow[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:43:37.014518578Z       Type 'XeroReportRow' is not assignable to type 'XeroReportRow'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:43:37.014520888Z         Types of property 'Cells' are incompatible.
2025-06-10T01:43:37.014522908Z           Type 'XeroReportCell[]' is not assignable to type 'XeroReportCell[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:43:37.014525118Z             Type 'XeroReportCell' has no properties in common with type 'XeroReportCell'.
2025-06-10T01:43:37.014527108Z src/services/xero/bank-account-service.ts(196,54): error TS2345: Argument of type 'XeroReport' is not assignable to parameter of type 'XeroBankSummaryReport'.
2025-06-10T01:43:37.014529208Z   Types of property 'Rows' are incompatible.
2025-06-10T01:43:37.014531218Z     Type 'XeroReportRow[]' is not assignable to type 'XeroReportRow[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:43:37.014533228Z       Type 'XeroReportRow' is not assignable to type 'XeroReportRow'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:43:37.014535368Z         Types of property 'Cells' are incompatible.
2025-06-10T01:43:37.014542598Z           Type 'XeroReportCell[]' is not assignable to type 'XeroReportCell[]'. Two different types with this name exist, but they are unrelated.
2025-06-10T01:43:37.014545068Z             Type 'XeroReportCell' has no properties in common with type 'XeroReportCell'.
2025-06-10T01:43:37.014550068Z src/services/xero/bank-account-service.ts(202,46): error TS2551: Property 'ReportID' does not exist on type 'XeroBankSummaryResponse'. Did you mean 'Reports'?
2025-06-10T01:43:37.014552409Z src/services/xero/bank-account-service.ts(204,56): error TS2559: Type 'XeroBankSummaryResponse' has no properties in common with type 'XeroBankSummaryReport'.
2025-06-10T01:43:37.014554519Z src/services/xero/bank-account-service.ts(314,28): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:43:37.014556779Z src/services/xero/bank-account-service.ts(326,28): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:43:37.014559229Z src/services/xero/bank-account-service.ts(347,18): error TS2339: Property 'address' does not exist on type 'XeroReportCell'.
2025-06-10T01:43:37.014564049Z src/services/xero/bank-account-service.ts(347,51): error TS2339: Property 'address' does not exist on type 'XeroReportCell'.
2025-06-10T01:43:37.014590749Z src/services/xero/bank-account-service.ts(347,82): error TS2339: Property 'address' does not exist on type 'XeroReportCell'.
2025-06-10T01:43:37.01459381Z src/services/xero/bank-account-service.ts(351,32): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:43:37.01459605Z src/services/xero/bank-account-service.ts(354,32): error TS2551: Property 'attribute' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:43:37.01460222Z src/services/xero/bank-account-service.ts(474,40): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:43:37.01460957Z src/services/xero/bank-account-service.ts(481,39): error TS2551: Property 'Attributes' does not exist on type 'XeroReportCell'. Did you mean 'attributes'?
2025-06-10T01:43:37.01461494Z src/services/xero/bank-account-service.ts(484,32): error TS2551: Property 'id' does not exist on type 'XeroReportAttribute'. Did you mean 'Id'?
2025-06-10T01:43:37.014634531Z src/services/xero/bank-account-service.ts(494,43): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:43:37.014637581Z src/services/xero/bank-account-service.ts(495,43): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:43:37.014657721Z src/services/xero/bank-account-service.ts(535,45): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:43:37.014662981Z src/services/xero/bank-account-service.ts(536,45): error TS2551: Property 'Value' does not exist on type 'XeroReportCell'. Did you mean 'value'?
2025-06-10T01:43:37.014687372Z src/services/xero/bank-account-service.ts(583,33): error TS2339: Property 'AccountID' does not exist on type 'unknown'.
2025-06-10T01:43:37.014690602Z src/services/xero/bank-account-service.ts(583,54): error TS2339: Property 'accountID' does not exist on type 'unknown'.
2025-06-10T01:43:37.014698942Z src/services/xero/bank-account-service.ts(584,35): error TS2339: Property 'Name' does not exist on type 'unknown'.
2025-06-10T01:43:37.014705342Z src/services/xero/bank-account-service.ts(584,51): error TS2339: Property 'name' does not exist on type 'unknown'.
2025-06-10T01:43:37.014708312Z src/services/xero/bank-account-service.ts(590,26): error TS2339: Property 'CurrentBalance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014716883Z src/services/xero/bank-account-service.ts(591,34): error TS2339: Property 'CurrentBalance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014722623Z src/services/xero/bank-account-service.ts(593,33): error TS2339: Property 'Balance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014729013Z src/services/xero/bank-account-service.ts(594,34): error TS2339: Property 'Balance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014791004Z src/services/xero/bank-account-service.ts(596,33): error TS2339: Property 'currentBalance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014798894Z src/services/xero/bank-account-service.ts(597,34): error TS2339: Property 'currentBalance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014807725Z src/services/xero/bank-account-service.ts(599,33): error TS2339: Property 'balance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014821015Z src/services/xero/bank-account-service.ts(600,34): error TS2339: Property 'balance' does not exist on type 'unknown'.
2025-06-10T01:43:37.014828015Z src/services/xero/repeating-bill-service.ts(64,30): error TS2698: Spread types may only be created from object types.
2025-06-10T01:43:37.014851046Z src/services/xero/xero-service.ts(281,7): error TS2322: Type 'TokenSet' is not assignable to type 'XeroTokenSet'.
2025-06-10T01:43:37.014854796Z   Property 'access_token' is optional in type 'TokenSet' but required in type 'XeroTokenSet'.
2025-06-10T01:43:37.014864966Z src/services/xero/xero-service.ts(319,9): error TS2353: Object literal may only specify known properties, and 'tenantConnections' does not exist in type 'XeroTenant'.
2025-06-10T01:43:37.014867096Z src/services/xero/xero-service.ts(442,9): error TS2322: Type 'import("/opt/render/project/src/src/services/xero/bank-account-service").BankBalances' is not assignable to type 'BankBalances'.
2025-06-10T01:43:37.014868976Z   Property 'totalOpeningBalance' is optional in type 'BankBalances' but required in type 'BankBalances'.
2025-06-10T01:43:37.014903017Z src/services/xero/xero-service.ts(462,9): error TS2322: Type 'RepeatingBill[]' is not assignable to type 'RepeatingBillData[]'.
2025-06-10T01:43:37.014907957Z   Type 'RepeatingBill' is missing the following properties from type 'RepeatingBillData': contact, lineAmount, totalAmount, period, and 2 more.
2025-06-10T01:43:37.014910927Z src/utils/backend-logger.ts(277,7): error TS2353: Object literal may only specify known properties, and 'method' does not exist in type 'LogContext'.
2025-06-10T01:43:37.014919237Z src/utils/backend-logger.ts(285,5): error TS2322: Type '(...args: any[]) => void' is not assignable to type '{ (cb?: () => void): Response<any, Record<string, any>>; (chunk: any, cb?: () => void): Response<any, Record<string, any>>; (chunk: any, encoding: BufferEncoding, cb?: () => void): Response<...>; }'.
2025-06-10T01:43:37.014923077Z   Type 'void' is not assignable to type 'Response<any, Record<string, any>>'.
2025-06-10T01:43:37.014948108Z src/utils/deal-tracking.ts(302,12): error TS2678: Type '"Harvest"' is not comparable to type 'DataSource'.
2025-06-10T01:43:37.014951678Z src/utils/deal-tracking.ts(303,12): error TS2678: Type '"Xero"' is not comparable to type 'DataSource'.
2025-06-10T01:43:37.014953918Z src/utils/deal-tracking.ts(307,12): error TS2678: Type '"User"' is not comparable to type 'DataSource'.
2025-06-10T01:43:37.014959809Z src/utils/deal-tracking.ts(313,12): error TS2678: Type '"Import"' is not comparable to type 'DataSource'.
2025-06-10T01:43:37.014964838Z src/utils/deal-tracking.ts(316,12): error TS2678: Type '"API"' is not comparable to type 'DataSource'.
2025-06-10T01:43:37.015002979Z src/utils/deal-tracking.ts(508,49): error TS2367: This comparison appears to be unintentional because the types '"HubSpot" | "Manual" | "Estimate"' and '"System"' have no overlap.
2025-06-10T01:43:37.01501463Z src/utils/deal-tracking.ts(513,48): error TS2367: This comparison appears to be unintentional because the types '"HubSpot" | "Manual" | "Estimate"' and '"System"' have no overlap.
2025-06-10T01:43:37.01501766Z src/utils/email.ts(74,20): error TS2339: Property 'message' does not exist on type 'unknown'.
2025-06-10T01:43:37.01502376Z src/utils/estimate-deal-sync.ts(138,34): error TS2339: Property 'updateDraftEstimate' does not exist on type 'EstimateDraftsRepository'.
2025-06-10T01:43:37.01502676Z src/utils/frontend-logger.ts(467,5): error TS2686: 'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.
2025-06-10T01:43:37.015063171Z src/utils/frontend-logger.ts(476,14): error TS2686: 'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.
2025-06-10T01:43:37.015067151Z src/utils/logger.ts(213,9): error TS2698: Spread types may only be created from object types.
2025-06-10T01:43:37.015069451Z src/utils/logger.ts(243,7): error TS2322: Type 'unknown' is not assignable to type 'string'.
2025-06-10T01:43:37.015071611Z src/utils/logger.ts(253,9): error TS2322: Type 'unknown' is not assignable to type 'string'.
2025-06-10T01:43:37.015077111Z src/utils/logging-migration.ts(64,52): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015080091Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015085041Z src/utils/logging-migration.ts(66,51): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015087932Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015100652Z src/utils/logging-migration.ts(83,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015103332Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015129933Z src/utils/logging-migration.ts(85,43): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015133373Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015135893Z src/utils/logging-migration.ts(101,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015138403Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015141093Z src/utils/logging-migration.ts(103,43): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015143703Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015149183Z src/utils/logging-migration.ts(119,45): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015151783Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015154123Z src/utils/logging-migration.ts(121,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015156433Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015161583Z src/utils/logging-migration.ts(137,45): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015164113Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015168923Z src/utils/logging-migration.ts(139,44): error TS2345: Argument of type 'ConsoleArgument' is not assignable to parameter of type 'LogContext'.
2025-06-10T01:43:37.015178244Z   Type 'string' has no properties in common with type 'LogContext'.
2025-06-10T01:43:37.015184094Z src/utils/logging-migration.ts(173,9): error TS2322: Type 'ServiceName' is not assignable to type '"xero" | "harvest" | "hubspot"'.
2025-06-10T01:43:37.015186694Z   Type '"unknown"' is not assignable to type '"xero" | "harvest" | "hubspot"'.
2025-06-10T01:43:37.015221025Z src/utils/logging-migration.ts(217,104): error TS2345: Argument of type 'LogMetadata' is not assignable to parameter of type '{ field: string; oldValue: any; newValue: any; }[]'.
2025-06-10T01:43:37.015224265Z   Type 'LogMetadata' is missing the following properties from type '{ field: string; oldValue: any; newValue: any; }[]': length, pop, push, concat, and 29 more.
2025-06-10T01:43:37.015226745Z src/utils/logging-setup.ts(221,11): error TS2345: Argument of type '(req: Request, res: Response, next: Function) => void' is not assignable to parameter of type 'ExpressMiddleware | ExpressErrorHandler'.
2025-06-10T01:43:37.015229535Z   Type '(req: Request, res: Response, next: Function) => void' is not assignable to type 'ExpressMiddleware'.
2025-06-10T01:43:37.015232195Z     Types of parameters 'req' and 'req' are incompatible.
2025-06-10T01:43:37.015234585Z       Type 'ExpressRequest' is missing the following properties from type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>': cookies, signedCookies, header, accepts, and 95 more.
2025-06-10T01:43:37.076417429Z TypeScript compilation failed, will use ts-node fallback
2025-06-10T01:43:38.599088922Z ==> Uploading build...
2025-06-10T01:43:52.745291827Z ==> Uploaded in 5.6s. Compression took 8.6s
2025-06-10T01:43:52.778891697Z ==> Build successful 🎉
2025-06-10T01:43:55.891755404Z ==> Deploying...
2025-06-10T01:45:19.346667771Z ==> Running 'npm start'
2025-06-10T01:45:20.239092176Z 
2025-06-10T01:45:20.239340081Z > onbord-financial-dashboard-preview@0.1.0 start
2025-06-10T01:45:20.239344931Z > node server.js
2025-06-10T01:45:20.239347701Z 
2025-06-10T01:45:20.366644212Z 🚀 Starting Upstream Financial Dashboard
2025-06-10T01:45:20.366668123Z Environment: production
2025-06-10T01:45:20.366671383Z Port: 10000
2025-06-10T01:45:20.366674143Z Found server file: /opt/render/project/src/dist/api/server.js
2025-06-10T01:45:22.449659824Z Production environment detected, using environment variable for callback URL
2025-06-10T01:45:22.449679884Z Xero callback URL configured as: https://upstream-preview.onbord.au/api/xero/callback
2025-06-10T01:45:22.450940528Z Initializing XeroClient with config: {
2025-06-10T01:45:22.450951248Z   clientId: '72609...',
2025-06-10T01:45:22.450955049Z   redirectUri: 'https://upstream-preview.onbord.au/api/xero/callback',
2025-06-10T01:45:22.450957849Z   scopes: [
2025-06-10T01:45:22.450961169Z     'openid',
2025-06-10T01:45:22.450964249Z     'profile',
2025-06-10T01:45:22.450966849Z     'email',
2025-06-10T01:45:22.450969909Z     'accounting.transactions',
2025-06-10T01:45:22.450972459Z     'accounting.reports.read',
2025-06-10T01:45:22.450975379Z     'accounting.settings',
2025-06-10T01:45:22.450978029Z     'offline_access',
2025-06-10T01:45:22.450980929Z     'payroll.employees',
2025-06-10T01:45:22.450984049Z     'payroll.payruns'
2025-06-10T01:45:22.450986789Z   ]
2025-06-10T01:45:22.450989709Z }
2025-06-10T01:45:22.451339476Z Creating Xero client with config: {
2025-06-10T01:45:22.451346346Z   clientId: '72609...',
2025-06-10T01:45:22.451350096Z   clientSecret: 'present',
2025-06-10T01:45:22.451353086Z   redirectUri: 'https://upstream-preview.onbord.au/api/xero/callback',
2025-06-10T01:45:22.451355896Z   scopes: [
2025-06-10T01:45:22.451358466Z     'openid',
2025-06-10T01:45:22.451360886Z     'profile',
2025-06-10T01:45:22.451363756Z     'email',
2025-06-10T01:45:22.451366586Z     'accounting.transactions',
2025-06-10T01:45:22.451369486Z     'accounting.reports.read',
2025-06-10T01:45:22.451372177Z     'accounting.settings',
2025-06-10T01:45:22.451374946Z     'offline_access',
2025-06-10T01:45:22.451377506Z     'payroll.employees',
2025-06-10T01:45:22.451379997Z     'payroll.payruns'
2025-06-10T01:45:22.451426407Z   ]
2025-06-10T01:45:22.451431407Z }
2025-06-10T01:45:22.452490988Z ✓ Xero API method "getReportBalanceSheet" available
2025-06-10T01:45:22.45264199Z ✓ Xero API method "getReportBankSummary" available
2025-06-10T01:45:22.452830364Z ✓ Xero API method "getRepeatingInvoices" available
2025-06-10T01:45:22.452835564Z Found Australian Payroll API
2025-06-10T01:45:22.452918576Z Xero SDK initialized with: {
2025-06-10T01:45:22.452925856Z   clientId: '72609...',
2025-06-10T01:45:22.452929926Z   redirectUri: 'https://upstream-preview.onbord.au/api/xero/callback',
2025-06-10T01:45:22.452932786Z   scopes: 9,
2025-06-10T01:45:22.452935566Z   hasPayrollApi: true
2025-06-10T01:45:22.452938466Z }
2025-06-10T01:45:22.453191681Z Initializing HarvestClient...
2025-06-10T01:45:22.453329034Z Harvest credentials: Token=Present, Account ID=Present
2025-06-10T01:45:22.45788613Z Harvest settings: Running in production mode, using data directory: /data
2025-06-10T01:45:22.459012902Z Harvest settings: Successfully verified write access to /data
2025-06-10T01:45:22.555097429Z Running in production mode, using data directory: /data
2025-06-10T01:45:22.55511514Z Using database path: /data/upstream.db
2025-06-10T01:45:22.555366764Z Successfully verified write access to /data
2025-06-10T01:45:22.579864361Z Successfully connected to SQLite database with timeouts configured
2025-06-10T01:45:22.58301941Z LeadsRepository: Using unified company table
2025-06-10T01:45:22.663553792Z KnowledgeGraphRepository: Database instance: available
2025-06-10T01:45:22.663790287Z KnowledgeGraphRepository: All repositories initialized successfully
2025-06-10T01:45:22.748024079Z Proxy trust enabled for production (first proxy only)
2025-06-10T01:45:22.750258211Z Cookie domain configured as: default (current domain)
2025-06-10T01:45:22.750274812Z Session config: NODE_ENV=production, API_PORT=3000, isLocalDevelopment=false, isPreviewDeployment=true
2025-06-10T01:45:22.750422185Z Using session configuration with cookie settings: {
2025-06-10T01:45:22.750426665Z   secure: true,
2025-06-10T01:45:22.750429605Z   maxAge: *********,
2025-06-10T01:45:22.750431795Z   httpOnly: true,
2025-06-10T01:45:22.750433985Z   sameSite: 'none',
2025-06-10T01:45:22.750436415Z   domain: '(default)',
2025-06-10T01:45:22.750438805Z   path: '/'
2025-06-10T01:45:22.750441585Z }
2025-06-10T01:45:22.750444745Z Production environment detected for cookies
2025-06-10T01:45:22.750450665Z Using Render hostname for cookies: upstream-preview.onrender.com
2025-06-10T01:45:22.750453075Z Preview deployment detected, using special cookie settings
2025-06-10T01:45:22.750458325Z Cookie domain: (default - using browser default)
2025-06-10T01:45:22.750467026Z Cookie sameSite: none
2025-06-10T01:45:22.750551937Z Cookie secure: true
2025-06-10T01:45:22.762331331Z Session middleware configured with RedisStore: {
2025-06-10T01:45:22.762343692Z   resave: false,
2025-06-10T01:45:22.762347181Z   saveUninitialized: false,
2025-06-10T01:45:22.762350132Z   cookieSecure: true,
2025-06-10T01:45:22.762352202Z   cookieMaxAge: '7 days',
2025-06-10T01:45:22.762354192Z   sameSite: 'none',
2025-06-10T01:45:22.762356322Z   domain: 'default (browser determined)'
2025-06-10T01:45:22.762358512Z }
2025-06-10T01:45:22.762360532Z PREVIEW DEPLOYMENT DETECTED
2025-06-10T01:45:22.762874491Z Initializing database...
2025-06-10T01:45:22.764038164Z Current database schema version: 1
2025-06-10T01:45:22.764182616Z Checking for missing tables...
2025-06-10T01:45:22.764447141Z Database initialization complete
2025-06-10T01:45:22.765250287Z Testing activity logging system...
2025-06-10T01:45:22.772843361Z Initializing cashflow snapshot job...
2025-06-10T01:45:22.772911773Z Initializing cashflow snapshot job...
2025-06-10T01:45:22.847047232Z Cashflow snapshot job scheduled to run at 1:00 AM daily
2025-06-10T01:45:22.847059253Z Cashflow snapshot job initialized and started
2025-06-10T01:45:22.847063863Z Environment configuration:
2025-06-10T01:45:22.847080773Z - Xero integration: Configured
2025-06-10T01:45:22.847693185Z - Harvest integration: Configured
2025-06-10T01:45:22.847706275Z Serving frontend static files from: /opt/render/project/src/dist (Render deployment)
2025-06-10T01:45:22.849966078Z API server running on port 3000
2025-06-10T01:45:22.849982218Z Socket.IO server running on port 3000
2025-06-10T01:45:22.85007734Z Xero callback URL configured as: https://upstream-preview.onbord.au/api/xero/callback
2025-06-10T01:45:22.851969206Z Activity created: system_started - Application server started
2025-06-10T01:45:22.852341333Z ✓ Activity logging test successful
2025-06-10T01:45:22.855241418Z Connected to Redis successfully.
2025-06-10T01:45:22.951341456Z [DEBUG PRE-SESSION] Request to /. Cookies: None
2025-06-10T01:45:22.952935157Z [DEBUG POST-SESSION] Request to /. SessionID: d9SMyXW0HZCDSjl_0jovrtOxqZIjXjMA. Has tokenSet: false
2025-06-10T01:45:23.656143122Z {"level":2,"message":"HubSpot MCP Session Manager initialized","timestamp":"2025-06-10T01:45:22.658Z","context":{"sessionId":"***MASKED***","requestId":"529e1ba9-cb01-49ba-85bb-e1b99307fd00"},"environment":"production","source":"backend"}
2025-06-10T01:45:32.712551162Z ==> Your service is live 🎉