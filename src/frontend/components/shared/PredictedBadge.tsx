import React from "react"; import { Badge } from './Badge'; import type { BadgeVariantProps } from '../../utils/component-variants'; interface PredictedBadgeProps extends Partial<BadgeVariantProps> { className?: string; quarterInfo?: { quarterName: string; quarterLabel: string; percentComplete: number; }; } /** * Lightning bolt icon component for predicted badges */ const LightningIcon: React.FC<{ className?: string }> = ({ className = "w-3 h-3" }) => ( <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> ); /** * Predicted badge component with enhanced styling and accessibility. * * Features: * - Uses design system predicted variant * - Consistent icon and spacing * - Enhanced accessibility with proper ARIA attributes * - Optional quarter information display * - Semantic amber color scheme * * @example * ```tsx * <PredictedBadge * quarterInfo={{ * quarterName: "Q2", * quarterLabel: "Q2 2024", * percentComplete: 75 * }} * /> * ``` */ const PredictedBadge: React.FC<PredictedBadgeProps> = ({ className, quarterInfo, size = 'default', ...props }) => { // Format quarter text if provided const quarterText = quarterInfo ? `${Math.round(quarterInfo.percentComplete)}% | ${quarterInfo.quarterLabel}` : ""; const badgeText = `Predicted${quarterInfo ? ` | ${quarterText}` : ""}`; return ( <Badge variant="predicted" size={size} withIcon={true} icon={<LightningIcon />} title="Using predicted amount (pro-rated for full quarter)" aria-label={`Predicted amount${quarterInfo ? ` - ${quarterText}` : ""}`} className={className} {...props} > {badgeText} </Badge> ); }; export default PredictedBadge;