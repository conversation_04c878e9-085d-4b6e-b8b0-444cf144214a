import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../src/api/server';
import { Database } from 'better-sqlite3';
import { CashflowSnapshotRepository } from '../../src/api/repositories/cashflow-snapshot-repository';
import { XeroService } from '../../src/services/xero/xero-service';
import { HarvestInvoiceService } from '../../src/services/harvest/invoice-service';
import type { DailyCashflow } from '../../src/types/financial';

// Mock external services
jest.mock('../../src/services/xero/xero-service');
jest.mock('../../src/services/harvest/invoice-service');
jest.mock('../../src/api/clients/xero-api-client');
jest.mock('../../src/api/clients/harvest-api-client');

describe('Cashflow Integration Tests', () => {
  let app: Express;
  let db: Database;
  let repository: CashflowSnapshotRepository;
  let mockXeroService: jest.Mocked<XeroService>;
  let mockHarvestService: jest.Mocked<HarvestInvoiceService>;

  beforeAll(async () => {
    // Create test database
    db = new (require('better-sqlite3'))(':memory:');
    
    // Run migrations
    const migrations = [
      require('fs').readFileSync('./migrations/001_initial_schema.sql', 'utf8'),
      require('fs').readFileSync('./migrations/002_update_activity_feed.sql', 'utf8'),
      require('fs').readFileSync('./migrations/003_fix_schema_mismatches.sql', 'utf8')
    ];
    
    migrations.forEach(migration => db.exec(migration));

    // Initialize repository
    repository = new CashflowSnapshotRepository(db);

    // Create app
    app = createApp();

    // Setup mocks
    mockXeroService = new XeroService(null as any) as jest.Mocked<XeroService>;
    mockHarvestService = new HarvestInvoiceService(null as any, null as any) as jest.Mocked<HarvestInvoiceService>;
  });

  afterAll(() => {
    db.close();
  });

  beforeEach(() => {
    // Clear data between tests
    db.exec('DELETE FROM cashflow_snapshot');
    jest.clearAllMocks();
  });

  describe('GET /api/cashflow/daily', () => {
    const mockXeroInvoices = [
      {
        InvoiceID: 'inv-1',
        Type: 'ACCREC',
        Total: 5000,
        AmountDue: 0,
        Status: 'PAID',
        Date: '2024-01-15',
        DueDate: '2024-02-15',
        Contact: { Name: 'Client A' }
      }
    ];

    const mockXeroBills = [
      {
        InvoiceID: 'bill-1',
        Type: 'ACCPAY',
        Total: 2000,
        AmountDue: 2000,
        Status: 'AUTHORISED',
        Date: '2024-01-20',
        DueDate: '2024-02-20',
        Contact: { Name: 'Supplier A' }
      }
    ];

    const mockHarvestInvoices = [
      {
        id: 1,
        amount: 10000,
        due_amount: 10000,
        state: 'open',
        issue_date: '2024-01-25',
        due_date: '2024-02-25',
        client: { name: 'Client B' }
      }
    ];

    beforeEach(() => {
      mockXeroService.getInvoices.mockResolvedValue(mockXeroInvoices);
      mockXeroService.getBills = jest.fn().mockResolvedValue(mockXeroBills);
      mockHarvestService.getInvoices.mockResolvedValue(mockHarvestInvoices);
    });

    it('should return daily cashflow projection', async () => {
      const response = await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      const cashflow: DailyCashflow = response.body;

      expect(cashflow).toHaveProperty('startDate', '2024-01-01');
      expect(cashflow).toHaveProperty('endDate', '2024-02-28');
      expect(cashflow).toHaveProperty('dailyBalances');
      expect(cashflow).toHaveProperty('transactions');
      expect(cashflow).toHaveProperty('totalIncome');
      expect(cashflow).toHaveProperty('totalExpenses');
      expect(cashflow).toHaveProperty('scenarios');

      // Verify transactions are included
      expect(cashflow.transactions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'income',
            amount: 5000,
            date: '2024-01-15'
          }),
          expect.objectContaining({
            type: 'expense',
            amount: -2000,
            date: '2024-02-20'
          })
        ])
      );
    });

    it('should apply projection settings', async () => {
      const response = await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28',
          enableProjectedInvoices: true,
          defaultPaymentTerms: 30
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      const cashflow: DailyCashflow = response.body;

      // Should include projected invoices
      expect(cashflow.projectedInvoices).toBeDefined();
      expect(cashflow.projectedInvoices.length).toBeGreaterThan(0);
    });

    it('should handle date validation', async () => {
      await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-02-01',
          endDate: '2024-01-01' // End before start
        })
        .set('Authorization', 'Bearer test-token')
        .expect(400);
    });

    it('should handle missing authentication', async () => {
      await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .expect(401);
    });
  });

  describe('POST /api/cashflow/snapshots', () => {
    it('should create a cashflow snapshot', async () => {
      mockXeroService.getInvoices.mockResolvedValue([]);
      mockXeroService.getBills = jest.fn().mockResolvedValue([]);
      mockHarvestService.getInvoices.mockResolvedValue([]);

      const response = await request(app)
        .post('/api/cashflow/snapshots')
        .send({
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('date');
      expect(response.body).toHaveProperty('startingBalance');
      expect(response.body).toHaveProperty('endingBalance');
      expect(response.body).toHaveProperty('totalIncome');
      expect(response.body).toHaveProperty('totalExpenses');
      expect(response.body).toHaveProperty('netCashflow');

      // Verify snapshot was saved to database
      const savedSnapshot = await repository.getById(response.body.id);
      expect(savedSnapshot).toBeTruthy();
    });

    it('should prevent duplicate snapshots for same date', async () => {
      mockXeroService.getInvoices.mockResolvedValue([]);
      mockXeroService.getBills = jest.fn().mockResolvedValue([]);
      mockHarvestService.getInvoices.mockResolvedValue([]);

      // Create first snapshot
      await request(app)
        .post('/api/cashflow/snapshots')
        .send({
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(201);

      // Attempt to create duplicate
      await request(app)
        .post('/api/cashflow/snapshots')
        .send({
          startDate: '2024-01-01',
          endDate: '2024-01-31'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(409);
    });
  });

  describe('GET /api/cashflow/snapshots', () => {
    beforeEach(async () => {
      // Create test snapshots
      await repository.create({
        date: '2024-01-31',
        startingBalance: 10000,
        endingBalance: 12000,
        totalIncome: 5000,
        totalExpenses: 3000,
        netCashflow: 2000,
        metadata: {
          transactionCount: 10,
          projectedInvoiceCount: 2,
          projectedBudgetCount: 3
        }
      });

      await repository.create({
        date: '2024-02-29',
        startingBalance: 12000,
        endingBalance: 15000,
        totalIncome: 6000,
        totalExpenses: 3000,
        netCashflow: 3000,
        metadata: {
          transactionCount: 15,
          projectedInvoiceCount: 3,
          projectedBudgetCount: 4
        }
      });
    });

    it('should return all snapshots', async () => {
      const response = await request(app)
        .get('/api/cashflow/snapshots')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body).toHaveLength(2);
      expect(response.body[0]).toHaveProperty('date', '2024-01-31');
      expect(response.body[1]).toHaveProperty('date', '2024-02-29');
    });

    it('should filter snapshots by date range', async () => {
      const response = await request(app)
        .get('/api/cashflow/snapshots')
        .query({
          startDate: '2024-02-01',
          endDate: '2024-02-29'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toHaveProperty('date', '2024-02-29');
    });
  });

  describe('GET /api/cashflow/transactions', () => {
    beforeEach(() => {
      mockXeroService.getInvoices.mockResolvedValue([
        {
          InvoiceID: 'inv-1',
          Type: 'ACCREC',
          Total: 5000,
          Status: 'AUTHORISED',
          Date: '2024-01-15',
          DueDate: '2024-02-15',
          Contact: { Name: 'Client A' }
        }
      ]);

      mockXeroService.getBills = jest.fn().mockResolvedValue([
        {
          InvoiceID: 'bill-1',
          Type: 'ACCPAY',
          Total: 2000,
          Status: 'AUTHORISED',
          Date: '2024-01-20',
          DueDate: '2024-02-20',
          Contact: { Name: 'Supplier A' }
        }
      ]);
    });

    it('should return filtered transactions', async () => {
      const response = await request(app)
        .get('/api/cashflow/transactions')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28',
          type: 'income'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body.transactions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'income',
            amount: 5000
          })
        ])
      );

      // Should not include expenses
      expect(response.body.transactions).not.toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'expense'
          })
        ])
      );
    });

    it('should support pagination', async () => {
      const response = await request(app)
        .get('/api/cashflow/transactions')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28',
          page: 1,
          limit: 10
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(response.body).toHaveProperty('transactions');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('limit', 10);
    });
  });

  describe('Error Handling', () => {
    it('should handle external service failures gracefully', async () => {
      mockXeroService.getInvoices.mockRejectedValue(new Error('Xero API error'));

      const response = await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Failed to fetch cashflow data');
    });

    it('should handle rate limiting', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      (rateLimitError as any).statusCode = 429;
      mockXeroService.getInvoices.mockRejectedValue(rateLimitError);

      const response = await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(429);

      expect(response.body).toHaveProperty('error', 'Rate limit exceeded');
    });
  });

  describe('Caching', () => {
    it('should cache cashflow responses', async () => {
      // First request
      await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(mockXeroService.getInvoices).toHaveBeenCalledTimes(1);

      // Second request - should use cache
      await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      // Should not call service again
      expect(mockXeroService.getInvoices).toHaveBeenCalledTimes(1);
    });

    it('should invalidate cache on data changes', async () => {
      // Initial request
      await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      // Simulate data change
      await request(app)
        .post('/api/cashflow/invalidate-cache')
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      // Next request should fetch fresh data
      await request(app)
        .get('/api/cashflow/daily')
        .query({
          startDate: '2024-01-01',
          endDate: '2024-02-28'
        })
        .set('Authorization', 'Bearer test-token')
        .expect(200);

      expect(mockXeroService.getInvoices).toHaveBeenCalledTimes(2);
    });
  });
});