#!/usr/bin/env node
/**
 * Test Soft Delete Functionality
 * 
 * This script tests that our soft delete implementation works correctly
 * across repositories and the database.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Import repositories
const { CompanyRepository } = require('../src/api/repositories/company-repository');
const { ContactRepository } = require('../src/api/repositories/contact-repository');
const { DealRepository } = require('../src/api/repositories/deal-repository');
const { ExpensesRepository } = require('../src/api/repositories/expenses-repository');

// Test database path
const dbPath = path.join(__dirname, '..', 'data', 'test-soft-delete.db');

// Create data directory if it doesn't exist
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Delete existing test database
if (fs.existsSync(dbPath)) {
  fs.unlinkSync(dbPath);
}

console.log('=== Testing Soft Delete Functionality ===\n');

// Initialize database with unified schema
console.log('1. Initializing database with unified schema...');
const db = new BetterSqlite3(dbPath);

try {
  // Read and execute unified schema
  const schemaPath = path.join(__dirname, '..', 'migrations', '000_unified_schema.sql');
  const schema = fs.readFileSync(schemaPath, 'utf8');
  db.exec(schema);
  console.log('✅ Database initialized successfully\n');

  // Initialize repositories
  const companyRepo = new CompanyRepository();
  const contactRepo = new ContactRepository();
  const dealRepo = new DealRepository();
  const expenseRepo = new ExpensesRepository();

  // Override their db connections to use our test database
  companyRepo.db = db;
  contactRepo.db = db;
  dealRepo.db = db;
  expenseRepo.db = db;

  console.log('2. Testing Company Repository...');
  
  // Create test company
  const testCompany = companyRepo.createCompany({
    name: 'Test Company',
    industry: 'Technology',
    website: 'https://test.com',
    createdBy: 'test-user'
  }, 'Manual');
  
  console.log(`   ✅ Created company: ${testCompany.name} (ID: ${testCompany.id})`);
  
  // Test getAllCompanies
  let allCompanies = companyRepo.getAllCompanies();
  console.log(`   ✅ getAllCompanies() returned ${allCompanies.length} companies`);
  
  // Test soft delete
  const deleteResult = companyRepo.deleteCompany(testCompany.id, true);
  console.log(`   ✅ Soft deleted company (affected rows: ${deleteResult})`);
  
  // Test that soft-deleted company doesn't appear in normal queries
  allCompanies = companyRepo.getAllCompanies();
  console.log(`   ✅ getAllCompanies() after soft delete: ${allCompanies.length} companies`);
  
  // Test that we can get soft-deleted companies when requested
  allCompanies = companyRepo.getAllCompanies({}, { includeDeleted: true });
  console.log(`   ✅ getAllCompanies({ includeDeleted: true }): ${allCompanies.length} companies`);
  
  // Verify the company is actually soft-deleted
  const deletedCompany = db.prepare('SELECT * FROM company WHERE id = ?').get(testCompany.id);
  console.log(`   ✅ Company deleted_at: ${deletedCompany.deleted_at ? 'SET' : 'NULL'}\n`);
  
  console.log('3. Testing Contact Repository...');
  
  // Create test contact
  const testContact = contactRepo.createContact({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    createdBy: 'test-user'
  });
  
  console.log(`   ✅ Created contact: ${testContact.firstName} ${testContact.lastName}`);
  
  // Test soft delete
  contactRepo.deleteContact(testContact.id);
  const activeContacts = contactRepo.getAllContacts();
  console.log(`   ✅ Active contacts after soft delete: ${activeContacts.length}`);
  
  console.log('\n4. Testing Cascade Behavior...');
  
  // Create company with related data
  const parentCompany = companyRepo.createCompany({
    name: 'Parent Company',
    createdBy: 'test-user'
  }, 'Manual');
  
  // Create deal linked to company
  const testDeal = dealRepo.createDeal({
    name: 'Test Deal',
    stage: 'Identified',
    companyId: parentCompany.id,
    value: 50000,
    createdBy: 'test-user'
  });
  
  console.log(`   ✅ Created deal linked to company`);
  
  // Delete the company
  companyRepo.deleteCompany(parentCompany.id, true);
  
  // Check if deal still exists but company_id is NULL (SET NULL behavior)
  const dealAfterDelete = db.prepare('SELECT * FROM deal WHERE id = ?').get(testDeal.id);
  console.log(`   ✅ Deal still exists: ${dealAfterDelete ? 'YES' : 'NO'}`);
  console.log(`   ✅ Deal company_id after parent delete: ${dealAfterDelete.company_id || 'NULL'}`);
  
  console.log('\n5. Testing Expense Repository...');
  
  // Create test expense
  const testExpense = expenseRepo.create({
    name: 'Office Supplies',
    amount: 250.00,
    type: 'Operating',
    date: new Date().toISOString().split('T')[0],
    createdBy: 'test-user'
  });
  
  console.log(`   ✅ Created expense: ${testExpense.name}`);
  
  // Test soft delete
  expenseRepo.delete(testExpense.id);
  const activeExpenses = expenseRepo.getAll();
  console.log(`   ✅ Active expenses after soft delete: ${activeExpenses.length}`);
  
  console.log('\n=== Summary ===');
  
  // Count soft-deleted records across tables
  const tables = ['company', 'contact', 'deal', 'expense'];
  for (const table of tables) {
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${table} WHERE deleted_at IS NOT NULL`).get();
    console.log(`   ${table}: ${count.count} soft-deleted records`);
  }
  
  console.log('\n✅ All soft delete tests passed!');
  
} catch (error) {
  console.error('\n❌ Test failed:', error.message);
  console.error(error);
  process.exit(1);
} finally {
  db.close();
  
  // Clean up test database
  if (fs.existsSync(dbPath)) {
    fs.unlinkSync(dbPath);
  }
}