import { HarvestProjectBudgetService } from '../../../../src/services/harvest/project-budget-service';
import { HarvestApiClient } from '../../../../src/api/clients/harvest-api-client';
import type { HarvestProject, HarvestProjectBudgetReport } from '../../../../src/types/api';

jest.mock('../../../../src/api/clients/harvest-api-client');

describe('HarvestProjectBudgetService', () => {
  let service: HarvestProjectBudgetService;
  let mockApiClient: jest.Mocked<HarvestApiClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockApiClient = new HarvestApiClient('mock-token', '12345') as jest.Mocked<HarvestApiClient>;
    service = new HarvestProjectBudgetService(mockApiClient);
  });

  describe('getProjectBudgets', () => {
    const mockProjects: HarvestProject[] = [
      {
        id: 1,
        name: 'Project Alpha',
        code: 'ALPHA',
        is_active: true,
        is_billable: true,
        is_fixed_fee: true,
        bill_by: 'project',
        budget: 50000,
        budget_by: 'project',
        budget_is_monthly: false,
        notify_when_over_budget: true,
        over_budget_notification_percentage: 80,
        show_budget_to_all: true,
        cost_budget: 40000,
        cost_budget_include_expenses: true,
        hourly_rate: 150,
        fee: 50000,
        notes: 'Main project',
        starts_on: '2024-01-01',
        ends_on: '2024-12-31',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T10:00:00Z',
        client: {
          id: 101,
          name: 'Client A',
          currency: 'USD'
        }
      },
      {
        id: 2,
        name: 'Project Beta',
        code: 'BETA',
        is_active: true,
        is_billable: true,
        is_fixed_fee: false,
        bill_by: 'tasks',
        budget: 100000,
        budget_by: 'person',
        budget_is_monthly: true,
        notify_when_over_budget: true,
        over_budget_notification_percentage: 90,
        show_budget_to_all: false,
        cost_budget: null,
        cost_budget_include_expenses: false,
        hourly_rate: null,
        fee: null,
        notes: '',
        starts_on: '2024-02-01',
        ends_on: null,
        created_at: '2024-02-01T00:00:00Z',
        updated_at: '2024-02-01T00:00:00Z',
        client: {
          id: 102,
          name: 'Client B',
          currency: 'USD'
        }
      }
    ];

    it('should fetch all active project budgets', async () => {
      mockApiClient.get.mockResolvedValue({
        projects: mockProjects,
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      });

      const result = await service.getProjectBudgets();

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(expect.objectContaining({
        id: 1,
        name: 'Project Alpha',
        budget: 50000,
        is_fixed_fee: true
      }));
      expect(mockApiClient.get).toHaveBeenCalledWith('/projects', {
        is_active: true,
        per_page: 100,
        page: 1
      });
    });

    it('should include inactive projects when requested', async () => {
      mockApiClient.get.mockResolvedValue({
        projects: mockProjects,
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      });

      await service.getProjectBudgets({ includeInactive: true });

      expect(mockApiClient.get).toHaveBeenCalledWith('/projects', {
        per_page: 100,
        page: 1
      });
    });

    it('should filter by client', async () => {
      mockApiClient.get.mockResolvedValue({
        projects: [mockProjects[0]],
        per_page: 100,
        total_pages: 1,
        total_entries: 1,
        next_page: null,
        previous_page: null,
        page: 1
      });

      const result = await service.getProjectBudgets({ clientId: 101 });

      expect(result).toHaveLength(1);
      expect(result[0].client.id).toBe(101);
      expect(mockApiClient.get).toHaveBeenCalledWith('/projects', {
        is_active: true,
        client_id: 101,
        per_page: 100,
        page: 1
      });
    });

    it('should handle pagination', async () => {
      mockApiClient.get
        .mockResolvedValueOnce({
          projects: [mockProjects[0]],
          per_page: 1,
          total_pages: 2,
          total_entries: 2,
          next_page: 2,
          previous_page: null,
          page: 1
        })
        .mockResolvedValueOnce({
          projects: [mockProjects[1]],
          per_page: 1,
          total_pages: 2,
          total_entries: 2,
          next_page: null,
          previous_page: 1,
          page: 2
        });

      const result = await service.getProjectBudgets();

      expect(result).toHaveLength(2);
      expect(mockApiClient.get).toHaveBeenCalledTimes(2);
    });
  });

  describe('getProjectBudgetReport', () => {
    const mockBudgetReport: HarvestProjectBudgetReport = {
      results: [
        {
          project_id: 1,
          project_name: 'Project Alpha',
          client_id: 101,
          client_name: 'Client A',
          budget_is_monthly: false,
          budget_by: 'project',
          is_active: true,
          budget: 50000,
          budget_spent: 30000,
          budget_remaining: 20000,
          over_budget: false,
          percentage_complete: 60
        },
        {
          project_id: 2,
          project_name: 'Project Beta',
          client_id: 102,
          client_name: 'Client B',
          budget_is_monthly: true,
          budget_by: 'person',
          is_active: true,
          budget: 10000,
          budget_spent: 12000,
          budget_remaining: -2000,
          over_budget: true,
          percentage_complete: 120
        }
      ],
      per_page: 100,
      total_pages: 1,
      total_entries: 2,
      next_page: null,
      previous_page: null,
      page: 1
    };

    it('should fetch project budget report', async () => {
      mockApiClient.get.mockResolvedValue(mockBudgetReport);

      const result = await service.getProjectBudgetReport();

      expect(result).toEqual(mockBudgetReport.results);
      expect(mockApiClient.get).toHaveBeenCalledWith('/reports/project_budget', {
        page: 1,
        per_page: 100
      });
    });

    it('should filter by active projects only', async () => {
      mockApiClient.get.mockResolvedValue({
        ...mockBudgetReport,
        results: [mockBudgetReport.results[0]]
      });

      const result = await service.getProjectBudgetReport({ activeOnly: true });

      expect(result).toHaveLength(1);
      expect(mockApiClient.get).toHaveBeenCalledWith('/reports/project_budget', {
        is_active: true,
        page: 1,
        per_page: 100
      });
    });

    it('should handle pagination for large reports', async () => {
      const page1 = {
        results: [mockBudgetReport.results[0]],
        per_page: 1,
        total_pages: 2,
        total_entries: 2,
        next_page: 2,
        previous_page: null,
        page: 1
      };

      const page2 = {
        results: [mockBudgetReport.results[1]],
        per_page: 1,
        total_pages: 2,
        total_entries: 2,
        next_page: null,
        previous_page: 1,
        page: 2
      };

      mockApiClient.get
        .mockResolvedValueOnce(page1)
        .mockResolvedValueOnce(page2);

      const result = await service.getProjectBudgetReport();

      expect(result).toHaveLength(2);
      expect(mockApiClient.get).toHaveBeenCalledTimes(2);
    });
  });

  describe('getProjectsWithRemainingBudget', () => {
    it('should return only projects with remaining budget', async () => {
      const budgetReport = {
        results: [
          {
            project_id: 1,
            project_name: 'Project Alpha',
            client_id: 101,
            client_name: 'Client A',
            budget: 50000,
            budget_spent: 30000,
            budget_remaining: 20000,
            over_budget: false
          },
          {
            project_id: 2,
            project_name: 'Project Beta',
            client_id: 102,
            client_name: 'Client B',
            budget: 10000,
            budget_spent: 12000,
            budget_remaining: -2000,
            over_budget: true
          },
          {
            project_id: 3,
            project_name: 'Project Gamma',
            client_id: 103,
            client_name: 'Client C',
            budget: 25000,
            budget_spent: 25000,
            budget_remaining: 0,
            over_budget: false
          }
        ],
        per_page: 100,
        total_pages: 1,
        total_entries: 3,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(budgetReport);

      const result = await service.getProjectsWithRemainingBudget();

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(expect.objectContaining({
        project_id: 1,
        budget_remaining: 20000
      }));
    });

    it('should apply minimum remaining budget threshold', async () => {
      const budgetReport = {
        results: [
          {
            project_id: 1,
            project_name: 'Project Alpha',
            client_id: 101,
            client_name: 'Client A',
            budget: 50000,
            budget_spent: 30000,
            budget_remaining: 20000,
            over_budget: false
          },
          {
            project_id: 2,
            project_name: 'Project Beta',
            client_id: 102,
            client_name: 'Client B',
            budget: 15000,
            budget_spent: 10000,
            budget_remaining: 5000,
            over_budget: false
          }
        ],
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(budgetReport);

      const result = await service.getProjectsWithRemainingBudget(10000);

      expect(result).toHaveLength(1);
      expect(result[0].budget_remaining).toBeGreaterThanOrEqual(10000);
    });
  });

  describe('getBudgetSummary', () => {
    it('should calculate budget summary statistics', async () => {
      const budgetReport = {
        results: [
          {
            project_id: 1,
            project_name: 'Project Alpha',
            budget: 50000,
            budget_spent: 30000,
            budget_remaining: 20000,
            over_budget: false
          },
          {
            project_id: 2,
            project_name: 'Project Beta',
            budget: 10000,
            budget_spent: 12000,
            budget_remaining: -2000,
            over_budget: true
          },
          {
            project_id: 3,
            project_name: 'Project Gamma',
            budget: 25000,
            budget_spent: 20000,
            budget_remaining: 5000,
            over_budget: false
          }
        ],
        per_page: 100,
        total_pages: 1,
        total_entries: 3,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(budgetReport);

      const result = await service.getBudgetSummary();

      expect(result).toEqual({
        totalProjects: 3,
        totalBudget: 85000,
        totalSpent: 62000,
        totalRemaining: 23000,
        projectsOverBudget: 1,
        projectsUnderBudget: 2,
        averageUtilization: 72.94, // (62000 / 85000) * 100
        projectsNearCompletion: expect.any(Number)
      });
    });

    it('should handle empty project list', async () => {
      mockApiClient.get.mockResolvedValue({
        results: [],
        per_page: 100,
        total_pages: 0,
        total_entries: 0,
        next_page: null,
        previous_page: null,
        page: 1
      });

      const result = await service.getBudgetSummary();

      expect(result).toEqual({
        totalProjects: 0,
        totalBudget: 0,
        totalSpent: 0,
        totalRemaining: 0,
        projectsOverBudget: 0,
        projectsUnderBudget: 0,
        averageUtilization: 0,
        projectsNearCompletion: 0
      });
    });
  });

  describe('getProjectBudgetAlerts', () => {
    it('should identify projects needing attention', async () => {
      const budgetReport = {
        results: [
          {
            project_id: 1,
            project_name: 'Over Budget Project',
            budget: 10000,
            budget_spent: 12000,
            budget_remaining: -2000,
            over_budget: true,
            percentage_complete: 120
          },
          {
            project_id: 2,
            project_name: 'Near Completion Project',
            budget: 20000,
            budget_spent: 18000,
            budget_remaining: 2000,
            over_budget: false,
            percentage_complete: 90
          },
          {
            project_id: 3,
            project_name: 'Healthy Project',
            budget: 30000,
            budget_spent: 15000,
            budget_remaining: 15000,
            over_budget: false,
            percentage_complete: 50
          }
        ],
        per_page: 100,
        total_pages: 1,
        total_entries: 3,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(budgetReport);

      const result = await service.getProjectBudgetAlerts();

      expect(result).toEqual({
        overBudget: [
          expect.objectContaining({
            project_id: 1,
            project_name: 'Over Budget Project',
            over_budget: true
          })
        ],
        nearCompletion: [
          expect.objectContaining({
            project_id: 2,
            project_name: 'Near Completion Project',
            percentage_complete: 90
          })
        ],
        healthy: [
          expect.objectContaining({
            project_id: 3,
            project_name: 'Healthy Project',
            percentage_complete: 50
          })
        ]
      });
    });

    it('should use custom threshold for near completion', async () => {
      const budgetReport = {
        results: [
          {
            project_id: 1,
            project_name: 'Project A',
            budget: 10000,
            budget_spent: 7000,
            budget_remaining: 3000,
            over_budget: false,
            percentage_complete: 70
          },
          {
            project_id: 2,
            project_name: 'Project B',
            budget: 10000,
            budget_spent: 7500,
            budget_remaining: 2500,
            over_budget: false,
            percentage_complete: 75
          }
        ],
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      };

      mockApiClient.get.mockResolvedValue(budgetReport);

      const result = await service.getProjectBudgetAlerts(70);

      expect(result.nearCompletion).toHaveLength(2);
      expect(result.healthy).toHaveLength(0);
    });
  });
});