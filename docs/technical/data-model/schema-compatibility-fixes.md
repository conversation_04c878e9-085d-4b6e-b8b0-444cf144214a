# Schema Compatibility Fixes

## Overview
This document summarizes all the schema fixes applied to ensure the unified schema (000_unified_schema.sql) is compatible with the existing codebase.

## Issue: Missing Columns
The codebase expected certain columns that were not present in the initial unified schema. These have been added:

### Company Table
- `size` - Company size classification
- `address` - Full address field (in addition to address components)
- `radar_state` - CRM radar tracking state
- `priority` - Priority classification
- `current_spend` - Current spending amount
- `potential_spend` - Potential spending amount
- `last_interaction_date` - Last interaction timestamp
- `notes` - General notes field

### Contact Table
- `notes` - General notes field

### Deal Table
- `harvest_id` - Legacy Harvest ID for backward compatibility (in addition to harvest_project_id)

### Expense Table
- `name` - Expense name
- `type` - Expense type
- `frequency` - Recurrence frequency
- `repeat_count` - Number of repetitions
- `source` - Data source (default: 'manual')
- `editable` - Whether the expense can be edited (0/1)
- `metadata` - JSON metadata field

### Deal_Estimate Table
- `harvest_estimate_id` - Harvest estimate reference

### Activity_Feed Table
Complete restructure to match repository expectations:
- Renamed columns: `activity_type` → `type`, added `subject` field
- Added: `status`, `due_date`, `completed_date`, `is_read`
- Removed redundant actor/target fields in favor of simpler entity fields

### Change_Log Table
- Added `change_type` with NOT NULL constraint and CHECK constraint
- Added additional audit fields for completeness

## Issue: Column Name Mismatches

### Field_Ownership Table
- Code expected `owner` column but schema had `owner_system`
- Fixed in `deal-tracking.ts` to use `owner_system`

### Activity_Feed Table
- Code expected different column names than schema provided
- Updated schema to match code expectations

## Implementation Strategy

1. **Unified Schema Updated**: The `000_unified_schema.sql` file now includes all required columns
2. **Code Fixes**: Updated `deal-tracking.ts` to use correct column names
3. **Migration Scripts**: Created SQL scripts to add missing columns to existing databases:
   - `fix-missing-columns-final.sql` - Comprehensive fix for all missing columns

## Running the Fixes

For existing databases, run:
```bash
sqlite3 /data/upstream.db < scripts/fix-missing-columns-final.sql
```

For new databases, the unified schema already includes all fixes.

## Validation

To verify all columns are present:
1. Run `npm run dev` and check for "no such column" errors
2. Test all major features: CRM, expenses, activity feed, deal updates
3. Run the test suite: `npm test`

## Summary of Changes

- **Total columns added**: 24
- **Tables modified**: 7 (company, contact, deal, expense, deal_estimate, activity_feed, change_log)
- **Code files updated**: 1 (deal-tracking.ts)
- **Migration scripts created**: 1 (fix-missing-columns-final.sql)