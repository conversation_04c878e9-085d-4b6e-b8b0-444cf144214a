import React from 'react';
import { formatCurrency, formatDate } from '../utils/format';
import { Card } from './shared/Card';

interface ExpenseCardProps {
  title: string;
  amount: number;
  date: Date;
  isAdded: boolean;
  onSync: () => void;
  description: string;
  isLoading?: boolean;
  hasRecurringExpense?: boolean;
}

const ExpenseCard: React.FC<ExpenseCardProps> = ({
  title,
  amount,
  date,
  isAdded,
  onSync,
  description,
  isLoading = false,
  hasRecurringExpense = false
}) => {
  // Determine border color based on state
  const borderColor = isAdded 
    ? 'border-l-green-500' 
    : hasRecurringExpense 
      ? 'border-l-purple-500' 
      : 'border-l-blue-500';

  return (
    <Card className={`border-l-4 ${borderColor} hover:shadow-md transition-all duration-200`}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{title}</h3>

      <div className="mt-2">
        <div className="text-2xl font-bold text-accent">
          {formatCurrency(amount)}
        </div>

        <div className="mt-1 text-sm text-gray-600 dark:text-gray-400 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          Payment date: {formatDate(date)}
        </div>

        <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          {description}
        </div>
      </div>

      <div className="mt-4">
        <button
          onClick={onSync}
          disabled={isAdded || isLoading}
          className={`w-full px-4 py-2 rounded-full font-medium text-sm text-white transition-colors ${
            isLoading
              ? 'bg-blue-400 cursor-wait'
              : isAdded
                ? 'bg-green-500'
                : hasRecurringExpense
                  ? 'bg-purple-500 hover:bg-purple-600'
                  : 'bg-blue-500 hover:bg-blue-600'
          }`}
        >
          {isLoading
            ? 'Syncing...'
            : isAdded
              ? 'Updated ✓'
              : hasRecurringExpense ? 'Update' : 'Sync with Upstream'
          }
        </button>
      </div>
    </Card>
  );
};

export default ExpenseCard;