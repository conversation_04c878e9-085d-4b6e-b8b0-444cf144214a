import { HarvestInvoiceCacheService } from '../../../../src/services/harvest/invoice-cache-service';
import { HarvestInvoiceCacheRepository } from '../../../../src/api/repositories/harvest-invoice-cache-repository';
import { HarvestApiClient } from '../../../../src/api/clients/harvest-api-client';
import type { HarvestInvoice } from '../../../../src/types/api';

jest.mock('../../../../src/api/repositories/harvest-invoice-cache-repository');
jest.mock('../../../../src/api/clients/harvest-api-client');

describe('HarvestInvoiceCacheService', () => {
  let service: HarvestInvoiceCacheService;
  let mockRepository: jest.Mocked<HarvestInvoiceCacheRepository>;
  let mockApiClient: jest.Mocked<HarvestApiClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockRepository = new HarvestInvoiceCacheRepository(null as any) as jest.Mocked<HarvestInvoiceCacheRepository>;
    mockApiClient = new HarvestApiClient('mock-token', '12345') as jest.Mocked<HarvestApiClient>;
    service = new HarvestInvoiceCacheService(mockRepository, mockApiClient);
  });

  describe('syncInvoices', () => {
    const mockHarvestInvoices: HarvestInvoice[] = [
      {
        id: 1,
        client: { id: 101, name: 'Client A' },
        number: 'INV-001',
        purchase_order: 'PO-123',
        amount: 5000,
        due_amount: 0,
        tax: 500,
        tax_amount: 500,
        tax2: 0,
        tax2_amount: 0,
        discount: 0,
        discount_amount: 0,
        subject: 'January services',
        notes: 'Thank you',
        state: 'paid',
        period_start: '2024-01-01',
        period_end: '2024-01-31',
        issue_date: '2024-02-01',
        due_date: '2024-03-01',
        payment_term: 'net 30',
        sent_at: '2024-02-01T10:00:00Z',
        paid_at: '2024-02-15T14:30:00Z',
        closed_at: '2024-02-15T14:30:00Z',
        created_at: '2024-02-01T09:00:00Z',
        updated_at: '2024-02-15T14:30:00Z'
      },
      {
        id: 2,
        client: { id: 102, name: 'Client B' },
        number: 'INV-002',
        purchase_order: null,
        amount: 3000,
        due_amount: 3000,
        tax: 300,
        tax_amount: 300,
        tax2: 0,
        tax2_amount: 0,
        discount: 10,
        discount_amount: 300,
        subject: 'February services',
        notes: '',
        state: 'open',
        period_start: '2024-02-01',
        period_end: '2024-02-29',
        issue_date: '2024-03-01',
        due_date: '2024-03-31',
        payment_term: 'net 30',
        sent_at: '2024-03-01T10:00:00Z',
        paid_at: null,
        closed_at: null,
        created_at: '2024-03-01T09:00:00Z',
        updated_at: '2024-03-01T10:00:00Z'
      }
    ];

    it('should sync all invoices from Harvest', async () => {
      mockApiClient.get.mockResolvedValue({
        invoices: mockHarvestInvoices,
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      });

      mockRepository.bulkUpsert.mockResolvedValue(undefined);

      const result = await service.syncInvoices();

      expect(result).toEqual({
        synced: 2,
        errors: 0,
        lastSyncTime: expect.any(Date)
      });

      expect(mockApiClient.get).toHaveBeenCalledWith('/invoices', {
        per_page: 100,
        page: 1
      });

      expect(mockRepository.bulkUpsert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            harvestId: 1,
            clientId: 101,
            clientName: 'Client A',
            invoiceNumber: 'INV-001',
            amount: 5000,
            dueAmount: 0,
            issueDate: '2024-02-01',
            dueDate: '2024-03-01',
            state: 'paid',
            paymentTerm: 'net 30'
          }),
          expect.objectContaining({
            harvestId: 2,
            clientId: 102,
            clientName: 'Client B',
            invoiceNumber: 'INV-002',
            amount: 3000,
            dueAmount: 3000,
            issueDate: '2024-03-01',
            dueDate: '2024-03-31',
            state: 'open',
            paymentTerm: 'net 30'
          })
        ])
      );
    });

    it('should handle pagination', async () => {
      const page1Invoices = [mockHarvestInvoices[0]];
      const page2Invoices = [mockHarvestInvoices[1]];

      mockApiClient.get
        .mockResolvedValueOnce({
          invoices: page1Invoices,
          per_page: 1,
          total_pages: 2,
          total_entries: 2,
          next_page: 2,
          previous_page: null,
          page: 1
        })
        .mockResolvedValueOnce({
          invoices: page2Invoices,
          per_page: 1,
          total_pages: 2,
          total_entries: 2,
          next_page: null,
          previous_page: 1,
          page: 2
        });

      mockRepository.bulkUpsert.mockResolvedValue(undefined);

      const result = await service.syncInvoices();

      expect(result.synced).toBe(2);
      expect(mockApiClient.get).toHaveBeenCalledTimes(2);
    });

    it('should handle API errors gracefully', async () => {
      mockApiClient.get.mockRejectedValue(new Error('API error'));

      const result = await service.syncInvoices();

      expect(result).toEqual({
        synced: 0,
        errors: 1,
        lastSyncTime: expect.any(Date),
        errorDetails: ['API error']
      });

      expect(mockRepository.bulkUpsert).not.toHaveBeenCalled();
    });

    it('should continue syncing despite individual invoice errors', async () => {
      const invalidInvoice = {
        ...mockHarvestInvoices[0],
        id: null // Invalid ID
      };

      mockApiClient.get.mockResolvedValue({
        invoices: [invalidInvoice, mockHarvestInvoices[1]],
        per_page: 100,
        total_pages: 1,
        total_entries: 2,
        next_page: null,
        previous_page: null,
        page: 1
      });

      mockRepository.bulkUpsert.mockResolvedValue(undefined);

      const result = await service.syncInvoices();

      expect(result.synced).toBe(1);
      expect(result.errors).toBe(1);
      expect(mockRepository.bulkUpsert).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            harvestId: 2
          })
        ])
      );
    });
  });

  describe('getCachedInvoices', () => {
    const mockCachedInvoices = [
      {
        id: 1,
        harvestId: 1,
        clientId: 101,
        clientName: 'Client A',
        invoiceNumber: 'INV-001',
        amount: 5000,
        dueAmount: 0,
        issueDate: '2024-02-01',
        dueDate: '2024-03-01',
        state: 'paid',
        paymentTerm: 'net 30',
        lastSyncedAt: new Date().toISOString(),
        rawData: {}
      },
      {
        id: 2,
        harvestId: 2,
        clientId: 102,
        clientName: 'Client B',
        invoiceNumber: 'INV-002',
        amount: 3000,
        dueAmount: 3000,
        issueDate: '2024-03-01',
        dueDate: '2024-03-31',
        state: 'open',
        paymentTerm: 'net 30',
        lastSyncedAt: new Date().toISOString(),
        rawData: {}
      }
    ];

    it('should return all cached invoices', async () => {
      mockRepository.getAll.mockResolvedValue(mockCachedInvoices);

      const result = await service.getCachedInvoices();

      expect(result).toEqual(mockCachedInvoices);
      expect(mockRepository.getAll).toHaveBeenCalled();
    });

    it('should filter by state', async () => {
      const openInvoices = mockCachedInvoices.filter(i => i.state === 'open');
      mockRepository.getByState.mockResolvedValue(openInvoices);

      const result = await service.getCachedInvoices({ state: 'open' });

      expect(result).toEqual(openInvoices);
      expect(mockRepository.getByState).toHaveBeenCalledWith('open');
    });

    it('should filter by client', async () => {
      const clientInvoices = mockCachedInvoices.filter(i => i.clientId === 101);
      mockRepository.getByClient.mockResolvedValue(clientInvoices);

      const result = await service.getCachedInvoices({ clientId: 101 });

      expect(result).toEqual(clientInvoices);
      expect(mockRepository.getByClient).toHaveBeenCalledWith(101);
    });

    it('should filter by date range', async () => {
      mockRepository.getByDateRange.mockResolvedValue(mockCachedInvoices);

      const result = await service.getCachedInvoices({
        from: '2024-01-01',
        to: '2024-03-31'
      });

      expect(result).toEqual(mockCachedInvoices);
      expect(mockRepository.getByDateRange).toHaveBeenCalledWith('2024-01-01', '2024-03-31');
    });

    it('should handle multiple filters', async () => {
      const filteredInvoices = [mockCachedInvoices[0]];
      mockRepository.getByFilters.mockResolvedValue(filteredInvoices);

      const result = await service.getCachedInvoices({
        state: 'paid',
        clientId: 101,
        from: '2024-01-01',
        to: '2024-03-31'
      });

      expect(result).toEqual(filteredInvoices);
      expect(mockRepository.getByFilters).toHaveBeenCalledWith({
        state: 'paid',
        clientId: 101,
        from: '2024-01-01',
        to: '2024-03-31'
      });
    });
  });

  describe('getLastSyncTime', () => {
    it('should return last sync timestamp', async () => {
      const lastSync = new Date('2024-03-01T10:00:00Z');
      mockRepository.getLastSyncTime.mockResolvedValue(lastSync);

      const result = await service.getLastSyncTime();

      expect(result).toEqual(lastSync);
      expect(mockRepository.getLastSyncTime).toHaveBeenCalled();
    });

    it('should return null if never synced', async () => {
      mockRepository.getLastSyncTime.mockResolvedValue(null);

      const result = await service.getLastSyncTime();

      expect(result).toBeNull();
    });
  });

  describe('getCacheSummary', () => {
    it('should return cache statistics', async () => {
      const summary = {
        totalInvoices: 10,
        totalAmount: 50000,
        paidAmount: 30000,
        outstandingAmount: 20000,
        overdueAmount: 5000,
        lastSyncTime: new Date('2024-03-01T10:00:00Z'),
        oldestInvoice: '2023-01-01',
        newestInvoice: '2024-03-01'
      };

      mockRepository.getSummary.mockResolvedValue(summary);

      const result = await service.getCacheSummary();

      expect(result).toEqual(summary);
      expect(mockRepository.getSummary).toHaveBeenCalled();
    });
  });

  describe('clearCache', () => {
    it('should clear all cached invoices', async () => {
      mockRepository.deleteAll.mockResolvedValue(5);

      const result = await service.clearCache();

      expect(result).toEqual({
        deletedCount: 5,
        success: true
      });
      expect(mockRepository.deleteAll).toHaveBeenCalled();
    });

    it('should handle clear errors', async () => {
      mockRepository.deleteAll.mockRejectedValue(new Error('Database error'));

      await expect(service.clearCache()).rejects.toThrow('Database error');
    });
  });

  describe('needsSync', () => {
    it('should return true if cache is empty', async () => {
      mockRepository.getSummary.mockResolvedValue({
        totalInvoices: 0,
        totalAmount: 0,
        paidAmount: 0,
        outstandingAmount: 0,
        overdueAmount: 0,
        lastSyncTime: null,
        oldestInvoice: null,
        newestInvoice: null
      });

      const result = await service.needsSync();

      expect(result).toBe(true);
    });

    it('should return true if last sync is older than threshold', async () => {
      const oldSyncTime = new Date();
      oldSyncTime.setHours(oldSyncTime.getHours() - 25); // 25 hours ago

      mockRepository.getSummary.mockResolvedValue({
        totalInvoices: 10,
        totalAmount: 50000,
        paidAmount: 30000,
        outstandingAmount: 20000,
        overdueAmount: 5000,
        lastSyncTime: oldSyncTime,
        oldestInvoice: '2023-01-01',
        newestInvoice: '2024-03-01'
      });

      const result = await service.needsSync();

      expect(result).toBe(true);
    });

    it('should return false if recently synced', async () => {
      const recentSyncTime = new Date();
      recentSyncTime.setHours(recentSyncTime.getHours() - 1); // 1 hour ago

      mockRepository.getSummary.mockResolvedValue({
        totalInvoices: 10,
        totalAmount: 50000,
        paidAmount: 30000,
        outstandingAmount: 20000,
        overdueAmount: 5000,
        lastSyncTime: recentSyncTime,
        oldestInvoice: '2023-01-01',
        newestInvoice: '2024-03-01'
      });

      const result = await service.needsSync();

      expect(result).toBe(false);
    });

    it('should accept custom sync threshold', async () => {
      const recentSyncTime = new Date();
      recentSyncTime.setMinutes(recentSyncTime.getMinutes() - 45); // 45 minutes ago

      mockRepository.getSummary.mockResolvedValue({
        totalInvoices: 10,
        totalAmount: 50000,
        paidAmount: 30000,
        outstandingAmount: 20000,
        overdueAmount: 5000,
        lastSyncTime: recentSyncTime,
        oldestInvoice: '2023-01-01',
        newestInvoice: '2024-03-01'
      });

      // With 30-minute threshold, should need sync
      expect(await service.needsSync(30)).toBe(true);

      // With 60-minute threshold, should not need sync
      expect(await service.needsSync(60)).toBe(false);
    });
  });
});