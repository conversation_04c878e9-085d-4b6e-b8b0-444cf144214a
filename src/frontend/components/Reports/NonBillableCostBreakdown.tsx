import { useState, useMemo, useCallback } from "react";
import { StaffUtilization } from "../../../services/harvest/time-report-service";
import SimpleTooltip, { TooltipData } from "../common/SimpleTooltip";
import { DEFAULT_HOURLY_COST_RATE } from "../../../constants/costs";

interface NonBillableCostBreakdownProps {
  utilizationData: StaffUtilization[];
  fromDate?: string;
  toDate?: string;
  excludeLeave?: boolean;
  currency: string;
}

type BreakdownItem = {
  id: string | number;
  name: string;
  cost: number;
  hours: number;
  color: string;
};

/**
 * A component that displays a breakdown of non-billable costs
 * with a segmented bar chart and toggleable views (by staff or by task)
 */
const NonBillableCostBreakdown = ({
  utilizationData,
  fromDate,
  toDate,
  excludeLeave = false,
  currency,
}: NonBillableCostBreakdownProps) => {
  // State for tooltip
  const [tooltip, setTooltip] = useState<TooltipData>({
    visible: false,
    content: "",
    x: 0,
    y: 0,
  });

  // State for view mode (staff or task)
  const [viewMode, setViewMode] = useState<"staff" | "task">("staff");

  // Calculate non-billable cost data
  const { totalNonBillableCost, staffBreakdown, taskBreakdown } =
    useMemo(() => {
      // Initialize result objects
      const staffItems: Record<number, BreakdownItem> = {};
      const taskItems: Record<string, BreakdownItem> = {};
      let totalCost = 0;

      // Process each staff member
      utilizationData.forEach((staff) => {
        // Skip contractors - we only care about salaried staff with fixed costs
        if (staff.isContractor) return;

        const staffCostRate = staff.costRate || DEFAULT_HOURLY_COST_RATE;

        // Calculate period capacity
        let periodCapacity = 0;
        const fromDateObj = fromDate ? new Date(fromDate) : null;
        const toDateObj = toDate ? new Date(toDate) : null;

        if (fromDateObj && toDateObj) {
          const daysDifference =
            Math.ceil(
              (toDateObj.getTime() - fromDateObj.getTime()) /
                (1000 * 60 * 60 * 24)
            ) + 1;
          const weeksDifference = daysDifference / 7;
          periodCapacity = staff.weeklyCapacity * weeksDifference;
        } else {
          periodCapacity = staff.weeklyCapacity;
        }

        // Calculate non-billable hours (excluding leave if excludeLeave is true)
        const nonBillableHours = excludeLeave
          ? staff.totalHours - staff.billableHours - staff.leaveHours
          : staff.totalHours - staff.billableHours;

        // Calculate how much of the non-billable time is at the expense of available billable time
        const nonBillableAtExpenseOfBillable = Math.min(
          nonBillableHours,
          Math.max(0, periodCapacity - staff.billableHours)
        );

        // Calculate the cost of this non-billable time
        const staffNonBillableCost =
          nonBillableAtExpenseOfBillable * staffCostRate;

        // Only include staff with non-billable cost
        if (staffNonBillableCost > 0) {
          // Add to staff breakdown
          staffItems[staff.userId] = {
            id: staff.userId,
            name: staff.userName,
            cost: staffNonBillableCost,
            hours: nonBillableAtExpenseOfBillable,
            color: getColorForIndex(Object.keys(staffItems).length),
          };

          // Add to total cost
          totalCost += staffNonBillableCost;

          // Process task breakdown for this staff member
          staff.taskBreakdown.forEach((task) => {
            // Skip billable tasks and leave tasks (if excludeLeave is true)
            if (task.isBillable) return;
            if (excludeLeave && isLeaveTask(task.taskName)) return;

            // Calculate proportion of non-billable time spent on this task
            const taskProportion = task.hours / nonBillableHours;

            // Calculate task cost (proportional to total non-billable cost for this staff member)
            const taskCost = staffNonBillableCost * taskProportion;

            // Only include tasks with cost
            if (taskCost > 0) {
              const taskKey = `${task.taskId}-${task.taskName}`;

              // Add to or update task breakdown
              if (taskItems[taskKey]) {
                taskItems[taskKey].cost += taskCost;
                taskItems[taskKey].hours += task.hours;
              } else {
                taskItems[taskKey] = {
                  id: task.taskId,
                  name: task.taskName,
                  cost: taskCost,
                  hours: task.hours,
                  color: getColorForIndex(Object.keys(taskItems).length),
                };
              }
            }
          });
        }
      });

      return {
        totalNonBillableCost: totalCost,
        staffBreakdown: Object.values(staffItems).sort(
          (a, b) => b.cost - a.cost
        ),
        taskBreakdown: Object.values(taskItems).sort((a, b) => b.cost - a.cost),
      };
    }, [utilizationData, fromDate, toDate, excludeLeave]);

  // Get current breakdown data based on view mode
  const currentBreakdown =
    viewMode === "staff" ? staffBreakdown : taskBreakdown;

  // Handle mouse enter for segments using data attributes
  const handleSegmentMouseEnter = useCallback((e: React.MouseEvent) => {
    const itemId = e.currentTarget.dataset.itemId;
    const item = currentBreakdown.find(item => item.id.toString() === itemId);
    if (item) {
      setTooltip({
        visible: true,
        title: item.name,
        content: `${new Intl.NumberFormat("en-AU", {
          style: "currency",
          currency: currency,
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(item.cost)} (${item.hours.toFixed(1)} hours)`,
        x: e.clientX,
        y: e.clientY,
      });
    }
  }, [currentBreakdown, currency]);

  // Handle mouse leave
  const handleMouseLeave = () => {
    setTooltip((prev) => ({ ...prev, visible: false }));
  };

  // If no non-billable cost, return null
  if (totalNonBillableCost === 0) {
    return null;
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <h4 className="text-sm font-medium text-gray-600 dark:text-gray-300">
            Non-bill. cost breakdown
          </h4>
          <span className="ml-2 text-base font-bold text-yellow-600 dark:text-yellow-400">
            {new Intl.NumberFormat("en-AU", {
              style: "currency",
              currency: currency,
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(totalNonBillableCost)}
          </span>
        </div>
        <div className="flex space-x-1">
          <button
            className={`text-xs px-2 py-0.5 rounded ${
              viewMode === "staff"
                ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                : "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
            }`}
            onClick={() => setViewMode("staff")}
          >
            By Staff
          </button>
          <button
            className={`text-xs px-2 py-0.5 rounded ${
              viewMode === "task"
                ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                : "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-200"
            }`}
            onClick={() => setViewMode("task")}
          >
            By Task
          </button>
        </div>
      </div>

      {/* Segmented bar chart */}
      <div className="relative mb-2">
        <div className="h-5 w-full bg-transparent rounded-sm overflow-hidden flex relative">
          {currentBreakdown.map((item) => (
            <div
              key={`${viewMode}-${item.id}`}
              className="h-full cursor-pointer"
              style={{
                backgroundColor: item.color,
                width: `${(item.cost / totalNonBillableCost) * 100}%`,
              }}
              data-item-id={item.id}
              onMouseEnter={handleSegmentMouseEnter}
              onMouseLeave={handleMouseLeave}
              onMouseMove={handleSegmentMouseEnter}
            ></div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap gap-x-3 gap-y-1 text-xs">
        {currentBreakdown.slice(0, 5).map((item) => (
          <div
            key={`legend-${viewMode}-${item.id}`}
            className="flex items-center cursor-pointer"
            data-item-id={item.id}
            onMouseEnter={handleSegmentMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div
              className="w-2 h-2 mr-1 rounded-sm"
              style={{ backgroundColor: item.color }}
            ></div>
            <span className="text-gray-700 dark:text-gray-200">
              {item.name}:{" "}
              {new Intl.NumberFormat("en-AU", {
                style: "currency",
                currency: currency,
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              }).format(item.cost)}
            </span>
          </div>
        ))}
        {currentBreakdown.length > 5 && (
          <div className="flex items-center">
            <span className="text-gray-500 dark:text-gray-300">
              +{currentBreakdown.length - 5} more
            </span>
          </div>
        )}
      </div>

      {/* Tooltip */}
      <SimpleTooltip tooltip={tooltip} />
    </div>
  );
};

// Helper function to check if a task is a leave task
const isLeaveTask = (taskName: string) => {
  const LEAVE_TASK_NAMES = [
    "public holiday",
    "annual leave",
    "other unpaid leave",
    "carer's leave",
    "community service leave",
    "compassionate leave",
    "parental leave",
    "personal leave",
    "sick leave",
  ];

  return LEAVE_TASK_NAMES.some((leaveName) =>
    taskName.toLowerCase().includes(leaveName.toLowerCase())
  );
};

// Helper function to get a color for a segment based on index
const getColorForIndex = (index: number): string => {
  const colors = [
    "#DA702C", // Flexoki orange-400
    "#879A39", // Flexoki green-400
    "#3AA99F", // Flexoki cyan-400
    "#9A7AA0", // Flexoki purple-400
    "#CE5D97", // Flexoki magenta-400
    "#D14D41", // Flexoki red-400
    "#66800B", // Flexoki green-600
    "#8B7EC8", // Flexoki purple-500
    "#D0A215", // Flexoki yellow-400
    "#3AA99F", // Flexoki cyan-400 (reused)
    "#9A7AA0", // Flexoki purple-400 (reused)
    "#AF3029", // Flexoki red-600
  ];

  return colors[index % colors.length];
};

export default NonBillableCostBreakdown;
