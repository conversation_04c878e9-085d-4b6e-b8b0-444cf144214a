import React, { useEffect, useCallback, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { format } from "date-fns";

// Import Types
import { StaffAllocation } from "../types/estimate-types";
import { Deal } from "../types/crm-types";

// Import Hooks
import { useEstimateFormState } from "../hooks/useEstimateFormState";
import { useEstimateStaffManagement } from "../hooks/useEstimateStaffManagement";
import { useEstimateDrafts } from "../hooks/useEstimateDrafts";
import { useEstimatePublishing } from "../hooks/useEstimatePublishing";
import { useStaffSelectionModal } from "../hooks/useStaffSelectionModal";
import { useDraftLoading } from "../hooks/useDraftLoading";
import { useCurrentUserPermissions } from "../hooks/useCurrentUserPermissions";
import { useWeeks } from "../hooks/useWeeks";

// Import Components
import EstimateTable from "./Estimate/EstimateTable";
import StaffSelectionModal from "./Estimate/StaffSelectionModal";
import DraftListModal from "./Estimate/DraftListModal";
import EstimateConfigurationForm from "./Estimate/EstimateConfigurationForm";
// import EstimateActions from './Estimate/EstimateActions'; // Removed unused import
import EstimateInfoAccordion from "./Estimate/EstimateInfoAccordion";
import FloatingEstimateActions from "./Estimate/FloatingEstimateActions";
import FloatingFinancialSummary from "./Estimate/FloatingFinancialSummary";

// Import Utilities
import { generateEstimateCsv } from "./Estimate/utils/export-utils";

// Import API functions
import { linkDealEstimate } from "../api/crm";

// Main Estimate Page component - Refactored with Hooks
export const EstimatePage: React.FC = () => {
  // Get estimate ID from URL params
  const { uuid } = useParams<{ uuid: string }>();
  const navigate = useNavigate();

  // Use the UUID from URL params as the estimateId
  const estimateId = uuid === "new" ? "new" : uuid;

  // --- Core Hooks ---
  const {
    isLoading: isLoadingDraft,
    error: draftLoadError,
    loadedData: loadedDraftData,
  } = useDraftLoading(estimateId);

  const formState = useEstimateFormState(); // Initialize form state hook

  const staffState = useEstimateStaffManagement(
    [], // initial allocations
    "percentage", // initial discount type
    0, // initial discount value
    formState.billingType,
    formState.hoursPerDay
  ); // Initialize staff management hook with billing params

  // Initialize separate hooks for drafts and publishing
  const draftState = useEstimateDrafts(loadedDraftData?.draftUuid);
  const publishingState = useEstimatePublishing(
    loadedDraftData?.harvestEstimateId
  );

  const staffModalState = useStaffSelectionModal(); // Initialize staff modal hook

  const {
    canSaveEstimate: canPublishToHarvest, // Renamed for clarity - this is specifically for Harvest publishing
    isLoadingPermissions,
    permissionError,
    currentUser,
  } = useCurrentUserPermissions(); // Permissions hook

  // --- Local UI State (Minimal) ---
  const [isEstimateInitialized, setIsEstimateInitialized] =
    useState<boolean>(false); // Tracks if the estimate config is set - Type already provided
  const [isDraftListModalOpen, setDraftListModalOpen] =
    useState<boolean>(false); // State for draft list modal visibility - Type already provided
  const [selectedDealForLinking, setSelectedDealForLinking] =
    useState<Deal | null>(null); // Track selected deal for linking
  // Note: Deal selection is now tracked in formState.isDealLinked

  // Wrap billing type change handler to also convert rates
  const handleBillingTypeChangeWithConversion = useCallback(
    (newBillingType: "daily" | "hourly") => {
      const oldBillingType = formState.billingType;
      formState.handleBillingTypeChange(newBillingType);

      // Convert all staff rates when billing type changes
      if (staffState.staffAllocations.length > 0) {
        staffState.convertAllRatesForBillingType(
          oldBillingType,
          newBillingType,
          formState.hoursPerDay
        );
      }
    },
    [formState, staffState]
  );

  // Handle deal selection/linking
  const handleDealSelected = useCallback(
    (deal: Deal | null) => {
      setSelectedDealForLinking(deal);
      if (deal) {
        // Update form state to reflect deal is linked
        formState.setFormDataFromDeal(deal);
      }
    },
    [formState.setFormDataFromDeal]
  ); // Only depend on the specific function

  // Unlinking is no longer supported - linking is permanent

  // --- Effect to Populate State from Loaded Draft ---
  useEffect(() => {
    if (loadedDraftData) {
      console.log("EstimatePage: Applying loaded draft data", loadedDraftData);

      // Log invoice settings specifically for debugging
      console.log("EstimatePage: Invoice settings from loaded draft", {
        invoiceFrequency: loadedDraftData.invoiceFrequency,
        paymentTerms: loadedDraftData.paymentTerms,
      });

      // Populate form state from loaded data
      formState.setFormData({
        clientId: loadedDraftData.clientId,
        clientName: loadedDraftData.clientName,
        projectName: loadedDraftData.projectName || "",
        startDate: format(loadedDraftData.startDate, "yyyy-MM-dd"),
        endDate: format(loadedDraftData.endDate, "yyyy-MM-dd"),
        invoiceFrequency: loadedDraftData.invoiceFrequency,
        paymentTerms: loadedDraftData.paymentTerms,
        billingType: loadedDraftData.billingType,
        hoursPerDay: loadedDraftData.hoursPerDay,
      });
      // Populate staff allocations
      staffState.setStaffAllocations(loadedDraftData.staffAllocations);
      // Set discount information if available
      if (
        loadedDraftData.discountType &&
        loadedDraftData.discountValue !== undefined
      ) {
        staffState.setDiscount(
          loadedDraftData.discountType,
          loadedDraftData.discountValue
        );
      }
      // Populate draft and publishing state with IDs
      draftState.setDraftUuid(loadedDraftData.draftUuid);
      // Mark estimate as initialized
      setIsEstimateInitialized(true);
      // Clear any previous status messages
      draftState.resetSaveStatus();
      publishingState.resetPublishStatus();
    } else if (!isLoadingDraft && !draftLoadError && !estimateId) {
      // Reset if estimateId is explicitly removed or null/undefined initially
      // formState.resetForm(); // Consider if resetting form is desired when ID is removed
      // staffState.resetStaffAllocations();
      // draftState.resetSaveStatus();
      // publishingState.resetPublishStatus();
      // setIsEstimateInitialized(false);
    }
    // Dependencies ensure this runs ONLY when the loaded data itself changes.
    // Include specific setters used inside to satisfy exhaustive-deps, assuming they are stable (useCallback).
  }, [
    loadedDraftData,
    formState.setFormData,
    staffState.setStaffAllocations,
    staffState.setDiscount,
    draftState.setDraftUuid,
    draftState.resetSaveStatus,
    publishingState.resetPublishStatus,
    setIsEstimateInitialized,
  ]); // <-- Simplified dependencies

  // --- Combined Error State ---
  // Prioritize displaying the most relevant error
  const displayError =
    draftLoadError ||
    permissionError ||
    draftState.saveError ||
    publishingState.publishError ||
    formState.formError;
  // Determine combined read-only state
  // Only read-only if already published to Harvest (internal estimates can always be edited)
  const isReadOnly = publishingState.isPublishedSuccessfully;

  // --- Event Handlers ---

  // Initialise the estimate using validated form data
  const handleInitializeEstimate = useCallback(() => {
    const formData = formState.validateAndGetFormData(formState.isDealLinked); // Use deal linking state from form
    if (formData) {
      console.log("Initialising estimate with:", formData);
      staffState.resetStaffAllocations(); // Clear previous staff
      draftState.resetSaveStatus(); // Clear previous save status
      publishingState.resetPublishStatus(); // Clear previous publish status
      draftState.setDraftUuid(null); // Clear draft UUID for new estimate
      setIsEstimateInitialized(true); // Show the table/editing UI
    }
    // If formData is null, formState.formError will be set and displayed
  }, [formState, staffState, draftState, publishingState]);

  // Add selected staff member
  const handleAddStaff = useCallback(
    (staffData: Omit<StaffAllocation, "internalId" | "weeklyAllocation">) => {
      const added = staffState.addStaffMember(staffData);
      if (added) {
        staffModalState.closeStaffSelectionModal(); // Close modal on success
      } else {
        console.warn("Attempted to add duplicate staff member.");
        // Optionally set an error message here if needed
      }
    },
    [staffState, staffModalState]
  );

  // Save draft estimate handler
  const handleSaveDraftClick = useCallback(async () => {
    // Get necessary data from hooks
    const {
      selectedClientId,
      projectName,
      startDateStr,
      endDateStr,
      invoiceFrequency,
      paymentTerms,
      billingType,
      hoursPerDay,
    } = formState;
    const client = formState.availableClients.find(
      (c) => c.id === selectedClientId
    );

    // Log the values for debugging
    console.log("EstimatePage - Save draft - Form values:", {
      invoiceFrequency,
      paymentTerms,
      formState,
    });

    // Basic validation before calling the hook's save function
    if (!selectedClientId || !client || !startDateStr || !endDateStr) {
      console.error("Save Draft validation failed: Client or dates missing.");
      // Optionally set a form error: formState.setFormError("Client or dates missing.");
      return false; // Indicate failure
    }

    // Call the drafts hook's function
    const savedDraftUuid = await draftState.saveDraft(
      {
        companyId: selectedClientId, // selectedClientId is actually a company ID
        clientName: client.name,
        projectName,
        startDate: new Date(startDateStr),
        endDate: new Date(endDateStr),
        invoiceFrequency: invoiceFrequency, // Use the destructured value
        paymentTerms: paymentTerms, // Use the destructured value
        billingType: billingType, // Use the destructured value
        hoursPerDay: hoursPerDay, // Use the destructured value
        staffAllocations: staffState.staffAllocations, // Pass base allocations
        projectTotals: {
          discountType: staffState.projectTotals.discountType,
          discountValue: staffState.projectTotals.discountValue,
        },
      },
      currentUser // Pass current user for backend check
    );

    // If draft was saved successfully and a deal was selected, link them
    if (savedDraftUuid && selectedDealForLinking) {
      console.log(
        "Linking estimate to deal:",
        savedDraftUuid,
        selectedDealForLinking.id
      );
      try {
        const linkResult = await linkDealEstimate(
          selectedDealForLinking.id,
          savedDraftUuid,
          "internal",
          currentUser?.email || "system"
        );
        if (linkResult.success) {
          console.log("Successfully linked estimate to deal");
        } else {
          console.error("Failed to link estimate to deal:", linkResult.error);
        }
      } catch (error) {
        console.error("Error linking estimate to deal:", error);
      }
    }

    return !!savedDraftUuid; // Return true if save was successful (UUID is not null)
  }, [formState, staffState, draftState, currentUser, selectedDealForLinking]);

  // Publish estimate to Harvest handler
  const handlePublishToHarvestClick = useCallback(async () => {
    // Show confirmation dialog before proceeding
    // Escaped quotes for JSX compatibility if needed, but standard strings are fine here.
    // Using template literal for easier multi-line handling.
    const confirmMessage = `Publishing an estimate to Harvest is a final step and should only be done when you're ready to share it with clients.

Once published, you'll need to make a new estimate to make changes, as Harvest doesn't support editing existing estimates.

If you plan to continue working on this estimate, consider saving as a draft first.`;

    if (!window.confirm(confirmMessage)) {
      return false; // User canceled
    }

    // First, ensure we have a saved draft
    if (!draftState.draftUuid) {
      const savedSuccessfully = await handleSaveDraftClick();
      if (!savedSuccessfully) {
        // Draft saving failed - error will be shown by the draft hook
        return false;
      }
    }

    // Now that we have a draft UUID, proceed with publishing
    // Get necessary data from hooks
    const { selectedClientId, projectName } = formState;
    const { allocationsWithTotals, projectTotals } = staffState; // Get calculated totals

    // Basic validation
    if (!selectedClientId) {
      console.error("Publish to Harvest validation failed: Client missing.");
      // Optionally set a form error: formState.setFormError("Client missing.");
      return false; // Indicate failure
    }
    if (allocationsWithTotals.length === 0 || projectTotals.totalDays <= 0) {
      console.error(
        "Publish to Harvest validation failed: No staff or time allocated."
      );
      return false;
    }

    // Call the publishing hook's function, passing all required data
    const client = formState.availableClients.find(
      (c) => c.id === selectedClientId
    );
    if (!client) {
      console.error(
        "Publish to Harvest validation failed: Client data not found."
      );
      return false; // Indicate failure
    }

    // Check if the company has a Harvest ID
    if (!client.harvestId) {
      formState.setFormError(
        "This company must first be created in Harvest before publishing estimates. Please go to Data Management to link this company to a Harvest client."
      );
      console.error(
        "Publish to Harvest validation failed: Company not linked to Harvest."
      );
      return false;
    }

    // Get invoice frequency and payment terms
    const { invoiceFrequency, paymentTerms } = formState;

    // Log the values for debugging
    console.log("EstimatePage - Publish to Harvest - Form values:", {
      invoiceFrequency,
      paymentTerms,
    });

    return await publishingState.publishToHarvest(
      {
        draftUuid: draftState.draftUuid!, // We've ensured this is not null
        clientId: client.harvestId, // Use the Harvest ID for publishing
        clientName: client.name, // Pass client name
        startDate: new Date(formState.startDateStr), // Pass start date
        endDate: new Date(formState.endDateStr), // Pass end date
        projectName,
        invoiceFrequency: invoiceFrequency, // Use the destructured value
        paymentTerms: paymentTerms, // Use the destructured value
        allocationsWithTotals,
        projectTotals,
      },
      currentUser // Pass current user
    );
  }, [
    formState,
    staffState,
    draftState.draftUuid,
    publishingState,
    handleSaveDraftClick,
    currentUser,
  ]);

  // Reset the entire estimate state
  const handleResetClick = useCallback(() => {
    if (
      window.confirm(
        "Are you sure you want to reset? All unsaved changes will be lost."
      )
    ) {
      formState.resetForm();
      staffState.resetStaffAllocations();
      draftState.resetSaveStatus();
      draftState.setDraftUuid(null);
      publishingState.resetPublishStatus();
      setIsEstimateInitialized(false); // Go back to the initial prompt state
    }
  }, [formState, staffState, draftState, publishingState]);

  // Get weeks directly using the hook at the component level
  const weeks = useWeeks(
    formState.startDateStr ? new Date(formState.startDateStr) : undefined,
    formState.endDateStr ? new Date(formState.endDateStr) : undefined
  );

  // Handle export to CSV
  const handleExportClick = useCallback(() => {
    // Check that we have required data
    if (
      !isEstimateInitialized ||
      !formState.startDateStr ||
      !formState.endDateStr ||
      staffState.allocationsWithTotals.length === 0
    ) {
      console.warn("Export failed: Missing required data for export");
      return;
    }

    // Get client name from the available clients
    const selectedClient = formState.availableClients.find(
      (c) => c.id === formState.selectedClientId
    );

    if (!selectedClient) {
      console.warn("Export failed: Client not found");
      return;
    }

    // Create export data
    const exportData = {
      clientName: selectedClient.name,
      projectName: formState.projectName || "Untitled Project",
      startDate: formState.startDateStr,
      endDate: formState.endDateStr,
    };

    // Call the export utility
    generateEstimateCsv(
      exportData,
      staffState.allocationsWithTotals,
      staffState.projectTotals,
      weeks
    );
  }, [
    isEstimateInitialized,
    formState.selectedClientId,
    formState.availableClients,
    formState.projectName,
    formState.startDateStr,
    formState.endDateStr,
    staffState.allocationsWithTotals,
    staffState.projectTotals,
    weeks,
  ]);

  // --- State for EstimateInfoAccordion ---
  const [showInformation, setShowInformation] = React.useState(false);

  // --- Render Logic ---
  return (
    <div className="p-4 md:p-6 lg:p-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-3">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
            Upstream Pricing Tool
          </h1>

          {/* Dynamic Project Information - only shows when client is selected */}
          {formState.selectedClientId && (
            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400 border-l-2 border-gray-200 dark:border-gray-700 pl-3">
              <div className="flex flex-wrap items-baseline gap-x-2">
                {/* Get client name from selected ID */}
                {(() => {
                  const selectedClient = formState.availableClients.find(
                    (c) => c.id === formState.selectedClientId
                  );
                  if (selectedClient) {
                    return (
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        {selectedClient.name}
                      </span>
                    );
                  }
                  return null;
                })()}

                {formState.projectName && (
                  <>
                    <span className="text-gray-400">•</span>
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      {formState.projectName}
                    </span>
                  </>
                )}

                {/* Linking is permanent - no unlink button */}
              </div>

              {formState.startDateStr && formState.endDateStr && (
                <div className="text-xs mt-1 text-gray-500">
                  {format(new Date(formState.startDateStr), "MMM d, yyyy")} —{" "}
                  {format(new Date(formState.endDateStr), "MMM d, yyyy")}
                </div>
              )}
            </div>
          )}
        </div>

        <button
          type="button"
          onClick={() => navigate("/estimates")}
          className="flex items-center px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600"
        >
          <svg
            className="h-5 w-5 mr-2"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          <span>Back to Estimates</span>
        </button>
      </div>

      {/* Info Accordion */}
      <div className="mb-3 rounded-lg overflow-hidden border border-blue-200 dark:border-blue-800/40 shadow-sm">
        <EstimateInfoAccordion
          showInformation={showInformation}
          setShowInformation={setShowInformation}
          variant="editor"
        />
      </div>

      {/* Main Content Area */}
      <div className="flex flex-col gap-4">
        {/* Error/Success Messages Area (only renders when there's content) */}
        {(displayError ||
          draftState.saveSuccessMessage ||
          publishingState.publishSuccessMessage ||
          isLoadingPermissions ||
          permissionError ||
          (!isLoadingPermissions &&
            !permissionError &&
            !canPublishToHarvest &&
            !publishingState.isPublishedSuccessfully)) && (
          <div className="mb-3">
            {displayError && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-sm text-red-600 dark:text-red-400">
                  {displayError}
                </p>
              </div>
            )}
            {publishingState.publishSuccessMessage &&
              !displayError &&
              publishingState.isPublishedSuccessfully &&
              publishingState.publishedEstimateId && ( // Only show success if no error and published to Harvest
                <div className="p-3 bg-[#f36c21]/10 dark:bg-[#f36c21]/20 border border-[#f36c21]/20 dark:border-[#f36c21]/30 rounded-md">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-[#f36c21] dark:text-[#f36c21]/90">
                      {publishingState.publishSuccessMessage}
                    </p>
                    <a
                      href={`https://onbord.harvestapp.com/estimates/${publishingState.publishedEstimateId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-3 py-1.5 rounded text-xs font-medium bg-[#f36c21] text-white hover:bg-[#e05a10] transition-colors"
                    >
                      View in Harvest
                      <svg
                        className="w-3.5 h-3.5 ml-1"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                    </a>
                  </div>
                </div>
              )}
            {draftState.saveSuccessMessage &&
              !displayError &&
              !publishingState.isPublishedSuccessfully && ( // Success for draft save
                <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
                  <p className="text-sm text-green-600 dark:text-green-400">
                    {draftState.saveSuccessMessage}
                  </p>
                </div>
              )}
            {/* Display permission loading/error */}
            {(isLoadingPermissions || permissionError) && !displayError && (
              <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                {isLoadingPermissions && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Checking permissions...
                  </p>
                )}
                {permissionError && (
                  <p className="text-xs text-red-600 dark:text-red-400">
                    Error checking permissions
                  </p>
                )}
              </div>
            )}
            {!isLoadingPermissions &&
              !permissionError &&
              !canPublishToHarvest &&
              !publishingState.isPublishedSuccessfully &&
              !displayError && (
                <div className="p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                  <p className="text-xs text-yellow-600 dark:text-yellow-400">
                    Note: You can create and save internal estimates, but lack
                    permission to publish to Harvest.
                  </p>
                </div>
              )}
          </div>
        )}

        {/* Configuration Row - Actions Box Removed */}
        <div className="flex gap-4">
          {/* Use EstimateConfigurationForm Component (now takes full width) */}
          <EstimateConfigurationForm
            formState={formState}
            isReadOnly={isReadOnly} // Pass combined read-only state
            className="w-full" // Update to take full width
            draftUuid={draftState.draftUuid} // Pass draftUuid for deal linking
            onDealSelected={handleDealSelected} // Track selected deal
            // Note: Deal selection is now tracked internally in formState.isDealLinked
          />
        </div>

        {/* Main Content Area (Table or Initial Prompt) */}
        <div className="w-full">
          {isLoadingDraft ? (
            <div className="flex justify-center items-center h-64">
              {" "}
              {/* Loading indicator for draft */}
              <svg
                className="animate-spin h-8 w-8 text-primary"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            </div>
          ) : !isEstimateInitialized ? (
            // Initial Prompt
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 flex flex-col items-center justify-center min-h-[400px]">
              <div className="text-center max-w-lg mx-auto">
                <svg
                  className="w-16 h-16 text-gray-400 mb-4 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  ></path>
                </svg>
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">
                  Create Your Estimate
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Begin by selecting a client, specifying a date range, and
                  giving your estimate a name. Then click &quot;Initialise
                  Estimate&quot; to start building your project team and
                  pricing.
                </p>
                <div className="flex items-center justify-center">
                  <button
                    onClick={handleInitializeEstimate}
                    className="btn-modern btn-modern--primary px-4 py-2 rounded-lg font-medium disabled:opacity-50"
                    disabled={(() => {
                      const disabled =
                        !formState.selectedClientId ||
                        !formState.startDateStr ||
                        !formState.endDateStr ||
                        !formState.invoiceFrequency ||
                        !formState.paymentTerms ||
                        formState.isLoadingClients ||
                        !!formState.formError;

                      return disabled;
                    })()} // Disable if form invalid - require all fields including invoice frequency and payment terms
                  >
                    Initialise Estimate
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Estimate Table
            <div>
              <EstimateTable
                // Pass calculated totals from staff hook
                allocationsWithTotals={staffState.allocationsWithTotals}
                projectTotals={staffState.projectTotals}
                // Pass dates from form hook (convert string dates to Date objects)
                startDate={
                  formState.startDateStr
                    ? new Date(formState.startDateStr)
                    : undefined
                }
                endDate={
                  formState.endDateStr
                    ? new Date(formState.endDateStr)
                    : undefined
                }
                // Pass handlers from staff hook
                onAllocationChange={staffState.updateStaffAllocation}
                onRateChange={staffState.updateStaffRate}
                onRemoveStaff={staffState.removeStaffMember}
                onReorderAllocations={staffState.reorderAllocations}
                // Pass discount handlers
                onDiscountTypeChange={staffState.updateDiscountType}
                onDiscountValueChange={staffState.updateDiscountValue}
                // Pass handler from staff modal hook
                onTriggerAddStaff={staffModalState.openStaffSelectionModal}
                // Pass read-only status
                isReadOnly={isReadOnly}
                // Pass estimate ID for enhanced grid features
                estimateId={estimateId || ""}
                // Pass billing type and hours per day from form state
                billingType={formState.billingType}
                hoursPerDay={formState.hoursPerDay}
                onBillingTypeChange={handleBillingTypeChangeWithConversion}
                onHoursPerDayChange={formState.handleHoursPerDayChange}
              />
              {/* BudgetSummary rendering removed from here, will be added inside EstimateTable */}
            </div>
          )}
        </div>
      </div>

      {/* Floating panels - positioned fixed to the window */}
      <FloatingFinancialSummary projectTotals={staffState.projectTotals} />
      <FloatingEstimateActions
        isEstimateInitialized={isEstimateInitialized}
        isSaving={draftState.isSaving} // Only draft saving state
        isPublishing={publishingState.isPublishing} // Separate publishing state
        isSavedSuccessfully={!!draftState.draftUuid && !draftState.saveError} // Based on draft state
        isPublishedSuccessfully={publishingState.isPublishedSuccessfully} // Separate published state
        saveError={draftState.saveError} // Draft save errors
        publishError={publishingState.publishError} // Publish errors
        draftUuid={draftState.draftUuid}
        canSaveEstimate={true} // Always allow saving internal estimates
        canPublishToHarvest={canPublishToHarvest} // Pass Harvest permission separately
        isLoadingPermissions={isLoadingPermissions}
        hasStaffAllocations={
          staffState.staffAllocations.length > 0 &&
          staffState.projectTotals.totalDays > 0
        }
        onInitialize={handleInitializeEstimate}
        onReset={handleResetClick}
        onSaveDraft={handleSaveDraftClick}
        onSaveToHarvest={handlePublishToHarvestClick}
        onExport={handleExportClick}
        allocationsWithTotals={staffState.allocationsWithTotals}
        isFormValidForInit={
          !!formState.selectedClientId &&
          !!formState.startDateStr &&
          !!formState.endDateStr &&
          !!formState.invoiceFrequency &&
          !!formState.paymentTerms &&
          !formState.formError
        }
        isLoadingClients={formState.isLoadingClients}
      />

      {/* Modals */}
      <StaffSelectionModal
        isOpen={staffModalState.isStaffModalOpen}
        users={staffModalState.availableUsers || []}
        onSelect={handleAddStaff} // Use the wrapped handler
        onClose={staffModalState.closeStaffSelectionModal}
        isLoading={staffModalState.isLoadingUsers}
        error={staffModalState.userFetchError}
        // Pass existing user IDs from staff hook
        existingUserIds={staffState.staffAllocations.map(
          (a) => a.harvestUserId
        )}
      />

      {/* Draft List Modal with React Router navigation */}
      <DraftListModal
        isOpen={isDraftListModalOpen}
        onClose={() => setDraftListModalOpen(false)}
        onSelectDraft={(uuid) => {
          // Navigate to the estimate page with the selected UUID
          navigate(`/estimates/${uuid}`);
          setDraftListModalOpen(false);
        }}
      />
    </div>
  );
};

// Export is already handled with named export above
