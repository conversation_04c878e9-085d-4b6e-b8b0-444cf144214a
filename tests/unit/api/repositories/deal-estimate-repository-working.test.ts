import { jest } from '@jest/globals';
import Database from 'better-sqlite3';
import { DealEstimateRepository } from '../../../../src/api/repositories/relationships/deal-estimate-repository';

// Mock database
jest.mock('better-sqlite3');

describe('DealEstimateRepository - Working Tests', () => {
  let mockDb: jest.Mocked<Database.Database>;
  let repository: DealEstimateRepository;

  beforeEach(() => {
    // Create a more complete mock
    mockDb = {
      prepare: jest.fn(),
      exec: jest.fn(),
      pragma: jest.fn(),
      close: jest.fn(),
    } as any;
    
    (Database as unknown as jest.Mock).mockReturnValue(mockDb);
    repository = new DealEstimateRepository();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Working Examples', () => {
    it('should check if a deal is linked to an estimate', () => {
      const mockStatement = {
        get: jest.fn().mockReturnValue({ count: 1 }),
      };
      mockDb.prepare.mockReturnValue(mockStatement as any);

      const result = repository.isDealLinkedToEstimate('deal-123', 'estimate-456');

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('SELECT COUNT(*) as count FROM deal_estimate')
      );
      expect(mockStatement.get).toHaveBeenCalledWith('deal-123', 'estimate-456');
      expect(result).toBe(true);
    });

    it('should retrieve internal estimates for a deal', () => {
      const mockStatement = {
        all: jest.fn().mockReturnValue([
          { estimate_id: 'est-1' },
          { estimate_id: 'est-2' }
        ]),
      };
      mockDb.prepare.mockReturnValue(mockStatement as any);

      const result = repository.getInternalEstimatesForDeal('deal-123');

      expect(mockDb.prepare).toHaveBeenCalledWith(
        expect.stringContaining('SELECT estimate_id FROM deal_estimate')
      );
      expect(mockStatement.all).toHaveBeenCalledWith('deal-123');
      expect(result).toEqual(['est-1', 'est-2']);
    });

    it('should handle linkDealToEstimate with proper mocking', () => {
      // First mock for the SELECT check
      const mockSelectStatement = {
        get: jest.fn().mockReturnValue(null), // No existing relationship
      };
      
      // Second mock for the INSERT
      const mockInsertStatement = {
        run: jest.fn().mockReturnValue({ changes: 1, lastInsertRowid: 1 }),
      };
      
      // Set up the mock sequence
      mockDb.prepare
        .mockReturnValueOnce(mockSelectStatement as any)
        .mockReturnValueOnce(mockInsertStatement as any);

      const result = repository.linkDealToEstimate(
        'deal-123',
        'estimate-456',
        'internal',
        'user-789'
      );

      // Verify the SELECT was called
      expect(mockSelectStatement.get).toHaveBeenCalledWith('deal-123', 'estimate-456');
      
      // Verify the INSERT was called
      expect(mockInsertStatement.run).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          deal_id: 'deal-123',
          estimate_id: 'estimate-456',
          estimate_type: 'internal',
          linked_by: 'user-789',
          linked_at: expect.any(String),
        })
      );

      expect(result).toEqual(
        expect.objectContaining({
          deal_id: 'deal-123',
          estimate_id: 'estimate-456',
          estimate_type: 'internal',
        })
      );
    });
  });
});