import React from "react"; import { CustomExpense } from "../../../types"; import { formatCurrency, calculateMonthlyEquivalent } from "./utils"; import { Card } from "../../components/shared/Card"; import XeroBadge from "../../components/shared/XeroBadge"; /** * Get the current quarter and financial year * @returns Object with quarter and financial year information */ const getCurrentQuarterInfo = (): { quarter: number; quarterName: string; quarterLabel: string; financialYear: string; daysElapsed: number; daysInQuarter: number; percentComplete: number; } => { const today = new Date(); const currentMonth = today.getMonth(); // 0-indexed (0 = January) const currentYear = today.getFullYear(); // Determine current quarter (1-4) let quarter: number; let quarterStartMonth: number; let quarterEndMonth: number; if (currentMonth >= 0 && currentMonth <= 2) { // Q3 of financial year (Jan-Mar) quarter = 3; quarterStartMonth = 0; // January quarterEndMonth = 2; // March } else if (currentMonth >= 3 && currentMonth <= 5) { // Q4 of financial year (Apr-Jun) quarter = 4; quarterStartMonth = 3; // April quarterEndMonth = 5; // June } else if (currentMonth >= 6 && currentMonth <= 8) { // Q1 of financial year (Jul-Sep) quarter = 1; quarterStartMonth = 6; // July quarterEndMonth = 8; // September } else { // Q2 of financial year (Oct-Dec) quarter = 2; quarterStartMonth = 9; // October quarterEndMonth = 11; // December } // Determine financial year (e.g., "2023-24") let financialYearStart: number; let financialYearEnd: number; if (currentMonth >= 6) { // July onwards financialYearStart = currentYear; financialYearEnd = currentYear + 1; } else { // Before July financialYearStart = currentYear - 1; financialYearEnd = currentYear; } const financialYear = `${financialYearStart}-${String(financialYearEnd).slice( 2 )}`; // Calculate days elapsed in the quarter const quarterStart = new Date(currentYear, quarterStartMonth, 1); const quarterEnd = new Date(currentYear, quarterEndMonth + 1, 0); // Last day of end month const daysElapsed = Math.floor( (today.getTime() - quarterStart.getTime()) / (1000 * 60 * 60 * 24) ); const daysInQuarter = Math.floor( (quarterEnd.getTime() - quarterStart.getTime()) / (1000 * 60 * 60 * 24) ) + 1; const percentComplete = (daysElapsed / daysInQuarter) * 100; // Quarter name mapping const quarterNames = ["Jul-Sep", "Oct-Dec", "Jan-Mar", "Apr-Jun"]; const quarterName = quarterNames[quarter - 1]; // Quarter label (Q1, Q2, etc.) const quarterLabel = `Q${quarter}`; return { quarter, quarterName, quarterLabel, financialYear, daysElapsed, daysInQuarter, percentComplete, }; }; import { isXeroExpense } from "./utils"; /** * Find the GST expense (BAS payment) in the expenses list * @param expenses List of expenses * @returns GST expense or undefined if not found */ const findGSTExpense = ( expenses: CustomExpense[] ): CustomExpense | undefined => { return expenses.find( (e) => e.type === "Taxes" && isXeroExpense(e) && // First check if it's from Xero e.source?.includes("xero-gst") ); }; interface ExpenseSummaryProps { expenses: CustomExpense[]; } /** * Component for displaying expense summary cards */ export const ExpenseSummary: React.FC<ExpenseSummaryProps> = ({ expenses }) => { // Get current quarter information const quarterInfo = getCurrentQuarterInfo(); const { quarterName, quarterLabel, financialYear, percentComplete } = quarterInfo; // Calculate totals const monthlyTotal = expenses .filter((e) => e.frequency === "monthly") .reduce((sum, e) => sum + e.amount, 0); // Find the GST expense (BAS payment) const gstExpense = findGSTExpense(expenses); const gstAmount = gstExpense?.amount || 0; // Check if the GST expense was created with a predicted amount const isUsingPredictedAmount = gstExpense?.metadata?.usedProjectedAmount === true; const monthlyEquivalentTotal = expenses.reduce( (sum, e) => sum + calculateMonthlyEquivalent(e), 0 ); return ( <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 onboarding-expense-summary"> <Card className="border-l-4 border-l-accent hover:shadow-md transition-all duration-200"> <div className="flex items-center mb-2"> <div className="w-7 h-7 mr-2 bg-accent/10 rounded-full flex items-center justify-center text-accent"> <span className="font-semibold text-xs">M</span> </div> <h3 className="text-sm font-medium text-gray-700 dark:text-gray-100"> Monthly Expenses </h3> </div> <p className="text-2xl font-bold text-accent"> {formatCurrency(monthlyTotal)} </p> <p className="text-xs text-gray-500 dark:text-gray-400 mt-1"> Recurring monthly payments </p> </Card> <Card className="border-l-4 border-l-blue-500 hover:shadow-md transition-all duration-200"> <div className="flex items-center justify-between mb-2"> <div className="flex items-center"> <div className="w-7 h-7 mr-2 bg-blue-100 rounded-full flex items-center justify-center text-blue-600"> <img src="/ATO_Logo.svg" alt="ATO Logo" className="w-5 h-5" /> </div> <h3 className="text-sm font-medium text-gray-700 dark:text-gray-100"> {quarterLabel} FY{financialYear} BAS ({quarterName}) </h3> </div> <div className="flex space-x-2"> <XeroBadge /> {isUsingPredictedAmount ? ( <div className="inline-flex items-center px-2 py-0.5 rounded bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 text-xs"> <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> Predicted </div> ) : ( <div className="inline-flex items-center px-2 py-0.5 rounded bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 text-xs"> <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> Accrued </div> )} </div> </div> <p className="text-2xl font-bold text-blue-600"> {formatCurrency(gstAmount)} </p> <div className="flex flex-col mt-1"> <p className="text-xs text-gray-500 dark:text-gray-400"> {isUsingPredictedAmount ? "Using predicted amount (includes future invoices)" : "Using accrued amount (current liability)"} </p> <div className="inline-flex items-center self-start px-2 py-0.5 mt-1 rounded bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 text-xs"> <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> {Math.round(percentComplete)}% through {quarterName} ({quarterLabel} ) </div> </div> </Card> <Card className="border-l-4 border-l-secondary hover:shadow-md transition-all duration-200"> <div className="flex items-center mb-2"> <div className="w-7 h-7 mr-2 bg-secondary/10 rounded-full flex items-center justify-center text-secondary"> <span className="font-semibold text-xs">Σ</span> </div> <h3 className="text-sm font-medium text-gray-700 dark:text-gray-100"> Monthly Equivalent </h3> </div> <p className="text-2xl font-bold text-gray-900 dark:text-gray-100"> {formatCurrency(monthlyEquivalentTotal)} </p> <p className="text-xs text-gray-500 dark:text-gray-400 mt-1"> Weekly × 4.333, Monthly as-is, Quarterly ÷ 3, One-off not included </p> </Card> </div> ); }; export default ExpenseSummary; 