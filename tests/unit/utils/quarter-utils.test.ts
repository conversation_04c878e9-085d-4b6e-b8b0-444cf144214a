import {
  getCurrentQuarter,
  getQuarterDateRange,
  getQuarterLabel,
  isInQuarter,
  getFinancialYearQuarters,
  getQuarterProgress,
  getNextQuarterStart,
  getPreviousQuarterEnd,
  parseQuarterString
} from '../../../src/frontend/utils/quarter-utils';

describe('Quarter Utilities', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-06-15'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('getCurrentQuarter', () => {
    it('should return current quarter', () => {
      const quarter = getCurrentQuarter();
      
      expect(quarter).toEqual({
        year: 2024,
        quarter: 2,
        label: 'Q2 2024',
        startDate: new Date('2024-04-01'),
        endDate: new Date('2024-06-30')
      });
    });

    it('should handle different months correctly', () => {
      jest.setSystemTime(new Date('2024-01-15'));
      expect(getCurrentQuarter().quarter).toBe(1);

      jest.setSystemTime(new Date('2024-04-15'));
      expect(getCurrentQuarter().quarter).toBe(2);

      jest.setSystemTime(new Date('2024-07-15'));
      expect(getCurrentQuarter().quarter).toBe(3);

      jest.setSystemTime(new Date('2024-10-15'));
      expect(getCurrentQuarter().quarter).toBe(4);
    });

    it('should handle financial year offset', () => {
      // Australian financial year starts July 1
      const quarter = getCurrentQuarter({ financialYearStart: 7 });
      
      expect(quarter).toEqual({
        year: 2023, // FY 2023-24
        quarter: 4,
        label: 'Q4 FY23-24',
        startDate: new Date('2024-04-01'),
        endDate: new Date('2024-06-30')
      });
    });
  });

  describe('getQuarterDateRange', () => {
    it('should return correct date range for each quarter', () => {
      expect(getQuarterDateRange(2024, 1)).toEqual({
        start: new Date('2024-01-01'),
        end: new Date('2024-03-31')
      });

      expect(getQuarterDateRange(2024, 2)).toEqual({
        start: new Date('2024-04-01'),
        end: new Date('2024-06-30')
      });

      expect(getQuarterDateRange(2024, 3)).toEqual({
        start: new Date('2024-07-01'),
        end: new Date('2024-09-30')
      });

      expect(getQuarterDateRange(2024, 4)).toEqual({
        start: new Date('2024-10-01'),
        end: new Date('2024-12-31')
      });
    });

    it('should handle invalid quarter numbers', () => {
      expect(() => getQuarterDateRange(2024, 0)).toThrow('Invalid quarter');
      expect(() => getQuarterDateRange(2024, 5)).toThrow('Invalid quarter');
    });

    it('should handle leap years correctly', () => {
      const q1_2024 = getQuarterDateRange(2024, 1);
      expect(q1_2024.end.getDate()).toBe(31); // March 31

      // February 29 exists in 2024
      const feb29 = new Date('2024-02-29');
      expect(feb29.getDate()).toBe(29);
    });
  });

  describe('getQuarterLabel', () => {
    it('should format quarter label correctly', () => {
      expect(getQuarterLabel(2024, 1)).toBe('Q1 2024');
      expect(getQuarterLabel(2024, 4)).toBe('Q4 2024');
    });

    it('should handle financial year format', () => {
      expect(getQuarterLabel(2023, 4, { 
        financialYear: true,
        financialYearStart: 7 
      })).toBe('Q4 FY23-24');
    });

    it('should support custom formats', () => {
      expect(getQuarterLabel(2024, 2, { 
        format: 'long' 
      })).toBe('2nd Quarter 2024');

      expect(getQuarterLabel(2024, 3, { 
        format: 'short' 
      })).toBe('Q3 24');
    });
  });

  describe('isInQuarter', () => {
    it('should check if date is in quarter', () => {
      const date1 = new Date('2024-05-15');
      const date2 = new Date('2024-07-15');

      expect(isInQuarter(date1, 2024, 2)).toBe(true);
      expect(isInQuarter(date1, 2024, 3)).toBe(false);
      expect(isInQuarter(date2, 2024, 3)).toBe(true);
    });

    it('should handle edge dates', () => {
      const startDate = new Date('2024-04-01');
      const endDate = new Date('2024-06-30');

      expect(isInQuarter(startDate, 2024, 2)).toBe(true);
      expect(isInQuarter(endDate, 2024, 2)).toBe(true);
    });

    it('should handle dates at midnight', () => {
      const midnight = new Date('2024-07-01T00:00:00');
      
      expect(isInQuarter(midnight, 2024, 2)).toBe(false);
      expect(isInQuarter(midnight, 2024, 3)).toBe(true);
    });
  });

  describe('getFinancialYearQuarters', () => {
    it('should return all quarters for a financial year', () => {
      const quarters = getFinancialYearQuarters(2023);

      expect(quarters).toHaveLength(4);
      expect(quarters[0]).toEqual({
        year: 2023,
        quarter: 1,
        label: 'Q1 FY23-24',
        startDate: new Date('2023-07-01'),
        endDate: new Date('2023-09-30')
      });
      expect(quarters[3]).toEqual({
        year: 2023,
        quarter: 4,
        label: 'Q4 FY23-24',
        startDate: new Date('2024-04-01'),
        endDate: new Date('2024-06-30')
      });
    });

    it('should handle different financial year starts', () => {
      const quarters = getFinancialYearQuarters(2024, { 
        financialYearStart: 4 // April
      });

      expect(quarters[0].startDate).toEqual(new Date('2024-04-01'));
      expect(quarters[3].endDate).toEqual(new Date('2025-03-31'));
    });

    it('should include metadata', () => {
      const quarters = getFinancialYearQuarters(2023, {
        includeMetadata: true
      });

      quarters.forEach(quarter => {
        expect(quarter.daysInQuarter).toBeDefined();
        expect(quarter.workingDays).toBeDefined();
        expect(quarter.isCurrentQuarter).toBeDefined();
      });
    });
  });

  describe('getQuarterProgress', () => {
    it('should calculate progress through current quarter', () => {
      jest.setSystemTime(new Date('2024-05-15')); // Midway through Q2
      
      const progress = getQuarterProgress();

      expect(progress.percentComplete).toBeCloseTo(48.9, 1); // ~44 days / 91 days
      expect(progress.daysElapsed).toBe(44);
      expect(progress.daysRemaining).toBe(47);
      expect(progress.totalDays).toBe(91);
    });

    it('should handle start of quarter', () => {
      jest.setSystemTime(new Date('2024-04-01'));
      
      const progress = getQuarterProgress();

      expect(progress.percentComplete).toBe(0);
      expect(progress.daysElapsed).toBe(0);
      expect(progress.daysRemaining).toBe(91);
    });

    it('should handle end of quarter', () => {
      jest.setSystemTime(new Date('2024-06-30'));
      
      const progress = getQuarterProgress();

      expect(progress.percentComplete).toBeCloseTo(100, 1);
      expect(progress.daysElapsed).toBe(90);
      expect(progress.daysRemaining).toBe(1);
    });

    it('should calculate business days progress', () => {
      const progress = getQuarterProgress({ businessDaysOnly: true });

      expect(progress.businessDaysElapsed).toBeDefined();
      expect(progress.businessDaysRemaining).toBeDefined();
      expect(progress.totalBusinessDays).toBeDefined();
    });
  });

  describe('getNextQuarterStart', () => {
    it('should return start date of next quarter', () => {
      expect(getNextQuarterStart(2024, 1)).toEqual(new Date('2024-04-01'));
      expect(getNextQuarterStart(2024, 2)).toEqual(new Date('2024-07-01'));
      expect(getNextQuarterStart(2024, 3)).toEqual(new Date('2024-10-01'));
      expect(getNextQuarterStart(2024, 4)).toEqual(new Date('2025-01-01'));
    });

    it('should handle year transition', () => {
      const nextStart = getNextQuarterStart(2024, 4);
      expect(nextStart.getFullYear()).toBe(2025);
      expect(nextStart.getMonth()).toBe(0); // January
    });
  });

  describe('getPreviousQuarterEnd', () => {
    it('should return end date of previous quarter', () => {
      expect(getPreviousQuarterEnd(2024, 2)).toEqual(new Date('2024-03-31'));
      expect(getPreviousQuarterEnd(2024, 3)).toEqual(new Date('2024-06-30'));
      expect(getPreviousQuarterEnd(2024, 4)).toEqual(new Date('2024-09-30'));
      expect(getPreviousQuarterEnd(2024, 1)).toEqual(new Date('2023-12-31'));
    });

    it('should handle year transition', () => {
      const prevEnd = getPreviousQuarterEnd(2024, 1);
      expect(prevEnd.getFullYear()).toBe(2023);
      expect(prevEnd.getMonth()).toBe(11); // December
      expect(prevEnd.getDate()).toBe(31);
    });
  });

  describe('parseQuarterString', () => {
    it('should parse quarter string formats', () => {
      expect(parseQuarterString('Q2 2024')).toEqual({ year: 2024, quarter: 2 });
      expect(parseQuarterString('2024-Q2')).toEqual({ year: 2024, quarter: 2 });
      expect(parseQuarterString('2024Q2')).toEqual({ year: 2024, quarter: 2 });
    });

    it('should handle financial year format', () => {
      expect(parseQuarterString('Q3 FY23-24')).toEqual({ 
        year: 2023, 
        quarter: 3,
        isFinancialYear: true 
      });
    });

    it('should handle invalid formats', () => {
      expect(() => parseQuarterString('invalid')).toThrow('Invalid quarter format');
      expect(() => parseQuarterString('Q5 2024')).toThrow('Invalid quarter number');
      expect(() => parseQuarterString('Q2 20240')).toThrow('Invalid year');
    });

    it('should handle lowercase and spaces', () => {
      expect(parseQuarterString('q2 2024')).toEqual({ year: 2024, quarter: 2 });
      expect(parseQuarterString(' Q2  2024 ')).toEqual({ year: 2024, quarter: 2 });
    });
  });

  describe('Quarter Comparisons', () => {
    it('should compare quarters correctly', () => {
      const q1_2024 = { year: 2024, quarter: 1 };
      const q2_2024 = { year: 2024, quarter: 2 };
      const q1_2025 = { year: 2025, quarter: 1 };

      expect(compareQuarters(q1_2024, q2_2024)).toBe(-1);
      expect(compareQuarters(q2_2024, q1_2024)).toBe(1);
      expect(compareQuarters(q1_2024, q1_2024)).toBe(0);
      expect(compareQuarters(q1_2024, q1_2025)).toBe(-1);
    });

    it('should calculate quarters between', () => {
      const start = { year: 2024, quarter: 1 };
      const end = { year: 2024, quarter: 4 };

      expect(quartersBetween(start, end)).toBe(3);
      expect(quartersBetween(start, { year: 2025, quarter: 1 })).toBe(4);
    });
  });

  describe('Business Logic', () => {
    it('should determine if quarter is closed for reporting', () => {
      jest.setSystemTime(new Date('2024-07-15')); // Q3 2024

      expect(isQuarterClosed(2024, 2)).toBe(true);
      expect(isQuarterClosed(2024, 3)).toBe(false);
      expect(isQuarterClosed(2024, 4)).toBe(false);
    });

    it('should get reporting deadline for quarter', () => {
      const deadline = getQuarterReportingDeadline(2024, 2);
      
      // Typically 30 days after quarter end
      expect(deadline).toEqual(new Date('2024-07-30'));
    });

    it('should handle tax quarters differently', () => {
      const taxQuarters = getTaxQuarters(2024);
      
      // BAS quarters might have different deadlines
      expect(taxQuarters[0].basDeadline).toEqual(new Date('2024-04-28'));
      expect(taxQuarters[0].paymentDeadline).toEqual(new Date('2024-04-21'));
    });
  });
});

// Helper functions that might be part of the utils
function compareQuarters(q1: any, q2: any): number {
  if (q1.year !== q2.year) {
    return q1.year - q2.year;
  }
  return q1.quarter - q2.quarter;
}

function quartersBetween(start: any, end: any): number {
  return (end.year - start.year) * 4 + (end.quarter - start.quarter);
}

function isQuarterClosed(year: number, quarter: number): boolean {
  const current = getCurrentQuarter();
  return compareQuarters({ year, quarter }, current) < 0;
}

function getQuarterReportingDeadline(year: number, quarter: number): Date {
  const { end } = getQuarterDateRange(year, quarter);
  const deadline = new Date(end);
  deadline.setDate(deadline.getDate() + 30);
  return deadline;
}

function getTaxQuarters(year: number): any[] {
  return getFinancialYearQuarters(year - 1).map(q => ({
    ...q,
    basDeadline: new Date(q.endDate.getFullYear(), q.endDate.getMonth() + 1, 28),
    paymentDeadline: new Date(q.endDate.getFullYear(), q.endDate.getMonth() + 1, 21)
  }));
}