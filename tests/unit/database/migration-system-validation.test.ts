/**
 * Migration System Validation Tests
 * 
 * These tests validate the migration system and ensure the database schema
 * is correctly created from the migration files.
 */

import BetterSqlite3 from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';

describe('Migration System Validation Tests', () => {
  let db: BetterSqlite3.Database;
  
  beforeEach(() => {
    // Create a fresh in-memory database for each test
    db = new BetterSqlite3(':memory:');
    
    // Enable foreign keys
    db.pragma('foreign_keys = ON');
  });
  
  afterEach(() => {
    db.close();
  });

  describe('Migration File Execution', () => {
    it('should execute the main migration file successfully', () => {
      // Load and execute the main migration file
      const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
      expect(fs.existsSync(migrationPath)).toBe(true);
      
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      expect(() => {
        db.exec(migrationSQL);
      }).not.toThrow();
      
      // Verify migrations table was created and populated
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='migrations'
      `).all();
      
      expect(tables).toHaveLength(1);
      expect(tables[0].name).toBe('migrations');
      
      // Verify migration record was inserted
      const migrationRecord = db.prepare('SELECT * FROM migrations WHERE id = ?').get('001');
      expect(migrationRecord).toBeDefined();
      expect(migrationRecord.name).toBe('corrected_initial_schema_with_text_uuids');
    });
    
    it('should create all expected tables from migration', () => {
      // Execute migration
      const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
      
      // Check that core tables were created
      const expectedTables = [
        'company', 'contact', 'deal', 'estimate', 'note',
        'contact_company', 'contact_role', 'deal_estimate',
        'activity_feed', 'field_ownership', 'change_log',
        'expense', 'cashflow_snapshot',
        'hubspot_settings', 'harvest_invoice_cache', 'settings',
        'estimate_allocation', 'estimate_time_allocation',
        'company_relationship', 'migrations'
      ];
      
      for (const tableName of expectedTables) {
        const tables = db.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name=?
        `).all(tableName);
        expect(tables).toHaveLength(1);
      }
    });
  });

  describe('Schema Structure Validation', () => {
    beforeEach(() => {
      // Execute migration for structure tests
      const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    });
    
    it('should create tables with correct structure', () => {
      // Verify company table structure
      const companyColumns = db.prepare('PRAGMA table_info(company)').all();
      const companyColumnNames = companyColumns.map((col: any) => col.name);
      
      expect(companyColumnNames).toContain('id');
      expect(companyColumnNames).toContain('name');
      expect(companyColumnNames).toContain('hubspot_id');
      expect(companyColumnNames).toContain('harvest_id');
      expect(companyColumnNames).toContain('source');
      expect(companyColumnNames).toContain('radar_state');
      expect(companyColumnNames).toContain('priority');
      expect(companyColumnNames).toContain('current_spend');
      expect(companyColumnNames).toContain('potential_spend');
      expect(companyColumnNames).toContain('notes');
      expect(companyColumnNames).toContain('contacts');
      expect(companyColumnNames).toContain('created_at');
      expect(companyColumnNames).toContain('updated_at');
      
      // Verify contact table structure
      const contactColumns = db.prepare('PRAGMA table_info(contact)').all();
      const contactColumnNames = contactColumns.map((col: any) => col.name);
      
      expect(contactColumnNames).toContain('id');
      expect(contactColumnNames).toContain('first_name');
      expect(contactColumnNames).toContain('last_name');
      expect(contactColumnNames).toContain('email');
      expect(contactColumnNames).toContain('hubspot_id');
      expect(contactColumnNames).toContain('harvest_user_id');
      expect(contactColumnNames).toContain('notes');
      
      // Verify deal table structure
      const dealColumns = db.prepare('PRAGMA table_info(deal)').all();
      const dealColumnNames = dealColumns.map((col: any) => col.name);
      
      expect(dealColumnNames).toContain('id');
      expect(dealColumnNames).toContain('name');
      expect(dealColumnNames).toContain('company_id');
      expect(dealColumnNames).toContain('stage');
      expect(dealColumnNames).toContain('value');
      expect(dealColumnNames).toContain('probability');
      expect(dealColumnNames).toContain('expected_close_date');
      expect(dealColumnNames).toContain('hubspot_id');
      expect(dealColumnNames).toContain('harvest_project_id');
    });
    
    it('should create foreign key relationships correctly', () => {
      // Test deal -> company foreign key
      const now = new Date().toISOString();
      const companyId = uuidv4();
      const dealId = uuidv4();
      
      // Insert company
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(companyId, 'Test Company', now, now, 'test', 'test');
      
      // Insert deal
      db.prepare(`
        INSERT INTO deal (id, name, company_id, stage, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(dealId, 'Test Deal', companyId, 'discovery', now, now, 'test', 'test');
      
      // Verify the relationship works
      const deal = db.prepare('SELECT * FROM deal WHERE id = ?').get(dealId);
      expect(deal).toBeDefined();
      expect(deal.company_id).toBe(companyId);
      
      // Test foreign key constraint
      expect(() => {
        db.prepare(`
          INSERT INTO deal (id, name, company_id, stage, created_at, updated_at, created_by, updated_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).run(uuidv4(), 'Invalid Deal', 'nonexistent-company', 'discovery', now, now, 'test', 'test');
      }).toThrow();
    });
    
    it('should have TEXT UUIDs as primary keys', () => {
      const mainTables = ['company', 'contact', 'deal', 'estimate', 'note', 'activity_feed'];
      
      for (const tableName of mainTables) {
        const columns = db.prepare(`PRAGMA table_info(${tableName})`).all();
        const idColumn = columns.find((col: any) => col.name === 'id' && col.pk === 1);
        
        expect(idColumn).toBeDefined();
        expect(idColumn.type).toBe('TEXT');
      }
    });
  });

  describe('Junction Table Functionality', () => {
    beforeEach(() => {
      // Execute migration for junction table tests
      const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    });
    
    it('should handle contact-company relationships correctly', () => {
      const now = new Date().toISOString();
      const companyId = uuidv4();
      const contactId = uuidv4();
      
      // Create test data
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(companyId, 'Test Company', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO contact (id, first_name, last_name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(contactId, 'John', 'Doe', now, now, 'test', 'test');
      
      // Create relationship
      db.prepare(`
        INSERT INTO contact_company (contact_id, company_id, role, is_primary, created_at)
        VALUES (?, ?, ?, ?, ?)
      `).run(contactId, companyId, 'employee', 1, now);
      
      // Verify relationship
      const relationship = db.prepare(`
        SELECT * FROM contact_company 
        WHERE contact_id = ? AND company_id = ?
      `).get(contactId, companyId);
      
      expect(relationship).toBeDefined();
      expect(relationship.role).toBe('employee');
      expect(relationship.is_primary).toBe(1);
    });
    
    it('should handle contact roles in deals correctly', () => {
      const now = new Date().toISOString();
      const companyId = uuidv4();
      const dealId = uuidv4();
      const contactId = uuidv4();
      
      // Create test data
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(companyId, 'Test Company', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO deal (id, name, company_id, stage, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(dealId, 'Test Deal', companyId, 'discovery', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO contact (id, first_name, last_name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(contactId, 'John', 'Doe', now, now, 'test', 'test');
      
      // Create contact role in deal using contact_role table
      db.prepare(`
        INSERT INTO contact_role (contact_id, deal_id, role, created_at)
        VALUES (?, ?, ?, ?)
      `).run(contactId, dealId, 'decision_maker', now);
      
      // Verify relationship
      const relationship = db.prepare(`
        SELECT * FROM contact_role 
        WHERE deal_id = ? AND contact_id = ?
      `).get(dealId, contactId);
      
      expect(relationship).toBeDefined();
      expect(relationship.role).toBe('decision_maker');
    });
    
    it('should handle deal-estimate linking correctly with composite primary key', () => {
      const now = new Date().toISOString();
      const companyId = uuidv4();
      const dealId = uuidv4();
      const estimateId = uuidv4();
      
      // Create test data
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(companyId, 'Test Company', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO deal (id, name, company_id, stage, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(dealId, 'Test Deal', companyId, 'discovery', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO estimate (id, company_id, client_name, status, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(estimateId, companyId, 'Test Client', 'draft', now, now, 'test', 'test');
      
      // Link deal to estimate with composite primary key
      db.prepare(`
        INSERT INTO deal_estimate (deal_id, estimate_id, estimate_type, linked_at, linked_by, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(dealId, estimateId, 'internal', now, 'test', now);
      
      // Verify link
      const link = db.prepare(`
        SELECT * FROM deal_estimate 
        WHERE deal_id = ? AND estimate_id = ? AND estimate_type = ?
      `).get(dealId, estimateId, 'internal');
      
      expect(link).toBeDefined();
      expect(link.estimate_type).toBe('internal');
    });
    
    it('should handle company relationships correctly', () => {
      const now = new Date().toISOString();
      const parentCompanyId = uuidv4();
      const childCompanyId = uuidv4();
      
      // Create test companies
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(parentCompanyId, 'Parent Company', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(childCompanyId, 'Child Company', now, now, 'test', 'test');
      
      // Create relationship
      db.prepare(`
        INSERT INTO company_relationship (parent_company_id, child_company_id, relationship_type, created_at, created_by)
        VALUES (?, ?, ?, ?, ?)
      `).run(parentCompanyId, childCompanyId, 'parent', now, 'test');
      
      // Verify relationship
      const relationship = db.prepare(`
        SELECT * FROM company_relationship 
        WHERE parent_company_id = ? AND child_company_id = ?
      `).get(parentCompanyId, childCompanyId);
      
      expect(relationship).toBeDefined();
      expect(relationship.relationship_type).toBe('parent');
    });
  });

  describe('Index and Constraint Validation', () => {
    beforeEach(() => {
      // Execute migration for index tests
      const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    });
    
    it('should create critical indexes', () => {
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      `).all() as Array<{name: string}>;
      
      const indexNames = indexes.map(idx => idx.name);
      
      // Check some critical indexes
      const criticalIndexes = [
        'idx_company_hubspot_id',
        'idx_company_harvest_id',
        'idx_contact_hubspot_id',
        'idx_contact_harvest_user_id',
        'idx_deal_hubspot_id',
        'idx_deal_company_id',
        'idx_activity_feed_entity',
        'idx_activity_feed_created_at'
      ];
      
      for (const indexName of criticalIndexes) {
        expect(indexNames).toContain(indexName);
      }
    });
    
    it('should have proper foreign key constraints', () => {
      // Test deal -> company foreign key
      const dealForeignKeys = db.prepare('PRAGMA foreign_key_list(deal)').all();
      const companyFK = dealForeignKeys.find((fk: any) => fk.from === 'company_id' && fk.table === 'company');
      
      expect(companyFK).toBeDefined();
      expect(companyFK.to).toBe('id');
      expect(companyFK.on_delete).toBe('CASCADE');
      
      // Test estimate -> company foreign key
      const estimateForeignKeys = db.prepare('PRAGMA foreign_key_list(estimate)').all();
      const estimateCompanyFK = estimateForeignKeys.find((fk: any) => fk.from === 'company_id' && fk.table === 'company');
      
      expect(estimateCompanyFK).toBeDefined();
      expect(estimateCompanyFK.to).toBe('id');
      expect(estimateCompanyFK.on_delete).toBe('CASCADE');
    });

    it('should validate repository queries work correctly', () => {
      // Test that common repository queries can be prepared without errors
      const testQueries = [
        'SELECT id, name, hubspot_id, harvest_id FROM company WHERE id = ?',
        'SELECT id, first_name, last_name, email FROM contact WHERE id = ?',
        'SELECT id, name, stage, value FROM deal WHERE company_id = ?',
        'SELECT deal_id, estimate_id, estimate_type FROM deal_estimate WHERE deal_id = ?',
        'SELECT contact_id, company_id, role, is_primary FROM contact_company WHERE contact_id = ?',
        'SELECT parent_company_id, child_company_id, relationship_type FROM company_relationship WHERE parent_company_id = ?'
      ];

      for (const query of testQueries) {
        expect(() => {
          db.prepare(query);
        }).not.toThrow();
      }
    });
  });

  describe('Data Integrity Tests', () => {
    beforeEach(() => {
      // Execute migration for data integrity tests
      const migrationPath = path.join(__dirname, '../../../migrations/001_initial_schema.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      db.exec(migrationSQL);
    });

    it('should enforce foreign key constraints', () => {
      const now = new Date().toISOString();
      
      // Try to insert a deal with a non-existent company
      expect(() => {
        db.prepare(`
          INSERT INTO deal (id, name, company_id, stage, created_at, updated_at, created_by, updated_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).run(uuidv4(), 'Test Deal', 'nonexistent-company', 'discovery', now, now, 'test', 'test');
      }).toThrow();
    });

    it('should support cascade deletes', () => {
      const now = new Date().toISOString();
      const companyId = uuidv4();
      const dealId = uuidv4();
      const contactId = uuidv4();
      
      // Create test data
      db.prepare(`
        INSERT INTO company (id, name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(companyId, 'Test Company', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO contact (id, first_name, last_name, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(contactId, 'John', 'Doe', now, now, 'test', 'test');
      
      db.prepare(`
        INSERT INTO deal (id, name, company_id, stage, created_at, updated_at, created_by, updated_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).run(dealId, 'Test Deal', companyId, 'discovery', now, now, 'test', 'test');
      
      // Create junction table relationships
      db.prepare(`
        INSERT INTO contact_company (contact_id, company_id, role, created_at)
        VALUES (?, ?, ?, ?)
      `).run(contactId, companyId, 'employee', now);
      
      db.prepare(`
        INSERT INTO contact_role (contact_id, deal_id, role, created_at)
        VALUES (?, ?, ?, ?)
      `).run(contactId, dealId, 'decision_maker', now);
      
      // Verify relationships exist
      expect(db.prepare('SELECT * FROM deal WHERE id = ?').get(dealId)).toBeDefined();
      expect(db.prepare('SELECT * FROM contact_company WHERE contact_id = ? AND company_id = ?').get(contactId, companyId)).toBeDefined();
      expect(db.prepare('SELECT * FROM contact_role WHERE contact_id = ? AND deal_id = ?').get(contactId, dealId)).toBeDefined();
      
      // Delete company - should cascade to deal and junction tables
      db.prepare('DELETE FROM company WHERE id = ?').run(companyId);
      
      // Verify cascaded deletions
      expect(db.prepare('SELECT * FROM deal WHERE id = ?').get(dealId)).toBeUndefined();
      expect(db.prepare('SELECT * FROM contact_company WHERE contact_id = ? AND company_id = ?').get(contactId, companyId)).toBeUndefined();
      expect(db.prepare('SELECT * FROM contact_role WHERE contact_id = ? AND deal_id = ?').get(contactId, dealId)).toBeUndefined();
      
      // Contact should still exist (no CASCADE from company)
      expect(db.prepare('SELECT * FROM contact WHERE id = ?').get(contactId)).toBeDefined();
    });
  });
});