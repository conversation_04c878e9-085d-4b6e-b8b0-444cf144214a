import React, { useState, useEffect } from "react"; import { useLocation, useNavigate } from "react-router-dom"; import RadarPage from "../../Leads/Radar/RadarPage"; import { KnowledgeGraph } from "../../Leads/KnowledgeGraph"; /** * Main Intelligence page component with tabs for different sections */ const IntelligencePage: React.FC = () => { const location = useLocation(); const navigate = useNavigate(); // Determine active tab based on URL const getActiveTab = () => { const path = location.pathname; if (path.includes("/knowledge-graph")) return "knowledge-graph"; return "radar"; // Default tab (Client Radar) }; const [activeTab, setActiveTab] = useState(getActiveTab()); // Update active tab when location changes useEffect(() => { setActiveTab(getActiveTab()); }, [location.pathname]); // Handle tab change const handleTabChange = (tab: string) => { setActiveTab(tab); if (tab === "radar") { navigate(`/crm/intelligence`); } else { navigate(`/crm/intelligence/${tab}`); } }; // Render the appropriate content based on the active tab const renderContent = () => { switch (activeTab) { case "knowledge-graph": return <KnowledgeGraph />; default: return <RadarPage />; } }; const tabs = [ { id: 'radar', label: 'Client Radar' }, { id: 'knowledge-graph', label: 'Knowledge Graph' } ]; return ( <div className="min-h-[calc(100vh-200px)] flex flex-col bg-gray-50 dark:bg-gray-900"> {/* Header */} <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex-shrink-0"> <div className="max-w-[84rem] mx-auto px-4 md:px-6 py-4"> <div className="flex items-center justify-between"> <div> <h2 className="text-xl font-semibold text-gray-900 dark:text-white"> Intelligence </h2> <p className="text-sm text-gray-600 dark:text-gray-400 mt-1"> Business intelligence and relationship insights </p> </div> </div> {/* Tabs navigation */} <div className="mt-4"> <nav className="flex -mb-px"> {tabs.map(tab => ( <button key={tab.id} className={`py-2 px-4 text-sm font-medium border-b-2 transition-colors ${ activeTab === tab.id ? 'border-purple-500 text-purple-600 dark:text-purple-400' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }`} onClick={() => handleTabChange(tab.id)} > {tab.label} </button> ))} </nav> </div> </div> </div> {/* Main content */} <div className="flex-1 p-4"> <div className={`${activeTab === 'knowledge-graph' ? 'h-full' : 'max-w-[84rem] mx-auto'}`}> {renderContent()} </div> </div> </div> ); }; export default IntelligencePage;