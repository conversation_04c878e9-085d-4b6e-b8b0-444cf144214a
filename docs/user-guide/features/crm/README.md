# CRM and Deal Management

The CRM feature in Upstream provides a comprehensive system for managing deals, contacts, and companies with a modern, intuitive interface designed for financial services businesses.

## Table of Contents

- [Overview](#overview)
- [Key Features](#key-features)
- [CRM Architecture](#crm-architecture)
- [Pipeline View](#pipeline-view)
- [Directory View](#directory-view)
- [Intelligence Dashboard](#intelligence-dashboard)
- [HubSpot Integration](#hubspot-integration)
- [Data Management](#data-management)
- [Command Palette](#command-palette)
- [Contextual Side Panels](#contextual-side-panels)
- [Deal Editing](#deal-editing)
- [Client Radar](#client-radar)
- [Reports](#reports)
- [Tips and Best Practices](#tips-and-best-practices)

## Overview

The CRM feature has been completely overhauled with a modern, three-section architecture that provides an intuitive and efficient way to manage your sales pipeline, client relationships, and business intelligence. The new design focuses on keyboard-first navigation, contextual information display, and seamless integration with external systems.

## Key Features

### New Architecture Features
- **Three-Section Layout**: Pipeline, Directory, and Intelligence sections for organized workflow
- **Command Palette**: Universal search and navigation with Cmd+K shortcut
- **Contextual Side Panels**: View and edit entities without losing context
- **Unified Search**: Search across all entities (deals, contacts, companies) from one place
- **Modern UI**: Clean, responsive design with dark mode support
- **Keyboard Navigation**: Efficient workflow with keyboard shortcuts
- **Real-time Updates**: Live data synchronization across all views

### Core CRM Features
- **Enhanced Deal Pipeline**: Modern Kanban board with multiple view modes
- **Comprehensive Deal Management**: Full deal lifecycle management with custom fields
- **Client Radar**: Strategic view of client portfolio with visual quadrants
- **Contact & Company Management**: Unified directory with powerful search
- **HubSpot Integration**: Seamless import and synchronization of data
- **Data Management**: Centralized control over cross-system data linking
- **Activity Feed**: Track all CRM activities and changes (coming soon)

## CRM Architecture

The new CRM uses a three-section architecture accessible through the main CRM navigation:

### 1. Pipeline Section
The Pipeline section focuses on deal management with an enhanced Kanban board interface.

### 2. Directory Section
The Directory section provides unified access to all CRM entities (contacts, companies, deals) with powerful search and filtering.

### 3. Intelligence Section
The Intelligence section offers business insights, analytics, and strategic views like the Client Radar (coming soon).

### Additional Tools
- **HubSpot Sync**: Accessible via secondary navigation for data import/export
- **Data Management**: Centralized interface for managing cross-system links

## Pipeline View

The Pipeline view features an enhanced deal board with modern functionality:

### Enhanced Deal Board Features

- **Multiple View Modes**:
  - **Board View**: Traditional Kanban board with drag-and-drop
  - **Table View**: Spreadsheet-like view for bulk operations (coming soon)
  - **Calendar View**: Timeline-based view of deals (coming soon)
  - **Forecast View**: Financial projection view (coming soon)

- **Quick Filters**:
  - All Deals
  - My Deals
  - High Value (>$50k)
  - Closing This Month
  - At Risk

- **Compact View Toggle**: Switch between detailed and compact card layouts

- **Real-time Metrics Bar**:
  - Total Pipeline Value
  - Weighted Pipeline Value
  - Average Deal Size
  - Win Rate

- **Deal Cards Display**:
  - Deal name and company
  - Deal value and probability
  - Stage progress indicator
  - Owner avatar
  - Days since creation
  - Expected close date
  - Visual indicators for high value, closing soon, and old deals

### Using the Pipeline

1. **Quick Add Deal**: Click the "Quick Add Deal" button or use keyboard shortcut
2. **Move Deals**: Drag cards between stage columns
3. **Filter Deals**: Use quick filter buttons or search bar
4. **Change Views**: Toggle between board, table, calendar, and forecast views
5. **Toggle Compact Mode**: Use the compact view button for more deals on screen

### Deal Stages

The pipeline uses the following stages (matching HubSpot):
- Identified
- Qualified
- Solution proposal
- Solution presentation
- Objection handling
- Finalising terms
- Closed won
- Closed lost
- Abandoned

## Directory View

The Directory provides unified search and management of all CRM entities:

### Unified Search Features

- **Single Search Box**: Search everything from one place
- **Entity Filter Pills**: Filter by All, Contacts, Companies, or Deals
- **Visual Results Grid**: Results displayed in organized columns
- **Instant Navigation**: Click any result to view details
- **Smart Matching**: Searches across multiple fields per entity type

### Search Capabilities

- **Contacts**: Search by name, email, or phone
- **Companies**: Search by name or industry
- **Deals**: Search by name or associated company
- **Result Limits**: Shows top 5 results per category

### Using the Directory

1. **Access Directory**: Navigate to CRM > Directory
2. **Search Everything**: Type in the search box to find any entity
3. **Filter Results**: Use entity pills to focus on specific types
4. **View Details**: Click any result to open in side panel
5. **Take Action**: Use quick actions from search results

## Intelligence Dashboard

The Intelligence section provides strategic insights and analytics powered by AI:

### Opportunity Intelligence

The system automatically analyzes your CRM data to identify business opportunities:

#### Available Intelligence Features

1. **Coverage Risk Assessment**
   - Identifies companies with insufficient team coverage
   - Highlights relationships that need attention
   - Provides team assignment recommendations
   - Risk scoring: High (85+), Medium (60-84), Low (<60)

2. **Project Renewal Opportunities**
   - Monitors Harvest projects approaching completion
   - Identifies renewal opportunities 30 days in advance
   - Shows project budget and timeline information
   - Provides conversation starters for client discussions

3. **Network Expansion Analysis**
   - Analyzes relationship network size and connections
   - Identifies companies with strong referral potential (5+ connections)
   - Maps relationship types and connection strengths
   - Suggests expansion strategies based on network analysis

4. **Engagement Risk Monitoring** (Coming Soon)
   - Tracks interaction frequency across team members
   - Identifies relationships at risk of going stale
   - Provides proactive engagement recommendations

#### Using Opportunity Intelligence

1. **Access Intelligence**: Navigate to CRM > Intelligence or view company-specific insights in detail pages
2. **Review Opportunities**: Each opportunity shows:
   - Priority score (1-100)
   - Opportunity type (renewal, expansion, risk)
   - Actionable description
   - Relevant metadata and context
   - Detection timestamp
3. **Take Action**: Use the insights to:
   - Schedule client meetings for renewal discussions
   - Assign team members to improve coverage
   - Leverage network connections for referrals
   - Plan proactive engagement strategies

### Project History Tracking

For contacts linked to Harvest users, the system provides detailed project history:

#### Features

- **12-month Activity Timeline**: Shows all projects worked on in the last year
- **Project Summaries**: Hours logged, date ranges, tasks performed
- **Client Work Overview**: Visual breakdown of work across different clients
- **Task Analysis**: Detailed breakdown of task types and time allocation

#### Using Project History

1. **View Contact Details**: Open any contact linked to a Harvest user
2. **Review Project Tab**: See comprehensive work history
3. **Analyze Patterns**: Identify collaboration opportunities and expertise areas
4. **Plan Assignments**: Use historical data for future project planning

### Relationship Network Visualization

The system tracks and visualizes professional relationships:

#### Network Features

- **Relationship Mapping**: Visual representation of contact connections
- **Team Coverage Analysis**: Shows which team members know which contacts
- **Relationship Types**: Tracks how contacts are connected (colleague, introduced_by, etc.)
- **Strength Indicators**: Visual representation of relationship strength

### Planned Intelligence Features

- **Sales Analytics**: Conversion rates, pipeline velocity, win/loss analysis
- **Revenue Forecasting**: AI-powered predictions based on historical data
- **Activity Analytics**: Team performance and activity tracking
- **Custom Dashboards**: Build your own insight views
- **Predictive Scoring**: ML-based opportunity scoring

## HubSpot Integration

The HubSpot integration has been enhanced with better navigation and import capabilities:

### Accessing HubSpot Sync

1. Navigate to **CRM > HubSpot Sync** (in secondary navigation)
2. Configure your HubSpot connection
3. Import or sync data

### Import Process

1. **Connect HubSpot**: Enter your HubSpot access token
2. **Import Options**:
   - Import All Data (recommended for first sync)
   - Import Deals Only
   - Import Contacts Only
   - Import Companies Only
3. **Monitor Progress**: Real-time import progress tracking
4. **View Results**: See summary of imported records

### Important Notes

- Imported deals must have associated companies to display properly
- Deal stages are mapped from HubSpot to match the pipeline
- Custom fields are preserved during import
- Use Data Management to link companies between systems after import

## Data Management

Data Management provides centralized control over cross-system data linking:

### Accessing Data Management

Navigate to **CRM > Data Management** (in secondary navigation)

### Key Features

- **Company Linking Table**: View all companies and their linking status
- **Link/Unlink Actions**: Connect companies to HubSpot or Harvest
- **Statistics Dashboard**: Overview of linking status distribution
- **Search and Filter**: Find specific companies quickly

### Linking Workflow

1. **Review Unlinked Companies**: Focus on companies needing connections
2. **Link to HubSpot**: Select matching HubSpot company from dropdown
3. **Link to Harvest**: Select matching Harvest client from dropdown
4. **Verify Links**: Ensure correct associations
5. **Monitor Status**: Track linking progress via statistics

## Command Palette

The Command Palette provides universal search and quick actions:

### Using Command Palette

1. **Open**: Press `Cmd+K` (Mac) or `Ctrl+K` (Windows/Linux)
2. **Search**: Start typing to search deals, contacts, companies
3. **Navigate**: Use arrow keys to select results
4. **Execute**: Press Enter to open selected item
5. **Close**: Press Escape to close palette

### Available Actions

- Search and open any deal, contact, or company
- Create new deal (type "create deal")
- Create new contact (type "create contact")
- Quick navigation to any CRM section

## Contextual Side Panels

Side panels provide detailed views without losing context:

### Side Panel Features

- **Slide-in Design**: Opens from the right without covering the main view
- **Multiple Tabs**: Overview, Activity, Contacts, Notes
- **Inline Editing**: Edit fields directly in the panel
- **Quick Actions**: Create activities, send emails, add notes
- **Full View Link**: Option to open in full page view

### Using Side Panels

1. **Open Panel**: Click any entity in search results or pipeline
2. **Navigate Tabs**: Switch between Overview, Activity, Contacts, Notes
3. **Edit Fields**: Click any field to edit inline
4. **Save Changes**: Changes save automatically
5. **Close Panel**: Click X or press Escape

## Deal Editing

The deal editing experience has been enhanced with the new architecture:

### Accessing Deal Edit

1. **From Pipeline**: Click a deal card, then "Open Full View" in side panel
2. **From Directory**: Search for deal and click "Open Full View"
3. **From Command Palette**: Search and select deal

### Deal Information

All deal fields are properly mapped to the database structure:
- **name**: Deal name
- **stage**: Current pipeline stage
- **value**: Deal value (previously "amount")
- **companyName**: Associated company name
- **probability**: Win probability percentage
- **expectedCloseDate**: When the deal is expected to close
- **owner**: Deal owner name

### Field Ownership

The system respects field ownership from different sources:
- **Manual Fields**: Editable directly in the UI
- **HubSpot Fields**: Updated via HubSpot sync
- **Estimate Fields**: Updated when estimates are linked

## Client Radar

The Client Radar remains accessible through the legacy navigation structure and provides the same strategic portfolio view with enhanced UI improvements as previously documented.

## Reports

CRM reports are being redesigned for the new architecture. The existing reports remain available through the legacy navigation while new analytics are developed for the Intelligence section.

## Tips and Best Practices

### Navigation Tips
- **Use Cmd+K**: Fastest way to find and open any CRM record
- **Bookmark Views**: Save frequently used filters and views
- **Keyboard Shortcuts**: Learn shortcuts for common actions
- **Side Panels**: Use for quick edits without losing context

### Data Management Best Practices
- **Import Order**: Import companies first, then deals, then contacts
- **Link Companies**: Use Data Management after imports to link systems
- **Regular Syncs**: Schedule regular HubSpot syncs for data freshness
- **Field Mapping**: Understand which fields map between systems

### Pipeline Management
- **Stage Definitions**: Ensure team alignment on stage criteria
- **Probability Rules**: Set clear guidelines for probability assignment
- **Regular Reviews**: Weekly pipeline reviews using different view modes
- **Quick Filters**: Use filters to focus on deals needing attention

### Common Issues and Solutions

1. **Deals Not Showing After Import**:
   - Ensure deals have associated companies
   - Check that deal stages match expected values
   - Verify import completed successfully

2. **Missing Companies in Deals**:
   - Import companies before deals
   - Use Data Management to link companies
   - Check company associations in HubSpot

3. **Field Updates Not Saving**:
   - Check field ownership indicators
   - Update through the owning system
   - Verify you have edit permissions

4. **Search Not Finding Records**:
   - Try partial name searches
   - Check entity type filters
   - Ensure records were imported successfully