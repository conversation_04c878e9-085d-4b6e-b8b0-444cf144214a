/**
 * Activity API Routes
 *
 * This file defines the REST API endpoints for the activity feed system.
 */

import express, { Request, Response } from 'express';
import { getActivityService } from '../services/activity-service';
import { ActivityFilters, ActivityCreate, ActivityUpdate } from '../../frontend/types/activity-types';

const router = express.Router();
const activityService = getActivityService();

/**
 * GET /api/activity
 * Get activity feed with optional filters
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const filters: ActivityFilters = {};

    // Parse query parameters
    if (req.query.type) {
      filters.type = Array.isArray(req.query.type) ? req.query.type as any[] : [req.query.type as any];
    }

    if (req.query.source) {
      filters.source = Array.isArray(req.query.source) ? req.query.source as any[] : [req.query.source as any];
    }

    if (req.query.entityType) {
      filters.entityType = req.query.entityType as any;
    }

    if (req.query.entityId) {
      filters.entityId = req.query.entityId as string;
    }

    if (req.query.createdBy) {
      filters.createdBy = req.query.createdBy as string;
    }

    if (req.query.isRead !== undefined) {
      filters.isRead = req.query.isRead === 'true';
    }

    if (req.query.importance) {
      filters.importance = req.query.importance as any;
    }

    if (req.query.dateFrom) {
      filters.dateFrom = req.query.dateFrom as string;
    }

    if (req.query.dateTo) {
      filters.dateTo = req.query.dateTo as string;
    }

    if (req.query.search) {
      filters.search = req.query.search as string;
    }

    // Pagination
    if (req.query.limit) {
      filters.limit = parseInt(req.query.limit as string, 10);
    }

    if (req.query.offset) {
      filters.offset = parseInt(req.query.offset as string, 10);
    }

    const result = await activityService.getActivityFeed(filters);
    res.json(result);
  } catch (error) {
    console.error('Error getting activity feed:', error);
    res.status(500).json({ 
      error: 'Failed to get activity feed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/activity/recent
 * Get recent activities
 */
router.get('/recent', async (req: Request, res: Response) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 20;
    const offset = req.query.offset ? parseInt(req.query.offset as string, 10) : 0;

    const activities = await activityService.getRecentActivities(limit, offset);
    res.json(activities);
  } catch (error) {
    console.error('Error getting recent activities:', error);
    res.status(500).json({ 
      error: 'Failed to get recent activities',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/activity/stats
 * Get activity statistics
 */
router.get('/stats', async (req: Request, res: Response) => {
  try {
    const stats = await activityService.getActivityStats();
    res.json(stats);
  } catch (error) {
    console.error('Error getting activity stats:', error);
    res.status(500).json({ 
      error: 'Failed to get activity stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/activity/unread-count
 * Get unread activity count
 */
router.get('/unread-count', async (req: Request, res: Response) => {
  try {
    const userId = req.query.userId as string;
    const count = await activityService.getUnreadCount(userId);
    res.json({ count });
  } catch (error) {
    console.error('Error getting unread count:', error);
    res.status(500).json({ 
      error: 'Failed to get unread count',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/activity/entity/:type/:id
 * Get activities for a specific entity
 */
router.get('/entity/:type/:id', async (req: Request, res: Response) => {
  try {
    const { type, id } = req.params;
    const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 50;

    const activities = await activityService.getActivitiesForEntity(type, id, limit);
    res.json(activities);
  } catch (error) {
    console.error('Error getting activities for entity:', error);
    res.status(500).json({ 
      error: 'Failed to get activities for entity',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/activity/:id
 * Get specific activity by ID
 */
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const activity = await activityService.getActivity(id);

    if (!activity) {
      return res.status(404).json({ error: 'Activity not found' });
    }

    res.json(activity);
  } catch (error) {
    console.error('Error getting activity:', error);
    res.status(500).json({ 
      error: 'Failed to get activity',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/activity
 * Create a new activity
 */
router.post('/', async (req: Request, res: Response) => {
  try {
    const activityData: ActivityCreate = req.body;

    // Validate required fields
    if (!activityData.type || !activityData.subject || !activityData.source || !activityData.createdBy) {
      return res.status(400).json({ 
        error: 'Missing required fields: type, subject, source, createdBy' 
      });
    }

    const activity = await activityService.createActivity(activityData);
    res.status(201).json(activity);
  } catch (error) {
    console.error('Error creating activity:', error);
    res.status(500).json({ 
      error: 'Failed to create activity',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/activity/:id
 * Update an activity
 */
router.put('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData: ActivityUpdate = req.body;

    const activity = await activityService.updateActivity(id, updateData);

    if (!activity) {
      return res.status(404).json({ error: 'Activity not found' });
    }

    res.json(activity);
  } catch (error) {
    console.error('Error updating activity:', error);
    res.status(500).json({ 
      error: 'Failed to update activity',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/activity/mark-read
 * Mark activities as read
 */
router.put('/mark-read', async (req: Request, res: Response) => {
  try {
    const { activityIds } = req.body;

    if (!Array.isArray(activityIds) || activityIds.length === 0) {
      return res.status(400).json({ error: 'activityIds must be a non-empty array' });
    }

    const success = await activityService.markAsRead(activityIds);
    res.json({ success });
  } catch (error) {
    console.error('Error marking activities as read:', error);
    res.status(500).json({ 
      error: 'Failed to mark activities as read',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/activity/:id
 * Delete an activity
 */
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const success = await activityService.deleteActivity(id);

    if (!success) {
      return res.status(404).json({ error: 'Activity not found' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting activity:', error);
    res.status(500).json({ 
      error: 'Failed to delete activity',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
