# Onbord API Reference

## Overview

This document provides a complete reference for all API endpoints in the Onbord application. All endpoints follow RESTful conventions and return JSON responses.

## Base URL

- Development: `http://localhost:3002/api`
- Production: `https://upstream.onbord.au/api`

## Authentication

Most endpoints require authentication via session cookies. OAuth2 is used for external integrations (Xero, HubSpot).

## Core Endpoints

### Companies

#### GET /api/companies
Get all companies with statistics.

**Response:**
```json
[
  {
    "id": "uuid",
    "name": "Company Name",
    "industry": "Technology",
    "hubspotId": "123",
    "harvestId": 456,
    "activeDealsCount": 3,
    "totalDealValue": 150000,
    "contactCount": 5
  }
]
```

#### GET /api/companies/:id
Get company by ID with full details.

#### POST /api/companies
Create new company.

**Body:**
```json
{
  "name": "New Company",
  "industry": "Technology",
  "website": "https://example.com",
  "customFields": {
    "accountManager": "<PERSON>"
  }
}
```

#### PUT /api/companies/:id
Update company.

#### DELETE /api/companies/:id
Soft delete company (sets deleted_at).

### Contacts

#### GET /api/contacts
Get all contacts with company relationships.

**Response:**
```json
[
  {
    "id": "uuid",
    "firstName": "Jane",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "companies": [
      {
        "company": {
          "id": "uuid",
          "name": "Tech Corp"
        },
        "role": "CTO",
        "isPrimary": true
      }
    ]
  }
]
```

#### POST /api/contacts
Create new contact.

#### PUT /api/contacts/:id
Update contact.

#### POST /api/contacts/:id/companies
Link contact to company.

**Body:**
```json
{
  "companyId": "uuid",
  "role": "CTO",
  "isPrimary": true
}
```

### Deals

#### GET /api/deals
Get all deals with filtering options.

**Query Parameters:**
- `stage`: Filter by pipeline stage
- `includeProjections`: Include deals valid for financial projections

#### GET /api/deals/:id
Get deal with full details including linked estimates.

#### POST /api/deals
Create new deal.

**Body:**
```json
{
  "name": "New Opportunity",
  "companyId": "uuid",
  "stage": "qualified",
  "value": 50000,
  "probability": 0.7,
  "expectedCloseDate": "2024-06-30"
}
```

#### PUT /api/deals/:id
Update deal.

**Note:** Some fields may be controlled by linked estimates (see field_ownership).

#### POST /api/deals/:id/estimate
Link estimate to deal (one-to-one relationship).

**Body:**
```json
{
  "estimateId": "uuid"
}
```

### Projects

#### GET /api/projects
Get all projects with optional filters.

**Query Parameters:**
- `status`: Filter by project status (planning, active, paused, completed)
- `companyId`: Filter by company

#### POST /api/projects
Create new project.

**Body:**
```json
{
  "name": "Website Redesign",
  "companyId": "uuid",
  "dealId": "uuid",
  "projectType": "fixed",
  "budget": 50000,
  "startDate": "2024-01-01",
  "endDate": "2024-06-30"
}
```

#### POST /api/projects/:id/team
Add team member to project.

**Body:**
```json
{
  "contactId": "uuid",
  "role": "Senior Developer",
  "allocationPercentage": 80,
  "hourlyRate": 150
}
```

### Financial Transactions

#### GET /api/transactions
Get financial transactions with filtering.

**Query Parameters:**
- `type`: Filter by transaction type (invoice, bill, expense, payroll)
- `startDate`: Start date for range
- `endDate`: End date for range
- `companyId`: Filter by company
- `projectId`: Filter by project

#### POST /api/transactions
Create manual transaction.

**Body:**
```json
{
  "date": "2024-01-15",
  "amount": 5000,
  "description": "Consulting services",
  "type": "invoice",
  "companyId": "uuid",
  "status": "authorised"
}
```

### Estimates

#### GET /api/estimates
Get all estimates.

#### GET /api/estimates/:id
Get estimate with full details including staff allocations.

#### POST /api/estimates
Create new estimate.

**Body:**
```json
{
  "companyId": "uuid",
  "projectName": "New Project",
  "totalHours": 160,
  "totalConsultancy": 20000,
  "staffAllocations": [
    {
      "contactId": "uuid",
      "name": "Jane Doe",
      "role": "Senior Developer",
      "dailyRate": 1200,
      "totalDays": 20,
      "weeklyAllocation": {
        "2024-W01": 5,
        "2024-W02": 5,
        "2024-W03": 5,
        "2024-W04": 5
      }
    }
  ]
}
```

#### PUT /api/estimates/:id/status
Update estimate status.

**Body:**
```json
{
  "status": "sent"
}
```

## Integration Endpoints

### HubSpot

#### POST /api/hubspot/auth
Save HubSpot access token.

**Body:**
```json
{
  "accessToken": "token_value"
}
```

#### POST /api/hubspot/import
Trigger full HubSpot import.

**Response:**
```json
{
  "companies": {
    "created": 10,
    "updated": 5,
    "errors": []
  },
  "contacts": {
    "created": 20,
    "updated": 10,
    "errors": []
  },
  "deals": {
    "created": 5,
    "updated": 3,
    "errors": []
  }
}
```

### Harvest

#### GET /api/harvest/projects
Get projects from Harvest.

#### GET /api/harvest/invoices
Get invoices from Harvest.

**Query Parameters:**
- `clientId`: Filter by Harvest client ID
- `projectId`: Filter by Harvest project ID

#### GET /api/harvest/uninvoiced
Get uninvoiced amounts by project.

### Xero

#### GET /api/xero/auth
Get Xero OAuth authorization URL.

#### POST /api/xero/callback
Handle Xero OAuth callback.

#### GET /api/xero/invoices
Get invoices from Xero.

#### GET /api/xero/bills
Get bills from Xero.

#### GET /api/xero/bank-balances
Get current bank account balances.

## Utility Endpoints

### Activity Feed

#### GET /api/activity
Get activity log entries.

**Query Parameters:**
- `limit`: Number of entries (default: 50)
- `offset`: Pagination offset
- `entityType`: Filter by entity type
- `entityId`: Filter by specific entity

#### POST /api/activity
Log new activity.

**Body:**
```json
{
  "activityType": "deal_updated",
  "description": "Deal stage changed to Solution Proposal",
  "entityType": "deal",
  "entityId": "uuid",
  "metadata": {
    "oldStage": "qualified",
    "newStage": "solution_proposal"
  }
}
```

### Field Ownership

#### GET /api/field-ownership/:entityType/:entityId
Get field ownership for an entity.

**Response:**
```json
{
  "value": "hubspot",
  "stage": "hubspot",
  "paymentTerms": "estimate"
}
```

#### POST /api/field-ownership
Set field ownership.

**Body:**
```json
{
  "entityType": "deal",
  "entityId": "uuid",
  "fieldName": "value",
  "ownerSystem": "estimate",
  "locked": true
}
```

### Enrichment

#### POST /api/enrichment/company/:id
Trigger company enrichment.

**Response:**
```json
{
  "source": "abn_lookup",
  "data": {
    "abn": "***********",
    "entityName": "Company Pty Ltd",
    "status": "Active"
  },
  "confidenceScore": 0.95
}
```

### Cashflow Projections

#### GET /api/cashflow/forecast
Get cashflow forecast with projections.

**Query Parameters:**
- `startDate`: Forecast start date
- `endDate`: Forecast end date
- `includeScenarios`: Include best/worst case scenarios

**Response:**
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "startingBalance": 50000,
  "endingBalance": 125000,
  "dailyCashflow": [
    {
      "date": "2024-01-01",
      "inflows": 10000,
      "outflows": 5000,
      "netFlow": 5000,
      "balance": 55000,
      "transactions": []
    }
  ],
  "projectedTransactions": [],
  "dealContributions": []
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  }
}
```

Common error codes:
- `VALIDATION_ERROR`: Invalid input data
- `NOT_FOUND`: Resource not found
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `CONFLICT`: Resource conflict (e.g., duplicate)
- `INTEGRATION_ERROR`: External system error

## Rate Limiting

API requests are rate limited to prevent abuse:
- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated requests
- Integration endpoints have specific limits based on external API constraints

## Webhooks

Webhook endpoints for external systems:

#### POST /api/webhooks/hubspot
Receive HubSpot webhook events.

#### POST /api/webhooks/xero
Receive Xero webhook events.

Headers must include valid webhook signatures for security.