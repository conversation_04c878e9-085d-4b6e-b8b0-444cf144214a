import React, { forwardRef } from "react"; import { cn, listItemVariants, ListItemVariantProps, } from "../../../utils/component-variants"; export interface ListItemProps extends Omit<React.HTMLAttributes<HTMLDivElement>, "children">, ListItemVariantProps { children: React.ReactNode; selected?: boolean; disabled?: boolean; loading?: boolean; leftIcon?: React.ReactNode; rightIcon?: React.ReactNode; leftContent?: React.ReactNode; rightContent?: React.ReactNode; onClick?: () => void; onKeyDown?: (event: React.KeyboardEvent) => void; } /** * Modern ListItem component with variant system * * Features: * - Type-safe variants (default, interactive, selected, disabled) * - Multiple sizes (compact, default, comfortable) * - Border variants (none, default, left, full) * - Icon and content slots * - Keyboard navigation support * - Accessibility features * - Dark mode support */ export const ListItem = forwardRef<HTMLDivElement, ListItemProps>( ( { className, variant = "default", size = "default", border = "none", children, selected = false, disabled = false, loading = false, leftIcon, rightIcon, leftContent, rightContent, onClick, onKeyDown, ...props }, ref ) => { // Determine effective variant based on state const effectiveVariant = disabled ? "disabled" : selected ? "selected" : onClick ? "interactive" : variant; // Handle keyboard navigation const handleKeyDown = (event: React.KeyboardEvent) => { if (onKeyDown) { onKeyDown(event); } else if (onClick && (event.key === "Enter" || event.key === " ")) { event.preventDefault(); onClick(); } }; const isInteractive = !disabled && (onClick || onKeyDown); return ( <div ref={ref} className={cn( listItemVariants({ variant: effectiveVariant, size, border, }), loading && "opacity-50 pointer-events-none", className )} role={isInteractive ? "button" : "listitem"} tabIndex={isInteractive ? 0 : undefined} aria-selected={selected} aria-disabled={disabled} onClick={disabled ? undefined : onClick} onKeyDown={isInteractive ? handleKeyDown : undefined} {...props} > {/* Left content area */} {(leftIcon || leftContent) && ( <div className="flex items-center space-x-3"> {leftIcon && ( <div className="flex-shrink-0 text-gray-400 dark:text-gray-500"> {leftIcon} </div> )} {leftContent && <div className="flex-shrink-0">{leftContent}</div>} </div> )} {/* Main content */} <div className="flex-1 min-w-0"> {children} </div> {/* Right content area */} {(rightIcon || rightContent) && ( <div className="flex items-center space-x-3"> {rightContent && <div className="flex-shrink-0">{rightContent}</div>} {rightIcon && ( <div className="flex-shrink-0 text-gray-400 dark:text-gray-500"> {rightIcon} </div> )} </div> )} {/* Loading indicator */} {loading && ( <div className="absolute inset-0 flex items-center justify-center bg-white/50 dark:bg-gray-900/50"> <svg className="animate-spin h-4 w-4 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" /> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" /> </svg> </div> )} </div> ); } ); ListItem.displayName = "ListItem"; export default ListItem; 