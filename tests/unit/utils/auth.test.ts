import {
  isAuthenticated,
  hasPermission,
  hasRole,
  checkAuth,
  logout,
  getAuthToken,
  setAuthToken,
  clearAuthToken
} from '../../../src/utils/auth';

describe('Auth Utilities', () => {
  beforeEach(() => {
    // Clear localStorage and sessionStorage
    localStorage.clear();
    sessionStorage.clear();
    
    // Reset fetch mock
    global.fetch = jest.fn();
  });

  describe('isAuthenticated', () => {
    it('should return true when user has valid session', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user: { id: '1', name: 'Test User' } })
      });

      const result = await isAuthenticated();
      expect(result).toBe(true);
    });

    it('should return false when no session exists', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401
      });

      const result = await isAuthenticated();
      expect(result).toBe(false);
    });

    it('should return false on network error', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await isAuthenticated();
      expect(result).toBe(false);
    });
  });

  describe('hasPermission', () => {
    it('should check user permissions', async () => {
      const user = {
        id: '1',
        permissions: ['read_estimates', 'write_estimates']
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user })
      });

      const canRead = await hasPermission('read_estimates');
      const canDelete = await hasPermission('delete_estimates');

      expect(canRead).toBe(true);
      expect(canDelete).toBe(false);
    });

    it('should return false when user has no permissions', async () => {
      const user = { id: '1' };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user })
      });

      const result = await hasPermission('any_permission');
      expect(result).toBe(false);
    });

    it('should handle admin override', async () => {
      const user = {
        id: '1',
        role: 'admin',
        permissions: []
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user })
      });

      const result = await hasPermission('any_permission');
      expect(result).toBe(true); // Admins have all permissions
    });
  });

  describe('hasRole', () => {
    it('should check user role', async () => {
      const user = {
        id: '1',
        role: 'manager'
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user })
      });

      const isManager = await hasRole('manager');
      const isAdmin = await hasRole('admin');

      expect(isManager).toBe(true);
      expect(isAdmin).toBe(false);
    });

    it('should handle multiple roles', async () => {
      const user = {
        id: '1',
        roles: ['viewer', 'editor']
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user })
      });

      const hasViewerRole = await hasRole('viewer');
      const hasEditorRole = await hasRole('editor');
      const hasAdminRole = await hasRole('admin');

      expect(hasViewerRole).toBe(true);
      expect(hasEditorRole).toBe(true);
      expect(hasAdminRole).toBe(false);
    });
  });

  describe('checkAuth', () => {
    it('should redirect to login when not authenticated', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401
      });

      const mockReplace = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { replace: mockReplace },
        writable: true
      });

      await checkAuth();

      expect(mockReplace).toHaveBeenCalledWith('/login?redirect=' + encodeURIComponent(window.location.pathname));
    });

    it('should not redirect when authenticated', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({ user: { id: '1' } })
      });

      const mockReplace = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { replace: mockReplace },
        writable: true
      });

      await checkAuth();

      expect(mockReplace).not.toHaveBeenCalled();
    });

    it('should include custom redirect path', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401
      });

      const mockReplace = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { replace: mockReplace },
        writable: true
      });

      await checkAuth('/dashboard');

      expect(mockReplace).toHaveBeenCalledWith('/login?redirect=%2Fdashboard');
    });
  });

  describe('logout', () => {
    it('should call logout endpoint and redirect', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({ ok: true });

      const mockReplace = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { replace: mockReplace },
        writable: true
      });

      await logout();

      expect(fetch).toHaveBeenCalledWith('/api/auth/logout', {
        method: 'POST',
        credentials: 'include'
      });
      expect(mockReplace).toHaveBeenCalledWith('/login');
    });

    it('should clear local storage on logout', async () => {
      localStorage.setItem('authToken', 'test-token');
      sessionStorage.setItem('userSession', 'test-session');

      (global.fetch as jest.Mock).mockResolvedValue({ ok: true });

      await logout();

      expect(localStorage.getItem('authToken')).toBeNull();
      expect(sessionStorage.getItem('userSession')).toBeNull();
    });

    it('should handle logout errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const mockReplace = jest.fn();
      Object.defineProperty(window, 'location', {
        value: { replace: mockReplace },
        writable: true
      });

      await logout();

      // Should still redirect even on error
      expect(mockReplace).toHaveBeenCalledWith('/login');
    });
  });

  describe('Token Management', () => {
    it('should get auth token from localStorage', () => {
      localStorage.setItem('authToken', 'test-token-123');

      const token = getAuthToken();
      expect(token).toBe('test-token-123');
    });

    it('should return null when no token exists', () => {
      const token = getAuthToken();
      expect(token).toBeNull();
    });

    it('should set auth token in localStorage', () => {
      setAuthToken('new-token-456');

      expect(localStorage.getItem('authToken')).toBe('new-token-456');
    });

    it('should clear auth token', () => {
      localStorage.setItem('authToken', 'token-to-clear');

      clearAuthToken();

      expect(localStorage.getItem('authToken')).toBeNull();
    });

    it('should handle token expiry', () => {
      const expiredToken = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE2MDAwMDAwMDB9.test';
      localStorage.setItem('authToken', expiredToken);

      const token = getAuthToken();
      expect(token).toBeNull(); // Should return null for expired token
    });
  });

  describe('OAuth Integration', () => {
    it('should handle OAuth callback', async () => {
      const mockCode = 'oauth-code-123';
      const mockState = 'oauth-state-456';

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({
          access_token: 'access-token',
          refresh_token: 'refresh-token',
          user: { id: '1', name: 'OAuth User' }
        })
      });

      const result = await handleOAuthCallback(mockCode, mockState);

      expect(fetch).toHaveBeenCalledWith('/api/auth/oauth/callback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: mockCode, state: mockState }),
        credentials: 'include'
      });

      expect(result.success).toBe(true);
      expect(result.user).toEqual({ id: '1', name: 'OAuth User' });
    });

    it('should handle OAuth errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Invalid authorization code' })
      });

      const result = await handleOAuthCallback('invalid-code', 'state');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid authorization code');
    });
  });

  describe('Session Management', () => {
    it('should refresh session before expiry', async () => {
      jest.useFakeTimers();

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({
          user: { id: '1' },
          expiresAt: Date.now() + 3600000 // 1 hour
        })
      });

      startSessionRefresh();

      // Fast forward to just before expiry
      jest.advanceTimersByTime(3300000); // 55 minutes

      expect(fetch).toHaveBeenCalledWith('/api/auth/refresh', {
        method: 'POST',
        credentials: 'include'
      });

      jest.useRealTimers();
    });

    it('should handle session refresh failure', async () => {
      jest.useFakeTimers();

      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            user: { id: '1' },
            expiresAt: Date.now() + 60000 // 1 minute
          })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 401
        });

      const onSessionExpired = jest.fn();
      startSessionRefresh({ onSessionExpired });

      jest.advanceTimersByTime(55000); // 55 seconds

      await Promise.resolve(); // Let async operations complete

      expect(onSessionExpired).toHaveBeenCalled();

      jest.useRealTimers();
    });
  });
});