/**
 * Create HubSpot Settings Table Script
 * 
 * This script creates the hubspot_settings table if it doesn't exist.
 * It's useful for fixing the "no such table: hubspot_settings" error.
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Get database path from environment or use default
const dbPath = process.env.DB_PATH || path.join(__dirname, '..', 'data', 'upstream.db');

// Check if database exists
if (!fs.existsSync(dbPath)) {
  console.error(`Database file not found at ${dbPath}`);
  console.log('Please run the initialize-fresh-database.js script first.');
  process.exit(1);
}

console.log(`Connecting to database at: ${dbPath}`);

// Initialize the database
const db = new BetterSqlite3(dbPath);

// Function to create the hubspot_settings table
function createHubSpotSettingsTable() {
  try {
    // Check if the table already exists
    const tableExists = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='hubspot_settings'
    `).get();

    if (tableExists) {
      console.log('hubspot_settings table already exists.');
      return true;
    }

    console.log('Creating hubspot_settings table...');
    
    // Create the table
    db.prepare(`
      CREATE TABLE hubspot_settings (
        id TEXT PRIMARY KEY,
        access_token TEXT,
        refresh_token TEXT,
        expires_at TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `).run();

    // Insert a default row
    const now = new Date().toISOString();
    const id = uuidv4();
    
    db.prepare(`
      INSERT INTO hubspot_settings (id, created_at, updated_at)
      VALUES (?, ?, ?)
    `).run(id, now, now);

    console.log('hubspot_settings table created successfully.');
    return true;
  } catch (error) {
    console.error('Error creating hubspot_settings table:', error);
    return false;
  }
}

// Function to create the settings table if it doesn't exist
function createSettingsTable() {
  try {
    // Check if the table already exists
    const tableExists = db.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='settings'
    `).get();

    if (tableExists) {
      console.log('settings table already exists.');
      return true;
    }

    console.log('Creating settings table...');
    
    // Create the table
    db.prepare(`
      CREATE TABLE settings (
        id TEXT PRIMARY KEY,
        category TEXT NOT NULL,
        key TEXT NOT NULL,
        value TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        UNIQUE(category, key)
      )
    `).run();

    console.log('settings table created successfully.');
    return true;
  } catch (error) {
    console.error('Error creating settings table:', error);
    return false;
  }
}

// Main function
function main() {
  try {
    // Create the tables
    const hubspotSettingsCreated = createHubSpotSettingsTable();
    const settingsCreated = createSettingsTable();

    if (hubspotSettingsCreated && settingsCreated) {
      console.log('All tables created successfully.');
    } else {
      console.error('Failed to create some tables.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    db.close();
  }
}

// Run the main function
main();
