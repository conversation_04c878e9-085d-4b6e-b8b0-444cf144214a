import React from "react"; import { useProjection, formatCurrency } from "."; /** * Component for cashflow summary cards */ const CashflowSummaryCards: React.FC = () => { const { projectionData, timeframe, totalInflows, totalOutflows } = useProjection(); if (!projectionData) return null; return ( <div className="py-2 px-2 sm:py-3 sm:px-3 md:py-4 md:px-4 onboarding-summary-cards h-full cashflow-cards-single-row"> {/* Mobile-first responsive flex layout - single row on larger screens */} <div className="flex flex-col sm:flex-row flex-wrap gap-1.5 sm:gap-2 md:gap-3 h-full"> {/* Current Balance Card */} <div className="w-full sm:w-1/2 md:w-1/4 mb-1.5 sm:mb-0"> <div className="card-info p-2 sm:p-2 md:p-3 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md flex flex-col justify-between h-full"> <div className="flex items-center mb-0.5 sm:mb-0.5 md:mb-1"> <svg className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 mr-1 sm:mr-1 md:mr-1.5 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="1.5" > <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> <h3 className="text-xs sm:text-xs md:text-sm font-medium text-gray-900 dark:text-gray-100 truncate"> Current Balance </h3> </div> <div> <p className="text-base sm:text-lg md:text-xl font-bold text-primary line-clamp-1"> {formatCurrency(projectionData.startingBalance || 0)} </p> <p className="text-xs text-gray-800 dark:text-gray-400 truncate"> Today </p> </div> </div> </div> {/* Projected Balance Card */} <div className="w-full sm:w-1/2 md:w-1/4 mb-1.5 sm:mb-0"> <div className={`${ (projectionData.endingBalance || 0) >= 0 ? "card-success" : "card-danger" } p-2 sm:p-2 md:p-3 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md flex flex-col justify-between h-full`} > <div className="flex items-center mb-0.5 sm:mb-0.5 md:mb-1"> <svg className={`w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 mr-1 sm:mr-1 md:mr-1.5 ${ (projectionData.endingBalance || 0) >= 0 ? "text-success" : "text-accent" }`} fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2" > <path strokeLinecap="round" strokeLinejoin="round" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" /> </svg> <h3 className="text-xs sm:text-xs md:text-sm font-medium text-gray-900 dark:text-gray-100 truncate"> Projected Balance </h3> </div> <div> <p className={`text-base sm:text-lg md:text-xl font-bold line-clamp-1 ${ (projectionData.endingBalance || 0) >= 0 ? "text-success" : "text-accent" }`} > {formatCurrency(projectionData.endingBalance || 0)} </p> <p className="text-xs text-gray-800 dark:text-gray-400 truncate"> in {timeframe} days </p> </div> </div> </div> {/* Money In Card */} <div className="w-full sm:w-1/2 md:w-1/4 mb-1.5 sm:mb-0"> <div className="card-success p-2 sm:p-2 md:p-3 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md flex flex-col justify-between h-full"> <div className="flex items-center mb-0.5 sm:mb-0.5 md:mb-1"> <svg className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 mr-1 sm:mr-1 md:mr-1.5 text-success" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" /> </svg> <h3 className="text-xs sm:text-xs md:text-sm font-medium text-gray-900 dark:text-gray-100 truncate"> Money In </h3> </div> <div> <p className="text-base sm:text-lg md:text-xl font-bold text-success line-clamp-1"> {formatCurrency(totalInflows)} </p> <p className="text-xs text-gray-800 dark:text-gray-400 truncate"> Incoming payments </p> </div> </div> </div> {/* Money Out Card */} <div className="w-full sm:w-1/2 md:w-1/4 mb-1.5 sm:mb-0"> <div className="card-danger p-2 sm:p-2 md:p-3 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md flex flex-col justify-between h-full"> <div className="flex items-center mb-0.5 sm:mb-0.5 md:mb-1"> <svg className="w-3 h-3 sm:w-3 sm:h-3 md:w-4 md:h-4 mr-1 sm:mr-1 md:mr-1.5 text-accent" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" transform="rotate(180, 10, 10)" /> </svg> <h3 className="text-xs sm:text-xs md:text-sm font-medium text-gray-900 dark:text-gray-100 truncate"> Money Out </h3> </div> <div> <p className="text-base sm:text-lg md:text-xl font-bold text-accent line-clamp-1"> {formatCurrency(totalOutflows)} </p> <p className="text-xs text-gray-800 dark:text-gray-400 truncate"> Outgoing expenses </p> </div> </div> </div> </div> </div> ); }; export default CashflowSummaryCards; 