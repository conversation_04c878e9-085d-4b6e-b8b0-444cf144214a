import { Request, Response, NextFunction } from 'express';
import { validationResult, body, param, query } from 'express-validator';

/**
 * Middleware to handle validation errors
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      type: 'VALIDATION_ERROR',
      details: errors.array(),
      timestamp: new Date().toISOString()
    });
  }
  next();
};

/**
 * Common validation rules
 */
export const validators = {
  // ID validation
  id: param('id')
    .isUUID()
    .withMessage('Invalid ID format'),
  
  // Company validation
  companyId: param('companyId')
    .isUUID()
    .withMessage('Invalid company ID format'),
  
  // Deal validation
  dealId: param('dealId')
    .isUUID()
    .withMessage('Invalid deal ID format'),
  
  // Contact validation
  contactId: param('contactId')
    .isUUID()
    .withMessage('Invalid contact ID format'),
  
  // Pagination validation
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],
  
  // Date range validation
  dateRange: [
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO date'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO date')
  ],
  
  // Activity validation
  activityType: query('type')
    .optional()
    .isArray()
    .withMessage('Type must be an array')
    .bail()
    .custom((types: string[]) => {
      const allowedTypes = [
        'company_created', 'company_updated', 'company_deleted',
        'contact_created', 'contact_updated', 'contact_deleted',
        'deal_created', 'deal_updated', 'deal_deleted',
        'estimate_created', 'estimate_published', 'estimate_updated',
        'xero_sync', 'harvest_sync', 'hubspot_sync',
        'system_event'
      ];
      return types.every(type => allowedTypes.includes(type));
    })
    .withMessage('Invalid activity type'),
  
  // Company creation/update validation
  companyData: [
    body('name')
      .trim()
      .notEmpty()
      .withMessage('Company name is required')
      .isLength({ max: 255 })
      .withMessage('Company name must be less than 255 characters'),
    body('website')
      .optional()
      .trim()
      .isURL()
      .withMessage('Invalid website URL'),
    body('linkedinUrl')
      .optional()
      .trim()
      .isURL()
      .withMessage('Invalid LinkedIn URL'),
    body('industry')
      .optional()
      .trim()
      .isLength({ max: 100 })
      .withMessage('Industry must be less than 100 characters'),
    body('domain')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Domain must be less than 255 characters')
  ],
  
  // Deal creation/update validation
  dealData: [
    body('name')
      .trim()
      .notEmpty()
      .withMessage('Deal name is required')
      .isLength({ max: 255 })
      .withMessage('Deal name must be less than 255 characters'),
    body('amount')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Amount must be a positive number'),
    body('probability')
      .optional()
      .isInt({ min: 0, max: 100 })
      .withMessage('Probability must be between 0 and 100'),
    body('stage')
      .optional()
      .isIn(['prospecting', 'qualification', 'proposal', 'negotiation', 'closed_won', 'closed_lost'])
      .withMessage('Invalid deal stage'),
    body('closeDate')
      .optional()
      .isISO8601()
      .withMessage('Close date must be a valid ISO date')
  ],
  
  // Contact creation/update validation
  contactData: [
    body('firstName')
      .trim()
      .notEmpty()
      .withMessage('First name is required')
      .isLength({ max: 100 })
      .withMessage('First name must be less than 100 characters'),
    body('lastName')
      .trim()
      .notEmpty()
      .withMessage('Last name is required')
      .isLength({ max: 100 })
      .withMessage('Last name must be less than 100 characters'),
    body('email')
      .optional()
      .trim()
      .isEmail()
      .withMessage('Invalid email address'),
    body('phone')
      .optional()
      .trim()
      .matches(/^[\d\s\-\+\(\)]+$/)
      .withMessage('Invalid phone number format')
  ],
  
  // Note creation validation
  noteData: [
    body('content')
      .trim()
      .notEmpty()
      .withMessage('Note content is required')
      .isLength({ max: 5000 })
      .withMessage('Note content must be less than 5000 characters'),
    body('entityType')
      .isIn(['company', 'deal', 'contact'])
      .withMessage('Invalid entity type'),
    body('entityId')
      .isUUID()
      .withMessage('Invalid entity ID format')
  ]
};

/**
 * Sanitize string input to prevent XSS
 */
export const sanitizeString = (value: string): string => {
  if (!value) return value;
  
  // Remove any HTML tags
  return value
    .replace(/<[^>]*>?/gm, '')
    .trim();
};

/**
 * Validate and sanitize query parameters
 */
export const sanitizeQuery = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize common query parameters
  if (req.query.search && typeof req.query.search === 'string') {
    req.query.search = sanitizeString(req.query.search);
  }
  
  if (req.query.filter && typeof req.query.filter === 'string') {
    req.query.filter = sanitizeString(req.query.filter);
  }
  
  next();
};