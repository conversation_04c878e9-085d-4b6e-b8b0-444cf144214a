import express from 'express';
import { DealRepository } from '../../repositories/deal-repository';
import { DealEstimateRepository } from '../../repositories/relationships/deal-estimate-repository';
import { NoteRepository } from '../../repositories/note-repository';

// TypeScript interfaces for this file
interface ApiError extends Error {
  statusCode?: number;
  code?: string;
}

const router = express.Router();
const dealRepository = new DealRepository();
const dealEstimateRepository = new DealEstimateRepository();
const noteRepository = new NoteRepository();

/**
 * @route GET /api/crm/estimates/:id/deals
 * @desc Get deals linked to an estimate
 * @access Private
 */
router.get('/estimates/:id/deals', async (req, res) => {
  try {
    const { id } = req.params;
    const { estimateType } = req.query;

    // Validate estimate type
    if (!estimateType || (estimateType !== 'internal' && estimateType !== 'harvest')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid or missing estimate type',
        validTypes: ['internal', 'harvest']
      });
    }

    // Get deals linked to this estimate
    const dealRelationships = dealEstimateRepository.getDealsForEstimate(id);

    // If no deals are linked, return an empty array
    if (dealRelationships.length === 0) {
      return res.json({
        success: true,
        data: []
      });
    }

    // Get full deal details for each linked deal
    const deals = dealRelationships.map(relationship => {
      const deal = dealRepository.getDealById(relationship.dealId);
      // We don't need to add linkedAt property to the deal object
      // The frontend already has access to the linked estimates through the deal.estimates property
      return deal;
    }).filter(Boolean); // Remove any null values

    res.json({
      success: true,
      data: deals
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error getting deals linked to estimate:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to get linked deals',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

/**
 * @route DELETE /api/crm/notes/:id
 * @desc Delete a note
 * @access Private
 */
router.delete('/notes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const success = noteRepository.deleteNote(id);

    if (!success) {
      return res.status(404).json({
        success: false,
        error: 'Note not found'
      });
    }

    res.json({
      success: true,
      message: 'Note deleted successfully'
    });
  } catch (error) {
    const apiError = error as ApiError;
    console.error('Error deleting note:', apiError);
    res.status(apiError.statusCode || 500).json({
      success: false,
      error: 'Failed to delete note',
      message: apiError.message || 'Unknown error occurred'
    });
  }
});

export default router;