#!/usr/bin/env node

/**
 * Comprehensive Button Audit Script
 * 
 * This script finds ALL buttons in the codebase and categorizes them by:
 * 1. Standardized buttons (using approved classes)
 * 2. Custom styled buttons (using custom classes/styles)
 * 3. Inconsistent buttons (mixing approaches)
 * 
 * Goal: Ensure all buttons use the Flexoki theme system consistently
 */

const fs = require('fs');
const path = require('path');

// Approved button classes from Flexoki theme
const APPROVED_BUTTON_CLASSES = [
  // Modern button system (preferred)
  'btn-modern',
  'btn-modern--primary',
  'btn-modern--secondary', 
  'btn-modern--ghost',
  'btn-modern--sm',
  'btn-modern--md',
  'btn-modern--lg',
  
  // Legacy foundation system (acceptable)
  'btn',
  'btn--primary',
  'btn--secondary',
  'btn--outline',
  'btn--ghost',
  'btn--danger',
  'btn--success',
  'btn--sm',
  'btn--lg',
  
  // Legacy tailwind system (needs migration)
  'btn-primary',
  'btn-secondary',
  'btn-danger',
  'btn-success'
];

// Problematic patterns that indicate custom styling
const PROBLEMATIC_PATTERNS = [
  // Direct background colors
  /bg-(red|green|blue|yellow|purple|pink|indigo|gray|orange|emerald|cyan|teal|lime|amber|violet|fuchsia|rose|sky|slate|zinc|neutral|stone)-\d+/g,
  
  // Direct hover colors
  /hover:bg-(red|green|blue|yellow|purple|pink|indigo|gray|orange|emerald|cyan|teal|lime|amber|violet|fuchsia|rose|sky|slate|zinc|neutral|stone)-\d+/g,
  
  // Direct text colors on buttons
  /text-(red|green|blue|yellow|purple|pink|indigo|gray|orange|emerald|cyan|teal|lime|amber|violet|fuchsia|rose|sky|slate|zinc|neutral|stone)-\d+/g,
  
  // Old primary/secondary patterns
  /bg-primary(?!-\d)/g,
  /hover:bg-primary-dark/g,
  /bg-secondary(?!-\d)/g,
  
  // Inline styles
  /style\s*=\s*\{\{[^}]*backgroundColor[^}]*\}\}/g,
  /style\s*=\s*\{\{[^}]*background[^}]*\}\}/g,
];

// File extensions to check
const FILE_EXTENSIONS = ['.tsx', '.ts', '.jsx', '.js'];

// Directories to scan
const SCAN_DIRECTORIES = ['src/frontend/components', 'src/frontend/pages'];

/**
 * Get all files to scan
 */
function getFilesToScan() {
  const files = [];
  
  function scanDirectory(dir) {
    if (!fs.existsSync(dir)) return;
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (FILE_EXTENSIONS.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  SCAN_DIRECTORIES.forEach(scanDirectory);
  return files;
}

/**
 * Extract button elements from file content
 */
function extractButtons(content, filePath) {
  const buttons = [];
  const lines = content.split('\n');
  
  // Find button elements
  const buttonRegex = /<button[^>]*>/g;
  let match;
  
  while ((match = buttonRegex.exec(content)) !== null) {
    const lineNumber = content.substring(0, match.index).split('\n').length;
    const buttonTag = match[0];
    
    // Extract className
    const classNameMatch = buttonTag.match(/className\s*=\s*["{`]([^"}` ]+)["}` ]/);
    const className = classNameMatch ? classNameMatch[1] : '';
    
    // Extract style attribute
    const styleMatch = buttonTag.match(/style\s*=\s*\{([^}]+)\}/);
    const style = styleMatch ? styleMatch[1] : '';
    
    buttons.push({
      filePath,
      lineNumber,
      buttonTag,
      className,
      style,
      fullMatch: match[0]
    });
  }
  
  return buttons;
}

/**
 * Categorize button based on its styling approach
 */
function categorizeButton(button) {
  const { className, style } = button;
  
  // Check if using approved classes
  const hasApprovedClass = APPROVED_BUTTON_CLASSES.some(approvedClass => 
    className.includes(approvedClass)
  );
  
  // Check for problematic patterns
  const hasProblematicPattern = PROBLEMATIC_PATTERNS.some(pattern => 
    pattern.test(className) || pattern.test(style)
  );
  
  // Check for inline styles
  const hasInlineStyles = style.length > 0;
  
  if (hasApprovedClass && !hasProblematicPattern && !hasInlineStyles) {
    return 'standardized';
  } else if (hasApprovedClass && (hasProblematicPattern || hasInlineStyles)) {
    return 'mixed';
  } else {
    return 'custom';
  }
}

/**
 * Generate fix suggestions for a button
 */
function generateFixSuggestion(button) {
  const { className, style } = button;
  
  // Suggest modern button system
  if (className.includes('btn-primary') || className.includes('bg-primary')) {
    return 'Replace with: btn-modern btn-modern--primary';
  } else if (className.includes('btn-secondary') || className.includes('bg-secondary')) {
    return 'Replace with: btn-modern btn-modern--secondary';
  } else if (className.includes('bg-red') || className.includes('btn-danger')) {
    return 'Replace with: btn-modern btn-modern--primary (with error styling)';
  } else if (className.includes('bg-green') || className.includes('btn-success')) {
    return 'Replace with: btn-modern btn-modern--primary (with success styling)';
  } else if (style.includes('backgroundColor')) {
    return 'Replace inline styles with: btn-modern btn-modern--primary (or appropriate variant)';
  } else {
    return 'Review and apply appropriate btn-modern variant';
  }
}

/**
 * Main audit function
 */
function auditButtons() {
  console.log('🔍 Starting Comprehensive Button Audit...\n');
  
  const files = getFilesToScan();
  console.log(`📁 Scanning ${files.length} files...\n`);
  
  const results = {
    standardized: [],
    mixed: [],
    custom: [],
    stats: {
      totalButtons: 0,
      standardizedCount: 0,
      mixedCount: 0,
      customCount: 0,
      filesScanned: files.length
    }
  };
  
  // Process each file
  files.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const buttons = extractButtons(content, filePath);
      
      buttons.forEach(button => {
        const category = categorizeButton(button);
        results[category].push({
          ...button,
          suggestion: generateFixSuggestion(button)
        });
        results.stats.totalButtons++;
        results.stats[`${category}Count`]++;
      });
      
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  });
  
  return results;
}

/**
 * Display audit results
 */
function displayResults(results) {
  const { stats } = results;
  
  console.log('📊 BUTTON AUDIT RESULTS');
  console.log('========================\n');
  
  console.log(`📁 Files Scanned: ${stats.filesScanned}`);
  console.log(`🔘 Total Buttons Found: ${stats.totalButtons}`);
  console.log(`✅ Standardized: ${stats.standardizedCount} (${Math.round(stats.standardizedCount/stats.totalButtons*100)}%)`);
  console.log(`⚠️  Mixed Approach: ${stats.mixedCount} (${Math.round(stats.mixedCount/stats.totalButtons*100)}%)`);
  console.log(`❌ Custom Styled: ${stats.customCount} (${Math.round(stats.customCount/stats.totalButtons*100)}%)\n`);
  
  // Show problematic buttons that need fixing
  if (results.mixed.length > 0) {
    console.log('⚠️  MIXED APPROACH BUTTONS (need cleanup):');
    console.log('==========================================');
    results.mixed.forEach(button => {
      console.log(`📍 ${button.filePath}:${button.lineNumber}`);
      console.log(`   Current: ${button.className}`);
      console.log(`   Suggestion: ${button.suggestion}\n`);
    });
  }
  
  if (results.custom.length > 0) {
    console.log('❌ CUSTOM STYLED BUTTONS (need standardization):');
    console.log('===============================================');
    results.custom.forEach(button => {
      console.log(`📍 ${button.filePath}:${button.lineNumber}`);
      console.log(`   Current: ${button.className || 'No className'}`);
      if (button.style) console.log(`   Style: ${button.style}`);
      console.log(`   Suggestion: ${button.suggestion}\n`);
    });
  }
  
  // Summary recommendations
  console.log('💡 RECOMMENDATIONS:');
  console.log('===================');
  if (stats.customCount > 0 || stats.mixedCount > 0) {
    console.log('1. Replace all custom button styling with btn-modern system');
    console.log('2. Use btn-modern--primary for primary actions');
    console.log('3. Use btn-modern--secondary for secondary actions');
    console.log('4. Use btn-modern--ghost for subtle actions');
    console.log('5. Remove all inline styles and custom bg-* classes');
    console.log('6. Ensure consistent sizing with btn-modern--sm/md/lg');
  } else {
    console.log('✅ All buttons are properly standardized!');
  }
}

// Run the audit
if (require.main === module) {
  const results = auditButtons();
  displayResults(results);
}

module.exports = { auditButtons, displayResults };
