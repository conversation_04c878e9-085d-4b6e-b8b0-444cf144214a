# Activity Feed Implementation Plan

## Overview

This plan outlines the implementation of a comprehensive activity feed system for the Onbord Financial Dashboard. The activity feed will provide users with a chronological view of all system activities, user actions, and integration events across the application.

## Current State Analysis

### Existing Infrastructure
- **Audit Tables**: `change_log` and `field_ownership` tables already exist
- **Activity Table**: EXISTS in `scripts/initialize-fresh-database.js` and integrated into schema system
- **Change Tracking**: `deal-tracking.ts` utility logs field changes with comprehensive field ownership tracking
- **Event System**: EventContext with pub/sub pattern (`publish()` and `subscribe()`) for frontend event handling
- **Logging Context**: Console interceptor and logging infrastructure with LogEntry interface
- **Notes Timeline**: Existing timeline component (`NotesTimeline.tsx`) provides good UI pattern for activity feed
- **Socket.IO**: Already configured in `src/api/server.ts` and used by HubSpot for real-time import progress
- **Repository Pattern**: Well-established with `BaseRepository` class and consistent error handling
- **Activity Feed**: ✅ IMPLEMENTED - Full activity feed system is now operational

### Current Activity Tracking
- Deal field changes logged to `change_log` table
- Company linking operations audited
- HubSpot import progress tracking with real-time updates via Socket.IO
- HubSpot service has `recordImport()` method for tracking sync history
- Notes timeline for deals
- Field ownership tracking by data source (HubSpot/Harvest/Manual/System)

### Schema Inconsistency Found
- Activity table exists in initialization script but missing from `src/database/schema/` modules
- No activity repositories, services, or API routes exist
- No frontend activity components exist
- Navigation types (in `src/frontend/types/navigation.ts`) don't include 'activity' tab

## Data Model Extensions

### Enhanced Activity Table

```sql
-- Create new activity_feed table (to avoid confusion with existing activity table)
CREATE TABLE IF NOT EXISTS activity_feed (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL, -- Activity type (see types below)
  subject TEXT NOT NULL, -- Brief description
  description TEXT, -- Detailed description
  status TEXT, -- 'planned', 'completed', 'canceled', 'in_progress'

  /* Entity Relationships */
  entity_type TEXT, -- 'deal', 'company', 'estimate', 'expense', 'contact'
  entity_id TEXT, -- ID of the related entity

  /* Timing Information */
  due_date TEXT,
  completed_date TEXT,

  /* Legacy Relationships (for backward compatibility) */
  company_id TEXT,
  contact_id TEXT,
  deal_id TEXT,

  /* Metadata */
  metadata TEXT, -- JSON for additional context and structured data
  is_read INTEGER DEFAULT 0, -- Track read status per activity
  importance TEXT DEFAULT 'normal', -- 'low', 'normal', 'high'

  /* User and System Information */
  created_by TEXT NOT NULL, -- User ID or 'system'
  source TEXT, -- 'user', 'hubspot', 'xero', 'harvest', 'system'

  /* Audit Information */
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,

  /* Foreign Key Constraints */
  FOREIGN KEY (company_id) REFERENCES company(id) ON DELETE CASCADE,
  FOREIGN KEY (contact_id) REFERENCES contact(id) ON DELETE CASCADE,
  FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_at ON activity_feed(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feed_type ON activity_feed(type);
CREATE INDEX IF NOT EXISTS idx_activity_feed_entity ON activity_feed(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_source ON activity_feed(source);
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_by ON activity_feed(created_by);
CREATE INDEX IF NOT EXISTS idx_activity_feed_unread ON activity_feed(is_read, user_id);
```

### Activity Types

**User Activities:**
- `deal_created`, `deal_updated`, `deal_deleted`, `deal_stage_changed`
- `estimate_created`, `estimate_updated`, `estimate_published`, `estimate_deleted`
- `company_created`, `company_updated`, `company_linked`, `company_unlinked`
- `contact_created`, `contact_updated`, `contact_deleted`
- `note_added`, `note_updated`, `note_deleted`
- `expense_created`, `expense_updated`, `expense_deleted`

**System Activities:**
- `hubspot_sync_started`, `hubspot_sync_completed`, `hubspot_sync_failed`
- `xero_sync_started`, `xero_sync_completed`, `xero_sync_failed`
- `harvest_sync_started`, `harvest_sync_completed`, `harvest_sync_failed`
- `cashflow_projection_generated`
- `estimate_deal_linked`, `estimate_deal_unlinked`

**Integration Activities:**
- `auth_connected`, `auth_disconnected`, `auth_refreshed`
- `data_import_started`, `data_import_completed`, `data_import_failed`
- `bulk_operation_started`, `bulk_operation_completed`

## Architecture Design

### Backend Components

#### 1. Activity Repository (`src/api/repositories/activity-repository.ts`)
```typescript
// Extend BaseRepository for consistency with existing patterns
export class ActivityRepository extends BaseRepository {
  constructor(db: Database) {
    super(db, 'activity_feed'); // Use activity_feed to avoid confusion with existing activity table
  }

  async findByEntity(entityType: string, entityId: string): Promise<Activity[]> {
    return this.findMany({ entity_type: entityType, entity_id: entityId });
  }

  async findRecent(limit: number, offset: number): Promise<Activity[]> {
    return this.db.prepare(`
      SELECT * FROM activity_feed 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `).all(limit, offset);
  }

  async getUnreadCount(userId: string): Promise<number> {
    const result = this.db.prepare(`
      SELECT COUNT(*) as count 
      FROM activity_feed 
      WHERE is_read = 0 AND (user_id != ? OR user_id IS NULL)
    `).get(userId);
    return result.count;
  }
}
```

#### 2. Activity Service (`src/api/services/activity-service.ts`)
```typescript
import { EventEmitter } from 'events';
import { Server } from 'socket.io';

export class ActivityService extends EventEmitter {
  constructor(
    private activityRepository: ActivityRepository,
    private io?: Server
  ) {
    super();
  }

  async logActivity(data: ActivityCreate): Promise<Activity> {
    const activity = await this.activityRepository.create(data);
    
    // Emit Socket.IO event for real-time updates
    if (this.io) {
      this.io.emit('activity:created', activity);
    }
    
    // Emit EventEmitter event for internal listeners
    this.emit('activity-created', activity);
    
    return activity;
  }

  async getActivityFeed(filters: ActivityFilters): Promise<ActivityFeedResponse> {
    const activities = await this.activityRepository.findByFilters(filters);
    const total = await this.activityRepository.count(filters);
    return { activities, total };
  }
}
```

#### 3. Activity Logger Utility (`src/utils/activity-logger.ts`)
```typescript
interface ActivityLogger {
  logUserAction(type: string, subject: string, details: ActivityDetails): Promise<void>;
  logSystemEvent(type: string, subject: string, details: ActivityDetails): Promise<void>;
  logIntegrationEvent(source: string, type: string, details: ActivityDetails): Promise<void>;
  logEntityChange(entityType: string, entityId: string, changes: Record<string, any>): Promise<void>;
}
```

#### 4. Activity API Routes (`src/api/routes/activity.ts`)
```typescript
// GET /api/activity - Get activity feed with filters
// GET /api/activity/:id - Get specific activity
// POST /api/activity - Create new activity
// PUT /api/activity/:id - Update activity
// DELETE /api/activity/:id - Delete activity
// GET /api/activity/entity/:type/:id - Get activities for specific entity
```

### Frontend Components

#### 1. Activity Feed Page (`src/frontend/components/Activity/ActivityFeedPage.tsx`)
- Main activity dashboard
- Filter controls
- Activity timeline
- Real-time updates

#### 2. Activity Timeline (`src/frontend/components/Activity/ActivityTimeline.tsx`)
- Chronological list of activities
- Infinite scroll or pagination
- Activity grouping by date

#### 3. Activity Item (`src/frontend/components/Activity/ActivityItem.tsx`)
- Individual activity display
- Activity type icons
- User avatars
- Timestamps and metadata

#### 4. Activity Filters (`src/frontend/components/Activity/ActivityFilters.tsx`)
- Filter by type, entity, date range, user
- Search functionality
- Quick filter presets

## Migration Strategy

Since an `activity` table already exists in the initialization script, we need a careful migration approach:

1. **Create Migration Script** (`src/database/migrations/20250530_create_activity_feed.js`):
   ```javascript
   exports.up = function(db) {
     // Check if existing activity table has data
     const hasData = db.prepare('SELECT COUNT(*) as count FROM activity').get();
     
     if (hasData.count > 0) {
       // Rename existing table to preserve data
       db.prepare('ALTER TABLE activity RENAME TO legacy_activity').run();
     }
     
     // Create new activity_feed table with enhanced schema
     // (using the schema defined above)
   };
   ```

2. **Update Schema Module**: Add activity_feed creation to `src/database/schema/audit.ts`

3. **Handle Legacy Data**: If needed, migrate relevant data from legacy_activity table

## Implementation Phases

### Phase 1: Core Activity System (Week 1-2)

**Backend Tasks:**
1. **Fix Schema Integration**: Move activity table from initialization script to `src/database/schema/audit.ts` module (fits conceptually with audit domain)
2. **Add Navigation Type**: Add 'activity' to TabId type in `src/frontend/types/navigation.ts`
3. **Implement ActivityRepository**: Create repository following existing patterns
4. **Implement ActivityService**: Create service layer for business logic
5. **Create Activity API Routes**: Add `/api/activity` endpoints
6. **Implement ActivityLogger Utility**: Create logging utility for system events

**Frontend Tasks:**
1. **Create ActivityFeedPage Component**: Main activity dashboard page
2. **Create ActivityTimeline Component**: Timeline display component (follow NotesTimeline.tsx pattern)
3. **Create ActivityItem Component**: Individual activity display
4. **Add Activity Tab to Navigation**: Update navigation configuration
5. **Implement Basic API Integration**: Connect frontend to backend with React Query
6. **Add Activity Badge to Header**: Show unread count in header ProfileBadge area

**Integration Tasks:**
1. **Add Activity Logging to Deal Operations**: Integrate with existing deal repository
2. **Add Activity Logging to Note Creation**: Integrate with note repository
3. **Test Basic Activity Feed Functionality**: End-to-end testing

### Phase 2: System Integration (Week 3-4)

**Backend Tasks:**
1. Integrate activity logging into existing operations (extend `trackDealChanges` function)
2. Add activity logging to estimate operations
3. Add activity logging to company operations
4. Add activity logging to sync operations (follow HubSpot's `recordImport()` pattern)
5. Implement activity aggregation and summarization

**Frontend Tasks:**
1. Implement ActivityFilters component
2. Add real-time activity updates via WebSocket
3. Improve activity item styling and icons
4. Add activity search functionality
5. Implement activity pagination

**Integration Tasks:**
1. Add activity logging to HubSpot sync (extend existing `recordImport()` method)
2. Add activity logging to Xero sync
3. Add activity logging to Harvest operations
4. Create migration script to handle existing `activity` table
5. Test comprehensive activity tracking

### Phase 3: Enhanced Features (Week 5-6)

**Backend Tasks:**
1. Implement activity analytics endpoints
2. Add activity notification system
3. Implement activity archiving
4. Add bulk activity operations
5. Performance optimization and indexing

**Frontend Tasks:**
1. Add activity notifications
2. Implement activity analytics dashboard
3. Add activity export functionality
4. Improve mobile responsiveness
5. Add activity preferences and settings

**Integration Tasks:**
1. Add activity-based insights
2. Implement activity-driven workflows
3. Add activity reporting features
4. Performance testing and optimization

## UI/UX Design

### Navigation Integration
- Add "Activity" tab to existing navigation with activity icon
- Position between "CRM" and "Leads" tabs  
- Use purple color scheme to match existing design patterns
- Add unread count badge to header (near ProfileBadge)
- Consider dropdown quick view for recent activities

### Activity Feed Layout
```
┌─────────────────────────────────────────────────────────┐
│ Activity Feed                                    [⚙️]    │
├─────────────────────────────────────────────────────────┤
│ [🔍 Search] [📅 Date] [👤 User] [🏷️ Type] [🔄 Refresh] │
├─────────────────────────────────────────────────────────┤
│ Today                                                   │
│ ┌─ 🔄 System synchronized 15 deals from HubSpot        │
│ │  2 minutes ago                                        │
│ ├─ 📝 John Smith added note to "Website Redesign"      │
│ │  5 minutes ago                                        │
│ └─ 💰 Deal "Mobile App" moved to "Proposal Sent"       │
│    1 hour ago                                           │
│                                                         │
│ Yesterday                                               │
│ ┌─ 📊 New estimate created for "Data Migration"        │
│ │  Yesterday at 4:30 PM                                │
│ └─ 🔗 Company "Acme Corp" linked to HubSpot           │
│    Yesterday at 2:15 PM                                │
└─────────────────────────────────────────────────────────┘
```

### Activity Item Design
- User avatar or system icon on the left
- Activity description with entity links
- Timestamp on the right
- Expandable details for complex activities
- Color-coded activity types

## Technical Considerations

### Performance
- Index activity_feed table by date, type, entity, and read status
- Implement pagination with cursor-based navigation
- Use efficient queries with proper indexing (see index definitions)
- Consider archiving activities older than 1 year
- Batch Socket.IO events to prevent flooding

### Real-time Updates
- Use existing Socket.IO infrastructure (already configured in `src/api/server.ts`)
- Follow HubSpot's pattern for real-time updates (see `recordImport()` method)
- Emit activity events when they occur: `io.emit('activity:created', activity)`
- Update activity feed in real-time on frontend
- Implement activity event batching for high-volume operations

### Data Privacy
- Respect user permissions when showing activities
- Filter activities based on user access rights
- Implement activity data retention policies
- Allow users to control activity visibility

### Integration Points
- Extend existing `logFieldChange` function in `src/utils/deal-tracking.ts`
- Use EventContext for activity event publishing (already has pub/sub pattern)
- Leverage existing audit infrastructure (change_log and field_ownership tables)
- Follow HubSpot's `recordImport()` pattern for integration sync tracking
- Build on NotesTimeline component pattern for UI
- Integrate with existing user management system
- Use existing BaseRepository pattern for data access
- Leverage Socket.IO setup from `src/api/server.ts`

## Success Metrics

### User Engagement
- Activity feed page views
- Time spent on activity feed
- Activity filter usage
- Activity search usage

### System Monitoring
- Activity logging performance
- Real-time update latency
- Database query performance
- Storage usage growth

### Business Value
- Improved user awareness of system changes
- Faster issue identification and resolution
- Better audit trail for compliance
- Enhanced user collaboration and communication

## Future Enhancements

### Advanced Features
- Activity-based notifications and alerts
- Activity analytics and insights
- Custom activity types and workflows
- Activity-driven automation rules

### Integration Expansions
- Slack/Teams activity notifications
- Email activity summaries
- Mobile app activity feed
- API webhooks for external systems

### AI/ML Opportunities
- Activity pattern recognition
- Predictive activity suggestions
- Automated activity categorization
- Intelligent activity summarization
