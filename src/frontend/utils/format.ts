/**
 * @file Consolidated formatting utilities
 *
 * This file contains all standardized formatting functions used throughout the application.
 * All components should import formatting functions from this file to ensure consistency.
 */

import { format as dateFnsFormat } from 'date-fns';

/**
 * Format style options for date formatting
 */
export type DateFormatStyle = 'standard' | 'readable' | 'short' | 'iso';

/**
 * Format a number as currency
 *
 * @param value Number to format as currency
 * @param options Optional configuration options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number,
  options: {
    /** Whether to use a more compact notation for large values */
    compact?: boolean;
    /** Always uses AUD currency */
    currency?: string;
  } = {}
): string => {
  const { compact = false, currency = 'AUD' } = options;

  // Basic check for non-numeric input
  if (typeof value !== 'number' || isNaN(value)) {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(0);
  }

  return new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency,
    notation: compact ? 'compact' : 'standard',
    minimumFractionDigits: compact ? 0 : 2,
    maximumFractionDigits: compact ? 1 : 2
  }).format(value);
};

/**
 * Legacy formatCurrency function for backwards compatibility
 * @deprecated Use formatCurrency with options object instead
 */
export const formatCurrencyLegacy = (value: number, currencyCode: string = 'AUD'): string => {
  return formatCurrency(value, { currency: currencyCode });
};

/**
 * Format a date with flexible options
 *
 * @param date Date to format
 * @param style Format style - 'standard' (DD/MM/YYYY), 'readable' (DD MMM YYYY), 'short' (DD/MM), or 'iso' (YYYY-MM-DD)
 * @param emptyPlaceholder Text to show for empty/invalid dates
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | string | null | undefined,
  style: DateFormatStyle = 'standard',
  emptyPlaceholder: string = '—' // Em dash is the default placeholder
): string => {
  // Handle empty cases
  if (!date) return emptyPlaceholder;

  // Convert string to Date object if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Ensure we have a valid Date object
  if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
    console.warn('Invalid date encountered:', date);
    return emptyPlaceholder;
  }

  try {
    switch (style) {
      case 'readable':
        // DD MMM YYYY format (01 Jan 2025)
        return dateFnsFormat(dateObj, 'dd MMM yyyy');

      case 'short':
        // DD/MM format (01/01)
        return dateFnsFormat(dateObj, 'dd/MM');

      case 'iso':
        // YYYY-MM-DD format (2025-01-01)
        return dateObj.toISOString().split('T')[0];

      case 'standard':
      default:
        // Standard DD/MM/YYYY format (01/01/2025)
        return dateObj.toLocaleDateString('en-AU', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        });
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    // Fallback formatting
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    return `${day}/${month}/${dateObj.getFullYear()}`;
  }
};

/**
 * Format a percentage value
 *
 * @param value Percentage value (e.g., 5.2 for 5.2%)
 * @param options Formatting options
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number,
  options: {
    /** Decimal places to show (defaults to 1) */
    decimals?: number;
    /** Whether to include the % sign (defaults to true) */
    showSymbol?: boolean;
    /** Whether to include a + sign for positive values */
    showPlusSign?: boolean;
  } = {}
): string => {
  const {
    decimals = 1,
    showSymbol = true,
    showPlusSign = false
  } = options;

  // Format with specified decimal places
  const formatted = value.toFixed(decimals);

  // Add plus sign if needed (and value is positive and not zero)
  const prefix = showPlusSign && parseFloat(formatted) > 0 ? '+' : '';

  // Return with or without % symbol
  return `${prefix}${formatted}${showSymbol ? '%' : ''}`;
};

/**
 * Calculate percentage change between two values
 *
 * @param currentValue Current value
 * @param previousValue Previous value
 * @returns Percentage change as a number (e.g., 5.2 for 5.2%)
 */
export const calculatePercentageChange = (currentValue: number, previousValue: number): number => {
  // Handle edge cases
  if (previousValue === 0) return currentValue > 0 ? 100 : 0; // Avoid division by zero
  if (currentValue === previousValue) return 0; // Explicitly handle no change

  // Calculate percentage change
  return ((currentValue - previousValue) / Math.abs(previousValue)) * 100;
};

/**
 * Format a number with thousands separators
 *
 * @param value Number to format
 * @param decimals Number of decimal places to show
 * @returns Formatted number string
 */
export const formatNumber = (value: number, decimals: number = 0): string => {
  return new Intl.NumberFormat('en-AU', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
};

/**
 * Format a date as a relative time string (e.g., "2 weeks ago", "3 days ago")
 *
 * @param date Date to format
 * @param emptyPlaceholder Text to show for empty/invalid dates
 * @returns Relative time string
 */
export const formatRelativeDate = (
  date: Date | string | null | undefined,
  emptyPlaceholder: string = 'Never'
): string => {
  // Handle empty cases
  if (!date) return emptyPlaceholder;

  // Convert string to Date object if needed
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Ensure we have a valid Date object
  if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
    return emptyPlaceholder;
  }

  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'Today';
  } else if (diffInDays === 1) {
    return 'Yesterday';
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
  } else if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return months === 1 ? '1 month ago' : `${months} months ago`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return years === 1 ? '1 year ago' : `${years} years ago`;
  }
};