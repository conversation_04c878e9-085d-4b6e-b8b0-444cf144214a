/**
 * Projection Filter Service Test
 * 
 * Tests for the Smart Forecast filtering logic.
 * This is one of the most critical services as it determines
 * which transactions are included in financial projections.
 */
import { ProjectionFilterService } from '@/services/cashflow/projection-filter-service';
import { ProjectionAuditDecision } from '@/types/financial';

describe('ProjectionFilterService', () => {
  let service: ProjectionFilterService;
  
  beforeEach(() => {
    service = new ProjectionFilterService();
  });
  
  describe('applyFilteringRules', () => {
    const baseProjection = {
      id: 'proj-1',
      projectId: 'harvest-123',
      projectName: 'Test Project',
      clientName: 'Test Client',
      amount: 5000,
      date: new Date('2025-02-15'),
      type: 'projected' as const,
      source: 'harvest' as const
    };
    
    const realInvoices = [
      {
        id: 'inv-1',
        projectId: 'harvest-123',
        amount: 5000,
        issueDate: new Date('2025-02-10'),
        dueDate: new Date('2025-02-24'),
        status: 'sent' as const
      }
    ];
    
    const uninvoicedWork = [
      {
        projectId: 'harvest-123',
        amount: 2500,
        fromDate: new Date('2025-01-01'),
        toDate: new Date('2025-01-31')
      }
    ];
    
    describe('Payment Terms Rule', () => {
      it('filters projections that match real invoice payment terms', () => {
        const decisions = service.applyFilteringRules(
          [baseProjection],
          realInvoices,
          uninvoicedWork
        );
        
        expect(decisions).toHaveLength(1);
        expect(decisions[0].decision).toBe('excluded');
        expect(decisions[0].rule).toBe('payment_terms');
        expect(decisions[0].reason).toContain('payment terms match');
      });
      
      it('includes projections when payment terms differ significantly', () => {
        const projectionWithDifferentDate = {
          ...baseProjection,
          date: new Date('2025-03-15') // Different month
        };
        
        const decisions = service.applyFilteringRules(
          [projectionWithDifferentDate],
          realInvoices,
          uninvoicedWork
        );
        
        expect(decisions[0].decision).toBe('included');
        expect(decisions[0].rule).toBe('no_conflict');
      });
    });
    
    describe('Real Invoice Duplicate Rule', () => {
      it('filters projections that duplicate real invoices', () => {
        const projectionMatchingInvoice = {
          ...baseProjection,
          date: new Date('2025-02-10'), // Same as invoice issue date
          amount: 5000
        };
        
        const decisions = service.applyFilteringRules(
          [projectionMatchingInvoice],
          realInvoices,
          uninvoicedWork
        );
        
        expect(decisions[0].decision).toBe('excluded');
        expect(decisions[0].rule).toBe('real_invoice_duplicate');
        expect(decisions[0].reason).toContain('real invoice exists');
      });
      
      it('includes projections for different amounts', () => {
        const projectionDifferentAmount = {
          ...baseProjection,
          amount: 3000 // Different from real invoice
        };
        
        const decisions = service.applyFilteringRules(
          [projectionDifferentAmount],
          realInvoices,
          uninvoicedWork
        );
        
        // Should be excluded by payment terms rule, not duplicate rule
        expect(decisions[0].rule).not.toBe('real_invoice_duplicate');
      });
    });
    
    describe('Uninvoiced Work Rule', () => {
      it('filters projections covered by uninvoiced work', () => {
        const projectionForUninvoicedPeriod = {
          ...baseProjection,
          date: new Date('2025-01-31'),
          amount: 2500
        };
        
        const decisions = service.applyFilteringRules(
          [projectionForUninvoicedPeriod],
          [],
          uninvoicedWork
        );
        
        expect(decisions[0].decision).toBe('excluded');
        expect(decisions[0].rule).toBe('uninvoiced_work');
        expect(decisions[0].reason).toContain('uninvoiced work exists');
      });
      
      it('includes projections for future periods', () => {
        const futureProjection = {
          ...baseProjection,
          date: new Date('2025-05-15') // Beyond uninvoiced work
        };
        
        const decisions = service.applyFilteringRules(
          [futureProjection],
          [],
          uninvoicedWork
        );
        
        expect(decisions[0].decision).toBe('included');
      });
    });
    
    describe('Multiple Rules Interaction', () => {
      it('applies rules in correct priority order', () => {
        // Projection that could match multiple rules
        const ambiguousProjection = {
          ...baseProjection,
          date: new Date('2025-02-24'), // Matches payment terms
          amount: 5000 // Also matches invoice amount
        };
        
        const decisions = service.applyFilteringRules(
          [ambiguousProjection],
          realInvoices,
          uninvoicedWork
        );
        
        // Payment terms rule should take precedence
        expect(decisions[0].rule).toBe('payment_terms');
      });
      
      it('tracks all applicable rules in metadata', () => {
        const decisions = service.applyFilteringRules(
          [baseProjection],
          realInvoices,
          uninvoicedWork
        );
        
        expect(decisions[0].metadata).toBeDefined();
        expect(decisions[0].metadata.matchedInvoice).toBeDefined();
      });
    });
    
    describe('Edge Cases', () => {
      it('handles empty inputs gracefully', () => {
        const decisions = service.applyFilteringRules([], [], []);
        expect(decisions).toEqual([]);
      });
      
      it('handles projections with missing data', () => {
        const invalidProjection = {
          ...baseProjection,
          projectId: undefined
        };
        
        const decisions = service.applyFilteringRules(
          [invalidProjection as any],
          realInvoices,
          uninvoicedWork
        );
        
        expect(decisions[0].decision).toBe('included');
        expect(decisions[0].rule).toBe('no_conflict');
      });
      
      it('handles date boundary conditions', () => {
        const boundaryProjection = {
          ...baseProjection,
          date: new Date('2025-02-24T23:59:59.999Z')
        };
        
        const decisions = service.applyFilteringRules(
          [boundaryProjection],
          realInvoices,
          uninvoicedWork
        );
        
        // Should still match payment terms
        expect(decisions[0].rule).toBe('payment_terms');
      });
    });
  });
  
  describe('getFilteringSummary', () => {
    it('provides accurate summary statistics', () => {
      const decisions: ProjectionAuditDecision[] = [
        {
          projectionId: '1',
          decision: 'excluded',
          rule: 'payment_terms',
          reason: 'Test',
          timestamp: new Date()
        },
        {
          projectionId: '2',
          decision: 'excluded',
          rule: 'payment_terms',
          reason: 'Test',
          timestamp: new Date()
        },
        {
          projectionId: '3',
          decision: 'excluded',
          rule: 'real_invoice_duplicate',
          reason: 'Test',
          timestamp: new Date()
        },
        {
          projectionId: '4',
          decision: 'included',
          rule: 'no_conflict',
          reason: 'Test',
          timestamp: new Date()
        }
      ];
      
      const summary = service.getFilteringSummary(decisions);
      
      expect(summary.total).toBe(4);
      expect(summary.included).toBe(1);
      expect(summary.excluded).toBe(3);
      expect(summary.byRule.payment_terms).toBe(2);
      expect(summary.byRule.real_invoice_duplicate).toBe(1);
      expect(summary.byRule.uninvoiced_work).toBe(0);
    });
  });
  
  describe('Performance', () => {
    it('handles large datasets efficiently', () => {
      // Create large dataset
      const projections = Array.from({ length: 1000 }, (_, i) => ({
        ...baseProjection,
        id: `proj-${i}`,
        amount: Math.random() * 10000
      }));
      
      const invoices = Array.from({ length: 500 }, (_, i) => ({
        id: `inv-${i}`,
        projectId: `harvest-${i % 10}`,
        amount: Math.random() * 10000,
        issueDate: new Date('2025-01-01'),
        dueDate: new Date('2025-01-15'),
        status: 'sent' as const
      }));
      
      const startTime = Date.now();
      const decisions = service.applyFilteringRules(projections, invoices, []);
      const endTime = Date.now();
      
      expect(decisions).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in under 1 second
    });
  });
});