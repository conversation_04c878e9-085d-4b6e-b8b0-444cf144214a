/**
 * <PERSON><PERSON><PERSON> to fix data model issues
 * 
 * This script:
 * 1. Creates the note table if it doesn't exist
 * 2. Adds any other missing tables or columns required by the fresh unified data model
 */

const BetterSqlite3 = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Determine the database path based on environment
const isDevelopment = process.env.NODE_ENV !== 'production';
const dbPath = isDevelopment
  ? path.join(__dirname, '..', 'data', 'upstream.db')
  : '/data/upstream.db';

// Ensure the data directory exists in development
if (isDevelopment) {
  const dataDir = path.join(__dirname, '..', 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Connect to the database
const db = new BetterSqlite3(dbPath, { verbose: console.log });

try {
  console.log('Starting data model fixes...');
  
  // Begin a transaction
  db.prepare('BEGIN TRANSACTION').run();
  
  // Fix 1: Create the note table if it doesn't exist
  console.log('Checking for note table...');
  const noteTableExists = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name='note'
  `).get();
  
  if (noteTableExists) {
    console.log('Note table already exists, skipping creation');
  } else {
    console.log('Creating note table...');
    db.prepare(`
      CREATE TABLE note (
        id TEXT PRIMARY KEY,
        deal_id TEXT,
        content TEXT NOT NULL,
        created_at TEXT NOT NULL,
        created_by TEXT,
        FOREIGN KEY (deal_id) REFERENCES deal(id) ON DELETE CASCADE
      )
    `).run();
    
    db.prepare(`
      CREATE INDEX idx_note_deal_id ON note(deal_id)
    `).run();
    
    console.log('Note table created successfully');
  }
  
  // Commit the transaction
  db.prepare('COMMIT').run();
  
  console.log('Data model fixes completed successfully');
} catch (error) {
  // Rollback the transaction in case of error
  db.prepare('ROLLBACK').run();
  console.error('Error fixing data model:', error);
} finally {
  // Close the database connection
  db.close();
}
