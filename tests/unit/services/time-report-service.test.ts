import { jest } from '@jest/globals';
import TimeReportService from '../../../src/services/harvest/time-report-service';
import { getTimeEntries, getProjects, getUsers, getTasks } from '../../../src/api/integrations/harvest';

// Mock dependencies
jest.mock('../../../src/api/integrations/harvest');
jest.mock('../../../src/utils/backend-logger');

describe('TimeReportService', () => {
  let service: TimeReportService;

  beforeEach(() => {
    service = new TimeReportService();
    jest.clearAllMocks();
  });

  describe('getTimeReports', () => {
    it('should generate time reports grouped by project', async () => {
      const mockTimeEntries = [
        {
          id: 1,
          hours: 8,
          project: { id: 100, name: 'Project A' },
          user: { id: 1, name: '<PERSON>' },
          task: { id: 10, name: '<PERSON>' },
          spent_date: '2024-01-15',
          billable: true,
          billable_rate: 150,
        },
        {
          id: 2,
          hours: 6,
          project: { id: 100, name: 'Project A' },
          user: { id: 2, name: '<PERSON>' },
          task: { id: 11, name: 'Design' },
          spent_date: '2024-01-15',
          billable: true,
          billable_rate: 120,
        },
        {
          id: 3,
          hours: 4,
          project: { id: 200, name: 'Project B' },
          user: { id: 1, name: 'John Doe' },
          task: { id: 12, name: 'Meeting' },
          spent_date: '2024-01-16',
          billable: false,
          billable_rate: null,
        },
      ];

      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const result = await service.getTimeReports({
        from: '2024-01-15',
        to: '2024-01-16',
        groupBy: 'project',
      });

      expect(getTimeEntries).toHaveBeenCalledWith({
        from: '2024-01-15',
        to: '2024-01-16',
      });

      expect(result).toEqual({
        summary: {
          totalHours: 18,
          billableHours: 14,
          nonBillableHours: 4,
          totalValue: 1920,
          averageRate: 137.14,
        },
        breakdown: [
          {
            projectId: 100,
            projectName: 'Project A',
            hours: 14,
            billableHours: 14,
            nonBillableHours: 0,
            value: 1920,
            users: expect.any(Array),
            tasks: expect.any(Array),
          },
          {
            projectId: 200,
            projectName: 'Project B',
            hours: 4,
            billableHours: 0,
            nonBillableHours: 4,
            value: 0,
            users: expect.any(Array),
            tasks: expect.any(Array),
          },
        ],
      });
    });

    it('should generate time reports grouped by user', async () => {
      const mockTimeEntries = [
        {
          id: 1,
          hours: 8,
          project: { id: 100, name: 'Project A' },
          user: { id: 1, name: 'John Doe' },
          task: { id: 10, name: 'Development' },
          spent_date: '2024-01-15',
          billable: true,
          billable_rate: 150,
        },
        {
          id: 2,
          hours: 4,
          project: { id: 200, name: 'Project B' },
          user: { id: 1, name: 'John Doe' },
          task: { id: 11, name: 'Testing' },
          spent_date: '2024-01-15',
          billable: true,
          billable_rate: 150,
        },
      ];

      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const result = await service.getTimeReports({
        from: '2024-01-15',
        to: '2024-01-15',
        groupBy: 'user',
      });

      expect(result.breakdown).toEqual([
        {
          userId: 1,
          userName: 'John Doe',
          hours: 12,
          billableHours: 12,
          nonBillableHours: 0,
          value: 1800,
          projects: expect.any(Array),
          tasks: expect.any(Array),
        },
      ]);
    });

    it('should filter by project ID', async () => {
      const mockTimeEntries = [
        {
          id: 1,
          hours: 8,
          project: { id: 100, name: 'Project A' },
          user: { id: 1, name: 'John Doe' },
          spent_date: '2024-01-15',
          billable: true,
          billable_rate: 150,
        },
      ];

      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      await service.getTimeReports({
        from: '2024-01-15',
        to: '2024-01-15',
        project_id: '100',
      });

      expect(getTimeEntries).toHaveBeenCalledWith({
        from: '2024-01-15',
        to: '2024-01-15',
        project_id: '100',
      });
    });
  });

  describe('getUtilizationReport', () => {
    it('should calculate user utilization rates', async () => {
      const mockUsers = [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          weekly_capacity: 40,
          is_active: true,
        },
        {
          id: 2,
          first_name: 'Jane',
          last_name: 'Smith',
          weekly_capacity: 40,
          is_active: true,
        },
      ];

      const mockTimeEntries = [
        {
          user: { id: 1 },
          hours: 32,
          billable: true,
          spent_date: '2024-01-15',
        },
        {
          user: { id: 1 },
          hours: 8,
          billable: false,
          spent_date: '2024-01-16',
        },
        {
          user: { id: 2 },
          hours: 20,
          billable: true,
          spent_date: '2024-01-15',
        },
      ];

      (getUsers as jest.Mock).mockResolvedValue(mockUsers);
      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const result = await service.getUtilizationReport({
        from: '2024-01-15',
        to: '2024-01-19',
      });

      expect(result).toEqual({
        period: {
          from: '2024-01-15',
          to: '2024-01-19',
          workingDays: 5,
        },
        summary: {
          totalCapacity: 80,
          totalHours: 60,
          totalBillableHours: 52,
          overallUtilization: 75,
          billableUtilization: 65,
        },
        users: [
          {
            userId: 1,
            userName: 'John Doe',
            capacity: 40,
            totalHours: 40,
            billableHours: 32,
            nonBillableHours: 8,
            utilization: 100,
            billableUtilization: 80,
          },
          {
            userId: 2,
            userName: 'Jane Smith',
            capacity: 40,
            totalHours: 20,
            billableHours: 20,
            nonBillableHours: 0,
            utilization: 50,
            billableUtilization: 50,
          },
        ],
      });
    });

    it('should handle users with no time entries', async () => {
      const mockUsers = [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          weekly_capacity: 40,
          is_active: true,
        },
      ];

      (getUsers as jest.Mock).mockResolvedValue(mockUsers);
      (getTimeEntries as jest.Mock).mockResolvedValue([]);

      const result = await service.getUtilizationReport({
        from: '2024-01-15',
        to: '2024-01-19',
      });

      expect(result.users[0]).toEqual({
        userId: 1,
        userName: 'John Doe',
        capacity: 40,
        totalHours: 0,
        billableHours: 0,
        nonBillableHours: 0,
        utilization: 0,
        billableUtilization: 0,
      });
    });
  });

  describe('getProjectProfitability', () => {
    it('should calculate project profitability', async () => {
      const mockProjects = [
        {
          id: 100,
          name: 'Project A',
          budget: 50000,
          budget_is_monthly: false,
          budget_by: 'project',
        },
      ];

      const mockTimeEntries = [
        {
          project: { id: 100 },
          hours: 100,
          billable: true,
          billable_rate: 150,
          cost_rate: 75,
          user: { id: 1, name: 'John Doe' },
        },
        {
          project: { id: 100 },
          hours: 50,
          billable: true,
          billable_rate: 120,
          cost_rate: 60,
          user: { id: 2, name: 'Jane Smith' },
        },
      ];

      (getProjects as jest.Mock).mockResolvedValue(mockProjects);
      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const result = await service.getProjectProfitability(100);

      expect(result).toEqual({
        projectId: 100,
        projectName: 'Project A',
        budget: 50000,
        revenue: 21000,
        costs: 10500,
        profit: 10500,
        profitMargin: 50,
        budgetUtilization: 42,
        hours: {
          total: 150,
          billable: 150,
          nonBillable: 0,
        },
        averageRates: {
          billable: 140,
          cost: 70,
        },
        userBreakdown: expect.any(Array),
      });
    });

    it('should handle projects with no budget', async () => {
      const mockProjects = [
        {
          id: 100,
          name: 'Project A',
          budget: null,
        },
      ];

      const mockTimeEntries = [
        {
          project: { id: 100 },
          hours: 10,
          billable: true,
          billable_rate: 150,
          cost_rate: 75,
        },
      ];

      (getProjects as jest.Mock).mockResolvedValue(mockProjects);
      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const result = await service.getProjectProfitability(100);

      expect(result.budget).toBe(0);
      expect(result.budgetUtilization).toBe(0);
    });
  });

  describe('getTaskAnalysis', () => {
    it('should analyze time by task categories', async () => {
      const mockTasks = [
        { id: 10, name: 'Development' },
        { id: 11, name: 'Design' },
        { id: 12, name: 'Meeting' },
      ];

      const mockTimeEntries = [
        {
          task: { id: 10, name: 'Development' },
          hours: 20,
          billable: true,
          billable_rate: 150,
        },
        {
          task: { id: 10, name: 'Development' },
          hours: 15,
          billable: true,
          billable_rate: 150,
        },
        {
          task: { id: 11, name: 'Design' },
          hours: 10,
          billable: true,
          billable_rate: 120,
        },
        {
          task: { id: 12, name: 'Meeting' },
          hours: 5,
          billable: false,
          billable_rate: null,
        },
      ];

      (getTasks as jest.Mock).mockResolvedValue(mockTasks);
      (getTimeEntries as jest.Mock).mockResolvedValue(mockTimeEntries);

      const result = await service.getTaskAnalysis({
        from: '2024-01-01',
        to: '2024-01-31',
      });

      expect(result).toEqual({
        summary: {
          totalHours: 50,
          totalTasks: 3,
          billablePercentage: 90,
        },
        tasks: [
          {
            taskId: 10,
            taskName: 'Development',
            hours: 35,
            percentage: 70,
            billableHours: 35,
            value: 5250,
            averageRate: 150,
          },
          {
            taskId: 11,
            taskName: 'Design',
            hours: 10,
            percentage: 20,
            billableHours: 10,
            value: 1200,
            averageRate: 120,
          },
          {
            taskId: 12,
            taskName: 'Meeting',
            hours: 5,
            percentage: 10,
            billableHours: 0,
            value: 0,
            averageRate: 0,
          },
        ],
      });
    });
  });

  describe('error handling', () => {
    it('should handle API errors gracefully', async () => {
      (getTimeEntries as jest.Mock).mockRejectedValue(new Error('API Error'));

      await expect(service.getTimeReports({
        from: '2024-01-01',
        to: '2024-01-31',
      })).rejects.toThrow('API Error');
    });

    it('should handle empty responses', async () => {
      (getTimeEntries as jest.Mock).mockResolvedValue([]);

      const result = await service.getTimeReports({
        from: '2024-01-01',
        to: '2024-01-31',
        groupBy: 'project',
      });

      expect(result).toEqual({
        summary: {
          totalHours: 0,
          billableHours: 0,
          nonBillableHours: 0,
          totalValue: 0,
          averageRate: 0,
        },
        breakdown: [],
      });
    });
  });
});