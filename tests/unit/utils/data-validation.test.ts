import * as validation from '../../../src/utils/data-validation';

describe('data-validation utilities', () => {
  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validation.validateEmail('<EMAIL>')).toBe(true);
      expect(validation.validateEmail('<EMAIL>')).toBe(true);
      expect(validation.validateEmail('<EMAIL>')).toBe(true);
      expect(validation.validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validation.validateEmail('invalid')).toBe(false);
      expect(validation.validateEmail('@example.com')).toBe(false);
      expect(validation.validateEmail('user@')).toBe(false);
      expect(validation.validateEmail('user @example.com')).toBe(false);
      expect(validation.validateEmail('user@example')).toBe(false);
      expect(validation.validateEmail('')).toBe(false);
    });

    it('should handle null and undefined', () => {
      expect(validation.validateEmail(null as any)).toBe(false);
      expect(validation.validateEmail(undefined as any)).toBe(false);
    });
  });

  describe('validatePhone', () => {
    it('should validate correct phone numbers', () => {
      expect(validation.validatePhone('+*********0')).toBe(true);
      expect(validation.validatePhone('+44 20 7946 0958')).toBe(true);
      expect(validation.validatePhone('(*************')).toBe(true);
      expect(validation.validatePhone('************')).toBe(true);
      expect(validation.validatePhone('5551234567')).toBe(true);
    });

    it('should reject invalid phone numbers', () => {
      expect(validation.validatePhone('123')).toBe(false);
      expect(validation.validatePhone('abcdefghij')).toBe(false);
      expect(validation.validatePhone('')).toBe(false);
    });
  });

  describe('validateUrl', () => {
    it('should validate correct URLs', () => {
      expect(validation.validateUrl('https://example.com')).toBe(true);
      expect(validation.validateUrl('http://subdomain.example.com')).toBe(true);
      expect(validation.validateUrl('https://example.com/path/to/page')).toBe(true);
      expect(validation.validateUrl('https://example.com?query=value')).toBe(true);
      expect(validation.validateUrl('https://example.com:8080')).toBe(true);
    });

    it('should reject invalid URLs', () => {
      expect(validation.validateUrl('not a url')).toBe(false);
      expect(validation.validateUrl('ftp://example.com')).toBe(false);
      expect(validation.validateUrl('//example.com')).toBe(false);
      expect(validation.validateUrl('example.com')).toBe(false);
      expect(validation.validateUrl('')).toBe(false);
    });
  });

  describe('validateCurrency', () => {
    it('should validate positive currency amounts', () => {
      expect(validation.validateCurrency('100')).toBe(true);
      expect(validation.validateCurrency('100.00')).toBe(true);
      expect(validation.validateCurrency('1,000.50')).toBe(true);
      expect(validation.validateCurrency('$1,000.50')).toBe(true);
      expect(validation.validateCurrency('999999.99')).toBe(true);
    });

    it('should validate negative currency amounts', () => {
      expect(validation.validateCurrency('-100')).toBe(true);
      expect(validation.validateCurrency('-100.00')).toBe(true);
      expect(validation.validateCurrency('-$1,000.50')).toBe(true);
    });

    it('should reject invalid currency amounts', () => {
      expect(validation.validateCurrency('100.999')).toBe(false); // Too many decimals
      expect(validation.validateCurrency('abc')).toBe(false);
      expect(validation.validateCurrency('100,00')).toBe(false); // Wrong decimal separator
      expect(validation.validateCurrency('')).toBe(false);
    });
  });

  describe('validateDate', () => {
    it('should validate correct date formats', () => {
      expect(validation.validateDate('2024-01-01')).toBe(true);
      expect(validation.validateDate('2024-12-31')).toBe(true);
      expect(validation.validateDate('2024-02-29')).toBe(true); // Leap year
    });

    it('should reject invalid dates', () => {
      expect(validation.validateDate('2024-13-01')).toBe(false); // Invalid month
      expect(validation.validateDate('2024-01-32')).toBe(false); // Invalid day
      expect(validation.validateDate('2023-02-29')).toBe(false); // Not a leap year
      expect(validation.validateDate('01-01-2024')).toBe(false); // Wrong format
      expect(validation.validateDate('2024/01/01')).toBe(false); // Wrong separator
      expect(validation.validateDate('')).toBe(false);
    });
  });

  describe('validateRequired', () => {
    it('should validate non-empty values', () => {
      expect(validation.validateRequired('value')).toBe(true);
      expect(validation.validateRequired(123)).toBe(true);
      expect(validation.validateRequired(['item'])).toBe(true);
      expect(validation.validateRequired({ key: 'value' })).toBe(true);
      expect(validation.validateRequired(true)).toBe(true);
      expect(validation.validateRequired(false)).toBe(true);
      expect(validation.validateRequired(0)).toBe(true);
    });

    it('should reject empty values', () => {
      expect(validation.validateRequired('')).toBe(false);
      expect(validation.validateRequired(null)).toBe(false);
      expect(validation.validateRequired(undefined)).toBe(false);
      expect(validation.validateRequired([])).toBe(false);
      expect(validation.validateRequired({})).toBe(false);
    });

    it('should reject whitespace-only strings', () => {
      expect(validation.validateRequired('   ')).toBe(false);
      expect(validation.validateRequired('\t\n')).toBe(false);
    });
  });

  describe('validateMinLength', () => {
    it('should validate strings meeting minimum length', () => {
      expect(validation.validateMinLength('hello', 3)).toBe(true);
      expect(validation.validateMinLength('hello', 5)).toBe(true);
      expect(validation.validateMinLength('hello world', 5)).toBe(true);
    });

    it('should reject strings below minimum length', () => {
      expect(validation.validateMinLength('hi', 3)).toBe(false);
      expect(validation.validateMinLength('', 1)).toBe(false);
    });

    it('should handle non-string values', () => {
      expect(validation.validateMinLength(null as any, 1)).toBe(false);
      expect(validation.validateMinLength(123 as any, 1)).toBe(false);
    });
  });

  describe('validateMaxLength', () => {
    it('should validate strings within maximum length', () => {
      expect(validation.validateMaxLength('hello', 10)).toBe(true);
      expect(validation.validateMaxLength('hello', 5)).toBe(true);
      expect(validation.validateMaxLength('', 5)).toBe(true);
    });

    it('should reject strings exceeding maximum length', () => {
      expect(validation.validateMaxLength('hello world', 5)).toBe(false);
      expect(validation.validateMaxLength('toolong', 5)).toBe(false);
    });
  });

  describe('validateRange', () => {
    it('should validate numbers within range', () => {
      expect(validation.validateRange(5, 1, 10)).toBe(true);
      expect(validation.validateRange(1, 1, 10)).toBe(true);
      expect(validation.validateRange(10, 1, 10)).toBe(true);
      expect(validation.validateRange(0, -10, 10)).toBe(true);
    });

    it('should reject numbers outside range', () => {
      expect(validation.validateRange(0, 1, 10)).toBe(false);
      expect(validation.validateRange(11, 1, 10)).toBe(false);
      expect(validation.validateRange(-5, 0, 10)).toBe(false);
    });

    it('should handle non-numeric values', () => {
      expect(validation.validateRange('5' as any, 1, 10)).toBe(false);
      expect(validation.validateRange(null as any, 1, 10)).toBe(false);
    });
  });

  describe('validatePattern', () => {
    it('should validate strings matching pattern', () => {
      expect(validation.validatePattern('ABC123', /^[A-Z]+[0-9]+$/)).toBe(true);
      expect(validation.validatePattern('hello', /^[a-z]+$/)).toBe(true);
      expect(validation.validatePattern('123-45-6789', /^\d{3}-\d{2}-\d{4}$/)).toBe(true);
    });

    it('should reject strings not matching pattern', () => {
      expect(validation.validatePattern('abc123', /^[A-Z]+[0-9]+$/)).toBe(false);
      expect(validation.validatePattern('Hello', /^[a-z]+$/)).toBe(false);
      expect(validation.validatePattern('*********', /^\d{3}-\d{2}-\d{4}$/)).toBe(false);
    });
  });

  describe('validateCompanyData', () => {
    it('should validate complete company data', () => {
      const validCompany = {
        name: 'Test Company',
        email: '<EMAIL>',
        phone: '+*********0',
        website: 'https://testcompany.com',
        address: '123 Main St',
        city: 'Test City',
        state: 'TS',
        country: 'Test Country'
      };

      const result = validation.validateCompanyData(validCompany);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should report multiple validation errors', () => {
      const invalidCompany = {
        name: '', // Required
        email: 'invalid-email', // Invalid format
        phone: '123', // Too short
        website: 'not-a-url', // Invalid URL
        address: '',
        city: '',
        state: '',
        country: ''
      };

      const result = validation.validateCompanyData(invalidCompany);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Company name is required');
      expect(result.errors).toContain('Invalid email address');
      expect(result.errors).toContain('Invalid phone number');
      expect(result.errors).toContain('Invalid website URL');
    });
  });

  describe('validateDealData', () => {
    it('should validate complete deal data', () => {
      const validDeal = {
        name: 'Test Deal',
        value: 10000,
        stage: 'qualification',
        probability: 50,
        close_date: '2024-12-31',
        company_id: 1
      };

      const result = validation.validateDealData(validDeal);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate deal constraints', () => {
      const invalidDeal = {
        name: '', // Required
        value: -100, // Must be positive
        stage: '', // Required
        probability: 150, // Must be 0-100
        close_date: 'invalid-date', // Invalid format
        company_id: null // Required
      };

      const result = validation.validateDealData(invalidDeal);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Deal name is required');
      expect(result.errors).toContain('Deal value must be positive');
      expect(result.errors).toContain('Deal stage is required');
      expect(result.errors).toContain('Probability must be between 0 and 100');
      expect(result.errors).toContain('Invalid close date');
      expect(result.errors).toContain('Company is required');
    });
  });

  describe('validateEstimateData', () => {
    it('should validate complete estimate data', () => {
      const validEstimate = {
        estimate_number: 'EST-001',
        project_name: 'Test Project',
        client_name: 'Test Client',
        total_amount: 10000,
        total_hours: 100,
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        valid_until: '2024-02-01'
      };

      const result = validation.validateEstimateData(validEstimate);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should validate estimate constraints', () => {
      const invalidEstimate = {
        estimate_number: '', // Required
        project_name: '', // Required
        client_name: '', // Required
        total_amount: -1000, // Must be positive
        total_hours: -10, // Must be positive
        start_date: '2024-12-31',
        end_date: '2024-01-01', // End before start
        valid_until: 'invalid'
      };

      const result = validation.validateEstimateData(invalidEstimate);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Estimate number is required');
      expect(result.errors).toContain('Project name is required');
      expect(result.errors).toContain('Client name is required');
      expect(result.errors).toContain('Total amount must be positive');
      expect(result.errors).toContain('Total hours must be positive');
      expect(result.errors).toContain('End date must be after start date');
    });
  });

  describe('sanitizeInput', () => {
    it('should remove HTML tags', () => {
      expect(validation.sanitizeInput('<script>alert("xss")</script>Hello')).toBe('Hello');
      expect(validation.sanitizeInput('Hello <b>World</b>')).toBe('Hello World');
      expect(validation.sanitizeInput('<div>Test</div>')).toBe('Test');
    });

    it('should trim whitespace', () => {
      expect(validation.sanitizeInput('  Hello  ')).toBe('Hello');
      expect(validation.sanitizeInput('\tWorld\n')).toBe('World');
    });

    it('should handle special characters', () => {
      expect(validation.sanitizeInput('Hello & World')).toBe('Hello &amp; World');
      expect(validation.sanitizeInput('Price < $100')).toBe('Price &lt; $100');
      expect(validation.sanitizeInput('5 > 3')).toBe('5 &gt; 3');
    });

    it('should handle empty input', () => {
      expect(validation.sanitizeInput('')).toBe('');
      expect(validation.sanitizeInput(null as any)).toBe('');
      expect(validation.sanitizeInput(undefined as any)).toBe('');
    });
  });
});