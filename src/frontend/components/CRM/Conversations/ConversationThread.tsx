/**
 * Conversation Thread Component
 * Shows threaded conversations for an entity (deal, contact, company)
 */

import React, { useState, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  VideoCameraIcon,
  EnvelopeIcon,
  HashtagIcon,
  PlusIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  ClockIcon,
  CheckCircleIcon,
  PauseCircleIcon,
  ArchiveBoxIcon,
  UserCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { format, formatDistanceToNow } from 'date-fns';
import type { ThreadedNote, ConversationThread, ConversationType, NoteStatus } from '../../../../api/repositories/note-repository';
import { getContacts } from '../../../api/crm';
import Badge from '../../shared/Badge';

interface ConversationThreadProps {
  entityType: 'deal' | 'contact' | 'company';
  entityId: string;
  currentUserId?: string;
  onContactClick?: (contactId: string) => void;
}

const conversationTypeIcons: Record<ConversationType, React.ComponentType<any>> = {
  email: EnvelopeIcon,
  call: PhoneIcon,
  meeting: VideoCameraIcon,
  slack: HashtagIcon,
  internal: ChatBubbleLeftRightIcon
};

const statusColors: Record<NoteStatus, string> = {
  open: 'blue',
  resolved: 'green',
  parked: 'yellow',
  archived: 'gray'
};

const ConversationThreadComponent: React.FC<ConversationThreadProps> = ({
  entityType,
  entityId,
  currentUserId = 'system',
  onContactClick
}) => {
  const [expandedThreads, setExpandedThreads] = useState<Set<string>>(new Set());
  const [showNewThread, setShowNewThread] = useState(false);
  const [newThreadData, setNewThreadData] = useState({
    content: '',
    conversationType: 'internal' as ConversationType,
    participants: [] as string[],
    status: 'open' as NoteStatus
  });
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');

  const queryClient = useQueryClient();

  // Fetch all contacts for participant selection
  const { data: contacts = [] } = useQuery('contacts', getContacts, {
    staleTime: 5 * 60 * 1000
  });

  // Fetch conversation threads
  const { data: threads = [], isLoading } = useQuery<ConversationThread[]>(
    ['conversation-threads', entityType, entityId],
    async () => {
      const response = await fetch(`/api/crm/notes/threads?entityType=${entityType}&entityId=${entityId}`);
      if (!response.ok) throw new Error('Failed to fetch conversation threads');
      const result = await response.json();
      return result.data || [];
    },
    {
      refetchInterval: 30 * 1000 // Refresh every 30 seconds
    }
  );

  // Fetch thread details when expanded
  const fetchThreadNotes = async (threadId: string): Promise<ThreadedNote[]> => {
    const response = await fetch(`/api/crm/notes/thread/${threadId}`);
    if (!response.ok) throw new Error('Failed to fetch thread notes');
    const result = await response.json();
    return result.data || [];
  };

  // Create new thread mutation
  const createThreadMutation = useMutation(
    async (data: typeof newThreadData) => {
      const response = await fetch('/api/crm/notes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dealId: entityId, // Legacy field name
          content: data.content,
          conversationType: data.conversationType,
          participants: data.participants,
          status: data.status,
          createdBy: currentUserId
        })
      });
      if (!response.ok) throw new Error('Failed to create thread');
      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['conversation-threads', entityType, entityId]);
        setShowNewThread(false);
        setNewThreadData({
          content: '',
          conversationType: 'internal',
          participants: [],
          status: 'open'
        });
      }
    }
  );

  // Create reply mutation
  const createReplyMutation = useMutation(
    async ({ parentNoteId, content }: { parentNoteId: string; content: string }) => {
      const response = await fetch('/api/crm/notes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          dealId: entityId,
          content,
          parentNoteId,
          createdBy: currentUserId
        })
      });
      if (!response.ok) throw new Error('Failed to create reply');
      return response.json();
    },
    {
      onSuccess: (data, variables) => {
        queryClient.invalidateQueries(['thread-notes', variables.parentNoteId]);
        queryClient.invalidateQueries(['conversation-threads', entityType, entityId]);
        setReplyingTo(null);
        setReplyContent('');
      }
    }
  );

  // Update thread status mutation
  const updateStatusMutation = useMutation(
    async ({ noteId, status }: { noteId: string; status: NoteStatus }) => {
      const response = await fetch(`/api/crm/notes/${noteId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });
      if (!response.ok) throw new Error('Failed to update status');
      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['conversation-threads', entityType, entityId]);
      }
    }
  );

  // Toggle thread expansion
  const toggleThread = (threadId: string) => {
    setExpandedThreads(prev => {
      const next = new Set(prev);
      if (next.has(threadId)) {
        next.delete(threadId);
      } else {
        next.add(threadId);
      }
      return next;
    });
  };

  // Get contact name by ID
  const getContactName = (contactId: string) => {
    const contact = contacts.find(c => c.id === contactId);
    return contact ? `${contact.firstName} ${contact.lastName}` : 'Unknown';
  };

  // Render conversation icon
  const ConversationIcon = ({ type }: { type?: ConversationType }) => {
    const Icon = type ? conversationTypeIcons[type] : ChatBubbleLeftRightIcon;
    return <Icon className="w-4 h-4" />;
  };

  // Render thread status badge
  const ThreadStatusBadge = ({ status }: { status: NoteStatus }) => {
    const colors = {
      open: 'blue',
      resolved: 'green',
      parked: 'yellow',
      archived: 'gray'
    };
    
    const icons = {
      open: null,
      resolved: CheckCircleIcon,
      parked: PauseCircleIcon,
      archived: ArchiveBoxIcon
    };
    
    const Icon = icons[status];
    
    return (
      <Badge variant={colors[status] as any} size="sm">
        {Icon && <Icon className="w-3 h-3 mr-1" />}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Render a single note
  const NoteItem: React.FC<{ note: ThreadedNote; isReply?: boolean }> = ({ note, isReply = false }) => {
    const [showReplies, setShowReplies] = useState(true);
    
    return (
      <div className={`${isReply ? 'ml-12' : ''}`}>
        <div className="flex gap-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <UserCircleIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </div>
          </div>
          <div className="flex-1">
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm text-gray-900 dark:text-white">
                    {note.createdBy}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDistanceToNow(new Date(note.createdAt), { addSuffix: true })}
                  </span>
                  {note.conversationType && (
                    <Badge variant="neutral" size="sm">
                      <ConversationIcon type={note.conversationType} />
                      <span className="ml-1">{note.conversationType}</span>
                    </Badge>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {note.content}
              </p>
              {note.participants && note.participants.length > 0 && (
                <div className="mt-2 flex items-center gap-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Participants:</span>
                  <div className="flex gap-1">
                    {note.participants.map(participantId => (
                      <button
                        key={participantId}
                        onClick={() => onContactClick?.(participantId)}
                        className="text-xs text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                      >
                        {getContactName(participantId)}
                      </button>
                    ))}
                  </div>
                </div>
              )}
              <div className="mt-2 flex items-center gap-3">
                <button
                  onClick={() => setReplyingTo(note.id)}
                  className="text-xs text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                >
                  Reply
                </button>
                {note.replies && note.replies.length > 0 && (
                  <button
                    onClick={() => setShowReplies(!showReplies)}
                    className="text-xs text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300 flex items-center gap-1"
                  >
                    {showReplies ? <ChevronDownIcon className="w-3 h-3" /> : <ChevronRightIcon className="w-3 h-3" />}
                    {note.replies.length} {note.replies.length === 1 ? 'reply' : 'replies'}
                  </button>
                )}
              </div>
            </div>
            
            {/* Reply form */}
            {replyingTo === note.id && (
              <div className="mt-2 ml-8">
                <textarea
                  value={replyContent}
                  onChange={(e) => setReplyContent(e.target.value)}
                  placeholder="Write a reply..."
                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                  rows={3}
                />
                <div className="mt-2 flex justify-end gap-2">
                  <button
                    onClick={() => {
                      setReplyingTo(null);
                      setReplyContent('');
                    }}
                    className="px-3 py-1.5 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => createReplyMutation.mutate({ parentNoteId: note.id, content: replyContent })}
                    disabled={!replyContent.trim() || createReplyMutation.isLoading}
                    className="px-3 py-1.5 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
                  >
                    {createReplyMutation.isLoading ? 'Sending...' : 'Send'}
                  </button>
                </div>
              </div>
            )}
            
            {/* Nested replies */}
            {showReplies && note.replies && note.replies.length > 0 && (
              <div className="mt-3 space-y-3">
                {note.replies.map(reply => (
                  <NoteItem key={reply.id} note={reply} isReply />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render a conversation thread
  const ThreadItem: React.FC<{ thread: ConversationThread }> = ({ thread }) => {
    const isExpanded = expandedThreads.has(thread.threadId);
    const { data: notes = [], isLoading: loadingNotes } = useQuery(
      ['thread-notes', thread.threadId],
      () => fetchThreadNotes(thread.threadId),
      {
        enabled: isExpanded,
        staleTime: 5 * 60 * 1000
      }
    );

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div 
          className="p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50"
          onClick={() => toggleThread(thread.threadId)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${
                thread.conversationType === 'email' ? 'bg-blue-100 dark:bg-blue-900/30' :
                thread.conversationType === 'call' ? 'bg-green-100 dark:bg-green-900/30' :
                thread.conversationType === 'meeting' ? 'bg-purple-100 dark:bg-purple-900/30' :
                thread.conversationType === 'slack' ? 'bg-blue-100 dark:bg-blue-900/30' :
                'bg-gray-100 dark:bg-gray-700'
              }`}>
                <ConversationIcon type={thread.conversationType} />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white line-clamp-1">
                  {thread.topic}
                </h4>
                <div className="flex items-center gap-3 mt-1 text-xs text-gray-500 dark:text-gray-400">
                  <span>{thread.messageCount} messages</span>
                  {thread.participantCount > 0 && (
                    <span>{thread.participantCount} participants</span>
                  )}
                  <span>
                    <ClockIcon className="w-3 h-3 inline mr-1" />
                    {formatDistanceToNow(new Date(thread.lastActivity), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <ThreadStatusBadge status={thread.status} />
              {isExpanded ? <ChevronDownIcon className="w-5 h-5 text-gray-400" /> : <ChevronRightIcon className="w-5 h-5 text-gray-400" />}
            </div>
          </div>
        </div>
        
        {isExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            {loadingNotes ? (
              <div className="animate-pulse space-y-3">
                <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg ml-12"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {notes.map(note => (
                  <NoteItem key={note.id} note={note} />
                ))}
              </div>
            )}
            
            {/* Quick status update */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Update thread status:</span>
                <div className="flex gap-2">
                  {(['open', 'resolved', 'parked', 'archived'] as NoteStatus[]).map(status => (
                    <button
                      key={status}
                      onClick={() => notes[0] && updateStatusMutation.mutate({ noteId: notes[0].id, status })}
                      disabled={thread.status === status || updateStatusMutation.isLoading}
                      className={`px-3 py-1.5 text-xs rounded-lg transition-colors ${
                        thread.status === status
                          ? 'bg-gray-200 dark:bg-gray-700 text-gray-500 cursor-not-allowed'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                      }`}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Conversations
        </h3>
        <button
          onClick={() => setShowNewThread(!showNewThread)}
          className="flex items-center gap-2 px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          <PlusIcon className="w-4 h-4" />
          New Conversation
        </button>
      </div>

      {/* New thread form */}
      {showNewThread && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Conversation Type
              </label>
              <div className="flex gap-2">
                {(['internal', 'email', 'call', 'meeting', 'slack'] as ConversationType[]).map(type => (
                  <button
                    key={type}
                    onClick={() => setNewThreadData(prev => ({ ...prev, conversationType: type }))}
                    className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
                      newThreadData.conversationType === type
                        ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-300 dark:border-purple-700'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    <ConversationIcon type={type} />
                    <span className="text-sm capitalize">{type}</span>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Participants
              </label>
              <select
                multiple
                value={newThreadData.participants}
                onChange={(e) => {
                  const selected = Array.from(e.target.selectedOptions).map(o => o.value);
                  setNewThreadData(prev => ({ ...prev, participants: selected }));
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                size={4}
              >
                {contacts.map(contact => (
                  <option key={contact.id} value={contact.id}>
                    {contact.firstName} {contact.lastName} {contact.company ? `(${contact.company})` : ''}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Hold Ctrl/Cmd to select multiple participants
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Message
              </label>
              <textarea
                value={newThreadData.content}
                onChange={(e) => setNewThreadData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Start the conversation..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
                rows={4}
              />
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowNewThread(false);
                  setNewThreadData({
                    content: '',
                    conversationType: 'internal',
                    participants: [],
                    status: 'open'
                  });
                }}
                className="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={() => createThreadMutation.mutate(newThreadData)}
                disabled={!newThreadData.content.trim() || createThreadMutation.isLoading}
                className="px-4 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
              >
                {createThreadMutation.isLoading ? 'Creating...' : 'Start Conversation'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Thread list */}
      {threads.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" />
          <p>No conversations yet</p>
          <p className="text-sm mt-1">Start a conversation to track discussions</p>
        </div>
      ) : (
        <div className="space-y-3">
          {threads.map(thread => (
            <ThreadItem key={thread.threadId} thread={thread} />
          ))}
        </div>
      )}
    </div>
  );
};

export default ConversationThreadComponent;