// Recharts Type Definitions
// Consolidated from various sources in the codebase

import * as React from 'react';

// This declaration file extends the Recharts components to make them work with TypeScript
// by properly declaring that they can be used as JSX elements
declare module 'recharts' {
  // Base props interface for all Recharts components
  interface ComponentProps {
    [key: string]: any;
    children?: React.ReactNode;
  }

  // Chart components
  export class ResponsiveContainer extends React.Component<ComponentProps> {}
  export class LineChart extends React.Component<ComponentProps> {}
  export class BarChart extends React.Component<ComponentProps> {}
  export class ComposedChart extends React.Component<ComponentProps> {}
  export class PieChart extends React.Component<ComponentProps> {}
  export class AreaChart extends React.Component<ComponentProps> {}
  export class RadarChart extends React.Component<ComponentProps> {}
  export class RadialBarChart extends React.Component<ComponentProps> {}
  export class SankeyChart extends React.Component<ComponentProps> {}
  export class Treemap extends React.Component<ComponentProps> {}
  export class FunnelChart extends React.Component<ComponentProps> {}

  // Axis components
  export class XAxis extends React.Component<ComponentProps> {}
  export class YAxis extends React.Component<ComponentProps> {}
  export class CartesianGrid extends React.Component<ComponentProps> {}
  
  // Tooltip and legend
  export class Tooltip extends React.Component<ComponentProps> {}
  export class Legend extends React.Component<ComponentProps> {}
  
  // Shape components
  export class Line extends React.Component<ComponentProps> {}
  export class Bar extends React.Component<ComponentProps> {}
  export class Area extends React.Component<ComponentProps> {}
  export class Pie extends React.Component<ComponentProps> {}
  export class Radar extends React.Component<ComponentProps> {}
  export class RadialBar extends React.Component<ComponentProps> {}
  export class Sankey extends React.Component<ComponentProps> {}
  export class Funnel extends React.Component<ComponentProps> {}
  export class Scatter extends React.Component<ComponentProps> {}

  // Reference components
  export class ReferenceLine extends React.Component<ComponentProps> {}
  export class ReferenceArea extends React.Component<ComponentProps> {}
  
  // Other components
  export class Cell extends React.Component<ComponentProps> {}
  export class LabelList extends React.Component<ComponentProps> {}
  export class Label extends React.Component<ComponentProps> {}
  export class Brush extends React.Component<ComponentProps> {}
  export class ErrorBar extends React.Component<ComponentProps> {}
  export class Customized extends React.Component<ComponentProps> {}
  export class Sector extends React.Component<ComponentProps> {}
  export class Rectangle extends React.Component<ComponentProps> {}
  export class Dot extends React.Component<ComponentProps> {}
  export class Cross extends React.Component<ComponentProps> {}
  export class Curve extends React.Component<ComponentProps> {}
  export class Polygon extends React.Component<ComponentProps> {}

  // Props interface for TooltipProps
  export interface TooltipProps {
    active?: boolean;
    payload?: any[];
    label?: any;
    content?: React.ReactElement | ((props: any) => React.ReactNode);
  }
}