/**
 * Comprehensive Color Migration Guide
 * Maps all hardcoded colors found in the codebase to Flexoki equivalents
 * 
 * Generated from analysis of 37 files containing hardcoded colors
 */

import { flexokiPalette } from './flexoki-theme';

export const comprehensiveColorMap = {
  // === MOST COMMON COLORS (by frequency) ===
  
  // Harvest Orange (17 occurrences) → Flexoki orange
  '#f36c21': flexokiPalette.orange[600], // DA702C
  '#F36C21': flexokiPalette.orange[600],
  
  // Light grays (10+ occurrences) → Flexoki base scale
  '#f1f5f9': flexokiPalette.base[50],    // F2F0E5
  '#F1F5F9': flexokiPalette.base[50],
  '#2C3E50': flexokiPalette.base[800],   // 403E3C (dark gray)
  '#2c3e50': flexokiPalette.base[800],
  
  // Primary blues (9+ occurrences) → Flexoki blue
  '#3498db': flexokiPalette.blue[600],   // 4385BE
  '#3498DB': flexokiPalette.blue[600],
  '#3B82F6': flexokiPalette.blue[600],
  '#3b82f6': flexokiPalette.blue[600],
  '#4385BE': flexokiPalette.blue[600],   // Already Flexoki blue
  
  // Semantic colors
  '#e74c3c': flexokiPalette.red[600],    // AF3029 (error/danger)
  '#E74C3C': flexokiPalette.red[600],
  '#27ae60': flexokiPalette.green[600],  // 66800B (success)
  '#27AE60': flexokiPalette.green[600],
  
  // === FLEXOKI COLORS ALREADY IN USE ===
  // These are already Flexoki colors, keep as-is
  '#DA702C': flexokiPalette.orange[400], // Flexoki orange
  '#D14D41': flexokiPalette.red[400],    // Flexoki red
  '#879A39': flexokiPalette.green[400],  // Flexoki green
  '#3AA99F': flexokiPalette.cyan[400],   // Flexoki cyan
  '#4385BE': flexokiPalette.blue[400],   // Flexoki blue
  '#9A7AA0': flexokiPalette.purple[400], // Flexoki purple
  '#8B7EC8': flexokiPalette.purple[500], // Flexoki purple
  '#CE5D97': flexokiPalette.magenta[400],// Flexoki magenta
  
  // === GRAY SCALE MAPPING ===
  '#CECDC3': flexokiPalette.base[200],   // Flexoki base-200
  '#9F9D96': flexokiPalette.base[400],   // Flexoki base-400
  '#878580': flexokiPalette.base[500],   // Flexoki base-500
  '#6F6E69': flexokiPalette.base[600],   // Flexoki base-600
  '#6B7280': flexokiPalette.base[600],   // Map to base-600
  '#9ca3af': flexokiPalette.base[400],   // Map to base-400
  '#64748b': flexokiPalette.base[500],   // Map to base-500
  '#374151': flexokiPalette.base[700],   // Map to base-700
  '#4b5563': flexokiPalette.base[600],   // Map to base-600
  '#e5e7eb': flexokiPalette.base[150],   // Map to base-150
  '#f3f4f6': flexokiPalette.base[100],   // Map to base-100
  '#d1d5db': flexokiPalette.base[200],   // Map to base-200
  
  // === ADDITIONAL COLORS FOUND ===
  '#10b981': flexokiPalette.green[600],  // Emerald → green
  '#f59e0b': flexokiPalette.yellow[600], // Amber → yellow
  '#06b6d4': flexokiPalette.cyan[600],   // Cyan
  '#8b5cf6': flexokiPalette.purple[600], // Violet → purple
  '#ec4899': flexokiPalette.magenta[600],// Pink → magenta
  '#ef4444': flexokiPalette.red[600],    // Red
  '#84cc16': flexokiPalette.green[500],  // Lime → green
  '#a855f7': flexokiPalette.purple[500], // Purple
  '#6366f1': flexokiPalette.purple[600], // Indigo → purple
  
  // === SPECIAL CASES ===
  '#ffffff': flexokiPalette.paper,        // White → paper
  '#FFFFFF': flexokiPalette.paper,
  '#000000': flexokiPalette.black,        // Black
  '#000': flexokiPalette.black,
  
  // Transparent variations (preserve transparency)
  'rgba(0, 0, 0, 0.1)': `${flexokiPalette.black}1A`,  // 10% opacity
  'rgba(0, 0, 0, 0.05)': `${flexokiPalette.black}0D`, // 5% opacity
  'rgba(255, 255, 255, 0.1)': `${flexokiPalette.paper}1A`,
  'rgba(255, 255, 255, 0.9)': `${flexokiPalette.paper}E6`,
};

// CSS Variable mapping for components
export const cssVariableMap = {
  // Direct hex to CSS variable mapping
  '#f36c21': 'var(--color-orange-600)',
  '#3498db': 'var(--color-blue-600)', 
  '#27ae60': 'var(--color-green-600)',
  '#e74c3c': 'var(--color-red-600)',
  '#3b82f6': 'var(--color-blue-600)',
  '#10b981': 'var(--color-green-600)',
  '#f1f5f9': 'var(--color-base-50)',
  '#9ca3af': 'var(--color-base-400)',
  '#6b7280': 'var(--color-base-600)',
  '#374151': 'var(--color-base-700)',
};

// Tailwind class mapping
export const tailwindClassMap = {
  // Background colors
  'bg-[#f36c21]': 'bg-orange-600',
  'bg-[#3498db]': 'bg-blue-600',
  'bg-[#27ae60]': 'bg-green-600',
  'bg-[#e74c3c]': 'bg-red-600',
  
  // Text colors
  'text-[#f36c21]': 'text-orange-600',
  'text-[#3498db]': 'text-blue-600',
  'text-[#27ae60]': 'text-green-600',
  'text-[#e74c3c]': 'text-red-600',
  
  // Border colors
  'border-[#f36c21]': 'border-orange-600',
  'border-[#3498db]': 'border-blue-600',
  'border-[#27ae60]': 'border-green-600',
  'border-[#e74c3c]': 'border-red-600',
};

// Priority order for migration
export const migrationPriority = {
  high: [
    'CashflowChart.tsx',
    'allocation-table.css',
    'transaction-card.css',
    'cashflow.css',
    'TaskBreakdownChart.tsx',
    'KnowledgeGraph.tsx',
  ],
  medium: [
    'EstimatesList.tsx',
    'NetworkVisualization.tsx',
    'WeeklyEffortHistogram.tsx',
    'xero-integration.css',
    'crm.css',
    'deal-edit.css',
  ],
  low: [
    'colorUtils.ts', // To be removed
    'SimpleTooltip.tsx',
    'Tooltip.tsx',
    'LoadingIndicator.tsx',
  ]
};