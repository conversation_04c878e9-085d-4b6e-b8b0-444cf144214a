/** * Utility functions for exporting estimate data to CSV format */ import { StaffAllocation } from '../../../types/estimate-types'; import { AllocationWithTotals, ProjectTotals } from '../../../hooks/useEstimateStaffManagement'; import { WeekInfo } from '../../../types/estimate-types'; import { formatCurrency } from '../utils'; /** * Escapes a string value for CSV format by: * 1. Adding double quotes around any values containing commas, quotes, or newlines * 2. Doubling any quotes within the string * * @param value The value to escape for CSV * @returns The escaped value */ const escapeCsvValue = (value: string | number | null | undefined): string => { if (value === null || value === undefined) { return ''; } const stringValue = String(value); // Check if the value needs quoting if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) { // Double any quotes in the value itself const escaped = stringValue.replace(/"/g, '""'); // Return the quoted value return `"${escaped}"`; } return stringValue; }; /** * Creates a CSV row from an array of values, properly escaped * * @param values Array of values for the row * @returns CSV-formatted row as a string */ const createCsvRow = (values: (string | number | null | undefined)[]): string => { return values.map(escapeCsvValue).join(','); }; /** * Formats a date for CSV export * * @param date Date to format * @returns Formatted date string (YYYY-MM-DD) */ const formatDateForCsv = (date: Date | null | undefined): string => { if (!date) return ''; return date.toISOString().split('T')[0]; // YYYY-MM-DD format }; /** * Generates a CSV file containing estimate data and triggers download * * @param formData Estimate form data including client, project, and dates * @param staffAllocations Staff allocations with calculated totals * @param projectTotals Project totals and calculations * @param weeks Array of week information */ export const generateEstimateCsv = ( formData: { clientName: string; projectName: string; startDate: Date | string; endDate: Date | string; }, staffAllocations: AllocationWithTotals[], projectTotals: ProjectTotals, weeks: WeekInfo[] ): void => { if (!formData || !staffAllocations || !projectTotals || !weeks) { console.error('Missing required data for CSV export'); return; } // Convert string dates to Date objects if needed const startDate = typeof formData.startDate === 'string' ? new Date(formData.startDate) : formData.startDate; const endDate = typeof formData.endDate === 'string' ? new Date(formData.endDate) : formData.endDate; // Initialize CSV content let csvContent = ''; const rows: string[] = []; // --- SECTION 1: Configuration --- rows.push(createCsvRow(['Category', 'Item', 'Value'])); rows.push(createCsvRow(['Configuration', 'Client Name', formData.clientName])); rows.push(createCsvRow(['Configuration', 'Project Name', formData.projectName])); rows.push(createCsvRow(['Configuration', 'Start Date', formatDateForCsv(startDate)])); rows.push(createCsvRow(['Configuration', 'End Date', formatDateForCsv(endDate)])); // Empty row as section separator rows.push(''); // --- SECTION 2: Staff Allocation Table --- // Create header row for staff allocation table const headerRow = [ 'Staff Allocation', 'Staff Name', 'Role', 'Proposed Rate', 'Cost Rate', 'Target Rate' ]; // Add week identifiers to header weeks.forEach(week => { headerRow.push(`Week ${week.shortLabel}`); }); // Add totals columns to header headerRow.push('Total Days', 'Total Cost', 'Total Fees', 'Margin %'); rows.push(createCsvRow(headerRow)); // Add rows for each staff member staffAllocations.forEach(staff => { const staffRow = [ 'Staff Allocation', `${staff.firstName} ${staff.lastName}`, staff.projectRole, staff.rateProposedDaily, staff.onbordCostRateDaily, staff.onbordTargetRateDaily ]; // Add allocation for each week weeks.forEach(week => { staffRow.push(staff.weeklyAllocation[week.identifier] || 0); }); // Add calculated totals staffRow.push( staff.totalDays, staff.totalCost, staff.totalFees, staff.dailyGM.toFixed(2) ); rows.push(createCsvRow(staffRow)); }); // Add weekly totals row const weeklyTotalsRow = [ 'Weekly Totals', '', '', '', '', '' ]; // Calculate totals for each week weeks.forEach(week => { const totalForWeek = staffAllocations.reduce( (sum, staff) => sum + (staff.weeklyAllocation[week.identifier] || 0), 0 ); weeklyTotalsRow.push(totalForWeek); }); // Add project totals weeklyTotalsRow.push( projectTotals.totalDays, '', '', '' ); rows.push(createCsvRow(weeklyTotalsRow)); // Empty row as section separator rows.push(''); // --- SECTION 3: Project Totals --- rows.push(createCsvRow(['Project Totals', 'Item', 'Value'])); rows.push(createCsvRow(['Project Totals', 'Total Revenue (Pre-Discount)', projectTotals.totalRevenue])); if (projectTotals.discountType !== 'none' && projectTotals.discountValue > 0) { rows.push(createCsvRow(['Project Totals', 'Discount Type', projectTotals.discountType])); rows.push(createCsvRow(['Project Totals', 'Discount Value', projectTotals.discountValue])); rows.push(createCsvRow(['Project Totals', 'Discount Amount', projectTotals.discountAmount])); rows.push(createCsvRow(['Project Totals', 'Total Revenue (Post-Discount)', projectTotals.discountedRevenue])); } rows.push(createCsvRow(['Project Totals', 'Total Cost', projectTotals.totalCost])); rows.push(createCsvRow(['Project Totals', 'Margin Amount', projectTotals.marginAmount])); rows.push(createCsvRow(['Project Totals', 'Margin %', projectTotals.marginPercentage.toFixed(2)])); rows.push(createCsvRow(['Project Totals', 'Total Days', projectTotals.totalDays])); rows.push(createCsvRow(['Project Totals', 'Average Daily Rate', projectTotals.averageDailyRate])); // Join all rows with newlines csvContent = rows.join('\n'); // Create a Blob with the CSV content const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' }); const url = URL.createObjectURL(blob); // Create a temporary link element to trigger download const link = document.createElement('a'); link.href = url; // Set filename based on project name with fallback const sanitizedProjectName = formData.projectName .replace(/[/\\?%*:|"<>]/g, '-') // Replace invalid filename characters .replace(/\s+/g, '-'); // Replace spaces with hyphens const filename = sanitizedProjectName ? `estimate-${sanitizedProjectName}-detailed.csv` : 'estimate-detailed.csv'; link.setAttribute('download', filename); // Append link to body, click it, and remove it document.body.appendChild(link); link.click(); document.body.removeChild(link); // Release the Blob URL to free memory setTimeout(() => { URL.revokeObjectURL(url); }, 100); };