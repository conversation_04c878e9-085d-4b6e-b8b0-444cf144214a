import React from "react";
import {
  cn,
  formFieldVariants,
  FormFieldVariantProps,
} from "../../../utils/component-variants";

export interface FormFieldProps
  extends React.HTMLAttributes<HTMLDivElement>,
    FormFieldVariantProps {
  label?: string;
  error?: string;
  helpText?: string;
  required?: boolean;
  children: React.ReactNode;
  htmlFor?: string;
}

/**
 * FormField wrapper component for consistent field layout
 *
 * Features:
 * - Consistent spacing and layout
 * - Label, error, and help text management
 * - Inline and stacked variants
 * - Required field indicators
 * - Accessibility features
 * - Dark mode support
 */
export const FormField: React.FC<FormFieldProps> = ({
  className,
  variant = "default",
  size = "default",
  label,
  error,
  helpText,
  required = false,
  children,
  htmlFor,
  ...props
}) => {
  // Generate unique ID if not provided
  const fieldId = htmlFor || `field-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div
      className={cn(
        formFieldVariants({
          variant,
          size,
        }),
        className
      )}
      {...props}
    >
      {/* Label */}
      {label && (
        <label htmlFor={fieldId} className="form-label">
          {label}
          {required && (
            <span className="form-required" aria-label="required">
              *
            </span>
          )}
        </label>
      )}

      {/* Form control */}
      <div className="relative">{children}</div>

      {/* Error message */}
      {error && (
        <div
          id={`${fieldId}-error`}
          className="form-error"
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}

      {/* Help text */}
      {helpText && !error && (
        <div id={`${fieldId}-help`} className="form-help">
          {helpText}
        </div>
      )}
    </div>
  );
};

export default FormField;
