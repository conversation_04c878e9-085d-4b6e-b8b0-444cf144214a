/**
 * Unified Navigation Component
 *
 * This component provides a consistent navigation experience across all screen sizes.
 * It can render either mobile or desktop navigation based on the variant prop.
 * Uses React Router for navigation.
 */

import React from "react";
import { NavLink, useLocation } from "react-router-dom";
import { VISIBLE_NAVIGATION_TABS } from "../../config/navigation";

interface UnifiedNavigationProps {
  variant?: "mobile" | "desktop" | "both";
}

export const UnifiedNavigation: React.FC<UnifiedNavigationProps> = ({
  variant = "both",
}) => {
  const location = useLocation();

  // Helper to determine if a tab is active based on the current path
  const isTabActive = (tabId: string) => {
    // Special case for estimates with UUID
    if (tabId === "estimates" && location.pathname.startsWith("/estimates/")) {
      return true;
    }

    // Special case for CRM routes
    if (tabId === "crm/deals" && location.pathname.startsWith("/crm/")) {
      return true;
    }

    return location.pathname === `/${tabId}`;
  };

  const renderMobileNavigation = () => (
    <nav className="mobile-navigation" aria-label="Main Navigation">
      {/* Only show tabs marked as visible in navigation */}
      {VISIBLE_NAVIGATION_TABS.map((tab) => (
        <NavLink
          key={tab.id}
          to={tab.id === "crm/deals" ? "/crm/deals" : `/${tab.id}`}
          className={({ isActive }) =>
            `nav-tab ${
              isActive || isTabActive(tab.id) ? "nav-tab--active" : ""
            } ${tab.color ? `nav-tab--${tab.color}` : ""}`
          }
          aria-label={tab.label}
        >
          <div className="nav-tab__icon">{tab.icon}</div>
          <span className="nav-tab__label">
            {tab.label}
            {tab.newBadge && <span className="nav-tab__badge">NEW</span>}
          </span>
        </NavLink>
      ))}
    </nav>
  );

  const renderDesktopNavigation = () => (
    <nav className="desktop-navigation" aria-label="Main Navigation">
      {/* Only show tabs marked as visible in navigation */}
      {VISIBLE_NAVIGATION_TABS.map((tab) => (
        <NavLink
          key={tab.id}
          to={tab.id === "crm/deals" ? "/crm/deals" : `/${tab.id}`}
          className={({ isActive }) =>
            `nav-tab ${
              isActive || isTabActive(tab.id)
                ? `nav-tab--active nav-tab--${tab.color || "default"}`
                : ""
            }`
          }
          role="tab"
          aria-selected={isTabActive(tab.id)}
        >
          <span className="nav-tab__content">
            {tab.icon && <span className="nav-tab__icon">{tab.icon}</span>}
            <span className="nav-tab__label">
              {tab.label}
              {tab.newBadge && <span className="nav-tab__badge">NEW</span>}
            </span>
          </span>
        </NavLink>
      ))}
    </nav>
  );

  // Render based on the variant prop
  if (variant === "mobile") {
    return renderMobileNavigation();
  } else if (variant === "desktop") {
    return renderDesktopNavigation();
  } else {
    // Default: render both
    return (
      <>
        {renderMobileNavigation()}
        {renderDesktopNavigation()}
      </>
    );
  }
};
