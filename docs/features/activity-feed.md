# Activity Feed Feature

## Overview

The Activity Feed is a comprehensive system for tracking and displaying all user actions, system events, and integration activities within the financial dashboard application. It provides real-time visibility into what's happening across the system.

## Features

### Core Functionality
- **Real-time Activity Tracking**: All user actions and system events are logged automatically
- **Comprehensive Timeline**: Chronological display of activities with date grouping
- **Advanced Filtering**: Filter by activity type, source, importance, date range, and read status
- **Activity Statistics**: Dashboard showing activity counts, trends, and breakdowns
- **Real-time Updates**: Socket.IO integration for live activity updates

### Activity Types
- **User Activities**: Deal creation/updates, estimate publishing, note additions
- **System Events**: Cashflow projections, bulk operations, data processing
- **Integration Activities**: HubSpot/Xero/Harvest synchronization events
- **Authentication Events**: Login/logout, token refresh activities
- **HubSpot Notes**: Imported engagements (emails, calls, meetings) create activity entries
- **Association Events**: Contact-company and deal-contact linking activities

### Activity Sources
- **User**: Manual actions performed by users
- **System**: Automated system processes
- **HubSpot**: CRM integration activities
- **Xero**: Accounting integration activities  
- **Harvest**: Time tracking integration activities

## Implementation

### Database Schema
The activity feed uses the `activity_feed` table with the following structure:
- Core fields: `id`, `type`, `subject`, `description`, `status`
- Entity relationships: `entity_type`, `entity_id`
- Timing: `due_date`, `completed_date`, `created_at`, `updated_at`
- Metadata: `metadata` (JSON), `importance`, `is_read`
- User tracking: `created_by`, `source`

### API Endpoints
- `GET /api/activity` - Get activity feed with filters
- `GET /api/activity/recent` - Get recent activities
- `GET /api/activity/stats` - Get activity statistics
- `GET /api/activity/unread-count` - Get unread activity count
- `POST /api/activity` - Create new activity
- `PUT /api/activity/:id` - Update activity
- `PUT /api/activity/mark-read` - Mark activities as read
- `DELETE /api/activity/:id` - Delete activity

### Frontend Components
- **ActivityFeedPage**: Main dashboard with filters and timeline
- **ActivityTimeline**: Chronological activity display with infinite scroll
- **ActivityItem**: Individual activity display with expandable details
- **ActivityFilters**: Comprehensive filtering controls
- **ActivityStatsCard**: Statistics dashboard

### Integration Points
- **Deal Tracking**: Automatically logs deal creation, updates, and stage changes
- **HubSpot Service**: Logs sync start, completion, and failure events
  - Notes import: Each imported engagement creates an activity entry
  - Association import: Contact-company and deal-contact links are tracked
- **Xero Integration**: Tracks data synchronization activities
- **Harvest Integration**: Logs time tracking sync events
- **Socket.IO**: Real-time activity broadcasting

## Usage

### Navigation
Access the activity feed through the main navigation menu. The "Activity" tab provides access to the full activity dashboard.

### Filtering Activities
Use the sidebar filters to narrow down activities:
- **Search**: Text search across activity subjects and descriptions
- **Date Range**: Filter by creation date
- **Read Status**: Show all, unread only, or read only
- **Importance**: Filter by low, normal, or high importance
- **Activity Types**: Select specific activity types to display
- **Sources**: Filter by activity source (user, system, integrations)

### Activity Details
Click "Show more" on any activity to view:
- Full description
- Metadata and context information
- Related entity details
- Timestamps and user information

### Real-time Updates
Activities appear automatically as they occur throughout the system. No manual refresh is needed.

## Configuration

### Activity Logging
Activities are logged automatically through the `ActivityLogger` utility:

```typescript
import activityLogger from '../utils/activity-logger';

// Log user actions
await activityLogger.logDealCreated(dealId, dealName, userId);

// Log system events
await activityLogger.logHubSpotSyncCompleted(count, errors);

// Log integration events
await activityLogger.logXeroSyncStarted('invoices');
```

### Real-time Updates
Socket.IO events are automatically emitted for:
- `activity:created` - New activity created
- `activity:updated` - Activity updated
- `activity:deleted` - Activity deleted
- `activity:marked-read` - Activities marked as read

## Maintenance

### Data Cleanup
The system includes automatic cleanup functionality:
- Old activities can be purged using `cleanupOldActivities(daysToKeep)`
- Default retention period is 365 days
- Cleanup can be scheduled as a maintenance task

### Performance Considerations
- Activities are paginated for efficient loading
- Indexes are created on frequently queried fields
- Real-time updates use efficient Socket.IO broadcasting
- Filters use database indexes for fast queries

## Future Enhancements

### Planned Features
- **Activity Notifications**: Email/push notifications for important activities
- **Activity Exports**: Export activity data to CSV/PDF
- **Advanced Analytics**: Activity trends and insights
- **Custom Activity Types**: User-defined activity categories
- **Activity Templates**: Predefined activity formats

### Integration Opportunities
- **Slack Integration**: Post important activities to Slack channels
- **Email Digests**: Daily/weekly activity summaries
- **Mobile App**: Push notifications for mobile users
- **API Webhooks**: External system notifications
