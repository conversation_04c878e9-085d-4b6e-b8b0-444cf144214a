/**
 * Data Validation Utilities
 *
 * These utilities help ensure data consistency across the unified data model
 */

import { Company, CompanyUpdate } from '../types/company-types';
import { CompanySource, RadarState, CompanyPriority } from '../types/shared-types';

/**
 * Valid radar states
 */
const VALID_RADAR_STATES: RadarState[] = ['Strategy', 'Transformation', 'BAU', 'Transition out'];

/**
 * Valid priority levels
 */
const VALID_PRIORITIES: CompanyPriority[] = ['High', 'Medium', 'Low', 'Qualified out'];

/**
 * Valid data sources
 */
const VALID_SOURCES: CompanySource[] = ['HubSpot', 'Harvest', 'Manual'];

/**
 * Validates company radar-specific fields
 *
 * @param companyData Full or partial company data
 * @returns Object with validation results
 */
export function validateCompanyRadarFields(companyData: Partial<Company> | CompanyUpdate): {
  isValid: boolean;
  errors: string[]
} {
  const errors: string[] = [];

  // Validate radar state if provided (allow null to remove from radar)
  if (companyData.radarState !== undefined && companyData.radarState !== null) {
    if (!VALID_RADAR_STATES.includes(companyData.radarState)) {
      errors.push(`Invalid radar state: ${companyData.radarState}. Must be one of: ${VALID_RADAR_STATES.join(', ')} or null`);
    }
  }

  // Validate priority if provided (allow null to clear priority)
  if (companyData.priority !== undefined && companyData.priority !== null) {
    if (!VALID_PRIORITIES.includes(companyData.priority)) {
      errors.push(`Invalid priority: ${companyData.priority}. Must be one of: ${VALID_PRIORITIES.join(', ')} or null`);
    }
  }

  // Validate source if provided
  if (companyData.source !== undefined) {
    if (!VALID_SOURCES.includes(companyData.source as CompanySource)) {
      errors.push(`Invalid source: ${companyData.source}. Must be one of: ${VALID_SOURCES.join(', ')}`);
    }
  }

  // Validate numeric fields
  if (companyData.currentSpend !== undefined && isNaN(Number(companyData.currentSpend))) {
    errors.push('Current spend must be a number');
  }

  if (companyData.potentialSpend !== undefined && isNaN(Number(companyData.potentialSpend))) {
    errors.push('Potential spend must be a number');
  }

  // Validate date fields
  if (companyData.lastInteractionDate !== undefined) {
    try {
      new Date(companyData.lastInteractionDate);
    } catch (e) {
      errors.push('Last interaction date must be a valid date string');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate company data consistency
 *
 * @param companyData Full or partial company data
 * @returns Object with validation results
 */
export function validateCompanyData(companyData: Partial<Company> | CompanyUpdate): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate required fields for creation (if this is a full company)
  if ('id' in companyData && !companyData.id) {
    errors.push('Company ID is required');
  }

  if ('name' in companyData && !companyData.name) {
    errors.push('Company name is required');
  }

  // Add radar-specific validation
  const radarValidation = validateCompanyRadarFields(companyData);
  if (!radarValidation.isValid) {
    errors.push(...radarValidation.errors);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}