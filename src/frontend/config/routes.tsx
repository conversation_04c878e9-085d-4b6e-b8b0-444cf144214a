import React from "react";
import { Navigate, useNavigate } from "react-router-dom";
import { ForwardProjectionPage } from "../components/ForwardProjectionPage";
import SmartForecastPage from "../components/SmartForecastPage";
// Import CustomExpensesTab - use the named export
import { CustomExpensesTab } from "../components/CustomExpensesTab";
import EstimatesList from "../components/Estimate/EstimatesList";
// Import EstimatePage - use the named export
import { EstimatePage } from "../components/EstimatePage";
// Import CRM routes
import { CRMRoutes } from "./crm-routes";
// Import ActivityFeedPage
import ActivityFeedPage from "../components/Activity/ActivityFeedPage";
import ReportsPage from "../components/ReportsPage";
import HelpPage from "../components/HelpPage";
import AccountPage from "../components/AccountPage";
import { useUser } from "../contexts/UserContext";
import { useAuthStatus } from "../hooks/useAuthStatus";
import VersionHistoryPage from "../pages/VersionHistoryPage";
import NotFound from "../components/NotFound";

// AccountPageWrapper component to handle user data fetching
const AccountPageWrapper = () => {
  // Use hooks in the wrapper component
  const { user, organization } = useUser();
  const { logout } = useAuthStatus();

  // If user data is not available, return a loading state
  if (!user) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <AccountPage
      userName={user.name}
      userEmail={user.email}
      organization={organization || { name: "Your Organization", id: "" }}
      onLogout={logout}
    />
  );
};

/**
 * Application routes configuration
 * Defines all routes and their corresponding components
 */
export const routes = [
  {
    path: "/",
    element: <Navigate to="/projection" replace />,
  },
  {
    path: "/projection",
    element: <ForwardProjectionPage />,
  },
  {
    path: "/forecast",
    element: <SmartForecastPage />,
  },
  {
    path: "/expenses",
    element: <CustomExpensesTab />,
  },
  {
    path: "/deals",
    element: <Navigate to="/crm/deals" replace />,
  },
  {
    path: "/crm/*",
    element: <CRMRoutes />,
  },
  {
    path: "/activity",
    element: <ActivityFeedPage />,
  },
  {
    path: "/estimates",
    element: <EstimatesList />,
  },
  {
    path: "/estimates/:uuid",
    element: <EstimatePage />,
  },
  {
    path: "/reports",
    element: <ReportsPage />,
  },
  {
    path: "/help",
    element: <HelpPage />,
  },
  {
    path: "/account",
    element: <AccountPageWrapper />,
  },
  {
    path: "/version-history",
    element: <VersionHistoryPage />,
  },
  {
    path: "*",
    element: <NotFound />,
  },
];
