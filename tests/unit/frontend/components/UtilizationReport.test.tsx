import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { UtilizationReport } from '../../../../src/frontend/components/Reports/UtilizationReport';
import * as api from '../../../../src/frontend/api/harvest';

jest.mock('../../../../src/frontend/api/harvest');

describe('UtilizationReport', () => {
  let queryClient: QueryClient;

  const mockUtilizationData = {
    users: [
      {
        id: '1',
        name: '<PERSON>',
        billable_hours: 120,
        non_billable_hours: 40,
        total_hours: 160,
        capacity_hours: 176,
        utilization_rate: 68.2,
        billable_rate: 150,
        revenue: 18000
      },
      {
        id: '2',
        name: '<PERSON>',
        billable_hours: 140,
        non_billable_hours: 20,
        total_hours: 160,
        capacity_hours: 176,
        utilization_rate: 79.5,
        billable_rate: 175,
        revenue: 24500
      }
    ],
    summary: {
      total_billable_hours: 260,
      total_non_billable_hours: 60,
      total_hours: 320,
      total_capacity: 352,
      average_utilization: 73.9,
      total_revenue: 42500
    }
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });
    (api.getUtilizationReport as jest.Mock).mockResolvedValue(mockUtilizationData);
  });

  const renderReport = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <UtilizationReport />
      </QueryClientProvider>
    );
  };

  it('should render report header', () => {
    renderReport();

    expect(screen.getByText('Utilization Report')).toBeInTheDocument();
    expect(screen.getByText('Select Period')).toBeInTheDocument();
  });

  it('should load and display utilization data', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    expect(screen.getByText('68.2%')).toBeInTheDocument();
    expect(screen.getByText('79.5%')).toBeInTheDocument();
  });

  it('should display summary statistics', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('Average Utilization')).toBeInTheDocument();
      expect(screen.getByText('73.9%')).toBeInTheDocument();
      expect(screen.getByText('Total Revenue')).toBeInTheDocument();
      expect(screen.getByText('$42,500')).toBeInTheDocument();
    });
  });

  it('should change period selection', async () => {
    renderReport();

    const periodSelect = screen.getByLabelText('Select Period');
    fireEvent.change(periodSelect, { target: { value: 'last_month' } });

    await waitFor(() => {
      expect(api.getUtilizationReport).toHaveBeenCalledWith(
        expect.objectContaining({ period: 'last_month' })
      );
    });
  });

  it('should filter by user', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const userFilter = screen.getByPlaceholderText('Filter by user...');
    fireEvent.change(userFilter, { target: { value: 'Jane' } });

    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('should sort by column', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const utilizationHeader = screen.getByText('Utilization %');
    fireEvent.click(utilizationHeader);

    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('Jane Smith'); // Higher utilization first
    expect(rows[2]).toHaveTextContent('John Doe');
  });

  it('should display utilization chart', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByTestId('utilization-chart')).toBeInTheDocument();
    });
  });

  it('should show loading state', () => {
    (api.getUtilizationReport as jest.Mock).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    renderReport();

    expect(screen.getByText('Loading utilization data...')).toBeInTheDocument();
  });

  it('should handle errors', async () => {
    (api.getUtilizationReport as jest.Mock).mockRejectedValue(
      new Error('Failed to load report')
    );

    renderReport();

    await waitFor(() => {
      expect(screen.getByText('Failed to load utilization report')).toBeInTheDocument();
    });
  });

  it('should export to CSV', async () => {
    const mockCreateObjectURL = jest.fn();
    const mockClick = jest.fn();
    
    global.URL.createObjectURL = mockCreateObjectURL;
    global.document.createElement = jest.fn().mockImplementation((tag) => {
      if (tag === 'a') {
        return { click: mockClick, setAttribute: jest.fn() };
      }
      return {};
    });

    renderReport();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const exportButton = screen.getByText('Export CSV');
    fireEvent.click(exportButton);

    expect(mockCreateObjectURL).toHaveBeenCalled();
    expect(mockClick).toHaveBeenCalled();
  });

  it('should toggle between table and chart view', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    const chartViewButton = screen.getByText('Chart View');
    fireEvent.click(chartViewButton);

    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    expect(screen.getByTestId('utilization-chart')).toBeInTheDocument();

    const tableViewButton = screen.getByText('Table View');
    fireEvent.click(tableViewButton);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('should calculate utilization correctly', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Check utilization calculation
    const johnRow = screen.getByText('John Doe').closest('tr');
    expect(johnRow).toHaveTextContent('120h'); // Billable
    expect(johnRow).toHaveTextContent('40h'); // Non-billable
    expect(johnRow).toHaveTextContent('68.2%'); // Utilization
  });

  it('should highlight low utilization', async () => {
    const lowUtilizationData = {
      ...mockUtilizationData,
      users: [
        {
          ...mockUtilizationData.users[0],
          utilization_rate: 45 // Below 50%
        }
      ]
    };

    (api.getUtilizationReport as jest.Mock).mockResolvedValue(lowUtilizationData);
    renderReport();

    await waitFor(() => {
      const utilizationCell = screen.getByText('45.0%');
      expect(utilizationCell).toHaveClass('low-utilization');
    });
  });

  it('should show team comparison', async () => {
    renderReport();

    await waitFor(() => {
      expect(screen.getByText('Team Comparison')).toBeInTheDocument();
    });

    const teamComparisonToggle = screen.getByLabelText('Show team comparison');
    fireEvent.click(teamComparisonToggle);

    expect(screen.getByTestId('team-comparison-chart')).toBeInTheDocument();
  });

  it('should filter by date range', async () => {
    renderReport();

    const startDateInput = screen.getByLabelText('Start Date');
    const endDateInput = screen.getByLabelText('End Date');

    fireEvent.change(startDateInput, { target: { value: '2024-01-01' } });
    fireEvent.change(endDateInput, { target: { value: '2024-01-31' } });

    await waitFor(() => {
      expect(api.getUtilizationReport).toHaveBeenCalledWith(
        expect.objectContaining({
          start_date: '2024-01-01',
          end_date: '2024-01-31'
        })
      );
    });
  });
});