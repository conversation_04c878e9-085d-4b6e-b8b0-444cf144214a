import React from 'react';

/**
 * Overview section content for the help page
 */
export const OverviewSection: React.FC = () => {
  return (
    <>
      <p>
        Upstream is a comprehensive financial management platform
        designed for agencies and service businesses. It provides
        real-time visibility into your current financial position
        while offering powerful forecasting tools to help you make
        informed business decisions.
      </p>

      <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-4">
        Key Features
      </h3>
      <div className="grid md:grid-cols-2 gap-4 mt-3">
        <div className="p-3 border border-blue-200 dark:border-blue-800/40 rounded-lg bg-blue-50/50 dark:bg-blue-900/10">
          <h4 className="font-medium text-blue-800 dark:text-blue-300">
            Financial Integration
          </h4>
          <p className="text-sm mt-1">
            Upstream connects with Xero for accounting data and
            Harvest for project budgets, time tracking, and invoicing
            to provide a unified view of your finances.
          </p>
        </div>

        <div className="p-3 border border-blue-200 dark:border-blue-800/40 rounded-lg bg-blue-50/50 dark:bg-blue-900/10">
          <h4 className="font-medium text-blue-800 dark:text-blue-300">
            Cash Flow Forecasting
          </h4>
          <p className="text-sm mt-1">
            Visualize your financial position up to 90 days in the
            future with accurate forecasts based on your actual
            accounting data and intelligent projections.
          </p>
        </div>

        <div className="p-3 border border-blue-200 dark:border-blue-800/40 rounded-lg bg-blue-50/50 dark:bg-blue-900/10">
          <h4 className="font-medium text-blue-800 dark:text-blue-300">
            Project Estimation
          </h4>
          <p className="text-sm mt-1">
            Create detailed project estimates with staff allocations,
            visual planning tools, and automatic margin calculations
            that integrate with Harvest.
          </p>
        </div>

        <div className="p-3 border border-blue-200 dark:border-blue-800/40 rounded-lg bg-blue-50/50 dark:bg-blue-900/10">
          <h4 className="font-medium text-blue-800 dark:text-blue-300">
            Smart Income Projection
          </h4>
          <p className="text-sm mt-1">
            Project future income based on your project budgets,
            payment terms, and invoice frequency with full
            transparency through the audit log.
          </p>
        </div>
      </div>

      <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mt-6">
        Main Sections
      </h3>
      <div className="mt-3 grid md:grid-cols-2 gap-4">
        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <h4 className="font-medium text-primary dark:text-primary-light flex items-center">
            <svg
              className="w-4 h-4 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            Cashflow
          </h4>
          <p className="text-sm mt-2">
            Visualize your company's cash position over time with
            daily projections up to 90 days in the future. Track
            current balance, projected balance, and money flow with
            interactive transaction lists and customizable thresholds.
          </p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <h4 className="font-medium text-primary dark:text-primary-light flex items-center">
            <svg
              className="w-4 h-4 mr-1.5"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                clipRule="evenodd"
              />
            </svg>
            Smart Forecast
          </h4>
          <p className="text-sm mt-2">
            Generate accurate income projections based on your project
            budgets, payment terms, and invoice frequency. Configure
            settings for each project and view the decision-making
            process through the transparent audit log.
          </p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <h4 className="font-medium text-primary dark:text-primary-light flex items-center">
            <svg
              className="w-4 h-4 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z"
              />
            </svg>
            Expenses
          </h4>
          <p className="text-sm mt-2">
            Add recurring expenses that aren't in your accounting
            system. Create weekly, monthly, quarterly, or annual
            expenses with specific categories to enhance forecast
            accuracy and track predictable costs.
          </p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <h4 className="font-medium text-primary dark:text-primary-light flex items-center">
            <svg
              className="w-4 h-4 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Estimates
            <span className="inline-flex items-center relative -top-1 ml-0.5">
              <span className="text-[8px] px-1 leading-none py-0.5 rounded border border-blue-300 dark:border-blue-600 text-blue-600 dark:text-blue-300 bg-blue-50/70 dark:bg-blue-900/20 font-medium uppercase tracking-tight">
                NEW
              </span>
            </span>
          </h4>
          <p className="text-sm mt-2">
            Create detailed project estimates with staff allocations,
            visual timelines, and automatic pricing. Save drafts
            within Upstream during preparation and publish finalized
            estimates to Harvest for client presentation.
          </p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <h4 className="font-medium text-primary dark:text-primary-light flex items-center">
            <svg
              className="w-4 h-4 mr-1.5"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                clipRule="evenodd"
              />
              <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
            </svg>
            CRM
          </h4>
          <p className="text-sm mt-2">
            Manage your sales pipeline with deal tracking, contact
            management, and relationship mapping. Follow deals from
            initial contact through to project delivery with
            comprehensive activity logging.
          </p>
        </div>

        <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50">
          <h4 className="font-medium text-primary dark:text-primary-light flex items-center">
            <svg
              className="w-4 h-4 mr-1.5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Reports
          </h4>
          <p className="text-sm mt-2">
            Access detailed financial reports and analytics to
            understand your business performance, including utilization
            reports, time tracking analysis, and financial summaries
            from your connected systems.
          </p>
        </div>
      </div>
    </>
  );
};