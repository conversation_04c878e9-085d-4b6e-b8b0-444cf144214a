import React, { useState, useEffect } from "react"; import { DraftEstimateSummary } from "../../../types/api"; import { getDraftEstimates, deleteDraftEstimate } from "../../api/estimates"; import { format } from "date-fns"; interface DraftListModalProps { isOpen: boolean; onClose: () => void; onSelectDraft: (draftId: string) => void; } const DraftListModal: React.FC<DraftListModalProps> = ({ isOpen, onClose, onSelectDraft, }) => { const [drafts, setDrafts] = useState<DraftEstimateSummary[]>([]); const [loading, setLoading] = useState(false); const [error, setError] = useState<string | null>(null); const [deleteLoading, setDeleteLoading] = useState<string | null>(null); // Fetch drafts when modal is opened useEffect(() => { if (isOpen) { fetchDrafts(); } }, [isOpen]); const fetchDrafts = async () => { setLoading(true); setError(null); try { const result = await getDraftEstimates(); setDrafts(result); } catch (err) { console.error("Failed to load draft estimates:", err); setError( err instanceof Error ? err.message : "Failed to load draft estimates" ); } finally { setLoading(false); } }; const handleSelectDraft = (uuid: string) => { onSelectDraft(uuid); onClose(); }; const handleDeleteDraft = async (uuid: string, e: React.MouseEvent) => { e.stopPropagation(); // Prevent selecting the draft when clicking delete if ( !window.confirm( "Are you sure you want to delete this draft? This action cannot be undone." ) ) { return; } setDeleteLoading(uuid); try { await deleteDraftEstimate(uuid); setDrafts((prev) => prev.filter((draft) => draft.uuid !== uuid)); } catch (err) { console.error("Failed to delete draft:", err); setError(err instanceof Error ? err.message : "Failed to delete draft"); } finally { setDeleteLoading(null); } }; if (!isOpen) return null; return ( <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"> <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full max-h-[80vh] flex flex-col"> <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4"> <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200"> Saved Draft Estimates </h2> <button onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" > <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /> </svg> </button> </div> <div className="overflow-y-auto p-4 flex-grow"> {loading ? ( <div className="flex justify-center items-center h-32"> <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> </div> ) : error ? ( <div className="text-center py-8"> <p className="text-red-600 dark:text-red-400 mb-4">{error}</p> <button onClick={fetchDrafts} className="px-4 py-2 bg-primary text-white rounded-lg " > Try Again </button> </div> ) : drafts.length === 0 ? ( <div className="text-center py-8 text-gray-500 dark:text-gray-400"> <p>No draft estimates found.</p> <p className="mt-2 text-sm"> Create a new estimate and save it as a draft to see it here. </p> </div> ) : ( <ul className="space-y-2"> {drafts.map((draft) => ( <li key={draft.uuid} onClick={() => handleSelectDraft(draft.uuid)} className="p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-150" > <div className="flex justify-between items-start"> <div> <h3 className="font-medium text-gray-800 dark:text-gray-200"> {draft.projectName || `Project for ${draft.clientName}`} </h3> <p className="text-sm text-gray-600 dark:text-gray-400 mt-1"> {draft.clientName} </p> <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400"> <span className="mr-3"> {format(new Date(draft.startDate), "MMM d, yyyy")} -{" "} {format(new Date(draft.endDate), "MMM d, yyyy")} </span> <span className="flex items-center"> <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> {format(new Date(draft.updatedAt), "MMM d, h:mm a")} </span> </div> </div> <div className="flex items-center"> <span className={`px-2 py-1 text-xs rounded ${ draft.status === "published" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" } mr-2`} > {draft.status === "published" ? "Published" : "Draft"} </span> <button onClick={(e) => handleDeleteDraft(draft.uuid, e)} className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1" disabled={deleteLoading === draft.uuid} > {deleteLoading === draft.uuid ? ( <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> ) : ( <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /> </svg> )} </button> </div> </div> </li> ))} </ul> )} </div> <div className="border-t border-gray-200 dark:border-gray-700 p-4"> <button onClick={onClose} className="w-full px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-150" > Close </button> </div> </div> </div> ); }; export default DraftListModal; 