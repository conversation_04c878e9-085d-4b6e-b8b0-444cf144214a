import React, { useState, useEffect, useCallback } from "react";
import { Command } from "cmdk";
import { useNavigate } from "react-router-dom";

// Define types for search items
interface SearchItem {
  id: string;
  name: string;
  type: "deal" | "contact" | "company" | "estimate" | "expense" | "report";
  url: string;
  keywords?: string[];
}

interface SearchCommandProps {
  isOpen: boolean;
  onClose: () => void;
}

const SearchCommand: React.FC<SearchCommandProps> = ({ isOpen, onClose }) => {
  const [searchValue, setSearchValue] = useState("");
  const navigate = useNavigate();

  // Mock data - this would come from API in a real implementation
  const searchItems: SearchItem[] = [
    { id: "deals", name: "CRM Board", type: "deal", url: "/crm/deals" },
    { id: "contacts", name: "Contacts", type: "contact", url: "/crm/contacts" },
    {
      id: "companies",
      name: "Companies",
      type: "company",
      url: "/crm/companies",
    },
    { id: "estimates", name: "Estimates", type: "estimate", url: "/estimates" },
    { id: "expenses", name: "Expenses", type: "expense", url: "/expenses" },
    {
      id: "cashflow",
      name: "Cashflow Projection",
      type: "report",
      url: "/cashflow",
    },
    { id: "reports", name: "Reports", type: "report", url: "/reports" },
  ];

  const handleSelect = useCallback(
    (id: string) => {
      const selected = searchItems.find((item) => item.id === id);
      if (selected) {
        navigate(selected.url);
        onClose();
        setSearchValue("");
      }
    },
    [navigate, onClose, searchItems]
  );

  // Handle Escape key to close
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen, onClose]);

  // Reset search value when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setSearchValue("");
    }
  }, [isOpen]);

  // Debug the current state to see if it's working properly
  useEffect(() => {
    if (isOpen) {
      console.log("Search dialog open, value:", searchValue);
      console.log(
        "Filtered items:",
        searchItems.filter(
          (item) =>
            !searchValue ||
            item.name.toLowerCase().includes(searchValue.toLowerCase())
        )
      );
    }
  }, [isOpen, searchValue, searchItems]);

  if (!isOpen) return null;

  // Always show all items initially
  const filteredItems =
    searchValue.trim() === ""
      ? searchItems
      : searchItems.filter(
          (item) =>
            item.name.toLowerCase().includes(searchValue.toLowerCase()) ||
            item.keywords?.some((keyword) =>
              keyword.toLowerCase().includes(searchValue.toLowerCase())
            )
        );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-[20vh]">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg overflow-hidden">
        <Command
          label="Global Command Menu"
          shouldFilter={false} // Disable cmdk's internal filtering
        >
          <div className="border-b border-gray-200 dark:border-gray-700 px-4 py-3">
            <Command.Input
              value={searchValue}
              onValueChange={setSearchValue}
              placeholder="Search for anything..."
              autoFocus
            />
          </div>

          <Command.List>
            <Command.Empty>No results found.</Command.Empty>

            <Command.Group heading="Pages">
              {filteredItems.map((item) => (
                <Command.Item
                  key={item.id}
                  value={item.id}
                  onSelect={handleSelect}
                >
                  <span className="mr-2 text-sm px-1.5 py-0.5 rounded bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300">
                    {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                  </span>
                  {item.name}
                </Command.Item>
              ))}
            </Command.Group>
          </Command.List>

          <div className="border-t border-gray-200 dark:border-gray-700 px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex justify-between">
              <span>
                Press{" "}
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">
                  ↑
                </kbd>{" "}
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">
                  ↓
                </kbd>{" "}
                to navigate
              </span>
              <span>
                Press{" "}
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">
                  Enter
                </kbd>{" "}
                to select
              </span>
            </div>
          </div>
        </Command>
      </div>
    </div>
  );
};

export default SearchCommand;
