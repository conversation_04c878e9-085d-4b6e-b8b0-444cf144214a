import { logger, LogLevel, setLog<PERSON>evel, createLogger } from '../../../src/utils/logger';

describe('Logger Utilities', () => {
  let consoleSpy: {
    log: jest.SpyInstance;
    warn: jest.SpyInstance;
    error: jest.SpyInstance;
    debug: jest.SpyInstance;
  };

  beforeEach(() => {
    consoleSpy = {
      log: jest.spyOn(console, 'log').mockImplementation(),
      warn: jest.spyOn(console, 'warn').mockImplementation(),
      error: jest.spyOn(console, 'error').mockImplementation(),
      debug: jest.spyOn(console, 'debug').mockImplementation()
    };
    
    // Reset log level to default
    setLogLevel(LogLevel.INFO);
  });

  afterEach(() => {
    Object.values(consoleSpy).forEach(spy => spy.mockRestore());
  });

  describe('Basic Logging', () => {
    it('should log info messages', () => {
      logger.info('Info message', { data: 'test' });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[INFO]'),
        'Info message',
        { data: 'test' }
      );
    });

    it('should log warning messages', () => {
      logger.warn('Warning message');

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.stringContaining('[WARN]'),
        'Warning message'
      );
    });

    it('should log error messages', () => {
      const error = new Error('Test error');
      logger.error('Error occurred', error);

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR]'),
        'Error occurred',
        error
      );
    });

    it('should log debug messages when level allows', () => {
      setLogLevel(LogLevel.DEBUG);
      logger.debug('Debug info');

      expect(consoleSpy.debug).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG]'),
        'Debug info'
      );
    });
  });

  describe('Log Levels', () => {
    it('should respect log level settings', () => {
      setLogLevel(LogLevel.WARN);

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');

      expect(consoleSpy.debug).not.toHaveBeenCalled();
      expect(consoleSpy.log).not.toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalledTimes(1);
      expect(consoleSpy.error).toHaveBeenCalledTimes(1);
    });

    it('should handle ERROR level', () => {
      setLogLevel(LogLevel.ERROR);

      logger.info('Info');
      logger.warn('Warning');
      logger.error('Error');

      expect(consoleSpy.log).not.toHaveBeenCalled();
      expect(consoleSpy.warn).not.toHaveBeenCalled();
      expect(consoleSpy.error).toHaveBeenCalledTimes(1);
    });

    it('should handle NONE level', () => {
      setLogLevel(LogLevel.NONE);

      logger.debug('Debug');
      logger.info('Info');
      logger.warn('Warning');
      logger.error('Error');

      expect(consoleSpy.debug).not.toHaveBeenCalled();
      expect(consoleSpy.log).not.toHaveBeenCalled();
      expect(consoleSpy.warn).not.toHaveBeenCalled();
      expect(consoleSpy.error).not.toHaveBeenCalled();
    });
  });

  describe('Custom Loggers', () => {
    it('should create logger with custom namespace', () => {
      const customLogger = createLogger('MyModule');
      customLogger.info('Custom log');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[MyModule]'),
        expect.stringContaining('[INFO]'),
        'Custom log'
      );
    });

    it('should support nested namespaces', () => {
      const apiLogger = createLogger('API');
      const authLogger = apiLogger.child('Auth');
      
      authLogger.info('Authentication successful');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[API:Auth]'),
        expect.stringContaining('[INFO]'),
        'Authentication successful'
      );
    });
  });

  describe('Structured Logging', () => {
    it('should log with metadata', () => {
      logger.info('User action', {
        userId: '123',
        action: 'login',
        timestamp: new Date().toISOString()
      });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.any(String),
        'User action',
        expect.objectContaining({
          userId: '123',
          action: 'login',
          timestamp: expect.any(String)
        })
      );
    });

    it('should serialize errors properly', () => {
      const error = new Error('Test error');
      error.stack = 'Error: Test error\n    at test.js:10:5';
      
      logger.error('Operation failed', { error });

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.any(String),
        'Operation failed',
        expect.objectContaining({
          error: expect.objectContaining({
            message: 'Test error',
            stack: expect.stringContaining('test.js:10:5')
          })
        })
      );
    });

    it('should handle circular references', () => {
      const obj: any = { name: 'test' };
      obj.circular = obj;

      logger.info('Circular reference', obj);

      expect(consoleSpy.log).toHaveBeenCalled();
      // Should not throw error
    });
  });

  describe('Performance Logging', () => {
    it('should measure execution time', () => {
      const timer = logger.startTimer();
      
      // Simulate some work
      const work = () => {
        let sum = 0;
        for (let i = 0; i < 1000; i++) {
          sum += i;
        }
        return sum;
      };
      
      work();
      timer.done('Work completed');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.any(String),
        'Work completed',
        expect.objectContaining({
          duration: expect.any(Number)
        })
      );
    });

    it('should log slow operations as warnings', () => {
      const timer = logger.startTimer({ warnThreshold: 100 });
      
      // Simulate slow operation
      jest.advanceTimersByTime(150);
      
      timer.done('Slow operation');

      expect(consoleSpy.warn).toHaveBeenCalledWith(
        expect.any(String),
        'Slow operation',
        expect.objectContaining({
          duration: expect.any(Number),
          slow: true
        })
      );
    });
  });

  describe('Context and Correlation', () => {
    it('should include request context', () => {
      const requestLogger = logger.withContext({
        requestId: 'req-123',
        userId: 'user-456'
      });

      requestLogger.info('Processing request');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.any(String),
        'Processing request',
        expect.objectContaining({
          requestId: 'req-123',
          userId: 'user-456'
        })
      );
    });

    it('should merge contexts', () => {
      const baseLogger = logger.withContext({ app: 'myapp' });
      const requestLogger = baseLogger.withContext({ requestId: 'req-123' });

      requestLogger.info('Nested context');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.any(String),
        'Nested context',
        expect.objectContaining({
          app: 'myapp',
          requestId: 'req-123'
        })
      );
    });
  });

  describe('Environment-based Behavior', () => {
    let originalEnv: string | undefined;

    beforeEach(() => {
      originalEnv = process.env.NODE_ENV;
    });

    afterEach(() => {
      process.env.NODE_ENV = originalEnv;
    });

    it('should use JSON format in production', () => {
      process.env.NODE_ENV = 'production';
      const prodLogger = createLogger('ProdTest');
      
      prodLogger.info('Production log', { data: 123 });

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringMatching(/^\{.*\}$/) // JSON format
      );
    });

    it('should use human-readable format in development', () => {
      process.env.NODE_ENV = 'development';
      const devLogger = createLogger('DevTest');
      
      devLogger.info('Development log');

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.not.stringMatching(/^\{.*\}$/), // Not JSON
        'Development log'
      );
    });

    it('should suppress debug logs in production by default', () => {
      process.env.NODE_ENV = 'production';
      const prodLogger = createLogger('ProdTest');
      
      prodLogger.debug('Debug message');

      expect(consoleSpy.debug).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should capture error stack traces', () => {
      const error = new Error('Test error');
      
      logger.error('Operation failed', error);

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.any(String),
        'Operation failed',
        error
      );
    });

    it('should handle non-Error objects', () => {
      logger.error('String error', 'Something went wrong');
      logger.error('Object error', { code: 'ERR_001', message: 'Failed' });

      expect(consoleSpy.error).toHaveBeenCalledTimes(2);
    });

    it('should log unhandled promise rejections', () => {
      const unhandledRejection = new Error('Unhandled rejection');
      
      logger.logUnhandledRejection(unhandledRejection);

      expect(consoleSpy.error).toHaveBeenCalledWith(
        expect.stringContaining('[FATAL]'),
        'Unhandled Promise Rejection',
        unhandledRejection
      );
    });
  });

  describe('Batch Logging', () => {
    it('should batch multiple log entries', async () => {
      const batchLogger = logger.createBatch({ 
        flushInterval: 100,
        maxBatchSize: 5 
      });

      batchLogger.info('Message 1');
      batchLogger.info('Message 2');
      batchLogger.info('Message 3');

      expect(consoleSpy.log).not.toHaveBeenCalled();

      // Wait for flush
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(consoleSpy.log).toHaveBeenCalledWith(
        expect.stringContaining('[BATCH]'),
        expect.arrayContaining([
          expect.objectContaining({ message: 'Message 1' }),
          expect.objectContaining({ message: 'Message 2' }),
          expect.objectContaining({ message: 'Message 3' })
        ])
      );
    });

    it('should flush on max batch size', () => {
      const batchLogger = logger.createBatch({ maxBatchSize: 2 });

      batchLogger.info('Message 1');
      batchLogger.info('Message 2');

      expect(consoleSpy.log).toHaveBeenCalledTimes(1);
    });
  });
});