import React, { useState, useEffect } from "react";
import { Contact } from "../../../types/crm-types";
import { useQuery } from "react-query";
import { getContacts } from "../../../api/crm";
import ContactCard from "./ContactCard";
import ContactDetail from "./ContactDetail";
import { useLocation, useNavigate } from "react-router-dom";

/**
 * Component for displaying and managing contacts
 */
const ContactsList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Fetch contacts using React Query
  const {
    data: contacts = [],
    isLoading,
    error,
  } = useQuery("contacts", getContacts);

  // Handle URL-based selection
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const selectedId = params.get('selected');
    
    if (selectedId && contacts.length > 0) {
      const contact = contacts.find(c => c.id === selectedId);
      if (contact) {
        setSelectedContact(contact);
      }
    } else if (!selectedId) {
      setSelectedContact(null);
    }
  }, [location.search, contacts]);

  // Filter contacts based on search term
  const filteredContacts = contacts.filter((contact) => {
    const fullName = `${contact.firstName} ${contact.lastName}`.toLowerCase();
    const email = (contact.email || "").toLowerCase();
    const search = searchTerm.toLowerCase();

    // Check in legacy company data
    const legacyCompany = (contact.company?.name || "").toLowerCase();

    // Check in new relationship model
    let matchesCompanyRelationship = false;
    if (contact.companies && contact.companies.length > 0) {
      matchesCompanyRelationship = contact.companies.some((rel) =>
        (rel.company.name || "").toLowerCase().includes(search)
      );
    }

    return (
      fullName.includes(search) ||
      email.includes(search) ||
      legacyCompany.includes(search) ||
      matchesCompanyRelationship
    );
  });

  // Handle selecting a contact for detailed view
  const handleSelectContact = (contact: Contact) => {
    // Update URL to persist selection
    navigate(`/crm/contacts?selected=${contact.id}`);
  };

  // Close the detail view
  const handleCloseDetail = () => {
    // Remove selection from URL
    navigate('/crm/contacts');
  };

  return (
    <div className="w-full max-w-full px-4 md:px-6 lg:px-8 py-4 md:py-8 overflow-x-hidden">
      <div className="space-y-6 max-w-6xl mx-auto">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Contacts
          </h1>
        </div>

      {/* Search bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg
            className="h-5 w-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <input
          type="text"
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Search contacts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>

      {/* Contact detail modal */}
      {selectedContact && (
        <ContactDetail contact={selectedContact} onClose={handleCloseDetail} />
      )}

      {/* Main content */}
      {isLoading ? (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert"
        >
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline">
            {" "}
            Failed to load contacts. Please try again later.
          </span>
        </div>
      ) : filteredContacts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredContacts.map((contact) => (
            <ContactCard
              key={contact.id}
              contact={contact}
              onClick={() => handleSelectContact(contact)}
            />
          ))}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="text-center py-12">
            <svg
              className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
              />
            </svg>
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">
              {searchTerm ? "No contacts found" : "No contacts yet"}
            </h3>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm
                ? `No contacts match "${searchTerm}". Try a different search term.`
                : "No contacts available."}
            </p>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default ContactsList;
