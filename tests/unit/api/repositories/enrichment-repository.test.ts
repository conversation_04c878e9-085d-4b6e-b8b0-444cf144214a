/**
 * Enrichment Repository Unit Tests
 * 
 * Tests for the enrichment repository focusing on database operations.
 * These tests use an in-memory SQLite database for isolation.
 */

import { EnrichmentRepository } from '../../../../src/api/repositories/enrichment-repository';
import { Database } from '../../../../src/database';
import { runMigrations } from '../../../../src/api/services/db-init';

describe('EnrichmentRepository', () => {
  let repository: EnrichmentRepository;
  let db: Database;
  
  beforeAll(async () => {
    // Create in-memory database for testing
    db = new Database(':memory:');
    
    // Run migrations to set up schema
    await runMigrations(db);
  });
  
  beforeEach(() => {
    repository = new EnrichmentRepository();
    (repository as any).db = db; // Inject test database
    
    // Clear data before each test
    db.prepare('DELETE FROM company_enrichment').run();
    db.prepare('DELETE FROM contact_enrichment').run();
    db.prepare('DELETE FROM enrichment_log').run();
    db.prepare('DELETE FROM company').run();
    db.prepare('DELETE FROM contact').run();
  });
  
  afterAll(() => {
    db.close();
  });

  // Test data factories
  const createTestCompany = (id: string = 'comp-123') => {
    db.prepare(`
      INSERT INTO company (
        id, name, created_at, updated_at, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?)
    `).run(id, 'Test Company', new Date().toISOString(), new Date().toISOString(), 'test', 'test');
    return id;
  };

  const createTestContact = (id: string = 'cont-123', companyId?: string) => {
    db.prepare(`
      INSERT INTO contact (
        id, first_name, last_name, email, company_id, created_at, updated_at, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).run(
      id, 'John', 'Doe', '<EMAIL>', companyId || null,
      new Date().toISOString(), new Date().toISOString(), 'test', 'test'
    );
    return id;
  };

  describe('saveCompanyEnrichment', () => {
    it('should save company enrichment data successfully', async () => {
      const companyId = createTestCompany();
      const enrichmentData = {
        companyId,
        source: 'abn_lookup' as const,
        sourceId: 'abn-***********',
        data: {
          abn: '12 ***********',
          abnStatus: 'Active',
          entityType: 'Australian Private Company'
        },
        confidence: 0.95,
        expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        createdBy: 'test-user'
      };

      const enrichmentId = await repository.saveCompanyEnrichment(enrichmentData);

      expect(enrichmentId).toBeTruthy();
      expect(typeof enrichmentId).toBe('string');

      // Verify data was saved correctly
      const savedData = db.prepare(`
        SELECT * FROM company_enrichment WHERE id = ?
      `).get(enrichmentId) as any;

      expect(savedData).toBeTruthy();
      expect(savedData.company_id).toBe(companyId);
      expect(savedData.source).toBe('abn_lookup');
      expect(savedData.source_id).toBe('abn-***********');
      expect(savedData.confidence_score).toBe(0.95);
      expect(savedData.created_by).toBe('test-user');
      expect(JSON.parse(savedData.data)).toEqual(enrichmentData.data);
    });

    it('should save enrichment without optional fields', async () => {
      const companyId = createTestCompany();
      const enrichmentData = {
        companyId,
        source: 'abn_lookup' as const,
        data: { abn: '12 ***********' },
        confidence: 0.8
      };

      const enrichmentId = await repository.saveCompanyEnrichment(enrichmentData);

      const savedData = db.prepare(`
        SELECT * FROM company_enrichment WHERE id = ?
      `).get(enrichmentId) as any;

      expect(savedData.source_id).toBeNull();
      expect(savedData.expires_at).toBeNull();
      expect(savedData.created_by).toBe('system'); // Default value
    });

    it('should handle database errors', async () => {
      // Try to save with non-existent company
      const enrichmentData = {
        companyId: 'non-existent',
        source: 'abn_lookup' as const,
        data: { test: 'data' },
        confidence: 0.8
      };

      await expect(repository.saveCompanyEnrichment(enrichmentData))
        .rejects.toThrow();
    });
  });

  describe('saveContactEnrichment', () => {
    it('should save contact enrichment data successfully', async () => {
      const contactId = createTestContact();
      const enrichmentData = {
        contactId,
        source: 'apollo' as const,
        data: {
          linkedin: 'https://linkedin.com/in/johndoe',
          title: 'Software Engineer'
        },
        confidence: 0.85
      };

      const enrichmentId = await repository.saveContactEnrichment(enrichmentData);

      expect(enrichmentId).toBeTruthy();

      const savedData = db.prepare(`
        SELECT * FROM contact_enrichment WHERE id = ?
      `).get(enrichmentId) as any;

      expect(savedData.contact_id).toBe(contactId);
      expect(savedData.source).toBe('apollo');
      expect(JSON.parse(savedData.data)).toEqual(enrichmentData.data);
    });
  });

  describe('getCompanyEnrichment', () => {
    it('should retrieve all enrichment data for a company', async () => {
      const companyId = createTestCompany();

      // Save multiple enrichments
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: '12 ***********' },
        confidence: 0.95
      });

      await repository.saveCompanyEnrichment({
        companyId,
        source: 'clearbit',
        data: { website: 'test.com' },
        confidence: 0.8
      });

      const enrichments = await repository.getCompanyEnrichment(companyId);

      expect(enrichments).toHaveLength(2);
      expect(enrichments[0].companyId).toBe(companyId);
      expect(enrichments[0].data).toBeTruthy();
      expect(typeof enrichments[0].data).toBe('object');
      
      // Should be ordered by enriched_at DESC
      expect(enrichments[0].source).toBe('clearbit'); // More recent
      expect(enrichments[1].source).toBe('abn_lookup');
    });

    it('should filter by source when specified', async () => {
      const companyId = createTestCompany();

      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: '12 ***********' },
        confidence: 0.95
      });

      await repository.saveCompanyEnrichment({
        companyId,
        source: 'clearbit',
        data: { website: 'test.com' },
        confidence: 0.8
      });

      const abnEnrichments = await repository.getCompanyEnrichment(companyId, 'abn_lookup');

      expect(abnEnrichments).toHaveLength(1);
      expect(abnEnrichments[0].source).toBe('abn_lookup');
    });

    it('should return empty array for non-existent company', async () => {
      const enrichments = await repository.getCompanyEnrichment('non-existent');

      expect(enrichments).toEqual([]);
    });

    it('should handle JSON parsing errors gracefully', async () => {
      const companyId = createTestCompany();

      // Insert invalid JSON directly
      db.prepare(`
        INSERT INTO company_enrichment (
          id, company_id, source, data, confidence_score, enriched_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        'invalid-json-id',
        companyId,
        'test_source',
        'invalid json',
        0.5,
        new Date().toISOString(),
        'test'
      );

      const enrichments = await repository.getCompanyEnrichment(companyId);

      expect(enrichments).toHaveLength(1);
      expect(enrichments[0].data).toEqual({}); // Should default to empty object
    });
  });

  describe('getContactEnrichment', () => {
    it('should retrieve contact enrichment data', async () => {
      const contactId = createTestContact();

      await repository.saveContactEnrichment({
        contactId,
        source: 'apollo',
        data: { linkedin: 'https://linkedin.com/in/johndoe' },
        confidence: 0.85
      });

      const enrichments = await repository.getContactEnrichment(contactId);

      expect(enrichments).toHaveLength(1);
      expect(enrichments[0].contactId).toBe(contactId);
      expect(enrichments[0].source).toBe('apollo');
    });
  });

  describe('getLatestCompanyEnrichment', () => {
    it('should return the most recent enrichment for a source', async () => {
      const companyId = createTestCompany();

      // Save older enrichment
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: 'old data' },
        confidence: 0.7
      });

      // Wait a bit and save newer enrichment
      await new Promise(resolve => setTimeout(resolve, 10));
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: 'new data' },
        confidence: 0.9
      });

      const latest = await repository.getLatestCompanyEnrichment(companyId, 'abn_lookup');

      expect(latest).toBeTruthy();
      expect(latest?.data.abn).toBe('new data');
      expect(latest?.confidence).toBe(0.9);
    });

    it('should return null when no enrichment exists', async () => {
      const latest = await repository.getLatestCompanyEnrichment('non-existent', 'abn_lookup');

      expect(latest).toBeNull();
    });
  });

  describe('updateCompanyEnrichmentStatus', () => {
    it('should update company enrichment status', async () => {
      const companyId = createTestCompany();
      const status = {
        lastEnriched: new Date().toISOString(),
        sources: {
          abn_lookup: { success: true, confidence: 0.95 },
          clearbit: { success: false, error: 'API error' }
        }
      };

      await repository.updateCompanyEnrichmentStatus(companyId, status);

      // Verify status was updated
      const company = db.prepare(`
        SELECT enrichment_status, last_enriched_at FROM company WHERE id = ?
      `).get(companyId) as any;

      expect(company.enrichment_status).toBeTruthy();
      expect(JSON.parse(company.enrichment_status)).toEqual(status);
      expect(company.last_enriched_at).toBeTruthy();
    });
  });

  describe('updateContactEnrichmentStatus', () => {
    it('should update contact enrichment status', async () => {
      const contactId = createTestContact();
      const status = {
        lastEnriched: new Date().toISOString(),
        sources: {
          apollo: { success: true, confidence: 0.8 }
        }
      };

      await repository.updateContactEnrichmentStatus(contactId, status);

      const contact = db.prepare(`
        SELECT enrichment_status, last_enriched_at FROM contact WHERE id = ?
      `).get(contactId) as any;

      expect(contact.enrichment_status).toBeTruthy();
      expect(JSON.parse(contact.enrichment_status)).toEqual(status);
    });
  });

  describe('logEnrichmentAttempt', () => {
    it('should log successful enrichment attempt', async () => {
      const logEntry = {
        entityType: 'company' as const,
        entityId: 'comp-123',
        source: 'abn_lookup' as const,
        status: 'success' as const,
        responseTimeMs: 1500,
        apiCreditsUsed: 1
      };

      await repository.logEnrichmentAttempt(logEntry);

      const logs = db.prepare(`
        SELECT * FROM enrichment_log WHERE entity_id = ?
      `).all('comp-123') as any[];

      expect(logs).toHaveLength(1);
      expect(logs[0].entity_type).toBe('company');
      expect(logs[0].source).toBe('abn_lookup');
      expect(logs[0].status).toBe('success');
      expect(logs[0].response_time_ms).toBe(1500);
      expect(logs[0].api_credits_used).toBe(1);
    });

    it('should log failed enrichment attempt', async () => {
      const logEntry = {
        entityType: 'company' as const,
        entityId: 'comp-123',
        source: 'abn_lookup' as const,
        status: 'failed' as const,
        errorMessage: 'Network timeout',
        responseTimeMs: 10000
      };

      await repository.logEnrichmentAttempt(logEntry);

      const logs = db.prepare(`
        SELECT * FROM enrichment_log WHERE entity_id = ?
      `).all('comp-123') as any[];

      expect(logs).toHaveLength(1);
      expect(logs[0].status).toBe('failed');
      expect(logs[0].error_message).toBe('Network timeout');
    });

    it('should not throw on logging errors', async () => {
      // Invalid entity type should not break the main flow
      const logEntry = {
        entityType: 'invalid' as any,
        entityId: 'comp-123',
        source: 'abn_lookup' as const,
        status: 'success' as const
      };

      // Should not throw even if logging fails
      await expect(repository.logEnrichmentAttempt(logEntry)).resolves.not.toThrow();
    });
  });

  describe('getCompaniesNeedingEnrichment', () => {
    it('should return companies that have never been enriched', async () => {
      // Create companies with no enrichment
      createTestCompany('comp-1');
      createTestCompany('comp-2');
      
      // Create company with enrichment
      const enrichedCompanyId = createTestCompany('comp-3');
      await repository.saveCompanyEnrichment({
        companyId: enrichedCompanyId,
        source: 'abn_lookup',
        data: { abn: '***********' },
        confidence: 0.9
      });

      const companies = await repository.getCompaniesNeedingEnrichment(10);

      expect(companies.length).toBeGreaterThanOrEqual(2);
      expect(companies.some(c => c.id === 'comp-1')).toBe(true);
      expect(companies.some(c => c.id === 'comp-2')).toBe(true);
      expect(companies.some(c => c.id === 'comp-3')).toBe(false);
    });

    it('should return companies with expired enrichment', async () => {
      const companyId = createTestCompany();
      
      // Create expired enrichment
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);
      
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: '***********' },
        confidence: 0.9,
        expiresAt: pastDate
      });

      const companies = await repository.getCompaniesNeedingEnrichment(10);

      expect(companies.some(c => c.id === companyId)).toBe(true);
    });

    it('should exclude soft-deleted companies', async () => {
      // Create and then soft delete a company
      const deletedCompanyId = createTestCompany('deleted-comp');
      db.prepare(`
        UPDATE company SET deleted_at = ? WHERE id = ?
      `).run(new Date().toISOString(), deletedCompanyId);

      const companies = await repository.getCompaniesNeedingEnrichment(10);

      expect(companies.some(c => c.id === deletedCompanyId)).toBe(false);
    });

    it('should respect the limit parameter', async () => {
      // Create multiple companies
      for (let i = 0; i < 5; i++) {
        createTestCompany(`comp-${i}`);
      }

      const companies = await repository.getCompaniesNeedingEnrichment(3);

      expect(companies.length).toBeLessThanOrEqual(3);
    });

    it('should parse enrichment status JSON', async () => {
      const companyId = createTestCompany();
      
      // Update company with enrichment status
      const status = { lastEnriched: new Date().toISOString() };
      db.prepare(`
        UPDATE company SET enrichment_status = ? WHERE id = ?
      `).run(JSON.stringify(status), companyId);

      const companies = await repository.getCompaniesNeedingEnrichment(10);
      const company = companies.find(c => c.id === companyId);

      expect(company?.enrichmentStatus).toEqual(status);
    });
  });

  describe('getContactsNeedingEnrichment', () => {
    it('should return contacts that need enrichment', async () => {
      const companyId = createTestCompany();
      createTestContact('cont-1', companyId);
      createTestContact('cont-2', companyId);

      const contacts = await repository.getContactsNeedingEnrichment(10);

      expect(contacts.length).toBeGreaterThanOrEqual(2);
      expect(contacts[0]).toHaveProperty('firstName');
      expect(contacts[0]).toHaveProperty('lastName');
      expect(contacts[0]).toHaveProperty('email');
    });

    it('should exclude soft-deleted contacts', async () => {
      const deletedContactId = createTestContact('deleted-cont');
      db.prepare(`
        UPDATE contact SET deleted_at = ? WHERE id = ?
      `).run(new Date().toISOString(), deletedContactId);

      const contacts = await repository.getContactsNeedingEnrichment(10);

      expect(contacts.some(c => c.id === deletedContactId)).toBe(false);
    });
  });

  describe('deleteExpiredEnrichments', () => {
    it('should delete expired enrichments', async () => {
      const companyId = createTestCompany();
      const contactId = createTestContact();
      
      // Create expired enrichments
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 10);
      
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: '***********' },
        confidence: 0.9,
        expiresAt: pastDate
      });

      await repository.saveContactEnrichment({
        contactId,
        source: 'apollo',
        data: { linkedin: 'test' },
        confidence: 0.8,
        expiresAt: pastDate
      });

      // Create non-expired enrichment
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 10);
      
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'clearbit',
        data: { website: 'test.com' },
        confidence: 0.7,
        expiresAt: futureDate
      });

      const deletedCount = await repository.deleteExpiredEnrichments();

      expect(deletedCount).toBe(2); // Both expired enrichments

      // Verify non-expired enrichment still exists
      const remaining = await repository.getCompanyEnrichment(companyId);
      expect(remaining).toHaveLength(1);
      expect(remaining[0].source).toBe('clearbit');
    });

    it('should not delete enrichments without expiry dates', async () => {
      const companyId = createTestCompany();
      
      await repository.saveCompanyEnrichment({
        companyId,
        source: 'abn_lookup',
        data: { abn: '***********' },
        confidence: 0.9
        // No expiresAt
      });

      const deletedCount = await repository.deleteExpiredEnrichments();

      expect(deletedCount).toBe(0);
      
      const enrichments = await repository.getCompanyEnrichment(companyId);
      expect(enrichments).toHaveLength(1);
    });

    it('should handle database errors gracefully', async () => {
      // Close the database to simulate error
      const originalPrepare = db.prepare;
      db.prepare = jest.fn().mockImplementation(() => {
        throw new Error('Database error');
      });

      const deletedCount = await repository.deleteExpiredEnrichments();

      expect(deletedCount).toBe(0);
      
      // Restore original function
      db.prepare = originalPrepare;
    });
  });

  describe('Error handling', () => {
    it('should handle database connection errors', async () => {
      // Simulate database error
      const originalPrepare = db.prepare;
      db.prepare = jest.fn().mockImplementation(() => {
        throw new Error('Database connection lost');
      });

      await expect(repository.getCompanyEnrichment('comp-123'))
        .resolves.toEqual([]); // Should return empty array, not throw

      // Restore original function
      db.prepare = originalPrepare;
    });

    it('should handle invalid JSON in enrichment status', async () => {
      const companyId = createTestCompany();
      
      // Insert invalid JSON directly
      db.prepare(`
        UPDATE company SET enrichment_status = ? WHERE id = ?
      `).run('invalid json', companyId);

      const companies = await repository.getCompaniesNeedingEnrichment(10);
      const company = companies.find(c => c.id === companyId);

      expect(company?.enrichmentStatus).toBeNull();
    });
  });
});