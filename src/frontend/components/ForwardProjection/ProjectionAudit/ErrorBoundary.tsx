import React, { Component, ErrorInfo, ReactNode } from 'react'; interface ErrorBoundaryProps { children: ReactNode; fallback?: ReactNode; } interface ErrorBoundaryState { hasError: boolean; error?: Error; } /** * Error boundary component to prevent rendering errors from crashing the application * Used to catch errors like "Objects are not valid as React child" in the Decision Table */ class ErrorBoundaryComponent extends Component<ErrorBoundaryProps, ErrorBoundaryState> { constructor(props: ErrorBoundaryProps) { super(props); this.state = { hasError: false }; } static getDerivedStateFromError(error: Error): ErrorBoundaryState { return { hasError: true, error }; } componentDidCatch(error: Error, errorInfo: ErrorInfo): void { console.error('ErrorBoundary caught an error:', error); console.error('Error details:', errorInfo); } render(): ReactNode { if (this.state.hasError) { return this.props.fallback || ( <div className="p-2 text-xs text-red-600 dark:text-red-400 border border-red-200 dark:border-red-900/30 rounded bg-red-50 dark:bg-red-900/10"> Error rendering component </div> ); } return this.props.children; } } /** * Functional wrapper component for the ErrorBoundary */ const ErrorBoundary = (props: ErrorBoundaryProps): JSX.Element => { return <ErrorBoundaryComponent {...props} />; }; export { ErrorBoundary }; export default ErrorBoundary; // Export the wrapper as ErrorBoundary 