/* Foundation CSS - Tailwind-first component patterns and base styles */
@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

/* === BASE LAYER - Global styles and resets === */
@layer base {
  /* Enhanced CSS reset using Tailwind approach */
  *,
  ::before,
  ::after {
    @apply box-border;
  }

  html {
    @apply antialiased;
  }

  body {
    @apply font-sans text-text dark:text-text-dark bg-background dark:bg-background-dark leading-relaxed;
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-2 outline-offset-2 outline-primary-500;
  }
}

/* === COMPONENT LAYER - Reusable component patterns === */
@layer components {
  /* === ADAPTIVE CARD COMPONENT === */
  /* Replaces .adaptive-card with container query support */
  .card {
    container-type: inline-size;
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-card border border-gray-200 dark:border-gray-700 overflow-hidden;
    padding: theme("spacing.fluid-md");
  }

  /* Container query variants for cards */
  @container (min-width: 300px) {
    .card {
      padding: theme("spacing.fluid-lg");
    }
  }

  @container (min-width: 500px) {
    .card {
      padding: theme("spacing.fluid-xl");
    }
  }

  /* Card variants */
  .card--compact {
    padding: theme("spacing.fluid-sm");
  }

  .card--comfortable {
    padding: theme("spacing.fluid-lg");
  }

  .card--interactive {
    @apply hover:shadow-card-hover transition-shadow duration-300 cursor-pointer;
  }

  .card--bordered {
    @apply border-2 border-primary-200 dark:border-primary-700;
  }

  /* === RESPONSIVE CONTAINER COMPONENT === */
  /* Replaces .responsive-container */
  .responsive-layout {
    container-type: inline-size;
    @apply flex flex-col;
  }

  @container (min-width: 600px) {
    .responsive-layout {
      @apply flex-row;
    }
  }

  /* === BUTTON COMPONENT SYSTEM === */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn--primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white border-transparent focus:ring-primary-500;
  }

  .btn--secondary {
    @apply bg-primary-500 hover:bg-primary-600 text-white border-transparent focus:ring-primary-500;
  }

  .btn--outline {
    @apply bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 focus:ring-gray-500;
  }

  .btn--ghost {
    @apply bg-transparent hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300 border-transparent focus:ring-gray-500;
  }

  .btn--danger {
    @apply bg-red-500 hover:bg-red-600 text-white border-transparent focus:ring-red-500;
  }

  .btn--success {
    @apply bg-green-500 hover:bg-green-600 text-white border-transparent focus:ring-green-500;
  }

  /* Button sizes */
  .btn--sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn--lg {
    @apply px-6 py-3 text-base;
  }

  /* Button states */
  .btn--loading {
    @apply opacity-50 cursor-not-allowed;
  }

  /* === BADGE COMPONENT SYSTEM === */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded text-xs font-medium;
  }

  .badge--primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100;
  }

  .badge--secondary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100;
  }

  .badge--success {
    @apply bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100;
  }

  .badge--warning {
    @apply bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100;
  }

  .badge--danger {
    @apply bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100;
  }

  .badge--outline {
    @apply bg-transparent border border-current;
  }

  /* === FORM COMPONENT SYSTEM === */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-input--error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }

  .form-error {
    @apply text-xs text-red-600 dark:text-red-400 mt-1;
  }

  .form-help {
    @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
  }

  /* Form field wrapper */
  .form-field {
    @apply relative;
  }

  /* Form section grouping */
  .form-section {
    @apply space-y-4;
  }

  .form-section__title {
    @apply text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 pb-2 border-b border-gray-200 dark:border-gray-700;
  }

  .form-section__description {
    @apply text-sm text-gray-600 dark:text-gray-400 mb-4;
  }

  /* Form grid layouts */
  .form-grid {
    @apply grid gap-4;
  }

  .form-grid--cols-1 {
    @apply grid-cols-1;
  }

  .form-grid--cols-2 {
    @apply grid-cols-1 sm:grid-cols-2;
  }

  .form-grid--cols-3 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .form-grid--cols-4 {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Input icons */
  .form-input-icon {
    @apply absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none;
  }

  .form-input-icon svg {
    @apply h-5 w-5 text-gray-400;
  }

  /* Error icons */
  .form-error-icon {
    @apply absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none;
  }

  .form-error-icon svg {
    @apply h-5 w-5 text-red-500;
  }

  /* Success state */
  .form-input--success {
    @apply border-green-300 focus:ring-green-500 focus:border-green-500;
  }

  /* Loading state */
  .form-input--loading {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Required field indicator */
  .form-required {
    @apply text-red-500 ml-1;
  }

  /* === LIST COMPONENT SYSTEM === */
  .list {
    container-type: inline-size;
    @apply space-y-2;
  }

  /* List variants */
  .list--compact {
    @apply space-y-1;
  }

  .list--comfortable {
    @apply space-y-4;
  }

  /* List layouts */
  .list--stack {
    @apply flex flex-col;
  }

  .list--grid {
    @apply grid gap-4;
  }

  @container (min-width: 640px) {
    .list--grid {
      @apply grid-cols-2;
    }
  }

  @container (min-width: 1024px) {
    .list--grid {
      @apply grid-cols-3;
    }
  }

  .list--table {
    @apply divide-y divide-gray-200 dark:divide-gray-700;
  }

  .list--cards {
    @apply grid gap-4 grid-cols-1;
  }

  @container (min-width: 640px) {
    .list--cards {
      @apply grid-cols-2;
    }
  }

  @container (min-width: 1024px) {
    .list--cards {
      @apply grid-cols-3;
    }
  }

  /* List item component */
  .list-item {
    @apply relative transition-colors duration-200;
  }

  .list-item--interactive {
    @apply hover:bg-gray-50 dark:hover:bg-gray-800 dark:hover:bg-opacity-50 cursor-pointer;
  }

  .list-item--selected {
    @apply bg-primary-50 dark:bg-primary-900 dark:bg-opacity-20 border-primary-200 dark:border-primary-700;
  }

  .list-item--disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  /* List item sizes */
  .list-item--compact {
    @apply py-2 px-3;
  }

  .list-item--comfortable {
    @apply py-4 px-6;
  }

  /* List item borders */
  .list-item--bordered {
    @apply border border-gray-200 dark:border-gray-700 rounded-md;
  }

  .list-item--border-left {
    @apply border-l-4 border-l-gray-200 dark:border-l-gray-700 pl-4;
  }

  .list-item--border-full {
    @apply border border-gray-200 dark:border-gray-700 rounded-lg;
  }

  /* === TABLE COMPONENT SYSTEM === */
  .table-container {
    @apply overflow-x-auto bg-white dark:bg-gray-800 shadow-card rounded-lg border border-gray-200 dark:border-gray-700;
    -webkit-overflow-scrolling: touch; /* Enable smooth scrolling on iOS */
  }

  /* Mobile-specific table adjustments */
  @media (max-width: 767px) {
    .table-container {
      @apply rounded-none border-x-0; /* Remove side borders on mobile */
      margin-left: -1rem;
      margin-right: -1rem;
      width: calc(100% + 2rem);
    }

    /* Hide less important columns on mobile */
    .table .hide-mobile {
      display: none;
    }

    /* Reduce padding on mobile */
    .table th,
    .table td {
      @apply px-2 py-3 text-sm;
    }
  }

  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }

  /* Enhanced table variants */
  .table--striped tbody tr:nth-child(even) {
    @apply bg-gray-50 dark:bg-gray-800 dark:bg-opacity-50;
  }

  .table--bordered {
    @apply border border-gray-200 dark:border-gray-700;
  }

  .table--bordered th,
  .table--bordered td {
    @apply border-r border-gray-200 dark:border-gray-700;
  }

  .table--compact th,
  .table--compact td {
    @apply px-3 py-2;
  }

  .table--sm th,
  .table--sm td {
    @apply px-4 py-2 text-xs;
  }

  .table--lg th,
  .table--lg td {
    @apply px-8 py-4 text-base;
  }

  .table--responsive {
    container-type: inline-size;
  }

  @container (max-width: 640px) {
    .table--responsive {
      @apply hidden;
    }
  }

  .table-header {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-150;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }

  /* === DATA LIST COMPONENT SYSTEM === */
  .data-list {
    container-type: inline-size;
    @apply relative;
  }

  .data-list--cards {
    @apply grid gap-4 grid-cols-1;
  }

  @container (min-width: 640px) {
    .data-list--cards {
      @apply grid-cols-2;
    }
  }

  @container (min-width: 1024px) {
    .data-list--cards {
      @apply grid-cols-3;
    }
  }

  .data-list--table {
    @apply divide-y divide-gray-200 dark:divide-gray-700;
  }

  .data-list--grid {
    @apply grid gap-6;
  }

  @container (min-width: 640px) {
    .data-list--grid {
      @apply grid-cols-2;
    }
  }

  @container (min-width: 1024px) {
    .data-list--grid {
      @apply grid-cols-4;
    }
  }

  /* Data list density */
  .data-list--compact .list-item {
    @apply py-2 px-3;
  }

  .data-list--comfortable .list-item {
    @apply py-6 px-8;
  }

  /* Data list loading state */
  .data-list--loading {
    @apply opacity-50 pointer-events-none;
  }

  .data-list--loading::after {
    content: "";
    @apply absolute inset-0 bg-white bg-opacity-50 dark:bg-gray-900 dark:bg-opacity-50 flex items-center justify-center;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='10' stroke='currentColor' stroke-width='4' class='opacity-25'/%3E%3Cpath fill='currentColor' d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z' class='opacity-75'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 2rem;
  }

  /* === EXPENSE LIST COMPONENT SYSTEM === */
  .expense-list-item {
    container-type: inline-size;
  }

  .expense-item-responsive {
    @apply relative;
  }

  /* Default: Show card layout */
  .expense-card-layout {
    @apply block;
  }

  .expense-table-layout {
    @apply hidden;
  }

  /* Container query: Switch to table layout for wider containers */
  @container (min-width: 768px) {
    .expense-card-layout {
      @apply hidden;
    }

    .expense-table-layout {
      @apply block;
    }
  }

  /* === TRANSACTION LIST COMPONENT SYSTEM === */
  .transaction-list-item {
    container-type: inline-size;
  }

  .transaction-item-responsive {
    @apply relative;
  }

  /* Default: Show card layout */
  .transaction-card-layout {
    @apply block;
  }

  .transaction-table-layout {
    @apply hidden;
  }

  /* Container query: Switch to table layout for wider containers */
  @container (min-width: 768px) {
    .transaction-card-layout {
      @apply hidden;
    }

    .transaction-table-layout {
      @apply block;
    }
  }

  /* Transaction table wrapper for proper structure */
  .transaction-table-wrapper {
    container-type: inline-size;
  }

  /* Transaction table headers - sync with list item container queries */
  .transaction-headers-wrapper {
    container-type: inline-size;
  }

  .transaction-table-headers {
    @apply hidden;
  }

  @container (min-width: 768px) {
    .transaction-table-headers {
      @apply block;
    }
  }

  /* Fix DataList alignment for transaction tables */
  .transactions-container .data-list {
    @apply divide-y-0;
  }

  /* Remove DataList item wrapper divs in table mode - CRITICAL FIX */
  @container (min-width: 768px) {
    .transaction-table-content .data-list > div[role="listitem"] {
      display: contents !important;
    }

    /* Also ensure the transaction list items are visible */
    .transaction-list-item {
      display: contents !important;
    }

    /* But keep the table layout visible */
    .transaction-list-item .transaction-table-layout {
      display: block !important;
    }

    /* Hide the card layout */
    .transaction-list-item .transaction-card-layout {
      display: none !important;
    }
  }

  /* Ensure list items don't add extra padding in table mode */
  .transaction-table-layout .list-item,
  .decision-table-layout .list-item {
    padding: 0 !important;
  }

  /* SIMPLIFIED Transaction table grid layout */
  .transaction-table-grid {
    display: grid !important;
    grid-template-columns: 3rem 7rem 1fr 1fr auto 10rem;
    gap: 1rem;
    align-items: center;
    width: 100%;
    padding: 0 1rem;
  }

  /* Headers need the exact same structure */
  .transaction-headers-wrapper .transaction-table-grid {
    padding: 0.625rem 1rem;
  }

  /* Content rows */
  .transaction-table-layout .transaction-table-grid {
    padding: 0.75rem 1rem;
  }

  /* === DECISION LIST COMPONENT SYSTEM === */
  .decision-list-item {
    container-type: inline-size;
  }

  .decision-item-responsive {
    @apply relative;
  }

  /* Default: Show card layout */
  .decision-card-layout {
    @apply block;
  }

  .decision-table-layout {
    @apply hidden;
  }

  /* Container query: Switch to table layout for wider containers */
  @container (min-width: 768px) {
    .decision-card-layout {
      @apply hidden;
    }

    .decision-table-layout {
      @apply block;
    }
  }

  /* Decision table wrapper for proper structure */
  .decision-table-wrapper {
    container-type: inline-size;
  }

  /* Decision table headers - sync with list item container queries */
  .decision-headers-wrapper {
    container-type: inline-size;
  }

  .decision-table-headers {
    @apply hidden;
  }

  @container (min-width: 768px) {
    .decision-table-headers {
      @apply block;
    }
  }

  /* Fix DataList alignment for decision tables */
  .decision-container .data-list {
    @apply divide-y-0;
  }

  /* Remove DataList item wrapper divs in table mode - CRITICAL FIX */
  @container (min-width: 768px) {
    .decision-table-content .data-list > div[role="listitem"] {
      display: contents !important;
    }

    /* Also ensure the decision list items are visible */
    .decision-list-item {
      display: contents !important;
    }

    /* But keep the table layout visible */
    .decision-list-item .decision-table-layout {
      display: block !important;
    }

    /* Hide the card layout */
    .decision-list-item .decision-card-layout {
      display: none !important;
    }
  }

  /* Remove default list item hover states in table mode */
  .transaction-table-layout .list-item--interactive:hover,
  .decision-table-layout .list-item--interactive:hover {
    background-color: transparent;
  }

  /* SIMPLIFIED Decision table grid layout */
  .decision-table-grid {
    display: grid !important;
    grid-template-columns: 4rem 1fr 7rem 6rem 8rem 10rem;
    gap: 1rem;
    align-items: center;
    width: 100%;
    padding: 0 1rem;
  }

  /* Headers need the exact same structure */
  .decision-headers-wrapper .decision-table-grid {
    padding: 0.625rem 1rem;
  }

  /* Content rows */
  .decision-table-layout .decision-table-grid {
    padding: 0.75rem 1rem;
  }

  /* === FLOATING PANEL SYSTEM === */
  .floating-panel {
    @apply fixed z-40 flex flex-col items-end transition-all duration-300;
  }

  .floating-actions {
    @apply bottom-4 right-4;
    width: 220px;
  }

  .floating-finance {
    @apply bottom-52 right-4;
    width: 220px;
  }

  /* === LOADING STATES === */
  .loading-spinner {
    @apply animate-spin h-5 w-5 border-2 border-primary-500 border-t-transparent rounded-full;
  }

  .loading-dots {
    @apply flex space-x-1;
  }

  .loading-dot {
    @apply h-2 w-2 bg-primary-500 rounded-full;
  }

  .loading-dot:nth-child(1) {
    @apply animate-ellipsis1;
  }

  .loading-dot:nth-child(2) {
    @apply animate-ellipsis2;
  }

  .loading-dot:nth-child(3) {
    @apply animate-ellipsis3;
  }

  /* === UTILITY PATTERNS === */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme("colors.gray.400") theme("colors.gray.100");
  }

  .scrollbar-thin::-webkit-scrollbar {
    @apply w-2;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500 dark:bg-gray-500;
  }

  /* === LEGACY COMPATIBILITY === */
  /* Keep existing classes during transition */
  .adaptive-card {
    @apply card;
  }

  .responsive-container {
    @apply responsive-layout;
  }

  .default-button {
    @apply btn btn--primary;
  }
}

/* === UTILITY LAYER - Additional utilities === */
@layer utilities {
  /* Container query utilities for better responsive design */
  .container-responsive {
    container-type: inline-size;
  }

  /* Fluid typography utilities */
  .text-fluid-responsive {
    font-size: clamp(0.875rem, 2.5vw, 1.125rem);
    line-height: 1.6;
  }

  /* Enhanced focus utilities */
  .focus-ring-inset {
    @apply focus:ring-2 focus:ring-inset focus:ring-primary-500;
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Animation utilities */
  @keyframes animate-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-in {
    animation: animate-in 0.3s ease-out;
  }

  .fade-in-0 {
    opacity: 0;
    animation-fill-mode: forwards;
  }

  .zoom-in-95 {
    transform: scale(0.95);
    animation-fill-mode: forwards;
  }

  .animate-fade-in-up {
    animation: fadeIn 0.5s ease-out, slideInLeft 0.5s ease-out;
  }

  /* Accessibility utilities */
  .sr-only-focusable:focus {
    @apply not-sr-only;
  }

  /* Full width utility for estimates feature */
  .full-width-container {
    @apply w-full max-w-none px-4 lg:px-8;
  }
}
