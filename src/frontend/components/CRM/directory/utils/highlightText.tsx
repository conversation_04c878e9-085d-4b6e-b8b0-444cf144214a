import React from 'react';

/**
 * Highlights matching text within a string
 * Returns a React element with highlighted spans
 */
export const highlightText = (text: string | undefined, query: string): React.ReactNode => {
  if (!text || !query) return text || '';

  // Escape special regex characters
  const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  try {
    const regex = new RegExp(`(${escapedQuery})`, 'gi');
    const parts = text.split(regex);

    return (
      <>
        {parts.map((part, index) => {
          const isMatch = part.toLowerCase() === query.toLowerCase();
          return isMatch ? (
            <mark key={index} className="bg-yellow-200 dark:bg-yellow-900 text-inherit rounded px-0.5">
              {part}
            </mark>
          ) : (
            <span key={index}>{part}</span>
          );
        })}
      </>
    );
  } catch (error) {
    // If regex fails, return original text
    return text;
  }
};

/**
 * Check if any of the fields contain the search query
 */
export const matchesSearch = (item: any, query: string, fields: string[]): boolean => {
  if (!query) return true;
  
  const lowerQuery = query.toLowerCase();
  
  return fields.some(field => {
    const value = field.split('.').reduce((obj, key) => obj?.[key], item);
    return value && String(value).toLowerCase().includes(lowerQuery);
  });
};