# Database Schema Fixes Summary

## Overview
This document summarizes all the database schema fixes that were applied to resolve various constraint violations and missing column errors.

## Root Cause Analysis
The main issues were:
1. **Schema-Code Mismatch**: The unified schema was missing columns that the code expected
2. **Constraint Violations**: CHECK constraints were too restrictive or mismatched with actual data
3. **Format Mismatches**: Frontend and backend had different expectations for data formats

## Fixes Applied

### 1. Missing Columns
Added columns that the code expected but were missing from the schema:

**Company Table**:
- `size`, `address`, `radar_state`, `priority`
- `current_spend`, `potential_spend`, `last_interaction_date`, `notes`

**Contact Table**:
- `notes`

**Deal Table**:
- `harvest_id` (for backward compatibility alongside `harvest_project_id`)

**Expense Table**:
- `name`, `type`, `frequency`, `repeat_count`
- `source`, `editable`, `metadata`

**Deal_Estimate Table**:
- `harvest_estimate_id`

### 2. Column Name Fixes

**Activity_Feed Table**:
- Changed from `activity_type` → `type`
- Added `subject`, `status`, `due_date`, `completed_date`, `is_read`
- Removed redundant actor/target fields

**Field_Ownership Table**:
- Code expected `owner` but schema had `owner_system`
- Fixed in `deal-tracking.ts` to use correct column name

### 3. Constraint Fixes

**Source Constraints**:
- Updated to accept both lowercase and capitalized values
- `'harvest'` and `'Harvest'`, `'hubspot'` and `'HubSpot'`, etc.

**Change_Log Constraints**:
- Added mapping in code to convert data sources to valid values
- `'HubSpot'` → `'sync'`, `'Manual'` → `'ui'`, etc.
- Added missing `change_type` column with default 'update'

**Company Creation Logic**:
- Fixed incorrect assumption that `clientId` is always a Harvest ID
- Now checks if it's an actual company ID first
- Uses `source='manual'` for UI-created companies

**Week Identifier Format**:
- Changed from `YYYY-WW` (week number) to `YYYY-MM-DD` (date)
- Frontend was using dates, database expected week numbers
- Updated CHECK constraint and removed complex GENERATED column

## Migration Scripts

Created comprehensive migration scripts:
- `fix-missing-columns-final.sql` - Adds all missing columns and fixes constraints
- Handles both new installations and existing databases

## Key Learnings

1. **Always verify column existence** before assuming the schema matches the code
2. **CHECK constraints should be flexible** enough to handle real-world data variations
3. **Frontend and backend formats must align** - dates vs week numbers was a fundamental mismatch
4. **Test with actual user workflows** to catch these issues early

## Testing Recommendations

1. Create a new estimate to verify week allocation works
2. Import data from HubSpot to test source constraints
3. Update deals to test field ownership and change logging
4. Create expenses to test the new columns

### 5. Estimate System Decoupling from Harvest (January 2025)

**Problem**: 
- Estimates were tightly coupled to Harvest, requiring companies to have a `harvest_id` before creating estimates
- `clientId` field expected Harvest ID (integer) but received company UUID (string), causing foreign key errors
- System assumed all estimates needed Harvest integration at creation time

**Solution**:
- Changed `clientId` (number) to `companyId` (string) throughout the system
- Updated all SQL queries to reference companies by UUID instead of Harvest ID
- Removed automatic company creation/lookup logic from estimate repository
- Harvest integration now only required when publishing estimates to Harvest

**Files Updated**:
- Type definitions: Changed interfaces from `clientId: number` to `companyId: string`
- Repository queries: Updated from `c.harvest_id as clientId` to `e.company_id as companyId`
- Frontend components: Updated to use `companyId` and check for `harvestId` only when publishing
- API routes: Updated to use `companyId` in all estimate operations

**Benefits**:
- Estimates can be created for any company, regardless of Harvest integration
- Cleaner separation between internal operations and external integrations
- More flexible system that supports various business scenarios

## Status
All known database constraint violations have been resolved. The schema now properly supports all existing code paths. The estimate system has been successfully decoupled from Harvest, allowing for more flexible estimate creation workflows.