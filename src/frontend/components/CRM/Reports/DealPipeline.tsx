import React, { useMemo, useState } from "react"; import { useQuery } from "react-query"; import { getDeals } from "../../../api/crm"; import { Deal, DealStage } from "../../../types/crm-types"; import SimpleTooltip, { TooltipData } from "../../common/SimpleTooltip"; /** * Component for displaying deal pipeline reports */ const DealPipeline: React.FC = () => { // Fetch deals const { data: deals = [], isLoading, error } = useQuery("deals", getDeals); // Tooltip state const [tooltip, setTooltip] = useState<TooltipData>({ visible: false, content: "", x: 0, y: 0, }); // Handle mouse enter for tooltips const handleMouseEnter = ( e: React.MouseEvent, title: string, content: string ) => { setTooltip({ visible: true, title, content, x: e.clientX, y: e.clientY, }); }; // Handle mouse leave const handleMouseLeave = () => { setTooltip((prev) => ({ ...prev, visible: false })); }; // Define all possible deal stages const DEAL_STAGES: DealStage[] = [ "Identified", "Qualified", "Solution proposal", "Solution presentation", "Objection handling", "Finalising terms", "Closed won", "Closed lost", "Abandoned", ]; // Calculate pipeline metrics const pipelineMetrics = useMemo(() => { // Group deals by stage const dealsByStage = DEAL_STAGES.reduce<Record<DealStage, Deal[]>>( (acc, stage) => { acc[stage] = deals.filter((deal) => deal.stage === stage); return acc; }, {} as Record<DealStage, Deal[]> ); // Calculate total value by stage const valueByStage = DEAL_STAGES.reduce<Record<DealStage, number>>( (acc, stage) => { acc[stage] = dealsByStage[stage].reduce( (sum, deal) => sum + (deal.value || 0), 0 ); return acc; }, {} as Record<DealStage, number> ); // Calculate count by stage const countByStage = DEAL_STAGES.reduce<Record<DealStage, number>>( (acc, stage) => { acc[stage] = dealsByStage[stage].length; return acc; }, {} as Record<DealStage, number> ); // Calculate total pipeline value (excluding Closed won, Closed lost and Abandoned) const totalPipelineValue = Object.entries(valueByStage) .filter( ([stage]) => stage !== "Closed won" && stage !== "Closed lost" && stage !== "Abandoned" ) .reduce((sum, [, value]) => sum + value, 0); // Calculate total won value const totalWonValue = valueByStage["Closed won"] || 0; // Calculate total lost value const totalLostValue = valueByStage["Closed lost"] || 0; // Calculate open deals (all active deals in the pipeline) const openDealsStages = DEAL_STAGES.filter( (stage) => stage !== "Closed won" && stage !== "Closed lost" && stage !== "Abandoned" ); const openDealsCount = openDealsStages.reduce( (sum, stage) => sum + countByStage[stage], 0 ); const openDealsValue = openDealsStages.reduce( (sum, stage) => sum + valueByStage[stage], 0 ); // Calculate weighted deal amount (value * probability) const weightedDealAmount = deals .filter( (deal) => deal.stage !== "Closed won" && deal.stage !== "Closed lost" && deal.stage !== "Abandoned" ) .reduce((sum, deal) => { // Use the deal's probability if available, otherwise use a default based on stage // Deal probability is stored as a decimal (e.g., 0.5 for 50%) const probability = deal.probability !== undefined ? deal.probability // Already in decimal form (e.g., 0.5 for 50%) : getDefaultProbability(deal.stage); return sum + (deal.value || 0) * probability; }, 0); // Helper function to get default probability based on stage function getDefaultProbability(stage: DealStage): number { switch (stage) { case "Identified": return 0.1; // 10% case "Qualified": return 0.3; // 30% case "Solution proposal": return 0.5; // 50% case "Solution presentation": return 0.7; // 70% case "Objection handling": return 0.8; // 80% case "Finalising terms": return 0.9; // 90% default: return 0.5; // 50% default } } // Calculate win rate const closedDeals = countByStage["Closed won"] + countByStage["Closed lost"]; const winRate = closedDeals > 0 ? (countByStage["Closed won"] / closedDeals) * 100 : 0; return { dealsByStage, valueByStage, countByStage, totalPipelineValue, totalWonValue, totalLostValue, openDealsCount, openDealsValue, weightedDealAmount, winRate, }; }, [deals]); // Format currency const formatCurrency = (value: number): string => { return new Intl.NumberFormat("en-AU", { style: "currency", currency: "AUD", maximumFractionDigits: 0, }).format(value); }; // Get stage color const getStageColor = (stage: DealStage): string => { switch (stage) { case "Identified": return "bg-blue-500"; case "Qualified": return "bg-blue-500"; case "Solution proposal": return "bg-purple-500"; case "Solution presentation": return "bg-purple-500"; case "Objection handling": return "bg-yellow-500"; case "Finalising terms": return "bg-orange-500"; case "Closed won": return "bg-green-500"; case "Closed lost": return "bg-red-500"; case "Abandoned": return "bg-gray-500"; default: return "bg-gray-500"; } }; // Calculate the percentage width for the pipeline visualization const getStagePercentage = (stage: DealStage): number => { if (pipelineMetrics.openDealsValue === 0) return 0; if (stage === "Closed lost" || stage === "Abandoned") return 0; // Don't show Closed lost or Abandoned in the pipeline return ( (pipelineMetrics.valueByStage[stage] / pipelineMetrics.openDealsValue) * 100 ); }; if (isLoading) { return ( <div className="flex justify-center items-center h-64"> <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div> </div> ); } if (error) { return ( <div className="btn-modern--primary"border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert" > <strong className="font-bold">Error!</strong> <span className="block sm:inline"> {" "} Failed to load deals. Please try again later. </span> </div> ); } return ( <div className="space-y-6"> {/* Summary cards */} <div className="grid grid-cols-1 md:grid-cols-5 gap-3"> <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-3 cursor-help" onMouseEnter={(e) => handleMouseEnter( e, "Open Deals", "Includes all deals in active stages: Identified, Qualified, Solution proposal, Solution presentation, Objection handling, and Finalising terms." ) } onMouseLeave={handleMouseLeave} > <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400"> Open Deals </h3> <p className="mt-1 text-xl font-semibold text-blue-600 dark:text-blue-400"> {formatCurrency(pipelineMetrics.openDealsValue)} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> {pipelineMetrics.openDealsCount} active deals </p> </div> <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-3 cursor-help" onMouseEnter={(e) => handleMouseEnter( e, "Weighted Deal Amount", "The total value of all open deals multiplied by their probability. This represents the expected revenue from the current pipeline, taking into account the likelihood of each deal closing." ) } onMouseLeave={handleMouseLeave} > <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400"> Weighted Deal Amount </h3> <p className="mt-1 text-xl font-semibold text-purple-600 dark:text-purple-400"> {formatCurrency(pipelineMetrics.weightedDealAmount)} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> Expected revenue </p> </div> <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-3 cursor-help" onMouseEnter={(e) => handleMouseEnter( e, "Won Deals Value", "The total value of all deals in the 'Closed won' stage." ) } onMouseLeave={handleMouseLeave} > <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400"> Won Deals Value </h3> <p className="mt-1 text-xl font-semibold text-green-600 dark:text-green-400"> {formatCurrency(pipelineMetrics.totalWonValue)} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> {pipelineMetrics.countByStage["Closed won"]} deals </p> </div> <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-3 cursor-help" onMouseEnter={(e) => handleMouseEnter( e, "Lost Deals Value", "The total value of all deals in the 'Closed lost' stage." ) } onMouseLeave={handleMouseLeave} > <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400"> Lost Deals Value </h3> <p className="mt-1 text-xl font-semibold text-red-600 dark:text-red-400"> {formatCurrency(pipelineMetrics.totalLostValue)} </p> <p className="text-xs text-gray-500 dark:text-gray-400"> {pipelineMetrics.countByStage["Closed lost"]} deals </p> </div> <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-3 cursor-help" onMouseEnter={(e) => handleMouseEnter( e, "Win Rate", "The percentage of closed deals that were won. Calculated as (Closed won / (Closed won + Closed lost)) × 100%." ) } onMouseLeave={handleMouseLeave} > <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400"> Win Rate </h3> <p className="mt-1 text-xl font-semibold text-gray-900 dark:text-white"> {pipelineMetrics.winRate.toFixed(1)}% </p> <p className="text-xs text-gray-500 dark:text-gray-400"> {pipelineMetrics.countByStage["Closed won"] + pipelineMetrics.countByStage["Closed lost"]}{" "} closed deals </p> </div> </div> {/* Pipeline visualization */} <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4"> <h3 className="text-base font-medium text-gray-900 dark:text-white mb-3"> Deal Pipeline </h3> {deals.length === 0 ? ( <div className="text-center py-4 text-gray-500 dark:text-gray-400"> <p>No deals found.</p> <p className="text-xs"> Create deals to see your pipeline visualization. </p> </div> ) : ( <div className="space-y-4"> {/* Pipeline bar */} <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden flex"> {DEAL_STAGES.filter( (stage) => stage !== "Closed lost" && stage !== "Abandoned" ).map((stage) => ( <div key={stage} className={`${getStageColor(stage)} h-full`} style={{ width: `${getStagePercentage(stage)}%` }} title={`${stage}: ${formatCurrency( pipelineMetrics.valueByStage[stage] )}`} ></div> ))} </div> {/* Legend */} <div className="flex flex-wrap gap-2 text-xs"> {DEAL_STAGES.filter( (stage) => stage !== "Closed lost" && stage !== "Abandoned" ).map((stage) => ( <div key={stage} className="flex items-center"> <div className={`w-2 h-2 rounded-full ${getStageColor( stage )} mr-1`} ></div> <span className="text-xs text-gray-600 dark:text-gray-400"> {stage} </span> </div> ))} </div> {/* Stage details */} <div className="mt-4"> <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs"> <thead className="btn-modern--secondary"dark:bg-gray-700"> <tr> <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" > Stage </th> <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" > Deals </th> <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" > Value </th> <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" > Avg. Value </th> <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider" > % of Pipeline </th> </tr> </thead> <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"> {DEAL_STAGES.map((stage) => ( <tr key={stage}> <td className="px-3 py-2 whitespace-nowrap"> <div className="flex items-center"> <div className={`w-2 h-2 rounded-full ${getStageColor( stage )} mr-1`} ></div> <span className="text-xs font-medium text-gray-900 dark:text-white"> {stage} </span> </div> </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400"> {pipelineMetrics.countByStage[stage]} </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400"> {formatCurrency(pipelineMetrics.valueByStage[stage])} </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400"> {pipelineMetrics.countByStage[stage] > 0 ? formatCurrency( pipelineMetrics.valueByStage[stage] / pipelineMetrics.countByStage[stage] ) : "-"} </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400"> {stage !== "Closed lost" && stage !== "Abandoned" && pipelineMetrics.openDealsValue > 0 ? `${( (pipelineMetrics.valueByStage[stage] / pipelineMetrics.openDealsValue) * 100 ).toFixed(1)}%` : "-"} </td> </tr> ))} {/* Totals row */} <tr className="btn-modern--secondary"dark:bg-gray-700 font-semibold"> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300"> Total </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300"> {Object.values(pipelineMetrics.countByStage).reduce( (sum, count) => sum + count, 0 )} </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300"> {formatCurrency( Object.values(pipelineMetrics.valueByStage).reduce( (sum, value) => sum + value, 0 ) )} </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300"> {formatCurrency( Object.values(pipelineMetrics.valueByStage).reduce( (sum, value) => sum + value, 0 ) / Object.values(pipelineMetrics.countByStage).reduce( (sum, count) => sum + count, 0 ) )} </td> <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300"> 100% </td> </tr> </tbody> </table> </div> </div> )} </div> {/* Tooltip */} <SimpleTooltip tooltip={tooltip} /> </div> ); }; export default DealPipeline; 