/** * Presentation component for Utilization Report * Handles only UI rendering - no data fetching or business logic */ import React from 'react'; import { format } from 'date-fns'; import { StaffUtilization } from '../../../services/harvest/time-report-service'; import { TimePeriod, TIME_PERIODS } from '../../hooks/useDateRange'; // Import the component modules import PeriodSelector from './PeriodSelector'; import UtilizationSummary from './UtilizationSummary'; import UtilizationTable from './UtilizationTable'; import UtilizationSummaryGraph from './UtilizationSummaryGraph'; import TaskBreakdownSummary from './TaskBreakdownSummary'; interface UtilizationReportPresentationProps { // State timePeriod: TimePeriod; customStartDate: string; customEndDate: string; excludeLeave: boolean; isDropdownOpen: boolean; // Date range startDate: Date; endDate: Date; isNextDisabled: boolean; // Data utilizationData: StaffUtilization[]; isLoading: boolean; error?: Error | null; // Actions onTimePeriodChange: (period: TimePeriod) => void; onCustomStartDateChange: (date: string) => void; onCustomEndDateChange: (date: string) => void; onExcludeLeaveChange: (exclude: boolean) => void; onDropdownToggle: (open: boolean) => void; onGoToPrevious: () => void; onGoToNext: () => void; onRefresh: () => void; } /** * Pure presentation component for utilization report */ export const UtilizationReportPresentation: React.FC<UtilizationReportPresentationProps> = ({ // State timePeriod, customStartDate, customEndDate, excludeLeave, isDropdownOpen, // Date range startDate, endDate, isNextDisabled, // Data utilizationData, isLoading, error, // Actions onTimePeriodChange, onCustomStartDateChange, onCustomEndDateChange, onExcludeLeaveChange, onDropdownToggle, onGoToPrevious, onGoToNext, onRefresh }) => { // Format dates for display const formattedStartDate = format(startDate, 'yyyy-MM-dd'); const formattedEndDate = format(endDate, 'yyyy-MM-dd'); // Handle error state if (error) { return ( <div className="container mx-auto p-4"> <div className="btn-modern--primary"dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"> <h3 className="text-red-800 dark:text-red-200 font-semibold mb-2"> Error Loading Utilization Data </h3> <p className="text-red-600 dark:text-red-300 mb-4"> {error.message || 'An error occurred while fetching utilization data.'} </p> <button onClick={onRefresh} className="btn-modern--primary" text-white px-4 py-2 rounded-md text-sm font-medium" > Try Again </button> </div> </div> ); } return ( <div className="container mx-auto p-4"> <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6"> <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 lg:mb-0"> Staff Utilization Report </h1> {/* Period selector and navigation */} <div className="flex flex-col sm:flex-row gap-4"> <PeriodSelector timePeriod={timePeriod} onTimePeriodChange={onTimePeriodChange} customStartDate={customStartDate} customEndDate={customEndDate} onCustomStartDateChange={onCustomStartDateChange} onCustomEndDateChange={onCustomEndDateChange} isDropdownOpen={isDropdownOpen} onDropdownToggle={onDropdownToggle} startDate={startDate} endDate={endDate} onGoToPrevious={onGoToPrevious} onGoToNext={onGoToNext} isNextDisabled={isNextDisabled} /> </div> </div> {/* Leave exclusion toggle */} <div className="mb-6"> <label className="flex items-center space-x-3"> <input type="checkbox" checked={excludeLeave} onChange={(e) => onExcludeLeaveChange(e.target.checked)} className="w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-500 rounded focus:ring-blue-500 dark:focus:ring-blue-600 focus:ring-2" /> <span className="text-sm text-gray-700 dark:text-gray-300"> Exclude leave from capacity calculation </span> </label> </div> {/* Summary and graphs section */} {utilizationData.length > 0 && ( <> {/* Utilization summary */} <UtilizationSummary utilizationData={utilizationData} isLoading={isLoading} fromDate={formattedStartDate} toDate={formattedEndDate} excludeLeave={excludeLeave} /> {/* Utilization graph */} <UtilizationSummaryGraph utilizationData={utilizationData} excludeLeave={excludeLeave} /> {/* Task breakdown summary with loading skeleton */} {isLoading ? ( <div className="bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg p-6 mb-6"> <div className="animate-pulse"> <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div> <div className="space-y-2"> <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div> <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div> <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div> <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div> </div> </div> </div> ) : ( <TaskBreakdownSummary utilizationData={utilizationData} excludeLeave={excludeLeave} /> )} </> )} {/* Table component with task breakdown */} <UtilizationTable utilizationData={utilizationData} isLoading={isLoading} loadingType="harvest" fromDate={formattedStartDate} toDate={formattedEndDate} excludeLeave={excludeLeave} /> {/* Explanation of utilization calculation */} <div className="btn-modern--secondary"dark:bg-gray-700 p-4 rounded-lg mt-6"> <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2"> About Utilization Calculation </h3> <p className="text-sm text-gray-600 dark:text-gray-400"> Utilization is calculated as{" "} <strong>billable hours ÷ capacity</strong>. When &quot;Exclude leave from capacity&quot; is {excludeLeave ? "enabled" : "disabled"}, {excludeLeave ? " leave time is subtracted from capacity, showing utilisation based only on available working time." : " leave time counts against capacity, showing overall utilisation of total possible working time."}{" "} Capacity is retrieved from Harvest for each user (typically 37.5 hours per week), adjusted for the selected time period based on actual workdays (excluding weekends). For time periods that include future dates, capacity is prorated to only include days up to today. This differs from Harvest&apos;s calculation, which uses <strong> billable hours ÷ total hours</strong>. </p> </div> {/* Empty state */} {!isLoading && utilizationData.length === 0 && ( <div className="text-center py-12"> <div className="text-gray-400 mb-4"> <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /> </svg> </div> <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2"> No Utilization Data </h3> <p className="text-gray-500 dark:text-gray-400 mb-4"> No utilization data available for the selected time period. </p> <button onClick={onRefresh} className="btn-modern--primary" text-white px-4 py-2 rounded-md text-sm font-medium" > Refresh Data </button> </div> )} </div> ); }; export default UtilizationReportPresentation;