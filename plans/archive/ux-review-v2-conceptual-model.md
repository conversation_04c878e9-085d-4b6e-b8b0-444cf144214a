# UX Review V2: Conceptual Model - Three Pillars Architecture

## Overview

This document details the new conceptual model for Upstream V2, restructuring the application around three core business pillars that reflect how professional services companies actually operate.

## The Three Pillars Model

### 1. Operations Hub - "Run the Business"

**Purpose**: Day-to-day financial operations and cash management

**Key Questions It Answers**:
- What's our cash position today?
- What bills are coming due?
- Are we getting paid on time?
- What expenses are recurring?

**Features Included**:
```
Operations Hub
├── Cash Position
│   ├── Current Balance (from Xero)
│   ├── 30/60/90 Day Projection
│   └── Daily Transaction Timeline
├── Payables
│   ├── Upcoming Bills
│   ├── Expense Management
│   └── Payroll & Super Schedule
└── Receivables
    ├── Outstanding Invoices
    ├── Overdue Payments
    └── Collection Status
```

**User Personas**: Finance Manager, Operations Manager, Bookkeeper

### 2. Growth Hub - "Grow the Business"

**Purpose**: Revenue generation, client relationships, and pipeline management

**Key Questions It Answers**:
- What's in our pipeline?
- Which clients are most valuable?
- What estimates are pending?
- Where should we focus our BD efforts?

**Features Included**:
```
Growth Hub
├── Pipeline
│   ├── Opportunity Board (Kanban)
│   ├── Deal Details & Progress
│   └── Win/Loss Analytics
├── Clients
│   ├── Client Profiles (360° view)
│   ├── Relationship History
│   ├── Project Performance
│   └── Strategic Value (Radar)
├── Estimates
│   ├── Create New
│   ├── Drafts & Templates
│   ├── Published Estimates
│   └── Conversion Tracking
└── Business Development
    ├── Lead Tracking
    ├── Relationship Mapping
    └── Target Accounts
```

**User Personas**: Sales Manager, Account Manager, Business Development

### 3. Intelligence Hub - "Understand the Business"

**Purpose**: Strategic insights, forecasting, and performance analytics

**Key Questions It Answers**:
- Are we profitable?
- How utilized is our team?
- What's our financial forecast?
- Where can we improve?

**Features Included**:
```
Intelligence Hub
├── Financial Intelligence
│   ├── Smart Forecast (Auto-projections)
│   ├── Scenario Planning
│   ├── P&L Analysis
│   └── Cash Burn Rate
├── Operational Intelligence
│   ├── Team Utilization
│   ├── Project Profitability
│   ├── Resource Planning
│   └── Capacity Analysis
├── Strategic Intelligence
│   ├── Client Portfolio Analysis
│   ├── Revenue Concentration
│   ├── Growth Trends
│   └── Market Positioning
└── Executive Reports
    ├── Monthly Summaries
    ├── Board Reports
    ├── KPI Dashboards
    └── Custom Analytics
```

**User Personas**: CEO, CFO, Operations Director, Board Members

## Command Center (Dashboard)

The Dashboard serves as the mission control, providing:

### Widget Architecture
```
┌─────────────────────────────────────────────────┐
│                  Command Center                  │
├─────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│ │   Today's   │ │   Pipeline  │ │    Team     ││
│ │    Cash     │ │   Health    │ │ Utilization ││
│ │  Position   │ │  $850k/12   │ │    78%      ││
│ └─────────────┘ └─────────────┘ └─────────────┘│
│ ┌─────────────────────────────┐ ┌─────────────┐│
│ │      Action Required         │ │   Quick     ││
│ │ • 3 Overdue invoices        │ │  Actions    ││
│ │ • 2 Estimates expiring      │ │ • New Deal  ││
│ │ • Payroll due tomorrow      │ │ • Record    ││
│ │                             │ │   Expense   ││
│ └─────────────────────────────┘ └─────────────┘│
│ ┌─────────────────────────────────────────────┐│
│ │            Weekly Trend Chart                ││
│ │     [Cash position over last 7 days]        ││
│ └─────────────────────────────────────────────┘│
└─────────────────────────────────────────────────┘
```

### Personalization Rules
- **Role-based widgets**: Different KPIs for different users
- **Customizable layout**: Drag-and-drop widget arrangement
- **Alert preferences**: User-defined thresholds
- **Quick actions**: Based on common tasks per role

## Feature Migration Mapping

### Current → V2 Location

| Current Feature | Current Location | V2 Location | Rationale |
|----------------|------------------|-------------|-----------|
| Cashflow Projection | Main Nav | Operations Hub | Day-to-day cash management |
| Smart Forecast | Main Nav | Intelligence Hub | Strategic forecasting tool |
| Expenses | Main Nav | Operations Hub | Operational expense tracking |
| Deals Board | Main Nav → CRM | Growth Hub → Pipeline | Central to revenue generation |
| Companies | CRM Submenu | Growth Hub → Clients | Unified client view |
| Contacts | CRM Submenu | Growth Hub → Clients | Part of client relationships |
| Estimates | Main Nav | Growth Hub | Part of sales process |
| Client Radar | Leads → Radar | Growth Hub → Clients | Strategic client view |
| Reports | Main Nav | Intelligence Hub | All analytics consolidated |
| Data Management | CRM Submenu | Settings/Admin | Administrative function |

## Information Architecture Principles

### 1. Business Process Alignment
Features are grouped by business process, not technical implementation:
- Financial operations together (even if from different systems)
- All client data unified (regardless of source)
- Analytics consolidated (not scattered)

### 2. Progressive Disclosure
- Dashboard shows summary
- Hubs show category overview
- Individual features show detail
- Advanced features in settings

### 3. Contextual Navigation
- Breadcrumbs show location
- Related actions visible
- Cross-hub links where logical
- Search across all hubs

### 4. Task-Oriented Structure
URLs reflect user tasks:
- `/operations/cash-position`
- `/growth/pipeline/opportunities`
- `/intelligence/forecast/scenarios`

## Entity Relationships in V2

### Unified Client Model
```
Client (formerly Company)
├── Overview
│   ├── Basic Information
│   ├── Key Contacts
│   └── Quick Stats
├── Opportunities
│   ├── Active Deals
│   ├── Won/Lost History
│   └── Pipeline Value
├── Projects
│   ├── Current Projects
│   ├── Completed Work
│   └── Performance Metrics
├── Financials
│   ├── Revenue History
│   ├── Outstanding Invoices
│   └── Profitability
└── Strategy
    ├── Radar Position
    ├── Growth Potential
    └── Relationship Notes
```

### Opportunity Lifecycle
```
Lead → Qualified Lead → Opportunity → Won/Lost
                              ↓
                          Estimate
                              ↓
                      Project (in Harvest)
                              ↓
                          Invoice
                              ↓
                          Payment
```

## Technical Implementation Strategy

### Frontend-Only Changes
1. **New routing structure** - React Router updates
2. **Component reorganization** - Move into hub folders
3. **Shared components** - Reuse existing components
4. **State management** - Existing contexts work as-is

### API Compatibility
- All existing endpoints remain
- New aggregation endpoints for dashboard
- No breaking changes to data model
- Progressive enhancement approach

### Migration Approach
```typescript
// Old route redirects
const redirects = {
  '/projection': '/operations/cashflow',
  '/crm/deals': '/growth/pipeline',
  '/forecast': '/intelligence/smart-forecast',
  // etc...
};

// Feature flags for gradual rollout
const features = {
  v2Navigation: process.env.ENABLE_V2_NAV === 'true',
  v2Dashboard: process.env.ENABLE_V2_DASH === 'true',
  // etc...
};
```

## Success Criteria

### User Understanding
- Users can explain the three pillars
- Navigation choices are obvious
- Features are discoverable
- Mental model matches system model

### Efficiency Metrics
- Time to find features: -50%
- Clicks to complete tasks: -40%
- Cross-hub navigation: <10% of actions
- Dashboard engagement: >80% daily

### Business Outcomes
- Faster decision making
- Better cash management
- Improved pipeline visibility
- Increased strategic planning

## Conclusion

This three-pillar model aligns Upstream with how professional services businesses actually think about their operations. By restructuring around Operations, Growth, and Intelligence, we create a system that's intuitive, efficient, and scalable as the product continues to evolve.

The key insight is that users don't think in terms of features or data sources—they think in terms of business activities. This restructure reflects that reality.

---

**Next Document**: [V2 Implementation Roadmap](./ux-review-v2-implementation-roadmap.md)