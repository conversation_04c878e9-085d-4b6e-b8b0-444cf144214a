/* CRM Component Styles */

/* Hide scrollbars utility */
.scrollbar-none {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Deal Card */
.deal-card {
  container-type: inline-size; /* Enable container queries */
  transition: all 0.2s ease-in-out;
  min-height: auto; /* Allow cards to size to content */
}

/* We only need to keep the min-height: auto change */

.deal-card:hover {
  transform: translateY(-2px);
  box-shadow: theme("boxShadow.md");
}

/* Compact deal card */
.deal-card--compact {
  min-height: 3.5rem; /* Make compact cards slightly taller */
  max-height: 4rem;
}

/* Deal Stage Column */
.deal-column {
  min-width: 240px;
  max-width: 280px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Compact view styles */
.deal-column--compact {
  min-width: 150px;
  max-width: 180px;
  width: 160px; /* Fixed width to ensure consistent sizing */
  flex: 1; /* Allow columns to grow and fill available space */
  flex-shrink: 0; /* Prevent columns from shrinking */
  margin-right: -5px; /* Negative margin to significantly reduce gaps between columns */
}

/* Collapsed column styles */
.deal-column.min-w-\[40px\] {
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
  border-right: 1px solid theme("colors.gray.200");
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

/* Ensure collapsed column headers have proper height */
.deal-column.min-w-\[40px\] .deal-column__header {
  padding: 0.375rem 0;
  height: 3.5rem; /* Match the height of other headers */
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark .deal-column.min-w-\[40px\] {
  border-color: theme("colors.gray.700");
}

/* Narrow inactive column styles */
.deal-column.min-w-\[80px\] {
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
  border-right: 1px solid theme("colors.gray.200");
  width: 80px !important;
  min-width: 80px !important;
  max-width: 80px !important;
}

.dark .deal-column.min-w-\[80px\] {
  border-color: theme("colors.gray.700");
}

/* Expanded inactive column styles */
.deal-column.inactive-expanded {
  min-width: 240px !important;
  max-width: 280px !important;
  width: 260px !important;
  flex-shrink: 0;
  flex-grow: 1;
  border-right: 1px solid theme("colors.gray.200");
  transition: all 0.3s ease-in-out;
}

.dark .deal-column.inactive-expanded {
  border-color: theme("colors.gray.700");
}

/* Extra small text size for compact view */
.text-2xs {
  font-size: 0.65rem;
  line-height: 1rem;
}

.deal-column__header {
  padding: 0.375rem;
  border-bottom: 1px solid theme("colors.gray.200");
  background-color: theme("colors.gray.50");
  border-top-left-radius: theme("borderRadius.md");
  border-top-right-radius: theme("borderRadius.md");
  height: 3.5rem; /* Fixed height for all column headers */
}

.deal-column__header--compact {
  padding: 0.25rem 0.5rem;
}

.dark .deal-column__header {
  background-color: theme("colors.gray.800");
  border-color: theme("colors.gray.700");
}

.deal-column__content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  height: auto; /* Allow height to adjust based on content */
}

/* Deal Board */
.deal-board {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  padding: var(--space-sm);
  min-height: auto; /* Allow natural sizing */
  height: auto; /* Let height adjust based on content */
  width: 100%;
  max-width: 100%;
  margin-bottom: 0 !important;
}

.deal-board__columns {
  display: flex;
  overflow-x: auto;
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
  width: 100%;
  height: auto; /* Use appropriate height based on content */
  flex: 1; /* Take up all available space */
}

/* Ensure columns fit properly in compact view */
.deal-board__columns {
  scrollbar-width: thin;
}

.deal-board__columns::-webkit-scrollbar {
  height: 6px;
}

.deal-board__columns::-webkit-scrollbar-thumb {
  background-color: rgba(159, 157, 150, 0.5); /* Flexoki base-400 with 50% opacity */
  border-radius: 3px;
}

.deal-board__columns::-webkit-scrollbar-track {
  background-color: rgba(230, 228, 217, 0.3); /* Flexoki base-100 with 30% opacity */
}

/* Fix for vertical scrolling issue */
.deal-board::after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/* Fix for page layout */
body {
  overflow-x: hidden;
  min-height: 100vh;
  position: relative;
}

/* Ensure proper scrolling behavior */
.app-container,
.main-layout,
#root {
  min-height: 100vh;
  overflow-x: hidden;
}

/* Prevent extra space at the bottom */
.space-y-6 > :last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Deal Detail */
.deal-detail {
  padding: var(--space-md);
}

.deal-detail__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.deal-detail__section {
  margin-bottom: var(--space-md);
}

.deal-detail__section-title {
  font-weight: 600;
  margin-bottom: var(--space-xs);
  color: theme("colors.gray.700");
}

.dark .deal-detail__section-title {
  color: theme("colors.gray.300");
}

/* Deal Priority Badges */
.priority-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: theme("borderRadius.full");
  font-size: theme("fontSize.xs");
  font-weight: 500;
}

.priority-badge--high {
  background-color: theme("colors.red.100");
  color: theme("colors.red.800");
}

.dark .priority-badge--high {
  background-color: theme("colors.red.900");
  color: theme("colors.red.100");
}

.priority-badge--medium {
  background-color: theme("colors.amber.100");
  color: theme("colors.amber.800");
}

.dark .priority-badge--medium {
  background-color: theme("colors.amber.900");
  color: theme("colors.amber.100");
}

.priority-badge--low {
  background-color: theme("colors.green.100");
  color: theme("colors.green.800");
}

.dark .priority-badge--low {
  background-color: theme("colors.green.900");
  color: theme("colors.green.100");
}

/* Deal Stage Badges */
.stage-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: theme("borderRadius.full");
  font-size: theme("fontSize.xs");
  font-weight: 500;
}

.stage-badge--lead {
  background-color: theme("colors.gray.100");
  color: theme("colors.gray.800");
}

.dark .stage-badge--lead {
  background-color: theme("colors.gray.800");
  color: theme("colors.gray.100");
}

.stage-badge--qualified {
  background-color: theme("colors.blue.100");
  color: theme("colors.blue.800");
}

.dark .stage-badge--qualified {
  background-color: theme("colors.blue.900");
  color: theme("colors.blue.100");
}

.stage-badge--proposal {
  background-color: theme("colors.indigo.100");
  color: theme("colors.indigo.800");
}

.dark .stage-badge--proposal {
  background-color: theme("colors.indigo.900");
  color: theme("colors.indigo.100");
}

.stage-badge--negotiation {
  background-color: theme("colors.purple.100");
  color: theme("colors.purple.800");
}

.dark .stage-badge--negotiation {
  background-color: theme("colors.purple.900");
  color: theme("colors.purple.100");
}

.stage-badge--closed-won {
  background-color: theme("colors.green.100");
  color: theme("colors.green.800");
}

.dark .stage-badge--closed-won {
  background-color: theme("colors.green.900");
  color: theme("colors.green.100");
}

.stage-badge--closed-lost {
  background-color: theme("colors.red.100");
  color: theme("colors.red.800");
}

.dark .stage-badge--closed-lost {
  background-color: theme("colors.red.900");
  color: theme("colors.red.100");
}

/* Contact and Company Cards */
.contact-card,
.company-card {
  container-type: inline-size; /* Enable container queries */
  transition: all 0.2s ease-in-out;
}

.contact-card:hover,
.company-card:hover {
  transform: translateY(-2px);
  box-shadow: theme("boxShadow.md");
}

/* Notes */
.note-item {
  padding: var(--space-sm);
  border-radius: theme("borderRadius.md");
  background-color: theme("colors.gray.50");
  margin-bottom: var(--space-sm);
}

.dark .note-item {
  background-color: theme("colors.gray.800");
}

.note-item__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
  font-size: theme("fontSize.sm");
  color: theme("colors.gray.500");
}

.dark .note-item__header {
  color: theme("colors.gray.400");
}

.note-item__content {
  font-size: theme("fontSize.sm");
}

/* Forms */
.crm-form {
  max-width: 600px;
  margin: 0 auto;
}

.crm-form__section {
  margin-bottom: var(--space-md);
}

.crm-form__section-title {
  font-weight: 600;
  margin-bottom: var(--space-sm);
  color: theme("colors.gray.700");
}

.dark .crm-form__section-title {
  color: theme("colors.gray.300");
}

.crm-form__field {
  margin-bottom: var(--space-sm);
}

.crm-form__label {
  display: block;
  margin-bottom: var(--space-xs);
  font-size: theme("fontSize.sm");
  font-weight: 500;
  color: theme("colors.gray.700");
}

.dark .crm-form__label {
  color: theme("colors.gray.300");
}

.crm-form__input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid theme("colors.gray.300");
  border-radius: theme("borderRadius.md");
  background-color: theme("colors.white");
  color: theme("colors.gray.900");
}

.dark .crm-form__input {
  background-color: theme("colors.gray.800");
  border-color: theme("colors.gray.700");
  color: theme("colors.gray.100");
}

.crm-form__input:focus {
  outline: none;
  border-color: theme("colors.blue.500");
  box-shadow: 0 0 0 2px theme("colors.blue.200");
}

.dark .crm-form__input:focus {
  border-color: theme("colors.blue.400");
  box-shadow: 0 0 0 2px theme("colors.blue.900");
}

.crm-form__actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
  margin-top: var(--space-md);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  /* Deal board specific mobile fixes */
  
  .deal-board {
    flex-direction: column;
    height: auto;
    max-height: none;
    padding: var(--space-sm);
  }

  .deal-board__columns {
    flex-direction: column;
    gap: var(--space-sm);
    height: auto;
  }

  .deal-column {
    max-width: 100% !important;
    min-width: 100% !important;
    width: 100% !important;
    margin-bottom: var(--space-md);
    height: auto;
    min-height: auto;
  }

  .deal-column.min-w-\[40px\],
  .deal-column.min-w-\[80px\] {
    max-width: 100% !important;
    min-width: 100% !important;
    width: 100% !important;
  }

  .deal-column__content {
    max-height: 500px;
    height: auto;
  }
  
  /* Fix for CRM dashboard container */
  .space-y-6 {
    max-width: 100vw;
  }
  
  /* Ensure modals don't cause overflow */
  .fixed {
    max-width: 100vw;
  }
  
}
