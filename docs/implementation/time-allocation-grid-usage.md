# Time Allocation Grid Enhancement Usage Guide

## Overview
The enhanced Time Allocation Grid provides spreadsheet-like editing capabilities with minimal dependencies. This guide explains how to integrate and use the enhanced component.

## Database Setup

First, ensure your database has the `sort_index` column:

```bash
# Run the migration script
node scripts/apply-sort-index-migration.js
```

## Component Integration

### 1. Import the Enhanced Component

Replace the existing TimeAllocationGrid import:

```typescript
// Old
import TimeAllocationGrid from './TimeAllocationGrid';

// New
import TimeAllocationGridEnhanced from './TimeAllocationGridEnhanced';
```

### 2. Add the CSS

Import the allocation table styles in your main CSS file or component:

```css
/* In your main styles or component */
@import '../styles/components/allocation-table.css';
```

### 3. Update Component Usage

The enhanced component requires additional props:

```typescript
<TimeAllocationGridEnhanced
  allocationsWithTotals={allocationsWithTotals}
  weeks={weeks}
  onAllocationChange={updateStaffAllocation}
  isReadOnly={false}
  estimateId={estimateId}
  displayUnit={displayUnit}
  onDisplayUnitChange={setDisplayUnit}
/>
```

### 4. Add Display Unit State

Add state management for the hours/days toggle:

```typescript
const [displayUnit, setDisplayUnit] = useState<'hours' | 'days'>('days');
```

## Features

### Keyboard Navigation
- **Arrow Keys**: Navigate between cells
- **Tab/Shift+Tab**: Move forward/backward through cells
- **Enter**: Start editing current cell or move down after edit
- **Escape**: Cancel edit and restore original value
- **F2**: Start editing current cell

### Drag & Drop Row Reordering
- Hover over the drag handle (⋮⋮) on the left of each row
- Click and drag to reorder allocations
- Changes are automatically saved via API

### Hours/Days Toggle
- Click the toggle buttons in the header to switch display units
- Values are always stored as days internally
- Conversion: 1 day = 8 hours

### Inline Editing
- Click any cell to start editing
- Type a value (0-5 days or 0-40 hours)
- Press Enter or Tab to confirm
- Press Escape to cancel

### Visual Feedback
- Focused cells show a green outline
- Hovered cells have a light background
- Dragged rows become semi-transparent
- Drop targets show a green line

## API Integration

The component expects an API endpoint for reordering:

```typescript
PUT /api/estimates/drafts/:estimateId/allocation-order

Body: {
  orderedAllocationIds: string[] // Array of allocation IDs in new order
}
```

## Accessibility

The enhanced grid follows ARIA grid patterns:
- `role="grid"` on the table
- `role="gridcell"` on editable cells
- Keyboard navigation support
- Screen reader announcements

## Performance Considerations

- No virtualization needed for typical use (10-50 rows)
- Debounced saving reduces API calls
- Local state updates provide instant feedback
- Optimized for up to 100 allocations

## Migration from Original Component

1. The enhanced component is backward compatible
2. No changes needed to data structures
3. Optional props can be added incrementally
4. Original component can be kept as fallback

## Example Implementation

```typescript
import React, { useState } from 'react';
import TimeAllocationGridEnhanced from './TimeAllocationGridEnhanced';
import { useEstimateStaffManagement } from '../../hooks/useEstimateStaffManagement';

export const EstimateAllocationSection = ({ estimateId, weeks }) => {
  const [displayUnit, setDisplayUnit] = useState<'hours' | 'days'>('days');
  const {
    allocationsWithTotals,
    updateStaffAllocation,
  } = useEstimateStaffManagement();

  return (
    <TimeAllocationGridEnhanced
      allocationsWithTotals={allocationsWithTotals}
      weeks={weeks}
      onAllocationChange={updateStaffAllocation}
      isReadOnly={false}
      estimateId={estimateId}
      displayUnit={displayUnit}
      onDisplayUnitChange={setDisplayUnit}
    />
  );
};
```

## Troubleshooting

### Sort order not working
- Ensure the migration script has been run
- Check that allocations have `sortIndex` values
- Verify API endpoint is accessible

### Keyboard navigation issues
- Ensure the table has focus (click on it first)
- Check for conflicting keyboard shortcuts
- Verify no parent elements are preventing events

### Drag and drop not working
- Check browser compatibility (modern browsers required)
- Ensure `isReadOnly` is false
- Verify user has edit permissions