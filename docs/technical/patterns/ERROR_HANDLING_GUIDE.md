# Repository Error Handling Guide

This guide outlines the standardized error handling patterns for all repository classes.

## Key Principles

1. **Throw errors, don't catch and return defaults** - Let the service layer decide how to handle errors
2. **Use AppError with context** - Provide meaningful error messages and context for debugging
3. **Document throws in JSDoc** - Always add `@throws` to methods that can throw errors
4. **Be consistent** - All repositories should follow the same pattern

## Pattern Examples

### Basic CRUD Operations

```typescript
import { AppError } from '../../utils/error';

export class ExampleRepository extends BaseRepository {
  /**
   * Get all records
   * @returns Array of records
   * @throws AppError if database operation fails
   */
  getAllRecords(): Record[] {
    try {
      const records = this.db.prepare('SELECT * FROM table').all();
      return records;
    } catch (error) {
      throw new AppError(
        'Failed to fetch records from database',
        { 
          operation: 'getAllRecords',
          table: this.tableName,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }

  /**
   * Get record by ID
   * @param id Record ID
   * @returns Record or null if not found
   * @throws AppError if database operation fails
   */
  getRecordById(id: string): Record | null {
    try {
      const record = this.db.prepare('SELECT * FROM table WHERE id = ?').get(id);
      return record || null;
    } catch (error) {
      throw new AppError(
        'Failed to fetch record by ID',
        { 
          operation: 'getRecordById',
          recordId: id,
          table: this.tableName,
          error: error instanceof Error ? error.message : String(error)
        }
      );
    }
  }
}
```

## Service Layer Error Handling

Services should use the `withErrorHandling` utility:

```typescript
import { withErrorHandling } from '../../utils/error';

export class ExampleService {
  async getAllRecords() {
    return withErrorHandling(
      async () => {
        const repository = new ExampleRepository();
        return repository.getAllRecords();
      },
      {
        operationName: 'getAllRecords',
        fallbackValue: [], // Only if appropriate
        maxRetries: 2
      }
    );
  }
}
```

## Migration from Old Pattern

### Before (Anti-pattern):
```typescript
getAllContacts(): Contact[] {
  try {
    // ... database operations
    return contacts;
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return []; // Silent failure - BAD!
  }
}
```

### After (Correct pattern):
```typescript
getAllContacts(): Contact[] {
  try {
    // ... database operations
    return contacts;
  } catch (error) {
    throw new AppError(
      'Failed to fetch contacts from database',
      { 
        operation: 'getAllContacts',
        error: error instanceof Error ? error.message : String(error)
      }
    );
  }
}
```

## Benefits

1. **Better debugging** - Context in errors helps identify issues quickly
2. **Consistent behavior** - All errors follow the same pattern
3. **Service layer control** - Services decide whether to retry, use fallbacks, etc.
4. **Type safety** - AppError provides typed error information
5. **Monitoring ready** - Structured errors are easier to track in monitoring tools

## Checklist for Repository Methods

- [ ] Use try-catch blocks for database operations
- [ ] Throw AppError with meaningful message and context
- [ ] Include operation name in error context
- [ ] Include relevant IDs or parameters in error context
- [ ] Add `@throws` JSDoc comment
- [ ] Never return empty arrays/nulls on error (unless documented behavior)
- [ ] Let the service layer handle retries and fallbacks