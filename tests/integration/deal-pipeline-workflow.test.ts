import { jest } from '@jest/globals';
import request from 'supertest';
import { initializeDatabase, closeDatabase } from '../../src/api/services/db-service';
import DealRepository from '../../src/api/repositories/deal-repository';
import CompanyRepository from '../../src/api/repositories/company-repository';
import ContactRepository from '../../src/api/repositories/contact-repository';
import DealEstimateRepository from '../../src/api/repositories/relationships/deal-estimate-repository';
import ContactRoleRepository from '../../src/api/repositories/relationships/contact-role-repository';
import NoteRepository from '../../src/api/repositories/note-repository';
import ActivityService from '../../src/api/services/activity-service';
import DealProjectionService from '../../src/services/cashflow/deal-projection-service';

// Mock dependencies
jest.mock('../../src/utils/backend-logger');
jest.mock('../../src/utils/email');

describe('Deal Pipeline Workflow Integration', () => {
  let app: any;
  let dealRepo: DealRepository;
  let companyRepo: CompanyRepository;
  let contactRepo: ContactRepository;
  let dealEstimateRepo: DealEstimateRepository;
  let contactRoleRepo: ContactRoleRepository;
  let noteRepo: NoteRepository;
  let activityService: ActivityService;
  let dealProjectionService: DealProjectionService;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    await initializeDatabase();
    
    const { app: testApp } = await import('../../src/api/server');
    app = testApp;
    
    // Initialize repositories and services
    dealRepo = new DealRepository();
    companyRepo = new CompanyRepository();
    contactRepo = new ContactRepository();
    dealEstimateRepo = new DealEstimateRepository();
    contactRoleRepo = new ContactRoleRepository();
    noteRepo = new NoteRepository();
    activityService = new ActivityService();
    dealProjectionService = new DealProjectionService();
  });

  afterAll(async () => {
    await closeDatabase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Deal Lifecycle', () => {
    let testCompany: any;
    let testContacts: any[];
    let testEstimate: any;

    beforeEach(async () => {
      // Setup test data
      testCompany = await companyRepo.createCompany({
        name: 'Tech Startup Inc',
        website: 'https://techstartup.com',
        industry: 'Technology',
        annual_revenue: 5000000,
        employee_count: 50,
      });

      testContacts = [
        await contactRepo.createContact({
          first_name: 'Sarah',
          last_name: 'CEO',
          email: '<EMAIL>',
          position: 'Chief Executive Officer',
        }),
        await contactRepo.createContact({
          first_name: 'Mike',
          last_name: 'CTO',
          email: '<EMAIL>',
          position: 'Chief Technology Officer',
        }),
      ];

      // Link contacts to company
      for (const contact of testContacts) {
        await request(app)
          .post(`/api/crm/contacts/${contact.id}/companies`)
          .send({
            company_id: testCompany.id,
            role: contact.position,
            is_primary: contact.id === testContacts[0].id,
          })
          .expect(201);
      }

      // Create estimate
      const estimateResponse = await request(app)
        .post('/api/estimates')
        .send({
          estimate_name: 'Platform Development',
          client_name: testCompany.name,
          total_hours: 500,
          total_cost: 75000,
          status: 'published',
        })
        .expect(201);
      
      testEstimate = estimateResponse.body;
    });

    it('should progress deal through complete pipeline', async () => {
      // Step 1: Create deal in qualified stage
      const dealResponse = await request(app)
        .post('/api/crm/deals')
        .send({
          name: 'Platform Development Deal',
          amount: 75000,
          stage: 'qualified',
          probability: 0.2,
          close_date: '2024-06-30',
          company_id: testCompany.id,
          description: 'Custom platform development for tech startup',
          next_steps: 'Schedule discovery call',
        })
        .expect(201);

      const dealId = dealResponse.body.id;
      expect(dealId).toBeDefined();

      // Step 2: Add contacts to deal with roles
      await request(app)
        .post(`/api/crm/deals/${dealId}/contacts`)
        .send({
          contact_id: testContacts[0].id,
          role: 'Decision Maker',
          permissions: ['view', 'edit', 'approve'],
        })
        .expect(201);

      await request(app)
        .post(`/api/crm/deals/${dealId}/contacts`)
        .send({
          contact_id: testContacts[1].id,
          role: 'Technical Evaluator',
          permissions: ['view', 'comment'],
        })
        .expect(201);

      // Step 3: Add discovery call notes
      await request(app)
        .post(`/api/crm/deals/${dealId}/notes`)
        .send({
          content: 'Discovery call completed. Client needs: 1) User management system, 2) Analytics dashboard, 3) API integration',
          is_private: false,
        })
        .expect(201);

      // Step 4: Move to proposal stage
      let updateResponse = await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          stage: 'proposal',
          probability: 0.5,
          next_steps: 'Prepare and send proposal',
        })
        .expect(200);

      expect(updateResponse.body.stage).toBe('proposal');

      // Step 5: Link estimate to deal
      await request(app)
        .post(`/api/crm/deals/${dealId}/estimates`)
        .send({
          estimate_id: testEstimate.id,
          is_primary: true,
        })
        .expect(201);

      // Step 6: Add proposal notes
      await request(app)
        .post(`/api/crm/deals/${dealId}/notes`)
        .send({
          content: 'Proposal sent to client. Included 3-phase approach with milestones.',
          is_private: false,
        })
        .expect(201);

      // Step 7: Move to negotiation stage
      updateResponse = await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          stage: 'negotiation',
          probability: 0.75,
          amount: 72000, // Slight discount
          next_steps: 'Finalize contract terms',
        })
        .expect(200);

      // Step 8: Add negotiation notes
      await request(app)
        .post(`/api/crm/deals/${dealId}/notes`)
        .send({
          content: 'Client requested 4% discount for upfront payment. Agreed.',
          is_private: false,
        })
        .expect(201);

      // Step 9: Win the deal
      const winResponse = await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          stage: 'closed_won',
          probability: 1,
          actual_close_date: '2024-02-15',
          win_reason: 'Strong technical fit and competitive pricing',
        })
        .expect(200);

      expect(winResponse.body.stage).toBe('closed_won');
      expect(winResponse.body.probability).toBe(1);

      // Verify complete deal state
      const finalDeal = await dealRepo.getDealById(dealId);
      expect(finalDeal).toMatchObject({
        stage: 'closed_won',
        amount: 72000,
        actual_close_date: '2024-02-15',
      });

      // Verify activity timeline
      const activities = await activityService.getActivities({
        target_type: 'deal',
        target_id: dealId.toString(),
      });

      const stageChanges = activities.activities.filter(a => 
        a.action === 'deal_updated' && a.metadata.stage_change
      );
      expect(stageChanges).toHaveLength(3); // qualified->proposal->negotiation->closed_won

      // Verify financial projections were updated
      const projections = await dealProjectionService.getProjectionsForDeal(dealId);
      expect(projections).toBeNull(); // Closed deals don't have projections
    });

    it('should handle deal loss with analysis', async () => {
      // Create deal
      const dealResponse = await request(app)
        .post('/api/crm/deals')
        .send({
          name: 'Lost Opportunity',
          amount: 50000,
          stage: 'negotiation',
          probability: 0.6,
          close_date: '2024-05-31',
          company_id: testCompany.id,
        })
        .expect(201);

      const dealId = dealResponse.body.id;

      // Add competitive intelligence note
      await request(app)
        .post(`/api/crm/deals/${dealId}/notes`)
        .send({
          content: 'Competitor offering 30% lower price with similar features',
          is_private: true,
        })
        .expect(201);

      // Lose the deal
      const lossResponse = await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          stage: 'closed_lost',
          probability: 0,
          loss_reason: 'Lost to competitor',
          loss_analysis: 'Price was the primary factor. Need to revisit our pricing strategy for this market segment.',
          competitor_won: 'CompetitorX',
        })
        .expect(200);

      expect(lossResponse.body.stage).toBe('closed_lost');
      expect(lossResponse.body.loss_reason).toBe('Lost to competitor');

      // Verify loss analysis was recorded
      const activities = await activityService.getActivities({
        target_type: 'deal',
        target_id: dealId.toString(),
        limit: 1,
      });

      expect(activities.activities[0]).toMatchObject({
        action: 'deal_updated',
        metadata: {
          stage_change: { from: 'negotiation', to: 'closed_lost' },
          loss_reason: 'Lost to competitor',
        },
      });
    });

    it('should track deal velocity metrics', async () => {
      // Create multiple deals with different timelines
      const deals = [];
      const stages = ['qualified', 'proposal', 'negotiation', 'closed_won'];
      
      for (let i = 0; i < 3; i++) {
        const dealResponse = await request(app)
          .post('/api/crm/deals')
          .send({
            name: `Velocity Test Deal ${i + 1}`,
            amount: 50000,
            stage: 'qualified',
            probability: 0.2,
            close_date: '2024-12-31',
            company_id: testCompany.id,
          })
          .expect(201);

        const dealId = dealResponse.body.id;
        deals.push(dealId);

        // Progress through stages with different velocities
        for (let j = 1; j < stages.length; j++) {
          // Wait different amounts of time between stages
          await new Promise(resolve => setTimeout(resolve, (i + 1) * 100));
          
          await request(app)
            .put(`/api/crm/deals/${dealId}`)
            .send({
              stage: stages[j],
              probability: 0.2 + (j * 0.3),
            })
            .expect(200);
        }
      }

      // Get velocity report
      const velocityResponse = await request(app)
        .get('/api/crm/reports/deal-velocity')
        .query({
          company_id: testCompany.id,
          date_from: '2024-01-01',
          date_to: '2024-12-31',
        })
        .expect(200);

      expect(velocityResponse.body).toMatchObject({
        average_days_to_close: expect.any(Number),
        stage_durations: {
          qualified_to_proposal: expect.any(Number),
          proposal_to_negotiation: expect.any(Number),
          negotiation_to_closed: expect.any(Number),
        },
        conversion_rates: {
          qualified_to_proposal: 1, // All deals progressed
          proposal_to_negotiation: 1,
          negotiation_to_closed: 1,
        },
      });
    });

    it('should handle deal pipeline forecasting', async () => {
      // Create deals in various stages
      const pipelineDeals = [
        {
          name: 'Early Stage Deal',
          amount: 100000,
          stage: 'qualified',
          probability: 0.2,
          close_date: '2024-06-30',
        },
        {
          name: 'Mid Stage Deal',
          amount: 75000,
          stage: 'proposal',
          probability: 0.5,
          close_date: '2024-05-31',
        },
        {
          name: 'Late Stage Deal',
          amount: 150000,
          stage: 'negotiation',
          probability: 0.8,
          close_date: '2024-04-30',
        },
      ];

      for (const dealData of pipelineDeals) {
        await request(app)
          .post('/api/crm/deals')
          .send({
            ...dealData,
            company_id: testCompany.id,
          })
          .expect(201);
      }

      // Get pipeline forecast
      const forecastResponse = await request(app)
        .get('/api/crm/reports/pipeline-forecast')
        .query({
          months: 3,
        })
        .expect(200);

      expect(forecastResponse.body).toMatchObject({
        total_pipeline_value: 325000,
        weighted_pipeline_value: 180000, // Sum of (amount * probability)
        forecast_by_month: {
          '2024-04': {
            deals: 1,
            total_value: 150000,
            weighted_value: 120000,
          },
          '2024-05': {
            deals: 1,
            total_value: 75000,
            weighted_value: 37500,
          },
          '2024-06': {
            deals: 1,
            total_value: 100000,
            weighted_value: 20000,
          },
        },
        confidence_scenarios: {
          conservative: 120000, // Only high probability deals
          expected: 180000,
          optimistic: 245000, // Increased probabilities
        },
      });
    });

    it('should support deal templates and automation', async () => {
      // Create deal template
      const templateResponse = await request(app)
        .post('/api/crm/deal-templates')
        .send({
          name: 'Standard SaaS Deal',
          default_stage: 'qualified',
          default_probability: 0.25,
          default_days_to_close: 45,
          required_fields: ['company_id', 'amount', 'primary_contact'],
          stage_requirements: {
            proposal: ['estimate_linked', 'discovery_notes'],
            negotiation: ['proposal_sent', 'contract_draft'],
            closed_won: ['contract_signed', 'payment_terms'],
          },
          automation_rules: [
            {
              trigger: 'stage_change_to_proposal',
              action: 'send_proposal_template',
            },
            {
              trigger: 'days_in_stage_exceeds_7',
              action: 'notify_owner',
            },
          ],
        })
        .expect(201);

      const templateId = templateResponse.body.id;

      // Create deal from template
      const dealFromTemplateResponse = await request(app)
        .post('/api/crm/deals/from-template')
        .send({
          template_id: templateId,
          name: 'New SaaS Customer',
          amount: 24000,
          company_id: testCompany.id,
          primary_contact_id: testContacts[0].id,
        })
        .expect(201);

      const dealId = dealFromTemplateResponse.body.id;

      // Verify template was applied
      expect(dealFromTemplateResponse.body).toMatchObject({
        stage: 'qualified',
        probability: 0.25,
        close_date: expect.any(String), // 45 days from today
      });

      // Try to move to proposal without requirements
      const invalidMoveResponse = await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          stage: 'proposal',
        })
        .expect(400);

      expect(invalidMoveResponse.body.error).toContain('Missing required: estimate_linked');

      // Fulfill requirements
      await request(app)
        .post(`/api/crm/deals/${dealId}/estimates`)
        .send({
          estimate_id: testEstimate.id,
          is_primary: true,
        })
        .expect(201);

      await request(app)
        .post(`/api/crm/deals/${dealId}/notes`)
        .send({
          content: 'Discovery completed',
          note_type: 'discovery_notes',
        })
        .expect(201);

      // Now stage change should succeed
      await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          stage: 'proposal',
        })
        .expect(200);

      // Verify automation was triggered
      const activities = await activityService.getActivities({
        target_type: 'deal',
        target_id: dealId.toString(),
      });

      expect(activities.activities.some(a => 
        a.action === 'automation_triggered' && 
        a.metadata.automation === 'send_proposal_template'
      )).toBe(true);
    });
  });

  describe('Deal Collaboration Features', () => {
    it('should support team collaboration on deals', async () => {
      // Create deal
      const company = await companyRepo.createCompany({
        name: 'Collaboration Test Co',
      });

      const dealResponse = await request(app)
        .post('/api/crm/deals')
        .send({
          name: 'Team Collaboration Deal',
          amount: 100000,
          stage: 'proposal',
          company_id: company.id,
          team_members: [
            { user_id: 'user-123', role: 'Account Executive' },
            { user_id: 'user-456', role: 'Sales Engineer' },
            { user_id: 'user-789', role: 'Executive Sponsor' },
          ],
        })
        .expect(201);

      const dealId = dealResponse.body.id;

      // Add team mentions in notes
      await request(app)
        .post(`/api/crm/deals/${dealId}/notes`)
        .send({
          content: '@user-456 Can you review the technical requirements? @user-789 FYI on pricing',
          mentions: ['user-456', 'user-789'],
        })
        .expect(201);

      // Add tasks
      await request(app)
        .post(`/api/crm/deals/${dealId}/tasks`)
        .send({
          title: 'Schedule technical demo',
          assigned_to: 'user-456',
          due_date: '2024-03-01',
          priority: 'high',
        })
        .expect(201);

      // Subscribe to deal updates
      await request(app)
        .post(`/api/crm/deals/${dealId}/subscribe`)
        .send({
          user_id: 'user-999',
          notification_preferences: ['stage_change', 'note_added', 'amount_change'],
        })
        .expect(200);

      // Update deal amount
      await request(app)
        .put(`/api/crm/deals/${dealId}`)
        .send({
          amount: 120000,
        })
        .expect(200);

      // Verify notifications were created
      const notificationsResponse = await request(app)
        .get('/api/notifications')
        .query({
          user_id: 'user-999',
          unread: true,
        })
        .expect(200);

      expect(notificationsResponse.body.notifications.some(n => 
        n.type === 'deal_amount_changed' && 
        n.deal_id === dealId
      )).toBe(true);
    });
  });

  describe('Deal Analytics and Insights', () => {
    it('should provide win/loss analysis', async () => {
      // Create test data with various outcomes
      const company = await companyRepo.createCompany({
        name: 'Analytics Test Co',
      });

      const dealOutcomes = [
        { stage: 'closed_won', win_reason: 'Superior technology' },
        { stage: 'closed_won', win_reason: 'Competitive pricing' },
        { stage: 'closed_lost', loss_reason: 'Budget constraints' },
        { stage: 'closed_lost', loss_reason: 'Chose competitor' },
        { stage: 'closed_lost', loss_reason: 'Budget constraints' },
      ];

      for (const outcome of dealOutcomes) {
        await request(app)
          .post('/api/crm/deals')
          .send({
            name: `${outcome.stage} Deal`,
            amount: 50000,
            stage: outcome.stage,
            company_id: company.id,
            ...(outcome.win_reason && { win_reason: outcome.win_reason }),
            ...(outcome.loss_reason && { loss_reason: outcome.loss_reason }),
          })
          .expect(201);
      }

      // Get win/loss analysis
      const analysisResponse = await request(app)
        .get('/api/crm/reports/win-loss-analysis')
        .query({
          date_from: '2024-01-01',
          date_to: '2024-12-31',
        })
        .expect(200);

      expect(analysisResponse.body).toMatchObject({
        summary: {
          total_deals: 5,
          won_deals: 2,
          lost_deals: 3,
          win_rate: 0.4,
          total_won_value: 100000,
          total_lost_value: 150000,
        },
        win_reasons: {
          'Superior technology': { count: 1, percentage: 0.5 },
          'Competitive pricing': { count: 1, percentage: 0.5 },
        },
        loss_reasons: {
          'Budget constraints': { count: 2, percentage: 0.67 },
          'Chose competitor': { count: 1, percentage: 0.33 },
        },
        trends: {
          win_rate_by_month: expect.any(Object),
          average_deal_size_trend: expect.any(Object),
        },
      });
    });
  });
});