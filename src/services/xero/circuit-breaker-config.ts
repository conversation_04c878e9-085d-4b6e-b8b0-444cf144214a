/**
 * Circuit Breaker Configuration for Xero API
 */

import { CircuitBreakerOptions } from '../../utils/circuit-breaker';

export const XERO_CIRCUIT_BREAKER_CONFIG: Partial<CircuitBreakerOptions> = {
  failureThreshold: 5,        // Open circuit after 5 failures
  successThreshold: 3,        // Close circuit after 3 successes in half-open state
  timeout: 30000,            // 30 second timeout per request
  resetTimeout: 60000,       // Try again after 1 minute when circuit is open
  monitoringPeriod: 300000,  // Monitor failures over 5 minutes
  name: 'XeroAPI'
};

export const HARVEST_CIRCUIT_BREAKER_CONFIG: Partial<CircuitBreakerOptions> = {
  failureThreshold: 5,
  successThreshold: 3,
  timeout: 30000,
  resetTimeout: 60000,
  monitoringPeriod: 300000,
  name: 'HarvestAP<PERSON>'
};

export const HUBSPOT_CIRCUIT_BREAKER_CONFIG: Partial<CircuitBreakerOptions> = {
  failureThreshold: 5,
  successThreshold: 3,
  timeout: 30000,
  resetTimeout: 60000,
  monitoringPeriod: 300000,
  name: 'HubSpotAPI'
};