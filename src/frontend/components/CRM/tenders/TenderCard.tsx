import React from 'react'; import { useDrag } from 'react-dnd'; import { DocumentTextIcon, CalendarIcon, BuildingOfficeIcon, ExclamationTriangleIcon, LinkIcon } from '@heroicons/react/24/outline'; import { formatRelativeDate } from '../../../utils/format'; import type { BaseTender } from '../../../../types/shared-types'; interface TenderCardProps { tender: BaseTender; onClick: () => void; } export const TenderCard: React.FC<TenderCardProps> = ({ tender, onClick }) => { const [{ isDragging }, drag] = useDrag({ type: 'tender', item: { id: tender.id, currentStatus: tender.qualificationStatus }, collect: (monitor) => ({ isDragging: !!monitor.isDragging() }) }); // Calculate days until closing const daysUntilClosing = Math.ceil( (new Date(tender.closingDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24) ); const isOverdue = daysUntilClosing < 0; const isUrgent = daysUntilClosing <= 3 && daysUntilClosing >= 0; // Determine urgency styling const urgencyClass = isOverdue ? 'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20' : isUrgent ? 'border-orange-300 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/20' : ''; return ( <div ref={drag} onClick={onClick} className={`bg-white dark:bg-gray-900 rounded-lg p-3 shadow-sm border cursor-pointer hover:shadow-lg transition-all hover:scale-[1.02] ${ urgencyClass || 'border-gray-200 dark:border-gray-700' } ${isDragging ? 'opacity-50' : ''}`} > {/* Header */} <div className="flex items-start justify-between mb-2"> <div className="flex items-center gap-2 flex-1 min-w-0"> <DocumentTextIcon className="w-4 h-4 text-gray-400 flex-shrink-0" /> <div className="min-w-0"> <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate"> {tender.requestNo} </h4> <p className="text-xs text-gray-500 dark:text-gray-400"> {tender.type} </p> </div> </div> {/* Urgency Indicator */} {(isOverdue || isUrgent) && ( <ExclamationTriangleIcon className={`w-4 h-4 flex-shrink-0 ${ isOverdue ? 'text-red-500' : 'text-orange-500' }`} title={isOverdue ? 'Overdue' : `Closing in ${daysUntilClosing} days`} /> )} </div> {/* Summary */} <p className="text-sm text-gray-700 dark:text-gray-300 mb-2 line-clamp-2"> {tender.summary} </p> {/* Metadata */} <div className="space-y-1.5"> {/* Issuer */} <div className="flex items-center gap-2 text-xs"> <BuildingOfficeIcon className="w-3.5 h-3.5 text-gray-400" /> <span className="text-gray-600 dark:text-gray-400 truncate"> {tender.issuedBy} </span> {tender.company && ( <span className="text-green-600 dark:text-green-400" title="Linked to company"> ✓ </span> )} </div> {/* Closing Date */} <div className="flex items-center gap-2 text-xs"> <CalendarIcon className="w-3.5 h-3.5 text-gray-400" /> <span className={`${ isOverdue ? 'text-red-600 dark:text-red-400 font-medium' : isUrgent ? 'text-orange-600 dark:text-orange-400 font-medium' : 'text-gray-600 dark:text-gray-400' }`}> {isOverdue ? 'Closed' : 'Closes'} {formatRelativeDate(tender.closingDate)} </span> </div> {/* Additional Info */} {tender.tenderUrl && ( <div className="flex items-center gap-2 text-xs"> <LinkIcon className="w-3.5 h-3.5 text-gray-400" /> <span className="text-blue-600 dark:text-blue-400"> Has tender link </span> </div> )} </div> {/* Status Indicators */} {tender.qualificationReason && ( <div className="mt-3"> <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400" title={tender.qualificationReason}> Has reason </span> </div> )} </div> ); }; export default TenderCard;