/**
 * Version History Update Script
 * 
 * This script analyzes git commits since the latest version update and
 * generates a new version entry in src/constants/versionHistory.ts
 * 
 * Usage: node scripts/update-version-history.js
 */

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');
const readline = require('readline');

// File path
const versionHistoryPath = path.join(__dirname, '../src/constants/versionHistory.ts');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Get most recent version date from version history file
function getLatestVersionDate() {
  const fileContent = fs.readFileSync(versionHistoryPath, 'utf8');
  const match = fileContent.match(/date: "([0-9]{4}-[0-9]{2}-[0-9]{2})"/);
  return match ? match[1] : '2025-01-01'; // Fallback date if none found
}

// Get commits since the last version update
function getCommitsSinceDate(date) {
  const gitFormat = '%h|%an|%ad|%s'; // hash|author|date|subject
  const gitLog = execSync(
    `git log --since="${date}" --pretty=format:"${gitFormat}" --date=short`
  ).toString().trim();
  
  if (!gitLog) return [];
  
  return gitLog.split('\n').map(line => {
    const [hash, author, date, ...messageParts] = line.split('|');
    return {
      hash,
      author,
      date,
      message: messageParts.join('|') // Rejoin in case message contained pipe chars
    };
  });
}

// Categorize commits into features, bug fixes, etc.
function categorizeCommits(commits) {
  const categories = {
    features: [],
    bugfixes: [],
    docs: [],
    other: []
  };
  
  commits.forEach(commit => {
    const message = commit.message;
    if (message.startsWith('feat:') || message.includes('feature')) {
      categories.features.push({
        ...commit,
        cleanMessage: message.replace(/^feat:/, '').trim()
      });
    } else if (message.startsWith('fix:') || message.includes('bug')) {
      categories.bugfixes.push({
        ...commit,
        cleanMessage: message.replace(/^fix:/, '').trim()
      });
    } else if (message.startsWith('docs:') || message.includes('documentation')) {
      categories.docs.push({
        ...commit,
        cleanMessage: message.replace(/^docs:/, '').trim()
      });
    } else {
      categories.other.push({
        ...commit,
        cleanMessage: message
      });
    }
  });
  
  return categories;
}

// Group related commits into features/changes
function groupCommitsByFeature(categorizedCommits) {
  // This is a simple approach - group by first word or common prefix
  // A more sophisticated approach could use NLP or clustering
  
  function groupByPrefix(commits) {
    const groups = {};
    
    commits.forEach(commit => {
      // Extract likely feature name (first few words or up to first verb)
      const featureName = commit.cleanMessage.split(' ').slice(0, 3).join(' ');
      
      if (!groups[featureName]) {
        groups[featureName] = [];
      }
      groups[featureName].push(commit);
    });
    
    return Object.entries(groups).map(([name, commits]) => ({
      name,
      commits,
      primaryCommit: commits[0]
    }));
  }
  
  return {
    features: groupByPrefix(categorizedCommits.features),
    bugfixes: groupByPrefix(categorizedCommits.bugfixes),
    docs: groupByPrefix(categorizedCommits.docs),
    other: groupByPrefix(categorizedCommits.other)
  };
}

// Generate version entry
function generateVersionEntry(groupedCommits, userProvidedVersion = null) {
  // Determine what kind of release this is
  let releaseType = 'patch';
  if (groupedCommits.features.length > 3) {
    releaseType = 'major';
  } else if (groupedCommits.features.length > 0) {
    releaseType = 'minor';
  }
  
  // Get the current latest version
  const fileContent = fs.readFileSync(versionHistoryPath, 'utf8');
  const versionMatch = fileContent.match(/version: "([0-9]+\.[0-9]+\.[0-9]+)"/);
  if (!versionMatch) {
    console.error('Could not find current version!');
    process.exit(1);
  }
  
  const currentVersion = versionMatch[1];
  const [major, minor, patch] = currentVersion.split('.').map(Number);
  
  // Calculate new version (or use user provided)
  let newVersion;
  if (userProvidedVersion) {
    newVersion = userProvidedVersion;
  } else {
    switch (releaseType) {
      case 'major':
        newVersion = `${major + 1}.0.0`;
        break;
      case 'minor':
        newVersion = `${major}.${minor + 1}.0`;
        break;
      case 'patch':
      default:
        newVersion = `${major}.${minor}.${patch + 1}`;
        break;
    }
  }
  
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  
  // Generate title from feature groups or a default
  let title = 'Maintenance Update';
  if (groupedCommits.features.length > 0) {
    // Use the most substantial feature as the title basis
    const mainFeature = groupedCommits.features.sort(
      (a, b) => b.commits.length - a.commits.length
    )[0];
    
    title = mainFeature.name;
    
    // Add secondary feature if there are multiple substantial ones
    if (groupedCommits.features.length > 1) {
      title += ` & ${groupedCommits.features[1].name}`;
    }
  } else if (groupedCommits.bugfixes.length > 0) {
    title = 'Bug Fixes';
  }
  
  // Capitalize and clean title
  title = title.split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
    .replace(/^\w+:/, ''); // Remove prefix like "feat:"
  
  // Create description from all changes
  const allChanges = [
    ...groupedCommits.features.flatMap(f => f.commits.map(c => c.cleanMessage)),
    ...groupedCommits.bugfixes.flatMap(f => f.commits.map(c => c.cleanMessage))
  ];
  
  let description = 'Various improvements and fixes';
  if (allChanges.length > 0) {
    // Find the most detailed commit message to use as description
    const longestMessage = allChanges.sort((a, b) => b.length - a.length)[0];
    description = longestMessage.charAt(0).toUpperCase() + longestMessage.slice(1);
    
    // Truncate if too long
    if (description.length > 100) {
      description = description.substring(0, 97) + '...';
    }
  }
  
  // Prepare changes list
  const changesSet = new Set(); // To avoid duplicates
  
  // Add feature changes
  groupedCommits.features.forEach(feature => {
    feature.commits.forEach(commit => {
      const message = commit.cleanMessage.charAt(0).toUpperCase() + commit.cleanMessage.slice(1);
      changesSet.add(message);
    });
  });
  
  // Add bugfix changes
  groupedCommits.bugfixes.forEach(bugfix => {
    bugfix.commits.forEach(commit => {
      const message = commit.cleanMessage.charAt(0).toUpperCase() + commit.cleanMessage.slice(1);
      changesSet.add(message);
    });
  });
  
  // Add other notable changes
  groupedCommits.docs.forEach(doc => {
    const message = doc.primaryCommit.cleanMessage;
    if (message.length > 15) { // Only add substantial doc changes
      changesSet.add(message.charAt(0).toUpperCase() + message.slice(1));
    }
  });
  
  // Convert to array and limit to top changes
  const changes = [...changesSet].slice(0, 10);
  
  // Generate minor releases from features
  const minorReleases = groupedCommits.features.map((feature, index) => {
    // Get all related commits
    const commitMessages = feature.commits.map(c => 
      c.cleanMessage.charAt(0).toUpperCase() + c.cleanMessage.slice(1)
    );
    
    // Add related bugfixes if they seem connected
    const relatedBugfixes = groupedCommits.bugfixes
      .filter(bugfix => 
        bugfix.name.toLowerCase().includes(feature.name.toLowerCase()) ||
        feature.name.toLowerCase().includes(bugfix.name.toLowerCase())
      )
      .flatMap(bugfix => 
        bugfix.commits.map(c => c.cleanMessage.charAt(0).toUpperCase() + c.cleanMessage.slice(1))
      );
    
    // Create clean name for minor release version
    const minorName = feature.name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .replace(/^\w+:/, '');
    
    // Create unique set of changes
    const changesForMinor = [...new Set([...commitMessages, ...relatedBugfixes])];
    
    return {
      version: `${newVersion.split('.').slice(0, 2).join('.')}.${index + 1}`,
      date: today,
      title: minorName,
      description: changesForMinor[0] || feature.primaryCommit.cleanMessage,
      changes: changesForMinor,
      patches: [] // You could add patch releases here
    };
  });
  
  // Return the new major release entry
  return {
    version: newVersion,
    date: today,
    title,
    description,
    changes,
    minorReleases
  };
}

// Convert the entry object to formatted TypeScript code
function formatVersionEntry(entry) {
  // Helper to escape quotes in strings
  const escapeString = str => str.replace(/"/g, '\\"');
  
  const formatChanges = changes => 
    changes.map(c => `      "${escapeString(c)}"`).join(',\n');
  
  const formatMinorReleases = minorReleases => {
    if (minorReleases.length === 0) return '[]';
    
    return `[
${minorReleases.map(minor => {
      return `      {
        version: "${minor.version}",
        date: "${minor.date}",
        title: "${escapeString(minor.title)}",
        description: "${escapeString(minor.description)}",
        changes: [
${formatChanges(minor.changes)}
        ],
        patches: ${formatPatches(minor.patches)}
      }`;
    }).join(',\n')}
    ]`;
  };
  
  const formatPatches = patches => {
    if (patches.length === 0) return '[]';
    
    return `[
${patches.map(patch => {
      return `          {
            version: "${patch.version}",
            date: "${patch.date}",
            title: "${escapeString(patch.title)}",
            description: "${escapeString(patch.description)}",
            changes: [
${formatChanges(patch.changes)}
            ]
          }`;
    }).join(',\n')}
        ]`;
  };
  
  return `  {
    version: "${entry.version}",
    date: "${entry.date}",
    title: "${escapeString(entry.title)}",
    description: "${escapeString(entry.description)}",
    changes: [
${formatChanges(entry.changes)}
    ],
    minorReleases: ${formatMinorReleases(entry.minorReleases)}
  }`;
}

// Update the version history file
function updateVersionHistoryFile(newEntry) {
  const fileContent = fs.readFileSync(versionHistoryPath, 'utf8');
  
  // Insert the new entry at the top of the version history array
  const updatedContent = fileContent.replace(
    /export const versionHistory: MajorRelease\[\] = \[/,
    `export const versionHistory: MajorRelease[] = [\n${newEntry},`
  );
  
  // Write updated content back to file
  fs.writeFileSync(versionHistoryPath, updatedContent);
}

// Ask for user confirmation
function confirmUpdate(newEntry, callback) {
  console.log('\nProposed Version Update:');
  console.log(`Version: ${newEntry.version}`);
  console.log(`Title: ${newEntry.title}`);
  console.log(`Description: ${newEntry.description}`);
  console.log('\nChanges:');
  newEntry.changes.forEach(change => console.log(`- ${change}`));
  
  if (newEntry.minorReleases.length > 0) {
    console.log('\nMinor Releases:');
    newEntry.minorReleases.forEach(minor => {
      console.log(`- ${minor.version}: ${minor.title}`);
    });
  }
  
  rl.question('\nDo you want to proceed with this update? (y/n): ', answer => {
    callback(answer.toLowerCase() === 'y');
  });
}

// Ask for custom version if needed
function askForCustomVersion(suggestedVersion, callback) {
  rl.question(`Suggested version is ${suggestedVersion}. Use this or enter custom version: `, answer => {
    callback(answer.trim() || suggestedVersion);
  });
}

// Main function
function updateVersionHistory() {
  const latestDate = getLatestVersionDate();
  console.log(`Getting commits since ${latestDate}...`);
  
  const commits = getCommitsSinceDate(latestDate);
  if (commits.length === 0) {
    console.log('No new commits found since the last version update.');
    rl.close();
    return;
  }
  
  console.log(`Found ${commits.length} commits.`);
  
  const categorizedCommits = categorizeCommits(commits);
  const groupedCommits = groupCommitsByFeature(categorizedCommits);
  
  console.log(`Grouped into:
  - ${groupedCommits.features.length} features
  - ${groupedCommits.bugfixes.length} bug fixes
  - ${groupedCommits.docs.length} documentation updates
  - ${groupedCommits.other.length} other changes`);
  
  // Generate a temporary version entry to get the suggested version
  const tempEntry = generateVersionEntry(groupedCommits);
  
  askForCustomVersion(tempEntry.version, customVersion => {
    // Regenerate with the final version
    const newEntry = generateVersionEntry(groupedCommits, customVersion);
    const formattedEntry = formatVersionEntry(newEntry);
    
    confirmUpdate(newEntry, shouldProceed => {
      if (shouldProceed) {
        updateVersionHistoryFile(formattedEntry);
        console.log(`Successfully updated version history to ${newEntry.version}`);
      } else {
        console.log('Update cancelled.');
      }
      rl.close();
    });
  });
}

// Execute if run directly
if (require.main === module) {
  updateVersionHistory();
}

module.exports = { updateVersionHistory };