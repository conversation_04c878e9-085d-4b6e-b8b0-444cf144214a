/**
 * Company Enrichment Frontend Component Tests
 * 
 * Tests for the enrichment functionality in CompanyDetail component including:
 * - Enrichment trigger button behavior
 * - Loading states during enrichment
 * - Success and error handling
 * - Data display after enrichment
 * - User interaction flows
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from 'react-query';
import { BrowserRouter } from 'react-router-dom';
import CompanyDetail from '../../../../src/frontend/components/CRM/Companies/CompanyDetail';
import * as enrichmentApi from '../../../../src/frontend/api/enrichment';
import { Company } from '../../../../src/frontend/types/crm-types';

// Mock the enrichment API
jest.mock('../../../../src/frontend/api/enrichment');
const mockEnrichmentApi = enrichmentApi as jest.Mocked<typeof enrichmentApi>;

// Mock other dependencies
jest.mock('../../../../src/frontend/api/crm', () => ({
  updateCompany: jest.fn(),
  deleteCompany: jest.fn()
}));

jest.mock('../../../../src/frontend/components/CRM/TeamCoverage', () => ({
  TeamCoverageMatrix: () => <div data-testid="team-coverage">Team Coverage</div>
}));

jest.mock('../../../../src/frontend/components/CRM/NetworkVisualization', () => ({
  NetworkVisualization: () => <div data-testid="network-viz">Network Visualization</div>
}));

jest.mock('../../../../src/frontend/components/CRM/ProjectHistory', () => ({
  ProjectHistory: () => <div data-testid="project-history">Project History</div>
}));

jest.mock('../../../../src/frontend/components/CRM/OpportunityIntelligence', () => ({
  OpportunityIntelligence: () => <div data-testid="opportunity-intel">Opportunity Intelligence</div>
}));

describe('Company Enrichment Functionality', () => {
  let queryClient: QueryClient;
  
  const mockCompany: Company = {
    id: 'comp-123',
    name: 'Test Company Pty Ltd',
    industry: 'Technology',
    size: '10-50',
    website: 'https://test.com.au',
    address: '123 Collins St, Melbourne VIC 3000',
    description: 'A test company',
    source: 'manual',
    radarState: 'active',
    priority: 'High',
    currentSpend: 5000,
    potentialSpend: 10000,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'test-user',
    updatedBy: 'test-user',
    lastEnrichedAt: null,
    enrichmentStatus: null,
    activeDealsCount: 0,
    totalDealValue: 0,
    linkingStatus: 'none',
    hubspotId: null,
    harvestId: null,
    parentCompanies: [],
    childCompanies: []
  };

  const mockOnClose = jest.fn();

  const renderCompanyDetail = (company: Company = mockCompany) => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    return render(
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <CompanyDetail company={company} onClose={mockOnClose} />
        </BrowserRouter>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Enrichment Button', () => {
    it('should display enrichment button for companies', () => {
      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      expect(enrichButton).toBeInTheDocument();
      expect(enrichButton).not.toBeDisabled();
    });

    it('should show enrichment button as enabled for non-enriched companies', () => {
      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      expect(enrichButton).toBeEnabled();
    });

    it('should show appropriate text for companies that have been enriched', () => {
      const enrichedCompany = {
        ...mockCompany,
        lastEnrichedAt: '2024-01-01T12:00:00Z',
        enrichmentStatus: {
          lastEnriched: '2024-01-01T12:00:00Z',
          sources: {
            abn_lookup: { success: true, confidence: 0.95 }
          }
        }
      };

      renderCompanyDetail(enrichedCompany);

      // Should still show button but might have different text
      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment|re.*enrich/i });
      expect(enrichButton).toBeInTheDocument();
    });
  });

  describe('Enrichment Process', () => {
    it('should trigger enrichment when button is clicked', async () => {
      const mockEnrichmentResponse = {
        company: mockCompany,
        enrichment: [
          {
            id: 'enrich-1',
            source: 'abn_lookup',
            data: {
              abn: '12 ***********',
              abnStatus: 'Active',
              entityType: 'Australian Private Company',
              gstStatus: 'Registered'
            },
            confidence: 0.95
          }
        ],
        results: [
          {
            source: 'abn_lookup',
            success: true,
            confidence: 0.95,
            data: {
              abn: '12 ***********',
              abnStatus: 'Active'
            }
          }
        ]
      };

      mockEnrichmentApi.enrichCompany.mockResolvedValue(mockEnrichmentResponse);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      await waitFor(() => {
        expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalledWith('comp-123');
      });
    });

    it('should show loading state during enrichment', async () => {
      let resolveEnrichment: (value: any) => void;
      const enrichmentPromise = new Promise(resolve => {
        resolveEnrichment = resolve;
      });

      mockEnrichmentApi.enrichCompany.mockReturnValue(enrichmentPromise);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      // Should show loading state
      await waitFor(() => {
        expect(enrichButton).toBeDisabled();
      });

      // Should show loading indicator (text or spinner)
      expect(screen.getByText(/enriching|loading/i)).toBeInTheDocument();

      // Resolve the promise
      resolveEnrichment!({
        company: mockCompany,
        enrichment: [],
        results: []
      });

      await waitFor(() => {
        expect(enrichButton).not.toBeDisabled();
      });
    });

    it('should handle enrichment success', async () => {
      const successResponse = {
        company: {
          ...mockCompany,
          lastEnrichedAt: new Date().toISOString(),
          enrichmentStatus: {
            lastEnriched: new Date().toISOString(),
            sources: {
              abn_lookup: { success: true, confidence: 0.95 }
            }
          }
        },
        enrichment: [
          {
            id: 'enrich-1',
            source: 'abn_lookup',
            data: {
              abn: '12 ***********',
              abnStatus: 'Active',
              entityType: 'Australian Private Company'
            }
          }
        ],
        results: [
          { source: 'abn_lookup', success: true, confidence: 0.95 }
        ]
      };

      mockEnrichmentApi.enrichCompany.mockResolvedValue(successResponse);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      await waitFor(() => {
        expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalled();
      });

      // Should show success indication or updated data
      // The exact UI depends on how the component handles success
    });

    it('should handle enrichment errors', async () => {
      const errorMessage = 'Failed to enrich company data';
      mockEnrichmentApi.enrichCompany.mockRejectedValue(new Error(errorMessage));

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      await waitFor(() => {
        expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalled();
      });

      // Should show error message or indication
      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith('Enrichment failed:', expect.any(Error));
      });

      // Button should be re-enabled after error
      expect(enrichButton).not.toBeDisabled();
    });

    it('should handle partial enrichment results', async () => {
      const partialSuccessResponse = {
        company: mockCompany,
        enrichment: [],
        results: [
          {
            source: 'abn_lookup',
            success: false,
            error: 'No matching ABN found',
            confidence: 0
          }
        ]
      };

      mockEnrichmentApi.enrichCompany.mockResolvedValue(partialSuccessResponse);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      await waitFor(() => {
        expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalled();
      });

      // Should handle partial failures gracefully
      expect(enrichButton).not.toBeDisabled();
    });
  });

  describe('Enrichment Data Display', () => {
    it('should display enriched data when available', () => {
      const enrichedCompany = {
        ...mockCompany,
        lastEnrichedAt: '2024-01-01T12:00:00Z',
        enrichmentStatus: {
          lastEnriched: '2024-01-01T12:00:00Z',
          sources: {
            abn_lookup: {
              success: true,
              confidence: 0.95,
              data: {
                abn: '12 ***********',
                abnStatus: 'Active',
                entityType: 'Australian Private Company',
                gstStatus: 'Registered'
              }
            }
          }
        }
      };

      renderCompanyDetail(enrichedCompany);

      // Should show that company has been enriched
      // This depends on how the UI displays enrichment status
      expect(screen.getByText(/enriched|last.*enriched/i)).toBeInTheDocument();
    });

    it('should show enrichment confidence scores', () => {
      const enrichedCompany = {
        ...mockCompany,
        enrichmentStatus: {
          sources: {
            abn_lookup: { success: true, confidence: 0.95 }
          }
        }
      };

      renderCompanyDetail(enrichedCompany);

      // Look for confidence indicators (badges, percentages, etc.)
      const confidenceElements = screen.queryAllByText(/95%|0\.95|high.*confidence/i);
      expect(confidenceElements.length).toBeGreaterThan(0);
    });

    it('should display ABN data when available', () => {
      const companyWithABN = {
        ...mockCompany,
        enrichmentStatus: {
          sources: {
            abn_lookup: {
              success: true,
              data: {
                abn: '12 ***********',
                abnStatus: 'Active',
                entityType: 'Australian Private Company',
                gstStatus: 'Registered'
              }
            }
          }
        }
      };

      renderCompanyDetail(companyWithABN);

      // Should display ABN information
      expect(screen.getByText(/12 ***********|abn/i)).toBeInTheDocument();
    });
  });

  describe('Enrichment Status Indicators', () => {
    it('should show status for companies that need enrichment', () => {
      renderCompanyDetail(mockCompany);

      // Should indicate that enrichment is available or recommended
      const statusIndicators = screen.queryAllByText(/not.*enriched|needs.*enrichment|enrich.*available/i);
      // At least the button text should indicate enrichment is available
      expect(statusIndicators.length).toBeGreaterThan(0);
    });

    it('should show different status for enriched companies', () => {
      const enrichedCompany = {
        ...mockCompany,
        lastEnrichedAt: '2024-01-01T12:00:00Z'
      };

      renderCompanyDetail(enrichedCompany);

      // Should show enriched status
      expect(screen.getByText(/enriched|last.*enriched/i)).toBeInTheDocument();
    });

    it('should show enrichment sources', () => {
      const enrichedCompany = {
        ...mockCompany,
        enrichmentStatus: {
          sources: {
            abn_lookup: { success: true, confidence: 0.95 },
            clearbit: { success: false, error: 'API limit reached' }
          }
        }
      };

      renderCompanyDetail(enrichedCompany);

      // Should show which sources were used
      expect(screen.getByText(/abn.*lookup/i)).toBeInTheDocument();
    });
  });

  describe('User Experience', () => {
    it('should prevent multiple simultaneous enrichment requests', async () => {
      let resolveFirst: (value: any) => void;
      const firstRequest = new Promise(resolve => {
        resolveFirst = resolve;
      });

      mockEnrichmentApi.enrichCompany.mockReturnValueOnce(firstRequest);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      
      // Click button multiple times
      fireEvent.click(enrichButton);
      fireEvent.click(enrichButton);
      fireEvent.click(enrichButton);

      // Should only call the API once
      expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalledTimes(1);

      // Button should be disabled during request
      expect(enrichButton).toBeDisabled();

      // Resolve the first request
      resolveFirst!({
        company: mockCompany,
        enrichment: [],
        results: []
      });

      await waitFor(() => {
        expect(enrichButton).not.toBeDisabled();
      });
    });

    it('should provide feedback on enrichment quality', () => {
      const enrichedCompany = {
        ...mockCompany,
        enrichmentStatus: {
          sources: {
            abn_lookup: { success: true, confidence: 0.95 },
            clearbit: { success: true, confidence: 0.3 }
          }
        }
      };

      renderCompanyDetail(enrichedCompany);

      // Should show quality indicators (high/medium/low confidence)
      const qualityElements = screen.queryAllByText(/high|medium|low.*confidence|95%|30%/i);
      expect(qualityElements.length).toBeGreaterThan(0);
    });

    it('should handle network connectivity issues', async () => {
      const networkError = new Error('Network Error');
      mockEnrichmentApi.enrichCompany.mockRejectedValue(networkError);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      await waitFor(() => {
        expect(console.error).toHaveBeenCalledWith('Enrichment failed:', networkError);
      });

      // Button should be re-enabled to allow retry
      expect(enrichButton).not.toBeDisabled();
    });
  });

  describe('Integration with Company Data', () => {
    it('should trigger cache invalidation after successful enrichment', async () => {
      const mockInvalidateQueries = jest.fn();
      queryClient.invalidateQueries = mockInvalidateQueries;

      const successResponse = {
        company: mockCompany,
        enrichment: [],
        results: [{ source: 'abn_lookup', success: true, confidence: 0.95 }]
      };

      mockEnrichmentApi.enrichCompany.mockResolvedValue(successResponse);

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      await waitFor(() => {
        expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalled();
      });

      // Should invalidate relevant queries
      await waitFor(() => {
        expect(mockInvalidateQueries).toHaveBeenCalledWith(['company', 'comp-123']);
        expect(mockInvalidateQueries).toHaveBeenCalledWith('companies');
      });
    });

    it('should work with Australian companies', () => {
      const australianCompany = {
        ...mockCompany,
        website: 'https://example.com.au',
        address: 'Sydney, NSW 2000, Australia'
      };

      renderCompanyDetail(australianCompany);

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      expect(enrichButton).toBeEnabled();
    });

    it('should handle companies with existing ABN data', () => {
      const companyWithABN = {
        ...mockCompany,
        description: 'Company with ABN: 12 ***********'
      };

      renderCompanyDetail(companyWithABN);

      // Should still allow re-enrichment
      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      expect(enrichButton).toBeEnabled();
    });
  });

  describe('Accessibility', () => {
    it('should have accessible enrichment button', () => {
      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      expect(enrichButton).toHaveAttribute('type', 'button');
      
      // Should have appropriate aria labels or descriptions
      expect(enrichButton).toBeVisible();
    });

    it('should provide screen reader feedback during enrichment', async () => {
      mockEnrichmentApi.enrichCompany.mockResolvedValue({
        company: mockCompany,
        enrichment: [],
        results: []
      });

      renderCompanyDetail();

      const enrichButton = screen.getByRole('button', { name: /enrich.*data|data.*enrichment/i });
      fireEvent.click(enrichButton);

      // Should have aria-live regions or other accessibility features
      await waitFor(() => {
        expect(mockEnrichmentApi.enrichCompany).toHaveBeenCalled();
      });
    });
  });
});