/**
 * Validation script for the unified company model
 * 
 * This script:
 * 1. Checks the company table structure to ensure it has all required fields
 * 2. Checks for the presence of radar-specific columns
 * 3. Verifies indexes are properly created
 * 4. Returns validation results
 */

const path = require('path');
const BetterSqlite3 = require('better-sqlite3');
const fs = require('fs');

// Create log directory and file
const logDir = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const logFilePath = path.join(logDir, `company_validation_${new Date().toISOString().replace(/:/g, '-')}.log`);
const logStream = fs.createWriteStream(logFilePath, { flags: 'a' });

// Logging helper function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  logStream.write(logMessage + '\n');
}

// Error logging helper
function logError(message, error) {
  const errorMessage = `ERROR: ${message} - ${error.message}\n${error.stack || ''}`;
  log(errorMessage);
}

// Get database path from environment or use default
const dataDir = process.env.DATA_DIR || path.join(__dirname, '..', 'data');
const dbPath = path.join(dataDir, 'upstream.db'); // Using the correct database name

log(`Using database at: ${dbPath}`);

// Connect to the database
let db;
try {
  db = new BetterSqlite3(dbPath);
  log('Successfully connected to database');
} catch (error) {
  logError('Failed to connect to database', error);
  process.exit(1);
}

// Required columns for unified company model
const requiredColumns = [
  'id', 'name', 'industry', 'size', 'website', 'address', 'description',
  'hubspot_id', 'harvest_id', 'source',
  'radar_state', 'priority', 'current_spend', 'potential_spend', 'last_interaction_date', 'contacts', 'notes',
  'created_at', 'updated_at', 'created_by', 'updated_by', 'deleted_at'
];

// Required indexes for unified company model
const requiredIndexes = [
  'idx_company_name', 'idx_company_hubspot_id', 'idx_company_harvest_id',
  'idx_company_source', 'idx_company_radar_state', 'idx_company_priority',
  'idx_company_deleted_at', 'idx_company_last_interaction_date'
];

try {
  // Check if company table exists
  const companyTableExists = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table' AND name='company'
  `).get();

  if (!companyTableExists) {
    log('ERROR: company table does not exist!');
    process.exit(1);
  }

  log('Company table exists');

  // Check if company_radar table still exists
  const radarTableExists = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='table' AND name='company_radar'
  `).get();

  if (radarTableExists) {
    log('NOTE: company_radar table still exists. Consider dropping it after validation is complete.');
  } else {
    log('Company_radar table has been dropped');
  }

  // Check company table columns
  const tableInfo = db.prepare("PRAGMA table_info(company)").all();
  const columnNames = tableInfo.map(column => column.name);
  
  log(`Found ${columnNames.length} columns in company table: ${columnNames.join(', ')}`);

  const missingColumns = requiredColumns.filter(column => !columnNames.includes(column));
  if (missingColumns.length > 0) {
    log(`ERROR: Missing required columns: ${missingColumns.join(', ')}`);
  } else {
    log('All required columns exist in company table');
  }

  // Check company table indexes
  const indexInfo = db.prepare(`
    SELECT name FROM sqlite_master
    WHERE type='index' AND tbl_name='company'
  `).all();
  const indexNames = indexInfo.map(index => index.name);

  log(`Found ${indexNames.length} indexes on company table: ${indexNames.join(', ')}`);

  const missingIndexes = requiredIndexes.filter(index => !indexNames.includes(index));
  if (missingIndexes.length > 0) {
    log(`ERROR: Missing required indexes: ${missingIndexes.join(', ')}`);
  } else {
    log('All required indexes exist on company table');
  }

  // Check if there are any companies with radar data
  const radarCompanies = db.prepare(`
    SELECT COUNT(*) as count FROM company WHERE radar_state IS NOT NULL
  `).get();

  log(`Found ${radarCompanies.count} companies with radar_state data`);

  // Check if there are any companies with harvest_id
  const harvestCompanies = db.prepare(`
    SELECT COUNT(*) as count FROM company WHERE harvest_id IS NOT NULL
  `).get();

  log(`Found ${harvestCompanies.count} companies with harvest_id data`);

  // Check if there are any companies with hubspot_id
  const hubspotCompanies = db.prepare(`
    SELECT COUNT(*) as count FROM company WHERE hubspot_id IS NOT NULL
  `).get();

  log(`Found ${hubspotCompanies.count} companies with hubspot_id data`);

  // Overall validation result
  if (missingColumns.length === 0 && missingIndexes.length === 0) {
    log('VALIDATION SUCCESSFUL: Unified company model is correctly implemented');
  } else {
    log('VALIDATION FAILED: Unified company model is not correctly implemented');
  }

} catch (error) {
  logError('Error during validation', error);
} finally {
  // Close the database connection
  if (db) {
    db.close();
    log('Database connection closed');
  }
  // Close the log stream
  logStream.end();
}