import React, { useState, useEffect, useCallback, useMemo } from "react"; import { getXeroBills, convertBillToExpense, XeroBillDisplay, debouncedPublishExpenseUpdated, } from "../api/xero"; import { useEvents } from "../contexts"; import { useLoading } from "../contexts/LoadingContext"; import { LoadingIndicator } from "./ForwardProjection"; import { useAuthStatus } from "../hooks/useAuthStatus"; import { CustomExpense } from "../../types"; import { Card } from "./shared/Card"; import XeroBadge from "./shared/XeroBadge"; import { getExpenseTypeOptions } from "../../constants/expense-types"; /** * Component for displaying and converting Xero bills */ export const XeroBillsSection: React.FC = () => { const events = useEvents(); const { isAuthenticated } = useAuthStatus(); const { setLoading } = useLoading(); // Use global loading context const [bills, setBills] = useState<XeroBillDisplay[]>([]); const [localLoading, setLocalLoading] = useState<boolean>(true); const [error, setError] = useState<string | null>(null); const [lookbackDays, setLookbackDays] = useState<number>(30); const [statusFilter, setStatusFilter] = useState<string>("all"); // Loading states for individual actions const [processingIds, setProcessingIds] = useState<string[]>([]); const [successIds, setSuccessIds] = useState<string[]>([]); const [importCount, setImportCount] = useState<number>(0); // Track selected expense type for each bill const [selectedTypes, setSelectedTypes] = useState< Record<string, CustomExpense["type"]> >({}); const [typeSelectionErrors, setTypeSelectionErrors] = useState< Record<string, boolean> >({}); // Load bills on component mount and when lookback days changes useEffect(() => { if (isAuthenticated) { loadBills(); } }, [isAuthenticated, lookbackDays]); /** * Load bills from API */ const loadBills = async () => { setLocalLoading(true); setLoading(true, "xero"); // Set global loading state with Xero type setError(null); try { const data = await getXeroBills(lookbackDays); setBills(data); } catch (error: any) { setError(error.message || "Failed to load Xero bills"); } finally { setLocalLoading(false); setLoading(false); // Clear global loading state } }; /** * Handle adding a bill to expenses */ const handleAddToUpstream = async (bill: XeroBillDisplay) => { // Check if expense type is selected const selectedType = selectedTypes[bill.id]; if (!selectedType) { // Mark this bill as having a type selection error setTypeSelectionErrors((prev) => ({ ...prev, [bill.id]: true })); setError("Please select an expense type before adding to expenses."); return; } // Clear any type selection error for this bill setTypeSelectionErrors((prev) => ({ ...prev, [bill.id]: false })); setProcessingIds((prev) => [...prev, bill.id]); setError(null); try { // Pass the selected type to the API const expense = await convertBillToExpense(bill, selectedType); // Show success state setSuccessIds((prev) => [...prev, bill.id]); setImportCount((prev) => prev + 1); // Update the local state to mark this bill as already added setBills((prevBills) => prevBills.map((b) => b.id === bill.id ? { ...b, isAlreadyAdded: true } : b ) ); // Notify other components that expenses have been updated // Use debounced version to prevent event flooding debouncedPublishExpenseUpdated(events); // Remove from success state after 3 seconds setTimeout(() => { setSuccessIds((prev) => prev.filter((id) => id !== bill.id)); }, 3000); } catch (error: any) { setError( error.message || `Failed to add "${bill.reference}" to expenses` ); } finally { setProcessingIds((prev) => prev.filter((id) => id !== bill.id)); } }; /** * Handle expense type selection */ const handleTypeChange = (billId: string, type: CustomExpense["type"]) => { setSelectedTypes((prev) => ({ ...prev, [billId]: type })); // Clear any type selection error for this bill if (typeSelectionErrors[billId]) { setTypeSelectionErrors((prev) => ({ ...prev, [billId]: false })); } }; /** * Format currency */ const formatCurrency = (value: number): string => { return new Intl.NumberFormat("en-AU", { style: "currency", currency: "AUD", }).format(value); }; /** * Format date */ const formatDate = (date: Date): string => { // Format date only without time components to avoid timezone issues return date.toLocaleDateString("en-AU", { day: "numeric", month: "short", year: "numeric", }); }; /** * Get button state for a bill */ const getButtonState = (bill: XeroBillDisplay) => { if (processingIds.includes(bill.id)) return { text: "Adding...", disabled: true }; if (successIds.includes(bill.id)) return { text: "Added ✓", disabled: true }; return { text: "Add to Upstream", disabled: false }; }; /** * Filter bills based on status */ const filteredBills = useMemo(() => { if (statusFilter === "all") return bills; return bills.filter((bill) => { // Convert both to lowercase for case-insensitive comparison return bill.status.toLowerCase() === statusFilter.toLowerCase(); }); }, [bills, statusFilter]); /** * Reset state and reload */ const handleRefresh = () => { setProcessingIds([]); setSuccessIds([]); loadBills(); }; // Show authentication banner if not authenticated if (!isAuthenticated) { return ( <Card className="text-center py-8 bg-yellow-50/50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700"> <div className="flex items-center justify-center mb-2"> <svg className="w-6 h-6 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m0 0v2m0-2h2m-2 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" /> </svg> <h3 className="text-lg font-semibold text-yellow-700"> Authentication Required </h3> </div> <p className="text-yellow-600"> You need to connect to Xero to access bill data. </p> </Card> ); } return ( <div className="space-y-6"> <div className="xero-info-panel"> <div className="flex items-start"> <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /> </svg> <div> <h3 className="font-medium text-blue-800 dark:text-blue-300"> About Xero Bills Integration </h3> <p className="text-sm text-blue-700 dark:text-blue-400 mt-1"> This section shows bills from your Xero account. Click "Add to Upstream" to add a bill to your expenses for cashflow projection. </p> </div> </div> </div> {error && ( <Card className="btn-modern--primary/80"dark:bg-red-900/20 border-red-200 dark:border-red-700"> <div className="flex items-start"> <svg className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" /> </svg> <p className="text-sm text-red-600 dark:text-red-300">{error}</p> </div> </Card> )} {importCount > 0 && ( <Card className="btn-modern--primary/80"dark:bg-green-900/20 border-green-200 dark:border-green-700 border-l-4 border-l-green-500"> <div className="flex items-center"> <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" /> </svg> <p className="text-sm text-green-700 dark:text-green-400"> <span className="font-semibold">{importCount}</span> bill {importCount !== 1 ? "s" : ""} successfully added to expenses. </p> </div> </Card> )} <div className="xero-filter-bar"> <div className="xero-filter-group"> <div className="xero-filter-label"> <svg className="w-3.5 h-3.5 inline-block mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /> </svg> Time period </div> <select id="lookbackDays" value={lookbackDays} onChange={(e) => setLookbackDays(Number(e.target.value))} className="form-select rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 text-xs py-1 pl-2 pr-6" > <option value={30}>30 days</option> <option value={60}>60 days</option> <option value={90}>90 days</option> <option value={180}>180 days</option> </select> </div> <div className="xero-filter-group"> <div className="xero-filter-label"> <svg className="w-3.5 h-3.5 inline-block mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" /> </svg> Status </div> <select id="statusFilter" value={statusFilter} onChange={(e) => setStatusFilter(e.target.value)} className="form-select rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 text-xs py-1 pl-2 pr-6" > <option value="all">All Statuses</option> <option value="approved">Approved</option> <option value="awaiting approval">Awaiting Approval</option> <option value="awaiting payment">Awaiting Payment</option> <option value="draft">Draft</option> <option value="submitted">Submitted</option> </select> </div> <div className="ml-auto"> <button onClick={handleRefresh} className="xero-action-button xero-action-button--primary" disabled={localLoading} > {localLoading ? ( <> <svg className="w-3.5 h-3.5 xero-spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" > <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" ></circle> <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" ></path> </svg> <span>Loading...</span> </> ) : ( <> <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" /> </svg> <span>Refresh</span> </> )} </button> </div> </div> {localLoading ? ( <Card className="flex items-center justify-center py-12"> <div className="py-8 text-center text-gray-500 dark:text-gray-400"> <p>Loading bills from Xero...</p> </div> </Card> ) : filteredBills.length === 0 ? ( <Card className="text-center py-8 bg-gray-50/50 dark:bg-gray-800/50 border-gray-200/70 dark:border-gray-700/70"> <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /> </svg> <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100"> No bills found </h3> <p className="mt-1 text-sm text-gray-500 dark:text-gray-400"> No bills found in the selected time period. </p> </Card> ) : ( <> {/* Bill Count */} <div className="mb-4 text-sm text-gray-600 dark:text-gray-400"> Showing {filteredBills.length}{" "} {statusFilter !== "all" ? `${statusFilter} ` : ""}bill {filteredBills.length !== 1 ? "s" : ""} </div> {/* Mobile card view - only shown on small screens */} <div className="md:hidden space-y-3"> {filteredBills.map((bill) => { const buttonState = getButtonState(bill); return ( <Card key={bill.id} className="hover:shadow-md transition-all duration-200 border-t-2 border-t-blue-500/80" > <div className="flex justify-between items-center mb-3"> <div className="flex items-center space-x-2"> <h3 className="font-medium text-gray-900 dark:text-gray-100 truncate"> {bill.reference} </h3> <XeroBadge /> </div> <span className="font-bold text-accent whitespace-nowrap"> {formatCurrency(bill.amount)} </span> </div> <div className="mb-2"> <span className="text-sm text-gray-600 dark:text-gray-400"> {bill.vendor} </span> </div> <div className="grid grid-cols-2 gap-3 mb-3"> <div> <div className="text-xs text-gray-500 dark:text-gray-500 mb-0.5"> Date </div> <div className="flex items-center text-sm text-gray-600 dark:text-gray-400"> <svg className="w-3.5 h-3.5 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20" > <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" /> </svg> {formatDate(bill.date)} </div> </div> <div> <div className="text-xs text-gray-500 dark:text-gray-500 mb-0.5"> Due Date </div> <div className="flex items-center text-sm text-gray-600 dark:text-gray-400"> <svg className="w-3.5 h-3.5 mr-1 text-gray-500" fill="currentColor" viewBox="0 0 20 20" > <path d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" /> </svg> {formatDate(bill.dueDate)} </div> </div> </div> <div className="flex justify-between items-center mb-4"> <div className="text-xs text-gray-500 dark:text-gray-500"> Status </div> <div> <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700"> {bill.status} </span> </div> </div> <div className="mb-4"> <label htmlFor={`type-${bill.id}`} className="block text-xs text-gray-500 dark:text-gray-500 mb-1" > Expense Type </label> <select id={`type-${bill.id}`} value={selectedTypes[bill.id] || ""} onChange={(e) => handleTypeChange( bill.id, e.target.value as CustomExpense["type"] ) } className={`form-select w-full rounded-md shadow-sm py-1.5 ${ typeSelectionErrors[bill.id] ? "border-red-500 focus:border-red-500 focus:ring-red-500/20" : "border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500/20" }`} disabled={buttonState.disabled || bill.isAlreadyAdded} > <option value="" disabled> Select expense type </option> {getExpenseTypeOptions().map((option) => ( <option key={option.value} value={option.value}> {option.label} </option> ))} </select> {typeSelectionErrors[bill.id] && ( <p className="text-red-500 text-xs mt-1"> Please select an expense type </p> )} </div> <div className="flex justify-end"> <button onClick={() => handleAddToUpstream(bill)} disabled={buttonState.disabled || bill.isAlreadyAdded} className={`xero-action-button ${ successIds.includes(bill.id) ? "xero-action-button--success" : bill.isAlreadyAdded ? "xero-action-button--disabled" : "xero-action-button--primary" }`} > {bill.isAlreadyAdded ? ( <> <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /> </svg> <span>Added</span> </> ) : ( <span>Add to Upstream</span> )} </button> </div> </Card> ); })} </div> {/* Desktop table view - only shown on medium screens and up */} <div className="hidden md:block overflow-x-auto"> <Card className="p-0 overflow-hidden"> <table className="xero-bills-table"> <thead> <tr> <th className="reference-column">Reference</th> <th className="vendor-column">Vendor</th> <th className="date-column">Date</th> <th className="due-date-column">Due Date</th> <th className="amount-column">Amount</th> <th className="status-column">Status</th> <th className="type-column">Expense Type</th> <th className="action-column">Action</th> </tr> </thead> <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"> {filteredBills.map((bill, index) => { const buttonState = getButtonState(bill); return ( <tr key={bill.id}> <td className="reference-column"> <div className="flex items-center space-x-2"> <span className="truncate">{bill.reference}</span> <XeroBadge /> </div> </td> <td className="vendor-column">{bill.vendor}</td> <td className="date-column">{formatDate(bill.date)}</td> <td className="due-date-column"> {formatDate(bill.dueDate)} </td> <td className="amount-column"> {formatCurrency(bill.amount)} </td> <td className="status-column"> <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700"> {bill.status} </span> </td> <td className="type-column"> <div className="relative"> <select id={`desktop-type-${bill.id}`} value={selectedTypes[bill.id] || ""} onChange={(e) => handleTypeChange( bill.id, e.target.value as CustomExpense["type"] ) } className={`form-select w-full rounded-md shadow-sm py-1 text-sm ${ typeSelectionErrors[bill.id] ? "border-red-500 focus:border-red-500 focus:ring-red-500/20" : "border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500/20" }`} disabled={ buttonState.disabled || bill.isAlreadyAdded } > <option value="" disabled> Select type </option> {getExpenseTypeOptions().map((option) => ( <option key={option.value} value={option.value}> {option.label} </option> ))} </select> {typeSelectionErrors[bill.id] && ( <div className="absolute -bottom-4 left-0 right-0"> <p className="text-red-500 text-xs">Required</p> </div> )} </div> </td> <td className="action-column"> <button onClick={() => handleAddToUpstream(bill)} disabled={ buttonState.disabled || bill.isAlreadyAdded } className={`xero-action-button ${ successIds.includes(bill.id) ? "xero-action-button--success" : bill.isAlreadyAdded ? "xero-action-button--disabled" : "xero-action-button--primary" }`} > {bill.isAlreadyAdded ? ( <> <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" > <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /> </svg> <span>Added</span> </> ) : ( <span>Add to Upstream</span> )} </button> </td> </tr> ); })} </tbody> </table> </Card> </div> </> )} </div> ); }; export default XeroBillsSection; 