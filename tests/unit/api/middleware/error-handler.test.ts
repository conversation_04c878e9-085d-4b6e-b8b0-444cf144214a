import { Request, Response, NextFunction } from 'express';
import { errorHandler } from '../../../../src/api/middleware/error-handler';
import { AppError, ValidationError, NotFoundError } from '../../../../src/utils/error';

describe('Error Handler Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {};
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      locals: {}
    };
    mockNext = jest.fn();
  });

  it('should handle AppError with custom status code', () => {
    const error = new AppError('Custom error', 400);

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(400);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Custom error',
      statusCode: 400
    });
  });

  it('should handle ValidationError', () => {
    const error = new ValidationError('Invalid input');

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(400);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Invalid input',
      statusCode: 400,
      type: 'validation'
    });
  });

  it('should handle NotFoundError', () => {
    const error = new NotFoundError('Resource not found');

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(404);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Resource not found',
      statusCode: 404,
      type: 'not_found'
    });
  });

  it('should handle generic errors', () => {
    const error = new Error('Generic error');

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(500);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Internal server error',
      statusCode: 500
    });
  });

  it('should include stack trace in development', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    const error = new Error('Dev error');
    error.stack = 'Error stack trace';

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({
        stack: 'Error stack trace'
      })
    );

    process.env.NODE_ENV = originalEnv;
  });

  it('should not include stack trace in production', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    const error = new Error('Prod error');
    error.stack = 'Error stack trace';

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.not.objectContaining({
        stack: expect.any(String)
      })
    );

    process.env.NODE_ENV = originalEnv;
  });

  it('should handle errors with additional properties', () => {
    const error = new AppError('Error with details', 400);
    (error as any).details = { field: 'email', reason: 'invalid format' };

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Error with details',
        statusCode: 400,
        details: { field: 'email', reason: 'invalid format' }
      })
    );
  });

  it('should log errors', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    const error = new Error('Test error');

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(consoleSpy).toHaveBeenCalledWith('Error:', error);
    consoleSpy.mockRestore();
  });

  it('should handle database errors', () => {
    const error = new Error('SQLITE_CONSTRAINT: UNIQUE constraint failed');
    (error as any).code = 'SQLITE_CONSTRAINT';

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(409);
    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Database constraint violation',
        statusCode: 409
      })
    );
  });

  it('should handle timeout errors', () => {
    const error = new Error('Request timeout');
    (error as any).code = 'ETIMEDOUT';

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(408);
    expect(mockResponse.json).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Request timeout',
        statusCode: 408
      })
    );
  });

  it('should not send response if already sent', () => {
    mockResponse.headersSent = true;
    const error = new Error('Test error');

    errorHandler(error, mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
    expect(mockNext).toHaveBeenCalledWith(error);
  });
});