import { useState, useEffect } from "react";
import { XeroBillDisplay } from "../../api/xero";
import { formatCurrency } from "../../utils/format";
import { formatDate } from "./utils";

interface LateBillsSummaryProps {
  className?: string;
}

interface LateBillsData {
  bills: (XeroBillDisplay & { daysPastDue: number })[];
  totalAmount: number;
  count: number;
}

const LateBillsSummary = ({ className = "" }: LateBillsSummaryProps) => {
  const [lateBillsData, setLateBillsData] = useState<LateBillsData | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<boolean>(false);

  useEffect(() => {
    const fetchLateBills = async () => {
      setLoading(true);
      setError(null);

      try {
        // Use the existing API endpoint for late bills
        const response = await fetch("/api/xero/late-bills", {
          credentials: "include",
        });

        if (!response.ok) {
          throw new Error("Failed to fetch late bills");
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || "Failed to load late bills");
        }

        setLateBillsData(data.data);
      } catch (err: any) {
        console.error("Error fetching late bills:", err);
        setError(err.message || "Failed to load late bills");
      } finally {
        setLoading(false);
      }
    };

    fetchLateBills();
  }, []);

  // Always render the component, even with no late bills
  const hasNoLateBills =
    !loading && (!lateBillsData || lateBillsData.count === 0) && !error;

  // Sort bills by days past due (most overdue first)
  const sortedBills = lateBillsData?.bills
    ? [...lateBillsData.bills].sort((a, b) => b.daysPastDue - a.daysPastDue)
    : [];

  return (
    <div className={`relative group ${className}`}>
      {/* Adaptive card summary using container query-based design */}
      <button
        onClick={() => setExpanded(!expanded)}
        className={`
          flex items-center justify-between h-full min-h-[40px] px-2 py-1 rounded-lg w-full
          transition-all duration-200
          ${
            hasNoLateBills
              ? "bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200 hover:bg-green-100 dark:hover:bg-green-800/40 hover:shadow-md border-green-200 dark:border-green-800/50"
              : expanded
              ? "bg-red-100 dark:bg-red-900/40 text-red-800 dark:text-red-200 shadow-md border-red-200 dark:border-red-800/50"
              : "bg-yellow-50 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-800/40 hover:shadow-md border-yellow-200 dark:border-yellow-800/50"
          }
          border
          focus:outline-none focus:ring-2 ${
            hasNoLateBills
              ? "focus:ring-green-500/40 dark:focus:ring-green-600/40"
              : expanded
              ? "focus:ring-red-500/40 dark:focus:ring-red-600/40"
              : "focus:ring-yellow-500/40 dark:focus:ring-yellow-600/40"
          }
        `}
      >
        <div className="flex items-center">
          {loading ? (
            <span className="text-xs font-medium animate-pulse">
              Checking...
            </span>
          ) : error ? (
            <span className="text-xs font-medium text-red-600 dark:text-red-400">
              Error loading bills
            </span>
          ) : hasNoLateBills ? (
            <>
              <svg
                className="w-4 h-4 mr-1 flex-shrink-0 text-green-600 dark:text-green-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <span
                className="text-xs font-medium whitespace-nowrap mr-2"
                style={{ fontSize: "12px" }}
              >
                No late bills
              </span>
              <span
                className="font-bold whitespace-nowrap text-green-700 dark:text-green-300"
                style={{ fontSize: "12px" }}
              >
                $0
              </span>
            </>
          ) : (
            <>
              <span
                className="text-xs font-medium whitespace-nowrap mr-2"
                style={{ fontSize: "12px" }}
              >
                {lateBillsData?.count}{" "}
                {lateBillsData?.count === 1 ? "late bill" : "late bills"}
              </span>
              <span
                className={`font-bold whitespace-nowrap ${
                  expanded
                    ? "text-red-700 dark:text-red-300"
                    : "text-yellow-700 dark:text-yellow-300"
                }`}
                style={{ fontSize: "12px" }}
              >
                {formatCurrency(lateBillsData?.totalAmount || 0)}
              </span>
            </>
          )}
        </div>

        {/* Expand/collapse icon */}
        <svg
          className={`h-5 w-5 transform transition-transform duration-200 ${
            expanded ? "rotate-180" : ""
          }`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {/* Dropdown panel with adaptive styling using container queries */}
      {expanded && (
        <div className="absolute top-full left-0 lg:left-auto lg:right-0 mt-1 z-10 w-full min-w-[300px] sm:min-w-[400px] md:min-w-[460px] max-w-md transform transition-all duration-200 ease-out scale-100 opacity-100 origin-top-right">
          <div
            className={`adaptive-card container-type-inline-size overflow-hidden rounded-lg shadow-lg border ${
              hasNoLateBills
                ? "border-green-200 dark:border-green-800/50"
                : "border-yellow-200 dark:border-yellow-800/50"
            } dark:shadow-xl dark:shadow-gray-900/30 p-0 bg-white dark:bg-gray-800`}
          >
            {/* Panel Header */}
            <div
              className={`${
                hasNoLateBills
                  ? "bg-green-50 dark:bg-green-900/20"
                  : "bg-yellow-50 dark:bg-yellow-900/20"
              } px-3 sm:px-4 py-2 border-b ${
                hasNoLateBills
                  ? "border-green-100 dark:border-green-800/30"
                  : "border-yellow-100 dark:border-yellow-800/30"
              }`}
            >
              <h3
                className={`text-xs sm:text-sm font-semibold ${
                  hasNoLateBills
                    ? "text-green-800 dark:text-green-200"
                    : "text-yellow-800 dark:text-yellow-200"
                }`}
              >
                Overdue Bills
              </h3>
              <p
                className={`text-[10px] sm:text-xs ${
                  hasNoLateBills
                    ? "text-green-600 dark:text-green-400"
                    : "text-yellow-600 dark:text-yellow-400"
                }`}
              >
                {hasNoLateBills
                  ? "All bills are up to date"
                  : "Not reflected in your forecast"}
              </p>
            </div>

            {/* Content area */}
            {hasNoLateBills ? (
              <div className="bg-white dark:bg-gray-800 p-8 text-center">
                <svg
                  className="w-12 h-12 mx-auto mb-3 text-green-600 dark:text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  No overdue bills
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  All your vendor payments are on track
                </p>
              </div>
            ) : (
              <>
                {/* Bills List with responsive design */}
                <div className="overflow-y-auto w-full max-h-[300px] sm:max-h-[400px]">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800/90 sticky top-0">
                      <tr>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 text-left text-[10px] sm:text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/2"
                        >
                          Vendor
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-3 py-2 text-left text-[10px] sm:text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4"
                        >
                          Due
                        </th>
                        <th
                          scope="col"
                          className="px-2 sm:px-4 py-2 text-right text-[10px] sm:text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4"
                        >
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
                      {sortedBills.map((bill) => (
                        <tr
                          key={bill.id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <td className="px-2 sm:px-4 py-1 sm:py-2">
                            <div className="flex flex-col">
                              <span
                                className="text-[10px] sm:text-xs font-medium text-gray-900 dark:text-gray-200 truncate max-w-[150px] sm:max-w-[190px]"
                                title={bill.reference}
                              >
                                {bill.reference}
                              </span>
                              <span className="text-[10px] sm:text-xs font-medium text-red-600 dark:text-red-300">
                                {bill.daysPastDue}{" "}
                                {bill.daysPastDue === 1 ? "day" : "days"} late
                              </span>
                            </div>
                          </td>
                          <td className="px-2 sm:px-3 py-1 sm:py-2 text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(bill.dueDate.toString())}
                          </td>
                          <td className="px-2 sm:px-4 py-1 sm:py-2 text-[10px] sm:text-xs font-medium text-gray-900 dark:text-gray-200 text-right">
                            {formatCurrency(bill.amount)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Panel Footer */}
                <div className="bg-gray-50 dark:bg-gray-800 px-3 sm:px-4 py-2 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex justify-between items-center">
                    <span className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
                      Total
                    </span>
                    <span className="text-xs sm:text-sm font-bold text-gray-900 dark:text-gray-100">
                      {formatCurrency(lateBillsData?.totalAmount || 0)}
                    </span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LateBillsSummary;
