import React from 'react'; import '../../../styles/components/decision-card.css'; // Import component-specific CSS import { ProcessedDecision } from '../types/projection-audit-types'; import { formatDate, formatCurrency } from '../utils'; import DecisionCell from './DecisionCell'; // For the icon/status import { DecisionDescription } from './DecisionTable'; // Re-use description component import { RealInvoiceReconciliation, UninvoicedWorkReconciliation, PaymentTermsReconciliation } from './ReconciliationCards'; import ErrorBoundary from './ErrorBoundary'; import { safeString } from '../utils/projection-audit-utils'; interface DecisionCardProps { decision: ProcessedDecision; isExpanded: boolean; onToggleExpand: () => void; } const DecisionCard: React.FC<DecisionCardProps> = ({ decision, isExpanded, onToggleExpand }) => { const canExpand = decision.action === 'excluded'; // Determine which reconciliation card to show const renderReconciliationCard = () => { if (decision.relatedInvoiceId) { return <RealInvoiceReconciliation decision={decision} />; } switch (decision.ruleType) { case 'UNINVOICED_WORK': return <UninvoicedWorkReconciliation decision={decision} />; case 'PAYMENT_TERMS': return <PaymentTermsReconciliation decision={decision} />; case 'REAL_INVOICE': // This case should ideally be handled by relatedInvoiceId check above, // but include fallback/error display just in case. return <div className="text-xs text-red-500">Error: REAL_INVOICE without related ID.</div>; default: return ( <div className="text-xs text-gray-500"> No specific reconciliation view for rule type: {safeString(decision.ruleType)} </div> ); } }; return ( // Using adaptive-card class from shared styles for base styling <div className="decision-card adaptive-card border border-gray-200 dark:border-gray-700 rounded-lg mb-2 overflow-hidden"> {/* Main clickable area */} <div className={`decision-card__main p-fluid-sm flex flex-col gap-1 ${canExpand ? 'cursor-pointer' : ''}`} onClick={canExpand ? onToggleExpand : undefined} role={canExpand ? 'button' : undefined} tabIndex={canExpand ? 0 : undefined} aria-expanded={canExpand ? isExpanded : undefined} > {/* Row 1: Decision Icon/Status & Amount */} <div className="flex justify-between items-start"> <div className="flex items-center"> {/* Using DecisionCell for consistent icon/status */} <ErrorBoundary> <DecisionCell decision={decision} /> </ErrorBoundary> {/* Status Text Badge */} <span className={`ml-2 px-1.5 py-0.5 inline-flex items-center text-[10px] font-medium rounded border ${ decision.action === 'kept' ? 'border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300' : 'border-yellow-200 dark:border-yellow-800/50 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' }`}> {decision.action === 'kept' ? 'Included' : 'Excluded'} </span> </div> <span className="text-sm font-medium text-gray-800 dark:text-gray-200 whitespace-nowrap"> {formatCurrency(decision.amount)} </span> </div> {/* Row 2: Description (Project/Client) & Date */} <div className="flex justify-between items-start text-xs"> {/* Using DecisionDescription for consistent formatting */} <DecisionDescription decision={decision} /> <span className="text-gray-500 dark:text-gray-400 whitespace-nowrap text-right"> {formatDate(decision.paymentDate)} {decision.invoiceDate && ( <span className="block text-[10px] text-gray-400 dark:text-gray-500"> Inv: {formatDate(decision.invoiceDate)} </span> )} </span> </div> {/* Row 3: Reason Badge & Expand Indicator */} <div className="flex justify-between items-center mt-1"> {/* Reason Badge (simplified for card view) */} <span className={`px-1.5 py-0.5 inline-flex items-center text-[10px] font-medium rounded border ${decision.typeColor}`}> {decision.displayType || 'Unknown Reason'} </span> {/* Expand Indicator */} {canExpand && ( <span className="decision-card__expand-indicator text-gray-400 dark:text-gray-500"> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}> <path fillRule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" /> </svg> </span> )} </div> </div> {/* Expanded Section (Conditional Rendering + Animation) */} {/* TODO: Add animation (e.g., using framer-motion or simple CSS transitions) */} {isExpanded && canExpand && ( <div className="decision-card__expanded-content border-t border-gray-200 dark:border-gray-700 p-fluid-sm bg-gray-50/50 dark:bg-gray-900/20"> <ErrorBoundary fallback={ <div className="text-xs text-red-500 dark:text-red-400 p-2 border border-red-200 dark:border-red-900 rounded"> Error displaying detail view. </div> } > {renderReconciliationCard()} </ErrorBoundary> </div> )} </div> ); }; export default DecisionCard; 