import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { <PERSON>rowserRouter } from 'react-router-dom';
import EstimatePage from '../../../src/frontend/components/EstimatePage';
import * as api from '../../../src/frontend/api/estimates';
import * as harvestApi from '../../../src/frontend/api/harvest';
import { UserContext } from '../../../src/frontend/contexts/UserContext';
import { LoadingContext } from '../../../src/frontend/contexts/LoadingContext';

// Mock API modules
jest.mock('../../../src/frontend/api/estimates');
jest.mock('../../../src/frontend/api/harvest');

// Mock child components
jest.mock('../../../src/frontend/components/Estimate/EstimatesList', () => ({
  __esModule: true,
  default: ({ onEstimateSelect }: any) => (
    <div data-testid="estimates-list">
      <button onClick={() => onEstimateSelect({ estimate_number: 'EST-001' })}>
        Select Estimate
      </button>
    </div>
  )
}));

jest.mock('../../../src/frontend/components/Estimate/EstimateConfigurationForm', () => ({
  __esModule: true,
  default: ({ onCancel }: any) => (
    <div data-testid="estimate-config-form">
      <button onClick={onCancel}>Cancel</button>
    </div>
  )
}));

jest.mock('../../../src/frontend/components/Estimate/EstimateTable', () => ({
  __esModule: true,
  default: ({ estimate }: any) => (
    <div data-testid="estimate-table">
      Estimate: {estimate.estimate_number}
    </div>
  )
}));

describe('EstimatePage', () => {
  let queryClient: QueryClient;
  const mockSetIsLoading = jest.fn();
  
  const mockUserContext = {
    user: {
      email: '<EMAIL>',
      harvest_id: 123
    }
  };

  const mockLoadingContext = {
    isLoading: false,
    setIsLoading: mockSetIsLoading
  };

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <UserContext.Provider value={mockUserContext}>
          <LoadingContext.Provider value={mockLoadingContext}>
            <BrowserRouter>
              <EstimatePage />
            </BrowserRouter>
          </LoadingContext.Provider>
        </UserContext.Provider>
      </QueryClientProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    // Default API mocks
    (api.getEstimates as jest.Mock).mockResolvedValue([
      {
        estimate_number: 'EST-001',
        project_name: 'Test Project',
        client_name: 'Test Client',
        total_amount: 10000,
        date_sent: '2024-01-01'
      }
    ]);

    (api.getDrafts as jest.Mock).mockResolvedValue([]);
    (harvestApi.getProjects as jest.Mock).mockResolvedValue([]);
    (harvestApi.getClients as jest.Mock).mockResolvedValue([]);
    (harvestApi.getUsers as jest.Mock).mockResolvedValue([]);
  });

  describe('rendering', () => {
    it('should render the estimates list by default', async () => {
      renderComponent();

      await waitFor(() => {
        expect(screen.getByTestId('estimates-list')).toBeInTheDocument();
      });
    });

    it('should render the page header', () => {
      renderComponent();

      expect(screen.getByText('Estimates')).toBeInTheDocument();
      expect(screen.getByText('Create and manage project estimates')).toBeInTheDocument();
    });

    it('should render the create new button', () => {
      renderComponent();

      expect(screen.getByText('Create New')).toBeInTheDocument();
    });
  });

  describe('navigation', () => {
    it('should show configuration form when create new is clicked', async () => {
      renderComponent();

      const createButton = screen.getByText('Create New');
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(screen.getByTestId('estimate-config-form')).toBeInTheDocument();
      });
    });

    it('should return to list view when form is cancelled', async () => {
      renderComponent();

      // Navigate to form
      fireEvent.click(screen.getByText('Create New'));
      
      await waitFor(() => {
        expect(screen.getByTestId('estimate-config-form')).toBeInTheDocument();
      });

      // Cancel form
      fireEvent.click(screen.getByText('Cancel'));

      await waitFor(() => {
        expect(screen.getByTestId('estimates-list')).toBeInTheDocument();
      });
    });

    it('should show estimate detail when estimate is selected', async () => {
      renderComponent();

      const selectButton = screen.getByText('Select Estimate');
      fireEvent.click(selectButton);

      await waitFor(() => {
        expect(screen.getByTestId('estimate-table')).toBeInTheDocument();
        expect(screen.getByText('Estimate: EST-001')).toBeInTheDocument();
      });
    });
  });

  describe('data fetching', () => {
    it('should fetch estimates on mount', async () => {
      renderComponent();

      await waitFor(() => {
        expect(api.getEstimates).toHaveBeenCalled();
      });
    });

    it('should fetch drafts on mount', async () => {
      renderComponent();

      await waitFor(() => {
        expect(api.getDrafts).toHaveBeenCalled();
      });
    });

    it('should handle API errors gracefully', async () => {
      (api.getEstimates as jest.Mock).mockRejectedValue(new Error('API Error'));

      renderComponent();

      await waitFor(() => {
        expect(screen.queryByTestId('estimates-list')).toBeInTheDocument();
      });
    });
  });

  describe('draft handling', () => {
    it('should show draft count when drafts exist', async () => {
      (api.getDrafts as jest.Mock).mockResolvedValue([
        { id: 1, name: 'Draft 1' },
        { id: 2, name: 'Draft 2' }
      ]);

      renderComponent();

      await waitFor(() => {
        expect(screen.getByText('2 Drafts')).toBeInTheDocument();
      });
    });

    it('should not show draft count when no drafts', async () => {
      (api.getDrafts as jest.Mock).mockResolvedValue([]);

      renderComponent();

      await waitFor(() => {
        expect(screen.queryByText(/Drafts/)).not.toBeInTheDocument();
      });
    });
  });

  describe('loading states', () => {
    it('should show loading indicator when fetching data', async () => {
      let resolveEstimates: any;
      (api.getEstimates as jest.Mock).mockImplementation(() => 
        new Promise(resolve => { resolveEstimates = resolve; })
      );

      renderComponent();

      expect(mockSetIsLoading).toHaveBeenCalledWith(true);

      // Resolve the promise
      resolveEstimates([]);

      await waitFor(() => {
        expect(mockSetIsLoading).toHaveBeenCalledWith(false);
      });
    });
  });

  describe('error handling', () => {
    it('should display error message when estimates fail to load', async () => {
      const consoleError = jest.spyOn(console, 'error').mockImplementation();
      (api.getEstimates as jest.Mock).mockRejectedValue(new Error('Failed to load'));

      renderComponent();

      await waitFor(() => {
        expect(consoleError).toHaveBeenCalledWith(
          'Error fetching estimates:',
          expect.any(Error)
        );
      });

      consoleError.mockRestore();
    });
  });

  describe('permissions', () => {
    it('should render for authenticated users', () => {
      renderComponent();

      expect(screen.getByText('Estimates')).toBeInTheDocument();
    });

    it('should handle missing user context', () => {
      render(
        <QueryClientProvider client={queryClient}>
          <UserContext.Provider value={{ user: null }}>
            <LoadingContext.Provider value={mockLoadingContext}>
              <BrowserRouter>
                <EstimatePage />
              </BrowserRouter>
            </LoadingContext.Provider>
          </UserContext.Provider>
        </QueryClientProvider>
      );

      // Should still render but with limited functionality
      expect(screen.getByText('Estimates')).toBeInTheDocument();
    });
  });

  describe('refresh functionality', () => {
    it('should refetch data when refresh is triggered', async () => {
      renderComponent();

      await waitFor(() => {
        expect(api.getEstimates).toHaveBeenCalledTimes(1);
      });

      // Simulate refresh action (this would typically be triggered by a button or event)
      queryClient.invalidateQueries({ queryKey: ['estimates'] });

      await waitFor(() => {
        expect(api.getEstimates).toHaveBeenCalledTimes(2);
      });
    });
  });
});