# Onbord Financial Dashboard

A comprehensive financial management platform that integrates with Xero, Harvest, and HubSpot to provide unified financial insights, cashflow projections, and business intelligence.

## Features

### Core Functionality
- **Smart Forecast System**: Automated income projections based on project budgets and invoice patterns
- **Cashflow Visualization**: Interactive charts with worst-case, expected, and best-case scenarios
- **CRM Integration**: Deal pipeline management with HubSpot synchronization
- **Knowledge Graph**: Interactive network visualization of business relationships and entities
- **Expense Management**: Custom expense tracking integrated with Xero
- **Project Estimates**: Sophisticated estimation tools with Harvest integration
- **Tax Calendar**: Australian tax obligation tracking and reminders

### Key Integrations
- **Xero**: Accounting data, bills, invoices, and financial reports
- **Harvest**: Time tracking, project management, and invoicing
- **HubSpot**: CRM data, deals, companies, contacts, notes/activities, and associations
- **ABN Lookup**: Australian business data enrichment for companies
- **MCP (Model Context Protocol)**: Natural language interaction with Xero data

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- SQLite (included)
- Redis (optional for production)
- API credentials for Xero, Harvest, and optionally HubSpot

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/onbord-financial-dashboard.git
cd onbord-financial-dashboard

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Configure your API credentials in .env
```

### Development

```bash
# Run both frontend and backend
npm run dev

# Run with mock authentication (for testing)
npm run dev:mock

# Run frontend only
npm run dev:frontend

# Run backend only
npm run dev:backend
```

The application will be available at:
- Frontend: http://localhost:5173
- API: http://localhost:3002

## Testing

This project includes a comprehensive test suite covering unit tests, integration tests, and end-to-end tests.

### Running Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests
npm run test:e2e          # End-to-end tests
npm run test:schema       # Database schema validation

# Run with coverage
npm test -- --coverage

# Run in watch mode
npm test -- --watch
```

### Test Organization

```
tests/
├── unit/              # Isolated component and service tests
├── integration/       # API and cross-module tests
├── e2e/              # Browser automation tests
└── real-data/        # Tests against live APIs
```

For detailed testing documentation, see:
- [Testing Guide](tests/TESTING-GUIDE.md)
- [Test Coverage Summary](tests/TEST-COVERAGE-SUMMARY.md)

## Documentation

Comprehensive documentation is available in the `docs/` directory:

- **[User Guide](docs/user-guide/README.md)**: End-user documentation
- **[Technical Documentation](docs/technical/README.md)**: Architecture and implementation details
- **[API Reference](docs/api-reference/README.md)**: Internal and external API documentation
- **[Development Guide](DEVELOPMENT-GUIDE.md)**: Developer workflow and guidelines
- **[Deployment Guide](DEPLOYMENT-GUIDE.md)**: Deployment instructions

## Project Structure

```
src/
├── api/              # Backend API implementation
│   ├── controllers/  # Request handlers
│   ├── routes/       # API endpoint definitions
│   ├── repositories/ # Data access layer
│   └── services/     # Business logic
├── frontend/         # React frontend application
│   ├── components/   # UI components
│   ├── hooks/        # Custom React hooks
│   ├── contexts/     # React contexts
│   └── api/          # Frontend API clients
├── services/         # Shared business services
├── database/         # Database schema and migrations
└── utils/           # Shared utilities
```

## Environment Variables

Key environment variables (see `.env.example` for full list):

```bash
# Xero OAuth
XERO_CLIENT_ID=your_xero_client_id
XERO_CLIENT_SECRET=your_xero_client_secret

# Harvest API
HARVEST_ACCOUNT_ID=your_harvest_account_id
HARVEST_ACCESS_TOKEN=your_harvest_token

# HubSpot (optional)
HUBSPOT_ACCESS_TOKEN=your_hubspot_token

# Redis (required in production)
REDIS_URL=redis://localhost:6379

# Application
NODE_ENV=development
PORT=3002
```

## Deployment

The application is designed for deployment on Render.com:

```bash
# Production deployment (auto-deploys from main branch)
git push origin main

# Preview deployment (auto-deploys from preview branch)
git push origin preview
```

See [DEPLOYMENT-GUIDE.md](DEPLOYMENT-GUIDE.md) for detailed deployment instructions.

## Contributing

1. Create a feature branch from `preview`
2. Make your changes with appropriate tests
3. Submit a pull request to `preview`
4. After review and testing, changes will be merged to `main`

See [DEVELOPMENT-GUIDE.md](DEVELOPMENT-GUIDE.md) for detailed contribution guidelines.

## License

Proprietary - Onbord Pty Ltd

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: See `docs/` directory
- Issues: Use GitHub issues for bug reports

---

Built with ❤️ by Onbord