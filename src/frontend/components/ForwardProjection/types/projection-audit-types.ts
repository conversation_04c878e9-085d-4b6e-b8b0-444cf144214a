export interface FilterDecision {
  projectId: string;
  projectName: string;
  invoiceType: string;
  invoiceDate: string;
  paymentDate: string;
  amount: number;
  reason: string;
  action: 'kept' | 'excluded';
  relatedInvoice?: string;
  relatedInvoiceDate?: string;         // Payment date of the related invoice
  relatedInvoiceDueDate?: string;      // Due date of the related invoice
  relatedInvoiceId?: string;           // Invoice ID for URL construction
  relatedInvoiceNumber?: string;       // Invoice number for display
  id?: string; // For supporting legacy code
  
  // New properties
  clientName?: string;                 // Client name for consistent display
  formattedDescription?: string;       // Formatted description like TransactionsList
  relatedProjectedItem?: {             // For Rule 1 (Uninvoiced Work Rule)
    id?: string;
    description?: string;
    date?: string;
    amount?: number;
  };
  paymentTermsDays?: number;           // For Rule 3 (Payment Terms Rule)
  cutoffDate?: string;                 // For Rule 3 to display when invoice falls outside window
  projectSettings?: {                  // For Rule 3 to show configured settings
    invoiceFrequency?: string;
    paymentTerms?: number;
  };
  rule?: keyof typeof FILTER_RULES;    // Normalized rule type for easier processing
}

export interface FilterSummary {
  totalInvoices: number;
  keptInvoices: number;
  excludedInvoices: number;
  byReason: Record<string, number>;
}

export interface ProjectionFilterEvent {
  invoice: {
    id: string;
    projectId: string;
    projectName: string;
    type: string;
    what: string;
    date: Date | string;
    invoiceDate?: Date | string;
    amount: number;
    description?: string;
    metadata?: any;
  };
  action: 'kept' | 'excluded';
  reason: string;
  relatedInvoice?: {
    what: string;
    date: Date | string;
    id?: string;
    description?: string;
    metadata?: {
      id?: string;
    };
  };
}

/**
 * Extended decision with processed display properties
 */
export interface ProcessedDecision extends FilterDecision {
  formattedDescription: string;        // Client | Project format
  ruleType: keyof typeof FILTER_RULES; // Normalized rule
  displayType: string;                 // For UI presentation
  typeColor: string;                   // CSS classes for styling
}

// Constants
export const FILTER_RULES = {
  UNINVOICED_WORK: 'Projected income from same project exists within 3 days',
  REAL_INVOICE: 'Real invoice from same project exists within 5 days',
  PAYMENT_TERMS: 'Payment date before cutoff',
  MEETS_CRITERIA: 'Meets projection criteria'
};