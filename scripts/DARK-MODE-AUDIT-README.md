# Dark Mode Audit Script (Development Tool)

A **development-only** tool to scan your React/TypeScript codebase for missing dark mode support. This is NOT used in production or deployments.

## What it does

The dark mode audit script scans your codebase and identifies:

1. **Missing Dark Mode Classes** - Tailwind classes that should have `dark:` variants but don't
2. **Hardcoded Colors** - Hex colors, RGB values that won't adapt to dark mode
3. **Suspicious Patterns** - Inline styles, Canvas/SVG colors that might need dark mode support

## Usage

### Run on specific directory (recommended):
```bash
npm run dev:audit-dark-mode
# Scans src/frontend/components by default
```

### Run on entire source:
```bash
npm run dev:audit-dark-mode-all
# Scans entire src directory
```

### Run on custom path:
```bash
node scripts/dark-mode-audit.js path/to/directory
# Or for a single file:
node scripts/dark-mode-audit.js path/to/file.tsx
```

## Output

The script generates:
1. **Console Report** - Detailed findings organized by type
2. **CSV Report** - `dark-mode-audit-report.csv` for tracking and sharing

## Example Output

```
🌓 Dark Mode Audit Report
========================

📊 Summary:
   Files scanned: 142
   Total issues found: 47
   Missing dark mode variants: 32
   Hardcoded colors: 12
   Suspicious patterns: 3

🔍 Missing Dark Mode Classes:
----------------------------

📄 src/frontend/components/XeroChat/index.tsx
   Line 207: bg-white → dark:bg-gray-800
   Context: className="fixed bottom-6 left-4 w-96 h-[600px] bg-white rounded-lg"
```

## Common Issues Found

### 1. Background Colors
```tsx
// ❌ Missing dark mode
<div className="bg-white">

// ✅ With dark mode
<div className="bg-white dark:bg-gray-800">
```

### 2. Text Colors
```tsx
// ❌ Missing dark mode
<p className="text-gray-900">

// ✅ With dark mode
<p className="text-gray-900 dark:text-white">
```

### 3. Borders
```tsx
// ❌ Missing dark mode
<div className="border border-gray-200">

// ✅ With dark mode
<div className="border border-gray-200 dark:border-gray-700">
```

### 4. Hardcoded Colors
```tsx
// ❌ Hardcoded color
<div style={{ backgroundColor: '#FFFFFF' }}>

// ✅ Use Tailwind classes
<div className="bg-white dark:bg-gray-800">
```

## Color Mapping Reference

| Light Mode | Dark Mode |
|------------|-----------|
| `bg-white` | `dark:bg-gray-800` |
| `bg-gray-50` | `dark:bg-gray-900` |
| `bg-gray-100` | `dark:bg-gray-700` |
| `text-gray-900` | `dark:text-white` |
| `text-gray-700` | `dark:text-gray-300` |
| `border-gray-200` | `dark:border-gray-700` |
| `bg-red-50` | `dark:bg-red-900/20` |
| `text-red-700` | `dark:text-red-400` |

## Fixing Issues

### Quick Fixes

1. **Use find and replace** for common patterns:
   - Find: `"bg-white`
   - Replace: `"bg-white dark:bg-gray-800`

2. **Use the CSV report** to systematically fix issues:
   - Sort by file to fix multiple issues at once
   - Use the suggested fixes column

### Complex Cases

1. **Canvas/SVG Colors** - Use CSS variables:
```tsx
// Define in CSS
:root {
  --chart-color: #3B82F6;
}
.dark {
  --chart-color: #60A5FA;
}

// Use in code
ctx.fillStyle = getComputedStyle(document.documentElement)
  .getPropertyValue('--chart-color');
```

2. **Dynamic Classes** - Use complete class names:
```tsx
// ❌ Tailwind can't detect this
const bgColor = isDark ? 'dark:bg-gray-800' : 'bg-white';

// ✅ Include both in className
<div className="bg-white dark:bg-gray-800">
```

## Configuration

You can customize the script by editing `scripts/dark-mode-audit.js`:

- `DARK_MODE_MAPPINGS` - Add custom color mappings
- `FILE_EXTENSIONS` - Change which files to scan
- `SKIP_DIRS` - Add directories to ignore

## Best Practices

1. **Run during development** - Check your work before committing
2. **Fix incrementally** - Start with the most visible components
3. **Test thoroughly** - Always test in both light and dark modes
4. **Document exceptions** - Some colors (logos, charts) might intentionally not change

## Important Notes

- This is a **development tool only** - not used in production
- The script doesn't need to be run during builds or deployments
- It's meant to help developers catch missing dark mode support while coding

## Limitations

- Only detects Tailwind utility classes (not CSS-in-JS or styled-components)
- May have false positives for intentionally static colors
- Doesn't detect runtime theme switching issues

## Future Enhancements

- [ ] Integration with ESLint
- [ ] Auto-fix capability for simple cases
- [ ] Visual regression testing for dark mode
- [ ] Support for CSS-in-JS libraries