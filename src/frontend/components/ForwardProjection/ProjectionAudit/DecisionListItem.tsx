import React from "react"; import { ProcessedDecision } from "../types/projection-audit-types"; import { formatDate, formatCurrency } from "../utils"; import { safeString } from "../utils/projection-audit-utils"; import DecisionCell from "./DecisionCell"; import { DecisionDescription } from "./DecisionTable"; import { RealInvoiceReconciliation, UninvoicedWorkReconciliation, PaymentTermsReconciliation, } from "./ReconciliationCards"; import ErrorBoundary from "./ErrorBoundary"; interface DecisionListItemProps { decision: ProcessedDecision; isExpanded: boolean; onToggleExpand: () => void; index: number; isMobile: boolean; } /** * Modern unified DecisionListItem component with mobile/desktop views * Renders card layout for mobile, table row for desktop * Consolidates decision rendering logic from both mobile and desktop views */ export const DecisionListItem = ({ decision, isExpanded, onToggleExpand, index, isMobile, }: DecisionListItemProps) => { const canExpand = decision.action === "excluded"; // Consolidated reconciliation rendering logic const renderReconciliationCard = () => { if (decision.relatedInvoiceId) { return <RealInvoiceReconciliation decision={decision} />; } switch (decision.ruleType) { case "UNINVOICED_WORK": return <UninvoicedWorkReconciliation decision={decision} />; case "PAYMENT_TERMS": return <PaymentTermsReconciliation decision={decision} />; case "REAL_INVOICE": return ( <div className="text-xs text-yellow-500 dark:text-yellow-400 p-2 border border-yellow-200 dark:border-yellow-900 rounded"> <div className="font-medium"> REAL_INVOICE without relatedInvoiceId </div> <div className="text-[10px] mt-1"> This should never happen - please report this issue </div> </div> ); default: return ( <div className="text-xs text-red-500 dark:text-red-400 p-2 border border-red-200 dark:border-red-900 rounded"> <div className="font-medium"> Unhandled rule type: {safeString(decision.ruleType)} </div> <div className="text-[10px] mt-1"> Action: {decision.action}, Has relatedInvoiceId:{" "} {Boolean(decision.relatedInvoiceId).toString()}, Reason:{" "} {decision.reason} </div> </div> ); } }; // Background color based on index for table view const getBackgroundColorClass = () => { return index % 2 === 0 ? "bg-white dark:bg-gray-800" : "bg-gray-50/50 dark:bg-gray-800/50"; }; const bgColorClass = getBackgroundColorClass(); // Mobile card view if (isMobile) { return ( <div className={`${bgColorClass} border-b border-gray-200 dark:border-gray-700`}> <div className={`p-4 flex flex-col gap-3 ${ canExpand ? "cursor-pointer" : "" }`} onClick={canExpand ? onToggleExpand : undefined} role={canExpand ? "button" : undefined} tabIndex={canExpand ? 0 : undefined} aria-expanded={canExpand ? isExpanded : undefined} > {/* Row 1: Decision Icon/Status & Amount */} <div className="flex justify-between items-start"> <div className="flex items-center"> <ErrorBoundary> <DecisionCell decision={decision} /> </ErrorBoundary> {/* Status Text Badge */} <span className={`ml-2 px-1.5 py-0.5 inline-flex items-center text-[10px] font-medium rounded border ${ decision.action === "kept" ? "border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300" : "border-yellow-200 dark:border-yellow-800/50 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300" }`} > {decision.action === "kept" ? ( <> <span className="h-1.5 w-1.5 rounded-full bg-green-500 dark:bg-green-400 mr-1.5"></span> Included </> ) : ( <> <svg className="w-3 h-3 mr-1 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> Excluded </> )} </span> </div> <span className="text-sm font-medium text-gray-800 dark:text-gray-200 whitespace-nowrap"> {formatCurrency(decision.amount)} </span> </div> {/* Row 2: Description (Project/Client) & Date */} <div className="flex justify-between items-start text-xs"> <DecisionDescription decision={decision} /> <span className="text-gray-500 dark:text-gray-400 whitespace-nowrap text-right"> {formatDate(decision.paymentDate)} {decision.invoiceDate && ( <span className="block text-[10px] text-gray-400 dark:text-gray-500"> Inv: {formatDate(decision.invoiceDate)} </span> )} </span> </div> {/* Row 3: Reason Badge & Expand Indicator */} <div className="flex justify-between items-center"> {/* Rule Type Badge */} {decision.ruleType === "REAL_INVOICE" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-purple-200 dark:border-purple-800/30 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300"> <span className="h-1.5 w-1.5 rounded-full bg-purple-500 dark:bg-purple-400 mr-1.5"></span> Real Invoice Rule </span> )} {decision.ruleType === "UNINVOICED_WORK" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-cyan-200 dark:border-cyan-800/30 bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-300"> <span className="h-1.5 w-1.5 rounded-full bg-cyan-500 dark:bg-cyan-400 mr-1.5"></span> Uninvoiced Work Rule </span> )} {decision.ruleType === "PAYMENT_TERMS" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-blue-200 dark:border-blue-800/30 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"> <span className="h-1.5 w-1.5 rounded-full bg-blue-500 dark:bg-blue-400 mr-1.5"></span> Payment Terms Rule </span> )} {decision.ruleType === "MEETS_CRITERIA" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"> <span className="h-1.5 w-1.5 rounded-full bg-gray-500 dark:bg-gray-400 mr-1.5"></span> Meets Criteria </span> )} {/* Expand Indicator */} {canExpand && ( <span className="text-gray-400 dark:text-gray-500"> <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className={`w-4 h-4 transition-transform ${ isExpanded ? "rotate-180" : "" }`} > <path fillRule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clipRule="evenodd" /> </svg> </span> )} </div> </div> {/* Expanded Section for Card View */} {isExpanded && canExpand && ( <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50/50 dark:bg-gray-900/20"> <ErrorBoundary fallback={ <div className="text-xs text-red-500 dark:text-red-400 p-2 border border-red-200 dark:border-red-900 rounded"> Error displaying detail view. </div> } > {renderReconciliationCard()} </ErrorBoundary> </div> )} </div> ); } // Desktop table row view return ( <> <tr className={`${bgColorClass} ${ canExpand ? "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/30" : "" }`} onClick={canExpand ? onToggleExpand : undefined} > {/* Decision cell */} <td className="px-4 py-2.5 text-center"> <div className="flex items-center justify-center"> {/* Show expand icon for exclusions */} {canExpand && ( <svg className="h-3.5 w-3.5 mr-1 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={isExpanded ? "M19 9l-7 7-7-7" : "M9 5l7 7-7 7"} /> </svg> )} <ErrorBoundary> <DecisionCell decision={decision} /> </ErrorBoundary> </div> </td> {/* Description column */} <td className="px-4 py-2.5 text-gray-900 dark:text-gray-100 min-w-0"> <DecisionDescription decision={decision} /> </td> {/* Date column */} <td className="px-4 py-2.5 whitespace-nowrap text-gray-700 dark:text-gray-300"> <div>{formatDate(decision.paymentDate)}</div> <div className="text-[10px] text-gray-400 dark:text-gray-500"> {decision.invoiceDate ? `(Invoice: ${formatDate(decision.invoiceDate)})` : ""} </div> </td> {/* Amount column */} <td className="px-4 py-2.5 whitespace-nowrap text-gray-700 dark:text-gray-300"> {formatCurrency(decision.amount)} </td> {/* Status column */} <td className="px-4 py-2.5"> {decision.action === "kept" ? ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"> <span className="h-1.5 w-1.5 rounded-full bg-green-500 dark:bg-green-400 mr-1.5"></span> Included </span> ) : ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-yellow-200 dark:border-yellow-800/50 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300"> <svg className="w-3 h-3 mr-1 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" > <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" /> </svg> Excluded </span> )} </td> {/* Reason column */} <td className="px-4 py-2.5"> {decision.ruleType === "REAL_INVOICE" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-purple-200 dark:border-purple-800/30 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300"> <span className="h-1.5 w-1.5 rounded-full bg-purple-500 dark:bg-purple-400 mr-1.5"></span> Real Invoice Rule </span> )} {decision.ruleType === "UNINVOICED_WORK" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-cyan-200 dark:border-cyan-800/30 bg-cyan-100 dark:bg-cyan-900/30 text-cyan-700 dark:text-cyan-300"> <span className="h-1.5 w-1.5 rounded-full bg-cyan-500 dark:bg-cyan-400 mr-1.5"></span> Uninvoiced Work Rule </span> )} {decision.ruleType === "PAYMENT_TERMS" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-blue-200 dark:border-blue-800/30 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300"> <span className="h-1.5 w-1.5 rounded-full bg-blue-500 dark:bg-blue-400 mr-1.5"></span> Payment Terms Rule </span> )} {decision.ruleType === "MEETS_CRITERIA" && ( <span className="px-2 py-1 inline-flex items-center text-xs font-medium rounded border border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"> <span className="h-1.5 w-1.5 rounded-full bg-gray-500 dark:bg-gray-400 mr-1.5"></span> Meets Criteria </span> )} </td> </tr> {/* Expandable reconciliation details row for table view */} {isExpanded && canExpand && ( <tr className={bgColorClass}> <td colSpan={6} className="px-4 py-2"> <div className="ml-4"> <ErrorBoundary fallback={ <div className="text-xs text-red-500 dark:text-red-400 p-2 border border-red-200 dark:border-red-900 rounded"> <div className="font-medium"> Error displaying detail view </div> <div className="text-[10px]"> Please report this issue to the development team. </div> </div> } > {renderReconciliationCard()} </ErrorBoundary> </div> </td> </tr> )} </> ); }; export default DecisionListItem; 